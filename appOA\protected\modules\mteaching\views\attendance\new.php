<!-- 加载底部导航栏 -->
<?php

function statistics($data = '',$teacherData)
{
    $dataArr = array();
    if(isset($teacherData) && isset($teacherData[$data])){
        foreach ($teacherData[$data] as $val) {
            $dataArr[] = $val;
        }
    }
    return $dataArr;
}
$typeData = array(
    TimetableRecords::ATTENDANCE_STATUS => "P",
    TimetableRecords::LATE_STATUS => "T",
    TimetableRecords::LEAVE_STATUS => "L",
    TimetableRecords::ABSENTEE_STATUS => "A",
);
?>
<style>
    .weekNum{
        width:72px;
        height:50px;
        background: #FAFAFA;
        border-radius: 4px;
        border: 1px solid #EBEEF5;
        color:#666666;
    }
    .weekNum:hover{
        color:#fff !important;
        background: #4D88D2;
        border-radius: 4px;
        border: 1px solid #4D88D2;
        cursor:pointer 
    }
    .borderBto{
        border-bottom: 1px solid #EBEEF5;
    }
    .lineHeight{
        line-height:25px
    }
    .weekActive{
        color:#fff !important;
        background: #4D88D2;
        border-radius: 4px;
        border: 1px solid #4D88D2;
    }
    .weekActive .weekColor{
        color:#fff !important;
    }
    .border{
        width: 0;
        height: 0;
        border-top: 50px solid #5CB85C;
        border-right: 50px solid transparent;
        position: absolute;
        left: 0px;
        top: 0px;
    }
    .holiday{
        border-top: 50px solid #5CB85C;
    }
    .replace{
        border-top: 50px solid #F0AD4E;
    }
    .holidayText{
        position: absolute;
        left: 10px; 
        top: -48px;
        color: #fff;
        font-size: 18px;
        font-weight: 200;
    }
    .table{
        color:#606266
    }
    .table tr th {
        vertical-align:middle !important;
        text-align:center;
        color:#606266
    }
    .replaceCourse{
        color:#F0AD4E;
        position: absolute;
        top:20px;
        right: 20px;
        width: 42px;
    }
    .yellow{
        background:#FDEBC0 !important;
    }
    .blue{
        background:#EFF5FF;
    }
    .gray{
        background:#FAFAFA;
    }
    .font16{
        font-size:16px
    }
    .course{
        padding:8px; 
        border-radius:4px;
        color:#666666
    }
    .prev{
        position: absolute;
        left: 30px;
        top: 0;
        font-size: 18px;
        height: 52px;
        background: #fff;
        line-height: 50px;
        width:30px;
        z-index: 99;
        top:-1px;
    }
    .prevLeft{
        left:0;
    }
    .next{
        position: absolute;
        right: 30px;
        top: 0;
        font-size: 18px;
        height: 52px;
        background: #fff;
        line-height: 50px;
        width:30px;
        z-index: 99;
        top:-1px;
    }
    .nextRight{
        right:0;
    }
    .dis{
        color:#C0C4CC
    }
    .loading{
        width:98%;
        height:95%;
        background:#fff;
        position: absolute; 
        opacity: 0.5;
        z-index: 99
    }
    .loading span{
        width:100%;
        height:60%;
        display:block;
        background: url("<?php echo Yii::app()->theme->baseUrl?>/images/loading.gif")no-repeat center center ;
    }
    [v-cloak] {
        display: none;
    }
    .animation{
        animation:margin-left: 92px; 0.1s infinite;
        -webkit-animation:margin-left: 92px; 0.5s infinite;
    }
    @keyframes mymove
    {
        100% {margin-left: 92px;}
    }

    @-webkit-keyframes mymove /*Safari and Chrome*/
    {
        100% {margin-left: 92px;}
    }
    .actOpacity{
        opacity: .5;
        cursor: not-allowed !important; 
    }
    .bgYellow{
        background:#FFF7E3;
    }
    .btn-default.active{
        color: #fff;
        background-color: #337ab7;
        border-color: #2e6da4;
    }
    .pink{
        background:#FFECEE
    }
    .replaceTeacher{
        border: 1px solid #D9534F;
        border-radius: 4px;
        height: 24px;
        background: #fff;
        line-height: 24px;
    }
    .replaceTeacherText{
        display:inline-block;
        background: #D9534F;
        color: #fff;
        width: 28px;
        text-align: center;
        height: 100%;
    }
    .replaceTeacher img{
        width: 18px;
        height: 18px;
        border-radius: 50%;
        object-fit:cover;
        margin: -4px 3px 0;
    }
    .image{
        width: 44px; 
        height: 44px;
        object-fit:cover;
        border-radius:50%
    }
    .grey{
        background:#FAFAFA;
        padding:8px 16px;
        border-radius:5px
    }
    .teacherImg{
        width:54px;
        height:54px;
        border-radius:8px;
        object-fit:cover;
    }
    .font18{
        font-size:18px
    }
    .replaceBlue{
        background:#4D88D2;
        color:#fff;
        border-radius:5px
    }
</style>
<div class="container-fluid">
    <!-- 面包屑导航 -->
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Teaching Tasks'), array('//mteaching/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', 'G6-12 Student Attendance'); ?></li>
        <li class="active"><?php echo Yii::t('campus', 'Student Attendance'); ?></li>
    </ol>
    <div class="row">
        <div class="col-md-2">
            <?php
                $this->widget('zii.widgets.CMenu',array( 
                    'items'=> $this->siderMenu,
                    'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-right background-gray mb10'),
                    'activeCssClass'=>'active',
                    'itemCssClass'=>''
                ));
            ?>
        </div>
        <div class="col-md-10">
            <div id='container' v-cloak  v-if='week.item'>
                <p class='color3 mb20' ><strong style='font-size:18px'><?php echo Yii::t('campus','Student Attendance') ?></strong> <span class='ml20 font14'>{{startYear}}</span> </p>
                <h4 class='mb20'>
                    <span class="glyphicon glyphicon-user color6"></span>
                    <span style='color:#4D88D2' class='font16'>{{teacherName}}</span>
                    <span style='color:#4D88D2' title="<?php echo Yii::t('attends', 'View Schedules of Other Teachers')?>" @click="chooseTeacher()" class="glyphicon glyphicon-chevron-down"></span>
                </h4>
                <div class='loading'  v-if='showLoading'>
                    <span></span>
                </div>
                <div v-if='schedule.length!=0 && !showLoading'>
                    <div class='relative' v-if='week.item'>
                        <div class='glyphicon glyphicon-step-backward prev prevLeft color6' v-if='offsetLeft<0'  @click='headWeek()'></div>
                        <div class='glyphicon glyphicon-step-backward prev prevLeft dis color6' v-else></div>
                        <div class='glyphicon glyphicon-chevron-left prev color6' v-if='offsetLeft<0'  @click='nextWeek()'></div>
                        <div class='glyphicon glyphicon-chevron-left prev dis color6' v-else></div>
                        <div style='width:100% ;padding:0 60px;overflow:hidden;' ref='week'>
                            <div :style="'margin-left:'+ animateLeft +'px;width:'+weekWidth"  class='animation'>
                                <div class='pull-left mr20 weekNum text-center font12'  v-for='(list,index) in week.item' :class='list.number==currentWeek.number?"weekActive":list.category==null?"actOpacity":""' @click='eventWeek(list)'  aria-hidden="true" onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title='`<div>week ${list.number}</div>${formatTime(list.days[0])} — ${formatTime(list.days[4])}`'  data-placement="top">
                                    <div class='borderBto lineHeight'>week {{list.number}}</div>   
                                    <div class='weekColor lineHeight'><strong>{{list.category==null?'-':list.category}}</strong> </div>
                                </div>
                            </div>
                            <div class='clearfix'></div>
                        </div>
                        <div class='glyphicon glyphicon-chevron-right next color6' v-if='offsetLeft>=Difference' @click='prevWeek()'></div>
                        <div class='glyphicon glyphicon-chevron-right next color6 dis' v-else></div>
                        <div class='glyphicon glyphicon-step-forward next nextRight color6' v-if='offsetLeft>=Difference' @click='footWeek()'></div>
                        <div class='glyphicon glyphicon-step-forward next nextRight color6 dis' v-else></div>
                    </div>
                    <div class='loading'  v-if='tabLoading'>
                        <span></span>
                    </div>
                    <table class='table table-bordered mt20' style='table-layout: fixed' ref='table' v-if='currentWeek.days && isShow'>
                        <thead >
                            <tr class='text-center'>
                                <th width='100' >
                                <?php echo Yii::t('labels','Time') ?>
                                </th>
                                <th v-for='(list,id) in currentWeek.days' class='relative font14'>
                                    <div class='border holiday' v-if='holidaysData(list)' onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title='holidays[list]'  data-placement="top">
                                        <div class='holidayText' >H</div>  
                                    </div>
                                    <div class='border replace' v-if='replaceCourse(list)' onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title='`${replaceData[list].replace_tip}`'  data-placement="top">
                                        <div class='holidayText' >E</div>  
                                    </div>
                                    <div>{{currentWeek.weekDayData[id]}} <span v-if='list==nowDay'  class="label label-info">Today</span> </div>
                                    <div>{{formatTime(list)}}</div>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for='(data,index) in schedule[currentWeek.category]'>
                                <th class='text-center'>
                                    <div>{{timetableTimes[index].label}}</div>
                                    <div style='font-weight:200'>{{timetableTimes[index].timeslot}}</div>
                                </th>
                                <td v-for='(item,idx) in currentWeek.days' :class='holidaysData(item)?"gray":replaceCourse(item)?"bgYellow":""'>
                                    <template v-if='isShow'>
                                    <template v-for='(_item,i) in data[idx]' >                                        
                                        <div v-if='_item!=null && !replaceCourse(item) && !holidaysData(item)' :class='data[idx].length!=1?"pink mb5":"blue"'  class='course color6 ' >
                                            <div class='mb5 flex'>
                                                <div  class='flex1'>
                                                    <a v-if="_item[0].includes('MEET.')"  href="javascript:;" @click='meetDetail(_item,idx,index)'>{{courses[_item[0]]}}</a>
                                                    <a v-else href="javascript:;" @click='details(_item,idx,index)'>{{courses[_item[0]]}}</a>
                                                </div>
                                                <span class='glyphicon glyphicon-user font12' v-if='canReplace==1' @click='setReplace(_item[0],idx,index,item)'></span>
                                            </div>
                                            <p>{{_item[0]}}</p>
                                            <p>{{_item[1]}}</p>
                                            <p v-if="replaceClass[currentWeek.number+'_'+(idx+1)+'_'+(index+1)+'_'+currentWeek.category]  && replaceClass[currentWeek.number+'_'+(idx+1)+'_'+(index+1)+'_'+currentWeek.category][_item[0]]" class='replaceTeacher'>
                                                <span  class='replaceTeacherText'>替</span> 
                                                <img :src="replaceTeacherList[replaceClass[currentWeek.number+'_'+(idx+1)+'_'+(index+1)+'_'+currentWeek.category][_item[0]].new_teacher].photo" alt=""> 
                                                <span >{{replaceTeacherList[replaceClass[currentWeek.number+'_'+(idx+1)+'_'+(index+1)+'_'+currentWeek.category][_item[0]].new_teacher].name}}</span>
                                            </p>
                                            <p v-if="replacedCourse[currentWeek.number+'_'+(idx+1)+'_'+(index+1)+'_'+currentWeek.category] && replacedCourse[currentWeek.number+'_'+(idx+1)+'_'+(index+1)+'_'+currentWeek.category][_item[0]]" class='replaceTeacher'>
                                               <span  class='replaceTeacherText'>替</span> 
                                               <img :src="replaceTeacherList[replacedCourse[currentWeek.number+'_'+(idx+1)+'_'+(index+1)+'_'+currentWeek.category][_item[0]].new_teacher].photo" alt=""> 
                                               <span >{{replaceTeacherList[replacedCourse[currentWeek.number+'_'+(idx+1)+'_'+(index+1)+'_'+currentWeek.category][_item[0]].new_teacher].name}}</span>
                                            </p>
                                            <div v-if='signList[item] && signList[item][index+1] && signList[item][index+1][_item[0]]'>
                                                <strong class='mb5 mr10 inline-block  color6' v-if='signList[item][index+1][_item[0]].P!=0'  onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title='typesAttendance.P'  data-placement="top">P <span class="badge"  style='background:#5CB85C'>{{signList[item][index+1][_item[0]].P}}</span></strong>
                                                <strong class='mb5 mr10 inline-block  color6' v-if='signList[item][index+1][_item[0]].OP!=0'  onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title='typesAttendance.OP'  data-placement="top">OP <span class="badge"  style='background:#5CB85C'>{{signList[item][index+1][_item[0]].OP}}</span></strong>
                                                <strong class='mb5 mr10 inline-block  color6' v-if='signList[item][index+1][_item[0]].T!=0'  onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title='typesAttendance.T'  data-placement="top">T <span class="badge"  style='background:#F0AD4E'>{{signList[item][index+1][_item[0]].T}}</span></strong>
                                                <strong class='mb5 mr10 inline-block  color6' v-if='signList[item][index+1][_item[0]].PL!=0'  onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title='typesAttendance.PL'  data-placement="top">PL <span class="badge" style='background:#5BC0DE'>{{signList[item][index+1][_item[0]].PL}}</span></strong>
                                                <strong class='mb5 mr10 inline-block  color6' v-if='signList[item][index+1][_item[0]].SL!=0' onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title='typesAttendance.SL'  data-placement="top">SL <span class="badge" style='background:#5BC0DE'>{{signList[item][index+1][_item[0]].SL}}</span></strong>
                                                <strong class='mb5 mr10 inline-block  color6' v-if='signList[item][index+1][_item[0]].A!=0'  onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title='typesAttendance.A'  data-placement="top">A <span class="badge"  style='background:#D9534F'>{{signList[item][index+1][_item[0]].A}}</span></strong>
                                                <strong class='mb5 mr10 inline-block  color6' v-if='signList[item][index+1][_item[0]].ES!=0'  onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title='typesAttendance.ES'  data-placement="top">ES <span class="badge"  style='background:#D9534F'>{{signList[item][index+1][_item[0]].ES}}</span></strong>
                                                <strong class='mb5 inline-block  color6' v-if='signList[item][index+1][_item[0]].IS!=0'  onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title='typesAttendance.IS'  data-placement="top">IS <span class="badge"  style='background:#D9534F'>{{signList[item][index+1][_item[0]].IS}}</span></strong>
                                            </div>
                                        </div>
                                    </template>
                                    <template v-for='(_item,i) in course(item,idx,index)' >  
                                        <div v-if='replaceCourse(item) && course(item,idx,index)!=null'  :class='course(item,idx,index).length!=1?"pink mb5":"yellow"'  class='course color6 ' >
                                            <div class='mb5 flex'>
                                                <div  class='flex1'>
                                                    <a v-if="_item[0].includes('MEET.')"  href="javascript:;" @click='meetDetail(course(item,idx,index),idx,index)'>{{courses[_item[0]]}}</a>
                                                    <a v-else href="javascript:;" @click='details(course(item,idx,index),idx,index)' >{{courses[_item[0]]}}</a>
                                                </div>
                                                <span class='glyphicon glyphicon-user font12' v-if='canReplace==1' @click='setReplace(_item[0],idx,index,item)'></span>
                                            </div>
                                            <p>{{_item[0]}}</p>
                                            <p>{{_item[1]}}</p>
                                            <p v-if="replaceClass[currentWeek.number+'_'+(idx+1)+'_'+(index+1)+'_'+replaceData[item].category]  && replaceClass[currentWeek.number+'_'+(idx+1)+'_'+(index+1)+'_'+replaceData[item].category][_item[0]]" class='replaceTeacher'>
                                                <span  class='replaceTeacherText'>替</span> 
                                                <img :src="replaceTeacherList[replaceClass[currentWeek.number+'_'+(idx+1)+'_'+(index+1)+'_'+replaceData[item].category][_item[0]].new_teacher].photo" alt=""> 
                                                <span >{{replaceTeacherList[replaceClass[currentWeek.number+'_'+(idx+1)+'_'+(index+1)+'_'+replaceData[item].category][_item[0]].new_teacher].name}}</span>
                                            </p>
                                            <p v-if="replacedCourse[currentWeek.number+'_'+(idx+1)+'_'+(index+1)+'_'+replaceData[item].category] && replacedCourse[currentWeek.number+'_'+(idx+1)+'_'+(index+1)+'_'+replaceData[item].category][_item[0]]" class='replaceTeacher'>
                                               <span  class='replaceTeacherText'>替</span> 
                                               <img :src="replaceTeacherList[replacedCourse[currentWeek.number+'_'+(idx+1)+'_'+(index+1)+'_'+replaceData[item].category][_item[0]].new_teacher].photo" alt=""> 
                                               <span >{{replaceTeacherList[replacedCourse[currentWeek.number+'_'+(idx+1)+'_'+(index+1)+'_'+replaceData[item].category][_item[0]].new_teacher].name}}</span>
                                            </p>
                                            <div v-if='signList[item] && signList[item][index+1] && signList[item][index+1][_item[0]]'>
                                                <strong class='mb5 mr10 inline-block  color6' v-if='signList[item][index+1][_item[0]].P!=0' onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title='typesAttendance.P'  data-placement="top">P <span class="badge"  style='background:#5CB85C'>{{signList[item][index+1][_item[0]].P}}</span></strong>
                                                <strong class='mb5 mr10 inline-block  color6' v-if='signList[item][index+1][_item[0]].OP!=0' onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title='typesAttendance.OP'  data-placement="top">OP <span class="badge"  style='background:#5CB85C'>{{signList[item][index+1][_item[0]].OP}}</span></strong>
                                                <strong class='mb5 mr10 inline-block  color6' v-if='signList[item][index+1][_item[0]].T!=0' onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title='typesAttendance.T'  data-placement="top">T <span class="badge"  style='background:#F0AD4E'>{{signList[item][index+1][_item[0]].T}}</span></strong>
                                                <strong class='mb5 mr10 inline-block  color6' v-if='signList[item][index+1][_item[0]].PL!=0' onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title='typesAttendance.PL'  data-placement="top">PL <span class="badge" style='background:#5BC0DE'>{{signList[item][index+1][_item[0]].PL}}</span></strong>
                                                <strong class='mb5 mr10 inline-block  color6' v-if='signList[item][index+1][_item[0]].SL!=0' onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title='typesAttendance.SL'  data-placement="top">SL <span class="badge" style='background:#5BC0DE'>{{signList[item][index+1][_item[0]].SL}}</span></strong>
                                                <strong class='mb5 mr10 inline-block  color6' v-if='signList[item][index+1][_item[0]].A!=0' onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title='typesAttendance.A'  data-placement="top">A <span class="badge"  style='background:#D9534F'>{{signList[item][index+1][_item[0]].A}}</span></strong>
                                                <strong class='mb5 mr10 inline-block  color6' v-if='signList[item][index+1][_item[0]].IS!=0' onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title='typesAttendance.IS'  data-placement="top">IS <span class="badge"  style='background:#D9534F'>{{signList[item][index+1][_item[0]].IS}}</span></strong>
                                                <strong class='mb5 inline-block  color6' v-if='signList[item][index+1][_item[0]].ES!=0' onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title='typesAttendance.ES'  data-placement="top">ES <span class="badge"  style='background:#D9534F'>{{signList[item][index+1][_item[0]].ES}}</span></strong>
                                            </div>
                                        </div>
                                    </template>
                                    </template>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="alert alert-warning" v-else-if='!showLoading' role="alert"><?php echo Yii::t('attends', 'No course has been assigned to you.')?></div>

                <div class="modal fade" id="remarks" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false">
                    <div class="modal-dialog" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                                <h4 class="modal-title" ><?php echo Yii::t('attends','Select a Teacher') ?> <span id="remarks_t"></span></h4>
                            </div>
                            <div class="form-horizontal">
                                <div class="modal-body">
                                    <div name='teacherId' id="mySelect">
                                        <a class='mb5 btn btn-default mr10' href='javascript:;' :class='teacherId==list.id?"active":""' v-for='(list,index) in teacherList' @click='getData(list)'>{{list.value}}</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal fade" id="courseStu" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false" data-backdrop="static">
                    <div class="modal-dialog modal-lg" role="document" style="width: 1000px;">
                        <div class="modal-content">
                            <div class="modal-header">
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                                <h4 class="modal-title" ><?php echo Yii::t('campus','Student Attendance') ?> <span id="remarks_t"></span></h4>
                            </div>
                            <div class="modal-body" v-html='courseStuHtml'>
                                
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-primary" :disabled='statusFrom==0?true:disabled' @click='submitForm()'><?php echo Yii::t("global", 'Save');?></button>
                                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", 'Close');?></button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal fade" id="meetDetail" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false" data-backdrop="static">
                    <div class="modal-dialog modal-lg" role="document" style="width: 1000px;">
                        <div class="modal-content">
                            <div class="modal-header">
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                                <h4 class="modal-title" ><?php echo Yii::t('attends','Meet') ?> <span id="remarks_t"></span></h4>
                            </div>
                            <div class="modal-body" v-html='meetDetailHtml'>

                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", 'Close');?></button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal fade" id="replaceModal" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false" data-backdrop="static">
                    <div class="modal-dialog modal-lg" role="document" >
                        <div class="modal-content">
                            <div class="modal-header">
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                                <h4 class="modal-title" ><?php echo Yii::t('campus','设置代课老师') ?> <span id="remarks_t"></span></h4>
                            </div>
                            <div class="modal-body">
                                <div class='flex mb20' v-if='showTeacher!=""'>
                                    <div class='flex1'>
                                        <img :src="replaceTeacherList[showTeacher].photo" alt=""  class='teacherImg'>
                                        <span>{{replaceTeacherList[showTeacher].name}}</span>
                                    </div>
                                    <div>
                                        <button type="buztton" class="btn btn-primary" v-if='showTeacher==replaceTeacherId' :disabled='replaceBtn' @click='cancelReplaceTeacher'>取消代课</button>
                                    </div>
                                </div>
                                <div v-else class='mb20'>
                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/noAvatar.png' ?>" class='teacherImg' alt="">
                                    <span class='color9 font18'>暂未选择代课老师</span>
                                </div>
                                <div>
                                    <div v-for='(list,index) in teacherList' class='col-md-4 col-sm-12 mb20'>
                                        <div class='grey' :class='showTeacher==list.id?"replaceBlue":""' @click='slectTeacher(list)'>
                                            <img :src="replaceTeacherList[list.id].photo" class='image' alt="">    {{list.value}}
                                        </div>
                                    </div>
                                </div>
                                <div class='clearfix'></div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", 'Close');?></button>
                                <button type="button" class="btn btn-primary" @click='replaceTeacherSave()'  :disabled='replaceBtn'><?php echo Yii::t("global", 'Save');?></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<script>
    function calcNum() {
        var num = {
            10: 0,
            11: 0,
            20: 0,
            30: 0,
            31: 0,
            40: 0,//旷课
            41: 0,//校内停学
            42: 0,//校外停学
            0: 0,
            1: 0,
            60:0,//电子设备未违规
            61:0,//手机违规
            62:0,//其他电子违规
            63:0,//全部违规
        };
        
        $('#courseStu #visits-form .table input[type="radio"]:checked').each(function (key, val) {
            var v = $(val).val();
            num[v] += 1;
        });
        for (var key in num){
            var val = num[key];
            if (key == 31) {
                key=30
                val += num[30]
            }
            if(key == 41 || key == 42){
                key=40
                val = num[40]+num[41]+num[42]
            }
            if (key == 11) {
                key=10
                val += num[10]
            }
            if(key == 61 || key == 62 || key ==63){
                key=61
                val = num[61]+num[62]+num[63]
            }
            $('#current_'+key).text(val);
        }
        
    }

    function types(e, childid, min){
        $('#action_'+childid+'.attendance button').removeClass('active').eq(1).addClass('active').find('span.tflag').text( $(e).text() );
        $('#hospital_2_'+childid).attr('checked', true);
        $('#late_'+childid).val(min);
        calcNum();
    }

    function types1(e, childid, min, num){
        $('#action_'+childid+'.attendance button').removeClass('active').eq(num).addClass('active').find('span.tflag').text( $(e).text() );
        $('#hospital_'+min+'_'+childid).attr('checked', true);
        calcNum();
    }


    function types3(e, childid, min){
        $('#action_electronic_'+childid+'.attendance button').removeClass('active').eq(0).addClass('active').find('span.tflag').text( $(e).text() );
        $('#electronic_'+min+'_'+childid).attr('checked', true);
        calcNum();
    }

    function allSet(type, min) {
        if (type == 20) {
            $('.act button').removeClass('active');
            $('.table tbody tr .buttons[act] .btn_label_'+type).addClass('active').find('span.tflag').text( min+' <?php echo Yii::t('attends','Minutes');?>' );
            $('.table tbody tr .buttons[act] .btn_label_'+type+' input[type="radio"]').attr('checked', true);
            $('.table tbody tr .buttons[act] .btn_label_'+type+' input[type="hidden"]').val(min);
        }
        else if (type == 30 || type == 31) {
            $('.act button').removeClass('active');
            var text = type == 30 ? '<?php echo Yii::t('campus', 'Personal Leave');?>' : '<?php echo Yii::t('campus', 'Sick Leave');?>'
            $('.table tbody tr .buttons[act] .btn_label_30').addClass('active').find('span.tflag').text( text );
            $('.table tbody tr .buttons[act] .btn_label_30 input[value="'+type+'"]').attr('checked', true);
        }
        else if (type == 40 || type == 41 || type==42){
            $('.act button').removeClass('active');
            if(type==40){
                var text = '<?php echo Yii::t('attends', 'Absent');?>'
            }else if (type==41){
                var text = '<?php echo Yii::t('attends', 'Internal Suspension');?>'
            }else if(type==42){
                var text = '<?php echo Yii::t('attends', 'External Suspension');?>'
            }else{
                var text = '<?php echo Yii::t('attends', 'Absent');?>'
            }
            $('.table tbody tr .buttons[act] .btn_label_40').addClass('active').find('span.tflag').text( text );
            $('.table tbody tr .buttons[act] .btn_label_40 input[value="'+type+'"]').attr('checked', true);
        }
        else if (type == 10 || type == 11) {
            $('.act button').removeClass('active');
            var text = type == 10 ? '<?php echo Yii::t('attends', 'Present');?>' : '<?php echo Yii::t('attends', 'Online Present');?>'
            $('.table tbody tr .buttons[act] .btn_label_10').addClass('active').find('span.tflag').text( text );
            $('.table tbody tr .buttons[act] .btn_label_10 input[value="'+type+'"]').attr('checked', true);
        }
        else if(type == 61 || type == 62 ||type == 63){
            $("[id^='action_electronic_'] button").removeClass('active');
            if(type == 61 ){
                var text = '<?php echo Yii::t('attends', 'Phone');?>'
            }else if(type == 62){
                var text = '<?php echo Yii::t('attends', 'Other Personal Devices');?>'
            }else if(type == 63){
                var text = '<?php echo Yii::t('attends', 'Both');?>'
            } else{
                var text = '<?php echo Yii::t('attends', 'Phone');?>'
            }

            $('.table tbody tr .buttons .btn_label_61').addClass('active').find('span.tflag').text( text );
            $('.table tbody tr .buttons .btn_label_61 input[value="'+type+'"]').attr('checked', true);
        }
        else {
            $('.table tbody tr .buttons[act] .btn_label_'+type).click();
        }
        calcNum();
    }

    function allSets(type, min) {
        $('.table tbody tr .buttons .btn_label_'+type).click();
        calcNum();
    }

    function typeSel() {
        setTimeout(function () {
            calcNum();
        }, 300)
    }

    function electronic_all()
    {
        var data = $("#selectpicker_all").selectpicker('val');
        $(".selectpicker_child").selectpicker('val',data);
    }

    function electronic_y(child_id)
    {
        $("#electronic_n_"+child_id).removeAttr('checked')
        $("#electronic_n_"+child_id).parent().removeClass('active')
        typeSel()

    }

    function electronic_n(e)
    {
        var child_id = $(e).attr('child-id');
        $('#selectpicker_'+child_id).selectpicker('val',[]);
    }

    function setelectronic()
    {
        // alert(1);
        // $('#selectpicker_all').selectpicker('val',[]);
    }

    var name='<?php echo $userModel ? $userModel->getName() : $uid ?>';
    var teacherId='<?php echo $uid ?>';
    var tid='<?php echo $tid ?>';
    var schoolid='<?php echo $schoolid ?>';
    var weeknumber='<?php echo $weeknumber ?>';
    var container = new Vue({
        el: "#container",
        data: {
            week: {},
            canReplace: '<?php echo $canReplace ? 1 : 0; ?>',
            schedule: {},
            replaceData: {},
            courses: {},
            holidays: {},
            currentWeek:{},
            replaceDay:100,
            holidayIndex:100,
            teacherName:name,
            weekWidth:'',
            offsetLeft:0,
            Difference:0,
            width:276,
            weekLang:['<?php echo Yii::t('labels','Mon') ?>','<?php echo Yii::t('labels','Tue') ?>','<?php echo Yii::t('labels','Wed') ?>','<?php echo Yii::t('labels','Thu') ?>','<?php echo Yii::t('labels','Fri') ?>'],
            teacherList:[],
            showLoading:false,
            signList:{},
            tabLoading:false,
            html:'',
            courseStuHtml:'',
            meetDetailHtml:'',
            timetableTimes:{},
            animateLeft:0,
            teacherId:'',
            replaceList:{},
            disabled:false,
            startYear:'',
            nowDay:'',
            statusFrom:1,
            typesAttendance:{
                'P':'<?php echo Yii::t('attends','Present') ?>',
                'OP':'<?php echo Yii::t('attends','Online Present') ?>',
                'T':'<?php echo Yii::t('attends','Tardy') ?>',
                'PL':'<?php echo Yii::t('campus','Personal Leave') ?>',
                'SL':'<?php echo Yii::t('campus','Sick Leave') ?>',
                'A':'<?php echo Yii::t('attends','Absent') ?>',
                'ES':'<?php echo Yii::t('attends','External Suspension') ?>',
                'IS':'<?php echo Yii::t('attends','Internal Suspension') ?>',
            },
            replaceClass:{},
            replacedCourse:{},
            replaceTeacherList:{},
            replaceTeacherId:'',
            showTeacher:'',
            showTeacherList:{},
            replaceWeekday:'',
            replacePeriod:'',
            replacecourse_code:'',
            replaceTeaData:{},
            copyCourses:{},
            copySchedule:{},
            isShow:false,
            source_day:'',
            replaceCategory:'',
            replaceBtn:false
        },
        watch:{
            replaceClass(){
            }
        },
        created(){
            let that=this
            $.ajax({
                type: "POST",
                url: "<?php echo $this->createUrl('teacher')?>",
                data: {tid:tid},
                dataType: "json",
                success: function(data){
                    if(data){
                        let list=[]
                        for(var key in data){
                            list.push({
                                id:key,
                                value:data[key].name
                            })
                        }
                        that.replaceTeacherList=data
                        that.teacherList=that.sortTea(list)
                    }
                }
            });
          this.getData('')
        },
        methods: {
            sortTea(list){
                list.sort((x,y)=>{
                    return x['value'].localeCompare(y['value'])
                })
                return list
            },
            chooseTeacher(){
                $("#remarks").modal('show');
            },
            course(list,idx,index){
                var len=[]
                if(this.replaceData[list]){
                    let replaceList=this.replaceData[list]
                    if(this.schedule[replaceList.category]){
                        for(var i=0;i<this.schedule[replaceList.category].length;i++){
                            len.push(this.schedule[replaceList.category][i][replaceList.day-1])
                        }
                    }
                } 
                // alert(1)
                return len[index]
            },
            formatTime(date){
                let data=date+''
                let year=data.substring(0,4)
                let mon=data.substring(4,6)
                let day=data.substring(6,8)
                return year+'/'+mon+'/'+day
            },
            getData(list){
                let that=this
                this.showLoading=true
                if(list!=''){
                    this.teacherName=list.value
                }
                this.teacherId=list==''?teacherId:list.id
                // this.animateLeft=0
                // this.offsetLeft=0
                $.ajax({
                    url: '<?php echo $this->createUrl("teacherInfo") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        teacherId:this.teacherId,
                        weeknumber:this.currentWeek.number, //可选,
                    },
                    success: function(data) {
                        if(data.state=='success'){  
                            that.week=data.data.week
                            that.schedule=data.data.schedule
                            that.holidays=data.data.holidays
                            that.courses=data.data.courses
                            that.copyCourses=JSON.parse(JSON.stringify(data.data.courses))
                            that.copySchedule=JSON.parse(JSON.stringify(data.data.schedule))
                           
                            that.replaceData=data.data.replace
                            that.weekWidth=that.week.item.length*95+'px'
                            that.timetableTimes=data.data.timetableTimes
                            that.nowDay=data.data.nowDay
                           
                            if(!that.currentWeek.days){
                                for(var i=0;i<that.week.item.length;i++){
                                    if(that.week.item[i].number==that.week.current){
                                            that.currentWeek=that.week.item[i]
                                    }
                                }
                            }
                            let start=data.data.week.item[0].monday+''
                            let end=data.data.week.item[data.data.week.item.length-1].monday+''
                            that.startYear=start.substring(0,4)+"-"+end.substring(0,4)+' <?php echo Yii::t('labels','School Year') ?>'
                            if(list!=''){
                                $("#remarks").modal('hide');
                            }
                            that.showLoading=false
                            if(that.schedule.length!=0){
                                that.$nextTick(()=>{ 
                                    that.Difference=(that.$refs.week.offsetWidth-120)-that.week.item.length*92
                                    var len=parseInt(that.$refs.week.offsetWidth/92)
                                    if(that.week.current>len/2){
                                        if(-that.week.current*92>that.Difference && that.week.current<=that.week.item.length-len){
                                            that.offsetLeft=-(that.week.current-len/2)*92
                                            that.animateLeft=-(that.week.current-len/2)*92
                                        }else{
                                            that.offsetLeft=that.Difference
                                            that.animateLeft=that.Difference
                                        }
                                    }else{
                                        that.offsetLeft=0
                                        that.animateLeft=0
                                    }
                                    that.getSign()
                                    that.getReplaceTeacher()
                                    that.isShow=false
                                });
                            }
                        }else{
                            that.showLoading=false 
                        }
                    }
                })
            },
            getSign(){
                let that=this
                this.tabLoading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("studentSituation") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        teacherId: this.teacherId,
                        weekMon:this.currentWeek.monday, //可选,
                    },
                    success: function(data) {
                        if(data.state=='success'){  
                            that.signList=data.data
                        }
                        that.tabLoading=false
                    }
                })
            },
            eventWeek(data){
                if(data.category==null){
                    return
                }
                this.currentWeek=data
                this.holidayIndex=100
                this.replaceDay=100
                this.getSign()
                this.getReplaceTeacher()
                
            },
            getReplaceTeacher(){
                let that=this
                that.isShow=false
                that.schedule=JSON.parse(JSON.stringify(that.copySchedule))
                $.ajax({
                    url: '<?php echo $this->createUrl("replaceTeacherData") ?>',
                    type: "post",
                    data: {
                        "weeknum":this.currentWeek.number,
                        "teacher_id":this.teacherId,
                    },
                    dataType: "json",
                    success: function (data) {
                        if(data.state=='success'){  
                            that.replaceClass=data.data.replaceData
                            that.replacedCourse=data.data.replacedData
                            that.replaceTeaData=data.data.needData
                            that.schedule[that.currentWeek.category].forEach((item,index) => {
                               that.currentWeek.days.forEach((list,i) => {
                                    if(!that.replaceCourse(list) && !that.holidaysData(list)){
                                        if(data.data.replaceData[that.currentWeek.number+'_'+(i+1)+'_'+(index+1)+'_'+that.currentWeek.category]){
                                            if(data.data.needData.schedule[that.currentWeek.category] && data.data.needData.schedule[that.currentWeek.category][index+1] && data.data.needData.schedule[that.currentWeek.category][index+1][i+1]){
                                                if(item[i]==null){
                                                    item[i]=[]
                                                    item[i]=data.data.needData.schedule[that.currentWeek.category][index+1][i+1]
                                                }else{
                                                    item[i].push.apply(item[i],data.data.needData.schedule[that.currentWeek.category][index+1][i+1]);
                                                }
                                            }
                                        }
                                    }else{
                                        if(that.replaceData[list] && data.data.replaceData[that.currentWeek.number+'_'+(i+1)+'_'+(index+1)+'_'+that.replaceData[list].category]){
                                            if(data.data.needData.schedule[that.replaceData[list].category] && data.data.needData.schedule[that.replaceData[list].category][index+1] && data.data.needData.schedule[that.replaceData[list].category][index+1][i+1]){
                                                let replaceList=that.replaceData[list]
                                                if(that.schedule[replaceList.category]){
                                                    if(that.schedule[replaceList.category][index][replaceList.day-1]==null){
                                                        that.schedule[replaceList.category][index][replaceList.day-1]=data.data.needData.schedule[that.replaceData[list].category][index+1][i+1]
                                                    }else{
                                                        that.schedule[replaceList.category][index][replaceList.day-1].push.apply(that.schedule[replaceList.category][index][replaceList.day-1],data.data.needData.schedule[that.replaceData[list].category][index+1][i+1]);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    that.isShow=true
                               })
                            })
                            that.courses= Object.assign(JSON.parse(JSON.stringify(that.copyCourses)), data.data.needData.courses)
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.disabled=false
                    },
                    error: function(data) {
                        that.disabled=false
                     }
                })

            },
            replaceCourse(list){
                if(this.replaceData[list]){
                    // this.replaceDay=index
                    // this.replaceList=this.replaceData[list]
                    // console.log(this.replaceList)
                    return true
                } 
            },
            holidaysData(list){
                if(this.holidays[list]){
                    // this.holidayIndex=index
                    return true
                }
            },
            prevWeek(){
                if(this.offsetLeft-this.Difference<this.width){
                    this.offsetLeft=this.Difference
                    $(".animation").animate({marginLeft:this.offsetLeft+'px'});
                }else{
                    this.offsetLeft=this.offsetLeft-this.width
                    $(".animation").animate({marginLeft:this.offsetLeft+'px'});

                }
            },
            nextWeek(){
                if(this.offsetLeft>-this.width){
                    this.offsetLeft=0
                    $(".animation").animate({marginLeft:'0px'});

                }else{
                    this.offsetLeft=this.offsetLeft+this.width
                    $(".animation").animate({marginLeft:this.offsetLeft+'px'});

                }
            },
            headWeek(){
                this.offsetLeft=0
                $(".animation").animate({marginLeft:'0px'});
            },
            footWeek(){
                this.offsetLeft=this.Difference
                $(".animation").animate({marginLeft:this.offsetLeft+'px'});
            },
            details(item,idx,index){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("courseStudentNew") ?>',
                    type: "get",
                    data: {
                        uid: this.teacherId,
                        weekday: (idx+1)+'-'+(index+1),
                        course_code:item[0], //可选,
                        datatime:this.formatTime(this.currentWeek.days[idx])
                    },
                    success: function(data) {
                        that.courseStuHtml=data
                        $("#courseStu").modal('show');
                        setTimeout(function () {
                            that.statusFrom=$('#status').val()
                        }, 300)
                       
                            
                    }
                })
            },
            meetDetail(item,idx,index){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("meetTeacher") ?>',
                    type: "get",
                    data: {
                        uid: this.teacherId,
                        weekday: (idx+1)+'-'+(index+1),
                        course_code:item[0], //可选,
                        datatime:this.formatTime(this.currentWeek.days[idx])
                    },
                    success: function(data) {
                        that.meetDetailHtml=data
                        $("#meetDetail").modal('show');
                        setTimeout(function () {
                            that.statusFrom=$('#status').val()
                        }, 300)


                    }
                })
            },
            submitForm(){
                let that=this
                var targetUrl = $("#visits-form").attr("action");
                this.disabled=true
                $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: targetUrl,
                    data: $('#visits-form').serialize(),
                    success: function (data) {
                        if(data.state=='success'){  
                            resultTip({
                                msg: data.state
                            });
                            $("#courseStu").modal('hide');
                            that.getSign()
                            that.getReplaceTeacher()
                        }else{
                            resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                        }
                        that.disabled=false
                    },
                    error: function(data) {
                        that.disabled=false
                     }
                });
            },
            setReplace(item,idx,index,day){
                this.source_day=''
                this.replaceCategory=''
                if(this.replaceData[day]){
                    this.source_day=this.replaceData[day].source_day
                    this.replaceCategory=this.replaceData[day].category
                }else{
                    this.source_day=''
                    this.replaceCategory=this.currentWeek.category
                }
                this.showTeacher=''
                this.replaceWeekday=idx+1
                this.replacePeriod=index+1
                this.replacecourse_code=item
                let filter=this.currentWeek.number+'_'+(idx+1)+'_'+(index+1)+'_'+this.replaceCategory
                if(this.replaceClass[filter] || this.replacedCourse[filter]){
                    if(this.replaceClass[filter] && this.replaceClass[filter][item]){
                        let codes=this.replaceClass[filter][item]
                        this.replaceTeacherId=codes.new_teacher
                        this.showTeacher=codes.new_teacher
                    }
                    if(this.replacedCourse[filter] && this.replacedCourse[filter][item]){
                        let code=this.replacedCourse[filter][item]
                        this.replaceTeacherId=code.new_teacher
                        this.showTeacher=code.new_teacher
                    }
                }else{
                    this.replaceTeacherId=''
                    this.showTeacher=''
                }
                $("#replaceModal").modal('show');
                
            },
            slectTeacher(list){
                this.showTeacher=list.id
                this.showTeacherList=list
            },
            cancelReplaceTeacher(){
                let that=this
                this.replaceBtn=true
                $.ajax({
                    url: '<?php echo $this->createUrl("replaceTeacherCancel") ?>',
                    type: "post",
                    data: {
                        "weeknum":this.currentWeek.number,
                        "weekday":this.replaceWeekday,
                        "period":this.replacePeriod,
                        "category":this.currentWeek.category,
                        "course_code":this.replacecourse_code
                    },
                    dataType: "json",
                    success: function (data) {
                        if(data.state=='success'){  
                            resultTip({
                                msg: data.state
                            });
                            $("#replaceModal").modal('hide');
                            that.getReplaceTeacher()
                            that.getSign()
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.replaceBtn=false
                    },
                    error: function(data) {
                        that.replaceBtn=false
                     }
                })
            },
            replaceTeacherSave(){
                let that=this
                this.replaceBtn=true
                $.ajax({
                    url: '<?php echo $this->createUrl("replaceTeacherSave") ?>',
                    type: "post",
                    dataType: "json",
                    data: {
                        "weeknum":this.currentWeek.number,
                        "weekday":this.replaceWeekday,
                        "period":this.replacePeriod,
                        "category":this.replaceCategory,
                        "old_teacher":this.teacherId,
                        "new_teacher":this.showTeacher,
                        "course_code":this.replacecourse_code,
                        'source_day' :this.source_day
                    },
                    success: function (data) {
                        if(data.state=='success'){  
                            resultTip({
                                msg: data.state
                            });
                            $("#replaceModal").modal('hide');
                            that.getReplaceTeacher()
                            that.getSign()
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.replaceBtn=false
                    },
                    error: function(data) {
                        that.replaceBtn=false
                     }
                })
            }
        }
    })
</script>
