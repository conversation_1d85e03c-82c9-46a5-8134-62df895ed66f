<?php
class ReplaceCalendarCommand extends CConsoleCommand
{
    public $batchNum = 20;
    public function init() 
    {
        Yii::import('common.models.*');
        Yii::import('common.models.calendar.CalendarSchool');
        Yii::import('common.models.invoice.ChildReserve');
    }
    
    public function actionIndex($schoolId=null)
    {
        if (empty($schoolId))
        {
            $this->usageError('School id not null.');
        }
        $model = Branch::model()->findByPk($schoolId);
        if (empty($model))
        {
            $this->usageError('parameter error.');
        }
        //关闭校历
        $this->closeCalendar($model);
        //开启校历
        $this->openCalendar($model);
        //复制孩子预留表信
        $this->copyChildInfo($model);
    }
    
    public function closeCalendar($schoolObj)
    {
        //关闭当前学年班级
        echo "Start close............".$schoolObj->abb." classes.\r\n";
        IvyClass::model()->updateAll(array('stat'=>IvyClass::STATS_CLOSED),'schoolid=:schoolid and yid=:yid', array(':schoolid'=>$schoolObj->branchid,':yid'=>$schoolObj->schcalendar));
        echo $schoolObj->abb." classes is closed.\r\n";
        echo "Start update........... ".$schoolObj->abb." calendar status.\r\n";
        $schoolObj->semester_stat = 20;
        $schoolObj->save();
        echo $schoolObj->abb." status updated.\r\n";
        CalendarSchool::model()->updateAll(array('is_selected'=>0),"branchid=:branchid and yid=:yid", array(':branchid'=>$schoolObj->branchid,':yid'=>$schoolObj->schcalendar));
        echo $schoolObj->abb."...updated calendar_school success.\r\n";
    }
    
    public function openCalendar($schoolObj)
    {
        //开启新学年校历
       $nextCalendarId = $schoolObj->getNextCalendarId();
       if ($nextCalendarId)
       {
           $schoolObj->schcalendar = $nextCalendarId;
           $schoolObj->semester_stat = 10 ;
           $schoolObj->save();
           echo $schoolObj->abb."...updated branch tabel success.\r\n";
           CalendarSchool::model()->updateAll(array('is_selected'=>1),"branchid=:branchid and yid=:yid", array(':branchid'=>$schoolObj->branchid,':yid'=>$nextCalendarId));
           echo $schoolObj->abb."...updated calendar_school success.\r\n";
           echo "Start open............".$schoolObj->abb." classes.\r\n";
           IvyClass::model()->updateAll(array('stat'=>IvyClass::STATS_OPEN),'schoolid=:schoolid and yid=:yid', array(':schoolid'=>$schoolObj->branchid,':yid'=>$nextCalendarId));
           echo $schoolObj->abb." classes is opened.\r\n";
       }
       else
       {
           echo $schoolObj->abb."...not assign new calendar.\r\n";
           Yii::app()->end();
       }
    }
    
    public function copyChildInfo($schoolObj)
    {
        $criteria  = new CDbCriteria();
        $criteria->compare('schoolid', $schoolObj->branchid);
        $criteria->compare('calendar', $schoolObj->schcalendar);
        $count = ChildReserve::model()->count($criteria);
        $cycle = ceil($count/$this->batchNum);
        if ($count)
		{
            for($i=0;$i<$cycle;$i++)
            {
                $criteria->limit = $this->batchNum;
                $criteria->offset = $i * $this->batchNum;
                $modelList = ChildReserve::model()->findAll($criteria);
                if (!empty($modelList))
                {
                    foreach ($modelList as $val)
                    {
                        // 如果下半年学校与当前学校ID不同，设置更新搜索信息
                        $childModel = ChildProfileBasic::model()->findByPk($val->childid);
                        if ($childModel->schoolid != $val->schoolid) {
                            ChildProfileBasic::setChildSync($childModel->childid);
                        }

                        ChildProfileBasic::model()->updateAll(array('classid'=>$val->classid,'status'=>$val->stat,'schoolid'=>$val->schoolid), 'childid=:childid', array(':childid'=>$val->childid));
                        echo $val->childid."...updated child_profile_basic success.\r\n";
                        $model = ChildClassLink::model()->findByPk($val->childid);
                        if (empty($model))
                        {
                           $model = new ChildClassLink();
                        }
                        $model->setAttributes($val->getAttributes(array('childid','hid','schoolid','classid','calendar','semester','stat','updated_timestamp','userid')));
                        $model->save();
                        echo $val->childid."...updated school_class success.\r\n";
                    }
                }
            }
            ChildReserve::model()->deleteAllByAttributes(array('schoolid'=>$schoolObj->branchid,'calendar'=>$schoolObj->schcalendar));
            echo "Deleted ".$schoolObj->abb."...data(child_reserve) success.\r\n";
        }
    }
}
?>
