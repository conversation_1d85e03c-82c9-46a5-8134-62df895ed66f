<?php

/**
 * This is the model class for table "ivy_vendor_citylink".
 *
 * The followings are the available columns in table 'ivy_vendor_citylink':
 * @property integer $link_id
 * @property integer $vendor_id
 * @property integer $diglossia_category
 * @property integer $diglossia_id
 */
class IvyVendorCitylink extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return IvyVendorCitylink the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_vendor_citylink';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('vendor_id, diglossia_category, diglossia_id', 'numerical', 'integerOnly'=>true),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('link_id, vendor_id, diglossia_category, diglossia_id', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'vendor'=>array(self::BELONGS_TO, 'IvyVendor', 'vendor_id'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'link_id' => 'Link',
			'vendor_id' => 'Vendor',
			'diglossia_category' => 'Diglossia Category',
			'diglossia_id' => 'Diglossia',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('link_id',$this->link_id);
		$criteria->compare('vendor_id',$this->vendor_id);
		$criteria->compare('diglossia_category',$this->diglossia_category);
		$criteria->compare('diglossia_id',$this->diglossia_id);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}