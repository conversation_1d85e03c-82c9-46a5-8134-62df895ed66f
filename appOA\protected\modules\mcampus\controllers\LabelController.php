<?php

class LabelController extends BranchBasedController
{
    public $actionAccessAuths = array(
        'index' => 'ivystaff_hos_office',
        'addGroup'=> 'ivystaff_hos_office',
        'delGroup'=> 'ivystaff_hos_office',
        'saveLabel'=> 'ivystaff_hos_office',
        'delLabel'=> 'ivystaff_hos_office',
        'saveChildLabel'=> 'ivystaff_hos_office',
        'delDeptChildLabel'=> 'ivystaff_hos_office',
    );
    public function init()
    {
        parent::init();

        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";
        $this->pageTitle = Yii::t('labels','Student Tags');;
        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Campus Workspace');
        //初始化选择校园页面
        if (!$this->checkRole()) {
            $this->multipleBranch = false;
        }
        $this->branchSelectParams['hideOffice'] = false;
        $this->branchSelectParams['urlArray'] = array('//mcampus/label/index');

        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/viewer/viewer.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/xlsx.full.min.js');

        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/vue.global.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/sortable/Sortable.min.js');
        //标签字体需要的文件
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/label/iconfont.css?v=20231124');

        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/viewer/viewer.js');

        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/v-calendar/v-calendar.umd.min.js');
        $cs->registerCssFile(Yii::app()->theme->baseUrl . '/css/calendar.css');
    }

    public function createUrl($route, $params = array(), $ampersand = '&', $parentOnly = false)
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }

        return parent::createUrl($route, $params, $ampersand);
    }

    public function checkRole()
    {
        $roleList = array('ivystaff_it','ivystaff_hos_office');
        foreach ($roleList as $role) {
            if (Yii::app()->user->checkAccess($role)) {
                return true;
            }
        }
        return false;
    }

    public function actionIndex()
    {
        $this->render('index', array());
    }

    public function actionGetIcon()
    {
        $requestUrl = 'label/child/getIcon';
        $this->remote2($requestUrl,array(),'get');
    }

    //添加标签组
    public function actionAddGroup()
    {
        $requestUrl = 'label/child/addGroup';
        $group_name = Yii::app()->request->getParam('group_name', '');
        $item = Yii::app()->request->getParam('item', array());
        $data = array(
            'group_name'=>$group_name,
            'item'=>$item,
        );
        $this->remote2($requestUrl, array('data'=>$data,),'post');
    }

    //删除标签组
    public function actionDelGroup()
    {
        $requestUrl = 'label/child/delGroup';
        $group_id = Yii::app()->request->getParam('group_id');
        $this->remote2($requestUrl, array('group_id'=>$group_id),'delete');
    }

    //获取所有标签组
    public function actionGetGroup()
    {
        $requestUrl = 'label/child/getGroup';
        $this->remote2($requestUrl,array(),'get');
    }

    //获取标签组内的标签
    public function actionGetGroupItem()
    {
        $requestUrl = 'label/child/getGroupItem';
        $group_id = Yii::app()->request->getParam('group_id');
        $this->remote2($requestUrl, array('group_id'=>$group_id),'get');
    }

    //获取标签的详情
    public function actionGetDetail()
    {
        $requestUrl = 'label/child/getDetail';
        $flag = Yii::app()->request->getParam('flag');
        $this->remote2($requestUrl, array('flag'=>$flag),'get');
    }

    //操作标签 添加和修改
    public function actionSaveLabel()
    {
        $requestUrl = 'label/child/saveLabel';
        $id = Yii::app()->request->getParam('id',0);
        $group_id = Yii::app()->request->getParam('group_id');
        $flag = Yii::app()->request->getParam('flag');
        $name = Yii::app()->request->getParam('name');
        $desc = Yii::app()->request->getParam('desc');
        $desc2 = Yii::app()->request->getParam('desc2');
        $color = Yii::app()->request->getParam('color');
        $flag_text = Yii::app()->request->getParam('flag_text');
        $teacher_ids = Yii::app()->request->getParam('teacher_ids',-1);
        $child_ids = Yii::app()->request->getParam('child_ids',-1);
        $sort = Yii::app()->request->getParam('sort');
        $is_dynamic = Yii::app()->request->getParam('is_dynamic','0');
        $hide_desc= Yii::app()->request->getParam('hide_desc','0');
        $data = array(
            'id'=>$id,
            'group_id'=>$group_id,
            'flag'=>$flag,
            'name'=>$name,
            'desc'=>$desc,
            'desc2'=>$desc2,
            'color'=>$color,
            'flag_text'=>$flag_text,
            'sort'=>$sort,
            'is_dynamic'=>!empty($is_dynamic) ? 1 : 0,
            'hide_desc'=>!empty($hide_desc) ? 1 : 0,
        );
        if($teacher_ids !== -1){
            $data['teacher_ids'] = $teacher_ids;
        }
        if($child_ids !== -1){
            $data['child_ids'] = $child_ids;
        }
        $this->remote2($requestUrl, $data,'post');
    }

    //组内标签排序
    public function actionLabelSort()
    {
        $requestUrl = 'label/child/labelSort';
        $data = Yii::app()->request->getParam('data');
        $this->remote2($requestUrl, array('data'=>$data,),'post');
    }

    //删除标签
    public function actionDelLabel()
    {
        $requestUrl = 'label/child/delLabel';
        $id = Yii::app()->request->getParam('id');
        $this->remote2($requestUrl, array('id'=>$id),'delete');
    }

    //标签下所有孩子信息
    public function actionGetChildInfo()
    {
        $requestUrl = 'label/child/getChildInfo';
        $flag = Yii::app()->request->getParam('flag');
        $this->remote2($requestUrl, array('flag'=>$flag),'get');
    }

    //所有学校
    public function actionSchoolList()
    {
        $requestUrl = 'label/child/schoolList';
        $this->remote2($requestUrl,array(),'get');
    }

    //校园所有部门和老师
    public function actionSchoolTeacher()
    {
        $requestUrl = 'label/child/schoolTeacher';
        $school_id = Yii::app()->request->getParam('school_id');
        $this->remote2($requestUrl,array('school_id'=>$school_id),'get');
    }

    public function actionSaveChildLabel()
    {
        $type = Yii::app()->request->getParam('type');//1添加 2删除
        $child_ids = Yii::app()->request->getParam('child_ids',array());//孩子id数组
        $label = Yii::app()->request->getParam('label');//标签的位置
        $requestUrl = 'label/child';
        switch ($type){
            case 1:
                $this->remote2($requestUrl,array('child_ids'=>$child_ids,'label'=>$label),'put');
                break;
            case 2:
                $this->remote2($requestUrl,array('child_ids'=>$child_ids,'label'=>$label),'delete');
                break;
            default:
                $this->addMessage('state', 'fail');
                $this->addMessage('message', 'type error');
                $this->addMessage('data', '');
                $this->showMessage();
        }

    }

    public function actionDelDeptChildLabel()
    {
        $requestUrl = 'label/deptChildLabel';
        $school_id = Yii::app()->request->getParam('school_id');
        $flag = Yii::app()->request->getParam('flag');
        $dept = Yii::app()->request->getParam('dept');
        $child_status = Yii::app()->request->getParam('child_status');
        $this->remote2($requestUrl,array('school_id'=>$this->branchId,'flag'=>$flag,'dept'=>$dept,'child_status'=>$child_status),'delete');
    }

    public function actionClassList()
    {
        $grade = Yii::app()->request->getParam('group','');
        $yid = $this->branchObj->schcalendar;
        $data = $this->getClassList($yid,$grade);
        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->showMessage();
    }

    public function getClassList($yid,$grade='')
    {
        if(!empty($grade)){
            if($grade == 'ES'){
                $classType = array('mk','e1','e2','e3','e4','e5');
            }elseif ($grade == 'MS'){
                $classType = array('e6','e7','e8','e9','e10','e11','e12');
            }else{
                $classType = array();
            }
        }else{
            $classType = array('mk','e1','e2','e3','e4','e5','e6','e7','e8','e9','e10','e11','e12');
        }
        $classList = array();
        $models = IvyClass::getClassList($this->branchId, $yid);
        foreach ($models as $class) {
            if(in_array($class->classtype,$classType)){
                $classList[] = array(
                    'classid' => $class->classid,
                    'title' => $class->title,
                    'class_type' => $class->classtype,
                );
            }

        }
        return $classList;
    }

    public function remote2($requestUrl, $requestData = array(),$method)
    {
        if (empty($requestData['school_id'])) {
            $requestData['school_id'] = $this->branchId;
        }
        $res = CommonUtils::requestDsOnline2($requestUrl, $requestData,$method);
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        }
        $this->showMessage();
    }

}