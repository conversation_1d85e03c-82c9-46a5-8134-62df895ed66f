<?php

class DefaultController extends ProtectedController
{
    public $chartsTheme;
	
//	public function actionDashboard(){
	public function actionData(){
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/jquery.number.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/phpfunc.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/jquery.tinysort.min.js');
        $cs->registerScriptFile(Yii::app()->theme->baseUrl.'/js/dashboard.js');
        $cs->registerCssFile( Yii::app()->theme->baseUrl.'/css/css.css');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');

        $this->render('dashboard');
	}

    public function actionDashboard2(){
//    public function actionDashboard(){
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/jquery.number.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/phpfunc.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/jquery.tinysort.min.js');
        $cs->registerScriptFile(Yii::app()->theme->baseUrl.'/js/dashboard.js');


        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/echarts.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/datatables/jquery.dataTables.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/datatables/dataTables.bootstrap.min.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl.'/base/js/datatables/dataTables.bootstrap.min.css');
        
        $cs->registerCssFile( Yii::app()->theme->baseUrl.'/css/css.css');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');

        $this->render('dashboard2');
    }

    public function actionGenerateData(){

        @extract($_POST);
        $result = '';

        $actionType = Yii::app()->request->getParam('actionType','');
        if($actionType == 'getTPL'){
            $result['data'] = $this->renderPartial('container/dashboard_general', null, true);
            $result['type'] = 'replace';
            $result['replaceholder'] = $placeholder;
        }else{
            switch($type){
                case 'general':
                    $timestamp = strtotime($date);
                    $day1 = strtotime('-1 day', $timestamp);
                    $day7 = strtotime('-7 day', $timestamp);
                    $day30 = strtotime('-30 day', $timestamp);

                    $stamps = array($timestamp, $day1, $day7, $day30);

                    $items = Yii::app()->db->createCommand()
                        ->select('*')
                        ->from('ivy_stats_child_count')
                        ->where("classid = 0 and period_timestamp in (".$timestamp.", ".$day1.", ".$day7.", ".$day30.")")
                        ->order('period_timestamp DESC')
                        ->queryAll();

                    $nextNum = array();
                    $_data = array();
                    $data = array();
                    foreach($items as $item){
                        $_data[$item['schoolid']][$item['period_timestamp']] = ($item['num1']+$item['num2']);
                        if(!$nextNum[$item['schoolid']]){
                            $nextNum[$item['schoolid']] = array(
                                'num10' => $item['num10'],
                                'num11' => $item['num11'],
                                'num17' => $item['num17'],
                            );
                        }
                    }
                    foreach($_data as $schoolid=>$_datum){
                        $__data = array();
                        foreach($stamps as $stamp){
                            $__data[] = isset($_datum[$stamp]) ? $_datum[$stamp] : 0;
                        }
                        array_push($data, array(
                            'id'=>$schoolid,
                            'data'=>$__data,
                            'nextNum' => $nextNum[$schoolid],
                        ));
                    }

                    $result = array(
                        'cmd' => 'DataEnrollment = data.data;',
                        'type' => '',
                        'dates' => array($date, date('Y-m-d', $day1), date('Y-m-d', $day7), date('Y-m-d', $day30)),
                        'data' => $data
                    );
                    break;
                case 'target':
                    $criteira = new CDbCriteria();
                    $criteira->compare('period_type', IvyStatsItem::TYPE_M);
                    $criteira->compare('category', IvyStatsItem::CATEGORY_FINANCE);
                    $criteira->compare('code', IvyStatsItem::CODE_FINANCE_ACTUAL);
                    $criteira->compare('subcode', IvyStatsItem::SUBCODE_FINANCE_ENROLLMENT);
                    $criteira->compare('period', date('Ym', strtotime($date)));
                    $items = IvyStatsItem::model()->findAll($criteira);

                    $data = array();
                    foreach($items as $item){
                        $data[$item->schoolid] = $item->data;
                    }
                    $result = array(
                        'cmd' => 'DataTarget = data.data;',
                        'type' => '',
                        'data' => $data
                    );
                    break;
            }
        }


        echo CJSON::encode($result);
    }

    public function actionGenerateCampusFinance(){
        $actionType = Yii::app()->request->getParam('actionType','');
        $result = '';
        if($actionType == 'getTPL'){
            $result['data'] = $this->renderPartial('container/dashboard_campus_finance', null, true);
            $result['type'] = 'append';
//            $result['replaceholder'] = $placeholder;
        }else{
            $schoolId = Yii::app()->request->getParam('schoolid',null);
            $startYear = Yii::app()->request->getParam('startyear',0);
            $tuitionFee = '0.00';
            if(!empty($schoolId)){

                $years = IvyStatsItem::getYearsBySchool($schoolId);

                if(count($years)){
                    $startYear = in_array($startYear, $years)? $startYear : $years[0];
                    $yearlyDataRows = IvyStatsItem::getFinanceYearlyData($schoolId, $startYear);
                    foreach($yearlyDataRows as $_d){
                        $yearlyData[$_d['period']]['c'.$_d['code']]['s'.$_d['subcode']] = $_d;
                    }
                }
                //读学校当前月所在学年的标准月付费金�?
                Yii::import('application.components.policy.*');
                Yii::import('common.models.invoice.*');
                new PolicyApi($schoolId);
                $policy = new IvyPolicy(OA::POLICY_PAY, $startYear, $schoolId);
                $feeConfig = $policy->configs;
                $tuitionFee = isset($feeConfig[TUITION_CALCULATION_BASE][BY_MONTH][AMOUNT][FULL5D]) ? $feeConfig[TUITION_CALCULATION_BASE][BY_MONTH][AMOUNT][FULL5D] : '0.00';

                $result['cmd'] =    'financeData["'.$schoolId.$startYear.'"]={};'.
                    'financeData["'.$schoolId.$startYear.'"]["data"] = data.data;'.
                    'financeData["'.$schoolId.$startYear.'"]["extra"] = data.extra;'.
                    'financeSelectedYear["'.$schoolId.'"]=data.extra.year;';

                $result['extra'] = array(
                    'timestamp'=>time(),
                    'tuition' => $tuitionFee,
                    'year' => $startYear,
                    'years' => $years);
                $result['data'] = $yearlyData;
            }
        }
        echo CJSON::encode($result);

    }

    public function actionGenerateCampusCost(){
        $actionType = Yii::app()->request->getParam('actionType','');
        $result = '';
        if($actionType == 'getTPL'){
            $result['data'] = $this->renderPartial('container/dashboard_campus_cost', null, true);
            $result['type'] = 'append';
        }else{
            $schoolId = Yii::app()->request->getParam('schoolid',null);
            $startYear = Yii::app()->request->getParam('startyear',0);
            $tuitionFee = '0.00';
            if(!empty($schoolId)){

                $years = IvyStatsItem::getYearsBySchool($schoolId);

                if(count($years)){
                    $startYear = in_array($startYear, $years)? $startYear : $years[0];
                    $yearlyDataRows = IvyStatsItem::getFinanceYearlyData($schoolId, $startYear);
                    foreach($yearlyDataRows as $_d){
                        $yearlyData[$_d['period']]['c'.$_d['code']]['s'.$_d['subcode']] = $_d;
                    }
                }
                //读学校当前月所在学年的标准月付费金�?
                Yii::import('application.components.policy.*');
                Yii::import('common.models.invoice.*');
                new PolicyApi($schoolId);
                $policy = new IvyPolicy(OA::POLICY_PAY, $startYear, $schoolId);
                $feeConfig = $policy->configs;
                $tuitionFee = isset($feeConfig[TUITION_CALCULATION_BASE][BY_MONTH][AMOUNT][FULL5D]) ? $feeConfig[TUITION_CALCULATION_BASE][BY_MONTH][AMOUNT][FULL5D] : '0.00';

                $result['cmd'] =    'financeData["'.$schoolId.$startYear.'"]={};'.
                    'financeData["'.$schoolId.$startYear.'"]["data"] = data.data;'.
                    'financeData["'.$schoolId.$startYear.'"]["extra"] = data.extra;'.
                    'financeSelectedYear["'.$schoolId.'"]=data.extra.year;';

                $result['extra'] = array(
                    'timestamp'=>time(),
                    'tuition' => $tuitionFee,
                    'year' => $startYear,
                    'years' => $years);
                $result['data'] = $yearlyData;
            }
        }
        echo CJSON::encode($result);
    }

    public function actionGenerateCampusEnroll(){
        $actionType = Yii::app()->request->getParam('actionType','');
        $result = array();
        if($actionType == 'getTPL'){
            $result['data'] = $this->renderPartial('container/dashboard_campus_enroll', null, true);
            $result['type'] = 'append';
//            $result['replaceholder'] = $placeholder;
        }else{
            Yii::import('common.models.child.StatsChildCount');
            $schoolId = Yii::app()->request->getParam('schoolid',null);
            $specifiedDate = Yii::app()->request->getParam('specifiedDate',null);
            if(!empty($schoolId)){
                $criteria = new CDbCriteria();
                $criteria->compare('t.schoolid', $schoolId);
                $criteria->compare('t.period_timestamp', strtotime($specifiedDate));
                $criteria->order = 'classTitle.child_age asc, classTitle.classid asc';
                $items = StatsChildCount::model()->with('classTitle')->findAll($criteria);
                foreach($items as $item){
                    $inChild = trim($item->childids1.','.$item->childids2.','.$item->childids17, ',') ?
                        explode(',', trim($item->childids1.','.$item->childids2.','.$item->childids17, ',')) : array();

                    $neChild = trim($item->childids10.','.$item->childids11, ',') ?
                        explode(',', trim($item->childids10.','.$item->childids11, ',')) : array();

                    $_c10 = $item->childids10 ? explode(',', $item->childids10) : array();
                    $_c11 = $item->childids11 ? explode(',', $item->childids11) : array();
                    $_c17 = $item->childids17 ? explode(',', $item->childids17) : array();

                    $neChildPaid = array_diff($_c10, $inChild);
                    $neChildUnpaid = array_diff($_c11, $inChild);
                    $diffChild = array_diff($neChild, $inChild);

                    $_inChild = trim($item->childids1.','.$item->childids2, ',') ?
                        explode(',', trim($item->childids1.','.$item->childids2, ',')) : array();

                    $neChildPaidInte = array_intersect(array_diff($_c10, $_c17), $_inChild);
                    $neChildUnpaidInte = array_intersect(array_diff($_c11, $_c17), $_inChild);

                    $intersectChild = array_intersect($neChild, $_inChild);
                    $nextComing = $intersectChild + $diffChild;
                    $incoming = trim($item->childids15.','. $item->childids16.','.$item->childids3.','.$item->childids4.','.$item->childids6.','.$item->childids7) ? explode(',', trim($item->childids15.','. $item->childids16.','.$item->childids3.','.$item->childids4.','.$item->childids6.','.$item->childids7)) : array();;
                    $incoming_paid = trim($item->childids3.','.$item->childids6.','.$item->childids15) ? explode(',', trim($item->childids3.','.$item->childids6.','.$item->childids15)) : array();
                    $incoming_unpaid = trim($item->childids4.','.$item->childids7.','.$item->childids16) ? explode(',', trim($item->childids4.','.$item->childids7.','.$item->childids16)) : array();
                    $_tmp = array();
                    $_tmp['id']=$item->classid;
                    $_tmp['title']=$item->classTitle->title;
                    $_tmp['childData']=array(
                        'incoming' => intval($item->num15+$item->num16+$item->num3+$item->num4+$item->num6+$item->num7),
                        'incomingChildid' => (array_filter($incoming)) ? implode(',', array_filter($incoming)) : '',
                        'incoming_paid' => intval($item->num3+$item->num6+$item->num15),
                        'incoming_paidChildid' => (array_filter($incoming_paid)) ? implode(',', array_filter($incoming_paid)) : '',
                        'incoming_unpaid' => intval($item->num4+$item->num7+$item->num16),
                        'incoming_unpaidChildid' => (array_filter($incoming_unpaid)) ? implode(',', array_filter($incoming_unpaid)) : '',
                        'nextComing' => intval($item->num10+$item->num11-$item->num17),
                        'nextComingChildid' => ($nextComing) ? implode(',', $nextComing) : '',
                        'newNextComing' => intval(count($diffChild)),
                        'newNextComingChildid' => ($diffChild) ? implode(',', $diffChild) : '',
                        'neChildPaid' => intval(count($neChildPaid)),
                        'neChildPaidChildid' => ($neChildPaid) ? implode(',', $neChildPaid) : '',
                        'neChildUnpaid' => intval(count($neChildUnpaid)),
                        'neChildUnpaidChildid' => ($neChildUnpaid) ? implode(',', $neChildUnpaid) : '',
                        'neChildPaidInte' => intval(count($neChildPaidInte)),
                        'neChildPaidInteChildid' => ($neChildPaidInte) ? implode(',', $neChildPaidInte) : '',
                        'neChildUnpaidInte' => intval(count($neChildUnpaidInte)),
                        'neChildUnpaidInteChildid' => ($neChildUnpaidInte) ? implode(',', $neChildUnpaidInte) : '',
                        'intersectChild' => intval(count($intersectChild)),
                        'intersectChildId' => ($intersectChild) ? implode(',', $intersectChild) : '',
                    );
                    $_tmp['status']=$item->classTitle->stat == 10 ? '' : '预备中或已关闭';
                    $_tmp['data']['capacity']=$item->classTitle->capacity;
                    for($i=1;$i<18;$i++){
                        $_tmp['data']['num'.$i] = $item->{'num'.$i};
                        $_tmp['data']['childids'.$i] = $item->{'childids'.$i};
                    }
                    array_push($result, $_tmp);
                }
            }
        }

        echo CJSON::encode($result);

    }

    public function actionDashboard1(){
        $schoolId = Yii::app()->request->getParam('schoolid',null);
        $startYear = Yii::app()->request->getParam('startyear',0);
        $tuitionFee = '0.00';
        if(!empty($schoolId)){

            $years = IvyStatsItem::getYearsBySchool($schoolId);

            if(count($years)){
                $startYear = in_array($startYear, $years)? $startYear : $years[0];
                $yearlyDataRows = IvyStatsItem::getFinanceYearlyData($schoolId, $startYear);
                foreach($yearlyDataRows as $_d){
                    $yearlyData[$_d['period']]['c'.$_d['code']]['s'.$_d['subcode']] = $_d;
                }
            }
            //读学校当前月所在学年的标准月付费金�?
            Yii::import('application.components.policy.*');
            Yii::import('common.models.invoice.*');
            new PolicyApi($schoolId);
            $policy = new IvyPolicy(OA::POLICY_PAY, $startYear, $schoolId);
            $feeConfig = $policy->configs;
            $tuitionFee = isset($feeConfig[TUITION_CALCULATION_BASE][BY_MONTH][AMOUNT][FULL5D]) ? $feeConfig[TUITION_CALCULATION_BASE][BY_MONTH][AMOUNT][FULL5D] : '0.00';
        }
        Yii::app()->clientScript->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        Yii::app()->clientScript->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        Yii::app()->clientScript->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/jquery.number.min.js');

        Yii::app()->clientScript->registerCssFile( Yii::app()->theme->baseUrl.'/css/css.css');

        $this->render('dashboard',array('years'=>$years, 'startYear'=>$startYear, 'yearlyData'=>$yearlyData,'tuitionFee'=>$tuitionFee,'schoolId'=>$schoolId));
    }


    public function actionIndex()
	{
        $this->forward('dashboard'); // 临时做法
        $quarter = '"Sep", "Oct", "Nov"';
        $testData = array(
            'LJ' => array(
                'budgeted_number' => array(186, 191, 196),
                'autual_number' => array(181, 189, 190),
                'budgeted_revenue' => array(648563, 667313, 686063),
                'autual_revenue' => array(664625, 671055, 698380),
            ),
            'OE' => array(
                'budgeted_number' => array(161, 163, 166),
                'autual_number' => array(121, 124, 124),
                'budgeted_revenue' => array(1215253, 1231368, 1255540),
                'autual_revenue' => array(857956, 885537, 901334),
            ),
            'OG' => array(
                'budgeted_number' => array(70, 70, 71),
                'autual_number' => array(64, 65, 67),
                'budgeted_revenue' => array(492507, 492507, 499053),
                'autual_revenue' => array(449017, 460160,484067),
            ),
            'EL' => array(
                'budgeted_number' => array(63,63,63),
                'autual_number' => array(56,55,54),
                'budgeted_revenue' => array(875699, 875699,875699),
                'autual_revenue' => array(716207,653620,717701),
            ),
        );
		$this->render('index', array('testData'=>$testData, 'quarter'=>$quarter));
	}
    
    public function actionRevenue()
    {
        #Sep Revenue
        $revenue = array(
            'sep' => array(
                'LJ' => array(648563, 664625),
                'OE' => array(1215253, 857956),
                'OG' => array(492507, 449017),
                'EL' => array(875699, 716207),
                'GJ' => array(229520, 231599),
                'EC' => array(195822.5, 447328),
                'YJ' => array(245610, 172364),
                'HH' => array(480254, 219136),
                'SQ' => array(576765, 541446),
                'CF' => array(393360, 389802),
                'TT' => array(374447, 285586),
                'TS' => array(455582, 501136.5),
                'SA' => array(307641, 173979),
            ),
            'oct' => array(
                'LJ' => array(667313, 671055),
                'OE' => array(1231368, 885537),
                'OG' => array(492507, 460160),
                'EL' => array(875699, 653620),
                'GJ' => array(251820, 240205),
                'EC' => array(195822.5, 448530),
                'YJ' => array(259410, 178616),
                'HH' => array(485885, 200252),
                'SQ' => array(576765, 546943),
                'CF' => array(393360, 393982),
                'TT' => array(388387, 297872),
                'TS' => array(455582, 498480),
                'SA' => array(309641, 171511),
            ),
            'nov' => array(
                'LJ' => array(686063, 698380),
                'OE' => array(1255540, 901334),
                'OG' => array(499053, 484067),
                'EL' => array(875699, 717701),
                'GJ' => array(254020, 388999),
                'EC' => array(195822.5, 388999),
                'YJ' => array(268610, 176442),
                'HH' => array(485885, 216151),
                'SQ' => array(576765, 546670),
                'CF' => array(393360, 397565),
                'TT' => array(402747, 310118),
                'TS' => array(469222, 518047),
                'SA' => array(311641, 175582),
            ),
        );
        $this->render('revenue', array('revenue'=>$revenue));
    }
    
    public function actionVisit($week=3)
    {
        $schoolId = Yii::app()->request->getParam('schoolid',null);
        $specifiedDate = Yii::app()->request->getParam('specifiedDate',null);

        $nowtime = strtotime($specifiedDate);
        $dateN = date('N');
        $weks = 604800; # 一周的秒数
        $monday = $nowtime - ($dateN-1)*3600*24;
        
        $criteira = new CDbCriteria;
        $criteira->compare('period_type', IvyStatsItem::TYPE_W);
        $criteira->compare('category', IvyStatsItem::CATEGORY_VISIT);
        $criteira->compare('code', '<>'.IvyStatsItem::CODE_VISIT_INVALID);
        $criteira->compare('period', '>='.date('Ymd', $monday-$weks*$week));
        $criteira->compare('period', '<='.date('Ymd', $monday));
        $criteira->compare('schoolid', $schoolId);
        $criteira->order='period desc';
        $items = IvyStatsItem::model()->findAll($criteira);
        
        $data = array();
        $i=0;
        foreach ($items as $item){
            $data['data'][$item->period]['v_'.$item->code]=$item->data;
            $data['data'][$item->period]['period']=$item->period;
            $data['data'][$item->period]['index']=$i++;
        }
        $data['type'] = 'visit';
        echo CJSON::encode($data);
    }
    
    public function actionSchoolData()
    {
        $week = Yii::app()->request->getParam('week', 8);
        
        $nowtime = time();
        $dateN = date('N');
        $weks = 604800; # 一周的秒数
        $monday = ($nowtime - ($dateN-1)*3600*24) - ($week*$weks);
        $thursday = $monday+3600*24*3;
        $i = $thursday;
        while($i<$nowtime){
            $mm = date('Ym', $i);
            $amonth[$mm]=$mm;
            $i+=3600*24;
        }
        
        $thursdaystamp = mktime(0, 0, 0, date('n', $thursday), date('j', $thursday), date('Y', $thursday));
        
        for($i=0; $i<=$week; $i++){
            $amonstamp[$i] = ($thursdaystamp + ($i*$weks)) > $nowtime ? mktime(0, 0, 0, date('n'), date('j'), date('Y')) : $thursdaystamp+ ($i*$weks);
        }
        
        $branchs = Branch::model()->getBranchList();
        
        $criteira = new CDbCriteria;
        $criteira->compare('period_type', IvyStatsItem::TYPE_W);
        $criteira->compare('category', IvyStatsItem::CATEGORY_VISIT);
        $criteira->compare('code', array(IvyStatsItem::CODE_VISIT_NOLOG, IvyStatsItem::CODE_VISIT_ACTIVE));
        $criteira->compare('period', '>='.date('Ymd', $monday-$weks*$week));
        $criteira->order='period asc';
        $items = IvyStatsItem::model()->findAll($criteira);
        $actnolog = array();
        foreach($items as $item){
            foreach ($amonstamp as $ii=>$idata){
                if (!$actnolog[$item->code][$item->schoolid][$ii]){
                    $ttmp = ((strtotime($item->period)+3600*24*3) > $nowtime) ? mktime(0, 0, 0, date('n'), date('j'), date('Y')) : strtotime($item->period)+3600*24*3;
                    $actnolog[$item->code][$item->schoolid][$ii] = ($ttmp == $idata) ? $item->data : 0;
                }
            }
        }
        
        $criteira = new CDbCriteria;
        $criteira->compare('period_type', IvyStatsItem::TYPE_M);
        $criteira->compare('category', IvyStatsItem::CATEGORY_FINANCE);
        $criteira->compare('code', IvyStatsItem::CODE_FINANCE_BUDGET);
        $criteira->compare('subcode', IvyStatsItem::SUBCODE_FINANCE_ENROLLMENT);
        $criteira->compare('period', $amonth);
        $items = IvyStatsItem::model()->findAll($criteira);
        $budgeted = array();
        foreach($items as $item){
            foreach ($amonstamp as $ii=>$idata){
                if (!$budgeted[$item->schoolid][$ii])
                    $budgeted[$item->schoolid][$ii] = ($item->period == date('Ym', $idata)) ? $item->data : 0;
            }
        }
        
        Yii::import('common.models.child.StatsChildCount');
        $criteira = new CDbCriteria;
        $criteira->compare('period_timestamp', $amonstamp);
        $criteira->compare('classid', 0);
        $items = StatsChildCount::model()->findAll($criteira);
        $autual = array();
        foreach($items as $item){
            $autual[$item->schoolid][] = $item->num1 + $item->num2;
        }
        
        echo $this->renderPartial('schooldata', array('branchs'=>$branchs, 'budgeted'=>$budgeted, 'autual'=>$autual, 'week'=>$week, 'actnolog'=>$actnolog, 'monday'=>$monday, 'weks'=>$weks), true);
    }

    public function actionSettings()
    {
        $themeMenu = array(
            'gray'=>array('label'=>'Gray', 'active'=>$this->chartsTheme=='gray'?true:false),
            'grid'=>array('label'=>'Grid', 'active'=>$this->chartsTheme=='grid'?true:false),
            'skies'=>array('label'=>'Skies', 'active'=>$this->chartsTheme=='skies'?true:false),
            'dark-blue'=>array('label'=>'Dark Blue', 'active'=>$this->chartsTheme=='dark-blue'?true:false),
            'dark-green'=>array('label'=>'Dark Green', 'active'=>$this->chartsTheme=='dark-green'?true:false),
        );
        $this->render('settings', array('themeMenu'=>$themeMenu));
    }

    public function actionShowChildren($cids='', $tourl='')
    {
        $items = Yii::app()->db->createCommand()
            ->select('*')
            ->from('ivy_child_profile_basic')
            ->where("childid in (".$cids.")")
            ->order('first_name_en ASC')
            ->queryAll();
        $this->renderPartial('showchildren', array('items'=>$items, 'tourl'=>$tourl));
    }

    public function actionGenerateCampusReport()
    {
        $actionType = Yii::app()->request->getParam('actionType','');
        $result = array();
        if($actionType == 'getTPL'){
            $result['data'] = $this->renderPartial('container/dashboard_campus_report', null, true);
            $result['type'] = 'append';
        }else{
            Yii::import('common.models.portfolio.*');
            Yii::import('common.models.calendar.*');
            Yii::import('common.models.schedule.*');
            Yii::import('common.models.child.StatsChildCount');

            $schoolId = Yii::app()->request->getParam('schoolid',null);
            $specifiedDate = Yii::app()->request->getParam('specifiedDate',null);

            $sTime = strtotime($specifiedDate);
            $monTime = $sTime - ((date('N', $sTime)-1)*86400);

            $criteria = new CDbCriteria();
            $criteria->compare('branchid', $schoolId);
            $items = CalendarSchool::model()->findAll($criteria);
            $yids = array();
            foreach($items as $item){
                $yids[$item->yid] = $item->yid;
            }

            $criteria = new CDbCriteria();
            $criteria->compare('schoolid', $schoolId);
            $criteria->compare('stat', 10);
            $criteria->order='child_age, title';
            $citems = IvyClass::model()->findAll($criteria);
            foreach($citems as $citem){
                $result['classtitle'][]= array(
                    'id' => $citem->classid,
                    'title' => $citem->title,
                );
            }

            $criteria = new CDbCriteria();
            $criteria->compare('monday_timestamp', $monTime);
            $criteria->compare('yid', $yids);
            $model = CalendarWeek::model()->find($criteria);

            $criteria = new CDbCriteria();
            $criteria->compare('type', 10);
            $criteria->compare('stat', 20);
            $criteria->compare('yid', $model->yid);
            $criteria->compare('weeknumber', $model->weeknumber);
            $criteria->compare('classid', 0);
            $criteria->compare('schoolid', $schoolId);
            $count = NotesSchCla::model()->count($criteria);
            if($count)
                $result['schoolreport'] = 1;
            else
                $result['schoolreport'] = 0;

            $criteria = new CDbCriteria();
            $criteria->compare('t.type', 20);
            $criteria->compare('t.stat', 20);
            $criteria->compare('t.yid', $model->yid);
            $criteria->compare('t.weeknumber', $model->weeknumber);
            $criteria->compare('classTitle.schoolid', $schoolId);
            $criteria->order = 'classTitle.child_age asc, classTitle.classid asc';
            $items = NotesSchCla::model()->with('classTitle')->findAll($criteria);
            foreach($items as $item){
                $result['classreport'][$item->classid]=1;
//                $result['classtitle'][$item->classid]=$item->classTitle->title;
            }

            $criteria = new CDbCriteria();
            $criteria->compare('t.yid', $model->yid);
            $criteria->compare('t.weeknumber', $model->weeknumber);
            $criteria->compare('classTitle.schoolid', $schoolId);
            $items = ClassScheduleV2::model()->with('classTitle', 'data')->findAll($criteria);
            foreach($items as $item){
                if(strlen($item->data->data) > 3000){
                    $result['schedule'][$item->classid]=1;
                }
                else{
                    $result['schedule'][$item->classid]=0;
                }
            }

            $criteria = new CDbCriteria();
            $criteria->compare('t.yid', $model->yid);
            $criteria->compare('t.weeknumber', $model->weeknumber);
            $criteria->compare('t.stat', 20);
            $criteria->compare('cInfo.schoolid', $schoolId);
            $items = NotesChild::model()->with('cInfo')->findAll($criteria);
            foreach($items as $item){
                $result['childreport'][$item->classid][$item->childid] = $item->childid;
                if($item->invalid)
                    $result['childreport_invalid'][$item->classid][$item->childid] = $item->childid;
            }

            $criteria = new CDbCriteria();
            $criteria->compare('schoolid', $schoolId);
            $criteria->compare('classid', "<>0");
            $criteria->compare('period_timestamp', strtotime($specifiedDate));
            $items = StatsChildCount::model()->findAll($criteria);
            foreach($items as $item){
                $result['countchild'][$item->classid] = $item->num1+$item->num2;
            }
            $result['type'] = 'teaching';
            $result['oaUrl'] = Yii::app()->urlManager->baseUrl;
            $result['yid'] = $model->yid;
            $result['weeknum'] = $model->weeknumber;
        }
        echo CJSON::encode($result);
    }

    public function actionGenerateCampusLunch()
    {
        $schoolId = Yii::app()->request->getParam('schoolid',null);
        $specifiedDate = Yii::app()->request->getParam('specifiedDate',null);

        $sTime = strtotime($specifiedDate);
        $monTime = $sTime - ((date('N', $sTime)-1)*86400);

        $item = Yii::app()->db->createCommand()
            ->select('*')
            ->from('ivy_catering_menu_weeklink')
            ->where("monday_timestamp = :monday and schoolid = :schoolid", array(':monday'=>$monTime, ':schoolid'=>$schoolId))
            ->queryRow();

        $result = array();
        $result['type'] = 'lunch';
        $result['general_lunch'] = $item['menu_id'] ? 1 : 0;
        $result['special_lunch'] = $item['allergy_id'] ? 1 : 0;
        echo CJSON::encode($result);
    }
    // 导出
    public function actionExport()
    {
        Yii::import('common.models.child.StatsChildCount');
        Yii::import('common.models.Branch');
        Yii::import('common.models.calendar.*');

        $branch = Branch::model()->getBranchList(null, true);

        $time = Yii::app()->request->getParam('time', '');
        if (!$time) {
            return false;
        }
        $y = date('Y',strtotime($time));
        $m = date('m',strtotime($time));

        $start = strtotime(date("$y-$m-01"));
        $end = strtotime(date("$y-$m-01") . ' +1 month -1 day');
        if (time()<$end) {
            $end = strtotime(date('Y-m-d'));
        }
        $lastDay = date('d',$end);

        $title = "$y-$m 在读人数统计";
        header("Content-type:application/vnd.ms-excel");
        header("Content-Disposition:filename=$title.xls");
        echo $this->_t("时间/学校(在读人数)");
        echo "\t";

        foreach ($branch as $schoolId => $schoolName) {
            echo $this->_t($schoolName['abb']);
            echo "\t";
        }
        echo "\n";

        $total = array();
        for ($i=1; $i <= $lastDay; $i++) {
            echo date("$m-$i");
            echo "\t";
            foreach ($branch as $schoolId => $schoolName) {
                $startYear = $m>=9?$y:($y-1);
                $calendar = CalendarSchool::model()->find('startyear=:startyear AND branchid=:branchid',array(':startyear'=>$startYear,':branchid'=>$schoolId));
                foreach ($calendar->months as $v) {
                    if ($v->month == $y.$m) {
                        $schoolDays = $v->schoolday_array;
                        $schoolDays = split(',', $schoolDays);
                        break;
                    }
                }
                foreach ($schoolDays as $schoolDay) {
                    if ($i == $schoolDay) {
                        $day = strtotime(date("$y-$m-$i"));
                        if (in_array(date('N', $day), array(6,7))) {
                            break;
                        }
                        $criteria = new CDbCriteria();
                        $criteria->compare('period_timestamp',$day);
                        $criteria->compare('schoolid',$schoolId);
                        $criteria->compare('classid','0');
                        $data = StatsChildCount::model()->find($criteria);
                        $num = intval($data->num1)+intval($data->num2);
                        if ($num != 0) {
                            $total[$schoolId][] = $num;
                        }
                        echo $num;
                        break;
                    }
                }
                echo "\t";

            }
            echo "\n";
        }

        echo "\n";
        echo $this->_t('平均在读人数')."\t";
        foreach ($branch as $k=>$v) {
            echo round(array_sum($total[$k])/count($total[$k]));
            echo "\t";
        }
    }

    public function actionAttendance($actionType='count')
    {
        $schoolid       = Yii::app()->request->getParam('schoolid', '');
        $specifiedDate  = Yii::app()->request->getParam('specifiedDate', '');

        if ($schoolid && $specifiedDate) {
            $specifiedDateTime = strtotime($specifiedDate);

            $attend = array();
            $sql = "select childid from ivy_child_daily_sign where schoolid='".$schoolid."' and sign_timestamp=".$specifiedDateTime;
            $items = Yii::app()->db->createCommand($sql)->queryAll();
            foreach ($items as $item) {
                $attend[$item['childid']] = $item['childid'];
            }

            $hasInvoce = array();
            $sql = "select childids1,childids2 from ivy_stats_child_count where classid=0 and schoolid='".$schoolid."' and period_timestamp=".$specifiedDateTime;
            $ret = Yii::app()->db->createCommand($sql)->queryRow();
            $hasInvoceStr = $ret['childids1'] ? $ret['childids1'] : '';
            $hasInvoceStr .= $ret['childids2'] ? ','.$ret['childids2'] : '';
            $hasInvoce = explode(',', $hasInvoceStr);

            $diff = array_diff($attend, $hasInvoce);

            echo CJSON::encode(array('type'=>'attendance', 'count'=>count($diff), 'diff'=>implode(',', $diff)));
        }
    }

    //导出表格，字符转�?
    public function _t($str='')
    {
        $str = "\"".$str."\"";
        return iconv("UTF-8", "GBK", $str);
    }

    public function actionGetData2($date='')
    {
        if ($date) {
            Yii::import('common.models.child.StatsChildCount');
            Yii::import('common.models.campusmanage.CampusBudget');

            $ret = array();

            $dateTime = strtotime($date);

            $criteria = new CDbCriteria();
            $criteria->compare('classid', 0);
            $criteria->compare('period_timestamp', $dateTime);
            $criteria->index='schoolid';
            $items = StatsChildCount::model()->findAll($criteria);

            $month = date('Ym', $dateTime);
            $criteria = new CDbCriteria();
            $criteria->compare('classid', 0);
            $criteria->compare('status', 1);
            $criteria->compare('month', $month);
            $criteria->index='schoolid';
            $itemsBudget = CampusBudget::model()->findAll($criteria);

            $schools = array();
            $schools = array_keys($items)+array_keys($itemsBudget);
            if ($schools){
                $criteria = new CDbCriteria();
                $criteria->select='branchid,abb';
                $criteria->compare('branchid', $schools);
                $criteria->compare('type', array(20, 50));
                $criteria->order='`group`';
                $criteria->index='branchid';
                $branchs = Branch::model()->findAll($criteria);

                $mon = mktime(0, 0, 0, date('m', $dateTime), date('d', $dateTime)-date('N', $dateTime)+1, date('Y', $dateTime));

                for($i=4; $i>=0; $i--) {
                    $_t = $mon-604800*$i;
                    $last5Week[ $_t ] = date('Y-m-d', $_t).' 周一';
                }
                $last5Week[$dateTime] = date('Y-m-d', $dateTime);

                $lastyear_t = strtotime('-1 year', $dateTime);

                $lastyearmon = mktime(0, 0, 0, date('m', $lastyear_t), date('d', $lastyear_t)-date('N', $lastyear_t)+1, date('Y', $lastyear_t));

                for($i=count($last5Week); $i>=0; $i--) {
                    $_t = $lastyearmon-604800*$i;
                    $lastyearTime[ $_t ] = $_t;
                }

                foreach ($last5Week as $dt=>$dv) {
                    $dmon = date('Ym', $dt);
                    $monthTarget[$dmon] = $dmon;
                    $lastyearTitle[] = date('m-d', $dt);
                }
                $criteria = new CDbCriteria();
                $criteria->compare('classid', 0);
                $criteria->compare('status', 1);
                $criteria->compare('month', $monthTarget);
                $criteria->order='month';
                foreach (CampusBudget::model()->findAll($criteria) as $budget) {
                    $budgets[$budget->schoolid][$budget->month] = $budget->num;
                }

                $criteria = new CDbCriteria();
                $criteria->compare('classid', 0);
                $criteria->compare('period_timestamp', array_keys($last5Week));
                $criteria->order='period_timestamp';

                foreach (StatsChildCount::model()->findAll($criteria) as $statCount) {
                    $countLast5[$statCount->schoolid]['num1'][$statCount->period_timestamp] = intval($statCount->num1);
                    $countLast5[$statCount->schoolid]['num2'][$statCount->period_timestamp] = intval($statCount->num2);
                }

                $criteria = new CDbCriteria();
                $criteria->compare('classid', 0);
                $criteria->compare('period_timestamp', $lastyearTime);
                $criteria->order='period_timestamp';

                $lastyear5 = array();
                foreach (StatsChildCount::model()->findAll($criteria) as $statCount) {
                    $lastyear5[$statCount->schoolid][] = intval($statCount->num1+$statCount->num2);
                }

                $ret['xAxis'] = array_values($last5Week);

                $dateTime15 = $dateTime-1296000;
                $sql = "select schoolid,count(*) as count from ivy_visits_record where visit_timestamp between ".$dateTime15." and ".$dateTime." group by schoolid";
                $visits = array();
                foreach (Yii::app()->db->createCommand($sql)->queryAll() as $visit) {
                    $visits[$visit['schoolid']] = $visit['count'];
                }

                $sql = "select schoolid,count(*) as count from ivy_visits_trace_log where update_timestamp between ".$dateTime15." and ".$dateTime." group by schoolid";
                $logs = array();
                foreach (Yii::app()->db->createCommand($sql)->queryAll() as $log) {
                    $logs[$log['schoolid']] = $log['count'];
                }

                foreach ($lastyearTime as $_tmp){
                    $nullarr[] =0;
                }

                foreach ($schools as $schoolid) {
                    $unpaid = $items[$schoolid]->num2 ? intval($items[$schoolid]->num2) : 0;
                    $paid = $items[$schoolid]->num1 ? intval($items[$schoolid]->num1) : 0;
                    $target = $itemsBudget[$schoolid]->num ? intval($itemsBudget[$schoolid]->num) : 0;

                    $count5 = array();
                    foreach ($countLast5[$schoolid] as $countKey=>$countItem) {
                        foreach ($last5Week as $weekTime=>$weekText) {
                            $count5[$countKey][] = isset($countItem[$weekTime]) ? intval($countItem[$weekTime]) : 0;
                            $count5['target'][] = isset($budgets[$schoolid][date('Ym', $weekTime)]) ? intval($budgets[$schoolid][date('Ym', $weekTime)]) : 0;
                        }
                    }

                    $count5['title'] = $lastyearTitle;

                    $count5['lastyear'] = isset($lastyear5[$schoolid]) ? $lastyear5[$schoolid] : $nullarr;

                    $inChild = trim($items[$schoolid]->childids1.','.$items[$schoolid]->childids2.','.$items[$schoolid]->childids17, ',') ?
                        explode(',', trim($items[$schoolid]->childids1.','.$items[$schoolid]->childids2.','.$items[$schoolid]->childids17, ',')) : array();

                    $neChild = trim($items[$schoolid]->childids10.','.$items[$schoolid]->childids11, ',') ?
                        explode(',', trim($items[$schoolid]->childids10.','.$items[$schoolid]->childids11, ',')) : array();

                    $_c10 = $items[$schoolid]->childids10 ? explode(',', $items[$schoolid]->childids10) : array();
                    $_c11 = $items[$schoolid]->childids11 ? explode(',', $items[$schoolid]->childids11) : array();
                    $_c17 = $items[$schoolid]->childids17 ? explode(',', $items[$schoolid]->childids17) : array();

                    $neChildPaid = array_diff($_c10, $inChild);
                    $neChildUnpaid = array_diff($_c11, $inChild);
                    $diffChild = array_diff($neChild, $inChild);

                    $_inChild = trim($items[$schoolid]->childids1.','.$items[$schoolid]->childids2, ',') ?
                        explode(',', trim($items[$schoolid]->childids1.','.$items[$schoolid]->childids2, ',')) : array();

                    $neChildPaidInte = array_intersect(array_diff($_c10, $_c17), $_inChild);
                    $neChildUnpaidInte = array_intersect(array_diff($_c11, $_c17), $_inChild);
                    $intersectChild = array_intersect($neChild, $_inChild);

                    $ret['schools'][] = Branch::branchAbb($branchs[$schoolid]->abb);
                    $ret['unpaid'][] = $unpaid;
                    $ret['paid'][] = $paid;
                    $ret['target'][] = $target;
                    $ret['detail'][] = array(
                        'schoolid' => $schoolid,
                        'school' => Branch::branchAbb($branchs[$schoolid]->abb),
                        'unpaid' => $unpaid,
                        'paid' => $paid,
                        'target' => $target,
                        'countLast5' => $count5,
                        'visit' => isset($visits[$schoolid]) ? intval($visits[$schoolid]) : 0,
                        'log' => isset($logs[$schoolid]) ? intval($logs[$schoolid]) : 0,
                        'incoming' => intval($items[$schoolid]->num15+$items[$schoolid]->num16+$items[$schoolid]->num3+$items[$schoolid]->num4+$items[$schoolid]->num6+$items[$schoolid]->num7),
                        'incoming_paid' => intval($items[$schoolid]->num3+$items[$schoolid]->num6+$items[$schoolid]->num15),
                        'incoming_unpaid' => intval($items[$schoolid]->num4+$items[$schoolid]->num7+$items[$schoolid]->num16),
                        'nextComing' => intval($items[$schoolid]->num10+$items[$schoolid]->num11-$items[$schoolid]->num17),
                        'newNextComing' => intval(count($diffChild)),
                        'neChildPaid' => intval(count($neChildPaid)),
                        'neChildUnpaid' => intval(count($neChildUnpaid)),
                        'neChildPaidInte' => intval(count($neChildPaidInte)),
                        'neChildUnpaidInte' => intval(count($neChildUnpaidInte)),
                        'intersectChild' => intval(count($intersectChild)),
                    );
                }
            }

            echo CJSON::encode($ret);
        }
    }

    public function actionDashboard($targetDate = null)
    {
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerCssFile( Yii::app()->themeManager->baseUrl.'/blue/css/css.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/react.production.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/react-dom.production.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/babel.min.js');

        $data = CommonUtils::requestDsOnline2('dailyNumber/'.$targetDate);
        $this->render('dailyNumber', array('data' => $data));
    }
}