<div>
    <div class='flex relative'>
        <div class='dot'></div>
        <span class='stepBorderLeft'></span>
        <div class='ml8 pb24 flex1 width90'>
            <div class='font14 color3 fontBold'><?php echo Yii::t('withdrawal','Send WeChat request form');?></div>
            <div class='mt16 mb16 flex align-items'>
                <span class='flex1 font14 ' >
                    <label class='checkbox-inline'>
                        <input type="checkbox" id='checkAll' name='is_staff' value='1'  onchange='allChecked()' > <?php echo Yii::t('global','Select All');?>
                    </label>
                </span>
                <div class='flex align-items'>
                    <!-- <label class='checkbox-inline mr20'>
                        <input type="checkbox" id='record' name='record' value='1'  onchange='recordChecked()' > 显示互动记录
                    </label> -->
                    <div class='flex1 text-right font14 color3 mr24 relative'>
                        
                        <span class='cur-p qrcodeShow' onclick='showQrcode()'  ><span class='glyphicon glyphicon-qrcode  qrcodeIcon'></span></span>
                        <div id="popover_content_wrapper"  style="display: none;">
                            <div class='flex' >
                                <div class='mr20 text-center'  style='width:150px'>
                                    <div class='font14 color3 text-center'><?php echo Yii::t('newDS','For Preview Only');?></div>
                                    <div  class='font14 color6 text-center mt4 mb5'><?php echo Yii::t('newDS',"Don't Share");?></div>
                                    <div id='qrcodeView'>
                                    </div>
                                </div>
                                <div class='ml20 text-center'  style='width:150px'>
                                    <div class='font14 color3'>分享给家长</div>
                                    <div class='font14 color6 mt4 mb5'>需家长身份才能查看</div>
                                    <div id='qrcodeShare'>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <button type="button" class="btn btn-primary btn-sm" id='sendButton' onclick='sendWechat()'>推送</button>
                </div>
            </div>
            <div class='overscroll'>
                <div id='wechatBox'>
                </div>
            </div>
        </div>
    </div>
    <form class="J_ajaxForm formStu" action="<?php echo $this->createUrl("saveNode");?>" method="post" onsubmit="return check(this)">
        <div class='flex relative'>
            <div class='dot'></div>
            <div class='ml8 flex1 width90'>
                <div class='flex align-items' id='status'>
                    <span class='font14 color3 fontBold'><?php echo Yii::t('withdrawal','Form submission');?></span> 
                    <span class='labelTag redColor redBg'><?php echo Yii::t('withdrawal','Awaiting parent submission');?></span>
                    <!-- <span class='labelTag greenColor greenBg'>已代填写完成</span> -->
                </div>
                <div class="checkbox" id='replaceFill'>
                    <label>
                        <input type="checkbox" class='replaceFill' name='is_staff' value='1'><?php echo Yii::t('withdrawal','Submission on behalf of parent');?>
                    </label>
                </div>
                <div class='borderAppli form-horizontal font14 hide mt20'>
                    <div class='flex flexStart mb20'>
                        <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Applicant');?></div>
                        <div class='flex1 color3' id='stuApply'></div>
                    </div>
                    <div class='flex mb20 flexStart'>
                        <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Withdrawal Date');?></div>
                        <div class='flex1'>
                            <input type="text" class="form-control length_3 pull-left mr20" name='withdrawal_date'   id="dropout_date"   placeholder="<?php echo Yii::t("newDS", "Select a date");?>"  >
                        </div>
                    </div>
                    <div class='flex mb20 flexStart'>
                        <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Where are you transferring to?');?></div>
                        <div class='flex1'>
                            <div class='mb12' id='where'>
                            </div>
                            <div>
                                <input type="text" name='withdrawal_where_memo' class="form-control" placeholder="<?php echo Yii::t('withdrawal','Comment');?>">
                            </div>
                        </div>
                        
                    </div>
                    <div class='flex mb20 flexStart'>
                        <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Reason for withdrawal');?></div>
                        <div class='flex1'>
                            <div class='mb12' id='withdrawal_reason'>
                            </div>
                            <input type="text" class="form-control"  name='withdrawal_reason_memo' placeholder="<?php echo Yii::t('withdrawal','Comment');?>">
                        </div>
                    </div>
                    <div class='flex mb20 flexStart'>
                        <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Are there any aspects of school you are unsatisfied with?');?></div>
                        <div class='flex1'>
                            <textarea class="form-control" name='withdrawal_unsatisfied' rows="3"></textarea>
                        </div>
                    </div>
                    <div class='flex mb20 flexStart'>
                        <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Do you have any advice for us?');?></div>
                        <div class='flex1'>
                            <textarea class="form-control" name='withdrawal_recommendations' rows="3"></textarea>
                        </div>
                    </div>
                    <input type="hidden" name="type" id="type"  value='application_fill'>
                    <input type="hidden" name="applicationId" id="applicationId">
                    <input type="hidden" name="node" id="node">
                    <div class='modal-footer borderTopNone p0'>
                        <label class="checkbox-inline">
                            <input type="checkbox" name="status" value="1"> <?php echo Yii::t('withdrawal', 'Mark this step as completed'); ?>
                        </label>
                        <button type="submit" class="btn btn-primary ml24 submitSend"><?php echo Yii::t('global','Submit');?></button>
                        <button type="button" class="btn btn-primary J_ajax_submit_btn ml24"><?php echo Yii::t('global','Submit');?></button>
                    </div>
                    <!-- <div class='flex align-items'>
                        <input type="hidden" name="type" id="type"  value='application_fill'>
                        <input type="hidden" name="applicationId" id="applicationId">
                        <input type="hidden" name="node" id="node">
                        <div class='flex1 text-right'>
                            <label class="checkbox-inline lableHeight">
                                <input type="checkbox" name="status" value="1"> <?php echo Yii::t('withdrawal', 'Mark this step as completed'); ?>
                            </label>
                        </div>
                        <button type="submit" class="btn btn-primary ml24 submitSend"><?php echo Yii::t('global','Submit');?></button>
                        <button type="button" class="btn btn-primary J_ajax_submit_btn ml24"><?php echo Yii::t('global','Submit');?></button>
                    </div> -->
                </div>
            </div>
        </div>
    </form>
</div>
<script>
    initDate()
    init()
    $(function () {
        $('[data-toggle="popover"]').popover()
    })
    function init(){  
        $('#checkAll').prop('checked', false);
        let send_list=childDetails.forms[0].send_list
        if(send_list.length==0){
            return
        }
        var send_log={}
        var latest_send={}
        if(childDetails.forms[0].data.send_log){
            send_log=childDetails.forms[0].data.send_log
        }
        if(childDetails.forms[0].data.latest_send){
            latest_send=childDetails.forms[0].data.latest_send
        }
        let openids = send_list.map(user => user.openid);
        $.ajax({
            url: '<?php echo $this->createUrl("getParentsFreq") ?>',
            type: "post",
            dataType: 'json',
            data: {
                childId: container.child_id,
                openIds:openids,
            },
            success: function(data) {
                if (data.state == 'success') {
                    var list=''
                    for(let i=0;i<send_list.length;i++){
                        let parentType=send_list[i].parentType=='father'?"<?php echo Yii::t('global','Father');?>":"<?php echo Yii::t('global','Mother');?>"
                        let total=Object.values(data.data[send_list[i].openid]).reduce((acc, curr) => acc + curr, 0)
                        var commentClass = total === 0 ? 'sendTag' : 'sendTagNum';
                        var interactionList = `
                            <div style='width:200px;padding:7px 6px 3px'>
                                <div class='font12 color3 fontBold mb12'>互动次数记录</div>
                                <div class='flex mb8 font12'><span class='flex1 color6'>沟通</span><span class='color3'>${data.data[send_list[i].openid].comment}</span></div>
                                <div class='flex mb8 font12'><span class='flex1 color6'>参与资料收集</span><span class='color3'>${data.data[send_list[i].openid].coll}</span></div>
                                <div class='flex mb8 font12'><span class='flex1 color6'>参与问卷</span><span class='color3'>${data.data[send_list[i].openid].survey}</span></div>
                                <div class='flex  font12'><span class='flex1 color6'>参与调查</span><span class='color3'>${data.data[send_list[i].openid].research}</span></div>
                            </div>
                        `;
                        if(commentClass=='sendTagNum'){
                            var question='<div class="mt12 interaction"><span class="mr8 interactionPopover '+commentClass+'" data-html="true" data-content="'+interactionList+'" tabindex="0" data-placement="bottom"  role="button" data-trigger="focus" data-container="body" data-toggle="popover" >互动 '+total+' 次</span></div>'
                        }else{
                            var question='<div class="mt12 interaction"><span class="mr8 '+commentClass+'" >互动 '+total+' 次</span></div>'
                        }
                        if(latest_send[send_list[i].openid]){
                            list="<div class='wechatList'>"
                            if(latest_send[send_list[i].openid].can_send){
                                list+="<input type='checkbox' onchange='checkSend()' value='"+send_list[i].openid+"' name='sendList'><div class='flex1 ml15'><div class='flex align-items'><img src="+send_list[i].headimgurl+" alt='' class='img28'><div class='font14 color3 ml8'>"+send_list[i].nickname+"</div><div class='font14 color6'>｜"+parentType+"</div></div>"+question+"<div class='font12 color6 mt10 cur-p' onclick='sendLog(this)' data-id="+send_list[i].openid+">最近一次推送："+latest_send[send_list[i].openid].date+" <span class='el-icon-arrow-right'></span></div>"
                            }else{
                                list+="<input type='checkbox'disabled  value='"+send_list[i].openid+"' name='sendList'><div class='flex1 ml10'><div class='flex align-items'><img src="+send_list[i].headimgurl+" alt='' class='img28'><div class='font14 color3 ml8'>"+send_list[i].nickname+"</div><div class='font14 color6'>｜"+parentType+"</div></div>"+question+"<div class='font12 color6 mt10 cur-p' onclick='sendLog(this)' data-id="+send_list[i].openid+">最近一次推送："+latest_send[send_list[i].openid].date+" <span class='el-icon-arrow-right'></span></div>"
                            }
                        
                            if(!latest_send[send_list[i].openid].can_send){
                                list+="<div class='mt5 yellow'>"+latest_send[send_list[i].openid].interval_date+"可再次推送</div></div></div>"
                            }
                        }else{
                            list="<div class='wechatList'><input type='checkbox' onchange='checkSend()' value='"+send_list[i].openid+"' name='sendList'><div class='flex1 ml15'><div class='flex align-items'><img src="+send_list[i].headimgurl+" alt='' class='img28'><div class='font14 color3 ml8'>"+send_list[i].nickname+"</div><div class='font14 color6'>｜"+parentType+"</div></div>"+question+"<div class='font12 color9 mt10'>无发送记录</div></div></div>"
                        }
                        $('#wechatBox').append(list)
                    }
                    $('#record').prop('checked', true);
                    $('.interactionPopover').popover();
                    $('.interactionPopover').on('shown.bs.popover', function () {
                        $(this).addClass('clicked');
                    }).on('hidden.bs.popover', function () {
                        $(this).removeClass('clicked');
                    });
                }
            }
        })
    }
    function recordChecked() {
        if($('#record').prop('checked')){
            $('.interaction').show()
        }else{
            $('.interaction').hide()
        }
    }
    function allChecked(){
        if($('#checkAll').prop('checked')){
            var objs = window.document.getElementsByName("sendList");
            for(var   i=0;i<objs.length;i++){
                if (objs[i].type == "checkbox" && objs[i].disabled==false){
                    objs[i].checked = true;     
                }          
            }
        }else{
            var objs = window.document.getElementsByName("sendList");
            for(var   i=0;i<objs.length;i++){
                if (objs[i].type == "checkbox" && objs[i].disabled==false){
                    objs[i].checked = false;     
                }          
            }
        }
        
    }
    function checkSend(){
        var objs = window.document.getElementsByName("sendList");
        const checkboxes = document.querySelectorAll('input[type="checkbox"][name="sendList"]:checked');
        const values = Array.from(checkboxes).map(checkbox => checkbox.value); 
        if(values.length==objs.length){
            $('#checkAll').prop('checked', true);
        }else{
            $('#checkAll').prop('checked', false);
        }
    }
    function sendWechat(){
        const checkboxes = document.querySelectorAll('input[type="checkbox"][name="sendList"]:checked');
        const values = Array.from(checkboxes).map(checkbox => checkbox.value); 
        if(values.length==0){
            resultTip({
                error: 'warning',
                msg: '请勾选推送人员'
            });
            return
        }
        $('#sendButton').prop('disabled', true);
        $('#sendButton').text('推送中');
        $.ajax({
            url: '<?php echo $this->createUrl("sendAppForm") ?>',
            type: "post",
            dataType: 'json',
            data: {
                applicationId: container.applicationId,
                node:'application',
                task:'application_send',
                openids:values
            },
            success: function(data) {
                if (data.state == 'success') {
                    container.openModal(container.applicationId,'')
                    resultTip({
                        msg: data.state
                    });
                $('#sendButton').prop('disabled', false);
                $('#sendButton').text('推送');
                } else {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                    $('#sendButton').text('推送');
                    $('#sendButton').prop('disabled', false);
                }
            },
            error: function(data) {
                $('#sendButton').text('推送');
                $('#sendButton').prop('disabled', false);
            },
        })
    }
    function sendLog(that){
        let list=$(that).attr('data-id')
        let log=childDetails.forms[0].data.send_log
        var html=''
        for(let i=0;i<log[list].length;i++){
            html+="<div class='flex relative'>"+
                "<div class='dot'></div>"
                if(log[list].length>i+1){
                    html+="<span class='stepBorderLeft'></span>"
                }
                html+="<div class='ml8 pb24 flex1 width90' >"+
                    "<div>"+
                        "<span class='font14 color3 fontBold'>已推送</span>"+
                    "</div>"+
                    "<div class='color3 font14 mt5'>推送时间："+log[list][i].date+"</div>"+
                "</div>"+
            "</div>"
        }
        $('#dateList').html(html) 
        $('#dateListModal').modal('show')
    }
    function initDate() {
        let indexData=container.indexDataList
        for(let key in indexData.whereList){
            let label='<label class="radio-inline lableHeight mr10">'+
                '<input type="radio" name="withdrawal_where" value="'+key+'"> '+indexData.whereList[key]+''+
            '</label>'
            $('#where').append(label)
        }
        for(let key in indexData.reasonList){
            let option='<label class="radio-inline lableHeight mr10">'+
                '<input type="radio" name="withdrawal_reason" value="'+key+'"> '+indexData.reasonList[key]+''+
            '</label>'
            $('#withdrawal_reason').append(option)
        }
        $('#dropout_date').datepicker({
            dateFormat: 'yy-mm-dd',
            selectOtherMonths: false,
            showOtherMonths: false,
            changeMonth: true,
            changeYear: true,
            firstDay: 0,
            onSelect: function (date,str) {
                $('#dropout_date').val(date)
            }
        });
        $('#stuApply').text(childDetails.info.appInfo.staffInfo[childDetails.info.appInfo.currentStaff].name)
        let data=childDetails.forms[0].data
        if(data.length!=0){
            if(data.is_staff=='1'){   
                $('input[name="is_staff"]').prop('checked', true);
                $('.borderAppli').removeClass('hide')
            } else {
                $('.borderAppli').addClass('hide')
            }
            $('input[type="radio"][name="withdrawal_where"][value='+data.withdrawal_where+']').prop('checked', true);
            $('input[type="radio"][name="withdrawal_reason"][value='+data.withdrawal_reason+']').prop('checked', true);
            $('input[name="withdrawal_date"]').val(data.withdrawal_date)
            $('input[name="withdrawal_where_memo"]').val(data.withdrawal_where_memo)
            $('input[name="withdrawal_reason_memo"]').val(data.withdrawal_reason_memo)
            $('textarea[name="withdrawal_unsatisfied"]').val(data.withdrawal_unsatisfied)
            $('textarea[name="withdrawal_recommendations"]').val(data.withdrawal_recommendations)
        }
    }
    $('.replaceFill').on('click', function() {
        if ($(this).prop('checked')) {
            $('.borderAppli').removeClass('hide')
        } else {
            $('.borderAppli').addClass('hide')
        }
    });
    $('.submitSend').show()
    $('.J_ajax_submit_btn').hide()
    function check(_this){
        $('#J_fail_info').remove();
        var button = $(_this).find('button[type="submit"]');
        var flag = true, msg=[];
       
        if($('input[name="withdrawal_date"]').val()==''){
            flag = false;
            msg.push('<?php echo Yii::t('newDS','Please select');?> <?php echo Yii::t("withdrawal","Withdrawal Date");?>');
        }
        if($('input[name="withdrawal_where"]:checked').length == 0){
            flag = false;
            msg.push('<?php echo Yii::t("teaching","Where are you transferring to?");?>');
        }
        if($('input[name="withdrawal_reason"]:checked').length == 0){
            flag = false;
            msg.push('<?php echo Yii::t('newDS','Please select');?> <?php echo Yii::t("withdrawal","Reason for withdrawal");?>');
        }
        if($('textarea[name="withdrawal_unsatisfied"]').val()==''){
            flag = false;
            msg.push('<?php echo Yii::t('withdrawal','Are there any aspects of school you are unsatisfied with?');?>');
        }
        if($('textarea[name="withdrawal_recommendations"]').val()==''){
            flag = false;
            msg.push('<?php echo Yii::t('withdrawal','Do you have any advice for us?');?>');
        }
        if(flag){
            $('.submitSend').hide()
            $('.J_ajax_submit_btn').show()
            $('.formStu .J_ajax_submit_btn').click();
            return false;
        } else{
            $( '<span id="J_fail_info" class="text-warning"><i class="glyphicon glyphicon-remove text-warning"></i> ' + msg.join('、') + '</span>' ).appendTo(button.parent()).fadeIn( 'fast' );
            return false;
        }
    }
    function showQrcode(){
        if ($("#popover_content_wrapper").is(":visible")) {
            $('#popover_content_wrapper').toggle()
        }else{
            $('#qrcodeView').html('')
            $('#qrcodeShare').html('')
            $.ajax({
                url: '<?php echo $this->createUrl("getQrcode") ?>',
                type: "post",
                dataType: 'json',
                data: {
                    applicationId: container.applicationId,
                    type:'form',
                },
                success: function(data) {
                    if (data.state == 'success') {
                        $('#qrcodeView').qrcode({
                            width:120,
                            height:120,
                            text:data.data.qrcodePreviewUrl,
                        });
                        $('#qrcodeShare').qrcode({
                            width: 120,
                            height: 120,
                            text:data.data.qrcodeUrl,
                        });
                        $('#popover_content_wrapper').toggle()
                    } else {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                },
            })
        }
    }
</script>
<style>
    .wechatList{
        height: 135px;
        background: #FAFAFA;
        border-radius: 4px;
        display:inline-flex;
        padding:16px;
        margin-right:16px;
        align-items: flex-start;
    }
    .overscroll{
    }
    #wechatBox{
        overflow-x: auto;
        white-space: nowrap;
        padding-bottom:10px
    }
    #wechatBox::-webkit-scrollbar {
        height:8px; /* 设置滚动条的高度 */
    }
    #wechatBox::-webkit-scrollbar-track {
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0);
        background   : #fff;
        border-radius: 10px;
        border:none
    }
    #wechatBox::-webkit-scrollbar-thumb {
        border-radius   : 10px;
        background-color: #ccc;
        background-image: none
    }
    .width90{
        width:90%
    }
    .qrcodeIcon{
        font-size: 18px;
        padding: 6px;
        background: #FAFAFA;
        border-radius: 4px;
    }
    #popover_content_wrapper{
        padding: 24px 48px;
        position: absolute;
        right: 0;
        top:35px;
        background: #fff;
        border-radius: 4px;
        border:1px solid #fafafa;
        box-shadow:0px 0px 10px 0px #ccccccc4;
    }
    .sendTagNum{
        padding:4px 6px;
        font-size:12px;
        border-radius: 2px;
        background:#F0F5FB;
        color: #4D88D2;
    }
    .sendTag{
        padding:4px 6px;
        font-size:12px;
        border-radius: 2px;
        background:#F2F3F5;
        color: #333;
    }
    .interactionPopover{
        cursor: pointer;
    }
    .interactionPopover:hover{
        background: #4D88D2;
        color: #fff;
    }
    .clicked {
        background: #4D88D2;
        color: #fff;
    }
    .popover{
        border:none !important;
        z-index:1999 !important
    }
    .popover.bottom > .arrow{
        border-bottom-color: #fff;
        top: -10px;
    }
</style>