<?php if (!$show):?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Teaching Tasks'), array('//mteaching/default/index'));?></li>
        <li><?php echo Yii::t('newDS', 'SS Transcript');?></li>
    </ol>
    <div class="row">
        <div class="col-md-12">
            <div class="alert alert-warning" role="alert">Sorry, transcript is for secondary school only.</div>
        </div>
    </div>
</div>
<?php else:?>
<div class="container-fluid" id='container'>
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Teaching Tasks'), array('//mteaching/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('newDS','SS Transcript');?></li>
    </ol>
    <div class="row flex">
        <div class='col-md-4 col-sm-4 maxWidth ' >
            <div class='menu'  :style="'height:'+(height+30)+'px;'">
            <div class='flex flex_de'>
                <div class='flex1  overflow-y scroll-box'>
                    <div>
                        <div class='flex'>
                            <el-select v-model="year" placeholder="<?php echo Yii::t('global','Please Select') ?>" size='small' class='flex1' @change='getCourse'>
                                <el-option
                                v-for="(item,id) in yearList"
                                :key="item.key"
                                :label="item.value"
                                :value="item.key">
                                </el-option>
                            </el-select>
                            <div class='ml20'><button type="button" :disabled='setBtn'  size='small' class="btn btn-primary" @click='setCourse'><?php echo Yii::t("reg", "Options"); ?></button></div>
                        </div>
                        <hr>
                        <div v-if='setTableData.gradeCourseList'>  
                            <div class='mb24'>
                                <div class='font14 color3 mb12 font600'><?php echo Yii::t("user", "Grade"); ?></div>
                                <el-select v-model="grade" placeholder="<?php echo Yii::t('global','Please Select') ?>" size='small' @change='getChild' class='width100'>
                                    <el-option
                                    v-for="(item,id) in gradeList"
                                    :key="item.grade"
                                    :label="item.title"
                                    :value="item.grade">
                                    </el-option>
                                </el-select>
                            </div>
                            <div class='mb24'>
                                <div class='font14 color3 mb12 font600'><?php echo Yii::t("ptc", "Student"); ?></div>
                                <el-select v-model="childId" placeholder="<?php echo Yii::t('global','Please Select') ?>" size='small' @change='getstudentGrades' class='width100'>
                                    <el-option
                                    v-for="(item,id) in childList"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id">
                                    <div class='align-items flex mt5 mb5'>
                                    <img :src="item.avatar" class='avatar32 ' alt="">
                                    <span class='ml10'>{{ item.name }}</span>
                                    </div>
                         
                                    </el-option>
                                </el-select>
                            </div>
                            <div v-loading="stuGradeListLoading">
                                <div v-if='stuGradeList.length!=0'>
                                    <div class='font14 color3 mb12 font600'><?php echo Yii::t('newDS','Select Grade') ?></div>
                                    <div>
                                    <el-checkbox-group 
                                        @change='getList'
                                        v-model="yidList"
                                        :max="4">
                                        <el-checkbox v-for='(list,index) in stuGradeList' :label="list" :key="index">{{list.title}}</el-checkbox>
                                    </el-checkbox-group>
                                        <!-- <div class="checkbox" v-for='(list,index) in stuGradeList'>
                                            <label class='font14'>
                                                <input type="checkbox" :value="list" v-model='yidList' @change='getList'>{{list.title}}
                                            </label>
                                        </div> -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div  v-if='Object.keys(studentYidList).length>0' class='flex mt24'>
                    <button type="button" class="btn btn-primary btn-block flex1" @click='setNotes'><?php echo Yii::t('newDS','Generate Transcript');?></button>
                    <button type="button" class="btn btn-default ml10" @click='historyList'><?php echo Yii::t('newDS','Previous Versions');?></button>
                </div>
            </div>
            </div>
        </div>
        <div class='col-md-8 col-sm-8 flex1 overflow-y scroll-box' v-loading="loading"  :style="'height:'+(height+30)+'px;overflow-y: auto;'">
            <div class="flex alert alert-warning" role="alert"><?php echo Yii::t('newDS', 'Transcript Rules:<br/>1. G6-G12: Score will be obtained in the following order until it is available (final score - fourth score - second score). If no score is available, the subject will not be shown.'); ?></div>
            <div class="panel panel-default" v-for='(list,key,index) in studentYidList'>
                <div class="panel-heading text-center"> 
                    <div class='flex align-items'>
                        <div class='flex1 text-center color3 font16'>{{getTitlte(key)[0].title}} </div> 
                        <button type="button"  size='small' class="btn btn-primary text-right" @click='editCourse(getTitlte(key)[0])'><?php echo Yii::t("lunch", "Edit"); ?></button>
                    </div> 
                </div>
                <div class="panel-body">
                <el-table
                    :data="list"
                    empty-text='<?php echo Yii::t("ptc", "No Data"); ?>'
                    :header-cell-style="{background:'#F7F7F8',color:'#333'}"
                    style="width: 100%">
                    <el-table-column
                        prop="title"
                        label="<?php echo Yii::t('ptc','Subject') ?>"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="grade"
                        label="Grade"
                        align='center'
                        width="200">
                    </el-table-column>
                    <el-table-column
                        prop="credit"
                        width="100"
                        align='center'
                        label="Credit">
                        <template slot-scope="scope">
                            <span v-if='scope.row.customCredit!="" && scope.row.customCredit!=null'> {{scope.row.customCredit}}</span> 
                            <span v-else>{{scope.row.credit}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="name"
                        align='center'
                        label="Student Completion"
                        width="150">
                        <template slot-scope="scope">
                        <span v-if='scope.row.completion!=""'> {{completionConfig[scope.row.completion].title}}</span> 
                        </template>
                    </el-table-column>
                </el-table>
                </div>
            </div>
        </div>
    </div>
    <!-- 设置 -->
    <div class="modal fade"  id="subjectModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel"  aria-labelledby="wantSayModalLabel"  data-backdrop="static">
        <div class="modal-dialog  modal-lg" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title"><?php echo Yii::t("reg", "Options"); ?></h4>
            </div>
            <div class="modal-body p24 overflow-y scroll-box " :style="'max-height:'+(height)+'px;overflow-x: hidden;'">
                <div v-if='setTableData.gradeCourseList'>
                    <div>
                        <div >
                        <span  class="btnTab btn " v-for='(item,key1,idx) in setTableData.gradeCourseList' :class='tableIndex==idx?"btn-primary":"btnHover"' @click='showTableItem(idx)'>{{showTitle('yearList',year,'key','value')}} {{showTitle('gradeList',key1,'grade','title','e')}}</span>
                        </div>
                        <el-table
                            :header-cell-style="{background:'#F7F7F8',color:'#333'}"
                                :data="tableItem"
                                class='mb24 mt20'
                                style="width: 100%">
                                <el-table-column
                                    prop="title"
                                    label="<?php echo Yii::t('ptc','Subject') ?>">
                                    <template slot-scope="scope">
                                        <span v-if='setTableData.courseInfoList[scope.row.id]'>  {{setTableData.courseInfoList[scope.row.id].title}}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column
                                    prop="name"
                                    label="Credit"
                                    width="180">
                                    <template slot-scope="scope">
                                        <el-input v-model="scope.row.credit" size='small'  placeholder="<?php echo Yii::t('leave','Input') ?>"></el-input>
                                    </template>
                                </el-table-column>
                                <el-table-column
                                    prop="address"
                                    width="180"
                                    label="<?php echo Yii::t("asa", "Status"); ?>">
                                    <template slot-scope="scope">
                                    <el-select v-model="scope.row.show"  placeholder="<?php echo Yii::t('global','Please Select') ?>" size='small' class='flex1'>
                                        <el-option
                                    v-for="(item,id) in showList"
                                    :key="item.id"
                                    :label="item.title"
                                    :value="item.id">
                                    </el-option>
                                    </el-select>
                                    
                                    </template>
                                </el-table-column>
                            </el-table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                <button type="button" class="btn btn-primary" @click='saveCredit()' :disabled='btnLoading'><?php echo Yii::t("global", "Save"); ?></button>
            
            </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
    <!-- 编辑 -->
    <div class="modal fade"  id="editModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel"  aria-labelledby="wantSayModalLabel"  data-backdrop="static">
        <div class="modal-dialog  modal-lg" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title"><?php echo Yii::t("lunch", "Edit"); ?></h4>
            </div>
            <div class="modal-body p24 overflow-y scroll-box " v-if='editTablist.tableData' :style="'max-height:'+(height)+'px;overflow-x: hidden;'">
                <div class="alert alert-warning" role="alert"><span class='glyphicon glyphicon-exclamation-sign mr4'></span> <?php echo Yii::t('newDS','Please set course credit in settings before select a course.') ?> </div>
                <div class='flex'>
                    <div><img :src="crrentChild[0].avatar" alt="" class='avatar42'></div>
                    <div class='flex1 mt4 ml8'>
                        <div class='color3 font14'>{{crrentChild[0].name}}</div>
                        <div class='color6 font12'>{{editTablist.title}}</div>
                    </div>
                </div> 
                <div class='mt20 mb12 flex align-items'>
                    <el-switch
                        v-model="textColorShow"
                        @change='closeColor'
                        active-color="#428bca">
                    </el-switch>
                    <span class='font14 color3 ml8'><?php echo Yii::t("attends", "Yearly Watermark");?></span> 
                </div>  
                <div class='flex align-items textColorShow' v-if='textColorShow'>
                    <label class="radio-inline">
                        <input type="radio" class='input' v-model='textColorValue'  value="1" @change='watchTextColor'> <span style='color:#F6140F'>PROVISIONAL</span> 
                    </label>
                    <label class="radio-inline ml24">
                        <input type="radio" class='input' v-model='textColorValue' value="2"  @change='watchTextColor'> <span style='color:#CECECE'>UNOFFICIAL</span>
                    </label>
                    <label class="radio-inline ml24">
                        <input type="radio" class='input' v-model='textColorValue' value="3"  @change='watchTextColor'> <span class='color6'><?php echo Yii::t("attends", "Customize");?></span>
                    </label>
                    <span class='ml16 color6' v-if='textColorValue=="3"'><?php echo Yii::t("attends", "Text");?>：</span>
                    <el-input v-if='textColorValue=="3"' v-model="textColor.text" size='small' placeholder="<?php echo Yii::t("leave", "Input");?>" class='length_3'></el-input>
                    <span class='ml24 color6' v-if='textColorValue=="3"'><?php echo Yii::t("attends", "Color");?>：</span>
                    <el-color-picker v-model="textColor.color" size="mini" v-if='textColorValue=="3"'></el-color-picker>
                    <button type="button" class="btn btn-primary ml24" @click='saveTextColor()'  :disabled='btnLoading'><?php echo Yii::t("global", "Save"); ?></button>
                </div>  
                <div class='mt20 font14 color3 mb12'>
                    <div class='flex subTitle mb15'><span class='line'></span><span class='flex1 color3 font14 font600 ml5'>{{editTablist.title}}</span></div>
                </div>  
                <div>
                    <table class='table table-hover tableData'>
                        <thead>
                            <tr>
                                <th  width="50">
                                    <input type="checkbox" v-model='allCheckBox' @change='checkAll($event)'>
                                </div>
                                </th>
                                <th width="60%"><?php echo Yii::t('ptc','Subject') ?></th>
                                <th>Grade</th>
                                <th width="550">credit</th>
                                <th width="60" class='text-center'><?php echo Yii::t("campus", "Sort");?></th>
                            </tr>
                        </thead>
                        <tbody class='table_count' > 
                            <tr v-for='(list,index) in editTablist.tableData'>
                                <td>
                                    <input type="checkbox" v-model='showlist' :value='list.course_id' :disabled='list.credit==0?true:false' :checked='list.show==1?true:false' @change='checkShow(list)'>
                                </td>
                                <td>
                                    <div class='color3'>
                                        {{list.title}}
                                    </div>
                                    <div class='flex mt5 align-items'>
                                    <span class='color6'><?php echo Yii::t("attends", "Alias");?>：</span><el-input v-model="list.customTitle" size='small' placeholder="<?php echo Yii::t("leave", "Input");?>" class='length_5'></el-input>
                                    </div>
                                </td>
                                <td>
                                    <span>{{list.grade}}</span>
                                </td>
                                <td>
                                    <span class='color9' v-if='list.credit==0'><?php echo Yii::t('newDS','Not Set');?></span>
                                    <span v-else><?php echo Yii::t("attends", "For calculation");?>：{{list.credit}}</span>
                                    <div class='flex mt5 align-items'>
                                        <span class='color6'><?php echo Yii::t("attends", "For display");?>：</span><el-input v-model="list.customCredit" size='small' placeholder="<?php echo Yii::t("leave", "Input");?>" class='length_2'></el-input>
                                    </div>
                                </td>
                                <td class='text-center'><span class='el-icon-rank cur-p handle'></span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>  
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                <button type="button" class="btn btn-primary" @click='saveCourse()'  :disabled='btnLoading'><?php echo Yii::t("global", "Save"); ?></button>
            </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
    <!-- 生成成绩单 -->
    <div class="modal fade"  id="noteModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel"  aria-labelledby="wantSayModalLabel"  data-backdrop="static">
        <div class="modal-dialog  modal-lg" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title"><?php echo Yii::t('newDS','Generate Transcript');?></h4>
            </div>
            <div class="modal-body p24 overflow-y scroll-box " :style="'max-height:'+(height)+'px;overflow-x: hidden;'">
                <div class="form-horizontal">
                    <!-- <div class="form-group">
                        <label for="inputEmail3" class="col-sm-4 control-label">
                            <div v-if='showEdit' class='flex'>
                                <el-input
                                    class='textarea'
                                    size='small'
                                    placeholder="<?php echo Yii::t('leave','Input') ?>"
                                    v-model="diplomaCopy">
                                </el-input>
                                <button type="button" class="btn btn-link btn-xs ml5" @click='showEdit=false'>取消</button>
                                <button type="button" class="btn btn-link btn-xs" @click='showEdit=false;pursuing_title=diplomaCopy'>确认</button>

                            </div>
                            <span v-if='!showEdit' class='pt4 inline-block'  @click='showEdit=true;diplomaCopy=pursuing_title'>
                                <span class='colorBlue cur-p'>{{pursuing_title}}</span>
                                <span class='el-icon-edit ml5 colorBlue cur-p'></span>
                           </span>
                        </label>
                        <div class="col-sm-8">
                            <el-input
                                class='textarea pt7'
                                size='small'
                                placeholder="<?php echo Yii::t('leave','Input') ?>"
                                v-model="pursuing_text">
                            </el-input>
                        </div>
                    </div> -->
                    <div class="form-group" v-if='scoreData.fatherName'>
                        <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t('labels','Father Name') ?></label>
                        <div class="col-sm-10 mt10">
                            <span v-if='scoreData.fatherName.cn_name!=""' class='mr10'><span><?php echo Yii::t('labels','Chinese Name');?>：</span><span>{{scoreData.fatherName.cn_name}}</span></span>
                            <span v-if='scoreData.fatherName.en_firstname!=""'  class='mr10'><span><?php echo Yii::t('labels','First Name');?>：</span><span>{{scoreData.fatherName.en_firstname}}</span></span>
                            <span v-if='scoreData.fatherName.en_lastname!=""'  class='mr10'><span><?php echo Yii::t('labels','Last Name');?>：</span><span>{{scoreData.fatherName.en_lastname}}</span></span>
                        </div>
                    </div>
                    <div class="form-group"  v-if='scoreData.fatherName'>
                        <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t('labels','Mother Name') ?></label>
                        <div class="col-sm-10 mt10">
                            <span v-if='scoreData.motherName.cn_name!=""'  class='mr10'><span><?php echo Yii::t('labels','Chinese Name');?>：</span><span>{{scoreData.motherName.cn_name}}</span></span>
                            <span v-if='scoreData.motherName.en_firstname!=""'  class='mr10'><span><?php echo Yii::t('labels','First Name');?>：</span><span>{{scoreData.motherName.en_firstname}}</span></span>
                            <span v-if='scoreData.motherName.en_lastname!=""'  class='mr10'><span><?php echo Yii::t('labels','Last Name');?>：</span><span>{{scoreData.motherName.en_lastname}}</span></span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="inputEmail3" class="col-sm-2 control-label">Parent(s)</label>
                        <div class="col-sm-10">
                            <el-input
                                class='textarea'
                                size='small'
                                placeholder="<?php echo Yii::t('leave','Input') ?>"
                                v-model="parentName">
                            </el-input>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="inputPassword3" class="col-sm-2 control-label">Enrollment Date</label>
                        <div class="col-sm-10">
                            <el-date-picker
                            size='small'
                                v-model="graduationDate"
                                type="date"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                                placement="bottom-start"
                                placeholder="<?php echo Yii::t('ptc','Select a date') ?>">
                            </el-date-picker>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="inputPassword3" class="col-sm-2 control-label">Graduation/Withdraw Date</label>
                        <div class="col-sm-10">
                            <el-date-picker
                                size='small'
                                v-model="WithdrawDate"
                                type="date"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                                placement="bottom-start"
                                placeholder="<?php echo Yii::t('ptc','Select a date') ?>"> 
                            </el-date-picker>
                            <span class='ml24'>
                                <el-checkbox v-model="checked"></el-checkbox>
                                <el-input v-model="endStr" v-if='checked' class='length_3 ml10' size='small' placeholder="请输入内容"></el-input>
                            </span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="inputPassword3" class="col-sm-2 control-label">IB Diploma</label>
                        <div class="col-sm-10">
                            <div class='mt8 flex align-items'>
                                <el-switch
                                    v-model="pursuing"
                                    active-color="#428bca">
                                </el-switch>
                                <span class='font14 color3 ml8'><?php echo Yii::t("newDS", "Display");?></span> 
                            </div>
                            <div class='textColorShow mt12' v-if='pursuing'>
                                <div class='flex'>
                                    <span class='mt9 mr10'><?php echo Yii::t("global", "Title");?>：</span>
                                    <span class='flex1'>
                                        <div>
                                            <label class="radio-inline">
                                                <input type="radio"  v-model="pursuingTitle"  value="1"><span class='color6'>Pursuing Full IB Diploma</span>
                                            </label>
                                            <label class="radio-inline ml24">
                                                <input type="radio"  v-model="pursuingTitle" value="2"><span class='color6'>Earned Full IB Diploma</span>
                                            </label>
                                        </div>
                                        <div class='mt5 flex align-items' style='height:32px'>
                                            <label class="radio-inline">
                                                <input type="radio"  v-model="pursuingTitle" value="3"><span class='color6'><?php echo Yii::t("attends", "Customize");?></span>
                                            </label>
                                            <el-input v-if='pursuingTitle=="3"' v-model="pursuing_title" size='small' placeholder="<?php echo Yii::t("leave", "Input");?>" class='length_4 ml12 mt4'></el-input>
                                        </div>
                                    </span>
                                </div>  
                                <div class='flex mt12'>
                                    <span class='mt9 mr10'><?php echo Yii::t("labels", "Content");?>：</span>
                                    <span class='flex1'>
                                        <label class="radio-inline">
                                            <input type="radio"  v-model="pursuing_text" value="Yes"> <span class='color6'>Yes</span>
                                        </label>
                                        <label class="radio-inline ml24">
                                            <input type="radio"  v-model="pursuing_text" value="No"> <span class='color6'>No</span>
                                        </label>
                                        <label class="radio-inline ml24">
                                            <input type="radio"  v-model="pursuing_text" value="Blank"> <span class='color6'>Blank</span>
                                        </label>
                                    </span>
                                </div>            
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="inputPassword3" class="col-sm-2 control-label"><?php echo Yii::t("asa", "Status"); ?></label>
                        <div class="col-sm-10">
                            <el-select v-model="graduation" placeholder="<?php echo Yii::t('global','Please Select') ?>" size='small' class='flex1'>
                                <el-option
                                v-for="(item,id) in stateList"
                                :key="item.key"
                                :label="item.value"
                                :value="item.key">
                                </el-option>
                            </el-select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="inputPassword3" class="col-sm-2 control-label"><?php echo Yii::t('application','Notes') ?></label>
                        <div class="col-sm-10">
                            <el-input
                                type="textarea"
                                :rows="3"
                                class='textarea'
                                placeholder="<?php echo Yii::t('leave','Input') ?>"
                                v-model="notes">
                            </el-input> 
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="inputPassword3" class="col-sm-2 control-label"><?php echo Yii::t('payment','Issue Date') ?></label>
                        <div class="col-sm-10">
                            <el-date-picker
                            size='small'
                                v-model="issuedDate"
                                type="date"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                                placement="bottom-start"
                                placeholder="<?php echo Yii::t('ptc','Select a date') ?>">
                            </el-date-picker>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                <button type="button" class="btn btn-primary" @click='saveNotes()'><?php echo Yii::t("global", "OK"); ?></button>
            
            </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
     <!-- 生成记录 -->
     <div class="modal fade"  id="historyModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel"  aria-labelledby="wantSayModalLabel" >
        <div class="modal-dialog" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title"><?php echo Yii::t('newDS','Previous Versions');?></h4>
            </div>
            <div class="modal-body p24 overflow-y scroll-box " v-if='historyData.items' :style="'max-height:'+(height)+'px;overflow-x: hidden;'">
                <div class='flex align-items mb24' v-if='crrentChild[0]'>
                    <div><img :src="crrentChild[0].avatar" alt="" class='avatar42'></div>
                    <div class='flex1 mt4 ml8'>
                        <div class='color3 font14'>{{crrentChild[0].name}}</div>
                    </div>
                </div> 
                <div v-if='historyData.items.length>0'>
                    <div v-for='(list,index) in historyData.items' class='flex history'>
                        <span class='flex1'>Generated at {{list.created}} by {{historyData.staffInfo[list.created_by].name}}</span>
                        <span class='ml10'><button type="button" class="btn btn-link" @click='openUrl(list)'><?php echo Yii::t("newDS", "Open"); ?></button></span>
                    </div>
                </div>
                <div v-else>
                    <div class="alert alert-warning" role="alert"><?php echo Yii::t("ptc", "No Data"); ?></div>
                </div>
            </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
</div>
<script>
    var height=document.documentElement.clientHeight;
    var container = new Vue({
        el: "#container",
        data: {
            height:height-150,
            notes:'',
            yearList:[],
            gradeList:[],
            completionConfig:{},
            year:'',
            grade:'',
            childId:'',
            childList:[],
            setTableData: [],
            showList:[
                {id:0,title:'<?php echo Yii::t('newDS','Hide');?>'},
                {id:1,title:'<?php echo Yii::t('newDS','Display');?>'}
            ],
            stuGradeList:[],
            yidList:[],
            editTablist: [],
            setBtn:true,
            crrentChild:[],
            studentCourseData:[],
            studentYidList:{},
            pursuing:false,
            loading: false,
            btnLoading:false,
            tableItem:[],
            tableIndex:0,
            graduationDate:'',//入学时间
            WithdrawDate:'',//毕业或者转校
            issuedDate:'',
            stateList:[
                {key:1,value:'<?php echo Yii::t('newDS','Graduation');?>'},
                {key:2,value:'<?php echo Yii::t('newDS','Withdraw');?>'},
            ],
            graduation:'',
            historyData:{},
            stuGradeListLoading:false,
            parentName:'',
            
            scoreData:{},
            input:'',
            allCheckBox:false,
            showlist:[],
            selectTable:[],
            checked:false,
            endStr:'',
            textColor:{},
            textColorShow:false,
            showEdit:false,
            diplomaCopy:'',
            pursuing_text:'',
            pursuing_title:'',
            pursuingTitle:'',
            textColorValue:'',
            textColorCopy:{}
        },
        watch:{
        },
        created: function() {
            this.initData()
           
        },
        computed: {},
        methods: {
            showTitle(data,type,id,title,grade){
                if(grade){
                    type='e'+type
                }
                for(var i=0;i<this[data].length;i++){
                    if(this[data][i][id]==type){
                        return this[data][i][title]
                    }
                }
            },
            initData(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        url:'config',
                        year:this.year
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.yearList=data.data.yearList
                            that.year=data.data.yearList[0].key
                            that.gradeList=data.data.gradeList
                            that.completionConfig=data.data.completionConfig
                            that.getCourse()
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {},
                })
            },
            check(data,type ) {
                for (let item in data) {
                    if (typeof data[item] == "object") {
                        if (data[item].id) {
                            if(type=='show'){                           
                                if(this.setTableData.gradeCourseList[data[item].id]){
                                    Vue.set(data[item], 'credit', this.setTableData.gradeCourseList[data[item].id].credit);
                                    Vue.set(data[item], 'show', this.setTableData.gradeCourseList[data[item].id].show);
                                }else{
                                    Vue.set(data[item], 'credit', '');
                                    Vue.set(data[item], 'show', '');
                                }
                            }else{
                                if(data[item].credit!='' || data[item].show!=''){
                                    type.push({key:data[item].key,show:data[item].show,credit:data[item].credit})
                                }
                            }

                        }
                    this.check(data[item],type);
                    }
                }
                return type;
            },
            getCourse(){
                let that=this
                this.setBtn=true
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        url:'courseList',
                        year:this.year
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.setTableData=data.data
                            that.check(data.data.courseList,'show');
                            that.getChild()
                            that.setBtn=false
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {},
                })
            },
            setCourse(){
                $('#subjectModal').modal('show')
                this.tableIndex=0
                this.tableItem=Object.values(this.setTableData.gradeCourseList)[0]
            },
            showTableItem(index){
                this.tableIndex=index
                this.tableItem=Object.values(this.setTableData.gradeCourseList)[index]
            },
            saveCredit(){
                let that=this
                let result=that.check(this.setTableData.gradeCourseList,[]);
                this.btnLoading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        url:'creditSave',
                        data:result,
                        year:this.year,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg: data.message
                            });
                           $('#subjectModal').modal('hide')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.btnLoading=false
                    },
                    error: function(data) {
                        that.btnLoading=false
                    },
                })
            },
            getChild(){
                if(this.grade!=''){
                    this.childList=this.setTableData.gradeStudentList[this.grade]
                }
                this.childId=''
                this.stuGradeList=[]
                this.yidList=[]
                this.editTablist={}
                this.studentYidList={}
            },
            getstudentGrades(){
                let that=this
                this.crrentChild=this.childList.filter((i) => i.id==this.childId)
                this.stuGradeListLoading=true
                that.studentYidList={}
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        url:'studentGrades',
                        year:this.year,
                        childId:this.childId,
                        grade:this.grade
                    },
                    success: function(data) {
                        that.stuGradeListLoading=false
                        if (data.state == 'success') {
                            that.stuGradeList=data.data.gradesList
                            that.yidList=[]
                            that.pursuing=data.data.pursuing==1?true:false
                            that.notes=data.data.notes
                            that.graduation=data.data.graduation
                            that.WithdrawDate=data.data.endDate
                            that.graduationDate=data.data.startDate    
                            that.parentName=data.data.parentName   
                            // if(data.data.pursuing_text==''){
                            //     that.pursuing_text='Blank'
                            // }else{
                                that.pursuing_text=data.data.pursuing_text
                            // }
                            if(!that.pursuing || data.data.pursuing_title==''){
                                that.pursuingTitle='1'
                            }else{
                                if(data.data.pursuing_title=='Pursuing Full IB Diploma'){
                                    that.pursuingTitle='1'
                                }else if(data.data.pursuing_title=='Earned Full IB Diploma'){
                                    that.pursuingTitle='1'
                                }else{
                                    that.pursuingTitle='3'
                                } 
                            }
                            that.pursuing_title=data.data.pursuing_title
                            that.endStr=data.data.endStr
                            that.checked = (data.data.endStr !== '' && data.data.endStr !== null) && !(data.data.endDate !== '' && data.data.endDate !== null);
                            that.scoreData=data.data  
                            that.yidList=data.data.gradesList.slice(-4)
                            that.getList()
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.stuGradeListLoading=false
                    },
                })
            },
            getList(){
               if(this.yidList.length==0){
                this.studentYidList=[]
                return
               }
                let list=[]
                this.yidList.forEach(item => {
                    list.push(item.yid)
                });
                this.loading=true
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        url:'studentCourseList',
                        yidList:list,
                        childId:this.childId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.studentYidList=data.data
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.loading=false
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.loading=false
                    },
                    
                })
            },
            getTitlte(key){
                let data=this.stuGradeList.filter((i) => i.yid==key)
                if(data.length!=0){
                    return data
                }else{
                    return data=[]
                }
            },
            editCourse(data){
                this.editTablist=data
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        url:'studentCourseConfig',
                        yid:data.yid,
                        childId:this.childId,
                        classType:data.grade
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            $('#editModal').modal('show')
                            Vue.set(that.editTablist, 'tableData',data.data);
                            that.$nextTick(()=>{
                                that.selectTable=[]
                                that.showlist=[]
                                that.editTablist.tableData.forEach(item => {
                                    if(item.is_config!=0){
                                        that.selectTable.push(item);
                                        if(item.show==1){
                                            that.showlist.push(item.course_id)
                                        }
                                    }
                                });
                                if(that.selectTable.length==that.showlist.length){
                                    that.allCheckBox=true
                                }else{
                                    that.allCheckBox=false
                                }
                                const el = document.querySelector('.table_count')
                                new Sortable(el, {
                                    animation: 150,
                                    handle: '.handle',
                                    ghostClass: 'blue-background-class',
                                    onEnd: function ({ newIndex, oldIndex }) { //拖拽完毕之后发生该事件
                                        var list=JSON.parse( JSON.stringify (that.editTablist.tableData))
                                        list.splice(newIndex, 0, list.splice(oldIndex, 1)[0])
                                        var newArray = list.slice(0)
                                        that.editTablist.tableData = []
                                        that.$nextTick(function () {
                                            that.editTablist.tableData = newArray
                                        })
                                    }
                                });
                            })
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {},
                })
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        url:'transcriptWatermark',
                        yid:data.yid,
                        childId:this.childId,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.textColor=data.data
                           that.textColorCopy=JSON.parse(JSON.stringify(data.data))
                           if(data.data.color!='' || data.data.text!=''){
                            if(data.data.text=='PROVISIONAL' && data.data.color=='#F6140F'){
                                that.textColorValue='1'
                            }else if(data.data.text=='UNOFFICIAL' && data.data.color=='#CECECE'){
                                that.textColorValue='2'
                            }else{
                                that.textColorValue='3'
                            }
                            that.textColorShow=true
                           }else{
                             that.textColorValue=''
                             that.textColorShow=false
                           }
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        that.btnLoading=false
                    },
                })
            },
            watchTextColor(){
                if(this.textColorValue=='3' && ((this.textColor.text=='UNOFFICIAL'  && this.textColor.color=='#CECECE') || (this.textColor.text=='PROVISIONAL' && this.textColor.color=='#F6140F'))){
                    this.textColor.text=''
                    this.textColor.color=''
                }
            },
            closeColor(){
                if(this.textColorShow || (this.textColorCopy.color=='' && this.textColorCopy.text=='')){
                    this.textColorValue=''
                    return
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        url:'transcriptWatermarkSave',
                        yid:this.editTablist.yid,
                        childId:this.childId,
                        text:'',
                        color:''
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            this.textColorValue=''
                           resultTip({
                                msg: data.message
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        that.btnLoading=false
                    },
                })
            },
            saveTextColor(){
                let that=this
                if(this.textColorValue=='1'){
                    this.textColor.text='PROVISIONAL'
                    this.textColor.color='#F6140F'
                }
                if(this.textColorValue=='2'){
                    this.textColor.text='UNOFFICIAL'
                    this.textColor.color='#CECECE'
                }
                if(this.textColorValue=='3'){
                    if(this.textColor.text==''){
                        resultTip({
                            error: 'warning',
                            msg: '<?php echo Yii::t('leave','Input') ?>'+' '+'<?php echo Yii::t("attends", "Text");?>'
                        });
                        return
                    }
                    if(this.textColor.color==''){
                        resultTip({
                            error: 'warning',
                            msg:'<?php echo Yii::t('leave','Input') ?>'+' '+'<?php echo Yii::t("attends", "Color");?>'
                        });
                        return
                    } 
                }
               
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        url:'transcriptWatermarkSave',
                        yid:this.editTablist.yid,
                        childId:this.childId,
                        text:this.textColor.text,
                        color:this.textColor.color 
                    },
                    success: function(data) {
                        
                        if (data.state == 'success') {
                           resultTip({
                                msg: data.message
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        that.btnLoading=false
                    },
                })
            },
            checkAll(event){
                this.showlist=[]
                this.editTablist.tableData.forEach(item => {
                    if(item.is_config!=0){
                        if(event.target.checked){
                            item.show=1
                            this.showlist.push(item.course_id)
                        }else{
                            item.show=0
                        }
                    }
                });
            },
            checkShow(){
                if(this.selectTable.length==this.showlist.length){
                    this.allCheckBox=true
                }else{
                    this.allCheckBox=false
                }
            },
            handleSelectionChange(val) {
               this.studentCourseData=val
            },
            selectable(row, index) {
                if (row.is_config == 0) {
                    return false  
                } else {
                    return true
                }
            },
            saveCourse(){
                let dataList=[]
                let that=this
                let showList=[]
                // this.studentCourseData.forEach(item => {
                //     showList.push(item.course_id)
                // });
                that.editTablist.tableData.forEach((item,index) => {
                    if(this.showlist.indexOf(item.course_id)!=-1){
                        dataList.push({course_id:item.course_id,show:1,customTitle:item.customTitle,customCredit:item.customCredit,customWeight:index+1})
                    }else{
                        dataList.push({course_id:item.course_id,show:0,customTitle:item.customTitle,customCredit:item.customCredit,customWeight:index+1})
                    }
                });
                if(dataList.length==0){
                    resultTip({
                        error: 'warning',
                        msg: '科目列表为空不可提交'
                    });
                    return
                }
                this.btnLoading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        url:'studentCourseConfigSave',
                        yid:this.editTablist.yid,
                        childId:this.childId,
                        data:dataList
                    },
                    success: function(data) {
                        that.btnLoading=false
                        if (data.state == 'success') {
                            that.getList()
                            $('#editModal').modal('hide')
                            resultTip({
                                msg: data.state
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        that.btnLoading=false
                    },
                })
                
            },
            setNotes(){
                var now = new Date()
                var year = now.getFullYear() // 得到年份
                var month = now.getMonth() // 得到月份
                var date = now.getDate() // 得到日期
                month = month + 1
                month = month.toString().padStart(2, '0')
                date = date.toString().padStart(2, '0')
                var defaultDate = `${year}-${month}-${date}`
                this.issuedDate=defaultDate
                $('#noteModal').modal('show')                
            },
            saveNotes(){
                let that=this
                let list=[]
                this.yidList.forEach(item => {
                    list.push(item.yid)
                });
                var pursuingTitle=''
                if(that.pursuingTitle=='1'){
                    pursuingTitle='Pursuing Full IB Diploma'
                }else if(that.pursuingTitle=='2'){
                    pursuingTitle='Earned Full IB Diploma'
                }else{
                    pursuingTitle=this.pursuing_title
                }
                if(!this.pursuing){
                    pursuingTitle=''
                    this.pursuing_text=''
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        url:'transcriptGenerate',
                        yidList:list,
                        childId:this.childId,
                        pursuing:this.pursuing?'1':'0',
                        pursuing_text:this.pursuing_text,
                        pursuing_title:pursuingTitle,
                        notes:this.notes,
                        graduation:this.graduation,
                        startDate:this.graduationDate,
                        endDate:!this.checked?this.WithdrawDate:'',
                        year:this.year,
                        parentName:this.parentName,
                        issued:this.issuedDate,
                        endStr:this.checked?this.endStr:''
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            $('#noteModal').modal('hide')      
                            // that.pursuing=1
                            // that.notes=''
                            // that.graduation=''
                            // that.WithdrawDate=''
                            // that.graduationDate=''       
                           window.open('<?php echo $this->createUrl('preview') ?>&id='+data.data)
                           resultTip({
                                msg: data.state
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {},
                })
            },
            historyList(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        url:'transcriptHistory',
                        childId:this.childId,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.historyData=data.data
                            $('#historyModal').modal('show')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {},
                })
            },
            openUrl(data){
                window.open('<?php echo $this->createUrl('preview') ?>&id='+data.id)
            }
        }
        
    })
</script>
<style>
    .maxWidth{
        max-width:300px;
    }
    .menu{
        background: #F7F7F8;
        border-radius: 4px;
        padding: 16px;
    }
    .subTitle{
        align-items: center;
    }
    .subTitle .line{
        width: 6px;
        height: 14px;
        background: #4D88D2;
        border-radius: 1px;
    }
    .avatar32{
        width: 32px;
        height: 32px;
        border-radius: 50%;
    }
    .avatar42{
        width:42px;
        height: 42px;
        border-radius: 50%;
    }
    .flex_de{
        height:100%;
        flex-direction: column;
    }
    .scroll-box::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius   : 10px;
        background-color: #ccc;
        background-image: none
    }
    .scroll-box::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0);
        background   : #fff;
        border-radius: 10px;
        border:none
    }
    .width100{
        width:100%
    }
    textarea{
        padding:5px 12px !important
    }
    .font600{
        font-weight:600
    }
    .el-select-dropdown__item{
        height:auto !important
    }
    .btnTab{
        margin: 4px 0;
        display: inline-block;
        margin-bottom: 0;
        font-weight: normal;
        text-align: center;
        vertical-align: middle;
        cursor: pointer;
        background-image: none;
        border: 1px solid transparent;
        white-space: nowrap;
        padding: 6px 12px;
        font-size: 12px;
        line-height: 1.42857143;
        border-radius: 4px;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
    }
    .btnHover:hover{
        background: rgba(51,51,51,0.1)
    }
    .el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner{
        background-color: #4D88D2 !important;
        border-color: #4D88D2 !important;
    }
    .history{
        border: 1px solid #ccc;
        padding: 12px;
        border-radius: 5px;
        margin-top: 12px;
        align-items: center;
        font-size:12px
    }
    .el-checkbox__input.is-checked+.el-checkbox__label{
        color: #606266;
    }
    .el-switch__core{
        height:18px;
        width:35px !important;
    }
    .el-switch.is-checked .el-switch__core::after{
        margin-left: -15px;
    }
    .el-switch__core:after{
        width: 14px;
        height: 14px;
    }
    .tableData thead{
        background:#F7F7F8;
        color:#333;
       
    }
    .tableData thead th, .tableData tbody td{
        vertical-align: middle !important;
        font-size:14px
    }
    .colorBlue{
        color:#428bca
    }
    .textColorShow{
        background: #F7F7F8;
        border-radius: 4px;
        padding:16px 20px
    }
    .mt9{
        margin-top:9px
    }
    .input[type="radio"] {
        -webkit-appearance: none; */
        appearance: none;
        width: 13px;
        height: 13px;
        border: 1px solid #428bca;
        border-radius: 50%;
    }
    .input[type="radio"]:checked::after {
        position: relative;
        content: "";
        top: 2px;
        left: 2px;
        width: 7px;
        height: 7px;
        display: block;
        border-radius: 50%;
        visibility: visible;
        background-color: #428bca;
        z-index: 6;
    }
</style>
<?php endif;?>