<?php
//$labelMapping = IvyStatsItem::getFinanceType();
//$this->renderPartial('_finance_data_template');

$targetDate = Yii::app()->request->getParam('targetDate', date('Y-m-d'));

$tr = '
        <tr>
            <th width="80">' . Yii::t('labels','Campus') . '</th>
            <th width="200">' . 
                sprintf("%s / %s / %s", 
                    Yii::t('dashboard', 'Paid'),
                    Yii::t('dashboard', 'Invoices'),
                    Yii::t('dashboard', 'Target')
                ) . '</th>
            <th width="100">' . Yii::t('dashboard', 'Progress') . '</th>
            <th width="100">' . Yii::t('dashboard', 'Gap') . '</th>
            <th width="100">' . Yii::t('dashboard', 'Paid Ratio') . '</th>
            <th width="">' . Yii::t('dashboard', 'Trend (5 weeks)') . '</th>
            <th width="200">' . sprintf("%s", Yii::t('dashboard', 'Upcoming')) . '</th>
        </tr>
        ';
?>


<div class="container-fluid dashboard-page" id="container-general">
<div class="row">
    <div class="col-md-12">
        <p class="form-inline">
            <input type="text" id="datepicker" class="form-control" value="<?php echo $targetDate;?>">
        </p>
    <h3><?php echo Yii::t('dashboard', 'Total Numbers');?>
    <span class="pull-right">
        <span><?php echo Yii::t('dashboard', 'Paid');?> <span class="badge" id="paid_total">0</span></span>
        <span><?php echo Yii::t('dashboard', 'Invoices');?> <span class="badge" id="invoice_total">0</span></span>
        <span><?php echo Yii::t('dashboard', 'Target');?> <span class="badge" id="target_total">0</span></span>
    </span></h3>

    <div id="main" style="width: 100%;height:400px;"></div>

    <h3><?php echo Yii::t('dashboard', 'INTERNATIONAL SCHOOLS');?>
        <div class="btn-group btn-group-xs international_school_item" role="group" style="margin-bottom: 5px;">
            <a href="javascript:pictonumbat('international_school_item', 2);" class="btn btn-default active"><?php echo Yii::t('dashboard', 'Data');?></a>
            <a href="javascript:pictonumbat('international_school_item', 1);" class="btn btn-default"><?php echo Yii::t('dashboard', 'Chart');?></a>
        </div>
    <span class="pull-right">
        <span><?php echo Yii::t('dashboard', 'Paid');?> <span class="badge" id="paid_international">0</span></span>
        <span><?php echo Yii::t('dashboard', 'Invoices');?> <span class="badge" id="invoice_international">0</span></span>
        <span><?php echo Yii::t('dashboard', 'Target');?> <span class="badge" id="target_international">0</span></span>
    </span></h3>
    <table class="table table-striped table-bordered table-school" style="width:100%">
        <thead>
            <?php echo $tr; ?>
        </thead>
        <tbody id="international_school_item">
        </tbody>
        <tfoot>
            <?php echo $tr; ?>
        </tfoot>
    </table>

    <h3><?php echo Yii::t('dashboard', 'PH SCHOOLS');?>
        <div class="btn-group btn-group-xs local_school_item" role="group" style="margin-bottom: 5px;">
            <a href="javascript:pictonumbat('local_school_item', 2);" class="btn btn-default active"><?php echo Yii::t('dashboard', 'Data');?></a>
            <a href="javascript:pictonumbat('local_school_item', 1);" class="btn btn-default"><?php echo Yii::t('dashboard', 'Chart');?></a>
        </div>
        <span class="pull-right">
    <span><?php echo Yii::t('dashboard', 'Paid');?> <span class="badge" id="paid_local">0</span></span>
    <span><?php echo Yii::t('dashboard', 'Invoices');?> <span class="badge" id="invoice_local">0</span></span>
    <span><?php echo Yii::t('dashboard', 'Target');?> <span class="badge" id="target_local">0</span></span>
    </span></h3>
    <table class="table table-striped table-bordered table-school" style="width:100%">
        <thead>
            <?php echo $tr; ?>
        </thead>
        <tbody id="local_school_item">
        </tbody>
        <tfoot>
            <?php echo $tr; ?>
        </tfoot>
    </table>

    <h3><?php echo Yii::t('dashboard', 'DAYSTAR ACADEMY');?>
        <div class="btn-group btn-group-xs daystar_school_item" role="group" style="margin-bottom: 5px;">
            <a href="javascript:pictonumbat('daystar_school_item', 2);" class="btn btn-default active"><?php echo Yii::t('dashboard', 'Data');?></a>
            <a href="javascript:pictonumbat('daystar_school_item', 1);" class="btn btn-default"><?php echo Yii::t('dashboard', 'Chart');?></a>
        </div>
        <span class="pull-right">
    <span><?php echo Yii::t('dashboard', 'Paid');?> <span class="badge" id="paid_daystar">0</span></span>
    <span><?php echo Yii::t('dashboard', 'Invoices');?> <span class="badge" id="invoice_daystar">0</span></span>
    <span><?php echo Yii::t('dashboard', 'Target');?> <span class="badge" id="target_daystar">0</span></span>
    </span></h3>
        <table class="table table-striped table-bordered table-school" style="width:100%">
            <thead>
                <?php echo $tr; ?>
            </thead>
            <tbody id="daystar_school_item">
            </tbody>
            <tfoot>
                <?php echo $tr; ?>
            </tfoot>
        </table>
    </div>
</div>

</div>

<div class="modal" id="overall-progress">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body">
                <div class="text-center hint">Loading Data...</div>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<script id="item-template" type="text/template">
    <tr id="school_<%= index%>" data-index="<%= index%>">
    <td><h3 class="text-primary">
            <a target="_blank" href="<?php echo $this->createUrl('data');?>?schoolid=<%= schoolid%>"><%= school%></a>
        </h3></td>
    <td>
        <div class="progress">
            <%
                var sumpu = paid+unpaid;
                if (sumpu > target && target>0) {
                    var paid_percent = (paid/sumpu*100).toFixed();
                    var unpaid_percent = (unpaid/sumpu*100).toFixed();
                    var target_percent = 0;
                } else {
                    var paid_percent = (paid/target*100).toFixed();
                    var unpaid_percent = (unpaid/target*100).toFixed();
                    var target_percent = ((target-paid-unpaid)/target*100).toFixed();
                }
            %>
            <div class="progress-bar progress-bar-success" style="width: <%= paid_percent%>%">
                <%= target ? (paid/target*100).toFixed() : 0%>%
            </div>
            <div class="progress-bar progress-bar-warning progress-bar-striped" style="width: <%= unpaid_percent%>%">
                <%= target ? (unpaid/target*100).toFixed() : 0%>%
            </div>
            <div class="progress-bar progress-bar-empty progress-bar-striped" style="width: <%= target_percent%>%">
                <%= target ? ((target-unpaid-paid)/target*100).toFixed() : 0%>%
            </div>
        </div>
        <h3><%= paid%> <small> <%= unpaid+paid%> / <%= target%> </small></h3>
    </td>
    <td>
        <h3>
            <span class="label label-<%= target ? (((paid+unpaid)/target*100).toFixed() >= 100 ? 'success' : 'danger') : 'danger'%>">
                <%= target ? ((paid+unpaid)/target*100).toFixed() : 0%>%
            </span>
        </h3>
    </td>
        <td data-order="<%= (paid+unpaid)-target%>">
            <h3>
                <span class="label label-<%= (paid+unpaid)-target >= 0 ? 'success' : 'danger' %>"><%= (paid+unpaid)-target%></span>
            </h3>
        </td>
        <td>
            <h3>
                <span class="label label-<%= paid ? ((paid/(paid+unpaid)*100).toFixed() >= 100 ? 'success' : 'danger') : 'danger'%>">
                    <%= paid ? (paid/(paid+unpaid)*100).toFixed() : 0 %>%
                </span>
            </h3>
        </td>
        <td>
            <div class="btn-group btn-group-xs" role="group" id="switch-<%= index%>" style="margin-bottom: 5px;">
                <a href="javascript:pictonum(<%= index%>, 2)" class="btn btn-default active"><?php echo Yii::t('dashboard', 'Data');?></a>
                <a href="javascript:pictonum(<%= index%>, 1)" class="btn btn-default"><?php echo Yii::t('dashboard', 'Chart');?></a>
            </div>
            <div id="main-<%= index%>" style="min-height:170px;display: none;"></div>
            <div id="campustable-<%= index%>" style="min-height: 170px;">
                <table class="table table-striped table-bordered" style="width:100%;">
                    <tbody>
                    <tr>
                        <th></th>
                        <% for(var i=0; i<countLast5.title.length; i++){%>
                        <th><%= countLast5.title[i]%></th>
                        <% }%>
                    </tr>
                    <tr>
                        <th><?php echo Yii::t('dashboard', 'Paid');?></th>
                        <% for(var i=0; i<countLast5.title.length; i++){%>
                        <td align="right"><%= countLast5.num1[i]%></td>
                        <% }%>
                    </tr>
                    <tr>
                        <th><?php echo Yii::t('dashboard', 'Unpaid');?></th>
                        <% for(var i=0; i<countLast5.title.length; i++){%>
                        <td align="right"><%= countLast5.num2[i]%></td>
                        <% }%>
                    </tr>
                    <tr>
                        <th><?php echo Yii::t('dashboard', 'Target');?></th>
                        <% for(var i=0; i<countLast5.title.length; i++){%>
                        <td align="right"><%= countLast5.target[i]%></td>
                        <% }%>
                    </tr>
                    <tr>
                        <th><?php echo Yii::t('dashboard', 'SPLY');?></th>
                        <% for(var i=0; i<countLast5.title.length; i++){%>
                        <td align="right"><%= countLast5.lastyear[i]%></td>
                        <% }%>
                    </tr>
                    </tbody>
                </table>            
            </div>
        </td>
    <td>
        <table class="the-table" style="width:100%;">
            <tbody>
            <tr>
                <td rowspan="2"><?php echo Yii::t('dashboard', 'This Y');?><br><%= incoming%></td>
                <td colspan="2"><%= incoming_paid%> <?php echo Yii::t('dashboard', 'Paid');?></td>
            </tr>
            <tr>
                <td colspan="2"><%= incoming_unpaid%> <?php echo Yii::t('dashboard', 'Unpaid');?></td>
            </tr>
            <tr>
                <td rowspan="4"><?php echo Yii::t('dashboard', 'Next Y');?><br><%= nextComing%></td>
                <td rowspan="2"><?php echo Yii::t('dashboard', 'Returning');?><br><%= intersectChild%></td>
                <td><%= neChildPaidInte%> <?php echo Yii::t('dashboard', 'Paid');?></td>
            </tr>
            <tr>
                <td><%= neChildUnpaidInte%> <?php echo Yii::t('dashboard', 'Unpaid');?></td>
            </tr>
            <tr>
                <td rowspan="4"><?php echo Yii::t('dashboard', 'New');?><br><%= newNextComing%></td>
                <td><%= neChildPaid%> <?php echo Yii::t('dashboard', 'Paid');?></td>
            </tr>
            <tr>
                <td><%= neChildUnpaid%> <?php echo Yii::t('dashboard', 'Unpaid');?></td>
            </tr>
            </tbody>
        </table>
    </td>
<!--    <td><%= visit%></td>-->
<!--    <td><%= log%></td>-->
    </tr>
</script>

<script>

$(document).ready(function() {
    $( "#datepicker" ).datepicker({
        dateFormat: 'yy-mm-dd',
        onSelect: function (date) {
            location.href = "<?php echo $this->createUrl('dashboard')?>?targetDate="+date;
            //getData2(date);
        }
    });

    getData2('<?php echo $targetDate;?>');
} );    


// 指定图表的配置项和数据
var option = {
    width: 'auto',
    height: 'auto',
    tooltip : {
        trigger: 'axis',
        axisPointer : {            // 坐标轴指示器，坐标轴触发有效
            type : 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
        }
    },
    legend: {
        data:['<?php echo Yii::t('dashboard', 'Paid');?>','<?php echo Yii::t('dashboard', 'Unpaid');?>','<?php echo Yii::t('dashboard', 'Target');?>']
    },
    grid: {
        left: '1%',
        right: '1%',
        bottom: '1%',
        containLabel: true
    },
    xAxis : [
        {
            type : 'category',
            data : []
        }
    ],
    yAxis : [
        {
            type : 'value'
        }
    ],
    series : [
        {
            name:'<?php echo Yii::t('dashboard', 'Paid');?>',
            type:'bar',
            stack: 'total',
            data:[]
        },
        {
            name:'<?php echo Yii::t('dashboard', 'Unpaid');?>',
            type:'bar',
            stack: 'total',
            data:[]
        },
        {
            name:'<?php echo Yii::t('dashboard', 'Target');?>',
            type:'bar',
            barWidth: 5,
            data:[]
        }
    ]
};

var option1 = {
    width: 'auto',
    height: 'auto',    
    title: {
        text: ''
    },
    tooltip : {
        trigger: 'axis',
        axisPointer: {
            type: 'cross',
            label: {
                backgroundColor: '#6a7985'
            }
        }
    },
    legend: {
        data:['<?php echo Yii::t('dashboard', 'Paid');?>','<?php echo Yii::t('dashboard', 'Unpaid');?>','<?php echo Yii::t('dashboard', 'Target');?>']
    },
    toolbox: {
        feature: {
            saveAsImage: {}
        }
    },
    grid: {
        left: '5%',
        right: '5%',
        bottom: '3%',
        containLabel: false
    },
    xAxis : [
        {
            type : 'category',
            boundaryGap : false,
            data : []
        }
    ],
    yAxis : [
        {
            type : 'value'
        }
    ],
    series : [
        {
            name:'<?php echo Yii::t('dashboard', 'Paid');?>',
            type:'line',
            stack: 'total',
            areaStyle: {},
            data:[]
        },
        {
            name:'<?php echo Yii::t('dashboard', 'Unpaid');?>',
            type:'line',
            stack: 'total',
            areaStyle: {},
            data:[]
        },
        {
            name:'<?php echo Yii::t('dashboard', 'Target');?>',
            type:'line',
            areaStyle: {opacity: 0.5},
            data:[]
        }
    ]
};

// 基于准备好的dom，初始化echarts实例
var myChart = echarts.init(document.getElementById('main'), 'light');
var chartList = [];

function getData2(date)
{
    $('#overall-progress').modal({
        backdrop: 'static'
    });

    $.getJSON("<?php echo Yii::app()->createUrl('//charts/default/getData2') ;?>", {date: date}, function (data) {
        console.log(data);
        option.xAxis[0].data = data.schools;
        option.series[0].data = data.paid;
        option.series[1].data = data.unpaid;
        option.series[2].data = data.target;
        myChart.setOption(option);

        $('#international_school_item').html('');
        $('#daystar_school_item').html('');
        $('#local_school_item').html('');

        option1.xAxis[0].data = data.xAxis;

        var international_school = ['BJ_IASLT', 'BJ_OE', 'BJ_OG', 'BJ_CP', 'TJ_EB', 'NB_HH', 'NB_FJ'];
        var daystar_school = ['BJ_SLT', 'BJ_DS', 'BJ_QFF'];

        var paid_international=0,invoice_international=0,target_international=0,paid_total=0,invoice_total=0,target_total=0,paid_local=0,invoice_local=0,target_local=0,paid_daystar=0,invoice_daystar=0,target_daystar=0;

        for (var i=0; i<data.schools.length; i++) {
            data.detail[i]['index'] = i;
            var item = _.template( $('#item-template').html(), data.detail[i]);
            if(international_school.indexOf(data.detail[i].schoolid) != -1){
                $('#international_school_item').append(item);

                paid_international += data.detail[i].paid;
                invoice_international += (data.detail[i].unpaid+data.detail[i].paid);
                target_international += data.detail[i].target;

            }
            else if(daystar_school.indexOf(data.detail[i].schoolid) != -1){
                $('#daystar_school_item').append(item);

                paid_daystar += data.detail[i].paid;
                invoice_daystar += (data.detail[i].unpaid+data.detail[i].paid);
                target_daystar += data.detail[i].target;
            }
            else {
                $('#local_school_item').append(item);

                paid_local += data.detail[i].paid;
                invoice_local += (data.detail[i].unpaid+data.detail[i].paid);
                target_local += data.detail[i].target;
            }

            paid_total += data.detail[i].paid;
            invoice_total += (data.detail[i].unpaid+data.detail[i].paid);
            target_total += data.detail[i].target;

            var id = 'main-'+i;
            var myChart1 = echarts.init(document.getElementById(id), 'light');

            option1.series[0].data = data.detail[i].countLast5.num1;
            option1.series[1].data = data.detail[i].countLast5.num2;
            option1.series[2].data = data.detail[i].countLast5.target;
            myChart1.setOption(option1);
            chartList[i]=myChart1;
        }

        $('#paid_international').text(paid_international);
        $('#invoice_international').text(invoice_international);
        $('#target_international').text(target_international);
        $('#paid_local').text(paid_local);
        $('#invoice_local').text(invoice_local);
        $('#target_local').text(target_local);
        $('#paid_daystar').text(paid_daystar);
        $('#invoice_daystar').text(invoice_daystar);
        $('#target_daystar').text(target_daystar);

        $('#paid_total').text(paid_total);
        $('#invoice_total').text(invoice_total);
        $('#target_total').text(target_total);

        $('.table-school').DataTable({
            paging: false,
            info: false,
            searching: false,
            aoColumnDefs: [ { "bSortable": false, "aTargets": [ 1, 5, 6 ] }]
        });

        $('#overall-progress').modal('hide');
    });
}

function pictonum(i, type)
{
    $('#main-'+i).hide();
    $('#campustable-'+i).hide();

    $('#switch-'+i+' a').removeClass('active');

    if(type == 1) {
        $('#main-'+i).show();
        $('#switch-'+i+' a').eq(1).addClass('active');
    }
    else {
        $('#campustable-'+i).show();
        $('#switch-'+i+' a').eq(0).addClass('active');
    }
    chartList[i].resize();
}

function pictonumbat(group, type)
{
    $('.'+group+' a').removeClass('active');
    $('.'+group+' a').eq(type==2?0:1).addClass('active');
    $('#'+group+'>tr').each(function (index, value) {
        var dindex = $(value).data('index');
        pictonum(dindex, type)
    })
}

window.onresize = function() {
    myChart.resize();
    for (var i=0; i<chartList.length; i++) {
        chartList[i].resize();
    }
}
</script>

<style>
    .popover{
        max-width: 1000px !important;
    }
.progress-bar-empty {
  background-color: #f5f5f5;
  color: #999
}
.table-school .progress {
    margin-top: 17px;
    height: 28px;
}
.table-school .progress .progress-bar {
    line-height: 24px;
}
    .the-table {
        background-color: #dddddd;
        border-spacing: 1px;
        border-collapse: unset;
        margin-top: 27px;
    }
    .the-table td{
        padding: 5px;
        background-color: #fff;
        text-align: center;
        vertical-align: middle;
    }
</style>
