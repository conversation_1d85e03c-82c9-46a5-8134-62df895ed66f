<?php

/**
 * This is the model class for table "ivy_laseregg_log".
 *
 * The followings are the available columns in table 'ivy_laseregg_log':
 * @property integer $id
 * @property string $laseregg_id
 * @property integer $laseregg_num
 * @property integer $log_timestamp
 * @property integer $add_timestamp
 * @property integer $add_user
 * @property integer $add_type
 */
class LasereggLog extends CActiveRecord
{
	const AUTO = 1;
	const MAN = 2;

	public $time_hour;
	public $time_minute;

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_laseregg_log';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('laseregg_num, add_timestamp, add_user, laseregg_mac, time_hour, time_minute', 'required'),
			array('laseregg_num, add_timestamp, add_user, add_type', 'numerical', 'integerOnly'=>true),
			array('laseregg_id', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, laseregg_id, laseregg_num, log_timestamp, add_timestamp, add_user, add_type, laseregg_mac', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'lasereggInfo'=>array(self::BELONGS_TO, 'LasereggInfo', array('laseregg_mac'=>'laseregg_mac')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'laseregg_id' => Yii::t('laseregg', 'Sensor name'),
			'laseregg_num' => Yii::t('laseregg', 'Sensor readings'),
			'log_timestamp' => Yii::t('laseregg', 'Log Timestamp'),
			'add_timestamp' => Yii::t('laseregg', 'Add Timestamp'),
			'laseregg_mac' => Yii::t('laseregg', 'Sensor name'),
			'add_type' => Yii::t('laseregg', 'Add Type'),
			'add_user' => 'Add User',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('laseregg_id',$this->laseregg_id,true);
		$criteria->compare('laseregg_mac',$this->laseregg_mac);
		$criteria->compare('laseregg_num',$this->laseregg_num);
		$criteria->compare('log_timestamp',$this->log_timestamp);
		$criteria->compare('add_timestamp',$this->add_timestamp);
		$criteria->compare('add_user',$this->add_user);
		$criteria->compare('add_type',$this->add_type);
		$criteria->order = 'log_timestamp desc';

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return LasereggLog the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
