<?php
class Utils_tj_ec
{
    public $childObj = null;
    
	public function run($childId=0,$filterList =array(),$config,$startDate)
	{
        
        if( isset(Yii::app()->getController()->childObj) ){
            $this->childObj = Yii::app()->getController()->childObj;
        }
        else {
            $this->childObj = ChildProfileBasic::model()->findByPk($childId);
        }
        
        $countryId = $this->childObj->country;
		if ($childId && $countryId && count($filterList))
		{
			foreach ($filterList as $k => $val)
			{
				$func   =  'func_' . $k;
				if (method_exists($this,$func))
				{
					$resutl = $this->$func($countryId,$childId,$val,$config,$startDate);
					if (count($resutl))
					{
						return $resutl;
						break;
					}
				}
			}
		}
	}
	
	public function func_foreigner($countryId,$childId,$val,$config,$startDate)
	{
		if ($countryId != 36)
		{
			return $val;
		}
	}
	
	public function func_discounts($countryId,$childId,$val,$config,$startDate)
	{
		$criteria = new CDbCriteria;
		$criteria->select = 't.discount_id';
		$criteria->compare('t.childid', $childId);
		$criteria->compare('t.status', 1);
		$criteria->compare('DiscountSchoolNoTitle.flag', 1);
		$criteria->compare('DiscountSchoolNoTitle.stat', 10);
		$criteria->order = 't.id Desc';
		$model = ChildDiscountLink::model()->with('DiscountSchoolNoTitle')->find($criteria);
		//业主
		if (!empty($model))
		{
			foreach ($val[$model->discount_id]['linkedInvoice'] as $key=>$value)
			{
				$val[$model->discount_id]['linkedInvoice'][$key]['TITLE'] = $config->genInvoiceTitle(array('feeType'=>$value[FEETYPE],'startDate'=>$startDate));
			}
			return $val[$model->discount_id];
		}
		return array();
	}
	
	public function func_default($countryId,$childId,$val,$config,$startDate)
	{
		foreach ($val['linkedInvoice'] as $key=>$value)
		{
            $params = array(
                'p_byType' => PAYGROUP_MONTH,
                'p_feeType' => FEETYPE_TUITION,
                'p_startDate' => $startDate,
            );
            $result = $config->getCalculatedAmount($params,array(BY_MONTH=>$value[AMOUNT]));
			$val['linkedInvoice'][$key]['TITLE'] = $config->genInvoiceTitle(array('feeType'=>$value[FEETYPE],'startDate'=>$startDate));
			$val['linkedInvoice'][$key]['AMOUNT'] = $result['full']['amount'];
		}
		return $val;
	}
}