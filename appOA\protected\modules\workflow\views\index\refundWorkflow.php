<?php
$start_time = Yii::app()->request->getParam('start_time','');
$end_time = Yii::app()->request->getParam('end_time','');

?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Workspace'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Workflow Workspace'), array('//workflow/index/index'))?></li>
        <li class="active">退费列表</li>
    </ol>

    <div class="row">
        <!-- 左侧菜单 -->
        <div class="col-md-2 col-sm-2 mb10">
            <ul class="nav nav-pills nav-stacked background-gray" id="pageCategory">
                <?php foreach($wordDlowObj as $item){ ?>
                    <li class="<?php echo ($item->defination_id == $definationId) ? "active" : ""; ?>"><?php echo CHtml::link((Yii::app()->language == 'zh_cn' ? $item->defination_name_cn : $item->defination_name_en ), array('//workflow/index/workflowCount', 'definationId' => $item->defination_id)) ?></a></li>
                <?php } ?>
            </ul>

        </div>

        <?php if($dataProvider) {?>
            <div class="col-md-10 col-sm-12">
                <!--<div class="mb10 col-sm-1 row">
                    <a href="<php /*echo $this->createUrl('exportAdmissions', array("childid"=>"")) */?>" class="btn btn-info" target="_blank"><php /*echo Yii::t('user', 'Export');*/?></a>
                </div>-->
                <div class="mb10 row">
                    <!-- 搜索框 -->
                    <form  class="" style="float: left;width: 100%" id="refundList" action="<?php echo $this->createUrl('workflowCount'); ?>" method="get">
                        <?php echo Chtml::hiddenField('definationId',$definationId); ?>
                        <?php echo Chtml::hiddenField('branchId',$branchId); ?>
                        <!-- 状态 -->
                        <div class="col-sm-2 form-group">
                            <?php echo Chtml::textField('userName',$_GET['userName'], array('class'=>'form-control', 'placeholder' => '学生姓名')); ?>
                        </div>
                        <!-- 状态 -->
                        <div class="col-sm-2 form-group">
                            <?php echo Chtml::dropDownList('exte3',$_GET['exte3'], WorkflowOperation::getWay(), array('class'=>'form-control',  'empty' => Yii::t('teaching', '退费方式'))); ?>
                        </div>
                        <div class="col-sm-2 form-group">
                            <?php echo Chtml::dropDownList('state',$_GET['state'], WorkflowOperation::getConfig(), array('class'=>'form-control',  'empty' => Yii::t('teaching', '退费进度'))); ?>
                        </div>
                        <div class="">
                            <div class="col-sm-2" style="float: left;">
                                <input type="text" class="form-control datepicker start_time"  autocomplete="off" placeholder="发起日期开始"   name="start_time" value="<?php echo $start_time ? $start_time: ''; ?>">
                            </div>
                        </div>
                        <div class="">
                            <div class="col-sm-2" style="float: left;">
                                <input type="text" class="form-control datepicker end_time" autocomplete="off"  placeholder="发起日期结束"   name="end_time" value="<?php echo $end_time ? $end_time: ''; ?>">
                            </div>
                            <div class="">
                                <button class="btn btn-default ml5" type="button" id='buttonsa'><span class="glyphicon glyphicon-search"> </span> </button>
                                <?php if($start_time && $end_time){ ?>
                                    <a href="javascript:void(0);" onclick="exportRefund('<?php echo $url ?>')" class="btn btn-info" target="_blank">导出</a>
                                <?php } ?>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="col-md-12"></div>
                <div class="panel panel-default">
                    <div class="panel-body">
                        <?php
                        $this->widget('ext.ivyCGridView.BsCGridView', array(
                            'id'=>'confirmed-visit-grid',
                            'afterAjaxUpdate'=>'js:head.Util.modal',
                            'dataProvider'=>$dataProvider,
                            'template'=>"{items}{pager}{summary}",
                            'colgroups'=>array(
                                array(
                                    "colwidth"=>array(100,100,100,100,100,100),
                                )
                            ),
                            'columns'=>array(
                                array(
                                    'name' => Yii::t('asa','学生姓名'),
                                    'value'=> array($this, 'getChildName'),
                                ),
                                array(
                                    'name' => Yii::t('asa','退费方式'),
                                    'value'=> array($this, 'getExte3'),
                                ),
                                array(
                                    'name' => Yii::t('asa','退费状态'),
                                    'value'=> array($this, 'getWays'),
                                ),
                                array(
                                    'name' => Yii::t('asa','增加退费时间'),
                                    'value'=>'date("Y-m-d", $data->start_time)',
                                ),
                                array(
                                    'name'=> Yii::t('asa','总钱数'),
                                    'value'=> array($this, 'getAmount'),
                                ),
                                array(
                                    'name'=> Yii::t('asa','操作'),
                                    'value'=> array($this, 'getButton'),
                                ),
                            ),
                        ));
                        ?>
                    </div>
                </div>
            </div>
        <?php } ?>
    </div>
</div>
<div id="workflow-modal-template"></div>
<script>
    $('.datepicker').datepicker({
        changeMonth: true,
        changeYear: true,
        dateFormat:'yy-mm-dd'
    });

    $("#buttonsa").click(function(){
        var startTime = $(".start_time").val();
        var endTime = $(".end_time").val()
        if(startTime && endTime){
            var startTimeData = DateToUnix(startTime);
            var startEndData = DateToUnix(endTime);
            if(startTimeData > startEndData){
                resultTip({msg: '结束时间不能大于开始时间', error: 'warning'});
            }else if((startEndData > startTimeData+86400*180)){
                resultTip({msg: '开始时间和结束时间最多相差180天', error: 'warning'});
            }else{
                $("#refundList").submit();
            }
        }else{
            $("#refundList").submit();
        }
    });

    function DateToUnix(string) {
        var f = string. split( ' ', 2);
        var d = ( f[0 ] ? f[0 ] : ''). split( '-', 3);
        var t = ( f[1 ] ? f[1 ] : ''). split( ':', 3);
        return ( new Date(
            parseInt( d[0 ], 10) || null,
            ( parseInt( d[1 ], 10) || 1) - 1,
            parseInt( d[2 ], 10) || null,
            parseInt( t[0 ], 10) || null,
            parseInt( t[1 ], 10) || null,
            parseInt( t[2 ], 10) || null
        )). getTime() / 1000;
    }

    function exportRefund(url) {
        $.ajax({
            url: url,
            type: 'POST',
            success: function (res) {
                if (res.state == 'success') {
                    var data = res.data.items;
                    const filename = res.data.title;
                    const ws_name = "SheetJS";

                    const worksheet = XLSX.utils.aoa_to_sheet(data);
                    const workbook = XLSX.utils.book_new();
                    XLSX.utils.book_append_sheet(workbook, worksheet, ws_name);
                    // generate Blob
                    const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                    const blob = new Blob([wbout], {type: 'application/octet-stream'});
                    // save file
                    let link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = filename;
                    link.click();
                    setTimeout(function() {
                        // 延时释放掉obj
                        URL.revokeObjectURL(link.href);
                        link.remove();
                    }, 500);
                }
            },
            dataType: 'json'
        });
    }


</script>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
