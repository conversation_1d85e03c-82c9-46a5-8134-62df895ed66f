<?php
if (true === $this->lang || 'cn' == $this->lang) {
    $df_cn = new CDateFormatter('zh-cn');
    $this->render('mailHeader_lang', array('lang' => 'cn'));
    ?>

    <div style="font-family: 'Microsoft Yahei'">


        <p>亲爱的家长，</p>

        <p>
            您在<?php echo Mims::unIvy()?'幼儿园管理系统':'艾毅在线'; ?>上修改了登录账号，请使用新的账号登陆系统。
        </p>

        <p>如遇到问题，请联系校园 <?php echo CHtml::link($support_email, 'mailto:' . $support_email) ?>。</p>

        <p>
            <?php echo $df_cn->formatDateTime($date_time, 'medium', 'short'); ?>
        </p>

        <p>该邮件为系统自动发送。</p>
    </div>

    <?php
    $this->render('mailFooter_lang', array('lang' => 'cn'));
}
?>


<?php
if (true === $this->lang || 'en' == $this->lang) {
    $df_en = new CDateFormatter('en-us');
    $this->render('mailHeader_lang', array('lang' => 'en'));
    ?>

    <div style="font-family: 'Trebuchet MS','Microsoft Yahei',Arial,Helvetica,sans-serif">

        <p>Dear Parents,</p>

        <p>
            You have changed your email address used to login to <?php echo Mims::unIvy()?'the system':'IvyOnline';?>. Please use the new email address to log in.
        </p>

        <p>If you have any questions, please feel free to contact the campus <?php echo CHtml::link($support_email, 'mailto:' . $support_email) ?>.</p>

        <p>
            <?php echo $df_en->formatDateTime($date_time, 'medium', 'short'); ?>
        </p>

        <p>This is an automatic email.</p>
    </div>
    <?php
    $this->render('mailFooter_lang', array('lang' => 'en'));
}
?>
