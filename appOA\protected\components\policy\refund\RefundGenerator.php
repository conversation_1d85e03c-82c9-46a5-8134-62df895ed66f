<?php
/*
 * 退费观察者
 */
abstract class RefundGenerator
{
    private $observers = array();

    public function addObserver($observer){
        $this->observers[] = $observer;
    }
    
    public function notify(){
        $ret = array();
        foreach ($this->observers as $server){
            $ret[$server->invoiceObj->payment_type][$server->invoiceObj->invoice_id] = $server->fee();
        }
        return $ret;
    }
}
