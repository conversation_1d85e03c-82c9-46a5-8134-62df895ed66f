<?php $form=$this->beginWidget('CActiveForm', array(
    'id'=>'visits-form',
    'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
));
$branchList = Branch::model()->getBranchList();
$cfgs = $this->loadConfig();
$knowusList = array();
if (isset($cfgs['knowus']))
{
    foreach ($cfgs['knowus'] as $k=>$v)
    {
        $knowusList[$k] = (Yii::app()->language=='zh_cn') ? $v['cn'] : $v['en'];
    }
}
$languageList = array();
if (isset($cfgs['language']))
{
    foreach ($cfgs['language'] as $k=>$v)
    {
        $languageList[$k] = (Yii::app()->language=='zh_cn') ? $v['cn'] : $v['en'];
    }
}
$concerns = array();
if (isset($cfgs['concerns']))
{
    foreach ($cfgs['concerns'] as $k=>$v)
    {
        $concerns[$k] = (Yii::app()->language=='zh_cn') ? $v['cn'] : $v['en'];
    }
}

?>
<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
    <h4 class="modal-title"><?php echo $modalTitle;?></h4>
</div>
<div class="modal-body">
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'visit_date'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'visit_date',array('maxlength'=>255,'class'=>'form-control datepicker','readonly'=>'readonly')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'visit_time'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'visit_time',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'parent_name'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'parent_name',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'phone'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'phone',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'email'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'email',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'child_name'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'child_name',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'schoolid'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->dropDownList($model,'schoolid',$branchList, array('class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'birthdate'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'birthdate',array('maxlength'=>255,'class'=>'form-control datepicker','readonly'=>'readonly')); ?>
        </div>
    </div>
    <?php if(in_array($model->schoolid, array('BJ_DS','BJ_SLT'))): ?>
        <div class="form-group">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'current_school'); ?></label>
            <div class="col-xs-9">
                <?php echo $form->textField($model,'current_school',array('maxlength'=>255,'class'=>'form-control')); ?>
            </div>
        </div>
        <div class="form-group">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'current_grade'); ?></label>
            <div class="col-xs-9">
                <?php echo $form->textField($model,'current_grade',array('maxlength'=>255,'class'=>'form-control')); ?>
            </div>
        </div>
        <div class="form-group">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'application_grade'); ?></label>
            <div class="col-xs-9">
                <?php echo $form->dropDownList($model,'application_grade',$cfgs['applicationGradeDS'], array('class'=>'form-control')); ?>
            </div>
        </div>
        <div class="form-group">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'expected_attendance'); ?></label>
            <div class="col-xs-9">
                <?php echo $form->dropDownList($model,'expected_attendance', array(1 => 1,2 => 2), array('class'=>'form-control')); ?>
            </div>
        </div>
    <?php endif; ?>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'address'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'address',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'knowus'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->dropDownList($model,'knowus',$knowusList,array('class'=>'form-control')); ?>
        </div>
    </div>    
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'concerns'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->checkBoxList($model,'concerns',$concerns,array('class'=>'')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'receive_language'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->dropDownList($model,'receive_language',$languageList,array('class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'memo'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textArea($model,'memo', array('class'=>'form-control')); ?>
        </div>
    </div>
    <?php if($model->from): ?>
        <div class="form-group">
            <label class="col-xs-3 control-label">来源</label>
            <div class="col-xs-9">
                <div class=""><?php echo $model->from ?></div>
            </div>
        </div>
    <?php endif; ?>
</div>
<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
    <button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
</div>

<?php $this->endWidget(); ?>
<script>
    $('.datepicker').datepicker({
        changeMonth: true,
        changeYear: true,
        dateFormat:'yy-mm-dd',
    });
    // 回调：添加成功
    function cbVisit() {
        $('#modal').modal('hide');
        $.fn.yiiGridView.update('pending-visit-grid');
    }
</script>