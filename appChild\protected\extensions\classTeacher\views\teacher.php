<div id="teacher" class="pop-box">
    <h5><?php echo addColon(Yii::t("portfolio", 'Class Teachers'));?><?php echo $class_model->title;?></h5>
    <div class="t-box">
        <ul>
            <?php foreach($Teachers as $teacher){?>
            <li class="a-profile">
                <div class="fl">
                    <?php
                    $staffphoto = $teacher->staffInfo&&$teacher->staffInfo->staff_photo ? $teacher->staffInfo->staff_photo : 'blank.jpg';
                    echo CHtml::image(Mims::CreateUploadUrl('infopub/staff/'.$staffphoto), $teacher->userWithProfile->getName(), array("class"=>"face"))
                    ?>
                </div>
                <ul class="info">
                    <li class="name"><?php echo $teacher->userWithProfile->getName();?></li>
                    <li><label><?php echo addColon(Yii::t("labels", 'Gender'));?></label><?php echo ($teacher->userWithProfile->profile->user_gender==1)?Yii::t("userinfo",'Male'):Yii::t("userinfo",'Female');?></li>
                    <li><label><?php echo addColon(Yii::t("labels", 'Nationality'));?></label><?php echo $country[$teacher->userWithProfile->profile->nationality];?></li>
                    <?php if(!Yii::app()->user->getIsGuest()):?>
                    <li><label><?php echo addColon(Yii::t("labels", 'Email'));?></label><?php echo $teacher->userWithProfile->email;?></li>
                    <?php endif;?>
                </ul>
                
                <?php if ($teacher->staffInfo):?>
                <div class="intro"><?php echo HtoolKits::autoLang($teacher->staffInfo->intro_cn, $teacher->staffInfo->intro_en)?></div>
                <?php endif;?>
                <div class="clear"></div>
            </li>
            <?php }?>
        </ul>
    </div>
</div>