<script>
    tinymce.init({
        selector: '.tinymce',
        language: 'zh_CN',
        height: 700,
        plugins: 'fullscreen image link media imagetools preview tools table code',
        imagetools_cors_hosts: ['picsum.photos'],
        menubar: 'file edit view insert format tools help',
        toolbar: 'undo redo bold italic underline strikethrough fontselect fontsizeselect formatselect fullscreen',
        toolbar_sticky: true,
        setup: function (editor) {
            // 实时同步编辑器内容到 selector
            editor.on('change', function () {
                tinymce.triggerSave();
            });
        }
    });
</script>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('/mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','奖学金列表'), array('/mcampus/scholarship/introduceList'))?></li>
        <?php
        foreach ($config as $key=>$val){
            if($key == $type){
                echo '<li>' . $val['title'] . '</li>';
            }
        }
        ?>
    </ol>

    <div class="row">
        <div class="col-md-2 col-sm-2 mb10">
            <ul class="nav nav-pills nav-stacked background-gray" id="pageCategory">
                <?php
                foreach ($config as $key=>$val){
                    $class = $type==$key ? "active" : "" ;
                    echo '<li class="' . $class . '"><a href='. $this->createUrl('updateScholarship', array('id' => $model->id, 'type' => $key)) .'>'. $val['title'] .'</a></li>';
                }
                ?>
            </ul>
        </div>
        <div class="col-md-10 col-sm-10">
            <div class="table-responsive" id="cradList">
                <?php $form = $this->beginWidget('CActiveForm', array(
                    'id' => 'course-desc',
                    'enableAjaxValidation' => false,
                    'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form'),
                    'action' => $this->createUrl('save', array('id' => $model->id, 'type' => $type))
                )); ?>
                <div class="modal-body">
                    <div class="form-group">
                        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'intro_cn'); ?></label>
                        <div class="col-xs-9">
                            <?php echo $form->textArea($model, 'intro_cn', array('maxlength' => 255, 'class' => 'form-control tinymce')); ?>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'intro_cn'); ?></label>
                        <div class="col-xs-9">
                            <?php echo $form->textArea($model, 'intro_en', array('maxlength' => 255, 'class' => 'form-control tinymce')); ?>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
                </div>
                <?php $this->endWidget(); ?>
            </div>
        </div>
    </div>
</div>
<script>
    var ueScholarshipIntro_intro_cn;
    var ueScholarshipIntro_intro_en;
    function cbCourseDesc() {
        $('#modal').modal('hide');
        window.location.reload(true);
    }
</script>
