<?php
$weeks = array(0=>'本周', 1=>'近2周', 2=>'近3周', 3=>'近4周');
?>

<div class="row">
    <div class="col-sm-12 col-md-12">
        <table class="table table-bordered table-hover">
            <thead>
                <tr class="info">
                    <th width="6%"></th>
                    <th width="6%"></th>
                    <?php foreach ($branchs as $schoolid=>$branch):?>
                    <th width="6%"><?php echo CHtml::link($branch['abb'], 'javascript:;', array('class'=>'schooljs', 'sid'=>$schoolid));?></th>
                    <?php endforeach;?>
                </tr>
            </thead>
            <tbody>
                <?php $i=0;foreach ($data as $code=>$datum):?>
                <?php $j=0;foreach ($datum as $period=>$dd):?>
                <tr>
                    <?php if ($j==0):?>
                    <td class="success" rowspan="<?php echo count($datum)?>"><?php echo $type[$code]?></td>
                    <?php endif;?>
                    <td class="warning"><?php $tp = (int)(($monday-strtotime($period))/604800);echo $weeks[$tp];?></td>
                    
                    <?php foreach ($branchs as $schoolid=>$branch):?>
                    <td align="center" id="d_<?php echo $code;?>_<?php echo $schoolid;?>_<?php echo $tp?>"><?php echo $dd[$schoolid]->data?></td>
                    <?php endforeach;?>
                </tr>
                <?php $j++;endforeach;?>
                <?php $i++;endforeach;?>
            </tbody>
            <tfoot>
                <tr>
                    <th width="6%"></th>
                    <th width="6%"></th>
                    <?php foreach ($branchs as $schoolid=>$branch):?>
                    <th width="6%"><?php echo CHtml::link($branch['abb'], 'javascript:;', array('class'=>'schooljs', 'sid'=>$schoolid));?></th>
                    <?php endforeach;?>
                </tr>
            </tfoot>
        </table>
    </div>
</div>

<div id="container" style="min-width: 310px; height: 400px; margin: 0 auto"></div>
<div id="tdata" style="display: none;"></div>

<script>
    var ajax = true;
    $('#week').change(function(){
        location.href='<?php echo $this->createUrl('')?>'+'&week='+$('#week').val();
    });
    $('.schooljs').click(function(){
        var aobj = $(this);
        if (ajax){
            $.ajax({
                async: false,
                type: 'post',
                url: '<?php echo $this->createUrl('schooldata')?>',
                data: {}
            }).done(function(data){
                ajax = false;
                $('#tdata').html(data);
            });
            
            $('.J_dt').each(function(){
                var schoolid = $(this).attr('did');
                for(var i=0; i<4; i++){
                    var oba = $('#d_<?php echo IvyStatsItem::CODE_VISIT_ACTIVE?>_'+schoolid+'_'+(3-i));
                    if (oba.length){
                        $('#active_'+schoolid+'_'+i).text( oba.text() );
                    }
                    else {
                        $('#active_'+schoolid+'_'+i).text( 0 );
                    }
                    var obn = $('#d_<?php echo IvyStatsItem::CODE_VISIT_NOLOG?>_'+schoolid+'_'+(3-i));
                    if (obn.length){
                        $('#nolog_'+schoolid+'_'+i).text( obn.text() );
                    }
                    else{
                        $('#nolog_'+schoolid+'_'+i).text( 0 );
                    }
                }
            })
        }
        
        $('#container').highcharts({
            data: {
                table: document.getElementById('datatable_'+aobj.attr('sid'))
            },
            chart: {
                type: 'line'
            },
            title: {
                text: aobj.text()
            }
        });
    });
</script>
<script src="<?php echo Yii::app()->theme->baseUrl; ?>/js/modules/data.js"></script>