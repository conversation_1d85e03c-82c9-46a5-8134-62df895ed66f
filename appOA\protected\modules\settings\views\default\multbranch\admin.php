<div class="h_a">管理多校园设置</div>
<div class="prompt_text">
	<ul>
		<li>未开通管理多校园的用户请到用户管理设置</li>
	</ul>
</div>
<div class="mb10">
    <a href="<?php echo $this->createUrl('/settings/default/addMultBranch', array('type'=>$type));?>" class="btn">
        <span class="add"></span>
        添加用户
    </a>
</div>
<div class="table_list">
	<table width="100%" id="J_table_list" style="table-layout:fixed;">
		<thead>
			<tr>
				<td width="200">用户</td>
				<td>管辖校园</td>
				<td class="tac" width="100">操作</td>
			</tr>
		</thead>
		<tbody>
			<?php foreach ($userList as $ul): ?>
			<tr>
				<td><?php echo $ul->getName();?></td>
				<td id='adm_<?php echo $ul->uid?>'>
                        <?php
                        $activeList = array();
                        if (!empty($ul->multBranchWithParam)):
                            foreach($ul->multBranchWithParam as $mb):
                                echo CHtml::openTag('span');
                                echo $branchList[$mb->schoolid].'&nbsp;&nbsp;&nbsp;&nbsp;';
                                echo CHtml::closeTag('span');
                                $activeList[] = $mb->schoolid;
                            endforeach;
                        endif;
                        ?>
                    </ul>
				</td>
				<td class="tac">
                    <?php echo CHtml::link(Yii::t("settings",'分配校园'), array('/settings/default/schoolslist', 'id'=>$ul->uid, 'type'=>$type), array('class'=>'J_dialog', 'title'=>'分配校园：'.$ul->getName()));?>
                    <?php echo CHtml::link(Yii::t("Global",'删除'), '#', array('onclick'=>'deleteMult(\''.$type.'\', '.$ul->uid.')'));?>
				</td>
			</tr>
			<?php endforeach; ?>
		</tbody>

    </table>
</div>

<script type="text/javascript">
function deleteMult(type, uid)
{
    if (confirm('确定删除？')){
        $.ajax({
            type: 'POST',
            url: '<?php echo $this->createUrl('/settings/default/deleteMult');?>',
            data: {type: type, uid: uid},
            dataType: 'json',
            success: function(data){
                if (data.state === 'success'){
                    reloadPage(window);
                }
                else {
                    alert(data.message);
                }
            }
        });
    }
    return false;
}
</script>