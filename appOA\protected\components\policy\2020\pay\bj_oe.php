<?php
if(!defined("IVY_POLICY"))
throw new CException('DO NOT USE THIS FILE DIRECTLY');

return array( 'policy'=>array(

	//是否老生老办法
	ENABLE_CONSTANT_TUITION => true,
    // 按天收费
    FENTAN_FACTOR => DAILY,

	//年保育费
	//ANNUAL_CHILDCARE_AMOUNT => 8000,

	//是否开放课外活动账单
	ENABLE_AFTERSCHOOLS => true,

	//图书馆工本费
	FEETYPE_LIBCARD => 10,

	//学期四个时间点；取前后两端
	TIME_POINTS   => array(202009,202101,202102,202107),

	//年付开通哪些缴费类型
//	PAYGROUP_ANNUAL => array(
//		ITEMS => array(
//			FEETYPE_TUITION,
//			FEETYPE_LUNCH,
//			FEETYPE_SCHOOLBUS,
//		),
//		TUITION_CALC_BASE => BY_MONTH,
//	),
//
	//学期付开通哪些缴费类型
	PAYGROUP_SEMESTER => array(
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
           FEETYPE_SCHOOLBUS,
		),
		TUITION_CALC_BASE => BY_SEMESTER,
	),
//
//    PAYGROUP_MONTH => array(
//        ITEMS => array(
//            FEETYPE_TUITION,
//            FEETYPE_LUNCH,
//            FEETYPE_SCHOOLBUS,
//        ),
//        TUITION_CALC_BASE => BY_MONTH,
//    ),

	//计算基准：BY_MONTH, 或者 BY_SETTING, 前者表示所有的学费都基于月费用计算，后者表示所有的学费都基于学期或学年
	TUITION_CALCULATION_BASE => array(


        BY_MONTH => array(

            //月收费金额
            AMOUNT => array(
//                FULL5D => 10500,
//                HALF5D => 7560,
                // FULL3D => 6840,
                // HALF3D => 5050,
                // FULL2D => 4750,
                // HALF2D => 4321,
            ),

            //收费月份，及其权重
            MONTH_FACTOR => array(
                202009=>1,
                202010=>1,
                202011=>1,
                202012=>.55,
                202101=>1,
                202102=>.55,
                202103=>1,
                202104=>1,
                202105=>1,
                202106=>1,
                202107=>1
            ),

            //折扣
            GLOBAL_DISCOUNT => array(
                DISCOUNT_ANNUAL => '1:1:2021-06-01:round',
                DISCOUNT_SEMESTER1 => '1:1:2021-02-01:round',
                DISCOUNT_SEMESTER2 => '1:1:2021-07-20:round'
            )
        ),

        BY_SEMESTER1 => array(
            AMOUNT => array(
                FULL5D => 81015,
//                FULL5D2 => 63659,
                // FULL5D2 => 72378,
                // HALF2D => 28500,
                // HALF3D => 41500,
                // HALF5D => 69664,
            ),
        ),

        BY_SEMESTER2 => array(
            AMOUNT => array(
                FULL5D => 70269,
//                FULL5D2 => 87625,
                // FULL5D2 => 73906 ,
                // HALF2D => 28500,
                // HALF3D => 41500,
                // HALF5D => 55322,
            ),
        ),
	),

	//每餐费用
	LUNCH_PERDAY => 45,

	//账单到期日和账单开始日之差
	DUEDAYS_OFFSET => 1,

));