<div class="panel-heading">
    <h4><?php echo $childModel->getChildName(); ?></h4>
    <h4><?php echo $childModel->ivyclass->title; ?></h4>
</div>
<div style="border: 1px solid #ddd;    padding: 10px 15px;">
    <div class="rating-container rating-gly-star" style="width:42px;" data-content="">
        <div class="rating-stars" data-content="" style="width: 100%;">
        </div>
    </div>
    <?php echo Yii::t('menu', 'Not Yet - child has not yet demonstrated indicator') ?>
    <br>

    <div class="rating-container rating-gly-star" style="width:42px;" data-content="">
        <div class="rating-stars" data-content="" style="width: 100%;">
        </div>
    </div>
    <?php echo Yii::t('menu', 'In Process - child demonstrates indicator intermittently') ?>
    <br>

    <div class="rating-container rating-gly-star" style="width:42px;" data-content="">
        <div class="rating-stars" data-content="" style="width: 100%;">
        </div>
    </div>
    <?php echo Yii::t('menu', 'Proficient - child can reliably demonstrate indicator') ?>
</div>
<br/>
<?php

foreach ($taskData['checkListTemplates']['category'] as $cat_0 => $category):?>
    <div class="">
        <div class="panel-heading">
            <h3 class="panel-title">
                <?php echo CommonUtils::autoLang(
                    $taskData['checkListTemplates']['catList'][$cat_0]['cn_title'],
                    $taskData['checkListTemplates']['catList'][$cat_0]['en_title']
                );?>
            </h3>
        </div>
        <div class="">
            <table class="table table-bordered">
                    <colgroup>
                        <col width="300">
                        <col width="100">
                        <col width="100">
                    </colgroup>
                <?php foreach ($category as $cat_1 => $data): ?>
                    <tr>
                        <td>
                            <h4>
                                <?php echo CommonUtils::autoLang(
                                    $taskData['checkListTemplates']['catList'][$cat_1]['cn_title'],
                                    $taskData['checkListTemplates']['catList'][$cat_1]['en_title']
                                ); ?>
                            </h4>
                        </td>
                        <td><?php echo Yii::t("labels", '1st Semester'); ?></td>
                        <td><?php echo Yii::t("labels", '2nd Semester'); ?></td>
                    </tr>
                    <ul class="list-unstyled">
                        <?php
                        if (count($taskData['checkListTemplates']['checkList'][$cat_1])) {
                            foreach ($taskData['checkListTemplates']['checkList'][$cat_1] as $item) {
                                $fallContent = '';
                                $springContent = '';
                                if ($childdata) {
                                    $fall = (isset($childdata->development) && isset($childdata) && $childdata->development['fall']) ? $childdata->development['fall'][$item['id']] : 0;
                                    $spring = (isset($childdata->development) && isset($childdata) && $childdata->development['spring']) ? $childdata->development['spring'][$item['id']] : 0;
                                    switch ($fall) {
                                        case 1:
                                            $fallContent = '';
                                            break;
                                        case 2:
                                            $fallContent = '';
                                            break;
                                        case 3:
                                            $fallContent = '';
                                            break;
                                        default:
                                            $fallContent = '';
                                            break;
                                    }
                                    switch ($spring) {
                                        case 1:
                                            $springContent = '';
                                            break;
                                        case 2:
                                            $springContent = '';
                                            break;
                                        case 3:
                                            $springContent = '';
                                            break;
                                        default:
                                            $springContent = '';
                                            break;
                                    }
                                }
                                ?>
                                <tr class="" style="line-height:33px;">
                                    <td class="">
                                        <?php echo CommonUtils::autoLang($item['cn_content'], $item['en_content']); ?>
                                    </td>
                                    <td  id="rating-fall-1">
                                        <div class="rating-container rating-gly-star"
                                             data-content="<?php echo $fallContent ?>">
                                            <div class="rating-stars" data-content="<?php echo $fallContent ?>"
                                                 style="width: 100%;"></div>
                                        </div>
                                    </td>
                                    <td  id="rating-spring-1">
                                        <div class="rating-container rating-gly-star"
                                             data-content="<?php echo $springContent ?>">
                                            <div class="rating-stars" data-content="<?php echo $springContent ?>"
                                                 style="width:  100%;"></div>
                                        </div>
                                    </td>
                                </tr>
                            <?php }
                        } ?>
                        <?php
                        foreach ($data as $cat_2):?>

                            <tr>
                                <td colspan="3">
                                    <strong>
                                        <?php echo CommonUtils::autoLang(
                                            $taskData['checkListTemplates']['catList'][$cat_2]['cn_title'],
                                            $taskData['checkListTemplates']['catList'][$cat_2]['en_title']
                                        );?>
                                    </strong>
                                </td>
                            </tr>
                            <?php
                            if (count($taskData['checkListTemplates']['checkList'][$cat_2])) {
                                foreach ($taskData['checkListTemplates']['checkList'][$cat_2] as $item) {
                                    $fallContent = '';
                                    $springContent = '';

                                    if ($childdata) {
                                        $fall = (isset($childdata->development) && isset($childdata) && $childdata->development['fall']) ? $childdata->development['fall'][$item['id']] : 0;
                                        $spring = (isset($childdata->development) && isset($childdata) && $childdata->development['spring']) ? $childdata->development['spring'][$item['id']] : 0;

                                        switch ($fall) {
                                            case 1:
                                                $fallContent = '';
                                                break;
                                            case 2:
                                                $fallContent = '';
                                                break;
                                            case 3:
                                                $fallContent = '';
                                                break;
                                            default:
                                                $fallContent = '';
                                                break;
                                        }
                                        switch ($spring) {
                                            case 1:
                                                $springContent = '';
                                                break;
                                            case 2:
                                                $springContent = '';
                                                break;
                                            case 3:
                                                $springContent = '';
                                                break;
                                            default:
                                                $springContent = '';
                                                break;
                                        }
                                    }
                                    ?>
                                    <tr class="" style="line-height:33px;">
                                        <td >
                                            <?php echo CommonUtils::autoLang($item['cn_content'], $item['en_content']); ?>
                                        </td>
                                        <td  id="rating-fall-1">
                                            <div class="rating-container rating-gly-star"
                                                 data-content="<?php echo $fallContent ?>">
                                                <div class="rating-stars" data-content="<?php echo $fallContent ?>"
                                                     style="width: 100%;"></div>
                                            </div>
                                        </td>
                                        <td  id="rating-spring-1">
                                            <div class="rating-container rating-gly-star"
                                                 data-content="<?php echo $springContent ?>">
                                                <div class="rating-stars" data-content="<?php echo $springContent ?>"
                                                     style="width: 100%;"></div>
                                            </div>
                                        </td>
                                    </tr>
                                <?php }
                            } ?>
                        <?php endforeach; ?>
                    </ul>
                <?php endforeach;?>
            </table>
        </div>
    </div>
<?php endforeach; ?>