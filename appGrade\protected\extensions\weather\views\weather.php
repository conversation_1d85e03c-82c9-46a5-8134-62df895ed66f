
<style type="text/css">
.city-wrapper {
	float: right;
	position: relative;
}
.city-wrapper .hover{
	background-color:#fff;
	border-color:#ccc;
}
.show-city {
	background: url("<?php echo $this->assetsHomeUrl?>/images/down.gif")
		no-repeat scroll 92% 5px transparent;
	color: #666666;
	cursor: pointer;
	border: 1px solid #E8E7DA;
	float: left;
	height: 22px;
	line-height: 22px;
	padding: 0 22px 0 5px;
	width: 60px;
    text-align: center;
}

.city-list {
	background: none repeat scroll 0 0 white;
	border: 1px solid #CCCCCC;
	border-top: 0;
	padding: 5px;
	position: absolute;
	right: 0px;
	top: 22px;
	width: 140px;
	z-index: 999;
}
.city-list li{
	display:inline-block;
	float:left;
	margin-right: 4px;
}
.city-list li a{
	font-size: 12px;
	text-decoration:none;
	padding: 1px 4px;
}
.city-list li a:hover{
	font-size: 12px;
	text-decoration:none;
	background-color:#F37101;
	color:#fff;
}

.city-wrapper .close {
	color: #CCCCCC;
	cursor: pointer;
	font-size: 12px;
	position: absolute;
	right: 5px;
}
.
</style>

<h2>
	<?php echo Yii::t("global",'Weather');?>
	<div class="city-wrapper">

		<div class="show-city">
			<?php
			echo CHtml::tag('span',array('style'=>"position: static;"));
			echo $this->cities[$place];
			echo CHtml::closeTag('span');

			echo CHtml::hiddenField('current_city',$place);
			echo CHtml::ajaxLink("", $this->getController()->createUrl('profile/weather'),array(
							'type'=>'POST',
							'data'=>array('cityName'=>'js:$(\'#current_city\').val()'),
							'dataType'=>'JSON',
							'beforeSend'=>'beforeSendFunc',
							'success'=>'weatherCityChanged',
					),array('id'=>'change_city_link','style'=>'display:none;'));
			?>
		</div>
		<div class="city-list" style="display: none;">
			<b class="close">×</b>
			<ul>
				<?php foreach ($this->cities as $key=>$city) {
					echo CHtml::openTag('li');
					echo CHtml::link($city,'javascript:void();',array('class'=>'city','city'=>$key,'onclick'=>'return false;'));
					echo CHtml::closeTag('li');

				}?>
			</ul>
		</div>

	</div>

</h2>

<script type="text/javascript">
var show=false;
$(".show-city").click(function(){
	$('.city-list').toggle();
	show=!show;
	if(show){
		$(this).addClass("hover");
	}else{
		$(this).removeClass("hover");
	}
});

$(".city-list .city").click(function(){
	var current_city = $("#current_city").val();
	var select_city = $(this).attr("city");

	if(current_city != select_city){
		$(".show-city span").text($(this).text());
		$("#current_city").val(select_city);
		$("#change_city_link").click();
	}
	$('.city-list').hide();
});

$(".city-wrapper .close").click(function(){
	$(".show-city").click();
	//$('.city-list').hide();
});

</script>
<?php

/*
 * Howard.Wang
* 2012-8-1
*
*/



?>
<style type="text/css">

/**
* css for aq
*/
.aqi_grey {
	color: #CCC;
}

.aqi_green {
	color: #00E400;
}

.aqi_yellow {
	color: #FFFF00;
}

.aqi_orange {
	color: #FF7E00;
}

.aqi_red {
	color: #FF0000;
}

.aqi_purple {
	color: #950054;
}

.aqi_maroon {
	color: #7E0023;
}

.aqi_unknown {
	color: #9C9C9C;
	text-align: center;
}
</style>

<div class='weather_<?php echo Yii::app()->language;?>'
	id="weather_info">
	<?php
	$this->render($aqiView,array('wi'=>$wi,'aq'=>$aq));
	?>
</div>

<script type="text/javascript">

    function beforeSendFunc(){
		return true;
    }
    function weatherCityChanged(rt){

    	if(rt.flag == 'success'){
			$('#weather_info').html(rt.weatherInfo);
    	}
    }

</script>
