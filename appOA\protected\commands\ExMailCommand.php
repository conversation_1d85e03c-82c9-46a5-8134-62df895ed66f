<?php

class ExMailCommand extends CConsoleCommand {

    public $URLs;
    public $cacheId;
    private $adminId = 'superdude';
    private $key = '8662f9cd304153ae7b0cf8df900e8332';
    private $tokenData;

	public function init(){
        $this->URLs = array(
            'token' => 'https://exmail.qq.com/cgi-bin/token',

            'user-sync' => 'http://openapi.exmail.qq.com:12211/openapi/user/sync',
            'party-sync' => 'http://openapi.exmail.qq.com:12211/openapi/party/sync',
        );
        $this->cacheId['access_token'] = 'exmail-access-token';
        $this->cacheId['token_type'] = 'exmail-token-type';
	}


    public function actionGetToken() {
        getToken();
        print_r($this->tokenData);
    }

    public function actionSyncBranch() {
        $this->getToken();
        $crit = new CDbCriteria();
        $crit->compare('status', 10);
        $branches = CHtml::listData(Branch::model()->findAll($crit), 'branchid', 'title');

        $url = $this->URLs['party-sync'];
        foreach($branches as $title) {
            echo 'Creating ' . $title . "\n";
            $ch = curl_init();
            $params = array(
                'action' => 2,
                'DstPath' => sprintf('/%s' , $title)
            );
            $this->processRequest($ch, $url, $params);
        }
    }

    public function actionSyncUser($branchId=null) {
        $this->getToken();

        Yii::app()->language = 'zh_cn';

        $toDoBranches = array();
        $branches = CHtml::listData(Branch::model()->findAll(), 'branchid', 'title');
        if(!is_null($branchId) && in_array($branchId, array_keys($branches))){
            $toDoBranches[] = $branchId;
        } else {
            $toDoBranches = array_keys($branches);
        }

        Yii::import('common.components.general.CommonContentFetcher');
        $url = $this->URLs['user-sync'];
        foreach($toDoBranches as $_branchId){
            echo "processing " . $_branchId . "\n";
            $userData = CommonContentFetcher::getStaffByBranch($_branchId, $withData=false, $titleBilingual = true,
                $withPass=true);
            foreach($userData as $staff) {
                $ch = curl_init();
                $postParams = array(
                    'action' => 2,
                    'Alias' => trim($staff['email']),
                    'Name' => $staff['name'],
                    'Gender' => $staff['gender'],
                    'Position' => $staff['positionTitle'],
                    'ExtId' => $staff['uid'],
                    'Password' => $staff['pass'],
                    'Md5' => 1,
                    'PartyPath' => sprintf('/%s', $branches[$_branchId]),
//                    'Slave' => uniqid()."@ivygroup.org",
                    'OpenType' => 1
                );
                echo "    processing " . $staff['uid'] . "\n";
                $this->processRequest($ch, $url, $postParams);
            }
        }
        return true;
    }

    public function actionTestUser() {
        $this->getToken();

        $ch = curl_init();
        $postParams = array(
            'action' => 2,
            'Alias' => uniqid().'@ivyschools.com',
            'Name' => uniqid(),
            'Gender' => 1,
//            'Position' => uniqid(),
            'ExtId' => uniqid(),
            'Password' => uniqid().'Abc123',
            'Md5' => 0,
            'PartyPath' => '/TYG (天元港中心)',
            'OpenType' => 1,
            'Slave' => uniqid()."@ivykids.cn",
        );
        echo "    processing ... " . "\n";

        $url = $this->URLs['user-sync'];
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER,array('Authorization: ' . $this->tokenData['token_type'] . ' ' .
        $this->tokenData['access_token']));
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postParams);

        $result = curl_exec($ch);

        print_r($result);
    }

    private function processRequest($ch, $url, $postParams) {
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER,array('Authorization: ' . $this->tokenData['token_type'] . ' ' .
        $this->tokenData['access_token']));
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postParams);

        $result = curl_exec($ch);

        print_r($result);
    }

    private function getToken(){
        $accessToken = Yii::app()->cache->get($this->cacheId['access_token']);
        $tokenType = Yii::app()->cache->get($this->cacheId['token_type']);
        if ( $accessToken === false) {
            print_r("no cache found, requesting from server...\n");
            $params = array(
                'client_id' => $this->adminId,
                'client_secret' => $this->key,
                'grant_type' => 'client_credentials'
            );
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $this->URLs['token']);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

            $result = CJSON::decode(curl_exec($ch));
            print_r("data requested...\n");
            print_r($result);
            if( is_array($result) && isset($result['access_token']) && isset($result['token_type']) && isset
                ($result['expires_in']) ) {
                print_r("setting cache...\n");
                $expire = max($result['expires_in'] - 100, 100);
                Yii::app()->cache->set($this->cacheId['access_token'], $result['access_token'], $expire);
                Yii::app()->cache->set($this->cacheId['token_type'], $result['token_type'], $expire);
                $this->tokenData = array(
                    'access_token' => $result['access_token'],
                    'token_type' => $result['token_type']
                );
            }
        } else {
            print_r("cache found...\n");
            $this->tokenData = array(
                'access_token' => $accessToken,
                'token_type' => $tokenType
            );
        }
        return $this->tokenData;

    }

}