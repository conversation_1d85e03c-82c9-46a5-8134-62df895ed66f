<style>
     [v-cloak] {
        display: none;
    }
    .teacherImg{
        width:24px;
        height:24px;
        border-radius:50%;
        object-fit: cover;
    }
    .classlist{
        border: 1px solid #DBDBDB;
        border-radius: 4px;
        padding:16px;
        margin-bottom:16px;
        background:#fff
    }
    .font16{
        font-size:16px
    }
    .pt5{
        padding-top:3px
    }
    .repTeacher{
        background: #FAFAFA;
        border-radius: 4px;
        padding:12px;
        margin-bottom:10px
    }
    .table_wrap {
        width: 100%;
        overflow: auto;
        float:left;
        max-height:600px
    }
    #classlist{
        max-height:600px;
        overflow-y:auto

    }
    .table {
        table-layout: fixed;
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        border-left: 1px solid #DDDDDD;
        border-top: 1px solid #DDDDDD;
    }
    .table td,.table th {
        width: 250px;
        border-right: 1px solid #DDDDDD;
        border-bottom: 1px solid #DDDDDD;
        border-top:none !important;
        padding: 16px !important
    }
    /* 表头固定 */
    .table tr th {
        position: sticky;
        top: 0;
        background: #FAFAFA;
        z-index:1
    }
    .scrollbar::-webkit-scrollbar {
        /*滚动条整体样式*/
        width : 10px;  /*高宽分别对应横竖滚动条的尺寸*/
        height:10px;
    }
    .scrollbar::-webkit-scrollbar-thumb {
        border-radius   : 10px;
        background-color: skyblue;
        background-image: -webkit-linear-gradient(
        45deg,
        rgba(255, 255, 255, 0.2) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0.2) 75%,
        transparent 75%,
        transparent
        );
    }
    .scrollbar::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0.2);
        border-radius: 10px;
        background   : #ededed;
    }
    .reserved{
        background: #F6F9FF;
        border-radius: 4px;
        padding:12px
    }
    .timeStart{
        border-bottom: 1px solid #E5E7EB;
    }
    .timeList{
        min-height:100px
    }
    .green{
        color:#1FA11A
    }
    .avatar{
        width:54px;
        height:54px;
        border-radius:50%;
        object-fit: cover;
    }
    .ml0{
        margin-left:0px !important
    }
    .reserveImg{
        width:44px;
        height:44px;
        border-radius:50%;
        object-fit: cover;
    }
    .red{
        color:#D9534F
    }
    .colorc{
        color:#cccccc
    }
    .lineHeight{
        line-height:100px
    }
    .border{
        border: 1px solid #E5E7EB;
        border-radius: 5px;
        padding:10px
    }
    .unDate{
        display: inline-block;
        background: #4D88D2;
        color: #fff;
        padding: 5px 15px;
        margin-left: -15px;
        font-size: 14px;
        border-radius: 0 81px 81px 0;
    }
    .blue{
        background:rgba(77, 136, 210, 0.1);
        border:1px solid #4D88D2
    }
    .blueColor{
        color:#4D88D2 !important
    }
    .loading{
    width:98%;
    height:95%;
    background:#fff;
    position: absolute; 
    opacity: 0.5;
    z-index: 99
}
.loading span{
    width:100%;
    height:100%;
    display:block;
    background: url("<?php echo Yii::app()->theme->baseUrl?>/images/loading.gif")no-repeat center center ;
}
.labelBg{
    background:rgba(0, 0, 0, 0.04);
    color:rgba(0, 0, 0, 0.65);
    padding: 5px 10px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    margin-right:10px;
    font-weight:400;
    margin-bottom:10px;
    display:inline-block
}
.maxWidth{
    width:300px
}
.ghost{
    background:red !important
}
.alertPadding{
    padding:20px 60px
}
.repeat{
    background:#4D88D2;
    color:#fff;
    width: 18px;
    height: 18px;
    text-align: center;
    line-height: 18px;
    border-radius: 50%;
}
.translate{
    position: absolute;
    width: 25px;
    height: 25px;
    bottom: -8px;
    left: 40px;
}
.example{
    width: 25px;
    height: 25px;
}
.exportHide{
    display:none;
    margin-left:-15px
}
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Workspace'), array('//mcampus/default/index'))?></li>
        <li><?php echo Yii::t('site', 'G1-5 PTC Management') ?></li>
    </ol>
    <div class="row" id='container' v-cloak>
        <div class='col-md-12 col-sm-12 mb20'>
            <div class='flex mb20'>
                <div class='form-inline  flex1'>
                    <select class="form-control" id="startyear" onchange="change()" >
                        <?php foreach($this->startYearList as $startYear => $text): ?>
                            <option <?php echo $startYear == $this->startYear ? 'selected' : ''; ?> value='<?php echo $startYear; ?>'><?php echo $text; ?></option>
                        <?php endforeach ?>
                    </select>
                    <select class="form-control" id="semester" onchange="change()" >
                        <?php foreach($this->semesterList as $semester => $text): ?>
                            <option <?php echo $semester == $this->semester ? 'selected' : ''; ?> value='<?php echo $semester; ?>'><?php echo $text; ?></option>
                        <?php endforeach ?>
                    </select>
                </div>
                <button  type="button" class="btn btn-primary ml20" @click='exportTable()'><?php echo Yii::t('ptc','Export Daily Visitors List') ?></button>
            </div>
            <ul class="nav nav-tabs pt10">
                <li role="presentation" class="active"  @click='showType("reserve")'><a href="#reserve" role="tab" data-toggle="tab"><?php echo Yii::t("ptc", "Schedule Overview");?></a></li>
                <li role="presentation"  @click='showType("time")'><a href="#time" role="tab" data-toggle="tab"><?php echo Yii::t("ptc", "TimeSlots Settings");?></a></li>
            </ul>
        </div>
        <div class="tab-content">
            <div class="tab-pane active" id="reserve">
                <div class='col-md-2 col-sm-12 '>
                    <div class="list-group">
                        <span class="list-group-item " :class='classId==list.id?"active":""' v-for='(list,index) in classListData' @click='classChild(list.id)'>{{list.title}}</span>
                    </div>
                </div>
                <div class='loading ' v-if='classLoading'>
                    <span></span>
                </div>
                <div v-if='!classLoading'>
                    <div v-if='classId!="" && Object.keys(overview).length!=0'>
                        <div class='col-md-10 col-sm-12 font14 mb20' v-if='overview.subjectTeacher.length!=0'>
                            <span class='color6 mr20' v-for='(list,id,i) in overview.subjectTeacher'><span class='glyphicon glyphicon-user mr5'></span>{{overview.subjectConfig[id]}}：<span class='color3'>{{overview.teachersInfo[list].name}}</span></span> 
                        </div>
                        <div class='col-md-10 col-sm-12 mb20'>
                           <span class='font14 color3'><?php echo Yii::t('ptc','Legend: ') ?></span> <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/A-E.png' ?>" class='example'  alt=""> <span class='color6'>{{overview.translationConfig[1].title}}</span> 
                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/E-A.png' ?>" class='example ml20' alt=""> <span class='color6'>{{overview.translationConfig[2].title}}</span>
                        </div>
                        <div class='col-md-6 col-sm-12'>
                            <div class="panel panel-default">
                                <div class="panel-heading font14 color3"><?php echo Yii::t("ptc", "Scheduled");?>（{{overview.bookedChildIds.length}}）</div>
                                <div class="panel-body">
                                    <div v-if=' Object.keys(overview.ptcData).length!=0'>
                                        <div v-for='(date,key,idx) in overview.ptcData'>
                                            <span class='unDate'>{{key}}</span> 
                                            <div v-for='(item,i) in date' class='mt20'>
                                                <div class=' font14 color3 mb10'>
                                                    <span class='glyphicon glyphicon-time'></span> 
                                                    <strong class='font16'>{{item.start}}-{{item.end}}</strong>
                                                </div>
                                                <div class='ml15 mb10' v-if='Object.keys(item.location).length!=0'>
                                                    <span class="label label-default labelBg" v-for='(loca,sub,i) in item.location'>{{overview.subjectConfig[sub]}}：{{loca}}</span>
                                                </div>
                                                <div v-else class='color6 ml15 mb20 font14'>
                                                    <?php echo Yii::t("ptc", "No location provided.");?>
                                                </div>
                                                <div>
                                                    <div class='col-md-4 col-sm-12' v-for='(list,child,index) in item.items'>
                                                        <div class="media">
                                                            <div class="media-left pull-left media-middle mr15">
                                                                <a href="javascript:void(0)" class=''>
                                                                    <img  :src="overview.childInfo[child].avatar" data-holder-rendered="true" class="reserveImg">
                                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/E-A.png' ?>" class='translate' alt="" v-if='overview.translation[child]==3'>
                                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/A-E.png' ?>" class='translate' alt="" v-if='overview.translation[child]==2'>
                                                                </a>
                                                            </div>
                                                            <div class="media-body mt5 media-middle">
                                                                <h4 class="media-heading font14 color3"><strong>{{overview.childInfo[child].name}} </strong> <span class='ml5 glyphicon glyphicon-repeat repeat font12' @click='reset(child)' onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" title="<?php echo Yii::t("ptc", "Cancel Schedule"); ?>" data-placement="top"></span> </h4>
                                                                <div class="color6 font12 mt5"><span v-for='(sub,len) in list'>{{sub.subject}} <span v-if='len!=list.length-1'>- </span> </span> </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class='clearfix'></div>
                                                </div>
                                            </div>
                                            <hr>
                                        </div>
                                    </div>
                                    <div class="alert alert-warning" role="alert" v-else><?php echo Yii::t("ptc", "No Data");?></div>                       
                                </div>
                            </div>
                        </div>
                        <div class='col-md-4 col-sm-12'>
                            <div class="panel panel-default">
                                <div class="panel-heading  font14 color3"><?php echo Yii::t("ptc", "Not scheduled ");?>（{{overview.unBookedChildIds.length}}）</div>
                                <div class="panel-body">
                                    <div v-if='overview.unBookedChildIds.length!=0'>
                                        <div class='col-md-6 col-sm-12 mb20' v-for='(list,index) in overview.unBookedChildIds'>
                                            <img :src="overview.childInfo[list].avatar" alt="" class="reserveImg"> 
                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/E-A.png' ?>" class='translate' alt="" v-if='overview.translation[list]==3'>
                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/A-E.png' ?>" class='translate' alt="" v-if='overview.translation[list]==2'>
                                            <span class="color3 font14 ml10">{{overview.childInfo[list].name}}</span>
                                        </div>
                                    </div>
                                    <div class="alert alert-warning" role="alert" v-else><?php echo Yii::t("ptc", "No Data");?></div>                       
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-else-if='classListData.length!=0'  class='col-md-10 col-sm-12'>
                        <div class="alert alert-warning" role="alert" ><?php echo Yii::t('ptc', 'Select a class to continue') ?></div>
                    </div>
                </div>
            </div>
            <div id="time" class="tab-pane ">
                <div class='loading ' v-if='subLoading'>
                    <span></span>
                </div>
                <div v-if='!subLoading'>
                    <div class='col-md-12 col-sm-12 ' v-if='Object.keys(indexData).length!=0'>
                        <div class="alert alert-warning" role="alert" v-if='showClassTeacher()'><?php echo Yii::t('ptc', 'Please assign meeting teachers in class list below') ?></div>
                        <div class='form-inline mb20'>
                            <div class="form-group">
                                <label for="exampleInputName2"><?php echo Yii::t('ptc', 'Parent schedule time') ?></label>
                                <!-- <input type="text" class="form-control select_2  ml0" id="startDate" v-model='startdate'  placeholder="<?php echo Yii::t("newDS", "Select a date"); ?>"  :value='startdate'>
                                <select class="form-control" v-model='startHour'>
                                    <option value="">请选择</option>
                                    <option :value="index" v-for='index in 23'>{{index}}</option>
                                </select>
                                :
                                <select class="form-control" v-model='startMin'>
                                    <option value="">请选择</option>
                                    <option :value="startMinute(index)" v-for='index in 60'>{{startMinute(index)}}</option>
                                </select> -->
                                <v-date-picker v-model="startdate" mode="dateTime" is24hr  :model-config="modelConfig" :masks='masks'>
                                    <template v-slot="{ inputValue, inputEvents }">
                                        <input
                                        class="form-control"
                                        :value="inputValue"
                                        v-on="inputEvents"
                                        />
                                    </template>
                                </v-date-picker>
                            </div>
                            <div class="form-group">
                                <label for="exampleInputName2" class='ml10 mr10'>-</label>
                                <!-- <input type="text" class="form-control select_2  ml10" id="endDate" v-model='enddate'  placeholder="<?php echo Yii::t("newDS", "Select a date"); ?>"  :value='enddate'>
                                <select class="form-control" v-model='endHour'>
                                    <option value="">请选择</option>
                                    <option :value="index" v-for='index in 23'>{{index}}</option>
                                </select>
                                :
                                <select class="form-control" v-model='endMin'>
                                    <option value="">请选择</option>
                                    <option :value="startMinute(index)" v-for='index in 60'>{{startMinute(index)}}</option>
                                </select> -->
                                <v-date-picker v-model="enddate" mode="dateTime" is24hr  :model-config="modelConfig" :masks='masks'>
                                    <template v-slot="{ inputValue, inputEvents }">
                                        <input
                                        class="form-control"
                                        :value="inputValue"
                                        v-on="inputEvents"
                                        />
                                    </template>
                                </v-date-picker>
                            </div>
                            <button type="submit" class="btn btn-primary ml20" @click='saveTime'><?php echo Yii::t('global','Save') ?></button>
                        </div>
                    </div>
                    <div class='clearfix'></div>
                    <div class='flex'>
                        <div class='scroll-box maxWidth col-md-4 col-sm-4 '  v-if='indexData.classTeacherData' id='classlist' data-id='classlist' :style='"max-height:"+maxHeight'>
                            <div class='classlist ' v-for='(list,index) in indexData.classList'   :class='[indexData.classTeacherData[list.id] && Object.keys(indexData.classTeacherData[list.id]).length==3?"drag":"filter", showClassId==list.id?"blue":""]' :data-id="list.id">
                                <div  @click='expandTeacher(list,index)'>
                                    <div class='flex' >
                                        <div class='flex1'><strong class='color3 font16'>{{list.title}}</strong></div>
                                        <div class='pt5'>
                                            <span class='glyphicon glyphicon-chevron-up font14' v-if='showTeacher==index'></span>
                                            <span class='glyphicon glyphicon-chevron-down color6 font14' v-else></span>
                                            <span class='glyphicon glyphicon-move ml10  font14'  :class='indexData.classTeacherData[list.id] && Object.keys(indexData.classTeacherData[list.id]).length==3?"color6 move":"colorc"' ></span>
                                        </div>
                                    </div>
                                    <div class='mt10' :class='showTeacher==index?"hide":"show"' v-if='showTimeNum'>
                                        <div v-if='indexData.classTeacherData[list.id]'>
                                            <div v-for='(item,key,idx) in indexData.subjectList' class='pull-left'>
                                                <img v-if='indexData.classTeacherData[list.id][key]' :src="indexData.teachersInfo[indexData.classTeacherData[list.id][key]].photoUrl" class='teacherImg mr10' alt="">
                                                <img v-else src="http://m2.files.ivykids.cn/cloud01-file-8025768Fk0ZahbEW21YJ1L00suKO8vEQIWO.png" class='mr10 teacherImg' alt="">
                                            </div>
                                            <span class='color9 ml10' v-if='indexData.classTeacherData[list.id] && Object.keys(indexData.classTeacherData[list.id]).length!=3'>
                                            <?php echo Yii::t('ptc', 'Assign teachers') ?></span>
                                            <span v-else class='pull-right cur-p ' @click.stop='showSameClass(list.id)' onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" title="<?php echo Yii::t("ptc", "Click to view PTC time"); ?>" data-placement="top"><span class='glyphicon glyphicon-eye-open' :class='showClassId==list.id?"blueColor":""' ></span> <?php echo Yii::t('ptc', ' ') ?>{{classScheduleNum[list.id] || 0}}<?php echo Yii::t('ptc', ' slots assigned') ?></span>
                                            <div class='clearfix'></div>
                                        </div>
                                        <div v-else>
                                            <img v-for='(item,key,idx) in indexData.subjectList' src="http://m2.files.ivykids.cn/cloud01-file-8025768Fk0ZahbEW21YJ1L00suKO8vEQIWO.png" class='mr10 teacherImg' alt="">
                                            <span class='color9 ml10'><?php echo Yii::t('ptc', 'Assign teachers') ?></span>
                                        </div>
                                    </div>
                                </div>
                                <div class='mt10'  v-if='showTeacher==index'>
                                    <div v-for='(item,key,idx) in indexData.subjectList' class='repTeacher'>
                                        <div class='flex'>
                                            <div class='flex1 color3 font14'> {{item}} </div>
                                            <div class='color6'>20 minutes</div>
                                        </div>
                                        <div class='flex mt10' v-if='indexData.classTeacherData[list.id]'>
                                            <div class='flex1'>
                                                <img v-if='indexData.classTeacherData[list.id][key]' :src="indexData.teachersInfo[indexData.classTeacherData[list.id][key]].photoUrl" class='teacherImg mr5' alt="">
                                                <img v-else src="http://m2.files.ivykids.cn/cloud01-file-8025768Fk0ZahbEW21YJ1L00suKO8vEQIWO.png" class=' teacherImg' alt="">
                                                <span v-if='indexData.classTeacherData[list.id][key]' class='color3 '>{{indexData.teachersInfo[indexData.classTeacherData[list.id][key]].name}}</span>
                                            </div>
                                            <div class='font14 text-primary' v-if='indexData.classTeacherData[list.id][key]' @click='editTeacher(list)'><?php echo Yii::t('ptc', 'Replace a teacher') ?></div>
                                            <div class='font14 text-primary' v-else @click='editTeacher(list)'><?php echo Yii::t('ptc', 'Arrange teachers') ?></div>
                                        </div>
                                        <div v-else  class='flex mt10' >
                                            <div class='flex1'>
                                                <img src="http://m2.files.ivykids.cn/cloud01-file-8025768Fk0ZahbEW21YJ1L00suKO8vEQIWO.png" class=' teacherImg' alt="">
                                            </div>
                                            <div class='font14 text-primary' @click='editTeacher(list)'><?php echo Yii::t('ptc', 'Arrange teachers') ?></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class='col-md-8 col-sm-8 flex1'>
                            <div class='table_wrap scrollbar  mb20' v-if='indexData.scheduleList && Object.keys(indexData.scheduleList).length!=0 ' :style='"max-height:"+maxHeight'>
                                <table class="table" id='table' >
                                    <tr>
                                        <th v-for='(list,key,index) in indexData.scheduleList' class='font16 text-center'>
                                            <strong>{{key}}</strong> 
                                            <div class='font14 mt5 color6'>{{list[0].week}}</div>
                                        </th>
                                    </tr>
                                    <tr>
                                        <td v-for='(item,key,index) in indexData.scheduleList'> 
                                            <div v-for='(_item,idx) in item' >
                                                <div  v-if='indexData.classScheduleData[_item.key] && indexData.classScheduleData[_item.key].length!=0' class='reserved mb20'>
                                                    <div class='font14 color3 pb10 timeStart'>
                                                        <span class='glyphicon glyphicon-time mr10'></span> 
                                                        <strong>{{_item.start}} - {{_item.end}}</strong>
                                                        </div>
                                                    <div class='timeList' :data-id="key+'_'+_item.id">
                                                        <div v-if='indexData.classScheduleData[_item.key] && indexData.classScheduleData[_item.key].length!=0' >
                                                                <div class='flex mt15 border' :class='showClassId==classData.class_id?"blue":""' v-for='(classData,i) in indexData.classScheduleData[_item.key]' @click='showSameClass(classData.class_id)'>
                                                                    <span class='glyphicon glyphicon-ok-sign green pt5' v-if='classData.location==1'></span>
                                                                    <span class='glyphicon glyphicon-question-sign red pt5' v-else></span>
                                                                    <div class='flex1 ml10'>
                                                                        <div class='color3 font14'>{{classTitle(classData.class_id)}}</div>
                                                                        <div v-for='(item,key,idx) in indexData.classTeacherData[classData.class_id]' class='mt5'>
                                                                            <img :src="indexData.teachersInfo[item].photoUrl" class='teacherImg mr10 pull-left cur-p' alt="" @click.stop='myPTC(item)' onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" :title="indexData.teachersInfo[item].name" data-placement="top">
                                                                        </div>
                                                                        <div class='clearfix'></div>
                                                                        <div class='mt10'>
                                                                            <span class='glyphicon glyphicon-edit mr20 text-primary' @click.stop='setLocation(_item,classData,key)'></span>
                                                                            <span class='glyphicon glyphicon-trash text-primary' @click.stop='deleteSchedule(classData,_item.key,i)'></span>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                        </div>
                                                        <div v-else class='text-center colorc font14 lineHeight' >
                                                            <?php echo Yii::t('ptc','Drag and drop class here') ?>
                                                        </div>
                                                    </div>
                                                </div> 
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                            </div>           
                            <div class="alert alert-warning" role="alert" v-else><?php echo Yii::t("ptc", "No Data");?></div>                       
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal fade" id="teacher" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false"  data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" ><?php echo Yii::t('ptc','Arrange teachers') ?> <span id="remarks_t"></span></h4>
                    </div>
                    <div class="modal-body" v-if='Object.keys(replaceClass).length!=0'>
                        <div class='col-md-12 col-sm-12'><strong class='font16 color3'>{{replaceClass.title}}</strong> </div>
                        <div class='col-md-4 col-sm-12 mt20 text-center' v-for='(list,key,index) in indexData.subjectList'>
                            <div class='mb20 color3 font16'>{{list}}</div>
                            <div v-if='modelTeacher[key]!="" && classToTeacher[modelTeacher[key]]'>
                                <img :src="classToTeacher[modelTeacher[key]].photoUrl" alt="" class='avatar'>
                            </div>
                            <div v-else>
                                <img src="http://m2.files.ivykids.cn/cloud01-file-8025768Fk0ZahbEW21YJ1L00suKO8vEQIWO.png" class='avatar' alt="">
                            </div>
                            <div class='mt15 form-inline'>
                                <select class="form-control" v-model='modelTeacher[key]' @change=$forceUpdate()>
                                <option value=''><?php echo Yii::t("site", "Please Select");?></option>
                                    <option v-for='(item,key,idx) in classToTeacher' :value='key'>{{item.name}}</option>
                                </select>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" @click='saveTeacher'><?php echo Yii::t('global','Save') ?></button>
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal fade" id="interview" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false"  data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog " role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" ><?php echo Yii::t('ptc','Set location') ?> <span id="remarks_t"></span></h4>
                    </div>
                    <div class="modal-body" v-if='Object.keys(interviewItem).length!=0'>
                        <div class='color6 font14'><?php echo Yii::t("labels", "Class");?><?php echo Yii::t("global", ": ");?> <span class='color3'>{{classTitle(interviewClassData.class_id)}}</span> </div>
                        <div class='color6 font14'><?php echo Yii::t("ptc", "Time Slot");?><?php echo Yii::t("global", ": ");?> <span class='color3'>{{interviewItem.day}} {{interviewItem.start}} {{interviewItem.end}}</span> </div>
                        <div class="form-horizontal mt20">
                            <div class="form-group" v-for='(list,key,index) in indexData.subjectList'>
                                <label for="inputEmail3" class="col-sm-2 control-label">{{list}}</label>
                                <div class="col-sm-10" >
                                    <div class='mt10'>
                                        <img :src="indexData.teachersInfo[indexData.classTeacherData[interviewClassData.class_id][key]].photoUrl" alt="" class='teacherImg'>
                                        <span class='color3 font14 ml10'>{{indexData.teachersInfo[indexData.classTeacherData[interviewClassData.class_id][key]].name}}</span>
                                    </div>
                                    <input type="text" class="form-control mt20"  placeholder="<?php echo Yii::t('ptc','Input meeting location') ?>" :value='subject_location[key]' v-model='subject_location[key]'>
                                </div>
                            </div>
                        </div>
                        <div>
                            <p class='color3 font14 mb15'><?php echo Yii::t('ptc','Sync location to the following slots:') ?></p>
                            <div>
                                   <div v-if='copyScheduleData.length!=0'>
                                        <label class="checkbox-inline mr20 ml0 mb20" v-for='(item,idx) in copyScheduleData' >
                                            <input type="checkbox" :value="item.schedule_id" v-model='schedule_ids'> {{item.date}} {{item.start}}-{{item.end}}
                                        </label>
                                     </div>
                                
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" @click='assignLocation'><?php echo Yii::t('global','Save') ?></button>
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal fade" id="delModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
            <div class="modal-dialog modal-sm" role="document">
                <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "Delete");?></h4>
                </div>
                <div class="modal-body">      
                    <div ><?php echo Yii::t("ptc", "Are you sure you want to delete?");?></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>

                    <button type="button"  class="btn btn-primary" @click='deleteSchedule("model")'><?php echo Yii::t("newDS", "Delete");?></button>
                </div>
                </div>
            </div>
        </div>
        <div class="modal fade" id="resetModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
            <div class="modal-dialog modal-sm" role="document">
                <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("ptc", "Cancel Schedule");?></h4>
                </div>
                <div class="modal-body">      
                    <div ><?php echo Yii::t("ptc", "Confirm to cancel this schedule?");?></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>

                    <button type="button"  class="btn btn-primary" @click='reset("model")'><?php echo Yii::t("global", "OK");?></button>
                </div>
                </div>
            </div>
        </div>
        <div class="modal fade" id="exportModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("ptc", "Export Daily Visitors List");?></h4>
                </div>
                <div class="modal-body">      
                    <div class='mb10 color6 font14'><?php echo Yii::t("ptc", "请选择要导出的名单");?></div>
                    <div class='col-md-3 col-sm-3 mb10 ' v-for='(list,index) in exportDays'>
                        <div  :id='"export"+index' class='loading exportHide' style='height:100%'>
                            <span></span>
                        </div>
                        <div class='border text-center cur-p font14' @click='exportData(list.day,index)'>
                            <div class='color3'><label>{{list.day}}</label></div>
                            <div class='color6'>{{list.weekday}}</div>
                        </div>
                    </div>
                    <div class='clearfix'></div>
                </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    function change() {
        var startyear = $('#startyear').val();
        var semester = $('#semester').val();
        var url = '<?php echo $this->createUrl('index'); ?>&startyear=' + startyear + '&semester=' + semester;
        window.location.href = url;
    }
    var container = new Vue({
        el: "#container",
        data: {
            yid: "<?php echo $this->yid; ?>",
            startYearList: "<?php echo $this->startYearList; ?>",
            startYear: "<?php echo $this->startYear; ?>",
            semester: "<?php echo $this->semester; ?>",
            semesterList: <?php echo json_encode($this->semesterList); ?>,
            indexData:{},
            classId:'',
            showTeacher:null,
            replaceClass:{},
            modelTeacher:{},
            interviewItem:{},
            interviewClassData:{},
            copyScheduleData:{},
            schedule_ids:[],
            subject_location:{},
            delList:{},
            delKey:'',
            delIndex:'',
            overview:{},
            classToTeacher:{},
            startdate:'',
            enddate:'',
            showTimeNum:true,
            showClassId:'',
            classScheduleNum:{},
            maxHeight:'',
            classListData:[],
            classLoading:false,
            subLoading:false,
            resetId:'',
            exportDays:[],
            startHour:'',
            endHour:'',
            endMin:'',
            startMin:'',
            modelConfig: {
                type: 'string',
                mask: 'YYYY-MM-DD HH:mm', // Uses 'iso' if missing
            },
            masks: {
                inputDateTime24hr: 'YYYY-MM-DD HH:mm',
            },
        },
        mounted(){
            this.maxHeight=$(window).height()-150+'px'
            let that=this
            $.ajax({
                url: '<?php echo $this->createUrlPtc("classList") ?>',
                type: "post",
                dataType: 'json',
                data: {
                },
                success: function(data) {
                    if(data.state=='success'){  
                       that.classListData=data.data
                    }else{ 
                        resultTip({error: 'warning', msg:data.message});
                    }
                },
                error:function(data){
                    resultTip({error: 'warning', msg: '请求错误'});
                }
            })
        },
        methods: {
            showType(type){
                if(type=='time'){
                    if(Object.keys(this.indexData).length==0){
                        this.initData()
                    }
                }
            },
            initData(){
                let that=this
                this.subLoading=true
				$.ajax({
                    url: '<?php echo $this->createUrlPtc("scheduleData") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if(data.state=='success'){  
                           that.indexData=data.data
                           that.classScheduleNum=data.data.classScheduleNum
                           that.startdate=data.data.ptcInfo.start
                           that.enddate=data.data.ptcInfo.end
                           that.subLoading=false
                            // setTimeout(function() {
                            //     that.$nextTick(()=>{
                            //         $("#startDate").datepicker({
                            //             dateFormat: "yy-mm-dd ",
                            //             onClose : function(dateText, inst){//当日期面板关闭后触发此事件（无论是否有选择日期）
                            //                 that.startdate = dateText
                            //             }
                            //         });
                            //         $("#endDate").datepicker({
                            //             dateFormat: "yy-mm-dd ",
                            //             onClose : function(dateText, inst){//当日期面板关闭后触发此事件（无论是否有选择日期）
                            //                 that.enddate = dateText
                            //             }
                            //         });
                            //         that.ragFunc()
                            //     })
                            // }, 500);
                        }else{ 
                           that.subLoading=false
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        that.subLoading=false
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            showSameClass(id){
                if(this.showClassId==id){
                    this.showClassId=''
                }else{
                    this.showClassId=id
                }
            },
            childNum(data){
                var dataLen=[]
                for(var key in data){
                    for(var i=0;i<data[key].items.length;i++){
                        dataLen.push(data[key].items[i])
                    }
                }
                return dataLen.length
            },
            classChild(id){
                this.classId=id
                let that=this
                this.classLoading=true
				$.ajax({
                    url: '<?php echo $this->createUrlPtc("getPtcData") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        class_id:id
                    },
                    success: function(data) {
                        if(data.state=='success'){  
                           that.overview=data.data
                           that.classLoading=false
                        }else{ 
                            that.classLoading=false
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        that.classLoading=false
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            expandTeacher(list,index){
                if(index==this.showTeacher){
                    this.showTeacher=null
                }else{
                    this.showTeacher=index
                }
                this.replaceClass={}
            },
            classTitle(id){
                var data
                this.indexData.classList.forEach(parent => {
                    if(parent.id==id){
                        data=parent.title
                    }
                })
                return data
            },
            editTeacher(list){
                this.replaceClass=list
                this.modelTeacher={}
                if(this.indexData.classTeacherData[list.id]!=undefined){
                    this.modelTeacher=JSON.parse(JSON.stringify(this.indexData.classTeacherData[list.id]));
                }else{
                    for(var key in this.indexData.subjectList){
                        this.modelTeacher[key]=''
                    }
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrlPtc("teacherList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        class_id:that.replaceClass.id,
                    },
                    success: function(data) {
                        if(data.state=='success'){  
                            if(data.data.length!=0){
                                that.classToTeacher=data.data
                                that.indexData.teachersInfo=Object.assign(data.data,that.indexData.teachersInfo)
                                $('#teacher').modal('show')
                            }else{
                                resultTip({error: 'warning', msg:"暂无老师"});
                            }
                        }else{ 
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
                
            },
            saveTeacher(){
                let that=this
                for(var key in this.indexData.subjectList){
                    if(this.modelTeacher[key]==''){
                        resultTip({error: 'warning', msg: '请选择老师'});
                        return
                    }
                }
				$.ajax({
                    url: '<?php echo $this->createUrlPtc("assignTeacher") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        class_id:that.replaceClass.id,
                        subject_teacher:that.modelTeacher
                    },
                    success: function(data) {
                        if(data.state=='success'){  
                            that.$set(that.indexData.classTeacherData,that.replaceClass.id,that.modelTeacher)
                           resultTip({
                                msg: data.state
                            });
                            $('#teacher').modal('hide');
                        }else{ 
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            setLocation(item,datas,key){
                this.interviewItem=item
                this.interviewClassData=datas
               this.schedule_ids=[datas.schedule_id]
                let that=this
				$.ajax({
                    url: '<?php echo $this->createUrlPtc("locationData") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        class_id:datas.class_id,
                        day: key,
                        start: item.start,
                        end: item.end
                    },
                    success: function(data) {
                        if(data.state=='success'){  
                            if(data.data.locationData.length==0){
                                for(var key in that.indexData.subjectList){
                                    that.subject_location[key]=''
                                }
                            }else{
                                for(var key in data.data.locationData){
                                    that.subject_location[key]=data.data.locationData[key].location
                                }
                            }
                            that.copyScheduleData=data.data.scheduleData
                            $('#interview').modal('show')
                        }else{ 
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            assignLocation(){
                let that=this
                for(var key in this.indexData.subjectList){
                    if(this.subject_location[key]==''){
                        resultTip({error: 'warning', msg: '<?php echo Yii::t("ptc", "Input meeting location");?> '});
                        return
                    }
                }
				$.ajax({
                    url: '<?php echo $this->createUrlPtc("assignLocation") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        class_id:that.interviewClassData.class_id,
                        subject_location: that.subject_location,
                        schedule_ids: that.schedule_ids
                    },
                    success: function(data) {
                        if(data.state=='success'){
                            for(var key in that.indexData.classScheduleData){
                                for(var i=0;i<that.indexData.classScheduleData[key].length;i++){
                                    if( that.schedule_ids.indexOf(that.indexData.classScheduleData[key][i].schedule_id)!=-1 ){
                                        that.indexData.classScheduleData[key][i].location=1
                                    }
                                }
                            }  
                            resultTip({
                                msg: data.state
                            });
                            $('#interview').modal('hide')
                        }else{ 
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
           ragFunc () {
                var el = document.getElementById('classlist');
                //设置配置
                let that=this
                var ops1 = {
                    group: { name: "itxst.com", pull: 'clone', put: false },
                    animation: 1000,
                    draggable: ".drag",
                    direction: 'vertical',
                    handle:".move",
                    forceFallback: true,
                    sort: false,
                    filter:".filter",
                    onClone: function (evt) {
                    },
                    onEnd: function (evt) {
                       if(evt.to.dataset.id!='classlist'){
                            var cloneEl = evt.item;
                            cloneEl.innerHTML =''
                            cloneEl.className =''
                            that.assignSchedule(cloneEl.dataset.id,evt.to.dataset.id)
                       }
                    },
                };
                //初始化
                var sortable1 = Sortable.create(el, ops1);
                var ops2 = {
                    group: { name: "itxst.com", pull: 'clone', put: true },
                    animation: 1000,
                    draggable: ".timeList",
                    direction: 'vertical',
                    forceFallback: true,
                    sort: false,
                };
                //初始化
                const parents = document.querySelectorAll('.timeList')
                var newData=[]
                parents.forEach(parent => {
                    var sortable = new Sortable(parent,ops2 )
                    newData.push(sortable)
                })
           },
           assignSchedule(classid,toId){
               if(toId==undefined){
                   return
               }
              let that=this
              let date=toId.split(/[_]/)
              var start=''
              var day=''
              var end=''
              for(var i=0;i<this.indexData.scheduleList[date[0]].length;i++){
                  let list=this.indexData.scheduleList[date[0]][i]
                  if(list.id==date[1]){
                      start=list.start
                      day=list.day
                      end=list.end
                  }
              }
				$.ajax({
                    url: '<?php echo $this->createUrlPtc("assignSchedule") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        class_id:classid,
                        day: date[0],
                        start: start,
                        end: end
                    },
                    success: function(data) {
                        if(data.state=='success'){ 
                            if(that.indexData.classScheduleData[data.data.schedule_key]){
                                that.indexData.classScheduleData[data.data.schedule_key].push(
                                    {
                                        class_id: data.data.class_id,
                                        schedule_id: data.data.schedule_id,
                                        location:0
                                    }
                                )
                            }else{
                                let addscheduleData=[{
                                        class_id: data.data.class_id,
                                        schedule_id: data.data.schedule_id,
                                        location:0
                                    }]
                                    that.$set(that.indexData.classScheduleData,data.data.schedule_key,addscheduleData)
                            }
                            that.classScheduleNum[classid]=data.data.classScheduleNum
                            var newArray = that.indexData.classList.slice(0);
                            $('#classlist').html('')
                            that.indexData.classList =[];
                            that.$nextTick(function () {
                                that.indexData.classList = newArray;
                            });
                           resultTip({
                                msg: data.state
                            });
                        }else{ 
                            if(data.data.length!=''){
                                var name=''
                                for(var i=0;i<data.data.length;i++){
                                   name+=that.indexData.teachersInfo[data.data[i]].name+'<br>'
                                }
                                let text='<?php echo Yii::t("ptc", "Failed! Teachers conflict:");?><br>'+name
                                head.dialog.alert('<div class="alertPadding font14">'+text+'</div>');
                                // resultTip({error: 'warning', msg:text});

                            }else{
                                head.dialog.alert('<div class="alertPadding font14">'+data.message+'</div>');
                                // resultTip({error: 'warning', msg:data.message});
                            }
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
           },
           deleteSchedule(data,key,i){
                if(data!='model'){
                    this.delList=data
                    this.delKey=key
                    this.delIndex=i
                    $('#delModal').modal('show')
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrlPtc("deleteSchedule") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:that.delList.schedule_id
                    },
                    success: function(data) {
                        if(data.state=='success'){  
                            that.indexData.classScheduleData[that.delKey].splice(that.delIndex,1)
                            that.$set(that.classScheduleNum,that.delList.class_id, data.data)
                            resultTip({
                                msg: data.state
                            });
                            $('#delModal').modal('hide')
                        }else{ 
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
           },
           startMinute(index){
            return index-1<10?'0'+(index-1):index-1
           },
           saveTime(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrlPtc("ptcInit") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        dept:'1',
                        "p_schedule_start":that.startdate,
                        "p_schedule_end":that.enddate
                    },
                    success: function(data) {
                        if(data.state=='success'){  
                            resultTip({
                                msg: data.state
                            });
                        }else{ 
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
           },
           reset(data){
                if(data!='model'){
                    this.resetId=data
                    $('#resetModal').modal('show')
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrlPtc("resetStudentPtc") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        dept: 1, // 部门
                        child_id: this.resetId // 孩子ID
                    },
                    success: function(data) {
                        if(data.state=='success'){  
                            that.classChild(that.classId)
                            resultTip({
                                msg: data.state
                            });
                            $('#resetModal').modal('hide')
                        }else{ 
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
           },
           showClassTeacher(){
             var isShow=false
             for(var key in this.indexData.classTeacherData){
                 if(Object.keys(this.indexData.classTeacherData[key]).length!=3){     
                    isShow=true
                 }
             }
             return isShow
           },
            exportTable(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrlPtc("getPtcScheduleDay") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        dept: 1, // 部门
                    },
                    success: function(data) {
                        if(data.state=='success'){  
                           that.exportDays=data.data
                            $('#exportModal').modal('show')
                        }else{ 
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            exportData(day,index){
                let that=this
                $('#export'+index).show()
                $.ajax({
                    url: '<?php echo $this->createUrlPtc("getStudentsByDate") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        dept: 1, // 部门
                        date:day
                    },
                    success: function(data) {
                        if(data.state=='success'){  
                            let dataList=data.data
                            const filename =day+'.xlsx';
                            const ws_name = "SheetJS";
                            var exportDatas = [];
                            for(var i=0;i<dataList.ptcData.length;i++){
                                let childId=parseInt(dataList.ptcData[i].child_id)
                                if(dataList.childInfo[childId]){
                                if(dataList.parentsData[dataList.childInfo[childId].mid]){
                                    exportDatas.push(
                                    {
                                    '日期':day,
                                    "会谈时间":dataList.ptcData[i].time,
                                    "学生姓名":dataList.childInfo[childId].name,
                                    "学生ID":childId,
                                    "班级":dataList.childInfo[childId].className,
                                    "参会人员":'母亲',
                                    "姓名":dataList.parentsData[dataList.childInfo[childId].mid].name,
                                    "电话":dataList.parentsData[dataList.childInfo[childId].mid].mphone,
                                    })
                                    if(dataList.parentsData[dataList.childInfo[childId].fid]){
                                        exportDatas.push(
                                        {
                                        '日期':'',
                                        "会谈时间":'',
                                        "学生姓名":'',
                                        "学生ID":'',
                                        "参会人员":'父亲',
                                        "姓名":dataList.parentsData[dataList.childInfo[childId].fid].name,
                                        "电话":dataList.parentsData[dataList.childInfo[childId].fid].mphone,
                                        })
                                    }
                                }else{
                                    exportDatas.push(
                                        {
                                        '日期':day,
                                        "会谈时间":dataList.ptcData[i].time,
                                        "学生姓名":dataList.childInfo[childId].name,
                                        "学生ID":childId,
                                        "班级":dataList.childInfo[childId].className,
                                        "参会人员":'母亲',
                                        "姓名":dataList.parentsData[dataList.childInfo[childId].fid].name,
                                        "电话":dataList.parentsData[dataList.childInfo[childId].fid].mphone,
                                    })
                                    if(dataList.parentsData[dataList.childInfo[childId].mid]){
                                        exportDatas.push(
                                            {
                                            '日期':'',
                                            "会谈时间":'',
                                            "学生姓名":'',
                                            "学生ID":'',
                                            "参会人员":'父亲',
                                            "姓名":dataList.parentsData[dataList.childInfo[childId].mid].name,
                                            "电话":dataList.parentsData[dataList.childInfo[childId].mid].mphone,
                                        })
                                    }
                                }
                                }
                            }
                            var wb=XLSX.utils.json_to_sheet(exportDatas,{
                                origin:'A1',// 从A1开始增加内容
                                header: ['日期', '会谈时间', '学生姓名','学生ID','班级','参会人员','姓名','电话'],
                            });
                            const workbook = XLSX.utils.book_new();
                            XLSX.utils.book_append_sheet(workbook, wb, ws_name);
                            const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                            const blob = new Blob([wbout], {type: 'application/octet-stream'});
                            let link = document.createElement('a');
                            link.href = URL.createObjectURL(blob);
                            link.download = filename;
                            link.click();
                            setTimeout(function() {
                                // 延时释放掉obj
                                URL.revokeObjectURL(link.href);
                                link.remove();
                                $('#export'+index).hide()
                            }, 500);
                        }else{ 
                            $('#export'+index).hide()
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        $('#export'+index).hide()
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            myPTC(id){
                let url='<?php echo $this->createUrl('ptc/myPtc', array('branchId' => $this->branchId)); ?>&teacherId='+id
                window.open(url,'_blank');
            }
        }
    })
</script>