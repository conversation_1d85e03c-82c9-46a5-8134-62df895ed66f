<?php if ($rejectWorkflow->data) : ?>
<div class="panel panel-default">
    <div class="panel-heading" data-branchfilter="dailyBulletin"><span class="glyphicon glyphicon-bullhorn"></span> <?php echo Yii::t('workflow', 'Requests Rejected'); ?></div>
    <div class="panel-body">
        <?php
            if ($rejectWorkflow)
            $this->widget('ext.ivyCGridView.BsCGridView', array(
                'id'=>'rejectWorkflow',
                'afterAjaxUpdate'=>'js:function(){head.Util.ajax($("#rejectWorkflow"))}',
                'dataProvider'=>$rejectWorkflow,
                'pager'=>array(
                    'class'=>'BsCLinkPager',
                    'maxButtonCount'=>6,
                    'pageSize'=>10,
                ),
                'colgroups'=>array(
                    array(
                        "colwidth"=>array(100),
                    )
                ),
                'columns'=>array(
                    array(
                        'name'=>'',
                        'value'=>array($this,'showWorkflow'),
                    ),
                ),
            ));
        ?>
    </div>
</div>
<?php endif; ?>
<style>
    #rejectWorkflow thead{display: none};
    li.first{display: none;}
    li.first{display: none;}
    .first a{display: none;}
    .last a{display: none;}
</style>