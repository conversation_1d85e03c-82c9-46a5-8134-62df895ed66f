<?php
class PublicMonth{
	public $year;			//��һ��ı���
	public $month;			//�����ǵڼ���
	public $days;			//����������
	public $first_weekday; 	//���¿�ʼ�����ܼ� 1-7
	public $last_weekday;  	//���½��������ܼ� 1-7
	public $daterange;
	public $totalDays=0;
	public $fallDays=0;
	public $springDays=0;
	
	function __construct($year, $month, $daterange){
		 $this->year = $year;
		 $this->month=$month;
		 $this->days= date("t",mktime(0,0,0,$this->month,1,$this->year));
		 $this->last_weekday =date("N",mktime(0,0,0,$this->month,$this->days,$this->year));
		 $this->first_weekday=date("N",mktime(0,0,0,$this->month,1,$this->year));
		 $this->daterange = $daterange;	
	}
	
	function resetYm($year,$month)
	{
		 $this->year = $year;
		 $this->month=$month;
		 $this->days= date("t",mktime(0,0,0,$this->month,1,$this->year));
		 $this->last_weekday =date("N",mktime(0,0,0,$this->month,$this->days,$this->year));
		 $this->first_weekday=date("N",mktime(0,0,0,$this->month,1,$this->year));	
	}
	
	function getMonthData($schoolDays=array(),$calendarDays=array()){

		$result = array();
		$current=0;
		$beginnulls = array();
		if ($this->first_weekday < 7) {
			$lastMonthDays = date("t",mktime(0,0,0,$this->month-1,1,$this->year));
			for ($i=0; $i<$this->first_weekday; $i++){
				$result[$current] = array(
					'date'=>$lastMonthDays - $this->first_weekday + $i + 1,
					'hint'=>true, //�ڱ���������ʾ���µ�����
				);
				$beginnulls[] = null;
				$current++;
			}
		}
		
	
		$lineNO = 0;
		$newweek = 0;
		$currentTime = time();
		$currentYear  = date("Y", $currentTime);
		$currentMonth = date("m", $currentTime);
		$currentDay   = date("d", $currentTime);
		
		$checkInSchool = (is_null($this->daterange)) ? false : true;
		$workdaystart = 0;
		$thisdate=0;
		
		$this->totalDays += count($schoolDays);
		for($i=0; $i<$this->days; $i++){
			$_thistime = mktime(0,0,0,$this->month, $i+1, $this->year);
		
		
            $isSchoolDay = in_array(sprintf("%02d", $i+1), $schoolDays);
			if($isSchoolDay){
				if ( ($_thistime >= $this->daterange['10']['school_start_timestamp']) && ($_thistime <= $this->daterange['10']['school_end_timestamp']) ){
					$this->fallDays++;
				}
				elseif( ($_thistime >= $this->daterange['20']['school_start_timestamp']) && ($_thistime <= $this->daterange['20']['school_end_timestamp']) ){
					$this->springDays++;
				}			
			}
			
			$Ymd = date('Ymd',$_thistime);
			
			$isCalendarDay = (isset($calendarDays[$Ymd])) ?
				array(
					'class' => sprintf('s%d%d',$calendarDays[$Ymd]['day_type'],$calendarDays[$Ymd]['event']),
					'tip' => ( Yii::app()->language == 'zh_cn' ) ? $calendarDays[$Ymd]['title_cn'] . ' '. $calendarDays[$Ymd]['title_en'] :
																   $calendarDays[$Ymd]['title_en'] . ' '. $calendarDays[$Ymd]['title_cn']
				):null;

			//�ж�ĳ���Ƿ��ǽ���
			if(!$thisdate && $this->month == $currentMonth && $this->year == $currentYear){
				if(mktime(0,0,0,$this->month,$i+1,$this->year)==mktime(0,0,0,$currentMonth, $currentDay, $currentYear))
					$thisdate=1;
			}
					
			//ÿ�߸�һ��
			$newline = ( ($current + 1) % 7 ) ? 0 : 1;
			if ($newline) $lineNO++;
			
			//����ǵ������һ�죬���Ҹ����������ϸպ���Ҫ����
			$islastday = (($i+1 == $this->days) && $newline ) ? 1 : 0;
			
			//���������ڼ��� 1-7
			$thedate = date("N", mktime(0,0,0, $this->month, $i+1, $this->year));
			
			//�����Ƿ��ǹ����գ�
			$workdaystart = in_array($thedate, array(6,7)) ? $workdaystart : 1;
			
			if ($workdaystart) {
				if ($thedate == 7 ) $newweek = 0;
				if ( $thedate == 1 ) {
					$newweek = date("Ymd", mktime(0,0,0, $this->month, $i+1, $this->year));
				}elseif ($thedate > 1 && $newweek == 0 ) {
					$newweek = date("Ymd", mktime(0,0,0, $this->month, $i+1 - ($thedate - 1) , $this->year));
				}
				
			}

			$result[$current] = array(
				"date" => $i+1, // ���µڼ���
				"day" => date("Ymd", mktime(0,0,0, $this->month, $i+1, $this->year)), //����ʱ���ʽ YYYYmmdd
				"daystamp" => mktime(0,0,0, $this->month, $i+1, $this->year), //����ʱ���ʽ YYYYmmdd
				"thedate" => $thedate, //
				"newline" => $newline, // �Ƿ�����һ��
				"linenumber" => $lineNO, // �к�
				"islastday" => $islastday, // �Ƿ��Ǳ������һ��
				"thisdate" =>$thisdate,  // �Ƿ��ǽ���
				"newweek" => $newweek,
				"isschoolday" => $isSchoolDay,
				"iscalendarday" => $isCalendarDay,
				"weeknum" => 0, 
				"event" => isset($newDayarray[date("Ymd", mktime(0,0,0, $this->month, $i+1, $this->year))]) ? $newDayarray[date("Ymd", mktime(0,0,0, $this->month, $i+1, $this->year))]["event"] : 0,
				"day_type" => isset($newDayarray[date("Ymd", mktime(0,0,0, $this->month, $i+1, $this->year))]) ? $newDayarray[date("Ymd", mktime(0,0,0, $this->month, $i+1, $this->year))]["day_type"] : 0,
			);
			
			if ($thedate == 7){
				$result[$current]['weeknum'] = 1;
			}
			
			if ($checkInSchool){
				$thetime = strtotime($result[$current]['day']);
				$inschool = (isset($daterange['fall_start'])&&$thetime < $daterange['fall_start']) ? false : true;
				if ($inschool){
					$inschool = (isset($daterange['fall_start'])&&$thetime > $daterange['fall_end']) ? false : true;
					if (!$inschool)
						$inschool = (isset($daterange['fall_start'])&&$thetime < $daterange['spring_start']) ? false : true;
						if ($inschool)
							$inschool = (isset($daterange['fall_start'])&&$thetime > $daterange['spring_end']) ? false : true;
				}
				$result[$current]['inschool'] = intval($inschool);
			}
			
			$current++;
		}
		$endnulls = (6-$this->last_weekday >= 0) ? 6-$this->last_weekday : 6;
		
		$endnullarr = array();
		for ($i=0; $i<$endnulls; $i++){
			$endnullarr[$i] = $i;
			$result[$current] = array(
				'date'=>$i+1,
				'hint'=>true,//�ڱ���������ʾ���µ�����
			);
			$current++;
		}
		
		
		
		for($i=0; $i<count($result); $i++){
			if (($i % 7 == 0) || (($i+1) % 7 == 0) )
			$result[$i]["isweekend"] = 1;
		}
		if ((count($beginnulls)) < 7){
			$result[count($result)-1]["newline"] = 1;
		}
		if ((count($beginnulls)) < 6){
			$result[count($result)-1]["newweek"] = $newweek;
			$result[count($result)-1]["linenumber"] = ++$lineNO;
			
		}
		
		$ret["calendar"] = $result;
		$ret["beginnulls"] = $beginnulls;
		$ret["endnulls"] = $endnullarr;
		$month_time = mktime(0,0,0,$this->month,1, $this->year);
		$ret["year_month"] =date("M, Y", $month_time);
		$ret["year_month_str"] =date("Ym", $month_time);
		$ret["month_num"] =date("m", $month_time);
		$ret["dates_temp"] = $month_time;
		return $ret;
			
	}
	
	public function getStats(){
		return array(
			'total'=>$this->totalDays,
			'fall' =>$this->fallDays,
			'spring'=>$this->springDays
		);
	}
	
	
	/*****
	
	var $dates;//һ�����ܹ��ж�����
	var $fir_date;//һ���µĿ�ʼ�����ڼ�
	var $month;//�ĸ���
	var $dj_date;//һ�����еĵڼ���
	var $year;//��һ��
	var $days;// һ�����ж����죻
	var $last_date;//һ�������һ�������ڼ�
	var $daterange;
	function __construct($year,$month, $daterange=null){
	     //ʵ��ʱ�Զ����ù��캯��
		 $this->year = $year;
		 $this->month=$month;
		 $this->dates=date("t",mktime(0,0,0,$this->month,1,$this->year));
		 $this->days= date("t",mktime(0,0,0,$this->month,1,$this->year));
		 $this->last_date=date("N",mktime(0,0,0,$this->month,$this->days,$this->year));
		 $this->fir_date=date("N",mktime(0,0,0,$this->month,1,$this->year));
		 $this->dj_date=date("j");
		 $this->daterange = $daterange;
	 }
	function __destruct(){
	     //������������������ٶ���,�ͷ��ڴ�,���������Զ�����
	}
	function create_month_calendar($newDayarray){
		$result = array();
		$current=0;
		$beginnulls = array();
		if ($this->fir_date < 7) {
			for ($i=0; $i<$this->fir_date; $i++){
				$result[$current] = null;
				$beginnulls[] = null;
				$current++;
				
			}
		}
		
	
		$lineNO = 0;
		$newweek = 0;
		
		$daterange =&  $this->daterange;
		$checkInSchool = (is_null($daterange)) ? false : true;
		$workdaystart = 0;
		for($i=0; $i<$this->days; $i++){
            $thisdate=0;
			if(mktime(0,0,0,$this->month,$i+1,$this->year)==mktime(0,0,0,date("m",$currentTime),date("d",$currentTime),date("Y",$currentTime))){
				$thisdate=1;
			}
			
			
			$newline = ( ($current + 1) % 7 ) ? 0 : 1;
			if ($newline) $lineNO++;
			$islastday = (($i+1 == $this->days) && $newline ) ? 1 : 0;
			$thedate = date("N", mktime(0,0,0, $this->month, $i+1, $this->year));

			$workdaystart = in_array($thedate, array(6,7)) ? $workdaystart : 1;
			
			if ($workdaystart) {
				if ($thedate == 7 ) $newweek = 0;
				if ( $thedate == 1 ) {
					$newweek = date("Ymd", mktime(0,0,0, $this->month, $i+1, $this->year));
				}elseif ($thedate > 1 && $newweek == 0 ) {
					$newweek = date("Ymd", mktime(0,0,0, $this->month, $i+1 - ($thedate - 1) , $this->year));
				}
				
			}

			$result[$current] = array(
				"date" => $i+1,
				"day" => date("Ymd", mktime(0,0,0, $this->month, $i+1, $this->year)),
				"thedate" => $thedate,
				"newline" => $newline,
				"linenumber" => $lineNO,
				"islastday" => $islastday,
				"thisdate" =>$thisdate,
				"newweek" => $newweek,
				"weeknum" => 0,
				"event" => isset($newDayarray[date("Ymd", mktime(0,0,0, $this->month, $i+1, $this->year))]) ? $newDayarray[date("Ymd", mktime(0,0,0, $this->month, $i+1, $this->year))]["event"] : 0,
				"day_type" => isset($newDayarray[date("Ymd", mktime(0,0,0, $this->month, $i+1, $this->year))]) ? $newDayarray[date("Ymd", mktime(0,0,0, $this->month, $i+1, $this->year))]["day_type"] : 0,
			);
			
			if ($thedate == 7){
				$result[$current]['weeknum'] = 1;
			}
			
			if ($checkInSchool){
				$thetime = strtotime($result[$current]['day']);
				$inschool = ($thetime < $daterange['fall_start']) ? false : true;
				if ($inschool){
					$inschool = ($thetime > $daterange['fall_end']) ? false : true;
					if (!$inschool)
						$inschool = ($thetime < $daterange['spring_start']) ? false : true;
						if ($inschool)
							$inschool = ($thetime > $daterange['spring_end']) ? false : true;
				}
				$result[$current]['inschool'] = intval($inschool);
			}
			
			$current++;
		}
		$endnulls = (6-$this->last_date >= 0) ? 6-$this->last_date : 6;
		
		$endnullarr = array();
		for ($i=0; $i<$endnulls; $i++){
			$endnullarr[$i] = $i;
			$result[$current] = null;
			$current++;
		}
		
		
		
		for($i=0; $i<count($result); $i++){
			if (($i % 7 == 0) || (($i+1) % 7 == 0) )
			$result[$i]["isweekend"] = 1;
		}
		if ((count($beginnulls)) < 7){
			$result[count($result)-1]["newline"] = 1;
		}
		if ((count($beginnulls)) < 6){
			$result[count($result)-1]["newweek"] = $newweek;
			$result[count($result)-1]["linenumber"] = ++$lineNO;
			
		}
		
		$ret["calendar"] = $result;
		$ret["beginnulls"] = $beginnulls;
		$ret["endnulls"] = $endnullarr;
		$month_time = mktime(0,0,0,$this->month,1, $this->year);
		$ret["year_month"] =date("M, Y", $month_time);
		$ret["year_month_str"] =date("Ym", $month_time);
		$ret["month_num"] =date("m", $month_time);
		$ret["dates_temp"] = $month_time;
		return $ret;
		
	}
	****/
}
?>