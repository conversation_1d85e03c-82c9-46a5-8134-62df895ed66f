<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta name="language" content="en" />
</head>
<body>
<script language="javascript">
    function viewSchedule(_this)
    {
        window.location = "<?php echo Yii::app()->getController()->createUrl('//portfolio/portfolio/lunch', array('schoolid'=>$schoolId, 'yid'=>$yId));?>&weeknum="+_this.value;
    }
</script>
<div id="lunch" class="pop-box">
    <h5><?php echo addColon(Yii::t("portfolio", 'Lunch Menu'));?><?php if ($menu):echo $menu->title_cn;endif;?></h5>
    <div class="sh-tools">
        <div class="fl"><?php
            if($weeks) {
                echo CHtml::dropDownList('weeknum', $weekNum, $weeks, array('onchange' => 'viewSchedule(this)'));
            }
            ?></div>
        <div class="fr" style="line-height: 24px; margin-right: 60px;">
            <?php
            $minw = min(array_keys($weeks));
            $maxw = max(array_keys($weeks));
            ?>
            <?php if ($weekNum != $minw):?>
            <?php
            $linkText = "<span class='prev'>" . Yii::t("portfolio", "Prev Week") ."</span>";
            echo CHtml::link($linkText, Yii::app()->getController()->createUrl('//portfolio/portfolio/lunch', array('schoolid'=>$schoolId, 'yid'=>$yId, 'weeknum'=>$weekNum-1)), array('class'=>'alink'))?>
            <?php endif;?>
            <?php if ($weekNum != $maxw):?>
            <?php
            $linkText = "<span class='next'>" . Yii::t("portfolio", "Next Week") ."</span>";
            echo CHtml::link($linkText, Yii::app()->getController()->createUrl('//portfolio/portfolio/lunch', array('schoolid'=>$schoolId, 'yid'=>$yId, 'weeknum'=>$weekNum+1)), array('class'=>'alink'))?>
            <?php endif;?>
        </div>
        <div class="clear"></div>
    </div>
    <div>
        <table class="gtab" width="95%" cellpadding="1" cellspacing="1" border="0">
            <tr>
                <th width="15%"></th>

                <?php foreach ($wdarrs as $val){?>
                    <th width="10%"><?php echo $getWeekDayName[$val];?></th>
                <?php }?>
                
            </tr>
            <?php
            if ($mdArr){
            $wdarr = array('mon', 'tue', 'wed', 'thu', 'fri');
            $i=0;
            foreach($mdArr as $kmd=>$mA){?>
            <tr class="<?php echo (($i++)%2==0)?"bg1":"bg2";?>">
                <td align="center" class="lTit"><?php echo $mA['title']?></td>
                <?php foreach($wdarrs as $wd){?>
                <td valign="top">
                    <?php
                    if($mA[$wd]->photo){
                        echo CHtml::link(CHtml::image(Yii::app()->params['uploadBaseUrl'].'lunch/'.$mA[$wd]->photo, '', array('class'=>'thumb')), Yii::app()->params['uploadBaseUrl'].'lunch/'.$mA[$wd]->photo, array('target'=>'_blank'));
                        echo '<br>';
                    }
                    echo CFormatter::formatNtext($mA[$wd]->food_list);
                    ?>
                </td>
                <?php }?>
            </tr>
            <?php }}else{?>
                <tr><td colspan="6"><div class="journal-box" style="width: 600px;margin: 50px auto;padding: 10px; 0;<?php if(Yii::app()->language=='zh_cn'){echo 'text-align:center;';}?>"><?php echo Yii::t('lunch', 'Lunch information for this week is not yet available and are usually uploaded on Fridays at 6pm (except during school holidays).');?></div></td></tr>
            <?php }?>
        </table>
    </div>
</div>
        </body>
</html>