<?php 
$payType = array(
    'alipay'=>'网银金额（支付宝）',
    'wechat'=>'微信支付',
    'allinpay'=>'POS支付（通联）',
    'allinpayOnline'=>'网银支付（通联）',
    'yeepay'=>'POS支付（易宝）',
    );
 ?>
<h4>孩子名字：<?php echo ($model->ChildProfile) ? $model->ChildProfile->getChildName() : $child_name->getName() ;?>； 学校：<?php echo $model->school->abb  ?></h4>
<div class="table-responsive">
    <table class="table table-bordered">
        <thead>
        <tr>
            <th class="text-center" width="100">账单标题</th>
            <th class="text-center" style="width: 100px;">账单金额</th>
            <th class="text-center" style="width: 100px;"><?php echo $payType[$type];?></th>
        </tr>
        </thead>
        <tbody>
        <?php foreach($model->items as $item):?>
        <tr>
            <td><?php echo CHtml::link($item->invoice->title, array('/child/invoice/viewInvoice', 'invoiceid'=>$item->invoice->invoice_id, 'childid'=>$model->ChildProfile->childid), array('target'=>'_blank'));?></td>
            <td class="text-right"><?php echo number_format($item->invoice->amount, 2);?></td>
            <td class="text-right">
                <?php
                $tAmount = 0;
                foreach($item->invoice->invoiceTransaction as $tran){
                    if(($type == 'alipay'||$type == 'allinpayOnline') && ($tran->transactiontype == InvoiceTransaction::TYPE_ONLINEPAYMENT || $tran->transactiontype == InvoiceTransaction::TYPE_ONLINE_ALLINPAY)){
                        $tAmount += $tran->amount;
                    }
                    elseif($type == 'wechat' && ($tran->transactiontype == InvoiceTransaction::TYPE_WX_MICROPAY || $tran->transactiontype == InvoiceTransaction::TYPE_WX_NATIVE)){
                        $tAmount += $tran->amount;
                    }
                    elseif(($type != 'alipay'||$type != 'wechat') && ($tran->transactiontype == InvoiceTransaction::TYPE_POS || $tran->transactiontype == InvoiceTransaction::TYPE_POS_ALLINPAY)){
                        $tAmount += $tran->amount;
                    }
                }
                echo number_format($tAmount, 2);
                ?>
            </td>
        </tr>
        <?php endforeach;?>
        </tbody>
    </table>
</div>