<?php
class BillingsController extends BranchBasedController
{

    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mcampus/billings/overdues');

        Yii::import('common.models.invoice.Invoice');
        Yii::import('common.models.invoice.InvoiceTransaction');
    }

    public function actionOverdues(){
        //查询所以未付帐单
        $criter = new CDbCriteria;
        $criter->compare('t.inout', 'in');
        $criter->compare('t.childid', '<>0');
        $criter->compare('t.schoolid', $this->branchId);
        $criter->compare('t.status', array(Invoice::STATS_UNPAID,Invoice::STATS_PARTIALLY_PAID));
        $criter->compare('t.payment_type', '<>preschool_subsidy');
        $criter->addCondition('t.amount > 0 and t.duetime>0');
        $modelList = Invoice::model()->with('paidSum','childprofile','classInfo')->findAll($criter);

        $subject = array();
        $items = BranchVar::model()->findAllByAttributes(array('branchid'=>$this->branchId, 'category'=>'invoice'));
        foreach($items as $item){
            $subject[$item->subcategory] = $item->data;
        }

        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        $this->render('overdues',array('modelList'=>$modelList, 'subject'=>$subject));
    }

    public function actionOverduesSendMail(){
        Yii::import('common.models.mailer.*');
        $request = Yii::app()->request;
        if ($request->isAjaxRequest && $request->isPostRequest && isset($_POST['invoiceIds'])){
            $bankinfo = BranchVar::model()->bankInfo($this->branchId);
            $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
            $today = strtotime('today');
            $isProduction = OA::isProduction();
            //查询INVOICE
            $invoiceList = array();
            $childList = array();
            $criter = new CDbCriteria;
            $criter->compare('t.inout', 'in');
            $criter->compare('t.invoice_id', $_POST['invoiceIds']);
            $criter->compare('t.schoolid', $this->branchId);
            $criter->compare('t.status', array(Invoice::STATS_UNPAID,Invoice::STATS_PARTIALLY_PAID));
            $criter->addCondition('t.amount > 0 and t.duetime>0');
            $criter->index = "invoice_id";
            $list = Invoice::model()->with('invoiceTransaction')->findAll($criter);
            //support email
            $branchInfo = BranchInfo::model()->findByPk($this->branchId);
            $supportEmail = $branchInfo->support_email;

            $type = $request->getPost('type', '');

            $flag = $type == 'unpad' ? 'invoice_unpaid' : 'invoice_due';
            $crit = new CDbCriteria();
            $crit->compare('flag', $flag);
            $crit->index = 'branch_id';
            $modals = MailRecipient::model()->findAll($crit);
            if(isset($modals[$this->branchId])){
                $modal = $modals[$this->branchId];
            }
            else{
                $modal = $modals['all'] ? $modals['all'] : null;
            }

            $support = CJSON::decode($modal->support_email);

            $replytoEmail = $modal->mail_reply_to ? explode(',', $modal->mail_reply_to) : array();
            $cctoEmail = $modal->cc_to ? explode(',', $modal->cc_to) : array();

            if(isset($support['reply_to']))
                array_push($replytoEmail, $supportEmail);
            if(isset($support['cc']))
                array_push($cctoEmail, $supportEmail);

            $emailsubject = $request->getPost('emailsubject', '');
            if(!$emailsubject)
                $emailsubject = 'Invoices Due Soon/未付帐单即将到期';
            if (!empty($list)){
                foreach ($list as $val){
                    $tamount = 0;
                    if (!empty($val->invoiceTransaction)){
                        foreach ($val->invoiceTransaction as $tval){
                            $tamount += $tval->amount;
                        }
                    }
                    $invoiceList[$val->childid]['info'][$val->invoice_id] = $val->getAttributes();
                    $invoiceList[$val->childid]['info'][$val->invoice_id]['amount'] = $val->amount - $tamount;
                    $invoiceList[$val->childid]['info'][$val->invoice_id]['tAmount'] = $tamount;
                    $invoiceList[$val->childid]['info'][$val->invoice_id]['payAmounts'] = $val->amount;
                    if ($val->duetime < $today){
//                        $invoiceList[$val->childid]['unpay'][$val->invoice_id] = $val->invoice_id;
                        $invoiceList[$val->childid]['overdues'][$val->invoice_id] = $val->invoice_id;
                    }else{
                        $invoiceList[$val->childid]['unpay'][$val->invoice_id] = $val->invoice_id;
                    }
                    $childList[$val->childid] = $val->childid;
                }
                //循环孩子
                $data = array();
                $allbranches = $this->getAllBranch();
                foreach ($childList as $pk){
                    $model = ChildProfileBasic::model()->findByPk($pk);
                    $parentList = $model->getParents(null);
                    $emailList = array();
                    if (!empty($parentList['father']) && isset($parentList['father']->email)){
                        $emailList[] = $parentList['father']->email;
                    }
                    if (!empty($parentList['mother']) && isset($parentList['mother']->email)){
                        $emailList[] = $parentList['mother']->email;
                    }
                    $mailer->Subject = (OA::isProduction()) ? $emailsubject : '[测试] '.$emailsubject;
                    foreach($cctoEmail as $_emial){
                        $mailer->AddCC($_emial);
                    }
                    foreach($replytoEmail as $_emial){
                        $mailer->AddReplyTo($_emial);
                    }
                    foreach ($emailList as $uVal) {
                        if ($mailer->ValidateAddress($uVal)){
                            $mailer->AddAddress($uVal);
                        }
                    }
                    $mailer->iniMail( OA::isProduction() ); // 此行代码要放到AddAddress, AddCC方法下面
                    if($this->branchObj->type==50 || $this->branchId == "BJ_QFF"){
                        //获取不同学校下的提示信息
                        $res = CommonUtils::requestDsOnline('billings/getOverdueEmailTip', array(
                            'school_id'=> $this->branchId,
                        ));
                        $email_view = $res['data'];
                        $mailer->getView('overdues_ds_template',
                            array(
                                'schoolid' => $this->branchId,
                                'invoiceList'=>$invoiceList[$pk],
                                'name'=>$model->getChildName(),
                                'supportEmail'=>$replytoEmail[0],
                                'bankinfo'=>$bankinfo,
                                'allbranches'=>$allbranches,
                                'email_view'=>$email_view,
                            ),
                            'todsparent');
                    }elseif(OA::isYZQ($this->branchObj->branchid)) {
                        $mailer->getView('overdues_yzq', array('invoiceList'=>$invoiceList[$pk],'name'=>$model->getChildName(),'supportEmail'=>$replytoEmail[0]), 'toyzqparent');
                    } else{
                        $mailer->getView('overdues', array('invoiceList'=>$invoiceList[$pk],'name'=>$model->getChildName(),'supportEmail'=>$replytoEmail[0]), 'toparent');
                    }

//                    echo $mailer->Body;die;
//                    var_dump($mailer->Body);die;
                    if ($mailer->Send()) {
                        foreach ($invoiceList[$pk]['info'] as $val){
                            $list[$val['invoice_id']]->send_timestamp = time();
                            $list[$val['invoice_id']]->save();
                            $data[$val['invoice_id']] = Yii::t('billings','发送成功:').OA::formatDateTime($list[$val['invoice_id']]->send_timestamp);
                        }
                    } else {
                        $logInfo = $pk .'，'. $mailer->ErrorInfo;
                        Yii::log($logInfo, 'info', 'OverduesSendMail');
                        foreach ($invoiceList[$pk]['info'] as $val){
                             $data[$val['invoice_id']] = Yii::t('billings','发送失败.');
                        }
                    }
                    $mailer->ClearAddresses();
                    $mailer->ClearCCs();
                }

                $mBranch = BranchVar::model()->findByAttributes(array('branchid'=>$this->branchId, 'category'=>'invoice', 'subcategory'=>$type));
                if($mBranch == null)
                    $mBranch = new BranchVar();
                $mBranch->branchid = $this->branchId;
                $mBranch->category = 'invoice';
                $mBranch->subcategory = $type;
                $mBranch->data = $emailsubject;
                $mBranch->user = Yii::app()->user->id;
                $mBranch->updated = time();
                $mBranch->save();

                $this->addMessage('state', 'success');
                $this->addMessage('callback','callbackInvoice');
                $this->addMessage('data',$data);
                $this->addMessage('message', '');
                $this->showMessage();
            }
        }
    }
}
