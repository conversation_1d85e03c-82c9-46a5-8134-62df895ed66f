
<div id="container" v-cloak class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Teaching Tasks'), array('//mteaching/default/index')) ?></li>
        <li><?php echo Yii::t('directMessage', '汇报关系') ?></li>
    </ol>
    <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane :name="list.type" v-for='(list,index) in reportingLine' :label="list.name"></el-tab-pane>
    </el-tabs>
    <div class='mb16'>
        <button type="button" class="btn btn-primary" @click='addGroup()'><span class='el-icon-circle-plus-outline'></span> 创建组</button>
    </div>
    <div class='row' v-if='listData.list && listData.list.length!=0'>
      <div class='col-md-2'>
            <div v-for='(list,key,index) in listData.list' class="navList" :class="{ navAactive: activeSection === key }" @click='scrollList(key)'>
                {{list.name}}
            </div>
      </div>
      <div class='col-md-10  overflow-y scroll-box' ref='content' :style="'max-height:'+(height-320)+'px;overflow-x: hidden;'">
        <div v-for='(list,key,index) in listData.list' class='border mb16' :id='"list"+key' :ref='"list"+key'>
            <div class='flex align-items'>           
                <div class='font14 color3 flex1 font600'> {{list.name}}</div>
                <div class="btn-group">
                    <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    操作 <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu minWidth">
                        <li><a href="javascript:void(0)" @click='addGroup(list)'>编辑</a></li>
                        <li><a href="javascript:void(0)"  @click='delGroup(list)'>删除</a></li>
                    </ul>
                </div>
            </div>
            <div class='mt24 color6 font14'>共同审批人</div>
            <div class='row' v-if='list.config.backup.length!=0'>
                <div class='col-md-3' v-for='(item,index) in list.config.backup'>
                    <div class='flex  p8 align-items bgchild' >
                        <img :src="listData.staffInfo[item].photoUrl" class='img42 img-circle' alt="" />
                        <div class='flex1 flexText ml10'>
                            <div class='font14 color3 word-break'>{{listData.staffInfo[item].name}}</div>
                            <div class='font12 color6 word-break'>{{listData.staffInfo[item].hrPosition}}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div v-else class='mt12 font14 color3'>
                无
            </div>
            <div class='mt24 color6 font14'>审批抄送人</div>
            <div class='row' v-if='list.config.cc.length!=0'>
                <div class='col-md-3' v-for='(item,index) in list.config.cc'>
                    <div class='flex  p8 align-items bgchild' >
                        <img :src="listData.staffInfo[item].photoUrl" class='img42 img-circle' alt="">
                        <div class='flex1 flexText ml10'>
                            <div class='font14 color3 word-break'>{{listData.staffInfo[item].name}}</div>
                            <div class='font12 color6 word-break'>{{listData.staffInfo[item].hrPosition}}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div v-else class='mt12 font14 color3'>
                无
            </div>
        </div>
        
      </div>
    </div>
    <el-empty description="暂无数据" v-else-if='listData.list'></el-empty>
    <!-- 添加成员 -->
    <div class="modal fade" id="editModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentReplyLabel">{{groupData.id==''?"创建组":"编辑组"}}</h4>
            </div>
            <div class="p24 overflow-y scroll-box font14" :style="'max-height:'+(height-220)+'px;overflow-x: hidden;'"  >
                <div>   
                    <div class='font14 color3 font600 mb12'>组名称</div>
                    <div>
                        <el-input v-model="groupData.name" placeholder="请输入" size='small'></el-input>
                    </div>
                    <div class='font14 color3 font600 mb12 mt24'> 共同审批人</div>
                    <div>
                        <div class='flex align-items'>
                            <div class='flex1'>
                                <span class='colorBlue font14 cur-p' @click='addStaff("backup")'><span class='el-icon-circle-plus-outline mr5'></span>添加成员</span> 
                            </div>
                            <span class='color6 font14 cur-p' v-if='groupData.backup.length' @click='clearStaff("backup")'>清空</span>
                        </div>
                        <div class='row ' v-if='groupData.backup.length'>
                            <div class='col-md-6 mt16'  v-for='(item,idx) in groupData.backup'>
                                <div class="flex align-items listStaff">
                                    <div class='flex flex1' v-if='allDept[item]'>
                                        <img :src="allDept[item].photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                        <div class="flex1 ml10 flex1Text">
                                            <div class=" font14 mt8 color3 text_overflow">{{allDept[item].name}}</div>
                                            <div class="font12  mt5 color6 text_overflow">{{allDept[item].hrPosition}}</div>
                                        </div>
                                    </div>
                                    <div @click='editUnassign(item,idx,"backup")'>
                                        <span class='closeChild cur-p mt10 font16 el-icon-circle-close'></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class='font14 color3 font600 mb12 mt24'> 审批抄送人</div>
                    <div>
                        <div class='flex align-items'>
                            <div class='flex1'>
                                <span class='colorBlue font14 cur-p' @click='addStaff("cc")'><span class='el-icon-circle-plus-outline mr5'></span>添加成员</span> 
                            </div>
                            <span class='color6 font14 cur-p' v-if='groupData.cc.length' @click='clearStaff("cc")'>清空</span>
                        </div>
                        <div class='row ' v-if='groupData.cc.length'>
                            <div class='col-md-6 mt16'  v-for='(item,idx) in groupData.cc'>
                                <div class="flex align-items listStaff">
                                    <div class='flex flex1' v-if='allDept[item]'>
                                        <img :src="allDept[item].photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                        <div class="flex1 ml10 flex1Text">
                                            <div class=" font14 mt8 color3 text_overflow">{{allDept[item].name}}</div>
                                            <div class="font12  mt5 color6 text_overflow">{{allDept[item].hrPosition}}</div>
                                        </div>
                                    </div>
                                    <div @click='editUnassign(item,idx,"cc")'>
                                        <span class='closeChild cur-p mt10 font16 el-icon-circle-close'></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close"); ?></button>
                <button type="button" class="btn btn-primary" @click="saveStaff()"><?php echo Yii::t("global", "Save"); ?></button>
            </div>
            </div>
        </div>
    </div>
    <!-- 选择成员 -->
    <div class="modal fade" id="addStaffModal" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t('leave', '添加成员') ?></h4>
                </div>
                <div class="modal-body relative" style='padding:0'>
                    <span class='borderLeftpos'></span>
                    <div style='max-height:600px;' class='p24 row'>
                        <div class='col-md-6 col-sm-6'>
                          <div>
                            <el-select v-model="schoolId" style='width:100%' placeholder="<?php echo Yii::t('teaching', 'Please select') ?>" @change='addStaff(addType,"tab")'>
                                <el-option
                                    v-for="(item,key,index) in schoolList"
                                    :key="key"
                                    :label="item.title"
                                    :value="key">
                                </el-option>
                            </el-select>
                          </div>
                          <div class='mt10'>
                              <el-input
                              placeholder="<?php echo Yii::t('global', 'Search') ?>"
                              v-model='searchText' 
                              clearable>
                              </el-input>
                          </div>
                          <div  class="tab-pane active mt15 scroll-box" id="class" v-if='searchText==""'  style='max-height:460px;overflow-y:auto'>
                              <div v-for='(list,index) in currentDept.list' class='relative mb16'>
                                  <p  @click='showDepName(list)'>
                                      <span  class='font14 color606 cur-p'>{{list.dep_name}} </span>
                                      <span class='el-icon-arrow-down ml5' v-if='dep_name!=list.dep_name'></span>
                                      <span class='el-icon-arrow-up ml5' v-else></span>
                                  </p>
                                  <p  class='allCheck' v-if='dep_name==list.dep_name'><button class="btn btn-default pull-right btn-xs" type="button" @click='selectAll(list,index)' ><?php echo Yii::t("global", "Select All");?></button></p>
                                  <div  class='border scroll-box mr10 childList' v-if='dep_name==list.dep_name'>
                                      <div class="flex align-items listMedia" v-for='(item,key,idx) in list.user'>
                                          <div class='flex flex1' v-if='currentDept.user_info[item.uid]'>
                                              <img :src="currentDept.user_info[item.uid].photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                              <div class="flex1 ml10 flex1Text">
                                                  <div class=" font14 mt8 color3 text_overflow">{{currentDept.user_info[item.uid].name}}</div>
                                                  <div class="font12 mt5 color6 text_overflow">{{currentDept.user_info[item.uid].hrPosition}}</div>
                                              </div>
                                          </div>
                                          <div >
                                              <span class='cur-p font16 text-primary  el-icon-circle-plus-outline' v-if='!currentDept.user_info[item.uid].disabled' @click='assignStaff(item,index,idx)'></span>
                                              <span v-else><?php echo Yii::t('directMessage', 'selected') ?></span>
                                          </div>
                                      </div>
                                  </div>
                              </div>
                          </div>
                          <div v-else>
                              <div v-if='searchStaffList.length!=0' class='mt24 scroll-box'  style='max-height:460px;overflow-y:auto'>  
                                  <div class="flex align-items listMedia" v-for='(item,idx) in searchStaffList'>
                                      <div class='flex flex1' >
                                          <img :src="item.photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                          <div class="flex1 ml10 flex1Text">
                                              <div class=" font14 mt8 color3 text_overflow">{{item.name}}</div>
                                              <div class="font12 mt5 color6 text_overflow">{{item.hrPosition}}</div>
                                          </div>
                                      </div>
                                      <div >
                                          <span class='cur-p font16 text-primary  el-icon-circle-plus-outline' v-if='!currentDept.user_info[item.uid].disabled' @click='assignStaff(item,idx,"search")'></span>
                                          <span v-else><?php echo Yii::t('directMessage', 'selected') ?></span>
                                      </div>
                                  </div> 
                              </div>
                              <div v-else-if='searchText!=""'>
                                      <div class='font14 color6 text-center mt20'><?php echo Yii::t('ptc', 'No Data') ?></div>    
                              </div>
                          </div>
                        </div>
                        <div class='col-md-6 col-sm-6'>
                            <p class='mt10 font14 color6'>
                            <?php echo Yii::t("newDS", " ");?>{{staffSelected.length}} <?php echo Yii::t('leave', '成员') ?>
                            </p>
                            <div class='scroll-box p10 overflow-y' style='height:500px'>
                                <div class="flex align-items listMedia" v-for='(item,idx) in staffSelected'>
                                    <div class='flex flex1' v-if='allDept[item]'>
                                        <img :src="allDept[item].photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                        <div class="flex1 ml10 flex1Text">
                                            <div class=" font14 mt8 color3 text_overflow">{{allDept[item].name}}</div>
                                            <div class="font12  mt5 color6 text_overflow">{{allDept[item].hrPosition}}</div>
                                        </div>
                                    </div>
                                    <div @click='Unassign(item,idx)'>
                                        <span class='closeChild cur-p mt10 font16 el-icon-circle-close'></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                    </div>
                </div> 
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel");?></button>
                    <el-button type="button" class="btn btn-primary"  @click='confirmSatff()' :loading="confirmLoading"><?php echo Yii::t("global", "OK");?></el-button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="delModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t('leave', '删除') ?></h4>
                </div>
                <div class="modal-body p24" >
                  <div v-if='delType=="del"'>
                    <?php echo Yii::t('directMessage', 'Proceed to remove?') ?>
                  </div>
                  <div v-if='delType=="cc" || delType=="backup"'>
                    <?php echo Yii::t('directMessage', '确认清空吗？') ?>
                  </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" v-if='delType=="del"' @click='delGroup()'><?php echo Yii::t("global", "OK"); ?></button>
                    <button type="button" class="btn btn-primary" v-if='delType=="cc" || delType=="backup"' @click='clearStaff(delType,"clear")'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
  </div>
  <?php $this->renderPartial('//layouts/common/branchSelectBottom'); ?>
  <script>
     var reportingLine =  <?php echo json_encode($reportingLine);?>;
     var type = '<?php echo $_GET['type']?>';
     var branchId = '<?php echo $_GET['branchId']?>';
     console.log(type)
    $(document).ready(function () {
    // 通过该方法来为每次弹出的模态框设置最新的zIndex值，从而使最新的modal显示在最前面
        $(document).on('show.bs.modal', '.modal', function (event) {
            var zIndex = 1040 + (10 * $('.modal:visible').length);
            $(this).css('z-index', zIndex);
            setTimeout(function() {
                $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
            }, 0);
        });
    });
    var height=document.documentElement.clientHeight;
    var container = new Vue({
    el: "#container",
    data: {
        height:height,
        activeName:type,
        type:type,
        branchId:branchId,
        reportingLine:reportingLine,
        schoolList:{},
        schoolId:'',
        listData:{},
        reportData:{},
        searchText :'',
        allDept:{},
        currentDept :{},
        staffSelected:[],
        addType :'',
        confirmLoading :false,
        dep_name:'',
        groupData:{
            id:'',
            name:'',
            cc:[],
            backup:[]
        },
        delId:'',
        activeSection:'',
        delType:''
      },
    created() {
        let title=this.reportingLine.filter((i) => i.type === this.type)
        this.backTitle=title[0].name
        this.getIndex()
        this.getSchool()
    },
    beforeUnmount() {
        this.$refs.content.removeEventListener('scroll', this.checkActiveSection);
    },
    computed: {
        
        searchStaffList: function() {
            var search = this.searchText;
            var searchVal = ''; //搜索后的数据
            if(search) {
                searchVal =Object.values(this.currentDept.user_info).filter(function(product) {
                    return Object.keys(product).some(function(key) {
                        return String(product['name'].toLowerCase()).indexOf(search.toLowerCase()) !== -1;
                    })
                })
                return searchVal;
            }
            return this.searchStaffList;
        },
    },
    methods: {
        getSchool(){
          let that=this
          $.ajax({
            url: '<?php echo $this->createUrl("schoolList") ?>',
            type: "post",
            dataType: 'json',
            data: {
            },
            success: function(data) {
                if (data.state == 'success') {
                    that.schoolList=data.data.list  
                    that.schoolId=data.data.current_school
                }else{
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                }
            },
            error: function(data) {
                resultTip({
                    error: 'warning',
                    msg: data.message
                });
            },
          })
        },
        getIndex(){
          let that=this
          $.ajax({
              url: '<?php echo $this->createUrl("approvalGroupList") ?>',
              type: "get",
              dataType: 'json',
              data: {
                school_id:this.branchId,
                type:this.type
              },
              success: function(data) {
                  if (data.state == 'success') {
                    console.log(data)
                    that.listData=data.data
                    let list=Object.values( that.listData.list)
                    if(list.length!=0){
                        that.activeSection = list[0]._id;
                    }
                    that.$nextTick(()=>{
                        if(that.$refs.content){
                            that.$refs.content.addEventListener('scroll', that.checkActiveSection);
                        }
                    })
                  }else{
                      resultTip({
                          error: 'warning',
                          msg: data.message
                      });
                  }
              },
              error: function(data) {
                  resultTip({
                      error: 'warning',
                      msg: data.message
                  });
              },
          })
        },
        scrollList(id){
            this.activeSection=id
            var targetElement = document.querySelector('#list'+id);
            window.location.hash = '#list'+id
            // console.log(targetElement)
            // if (targetElement) {
            //     targetElement.scrollIntoView({behavior: 'smooth'});
            // }
        },
        checkActiveSection() {
            // 根据滚动位置更新activeSection
            if(this.listData.list){
                let list=Object.values( this.listData.list)
                list.forEach(list => {
                    const element = this.$refs['list'+list._id][0];
                    if (element) {
                        const rect = element.getBoundingClientRect();
                        if (rect.top >= 0 && rect.bottom <= this.$refs.content.offsetHeight) {
                            this.activeSection = list._id;
                        }
                    }
                });
            }
        },
        delGroup(list){
            if(list){
            this.delType='del'
            this.delId=list._id
            $("#delModal").modal('show')
            return
          }
          let that=this
          $.ajax({
              url: '<?php echo $this->createUrl("deleteApprovalGroup") ?>',
              type: "get",
              dataType: 'json',
              data: {
                id:this.delId
              },
              success: function(data) {
                  if (data.state == 'success') {
                    $("#delModal").modal('hide')
                     that.getIndex()
                     resultTip({
                          msg: data.message
                      });
                  }else{
                      resultTip({
                          error: 'warning',
                          msg: data.message
                      });
                  }
              },
              error: function(data) {
                  resultTip({
                      error: 'warning',
                      msg: data.message
                  });
              },
          })
        },
        addGroup(data){
            if(data){
              let copyData=JSON.parse( JSON.stringify (data)) 
                this.groupData={
                    id:copyData._id,
                    name:copyData.name,
                    cc:copyData.config.cc,
                    backup:copyData.config.backup
                }

            }else{
                this.groupData={
                    id:'',
                    name:'',
                    cc:[],
                    backup:[]
                }
            }
            this.allDept=Object.assign(this.allDept, this.listData.staffInfo)
            $("#editModal").modal('show')
        },
        addStaff(type,tab){
          let that=this
          this.addType=type
          this.dep_name=''
          $.ajax({
              url: '<?php echo $this->createUrl("leave/getAllDepartment") ?>',
              type: "post",
              dataType: 'json',
              data:{
                school_id:this.schoolId
              },
              success: function(data) {
                  if (data.state == 'success') {
                        that.currentDept=data.data
                        that.allDept=Object.assign(that.allDept,  data.data.user_info)
                       
                        if(!tab){
                            if(type=='backup'){
                                that.staffSelected=JSON.parse( JSON.stringify (that.groupData.backup)) 
                            }else{
                                that.staffSelected=JSON.parse( JSON.stringify (that.groupData.cc)) 

                            }
                        for(var key in that.currentDept.user_info){
                            if(that.staffSelected.indexOf(key+'')!=-1){
                                Vue.set(that.currentDept.user_info[key], 'disabled', true);
                            }else{
                                Vue.set(that.currentDept.user_info[key], 'disabled', false);
                            }
                        }
                        $("#addStaffModal").modal('show')
                        }
                  }else{
                      resultTip({
                          error: 'warning',
                          msg: data.message
                      });
                  }
              },
              error: function(data) {
                  resultTip({
                      error: 'warning',
                      msg: data.message
                  });
              },
          })
        },
        showDepName(list){
            if(this.dep_name==list.dep_name){
                this.dep_name=''
                return
            }
            this.dep_name=list.dep_name
           
            for(let i=0;i<list.user.length;i++){
                if(this.staffSelected.indexOf(list.user[i].uid)!=-1){
                    Vue.set(this.currentDept.user_info[list.user[i].uid], 'disabled', true);
                }
            }
            console.log(this.dep_name)
        },
        selectAll(list,index){
          list.user.forEach(item => {
              if(!this.currentDept.user_info[item.uid].disabled){
                  this.staffSelected.push(item.uid)
                  Vue.set(this.currentDept.user_info[item.uid], 'disabled', true);
              }
          });
        },
        assignStaff(list,index,idx){
            if(idx=='search'){
                this.searchStaffList[index].disabled=true
            }else{
                Vue.set(this.currentDept.user_info[list.uid], 'disabled', true);
            }
            this.staffSelected.push(list.uid)
        },
        Unassign(list,index){
            if(this.currentDept.user_info[list]){
                Vue.set(this.currentDept.user_info[list], 'disabled', false);
            }
            this.staffSelected.splice(index,1)
        },
        editUnassign(list,index,type){
            this.groupData[type].splice(index,1)
        },
        clearStaff(type,list){
            if(!list){
                this.delType=type
                $("#delModal").modal('show')
                return
            }
            $("#delModal").modal('hide')
            this.groupData[type]=[]
        },
        delSelect(id,type){
          if(type=='top'){
            this.reportData.top=0
          }else{
            this.reportData.last.splice(id,1)
          }
        },
        confirmSatff(){
         console.log(this.addType)
          let that=this
          if(this.addType=='backup'){
            that.groupData.backup=JSON.parse( JSON.stringify (that.staffSelected)) 
          }else{
            that.groupData.cc=JSON.parse( JSON.stringify (that.staffSelected)) 
          }
          $("#addStaffModal").modal('hide')

        },
        saveStaff(){
          let that=this
          $.ajax({
              url: '<?php echo $this->createUrl("saveApprovalGroup") ?>',
              type: "post",
              dataType: 'json',
              data:{data:that.groupData},
              success: function(data) {
                  if (data.state == 'success') {
                    resultTip({
                    msg: data.message
                    });
                    $("#editModal").modal('hide')
                    that.getIndex()
                  }else{
                      resultTip({
                          error: 'warning',
                          msg: data.message
                      });
                  }
              },
              error: function(data) {
                  resultTip({
                      error: 'warning',
                      msg: data.message
                  });
              },
          })
        },
        handleClick(){
          this.type=this.activeName
          this.getIndex()
        }
      }
    })
  </script>
<style >
  [v-cloak]{
        display:none;
    }
  .font600{
    font-weight:600
  }
    /* 盒子css */
  
    .scroll-box::-webkit-scrollbar{
      background-color: #ccc;
      height:6px;
      width: 5px;
    } 
    .scroll-box::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius   : 10px;
        background-color: skyblue;
        background-color: #D8D8D8;
        background-image: none
    }
    .scroll-box::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0);
        background   : #fff;
        border-radius: 10px;
    }
    .department_name{
      padding:6px 12px;
      background: #F7F7F8;
      border-radius: 4px;
      margin-bottom:8px
    }
    
    .ml0{
      margin:0
    }
    .colorBlue{
      color:#4D88D2
    }
    .listStaff{
        padding:8px 16px;
        border: 1px solid #fff;
        background: #FAFAFA;
        border-radius: 4px;
    }
    .listMedia{
        padding:8px;
        border: 1px solid #fff;
    }
    .listMedia:hover{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        border: 1px solid #4D88D2;
        cursor: pointer;
    }
    .borderLeftpos {
        display: inline-block;
        border-left: 1px solid #E4E7ED;
        height: 100%;
        width: 1px;
        position: absolute;
        left: 50%;
    }
    .optionSearch{
        height:auto;
        padding:5px 20px
    }
    .text_overflow {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        line-height: 14px;
    }
    .flexText{
      width:0
    }
    .word-break{
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .colorRed{
        color:rgb(217, 83, 79)
    }
    .allCheck{
        position: absolute;
        right: 10px;
        top: 0;
    }
    .border{
        border: 1px solid #DCDFE6;
        padding: 16px;
        border-radius: 4px;
    }
    .treeNum{
      background: #666666;
      border-radius: 20px;
      border: 1px solid #FFFFFF;
      padding:2px 6px;
      color:#fff;
      height: 20px;
      line-height: 19px;
      display: inline-block;
      margin-left: 10px;
      padding: 0 6px;
    }
    .img28{
      height: 28px;
      width: 28px;
      margin-right:6px;
      border-radius: 50%;
    }
    .img42{
      width: 42px;
      height: 42px;
      object-fit: cover;
      border-radius:50%;
    }
    .el-tabs__item:hover,.el-tabs__item.is-active{
      color:#4D88D2
    }
    .el-tabs__active-bar{
      background-color:#4D88D2
    }
    .pb50{
      padding-bottom:50px
    }
    .tabList{
      border-bottom:1px solid #D9D9D9;
    }
    .pl0{
      padding-left:0 !important
    }
    .borderBto{
      border-bottom:2px solid #4D88D2;
      color:#4D88D2
    }
    .tabHeight{
      height: 40px;
      box-sizing: border-box;
      line-height: 40px;
      display:inline-block;
    }
    .minWidth{
        min-width:auto
    }
    .navList{
        padding:10px;
        font-size:14px;
        color: #333333;
    }
    .navList:hover{
        background:#F7F7F8;
        cursor: pointer;
    }
    .navAactive{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        color: #4D88D2;
    }
</style>
 