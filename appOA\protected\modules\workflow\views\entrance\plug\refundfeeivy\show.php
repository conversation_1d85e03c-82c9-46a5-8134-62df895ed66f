<!-- Modal -->
<?php
    $labels = Invoice::model()->attributeLabels();
    $policyApi = new PolicyApi();
?>
<?php echo CHtml::form('/workflow/entrance/index', 'post',array('class'=>'J_ajaxForm'));?>
<div class="modal fade" id="<?php echo $data['modalNameId']?>" tabindex="-1" role="dialog" aria-labelledby="workflowModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel"><?php echo CommonUtils::autoLang($data['workflow']->definationObj->defination_name_cn,$data['workflow']->definationObj->defination_name_en);?> - <?php echo CommonUtils::autoLang($data['workflow']->nodeObj->node_name_cn,$data['workflow']->nodeObj->node_name_en);?><a href="#" id="print"> [打印]</a></h4>
            </div>
            <div class="modal-body" id="workflow-modal-body">
                <div class="alert alert-success" role="alert">
                    <span class="sr-only">Refund:</span>
                    [ <?php
                        echo CHtml::link($data['profile']->getChildName(), $this->createUrl('//child/invoice/finance',array('t'=>'invoice','childid'=>$data['workflow']->getOperateObjId())),array('target'=>'_blank'));?> ] <?php echo $data['type'][$data['workflow']->getOperateValue('exte3')];
                    ?>
                    <span class="text-danger">(本学年退过<?php echo $data['profile']->refundfeeNum(); ?>次学费，包含本次)</span>
                </div>
                <div class="table-responsive">
                <table class="table">
                    <colgroup>
                        <col class="col-md-4">
                        <col class="col-md-2">
                        <col class="col-md-2">
                        <col class="col-md-2">
                        <col class="col-md-2">
                        <col class="col-md-2">
                    </colgroup>
                    <thead>
                        <tr>
                            <td><?php echo $labels['title'];?></td>
                            <td><?php echo $labels['payment_type'];?></td>
                            <td><?php echo $labels['amount'];?></td>
                            <td><?php echo '预交学费'?></td>
                            <td><?php echo $labels['startdate'];?></td>
                            <td><?php echo $labels['enddate'];?></td>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($data['refundList'] as $invoice):
                            $depositAmount = OA::formatMoney($invoice->Invoice->original_amount - $invoice->Invoice->amount);
                        ?>
                        <tr>
                            <td><?php echo $invoice->Invoice->title;?></td>
                            <td><?php echo $policyApi->renderFeeType($invoice->Invoice->payment_type);?></td>
                            <td><?php echo $invoice->Invoice->amount;?></td>
                            <td><?php echo $depositAmount>0?$depositAmount:'0.00';?></td>
                            <td><?php echo OA::formatDateTime($invoice->Invoice->startdate);?></td>
                            <td><?php echo OA::formatDateTime($invoice->Invoice->enddate);?></td>
                        </tr>
                        <tr class="active">
                            <td colspan="3">
                                <div class="form-group" style="width: 250px;">
                                    退费标题：<?php echo $invoice->title;?>
                                </div>
                                <div class="form-group" style="width: 250px;">
                                    退费金额：<span style="color:#cc0001"><?php echo number_format($invoice->amount,2);?></span>
                                </div>
                            </td>
                            <td colspan="3">
                                <div class="form-group" style="width: 250px;">
                                    开始日期：<?php echo OA::formatDateTime($invoice->startdate);?>
                                </div>
                                <div class="form-group" style="width: 250px;">
                                    结束日期：<?php echo OA::formatDateTime($invoice->enddate);?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach;?>
                    </tbody>
                </table>
                </div>
                <!-- 显示附件 -->
                <div id="fileList">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>相关资料</th>
                            </tr>
                        </thead>
                        <?php foreach ($data['workflow']->getAttachmentList() as $uploadFile): ?>
                        <tr>
                            <td><a target="_blank" href="<?php echo $uploadFile['bigimages']; ?>"><?php echo $uploadFile['notes']; ?></a></td>
                        </tr>
                        <?php endforeach; ?>
                    </table>
                </div>
                <div class="retshow table-responsive">
                    <h3><?php echo Yii::t('workflow', 'Comments:');?></h3>
                     <?php
                        $processList = $data['workflow']->getWorkflowProcess();
                        foreach ($processList as $process):?>
                    <h4>
                        <em><?php echo Yii::t('workflow', 'Date submitted:');?>： <?php echo OA::formatDateTime($process->start_time);?></em>
                        <span><?php echo $process->user->getName();?></span>
                    </h4>
                    <div class="clearfix"></div>
                    <div class="comment">
                        <label><?php echo Yii::t('workflow', 'Option:');?></label>
                        <p><?php echo nl2br(htmlspecialchars($process->process_desc));?></p>
                    </div>
                    <?php endforeach;?>
                </div>
                <?php if ($data['workflow']->useRole && $data['workflow']->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_UNOP):?>
                <div class="form-group" style="padding-top:50px;">
                    <?php echo CHtml::textArea('memo','',array('class'=>'form-control','row'=>3));?>
                </div>
                <?php endif;?>
            </div>
            <?php
            if ($data['workflow']->useRole && $data['workflow']->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_UNOP && !$data['workflow']->isFristNode()):?>
            <div class="modal-footer">
                <?php echo CHtml::hiddenField('definationId', $data['workflow']->getWorkflowId());?>
                <?php echo CHtml::hiddenField('nodeId', $data['workflow']->getCurrentNodeId());?>
                <?php echo CHtml::hiddenField('branchId', $data['workflow']->schoolId);?>
                <?php echo CHtml::hiddenField('operationId', $data['workflow']->getOperateId());?>
                <?php echo CHtml::hiddenField('action', 'show');?>
                <?php echo CHtml::hiddenField('buttonCategory','');?>
                <button type="button" class="btn btn-primary J_ajax_submit_btn" data-category="<?php echo WorkflowOperation::WORKFLOW_STATS_OPED;?>"><?php echo Yii::t('workflow','Yes');?></button>
                <button type="button" class="btn btn-danger J_ajax_submit_btn" data-category="<?php echo WorkflowOperation::WORKFLOW_STATS_OPDENY;?>"><?php echo Yii::t('workflow','No');?></button>
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
            </div>
            <?php endif;?>
        </div>
    </div>
</div>
<?php CHtml::endForm();?>
<script type="text/javascript">
    //打印
    $(function(){
         $("#print").click(function(){
            $("#workflow-modal-body a").removeAttr('href');
            $("#workflow-modal-body").printThis();
         })
    });
</script>