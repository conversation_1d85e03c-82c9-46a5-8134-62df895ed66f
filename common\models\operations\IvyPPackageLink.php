<?php

/**
 * This is the model class for table "ivy_p_package_link".
 *
 * The followings are the available columns in table 'ivy_p_package_link':
 * @property integer $id
 * @property integer $product_id
 * @property string $package_id
 */
class IvyPPackageLink extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return IvyPPackageLink the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_p_package_link';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('package_id', 'required'),
			array('product_id', 'numerical', 'integerOnly'=>true),
			array('package_id', 'length', 'max'=>10),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, product_id, package_id', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'Product' => array(self::BELONGS_TO,'IvyPProduct','product_id','select'=>'title'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'product_id' => 'Product',
			'package_id' => 'Package',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('product_id',$this->product_id);
		$criteria->compare('package_id',$this->package_id,true);

		return new CActiveDataProvider(get_class($this), array(
			'criteria'=>$criteria,
		));
	}
	
	
	/**
	 * 根据包的标识查询产品信息
	 * @param string $packageId
	 * @return ArrayObject
	 * <AUTHOR>
	 * @copyright IvyGroup
	 */
	public function getProduct($packageId)
	{
		return $this->model()->with('Product')->findAll('package_id=:package_id',array(':package_id'=>$packageId));
	}
}