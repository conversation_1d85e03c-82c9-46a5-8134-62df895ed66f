<?php

class AttendanceController extends BranchBasedController
{
    public $siderMenu    = array();
    public $tid          = -1;
    public $timetable;
    public $allTimetable = array();
    public $printFW      = array();
    // 访问action的初级权限
    public $actionAccessAuths = array(
        // 教师
        //        'index'  => 'ivystaff_teacher',
        //        'courseStudent'  => array('ivystaff_teacher', 'o_MS_TimetableMgt'),
        //        'updateRecords'  => 'ivystaff_teacher',
        // 工作人员
        'assignTeacher'          => 'o_MS_TimetableMgt',
        'updateTeacher'          => 'o_MS_TimetableMgt',
        'student'                => 'o_MS_Timetable_Attendance',
        //        'arrangementOverview' => 'o_MS_TimetableMgt',
        //        'showCourseChild' => 'o_MS_TimetableMgt',
        'childCourseList'        => 'o_MS_TimetableMgt',
        'updateChildCourse'      => 'o_MS_TimetableMgt',
        'deleteChildCourse'      => 'o_MS_TimetableMgt',
        'childRecords'           => 'o_MS_Timetable_Attendance',
        'updateRecordsAll'       => 'o_MS_Timetable_Attendance',
        'updateRecordsOne'       => 'o_MS_Timetable_Attendance',
        'StudentRecordsOverview' => 'o_MS_Timetable_Attendance',
        'report'                 => 'o_MS_Timetable_Attendance',
        'showAttendStudent'      => 'o_MS_Timetable_Attendance',
    );

    public function createUrl($route, $params = array(), $ampersand = '&', $parentOnly = false)
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        if (empty($params['tid'])) {
            $params['tid'] = $this->tid;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout     = "//layouts/column1";

        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/vue2.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/jquery.qrcode.min.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.js');
        $this->modernMenuFlag          = 'campusOp';
        $this->modernMenu              = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Campus Workspace');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['hideKG']     = true;
        $this->branchSelectParams['urlArray']   = array('//mgrades/attendance/assignTeacher');

        Yii::import('common.models.timetable.*');
        Yii::import('common.models.portfolio.*');
        Yii::import('common.models.secondary.*');

        $this->siderMenu = array(
            'assignTeacher'          => array(
                'label' => Yii::t('attends', 'Course-Teacher Assignment'),
                'url'   => array('//mgrades/attendance/assignTeacher'),
            ),
            'studentSchedule'        => array(
                'label' => Yii::t('attends', 'Student Schedule'),
                'url'   => array('//mgrades/attendance/student'),
            ),
            'scheduleOverview'       => array(
                'label' => Yii::t('attends', 'Schedules Overview'),
                'url'   => array('//mgrades/attendance/arrangementOverview'),
            ),
            'studentRecordsOverview' => array(
                'label' => Yii::t('attends', 'Student Attendance Overview'),
                'url'   => array('//mgrades/attendance/studentRecordsOverview'),
            ),
            'report'                 => array(
                'label' => Yii::t('attends', 'Attendance Report'),
                'url'   => array('//mgrades/attendance/report'),
            ),
        );
        //
        if (!Yii::app()->user->checkAccess('ivystaff_it')) {
//            unset($this->siderMenu['report']);
        }
    }


    public function beforeAction($action)
    {
        if (!parent::beforeAction($action)) {
            return false;
        }

        $this->getCalendars();

        $this->tid = Yii::app()->request->getParam('tid', '-1');

        $criteria = new CDbCriteria();
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('status', 1);
        $timetables = Timetable::model()->findAll($criteria);

        if (!$timetables) {
            return false;
        }

        foreach ($timetables as $timetable) {
            $this->allTimetable[$timetable->id] = array(
                'yid' => $timetable->yid,
            );
            if ($this->tid == $timetable->id) {
                $this->timetable = $timetable;
            }
        }
        if (!$this->timetable) {
            $this->timetable = $timetables[0];
            $this->tid       = $timetables[0]->id;
        }
        return true;
    }


    /**
     * 按课程签到
     */
    public function actionIndex()
    {
        $weekTime  = Yii::app()->request->getParam('weekTime', '');
        $tid       = Yii::app()->request->getParam('tid', '');
        $teacherId = Yii::app()->request->getParam('teacherId', '');

        $tids = $tid ? $tid : $this->tid;

        $teacherData = array();
        $numberArr   = array();
        $nowtime     = ($weekTime) ? strtotime($weekTime) : time();
        //  根据时间获取周数的日期
        $dateN   = date('N', $nowtime);
        $weks    = 604800; # 一周的秒数
        $monday  = $nowtime - ($dateN - 1) * 3600 * 24;
        $strDate = array(strtotime(date("Y-m-d", $monday)) => date("Y-m-d", $monday));

        for ($i = 1; $i < 5; $i++) {
            $data                      = date("Y-m-d", strtotime($i . ' days', $monday));
            $strDate[strtotime($data)] = $data;
        }

        $uid       = $teacherId ? $teacherId : Yii::app()->user->id;
        $userModel = User::model()->findByPk($uid);

        // 根据时间判断是第一学期还是第二学期
        $semesterId         = $this->getSemesterIndex($this->timetable->yid, $nowtime);
        $scheduleCacheModel = $this->getScheduleCache(20, $uid, $tids, $semesterId);
        if (!$scheduleCacheModel) {
            $scheduleCacheModel = $this->cache($uid, $tids, $semesterId);
        }

        $teacherArrangement  = array();
        $teacherArrangements = array();
        if ($scheduleCacheModel) {
            $teacherArrangement = json_decode($scheduleCacheModel->data, true);
            foreach ($teacherArrangement as $key => $val) {
                foreach (explode(',', $val) as $item) {
                    $teacherArrangements[$key][$item] = $item;
                    $courseCodeArr[]                  = $item;
                }

            }
        }
        if ($teacherArrangements) {
            $criteria = new CDbCriteria;
            $criteria->compare('course_code', $courseCodeArr);
            $criteria->index = 'course_code';
            $courseModel     = TimetableCourses::model()->findAll($criteria);

            foreach ($teacherArrangements as $k => $val) {
                foreach ($val as $item) {
                    $teacherData[$k][] = array(
                        'course_code'  => $item,
                        'course_title' => ($courseModel[$item]) ? $courseModel[$item]->getAliasTitle($tids) : "",
                    );
                    $courseArrId[]     = $courseModel[$item]->id;
                }
            }

            $start    = strtotime(date("Y/m/d", $monday));
            $end      = strtotime(date("Y/m/d", strtotime('+5 days', $monday)));
            $criteria = new CDbCriteria;
            $criteria->compare('course_id', $courseArrId);
            $criteria->compare('target_date', ">={$start}");
            $criteria->compare('target_date', "<{$end}");
            $criteria->compare('tid', $tids);
            $numberCacheModel = TimetableStudentNumberCache::model()->findAll($criteria);

            if ($numberCacheModel) {
                foreach ($numberCacheModel as $item) {
                    $numberArr[$item->target_date][$item->weekday . "-" . $item->period][$item->courses->course_code] = json_decode($item->data, true);
                }
            }
        }

        $titleTime = date("Y/m/d", $monday) . " - " . date("Y/m/d", strtotime('+4 days', $monday));
        Yii::import('common.models.calendar.CalendarSchoolDays');
        $schoolDays = CalendarSchoolDays::model()->getCalendarSchooldays($this->timetable->yid, $monday, $monday + 3600 * 24 * 4);

        $this->render('index', array(
            'schoolid'    => $this->branchId,
            'strDate'     => $strDate,
            'teacherData' => $teacherData,
            'titleTime'   => $titleTime,
            'nowtime'     => $nowtime,
            'numberArr'   => $numberArr,
            'tid'         => $tids,
            'weekTime'    => $nowtime,
            'userModel'   => $userModel,
            'uid'         => $uid,
            'teacherId'   => $teacherId,
            'schoolDays'  => $schoolDays[$this->timetable->yid]['month'],
        ));
    }

    public function actionCourseStudent()
    {
        $weekday_period = Yii::app()->request->getParam('weekday', ''); // 例如 1-2
        $course_code    = Yii::app()->request->getParam('course_code', ''); // 课程的code
        $datatime       = Yii::app()->request->getParam('datatime', ''); // 选的时间
        $tid            = Yii::app()->request->getParam('tid', '');
        $uid            = Yii::app()->request->getParam('uid', '');

        $tids = $tid ? $tid : $this->tid;

        $studentLiat   = array();
        $studentLiat_t = array();
        $courseModel   = array();
        $recordsArr    = array();
        $numberTypes   = array(
            TimetableRecords::ATTENDANCE_STATUS => 0,
            TimetableRecords::LATE_STATUS       => 0,
            TimetableRecords::LEAVE_STATUS      => 0,
            TimetableRecords::ABSENTEE_STATUS   => 0,
            0                                   => 0,
            1                                   => 0,
        );
        if ($course_code) {
            // 根据时间判断是第一学期还是第二学期
            $semesterId       = $this->getSemesterIndex($this->timetable->yid, strtotime($datatime));
            $studentDataModel = $this->getStudentCourseData($tids, $course_code, $semesterId);

            foreach ($studentDataModel as $val) {
                if (in_array($val->childProfile->status, array(ChildProfileBasic::STATS_ACTIVE_WAITING, ChildProfileBasic::STATS_ACTIVE))) {
                    $studentLiat[$val->child_id] = array(
                        'class_room'  => $val->class_room,
                        'course_code' => $val->course_code,
                        'childName'   => isset($val->childProfile) ? $val->childProfile->getChildName(false, false, true) : "",
                        'photo'       => isset($val->childProfile) ? $val->childProfile->photo : "",
                        'gender'      => isset($val->childProfile) ? $val->childProfile->gender : "",
                    );
                    asort($studentLiat);
                } else {
                    $studentLiat_t[$val->child_id] = array(
                        'class_room'  => $val->class_room,
                        'course_code' => $val->course_code,
                        'childName'   => isset($val->childProfile) ? $val->childProfile->getChildName(false, false, true) : "",
                        'photo'       => isset($val->childProfile) ? $val->childProfile->photo : "",
                        'gender'      => isset($val->childProfile) ? $val->childProfile->gender : "",
                    );
                    asort($studentLiat_t);
                }
            }
            $recordsArr = array();
            foreach ($studentLiat as $childid => $val) {
                $recordsArr[$childid] = array(
                    'type'      => 10,
                    'memo'      => '',
                    'late_time' => '',
                    'dress'     => '',
                    'is_admin'  => 0,
                );
            }
            $criteria = new CDbCriteria;
            $criteria->compare('course_code', $course_code);
            $courseModel = TimetableCourses::model()->find($criteria);
            if ($weekday_period && $datatime) {
                $weekday = substr($weekday_period, 0, 1);
                $period  = substr($weekday_period, -1, 1);

                $criteria = new CDbCriteria;
                $criteria->compare('course_code', $course_code);
                $criteria->compare('weekday', $weekday);
                $criteria->compare('period', $period);
                $criteria->compare('target_date', strtotime($datatime));
                $criteria->compare('school_id', $this->branchId);
                $criteria->compare('tid', $tids);
                $criteria->compare('status', 10);
                $model = TimetableRecords::model()->findAll($criteria);

                if ($model) {
                    foreach ($model as $child => $val) {
                        $recordsArr[$val->childid] = array(
                            'type'      => $val->type,
                            'memo'      => $val->memo,
                            'late_time' => $val->late_time,
                            'dress'     => $val->dress,
                            'is_admin'  => $val->is_admin,
                        );
                        if (isset($studentLiat_t) && isset($studentLiat_t[$val->childid])) {
                            $studentLiat[$val->childid] = $studentLiat_t[$val->childid];
                        }
                        asort($studentLiat);
                        $numberTypes[$val->type]  += 1;
                        $numberTypes[$val->dress] += 1;
                    }
                }
            }
        }
        $status = 0;
        if ($uid == Yii::app()->user->id || Yii::app()->user->checkAccess('o_MS_TimetableMgt')) {
            $status = 1;
        }

        $this->render('courseStudent', array(
            'studentLiat' => $studentLiat,
            'datatime'    => $datatime,
            'weekday'     => $weekday_period,
            'course_code' => $course_code,
            'courseModel' => $courseModel,
            'recordsArr'  => $recordsArr,
            'numberTypes' => $numberTypes,
            'status'      => $status,
            'uid'         => $uid,
        ));
    }

    public function actionCourseStudentPrint()
    {
        $weekday_period = Yii::app()->request->getParam('weekday', ''); // 例如 1-2
        $course_code    = Yii::app()->request->getParam('course_code', ''); // 课程的code
        $datatime       = Yii::app()->request->getParam('datatime', ''); // 选的时间
        $tid            = Yii::app()->request->getParam('tid', '');
        $uid            = Yii::app()->request->getParam('uid', '');

        $tids = $tid ? $tid : $this->tid;

        $week      = substr($weekday_period, 0, 1);
        $period    = substr($weekday_period, -1, 1);
        $weekArr   = array(
            1 => Yii::t('attends', 'Mon'),
            2 => Yii::t('attends', 'Tue'),
            3 => Yii::t('attends', 'Wed'),
            4 => Yii::t('attends', 'Thu'),
            5 => Yii::t('attends', 'Fri'),
        );
        $getTime   = TimetableCourses::getTime();
        $weekTitle = sprintf(Yii::t('attends', '%s period #%s'), $weekArr[$week], $period);
        $classTime = $getTime[$period];

        $studentLiat   = array();
        $studentLiat_t = array();
        if ($course_code) {
            // 根据时间判断是第一学期还是第二学期
            $semesterId       = $this->getSemesterIndex($this->timetable->yid, strtotime($datatime));
            $studentDataModel = $this->getStudentCourseData($tids, $course_code, $semesterId);

            foreach ($studentDataModel as $val) {
                if (in_array($val->childProfile->status, array(ChildProfileBasic::STATS_ACTIVE_WAITING, ChildProfileBasic::STATS_ACTIVE))) {
                    $studentLiat[$val->child_id] = array(
                        'class_room'  => $val->class_room,
                        'course_code' => $val->course_code,
                        'childName'   => isset($val->childProfile) ? $val->childProfile->getChildName(false, false, true) : "",
                        'photo'       => isset($val->childProfile) ? $val->childProfile->photo : "",
                        'gender'      => isset($val->childProfile) ? $val->childProfile->gender : "",
                    );
                    asort($studentLiat);
                } else {
                    $studentLiat_t[$val->child_id] = array(
                        'class_room'  => $val->class_room,
                        'course_code' => $val->course_code,
                        'childName'   => isset($val->childProfile) ? $val->childProfile->getChildName(false, false, true) : "",
                        'photo'       => isset($val->childProfile) ? $val->childProfile->photo : "",
                        'gender'      => isset($val->childProfile) ? $val->childProfile->gender : "",
                    );
                    asort($studentLiat_t);
                }
            }
        }

        $teacherModel = User::model()->findByPk($uid);
        $courseModel  = TimetableCourses::model()->findByAttributes(array('course_code' => $course_code));

        $this->layout = '//layouts/print';

        $this->render('courseStudentPrint', array(
            'studentLiat'  => $studentLiat,
            'weekTitle'    => $weekTitle,
            'classTime'    => $classTime,
            'courseModel'  => $courseModel,
            'teacherModel' => $teacherModel,
        ));
    }

    public function actionUpdateRecords()
    {
        $weekday_period = Yii::app()->request->getParam('weekday', ''); // 例如 1-2
        $course_code    = Yii::app()->request->getParam('course_code', ''); // 课程的code
        $datatime       = Yii::app()->request->getParam('datatime', ''); // 选的时间
        $type           = Yii::app()->request->getParam('type', ''); // 例如 1-2
        $dress          = Yii::app()->request->getParam('dress', ''); // 例如 1-2
        $content        = Yii::app()->request->getParam('content', ''); //
        $select         = Yii::app()->request->getParam('select', ''); //
        $tid            = Yii::app()->request->getParam('tid', ''); //
        $teacherId      = Yii::app()->request->getParam('teacherId', ''); //
        $tids           = $tid ? $tid : $this->tid;

        if ($type) {
            $weekday = substr($weekday_period, 0, 1);
            $period  = substr($weekday_period, -1, 1);
            if (!Yii::app()->user->checkAccess('o_MS_TimetableMgt')) {
                if (strtotime($datatime) != strtotime(date("Y-m-d", time()))) {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('attends', 'The teachers can mark/edit the students attendance records within the current school day only'));
                    $this->showMessage();
                }
            }

            $criteria = new CDbCriteria;
            $criteria->compare('course_code', $course_code);
            $courseModel = TimetableCourses::model()->find($criteria);

            $criteria = new CDbCriteria;
            $criteria->compare('course_code', $course_code);
            $criteria->compare('child_id', array_keys($type));
            $criteria->compare('tid', $tids);
            $criteria->compare('status', 1);
            $criteria->index = 'child_id';
            $childidModel    = TimetableStudentData::model()->findAll($criteria);


            if ($courseModel) {
                $criteria = new CDbCriteria;
                $criteria->compare('course_id', $courseModel->id);
                $criteria->compare('teacher_id', Yii::app()->user->id);
                $criteria->compare('status', 1);
                $teacherModel = TimetableCourseTeacher::model()->count($criteria);
                if (!($teacherModel || Yii::app()->user->checkAccess('o_MS_TimetableMgt'))) {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('attends', 'View Only! You have no attendance permission of this course.'));
                    $this->showMessage();
                }

                $status = 0;
                foreach ($type as $key => $val) {
                    if ($val == TimetableRecords::LATE_STATUS) {
                        if (!$select[$key]) {
                            $status = 1;
                        }
                    }
                }
                if ($status) {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('attends', '迟到未填写迟到时间'));
                    $this->showMessage();
                } else {
                    $data       = array(
                        TimetableRecords::ATTENDANCE_STATUS => 0,
                        TimetableRecords::LATE_STATUS       => 0,
                        TimetableRecords::LEAVE_STATUS      => 0,
                        TimetableRecords::ABSENTEE_STATUS   => 0,
                    );
                    $class_room = "";

                    $criteria = new CDbCriteria;
                    $criteria->compare('course_code', $course_code);
                    $criteria->compare('weekday', $weekday);
                    $criteria->compare('period', $period);
                    $criteria->compare('target_date', strtotime($datatime));
                    $criteria->compare('school_id', $this->branchId);
                    $criteria->compare('childid', array_keys($type));
                    $criteria->compare('tid', $tids);
                    $recordsModel = TimetableRecords::model()->findAll($criteria);
                    if ($recordsModel) {
                        foreach ($recordsModel as $item) {
                            if ($item->is_admin < 1) {
                                $item->status     = 99;
                                $item->updated_at = time();
                                $item->updated_by = Yii::app()->user->id;
                                $item->save();
                            } else {
                                if ($item->status != 99) {
                                    $data[$item->type] += 1;
                                }
                            }
                        }
                    }


                    $criteria = new CDbCriteria;
                    $criteria->compare('childid', array_keys($type));
                    $criteria->index = 'childid';
                    $childModel      = ChildProfileBasic::model()->findAll($criteria);
                    if ($period == 1) {
                        TimetableRecords::childSignDel(array_keys($childModel), $this->branchId,strtotime($datatime),$this->staff->uid);
                    }
                    foreach ($type as $childid => $val) {
                        $criteria = new CDbCriteria;
                        $criteria->compare('course_code', $course_code);
                        $criteria->compare('weekday', $weekday);
                        $criteria->compare('period', $period);
                        $criteria->compare('target_date', strtotime($datatime));
                        $criteria->compare('school_id', $this->branchId);
                        $criteria->compare('childid', $childid);
                        $criteria->compare('tid', $tids);
                        $criteria->compare('status', 10);
                        $criteria->compare('is_admin', 1);
                        $modelCount = TimetableRecords::model()->find($criteria);

                        if (!$modelCount) {
                            $criteria = new CDbCriteria;
                            $criteria->compare('course_code', $course_code);
                            $criteria->compare('weekday', $weekday);
                            $criteria->compare('period', $period);
                            $criteria->compare('target_date', strtotime($datatime));
                            $criteria->compare('school_id', $this->branchId);
                            $criteria->compare('childid', $childid);
                            $criteria->compare('tid', $tids);
                            $criteria->compare('created_by', Yii::app()->user->id);
                            $model = TimetableRecords::model()->find($criteria);
                            if (!$model) {
                                $model             = new TimetableRecords();
                                $model->tid        = $tids;
                                $model->childid    = $childid;
                                $model->school_id  = $this->branchId;
                                $model->created_at = time();
                                $model->created_by = Yii::app()->user->id;
                            }

                            $model->status      = 10;
                            $model->course_code = $courseModel->course_code;
                            $model->class_room  = $childidModel[$childid]->class_room;
                            $model->class_id    = $childModel[$childid]->classid;
                            $model->weekday     = $weekday;
                            $model->period      = $period;
                            $model->teacher_id  = $teacherId;
                            $model->target_date = strtotime($datatime);
                            $model->late_time   = ($val == TimetableRecords::LATE_STATUS) ? $select[$childid] : "";
                            $model->type        = $val;
                            $model->dress       = $dress[$childid];
                            $model->memo        = $content[$childid];
                            $model->updated_at  = time();
                            $model->updated_by  = Yii::app()->user->id;
                            $model->save();
                            $data[$val] += 1;
                            $class_room = $model->class_room;
                        } else {
                            $modelCount->dress = $dress[$childid];
                            $modelCount->save();
                        }

                        if ($period == 1) {
                            if (in_array($val, array(10, 20, 30, 31,40, 41, 42))) {
                                $memo      = empty($content[$childid]) ? '无' : $content[$childid];
                                $late_time = ($val == TimetableRecords::LATE_STATUS) ? $select[$childid] : 0;

                                TimetableRecords::childSign($childModel[$childid], strtotime($datatime), $val, $late_time, $memo);
                            }
                        }
                    }

                    if ($data) {
                        $criteria = new CDbCriteria;
                        $criteria->compare('course_id', $courseModel->id);
                        $criteria->compare('weekday', $weekday);
                        $criteria->compare('period', $period);
                        $criteria->compare('target_date', strtotime($datatime));
                        $criteria->compare('tid', $tids);
                        $numberCacheModel = TimetableStudentNumberCache::model()->find($criteria);
                        if (!$numberCacheModel) {
                            $numberCacheModel              = new TimetableStudentNumberCache();
                            $numberCacheModel->tid         = $tids;
                            $numberCacheModel->course_id   = $courseModel->id;
                            $numberCacheModel->target_date = strtotime($datatime);
                            $numberCacheModel->weekday     = $weekday;
                            $numberCacheModel->period      = $period;
                            $numberCacheModel->class_room  = $class_room;
                            $numberCacheModel->updated_at  = time();
                        }

                        $numberCacheModel->data = json_encode($data);
                        $numberCacheModel->save();
                    }
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message', "成功"));
                    $this->addMessage('callback', 'cbSuccess');
                    $this->showMessage();
                }
            }
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '错误');
            $this->showMessage();
        }
    }

    // 学生老师分配
    public function actionAssignTeacher()
    {
        $tid     = Yii::app()->request->getParam('tid', $this->tid);
        $is_ajax = Yii::app()->request->getParam('is_ajax', 0); // 例如 1-2

        // 获取课程分类
        $courseType = TimetableCourses::getCourseType();
        $courseType = array(0 => Yii::t('attends', 'Not Assigned')) + $courseType;

        // 获取所有老师
//        $teachers = $this->getCampusTeachers();
        //获取中学老师（可被选择分配的老师）
        $teachers = $this->getMiddleSchoolTeachers();
//        $calendar = Calendar::model()->findByPk($this->branchObj->schcalendar);
        // 获取所有课程
        $coureTeacher = array();
        $criteria = new CDbCriteria;
        $criteria->addCondition('`status` = 1');
        $criteria->addCondition('`start_year` >= 2021');
        $criteria->order = 'course_code ASC';
        $courses = TimetableCourses::model()->findAll($criteria);

        $criteria     = new CDbCriteria();
        $criteria->compare('status', 1);
        $criteria->compare('tid', $tid);
        $criteria->index  = "course_id";
        $assignedTeachers = TimetableCourseTeacher::model()->findAll($criteria);
        $allCourseTeacher = array();//分配到课程的老师
        foreach ($assignedTeachers as $course_id=>$item){
            $allCourseTeacher[$item['teacher_id']] = $item['teacher_id'];
        }
        $allCourseTeacher = array_keys($allCourseTeacher);
        $criteria = new CDbCriteria();
        $criteria->compare('t.level', 1);
        $criteria->compare('t.uid', $allCourseTeacher);
        $criteria->index = 'uid';
        $courseTeacherInfo = User::model()->with('profile', 'staffInfo')->findAll($criteria);
        foreach ($courses as $course) {
            if ($tid > 1 && strlen($course->course_code) == 7) {
                continue;
            }
            if ($tid == 1 && strlen($course->course_code) == 8) {
                continue;
            }
            $typeid = substr($course->course_code, 0, 2);
            if (!in_array($typeid, array_keys($courseType))) {
                $typeid = 100;
            }
            $criteria = new CDbCriteria();
            $criteria->compare('course_id', $course->id);
            $criteria->compare('course_code', $course->course_code);
            $criteria->compare('tid', $tid);
            $courseCount = TimetableStudentData::model()->count($criteria);

            $coureTeacher[$typeid][$course->course_code]['course_id']       = $course->id;
            $coureTeacher[$typeid][$course->course_code]['course_code']     = $course->course_code;
            $coureTeacher[$typeid][$course->course_code]['course_title']    = $course->title_cn;
            $coureTeacher[$typeid][$course->course_code]['course_title_en'] = $course->title_en;
            $coureTeacher[$typeid][$course->course_code]['teacher_id']      = '';
            $coureTeacher[$typeid][$course->course_code]['teacher_name']    = '';
            $coureTeacher[$typeid][$course->course_code]['course_count']    = $courseCount;

            if (isset($assignedTeachers[$course->id])) {
                $coureTeacher[$typeid][$course->course_code]['teacher_id']   = $assignedTeachers[$course->id]->teacher_id;
                $coureTeacher[$typeid][$course->course_code]['teacher_name'] = isset($courseTeacherInfo[$assignedTeachers[$course->id]->teacher_id]) ? $courseTeacherInfo[$assignedTeachers[$course->id]->teacher_id]->getName() : $assignedTeachers[$course->id]->teacher_id;
                $coureTeacher[$typeid][$course->course_code]['course_count']    = $courseCount;
            } else {
                // 所有未分配课程
                $coureTeacher[0][$course->course_code]['course_id']       = $course->id;
                $coureTeacher[0][$course->course_code]['course_code']     = $course->course_code;
                $coureTeacher[0][$course->course_code]['course_title']    = $course->title_cn;
                $coureTeacher[0][$course->course_code]['course_title_en'] = $course->title_en;
                $coureTeacher[0][$course->course_code]['teacher_id']      = '';
                $coureTeacher[0][$course->course_code]['teacher_name']    = '';
                $coureTeacher[0][$course->course_code]['course_count']    = $courseCount;
            }
        }
        $data = array(
            'courseType'   => $courseType,
            'coureTeacher' => $coureTeacher,
            'teachers'     => $teachers,
        );
        if ($is_ajax) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
            $this->showMessage();
        }
        $this->render('assignteacher', $data);
    }

    public function actionMiddleClassList()
    {
        $yid = $this->branchObj->schcalendar;

        $classList = array();
        $crit = new CDbCriteria();
        $crit->compare('schoolid', $this->branchId);
        $crit->compare('child_age', '>=12');
        $crit->compare('yid', $yid);
        $crit->order = 'child_age ASC, title ASC';
        $models = IvyClass::model()->findAll($crit);
        foreach ($models as $class) {
            $classList[] = array(
                'classid' => $class->classid,
                'title' => $class->title,
                'classtype'=>$class->classtype,
            );
        }
        $this->addMessage('state', 'success');
        $this->addMessage('data', $classList);
        $this->showMessage();
    }


    public function actionRectTeacher()
    {
        $course_id  = Yii::app()->request->getParam('courseid');
        $teacher_id = Yii::app()->request->getParam('teacherid');
        $criteria   = new CDbCriteria();
        $criteria->compare('course_id', $course_id);
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('tid', $this->tid);
        $criteria->compare('status', 1);
        $oldModel = TimetableCourseTeacher::model()->find($criteria);
        if ($oldModel) {
            $oldModel->status = 0;
            $oldModel->end_at = time();
            if (!$oldModel->save()) {
                $this->addMessage('state', 'fail');
                $this->showMessage();
            }
            $this->addMessage('state', 'success');
            $this->showMessage();
        }
        $this->addMessage('state', 'fail');
        $this->showMessage();
    }

    // 分配老师动作
    public function actionUpdateTeacher()
    {
        $course_id  = Yii::app()->request->getParam('courseid');
        $teacher_id = Yii::app()->request->getParam('teacherid');
        //获取course_code
        $criteria = new CDbCriteria();
        $criteria->compare('id', $course_id);
        $course_info = TimetableCourses::model()->find($criteria);
        if (empty($course_info)) {
            $this->addMessage('state', 'fail');
            $this->showMessage();
        }
        $course_code = $course_info->course_code;
        $criteria    = new CDbCriteria();
        $criteria->compare('course_id', $course_id);
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('tid', $this->tid);
        $criteria->compare('status', 1);
        $oldModel = TimetableCourseTeacher::model()->find($criteria);
        if ($oldModel) {
            $oldModel->status = 0;
            $oldModel->end_at = time();
            if (!$oldModel->save()) {
                $this->addMessage('state', 'fail');
                $this->showMessage();
            }
        }
        $model              = new TimetableCourseTeacher();
        $model->tid         = $this->tid;
        $model->schoolid    = $this->branchId;
        $model->course_id   = $course_id;
        $model->course_code = $course_code;
        $model->teacher_id  = $teacher_id;
        $model->start_at    = time();
        $model->end_at      = 0;
        $model->type        = 1;
        if (!$model->save()) {
            $this->addMessage('state', 'fail');
            $this->showMessage();
        }
        $this->deleteTeacher($teacher_id, $this->tid);
        $this->addMessage('state', 'success');
        $this->showMessage();
    }

    // 更新课程标题
    public function actionUpdateCourse()
    {
        $title_cn   = Yii::app()->request->getParam('title_cn');
        $title_en   = Yii::app()->request->getParam('title_en');
        $is_all     = Yii::app()->request->getParam('is_all');
        $coursecode = Yii::app()->request->getParam('coursecode');

        if (!$title_cn || !$title_en || !$coursecode) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('attends', '参数错误'));
            $this->showMessage();
        }

        // 更新相同 program id 的课程
        if ($is_all == 1) {
            $program = substr($coursecode, 0, 5);
            $courses = TimetableCourses::model()->findAllByAttributes(array('program' => $program));
            $data    = array();
            foreach ($courses as $course) {
                $course->title_cn   = $title_cn;
                $course->title_en   = $title_en;
                $course->updated_at = time();
                $course->updated_by = $this->staff->uid;
                $course->save();
                $data[] = $course->course_code;
            }
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
            $this->showMessage();
        } else {
            $course             = TimetableCourses::model()->findByAttributes(array('course_code' => $coursecode));
            $course->title_cn   = $title_cn;
            $course->title_en   = $title_en;
            $course->updated_at = time();
            $course->updated_by = $this->staff->uid;
            $course->save();
            $this->addMessage('state', 'success');
            $this->addMessage('data', array($course->course_code));
            $this->showMessage();
        }
    }

    /**
     * 获取所有的中学老师和K12老师
     */

    public function getMiddleSchoolTeachers()
    {
        Yii::import('common.models.classTeacher.*');
        $criteria = new CDbCriteria();
        $criteria->compare('department_id', array(137, 139));
        $criteria->select = 'position_id';
        $depPosLink       = DepPosLink::model()->findAll($criteria);

        $position_id = array();
        foreach ($depPosLink as $v) {
            $position_id[] = $v->position_id;
        }
        $teachers = array();
        if ($position_id) {
            $criteria = new CDbCriteria();
            $criteria->compare('t.level', 1);
            $criteria->compare('profile.occupation_en', $position_id);
            $criteria->index = 'uid';
            $teachers        = User::model()->with('profile', 'staffInfo')->findAll($criteria);
        }
        return $teachers;
    }

    // 获取所有的教学老师
    public function getCampusTeachers()
    {
        $teacherids = OA::getCampusTeachers($this->branchId);

        $crit         = new CDbCriteria();
        $crit->select = "uid";
        $crit->compare('schoolid', $this->branchId);
        $crit->compare('type', 'edu');
        $teachers_edu = AdmBranchLink::model()->findAll($crit);
        $teacher_edu  = array();
        if ($teachers_edu) {
            foreach ($teachers_edu as $_teache) {
                $teacher_edu[] = $_teache->uid;
            }
            $teacherids = array_merge($teacherids, $teacher_edu);
        }

        $teachers = array();
        if ($teacherids) {
            $crit = new CDbCriteria();
            $crit->compare('t.uid', $teacherids);
            $crit->index = 'uid';
            $teachers    = User::model()->with(array('profile', 'staffInfo'))->findAll($crit);
        }

        return $teachers;
    }


    // 学生课表
    public function actionStudent()
    {
        $tid = $this->tid;
        $criteria = new CDbCriteria();
        $criteria->compare('yid', $this->timetable->yid);
        $criteria->compare('classtype', array("e6", "e7", 'e8', 'e9', 'e10', 'e11', 'e12'));
        $criteria->order = 'child_age asc';
        $classModel      = IvyClass::model()->findAll($criteria);

        //  所有中学班级
        $classArr = array();
        foreach ($classModel as $val) {
            $classArr[$val->classid] = $val->title;
//            $classArr[$val->classid] = $val->title_back;
        }
        // 判断当前模式
        $crite = new CDbCriteria;
        $crite->compare('status',1);
        $timetableInfo = Timetable::model()->find($crite);
        $now_tid = $timetableInfo->id;//当前tid
        if($tid<$now_tid){
            $mode = 'old';
        }elseif ($tid == $now_tid){
            $mode = 'current';
        }else{
            $mode = 'next';
        }
        // 所有中学班级里的所有孩子
        $childArr  = array();
        $classData = array();

        // 根据条件（历史，当前，未来）查询班级与孩子
        if ($mode == 'current') {
            $criteria = new CDbCriteria();
            $criteria->compare('classid', array_keys($classArr));
            $criteria->compare('schoolid', $this->branchId);
            $criteria->compare('status', array(ChildProfileBasic::STATS_ACTIVE_WAITING, ChildProfileBasic::STATS_ACTIVE));
            $childModel = ChildProfileBasic::model()->findAll($criteria);

            foreach ($childModel as $val) {
                $childArr[$val->classid][$val->childid] = array(
                    'childId'   => $val->childid,
                    'childName' => $val->getChildName(false, false, true),
                );
            }
        } elseif ($mode == 'next') {
            Yii::import('common.models.invoice.ChildReserve');

            $criteria = new CDbCriteria();
            $criteria->compare('schoolid', $this->branchId);
            $criteria->compare('calendar', $this->timetable->yid);
            $criteria->compare('stat', 20);
            $criteria->index = 'childid';
            $reserves        = ChildReserve::model()->findAll($criteria);
            $childids        = array_keys($reserves);
            $childModel      = ChildProfileBasic::model()->findAllByPk($childids);
            foreach ($childModel as $val) {
                $childid                      = $val->childid;
                $classid                      = $reserves[$childid]->classid;
                $childArr[$classid][$childid] = array(
                    'childId'   => $childid,
                    'childName' => $val->getChildName(false, false, true),
                );
            }
            unset($reserves);
        }
        if ($mode == 'old') {
            $criteria = new CDbCriteria();
            $criteria->compare('schoolid', $this->branchId);
            $criteria->compare('calendar', $this->timetable->yid);
            $criteria->compare('stat', 20);
            $criteria->index = "childid";
            $childModel      = ChildStudyHistory::model()->findAll($criteria);
            $childids        = array_keys($childModel);
            $childModel      = ChildProfileBasic::model()->findAllByPk($childids);
            foreach ($childModel as $val) {
                $childArr[$val->classid][$val->childid] = array(
                    'childId'   => $val->childid,
                    'childName' => $val->getChildName(false, false, true),
                );
            }
        }

        unset($childModel);

        foreach ($classArr as $key => $val) {
            if (isset($childArr[$key])) {
                $classData[] = array(
                    'id'   => $key,
                    'name' => $val
                );
            }
        }

        $criteria = new CDbCriteria();
        $criteria->compare('status', 1);
        $courseModel = TimetableCourses::model()->findAll($criteria);
        $courseArr   = array();
        if ($courseModel) {
            foreach ($courseModel as $val) {
                $courseArr[$val->course_code] = $val->getTitle();
            }
        }

        $this->render('studentSchedule', array(
            'classArr'  => $classData,
            'childArr'  => $childArr,
            'courseArr' => $courseArr,
            'tid'       => $tid,
        ));
    }

    // 课程安排总览
    public function actionArrangementOverview()
    {
        $tid      = Yii::app()->request->getParam('tid', '');
        $tids     = $tid ? $tid : $this->tid;
        $criteria = new CDbCriteria;
        $criteria->compare('status', 1);
        $criteria->index = 'id';
//        var_dump($criteria);
        $courseModel = TimetableCourses::model()->findAll($criteria);

        $courseData = array();
        if ($courseModel) {
            $criteria = new CDbCriteria;
            $criteria->compare('course_id', array_keys($courseModel));
            $criteria->compare('tid', $tids);
            $courseDataModel = TimetableCourseData::model()->findAll($criteria);
            foreach ($courseDataModel as $val) {
                $courseData[] = array(
                    'course_code'    => $val->course_code,
                    'weekday_period' => $val->weekday . '-' . $val->period,
                    'title'          => $courseModel[$val->course_id]->getTitle(),
                );
            }
        }
        $this->render('overview', array(
            'courseData' => $courseData,
            'tid'        => $tids,
        ));
    }

    /**
     * new获取课程安排总览接口
     */
    public function actionSchedulesOverview()
    {
        $tids = Yii::app()->request->getParam('tid', '');
        $yid  = $this->timetable->yid;
        //新版数据
        $res = CommonUtils::requestDsOnline("timetable/schedulesOverview", array(
            'tid'      => $tids,
            'branchId' => $this->branchId,
            'yid'      => $yid,
        ), 'get');
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    /**
     * 添加老师的课程安排
     */
    public function actionAddCourseData()
    {
        $tids            = Yii::app()->request->getParam('tid', '');
        $weekday         = Yii::app()->request->getParam('week', '');
        $period          = Yii::app()->request->getParam('period', '');
        $course_id       = Yii::app()->request->getParam('course_id', '');
        $course_code     = Yii::app()->request->getParam('course_code', '');
        $class_room_name = Yii::app()->request->getParam('class_room_name', '');
        $category        = Yii::app()->request->getParam('category', '');
        //新版数据
        $res = CommonUtils::requestDsOnline("timetable/addCourseData", array(
            'tid'             => $tids,
            'week'            => $weekday,
            'period'          => $period,
            'course_id'       => $course_id,
            'course_code'     => $course_code,
            'class_room_name' => $class_room_name,
            'category'        => $category,
        ), 'get');
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    /**
     * 添加课程
     */
    public function actionAddCourse()
    {
        $code     = Yii::app()->request->getParam('course_code', '');
        $cn_title = Yii::app()->request->getParam('title_cn', '');
        $en_title = Yii::app()->request->getParam('title_en', '');
        $this->addMessage("message", "Data Saving Failed!");
        $this->addMessage('state', 'fail');
        if (empty($code) || empty($cn_title) || empty($en_title)) {
            $this->addMessage("message", "Param Error");
            $this->showMessage();
        }
        $course_code_arr = explode('.', $code);
        if(empty($course_code_arr[1])){
            $this->addMessage("message", "CourseCode Error");
            $this->showMessage();
        }
        if (strpos($code, 'CLUB') === true) {
            $program = 'CLUB';
        } else {
            $lastDotIndex = strrpos($code, ".");
            $program = substr($code, 0, $lastDotIndex);
//            $program = substr($code, 0, 2);
        }
        $criteria = new CDbCriteria();
        $criteria->compare('course_code', $code);
        $TimetableCoursesModel = TimetableCourses::model()->findAll($criteria);
        if (!empty($TimetableCoursesModel)) {
            $this->addMessage("message", Yii::t('attends', 'Data duplication'));
        } else {
            $calendar            = Calendar::model()->findByPk($this->branchObj->schcalendar);
            $course              = new TimetableCourses();
            $course->program     = $program;
            $course->course_code = $code;
            $course->title_cn    = trim($cn_title);
            $course->title_en    = trim($en_title);
            $course->start_year  = $calendar->startyear;
            $course->status      = 1;
            $course->weight      = 0;
            $course->created_at  = time();
            $course->updated_at  = time();
            $course->created_by  = 1;
            $course->updated_by  = 1;
            if ($course->save()) {
                $this->addMessage('state', 'success');
                $this->addMessage("data", '');
            }
        }
        $this->showMessage();
    }

    /**
     * 删除课程总览中的一节课程
     */
    public function actionRemoveCourseData()
    {
        $id   = Yii::app()->request->getParam('id', '');
        $tids = Yii::app()->request->getParam('tid', '');
        //新版数据
        $res = CommonUtils::requestDsOnline("timetable/removeCourseData", array(
            'id'  => $id,
            'tid' => $tids,
        ), 'get');
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    /**
     * 课程总览修改一节课程信息
     */
    public function actionSaveCourseData()
    {
        $id        = Yii::app()->request->getParam('id', '');
        $week      = Yii::app()->request->getParam('week', '');
        $period    = Yii::app()->request->getParam('period', '');
        $course_id = Yii::app()->request->getParam('course_id', '');
//        $course_code = Yii::app()->request->getParam('course_code', '');
        $class_room_name = Yii::app()->request->getParam('class_room_name', '');
        $tids            = Yii::app()->request->getParam('tid', '');
        //新版数据
        $res = CommonUtils::requestDsOnline("timetable/saveCourseData", array(
            'tid'             => $tids,
            'id'              => $id,
            'week'            => $week,
            'period'          => $period,
            'course_id'       => $course_id,
            //            'course_code'=>$course_code,
            'class_room_name' => $class_room_name,
        ), 'get');
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }


    // 根据班级ID 导出班级下所选孩子的课表只有孩子的名字
    public function actionExportChild()
    {
        $childList        = Yii::app()->request->getParam('childList', '');
        $tid              = Yii::app()->request->getParam('tid', '');
        $scheduleTypeList = Yii::app()->request->getParam('scheduleTypeList', '');
        $yid              = Yii::app()->request->getParam('yid', '');
        //新版数据
        $res = CommonUtils::requestDsOnline("timetable/exportStudentSchedule", array(
            'schoolId'         => $this->branchId,
            'tid'              => $tid,
            'yid'              => $yid,
            'childList'        => $childList,
            'scheduleTypeList' => $scheduleTypeList,
        ), 'get');
        switch (Yii::app()->language) {
            case "zh_cn":
                $res['data']['scheduleType'] = '课表：' . implode(' ', $scheduleTypeList);
                break;
            case "en_us":
                $res['data']['scheduleType'] = 'Schedule: ' . implode(' ', $scheduleTypeList);
                break;
        }

        $this->layout = '//layouts/print';
        Yii::app()->clientScript->registerMetaTag('no-cache', '', 'cache-control');
        Yii::app()->clientScript->registerMetaTag('0', '', 'expires');
        Yii::app()->clientScript->registerMetaTag('no-cache', '', 'pragma');
        $this->render('exportChild', $res['data']);
    }

    // 显示课程下的所有孩子
    public function actionShowCourseChild()
    {
        $course_code = Yii::app()->request->getParam('course_code', '');
        $tid         = Yii::app()->request->getParam('tid', '');
        $tids        = $tid ? $tid : $this->tid;
        $data        = array();
        if ($course_code) {
            $criteria = new CDbCriteria();
            $criteria->compare('course_code', $course_code);
            $course = TimetableCourses::model()->find($criteria);

            $criteria = new CDbCriteria();
            $criteria->compare('course_code', $course_code);
            $criteria->compare('tid', $tids);
            $criteria->compare('status', 1);
            $studentData = TimetableStudentData::model()->findAll($criteria);

            $childArr = array();
            if ($studentData) {
                foreach ($studentData as $val) {
                    $childArr[] = isset($val->childProfile) ? $val->childProfile->getChildName(false, false, true) : $val->child_id;
                }
            }

            $data = array(
                'title'    => $course->getTitle(),
                'count'    => isset($childArr) ? count($childArr) : 0,
                'childArr' => $childArr,
            );
        }
        echo json_encode($data);
    }

    //通过 coursecode 获取上课的老师和学生list
    public function actionCourseChild()
    {
        $courseCode = Yii::app()->request->getParam('courseCode', '');
        $tid        = Yii::app()->request->getParam('tid', '');
        $weekday    = Yii::app()->request->getParam('weekday', '');
        $period     = Yii::app()->request->getParam('period', '');
        $tids       = $tid ? $tid : $this->tid;
        //新版数据
        $res = CommonUtils::requestDsOnline("timetable/courseChild", array(
            'tid'        => $tids,
            'courseCode' => $courseCode,
            'weekday'    => $weekday,
            'period'     => $period,
        ), 'get');
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();

    }

    //通过会议code获取会议相关信息
    public function actionMeetTeacher()
    {
        $courseCode = Yii::app()->request->getParam('courseCode', '');
        $tid        = Yii::app()->request->getParam('tid', '');
        $tid        = $tid ? $tid : $this->tid;
        //新版数据
        $res = CommonUtils::requestDsOnline('attendance/meetTeacher', array(
            'tid'         => $tid,
            'school_id'   => $this->branchId,
            'course_code' => $courseCode,
        ), 'get');
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    //  根据孩子ID 获取孩子的课程
    public function actionChildCourse()
    {
        $childid = Yii::app()->request->getParam('childid', '');
        $tids    = Yii::app()->request->getParam('tid', '');
        // 获取孩子所有的缓存
        $criteria = new CDbCriteria;
        $criteria->compare('type', 10);
        $criteria->compare('target_id', $childid);
        $criteria->compare('tid', $tids);
        $criteria->index     = 'term';
        $scheduleCacheModels = TimetableWeeklyScheduleCache::model()->findAll($criteria);
        if (!$scheduleCacheModels) {
            $this->childCache($childid, $tids, 0);
            $scheduleCacheModels = TimetableWeeklyScheduleCache::model()->findAll($criteria);
        }

        $courseData = array();
        foreach ($scheduleCacheModels as $term => $scheduleCacheModel) {
            $schedule = json_decode($scheduleCacheModel->data);
            foreach (json_decode($scheduleCacheModel->data) as $key => $value) {
                $courseData[$key][$term] = explode(',', $value);
            }
        }

        foreach ($courseData as $key => $item) {
            if (isset($item[0]) && isset($item[1]) && isset($item[2])) {
                if ($item[1] == $item[2]) {
                    unset($courseData[$key][1]);
                    unset($courseData[$key][2]);
                } else {
                    unset($courseData[$key][0]);
                }
            } elseif (isset($item[0]) && isset($item[1])) {
                if ($item[0] == $item[1]) {
                    unset($courseData[$key][1]);
                }
            } elseif (isset($item[0]) && isset($item[2])) {
                if ($item[0] == $item[2]) {
                    unset($courseData[$key][2]);
                }
            }
        }

        $data = array(
            'childid' => $childid,
            'data'    => $courseData,
        );

        echo json_encode($data);

    }

    /**
     * 新版本的获取学生的课表
     */
    public function actionStudentInfo()
    {
        $studentId  = Yii::app()->request->getParam('studentId', '');
        $tids       = Yii::app()->request->getParam('tid', '');
        $weekNumber = Yii::app()->request->getParam('weeknumber', '');
        //新版数据
        $res = CommonUtils::requestDsOnline("timetable/studentInfo", array(
            'branchId'   => $this->branchId,
            'tid'        => $tids,
            'studentId'  => $studentId,
            'weeknumber' => $weekNumber
        ), 'get');
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    //获取学生课表的连接二维码
    public function actionGetCourseQRCode()
    {
        $child_id = Yii::app()->request->getParam('child_id', 0);
        $res      = CommonUtils::requestDsOnline("child/getCourseQRCode", array(
            'school_id' => $this->branchId,
            'child_id'  => $child_id,
        ));
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    // 显示增加孩子课程列表
    public function actionUpdateChildCourseList()
    {
        $weekday    = Yii::app()->request->getParam('weekday', '');
        $period     = Yii::app()->request->getParam('period', '');
        $category   = Yii::app()->request->getParam('category', '');
        $semseterId = Yii::app()->request->getParam('semseterId', '');
        $tid        = $this->tid;
//        $courseArrangement = array();
        //新版数据
        $res = CommonUtils::requestDsOnline("timetable/showStudentAddCourseData", array(
            'tid'      => $tid,
            'weekday'  => $weekday,
            'category' => $category,
            'period'   => $period
        ), 'get');
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
        ////
//        if($weekday && $period){
//            $criteria = new CDbCriteria();
//            $criteria->compare('weekday', $weekday);
//            $criteria->compare('period', $period);
//            $criteria->compare('tid', $tid);
//            $criteria->compare('status', 1);
//            $criteria->index = 'course_id';
//            $courseDataModel = TimetableCourseData::model()->findAll($criteria);
//            if($courseDataModel){
//                $criteria = new CDbCriteria();
//                $criteria->compare('id', array_keys($courseDataModel));
//                $criteria->compare('status', 1);
//                $courseModel = TimetableCourses::model()->findAll($criteria);
//            }
//
//            foreach ($courseModel as $val) {
//                $data = array();
//                if(isset($val->courseData)){
//                    foreach ($val->courseData as $item) {
//                        $data[$item->course_id][] = array(
//                            'weekday' => $item->weekday,
//                            'period' => $item->period,
//                        );
//                    }
//                }
//
//                $courseArrangement[] = array(
//                    'title' => $val->getTitle(),
//                    'course_code' => $val->course_code,
//                    'data' => isset($data[$val->id]) ? $data[$val->id] : $data ,
//                );
//            }
//        }
//        echo json_encode($courseArrangement);
    }


    // 给孩子增加课程
    public function actionUpdateChildCourse()
    {
        $course_id = Yii::app()->request->getParam('course_id', '');
        $childid   = Yii::app()->request->getParam('student_id', '');
        $tid       = Yii::app()->request->getParam('tid', '');
        $category  = Yii::app()->request->getParam('category', 0);
        //新版数据
        $res = CommonUtils::requestDsOnline("timetable/addStudentCourse", array(
            'tid'        => $tid,
            'category'   => $category,
            'course_id'  => $course_id,
            'student_id' => $childid
        ), 'get');
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();

        ////
//        Yii::import('common.models.invoice.*');
//        if($course_code && $childid){
//            $childModel = ChildProfileBasic::model()->findByPk($childid);
//
//            $criteria = new CDbCriteria();
//            $criteria->compare('course_code', $course_code);
//            $courseModel = TimetableCourses::model()->find($criteria);
//
//            $date = "";
//            if(isset($courseModel->courseData)){
//                $date = $courseModel->courseData;
//            }
//            $criteria = new CDbCriteria();
//            $criteria->compare('course_code', $course_code);
//            $criteria->compare('child_id', $childid);
//            $criteria->compare('status', 1);
//            $criteria->compare('term', $semesterId);
//            if (!$model = TimetableStudentData::model()->find($criteria)) {
//                $model = new TimetableStudentData();
//                $model->tid = $tid;
//                $model->class_id = $childModel->classid;
//                $model->child_id = $childid;
//                $model->course_id = $courseModel->id;
//                $model->course_code = $courseModel->course_code;
//                $model->class_room = isset($date) ? $date[0]->class_room : "" ;
//                $model->class_room_name =  isset($date) ? $date[0]->class_room_name : "" ;
//                $model->start_at = time();
//                $model->status = 1;
//                $model->term = $semesterId;
//                $model->created_at = time();
//                $model->created_by = Yii::app()->user->id;
//            }
//
//            $model->updated_at = time();
//            $model->updated_by = Yii::app()->user->id;
//            if(!$model->save()){
//                $error = $model->getErrors();
//                echo $error[0];
//            }
//            $criteria = new CDbCriteria;
//            $criteria->compare('target_id', $childid);
//            $criteria->compare('tid', $tid);
//            $scheduleCache = TimetableWeeklyScheduleCache::model()->deleteAll($criteria);
//        }
//       echo $childid;
    }

    // 删除孩子课程 分为使用课程id和使用课程code
    public function actionDeleteChildCourse()
    {
        $childid     = Yii::app()->request->getParam('childid', '');
        $course_code = Yii::app()->request->getParam('course_code', '');
        $id          = Yii::app()->request->getParam('id', '');
        $tid         = Yii::app()->request->getParam('tid', '');
        if ($childid && $id) {
            //通过id删除学生的课表 无缓存
            $criteria = new CDbCriteria;
            $criteria->compare('child_id', $childid);
            $criteria->compare('id', $id);
            $criteria->compare('tid', $tid);
            $criteria->compare('status', 1);
            $studentDataModel = TimetableStudentData::model()->deleteAll($criteria);
            if ($studentDataModel) {
                $this->addMessage("state", "success");
                $this->addMessage("data", array($childid, $id));
            } else {
                $this->addMessage("state", "fail");
                $this->addMessage("message", 'DeleteChildCourse fail');
            }
            $this->showMessage();
        } elseif ($childid && $course_code) {
            //通过 $course_code 删除mysql数据和缓存
            $codeFormat = "%0" . strlen($course_code) . "d";
            $code       = sprintf($codeFormat, $course_code);
            $criteria   = new CDbCriteria;
            $criteria->compare('child_id', $childid);
            $criteria->compare('course_code', $code);
            $criteria->compare('tid', $tid);
            $criteria->compare('status', 1);
            $studentDataModel = TimetableStudentData::model()->deleteAll($criteria);

            if ($studentDataModel) {
                // $studentDataModel->updated_at = time();
                // $studentDataModel->updated_by = $this->staff->uid;
                // $studentDataModel->end_at = time();
                // $studentDataModel->status = 0;
                // $studentDataModel->save();
                // $studentDataModel->delete();
                $criteria = new CDbCriteria;
                $criteria->compare('target_id', $childid);
                $criteria->compare('tid', $tid);
                $scheduleCache = TimetableWeeklyScheduleCache::model()->deleteAll($criteria);
            }
        }
        echo $childid;
    }

    //新版的删除学生课表中的课程
    public function actionRemoveStudentCourse()
    {
        $id = Yii::app()->request->getParam('id', '');
        //新版数据
        $res = CommonUtils::requestDsOnline("timetable/removeStudentCourse", array(
            'id' => $id,
        ), 'get');
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    // 增加孩子缓存
    public function childCache($child_id, $tid, $semesterId)
    {
        $criteria = new CDbCriteria;
        $criteria->compare('child_id', $child_id);
        $criteria->compare('status', 1);
        $criteria->compare('tid', $tid);
        $criteria->compare('status', 1);
        $criteria->order = "updated_at DESC";
        $studentModel    = TimetableStudentData::model()->findAll($criteria);
        $all             = array();
        $last            = array();
        $next            = array();
        $cackeModel      = array();
        if ($studentModel) {
            foreach ($studentModel as $value) {
                if ($value->term == 0) {
                    $all[] = $value->course_id;
                }
                if ($value->term == 1) {
                    $last[] = $value->course_id;
                }
                if ($value->term == 2) {
                    $next[] = $value->course_id;
                }

            }

            // 全部
            $courseModel = array();
            if ($all) {
                $criteria = new CDbCriteria;
                $criteria->compare('course_id', $all);
                $courseModel = TimetableCourseData::model()->findAll($criteria);
            }
            $courseDataAll = array();
            if ($courseModel) {
                foreach ($courseModel as $item) {
                    $courseDataAll[$item->weekday . '-' . $item->period][] = $item->course_code;
                }
            }

            $courseData2All = array();
            if ($courseDataAll) {
                foreach ($courseDataAll as $k => $value) {
                    $courseData2All[$k] = implode(',', $value);
                }
            }


            // 上学期
            $courseModelList = array();
            if ($last) {
                $criteria = new CDbCriteria;
                $criteria->compare('course_id', $last);
                $courseModelList = TimetableCourseData::model()->findAll($criteria);
            }

            $courseDatabAist = array();
            if ($courseModelList) {
                foreach ($courseModelList as $item) {
                    $courseDatabAist[$item->weekday . '-' . $item->period][] = $item->course_code;
                }
            }

            if ($courseDatabAist) {
                $courseDataLists = array();
                if ($courseDataAll) {
                    $courseDataLists = $courseDatabAist + $courseDataAll;
                } else {
                    $courseDataLists = $courseDatabAist;
                }

                foreach ($courseDataLists as $k => $value) {
                    $courseData2List[$k] = implode(',', $value);
                }
            }
            //下学期
            $courseModelC = array();
            if ($next) {
                $criteria = new CDbCriteria;
                $criteria->compare('course_id', $next);
                $courseModelC = TimetableCourseData::model()->findAll($criteria);
            }

            $courseDataNext = array();
            if ($courseModelC) {
                foreach ($courseModelC as $item) {
                    $courseDataNext[$item->weekday . '-' . $item->period][] = $item->course_code;
                }
            }

            $courseData2Next = array();
            if ($courseDataNext) {
                $courseDataNexts = array();
                if ($courseDataAll) {
                    $courseDataLists = $courseDataNext + $courseDataAll;
                } else {
                    $courseDataLists = $courseDataNext;
                }
                foreach ($courseDataLists as $k => $value) {
                    $courseData2Next[$k] = implode(',', $value);
                }
            }
            if ($courseData2All) {
                $cackeModel1             = new TimetableWeeklyScheduleCache();
                $cackeModel1->tid        = $tid;
                $cackeModel1->type       = 10;
                $cackeModel1->term       = 0;
                $cackeModel1->target_id  = $child_id;
                $cackeModel1->data       = json_encode($courseData2All);
                $cackeModel1->updated_at = time();
                $cackeModel1->save();
            }
            if ($courseData2List) {
                $cackeModel2             = new TimetableWeeklyScheduleCache();
                $cackeModel2->tid        = $tid;
                $cackeModel2->type       = 10;
                $cackeModel2->term       = 1;
                $cackeModel2->target_id  = $child_id;
                $cackeModel2->data       = json_encode($courseData2List);
                $cackeModel2->updated_at = time();
                $cackeModel2->save();
            }
            if ($courseData2Next) {
                $cackeModel3             = new TimetableWeeklyScheduleCache();
                $cackeModel3->tid        = $tid;
                $cackeModel3->type       = 10;
                $cackeModel3->term       = 2;
                $cackeModel3->target_id  = $child_id;
                $cackeModel3->data       = json_encode($courseData2Next);
                $cackeModel3->updated_at = time();
                $cackeModel3->save();
            }
        }

        if ($semesterId == 1) {
            $data = ($cackeModel2) ? $cackeModel2 : (($cackeModel1) ? $cackeModel1 : "");
        }
        if ($semesterId == 2) {
            $data = ($cackeModel3) ? $cackeModel3 : (($cackeModel1) ? $cackeModel1 : "");
        }
        if ($semesterId == 0) {
            $data = ($cackeModel1) ? $cackeModel1 : "";
        }

        return $data;
    }

    // 增加老师缓存
    public function cache($uid = "", $tid, $semesterId)
    {
        $scheduleCacheModel = array();
        if ($uid) {
            $criteria = new CDbCriteria();
            $criteria->compare('teacher_id', $uid);
            $criteria->compare('tid', $tid);
            $criteria->compare('status', 1);
            $couorseTeacherModel = TimetableCourseTeacher::model()->findAll($criteria);

            $all          = array();
            $lastSemester = array();
            $nextSemester = array();
            if ($couorseTeacherModel) {
                foreach ($couorseTeacherModel as $value) {
                    if ($value->term == 0) {
                        $all[] = $value->course_id;
                    }
                    if ($value->term == 1) {
                        $lastSemester[] = $value->course_id;
                    }
                    if ($value->term == 2) {
                        $nextSemester[] = $value->course_id;
                    }

                }

                // 全部的
                if ($all) {
                    $criteria = new CDbCriteria;
                    $criteria->compare('course_id', $all);
                    $courseAllModel = TimetableCourseData::model()->findAll($criteria);
                }

                $courseDataAll = array();
                if ($courseAllModel) {
                    foreach ($courseAllModel as $item) {
                        $courseDataAll[$item->weekday . '-' . $item->period][] = $item->course_code;
                    }
                }
                $courseDataAll2 = array();
                if ($courseDataAll) {
                    foreach ($courseDataAll as $k => $value) {
                        $courseDataAll2[$k] = implode(',', $value);
                    }
                }


                // 上学期
                if ($lastSemester) {
                    $criteria = new CDbCriteria;
                    $criteria->compare('course_id', $lastSemester);
                    $courseLastModel = TimetableCourseData::model()->findAll($criteria);
                }

                $courseDataLast = array();
                if ($courseLastModel) {
                    foreach ($courseLastModel as $item) {
                        $courseDataLast[$item->weekday . '-' . $item->period][] = $item->course_code;
                    }
                }

                $courseDataLast2 = array();
                if ($courseDataLast) {
                    $courseLast = array();

                    if ($courseDataAll) {
                        $courseLast = $courseDataLast + $courseDataAll;
                    } else {
                        $courseLast = $courseDataLast;
                    }

                    foreach ($courseLast as $k => $value) {
                        $courseDataLast2[$k] = implode(',', $value);
                    }
                }


                // 下学期
                if ($nextSemester) {
                    $criteria = new CDbCriteria;
                    $criteria->compare('course_id', $nextSemester);
                    $courseNextModel = TimetableCourseData::model()->findAll($criteria);
                }

                $courseDataNext = array();
                if ($courseNextModel) {
                    foreach ($courseNextModel as $item) {
                        $courseDataNext[$item->weekday . '-' . $item->period][] = $item->course_code;
                    }
                }
                $courseDataNext2 = array();
                if ($courseDataNext) {
                    $courseNext = array();
                    if ($courseDataAll) {
                        $courseNext = $courseDataNext + $courseDataAll;
                    } else {
                        $courseNext = $courseDataNext;
                    }
                    foreach ($courseNext as $k => $value) {
                        $courseDataNext2[$k] = implode(',', $value);
                    }
                }

                if ($courseDataAll2) {
                    $scheduleCacheModel             = new TimetableWeeklyScheduleCache();
                    $scheduleCacheModel->tid        = $tid;
                    $scheduleCacheModel->type       = 20;
                    $scheduleCacheModel->term       = 0;
                    $scheduleCacheModel->target_id  = $uid;
                    $scheduleCacheModel->data       = json_encode($courseDataAll2);
                    $scheduleCacheModel->updated_at = time();
                    $scheduleCacheModel->save();
                }

                if ($courseDataLast2) {
                    $scheduleCacheModelLast             = new TimetableWeeklyScheduleCache();
                    $scheduleCacheModelLast->tid        = $tid;
                    $scheduleCacheModelLast->type       = 20;
                    $scheduleCacheModelLast->term       = 1;
                    $scheduleCacheModelLast->target_id  = $uid;
                    $scheduleCacheModelLast->data       = json_encode($courseDataLast2);
                    $scheduleCacheModelLast->updated_at = time();
                    $scheduleCacheModelLast->save();
                }

                if ($courseDataNext2) {
                    $scheduleCacheModelNext             = new TimetableWeeklyScheduleCache();
                    $scheduleCacheModelNext->tid        = $tid;
                    $scheduleCacheModelNext->type       = 20;
                    $scheduleCacheModelNext->term       = 2;
                    $scheduleCacheModelNext->target_id  = $uid;
                    $scheduleCacheModelNext->data       = json_encode($courseDataNext2);
                    $scheduleCacheModelNext->updated_at = time();
                    $scheduleCacheModelNext->save();
                }
            }
        }

        if ($semesterId == 1) {
            $data = ($scheduleCacheModelLast) ? $scheduleCacheModelLast : (($scheduleCacheModel) ? $scheduleCacheModel : "");
        }
        if ($semesterId == 2) {
            $data = ($scheduleCacheModelNext) ? $scheduleCacheModelNext : (($scheduleCacheModel) ? $scheduleCacheModel : "");
        }
        if ($semesterId == 0) {
            $data = ($scheduleCacheModel) ? $scheduleCacheModel : "";
        }

        return $data;
    }

    // 切换老师
    public function actionTeacher()
    {
        $tid = Yii::app()->request->getParam('tid', '');

        $criteria = new CDbCriteria;
        $criteria->compare('tid', $tid);
        $criteria->compare('status', 1);
        $criteria->compare('schoolid', $this->branchId);
        $criteria->index = 'teacher_id';
        $teacherModel    = TimetableCourseTeacher::model()->findAll($criteria);

        $teacherList = array();
        if ($teacherModel) {
            $criteria = new CDbCriteria;
            $criteria->compare('uid', array_keys($teacherModel));
            $criteria->index = 'uid';
            $userModel       = User::model()->findAll($criteria);
            foreach ($teacherModel as $val) {
                $teacherList[$val->teacher_id] = isset($userModel[$val->teacher_id]) ? $userModel[$val->teacher_id]->getName() : $val->teacher_id;
            }
        }
        echo json_encode($teacherList);
    }

    public function deleteTeacher($uid, $tid)
    {
        $criteria = new CDbCriteria;
        $criteria->compare('tid', $tid);
        $criteria->compare('type', 20);
        $criteria->compare('target_id', $uid);
        $scheduleCache = TimetableWeeklyScheduleCache::model()->deleteAll($criteria);
        return true;
    }

    // 学生课程安排总览
    public function actionStudentRecordsOverview()
    {
        $tid  = Yii::app()->request->getParam('tid', '');
        $tids = $tid ? $tid : $this->tid;
        $res  = CommonUtils::requestDsOnline('attendance/studentRecordsOverview', array(
            'tid'       => $tids,
            'school_id' => $this->branchId,
            'yid'       => $this->timetable->yid,
        ));
        $this->render('recordsoverview', array(
            'classArr'          => $res['data']['classArr'],
            'childArr'          => $res['data']['childArr'],
            'courseArr'         => $res['data']['courseArr'],
            'continuousAbsence' => $res['data']['continuousAbsence'],
            'tid'               => $tids,
        ));
    }

    //老接口 根据孩子ID 获取孩子的签到记录
    public function actionChildRecords()
    {
        $weekTime = Yii::app()->request->getParam('weekTime', '');
        $childid  = Yii::app()->request->getParam('childid', '');
        $tids     = Yii::app()->request->getParam('tid', '');

        $startDate = strtotime('last sunday +1 day', strtotime($weekTime));
        $endDate   = strtotime(' +4 day', $startDate);

        // 查找孩子当前周签到记录
        $criteria = new CDbCriteria();
        $criteria->compare('target_date', '>=' . $startDate);
        $criteria->compare('target_date', '<=' . $endDate);
        $criteria->compare('childid', $childid);
        $criteria->compare('status', 10);
        $criteria->compare('tid', $tids);
        $childRecords = TimetableRecords::model()->findAll($criteria);

        $records = array();
        foreach ($childRecords as $key => $val) {
            $records[$val->weekday . '-' . $val->period][$val->course_code] = $val->type;
            if ($val->type == TimetableRecords::LATE_STATUS) {
                $records[$val->weekday . '-' . $val->period][$val->course_code] = $val->type . $val->late_time;
            }
        }

        // 根据时间判断是第一学期还是第二学期
        $semesterId = $this->getSemesterIndex($this->timetable->yid, $startDate);
        $childCache = $this->getScheduleCache(10, $childid, $tids, $semesterId);
        if (!$childCache) {
            $childCache = $this->childCache($childid, $tids, $semesterId);
        }

        $courseData = array();
        foreach (json_decode($childCache->data) as $key => $val) {
            $courseData[$key] = explode(",", $val);

        }

        $data = array(
            'childid' => $childCache->target_id,
            'data'    => array(
                'items'      => $courseData,
                'records'    => $records,
                'date_title' => date('Y-m-d', $startDate) . '-' . date('Y-m-d', $endDate),
            ),
        );

        echo json_encode($data);
    }

    /**
     * 新接口 根据孩子ID 获取学生的签到总览情况 需要结合学生课表接口使用
     */
    public function actionStudentAttendanceOverview()
    {
        $weekMon   = Yii::app()->request->getParam('weekMon', '');
        $studentId = Yii::app()->request->getParam('studentId', '');
        $tids      = Yii::app()->request->getParam('tid', '');
        //新版数据
        $res = CommonUtils::requestDsOnline("timetable/studentAttendanceOverview", array(
            'branchId'  => $this->branchId,
            'tid'       => $tids,
            'studentId' => $studentId,
            'weekMon'   => $weekMon
        ), 'get');
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();

    }

    // 管理员单个更新孩子的签到
    public function actionUpdateRecordsOne()
    {
        $weekday     = Yii::app()->request->getParam('weekday', ''); // 签到周几
        $period      = Yii::app()->request->getParam('period', ''); // 第几节课
        $childid     = Yii::app()->request->getParam('childid', ''); // 孩子id
        $datatime    = Yii::app()->request->getParam('datatime', ''); // 签到时间(课程对应的时间)
        $types       = Yii::app()->request->getParam('type', ''); // 签到类型迟到类型是205 2010等类型需要切割
        $course_code = Yii::app()->request->getParam('course_code', ''); // 课程code
        $type        = substr($types, 0, 2);
        $latetime    = substr($types, 2);// 迟到时间

        $startDate = strtotime('last sunday +1 day', strtotime($datatime));

        $targetDate = $startDate + ($weekday - 1) * 86400;

        if (in_array($type, array(9, 10, 11, 20, 30, 31, 40, 41, 42), false) && $targetDate && $childid && $course_code) {
            $criteria = new CDbCriteria;
            $criteria->compare('course_code', $course_code);
            $criteria->compare('child_id', $childid);
            $criteria->compare('tid', $this->tid);
            $criteria->compare('status', 1);
            $criteria->index = 'child_id';
            $childidModel    = TimetableStudentData::model()->findAll($criteria);

            $courseTeacher = TimetableCourseTeacher::model()->findByAttributes(array('course_id' => current($childidModel)->course_id, 'status' => 1));
            $teacher_id    = $courseTeacher ? $courseTeacher->teacher_id : Yii::app()->user->id;

            $criteria = new CDbCriteria();
            $criteria->compare('tid', $this->tid);
            $criteria->compare('childid', $childid);
            $criteria->compare('school_id', $this->branchId);
            $criteria->compare('course_code', $course_code);
            $criteria->compare('target_date', $targetDate);
            $criteria->compare('weekday', $weekday);
            $criteria->compare('period', $period);
            $criteria->compare('status', '<>' . 99);
            $model = TimetableRecords::model()->find($criteria);

            $childModel = ChildProfileBasic::model()->findByPk($childid);

            if ($model && $type == 9) {
                $model->status     = 99;
                $model->type       = 9;
                $model->updated_at = time();
                $model->updated_by = Yii::app()->user->id;
                $model->save();
                if ($period == 1) {
                    TimetableRecords::childSignDel($childid, $this->branchId,$targetDate,$this->staff->uid);
                }
                TimetableRecords::updateStudentCache($course_code, $weekday, $period, $targetDate, $this->tid, $model->class_room);
                $this->addMessage('state', 'success');
                $this->showMessage();
            }
            if (!$model) {
                $model             = new TimetableRecords();
                $model->tid        = $this->tid;
                $model->childid    = $childid;
                $model->school_id  = $this->branchId;
                $model->status     = 10;
                $model->created_at = time();
                $model->created_by = Yii::app()->user->id;
            }

            $model->course_code = $course_code;
            $model->class_room  = $childidModel[$childid]->class_room;
            $model->class_id    = $childModel->classid;
            $model->weekday     = $weekday;
            $model->period      = $period;
            $model->is_admin    = 1;
            $model->teacher_id  = $teacher_id;
            $model->target_date = $targetDate;
            $model->late_time   = ($type == TimetableRecords::LATE_STATUS) ? $latetime : "";
            $model->type        = $type;
            $model->updated_at  = time();
            $model->updated_by  = Yii::app()->user->id;
            if (!$model->save()) {
                $errors = current($model->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', $errors[0]);
                $this->showMessage();
            }

            if ($period == 1) {
                TimetableRecords::childSignDel($childid, $this->branchId,$targetDate,$this->staff->uid);
                $typeDate = $type;
                if (in_array($type, array(205, 2010, 2015, 2020))) {
                    $typeDate = 20;
                }
                if (in_array($typeDate, array(10, 11, 20, 30, 31, 40, 41, 42))) {
                    TimetableRecords::childSign($childModel, $targetDate, $typeDate, $latetime);
                }
            }

            TimetableRecords::updateStudentCache($course_code, $weekday, $period, $targetDate, $this->tid, $model->class_room);

            $this->addMessage('state', 'success');
            $this->showMessage();
        }

        $this->addMessage('state', 'fail');
        $this->addMessage('message', '参数错误');
        $this->showMessage();
    }

    // 管理员批量更新孩子某天全部课程的签到
    public function actionUpdateRecordsAll()
    {
        $weekday    = Yii::app()->request->getParam('weekday', ''); // 签到周几 1
        $childid    = Yii::app()->request->getParam('childid', ''); // 孩子id
        $datatime   = Yii::app()->request->getParam('datatime', ''); // 签到时间
        $types      = Yii::app()->request->getParam('type', ''); // 签到类型 205迟到5分钟2010迟到10分钟依次类推 10签到 30请假 40旷课
        $type       = substr($types, 0, 2);
        $latetime   = substr($types, 2);
        $startDate = strtotime('last sunday +1 day', strtotime($datatime));#周一时间
        $targetDate = $startDate + ($weekday - 1) * 86400;#上课时间
        //新版数据
        $res = CommonUtils::requestDsOnline("timetable/updateRecordsAll", array(
            'tid'        => $this->tid,
            'branchId'   => $this->branchId,
            'uid'        => $this->staff->uid,
            'yid'        => $this->timetable->yid,
            'weekday'    => $weekday,
            'studentId'  => $childid,
            'mondayTime' => $startDate,
            'targetTime' => $targetDate,
            'type'       => $types,
        ), 'get');
        if ($res['code'] == 0) {
            TimetableRecords::childSignDel($childid,$this->branchId, $targetDate,$this->staff->uid);
            $memo = '';
            if (in_array($type, array(10, 11, 20, 30, 31,40, 41, 42), false)) {
                $memo = '管理员批量操作';
            }
            $childModel = ChildProfileBasic::model()->findByPk($childid);
            TimetableRecords::childSign($childModel, $targetDate, $type, $latetime, $memo);
            $this->addMessage("state", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    /**
     * 考勤数据 直接返回到页面
     * @param string $t
     */
    public function actionReport($t = 'r01')
    {
        //教师考勤提交报表
        if ($t == 'r01') {

        }
        //每日课堂考勤报表
        if ($t == 'r02') {


        }
        //年级日考勤报表 以每日第二节课考勤结果统计
        if ($t == 'r03') {

        }
        //年级考勤统计
        if ($t == 'r04') {
            $targetDate = Yii::app()->request->getParam('targetDate', date('Y-m-d'));
            $targetDate = strtotime($targetDate);
            $classid    = Yii::app()->request->getParam('class_id');
            //  所有中学班级
            $classModel = $this->getCurrentClass();
            $criteria   = new CDbCriteria();
            $criteria->compare('tid', $this->tid);
            $criteria->compare('status', TimetableRecords::ATTENDANCE_STATUS);
            $criteria->select = 'class_id, COUNT(*) as typeNum';
            $criteria->group  = 'class_id';
            $criteria->index  = 'class_id';
            $records          = TimetableRecords::model()->findAll($criteria);

            $classArr = array();
            $classArr2 = array();
            foreach ($classModel as $val) {
                if (isset($records[$val->classid])) {
                    $classArr[$val->classid] = $val->title;
                    $classArr2[] = array(
                        'classid'=>$val->classid,
                        'name'=>$val->title,
                        'child_age'=>$val->child_age,
                    );
//                    $classArr[$val->classid] = $val->title_back;
                }
            }

            unset($classModel);
            $numberTypes = array(
                TimetableRecords::ATTENDANCE_STATUS => Yii::t('attends', 'Present'),
                TimetableRecords::LATE_STATUS       => Yii::t('attends', 'Tardy'),
                TimetableRecords::LEAVE_STATUS      => Yii::t('attends', 'Personal Leave'),
                TimetableRecords::SICK_LEAVE_STATUS => Yii::t('attends', 'Sick Leave'),
                TimetableRecords::ABSENTEE_STATUS   => Yii::t('attends', 'Absent'),
                TimetableRecords::ABSENTEE_STATUS2  => Yii::t('attends', 'Internal Suspension'),
                TimetableRecords::ABSENTEE_STATUS3  => Yii::t('attends', 'External Suspension'),
                50                                  => Yii::t('attends', 'Uniform Infraction'),
            );
            $data        = array(
                'targetDate'  => $targetDate,
                'numberTypes' => $numberTypes,
                'classArr'    => $classArr,
                'classArr2'    => $classArr2,
                'classid'     => $classid,
                'items'       => array(),
                'child'       => array(),
            );
        }
        //学生考勤统计
        if ($t == 'r05') {
            $classid = Yii::app()->request->getParam('class_id');

            //  所有中学班级
            $criteria = new CDbCriteria();
            $criteria->compare('tid', $this->tid);
            $criteria->compare('status', TimetableRecords::ATTENDANCE_STATUS);
            $criteria->select = 'childid,class_id, COUNT(*) as typeNum';
            $criteria->group  = 'childid,class_id';
            $criteria->index  = 'childid';
            $records          = TimetableRecords::model()->findAll($criteria);

            $classids = array();
            $childids = array_keys($records);

            foreach ($records as $childid => $record) {
                $classids[] = $record->class_id;
            }
            //  所有中学班级
            $classModel = $this->getCurrentClass();

            $classArr = array();
            $classArr2 = array();
            foreach ($classids as $classid) {
                $classArr[$classid] = $classModel[$classid]->title;
                $classArr2[$classid] = array(
                    'classid'=>$classid,
                    'name'=>$classModel[$classid]->title,
                    'child_age'=>$classModel[$classid]->child_age,
                );
//                $classArr[$classid] = $classModel[$classid]->title_back;
            }
            $classArr2 = array_values($classArr2);
            asort($classArr);
            unset($classModel);

            $childModel = ChildProfileBasic::model()->findAllByPk($childids);

            // 所有中学班级里的所有孩子
            $childArr = array();
            foreach ($childModel as $val) {
//                $childArr[$records[$val->childid]->class_id][] = array(
//                    'childId'   => $val->childid,
//                    'childName' => $val->getChildName(false, false, true),
//                );
                $childArr[$val->classid][] = array(
                    'childId'   => $val->childid,
                    'childName' => $val->getChildName(false, false, true),
                );
            }
            unset($childModel);
            unset($records);
            $data = array(

                'classArr' => $classArr,
                'childArr' => $childArr,
                'classArr2' => $classArr2,
            );
        }
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/xlsx.full.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/FileSaver.js');
        //标签字体需要的文件
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/label/iconfont.css?v=20231124');
        $this->render('report', array('t' => $t, 'data' => $data));
    }

    /**
     * 返回教师考勤提交报表数据
     */
    public function actionReportR01()
    {
        $tids       = Yii::app()->request->getParam('tid', '');
        $targetDate = Yii::app()->request->getParam('targetDate', '');
        //新版数据
        $res = CommonUtils::requestDsOnline("timetable/reportR01", array(
            'tid'        => $tids,
            'branchId'   => $this->branchId,
            'yid'        => $this->timetable->yid,
            'targetDate' => $targetDate,
        ), 'get');
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    /**
     * 每日课堂考勤报表
     */
    public function actionReportR02()
    {

        $tids       = Yii::app()->request->getParam('tid', '');
        $targetDate = Yii::app()->request->getParam('targetDate', '');
        //新版数据
        $res = CommonUtils::requestDsOnline("timetable/reportR02", array(
            'tid'        => $tids,
            'branchId'   => $this->branchId,
            'yid'        => $this->timetable->yid,
            'targetDate' => $targetDate,
        ), 'get');
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    /***
     * 年级日考勤报表 以每日第二节课考勤结果统计
     */
    public function actionReportR03()
    {
        $tids       = Yii::app()->request->getParam('tid', '');
        $targetDate = Yii::app()->request->getParam('targetDate', '');
        //新版数据
        $res = CommonUtils::requestDsOnline("timetable/reportR03", array(
            'tid'        => $tids,
            'yid'        => $this->timetable->yid,
            'targetDate' => $targetDate,
        ), 'get');
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    /**
     * 年级考勤统计数据接口
     */
    public function actionReportR04()
    {
        $tids    = Yii::app()->request->getParam('tid', '');
        $classid = Yii::app()->request->getParam('class_id');
        //新版数据
        $res = CommonUtils::requestDsOnline("timetable/reportR04", array(
            'tid'      => $tids,
            'class_id' => $classid,
        ), 'get');
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    // 根据当前永华获取需要修改的课程列表
    public function actionCourseAlias()
    {
        $teacher_id = Yii::app()->request->getParam('teacherId', '');
        $tid        = Yii::app()->request->getParam('tid', '');
        $teacher_id = ($teacher_id) ? $teacher_id : Yii::app()->user->id;
        $tid        = ($tid) ? $tid : $this->tid;

        $userModel = User::model()->findByPk($teacher_id);

        $model   = Timetable::model()->findAll();
        $yidDate = array();
        if ($model) {
            Yii::import('common.models.calendar.*');
            $criteria = new CDbCriteria();
            $criteria->compare('yid', $model->yid);
            $criteria->compare('branchid', $this->branchId);
            $schoolModel = CalendarSchool::model()->findAll($criteria);
            if ($schoolModel) {
                foreach ($schoolModel as $val) {
                    $end             = $val->startyear + 1;
                    $date[$val->yid] = ($val) ? $val->startyear . ' - ' . $end : "";
                }
            }
            foreach ($model as $val) {
                $yidDate[$val->id] = $date[$val->yid];
            }
        }

        $criteria = new CDbCriteria();
        $criteria->compare('tid', $tid);
        $criteria->compare('teacher_id', $teacher_id);
        $criteria->compare('status', 1);
        $teacherModel = TimetableCourseTeacher::model()->findAll($criteria);

        $courseList = array();
        if ($teacherModel) {
            foreach ($teacherModel as $val) {
                $courseList[] = array(
                    'id'          => $val->course_id,
                    'course_code' => $val->course->course_code,
                    'title'       => $val->course->getTitle(),
                    'alias'       => ($val->course->alias) ? $val->course->alias->alias_cn : '',
                );
            }
        }

        $status = 0;
        if ($teacher_id == Yii::app()->user->id) {
            $status = 1;
        }

        $this->render('courseListAlias', array(
            'courseList' => $courseList,
            'userModel'  => $userModel,
            'teacher_id' => $teacher_id,
            'tid'        => $tid,
            'status'     => $status,
            'yidDate'    => $yidDate
        ));
    }

    // 修改课程别名
    public function actionShowAlias()
    {
        $course_id   = Yii::app()->request->getParam('course_id', '');
        $courseModel = TimetableCourses::model()->findByPk($course_id);
        $criteria    = new CDbCriteria();
        $criteria->compare('tid', $this->tid);
        $criteria->compare('program', $courseModel->program);
        $criteria->compare('course_code', $courseModel->course_code);
        $aliasModel    = TimetableCoursesAlias::model()->find($criteria);
        $data['alias'] = '';
        if ($aliasModel) {
            $data['alias']       = $aliasModel->alias_cn;
            $data['course_code'] = $aliasModel->course_code;
        }
        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->showMessage();
    }

    //增加和修改课程的别名
    public function actionUpdateAlias()
    {
        $course_id = Yii::app()->request->getParam('course_id', '');
        $alias     = Yii::app()->request->getParam('alias', '');
        $teacherId = Yii::app()->request->getParam('teacherId', '');

        if ($teacherId != Yii::app()->user->id) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('attends', 'Only courses assigned to you could be edited.'));
            $this->showMessage();
        }

        $courseModel = TimetableCourses::model()->findByPk($course_id);
        $criteria    = new CDbCriteria();
        $criteria->compare('tid', $this->tid);
        $criteria->compare('program', $courseModel->program);
        $criteria->compare('course_code', $courseModel->course_code);
        $model = TimetableCoursesAlias::model()->find($criteria);
        if (!$model) {
            $model              = new TimetableCoursesAlias();
            $model->tid         = $this->tid;
            $model->program     = $courseModel->program;
            $model->course_code = $courseModel->course_code;
            $model->created_at  = time();
            $model->created_by  = Yii::app()->user->id;
        }
        $model->alias_cn   = $alias;
        $model->alias_en   = $alias;
        $model->updated_at = time();
        $model->updated_by = Yii::app()->user->id;
        if (!$model->save()) {
            $errors = current($model->getErrors());
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $errors[0]);
            $this->showMessage();
        }

        $this->addMessage('state', 'success');
        $this->addMessage('message', Yii::t('teaching', 'Save succefully!'));
        $this->showMessage();
    }

    // 获取指定日期，某个类型的签到孩子数据
    public function actionShowAttendStudent()
    {
        $targetDate = Yii::app()->request->getParam('targetDate');
        $type       = Yii::app()->request->getParam('type');
        $period     = Yii::app()->request->getParam('period');
        //新版数据
        $res = CommonUtils::requestDsOnline("timetable/showAttendStudent", array(
            'targetDate' => $targetDate,
            'type'       => $type,
            'period'     => $period,
            'tid'        => $this->tid,
        ), 'get');
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();


    }

    // 获取指定日期 某课程 某个类型的孩子签到数据
    public function actionShowAttendStudent2()
    {
        $targetDate = Yii::app()->request->getParam('targetDate');
        $type       = Yii::app()->request->getParam('type');
        $classid    = Yii::app()->request->getParam('classid');

        //新版数据
        $res = CommonUtils::requestDsOnline("timetable/showAttendStudent2", array(
            'targetDate' => $targetDate,
            'type'       => (int)$type,
            'classId'    => $classid,
            'tid'        => $this->tid,
        ), 'get');
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();

    }

    // 获取指定孩子，某个类型的签到数据
    public function actionShowStudentAttend()
    {
        $childid = Yii::app()->request->getParam('childid');
        $classid = Yii::app()->request->getParam('classId');
        $type    = Yii::app()->request->getParam('type');
        $period  = Yii::app()->request->getParam('period');
        $yid     = Yii::app()->request->getParam('yid');#根据指定的学年获取相应学年下的考勤数据
        //新版数据
        $res = CommonUtils::requestDsOnline("timetable/showStudentAttend", array(
            'childid' => $childid,
            'tid'     => $this->tid,
            'classId' => $classid,
            'type'    => $type,
            'period'  => $period,
            'yid'     => $yid,
        ), 'get');
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    // 获取指定孩子，某个类型的签到数量
    public function actionGetStudentAttendCount()
    {
        $childid = Yii::app()->request->getParam('childid');
        $classid = Yii::app()->request->getParam('classid');

        //
        if (!$childid || !$classid) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }

        $data = array(
            'total' => 0,
            'items' => array(),
        );

        $criteria = new CDbCriteria();
        $criteria->compare('childid', $childid);
        $criteria->compare('class_id', $classid);
        $criteria->compare('tid', $this->tid);
        $criteria->compare('status', TimetableRecords::ATTENDANCE_STATUS);
        $criteria->group  = 'period';
        $criteria->select = 'type,period, COUNT(*) as typeNum';
        $criteria->compare('type', TimetableRecords::ATTENDANCE_STATUS);
        $records = TimetableRecords::model()->findAll($criteria);

        foreach ($records as $record) {
            $data[$record->period]['total']                       += $record->typeNum;
            $data['items'][$record->period][$record->type]['num'] = $record->typeNum;
        }

        $criteria = new CDbCriteria();
        $criteria->compare('childid', $childid);
        $criteria->compare('tid', $this->tid);
        $criteria->compare('status', TimetableRecords::ATTENDANCE_STATUS);
        $criteria->group  = 'period';
        $criteria->select = 'type,period, COUNT(*) as typeNum';
        $criteria->compare('type', TimetableRecords::LATE_STATUS);
        $records = TimetableRecords::model()->findAll($criteria);

        foreach ($records as $record) {
            $data[$record->period]['total']                       += $record->typeNum;
            $data['items'][$record->period][$record->type]['num'] = $record->typeNum;
        }

        $criteria = new CDbCriteria();
        $criteria->compare('childid', $childid);
        $criteria->compare('tid', $this->tid);
        $criteria->compare('status', TimetableRecords::ATTENDANCE_STATUS);
        $criteria->group  = 'period';
        $criteria->select = 'type,period, COUNT(*) as typeNum';
        $criteria->compare('type', TimetableRecords::LEAVE_STATUS);
        $records = TimetableRecords::model()->findAll($criteria);

        foreach ($records as $record) {
            $data[$record->period]['total']                       += $record->typeNum;
            $data['items'][$record->period][$record->type]['num'] = $record->typeNum;
        }

        $criteria = new CDbCriteria();
        $criteria->compare('childid', $childid);
        $criteria->compare('tid', $this->tid);
        $criteria->compare('status', TimetableRecords::ATTENDANCE_STATUS);
        $criteria->group  = 'period';
        $criteria->select = 'type,period, COUNT(*) as typeNum';
        $criteria->compare('type', TimetableRecords::ABSENTEE_STATUS);
        $records = TimetableRecords::model()->findAll($criteria);

        foreach ($records as $record) {
            $data[$record->period]['total']                       += $record->typeNum;
            $data['items'][$record->period][$record->type]['num'] = $record->typeNum;
        }
//        echo "<pre>";
//        var_dump($data);die;
        for ($i = 1; $i < 9; $i++) {
            foreach (array(10, 20, 30, 40, 41, 42) as $v) {
                if (isset($data['items'][$i][$v])) {
                    $num                              = $data['items'][$i][$v]['num'];
                    $data['items'][$i][$v]['percent'] = round($num / $data[$i]['total'], 2) * 100;
                } else {
                    $data['items'][$i][$v]['num']     = 0;
                    $data['items'][$i][$v]['percent'] = 0;
                }
                $data['items'][$i][$v]['period'] = $i;
            }
        }

        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->showMessage();
    }

    /**
     * 新版数据 获取指定孩子，某个类型的签到数量
     */
    public function actionStudentAttendCount()
    {
        $childid = Yii::app()->request->getParam('childid');
        $classid = Yii::app()->request->getParam('classId');

        $res = CommonUtils::requestDsOnline("timetable/studentAttendCount", array(
            'childid' => $childid,
            'tid'     => $this->tid,
            'classId' => $classid,
        ), 'get');
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }


    // 获取当前
    public function getCurrentClass()
    {
        $criteria = new CDbCriteria();
        $criteria->compare('yid', $this->timetable->yid);
        $criteria->compare('classtype', array("e6", "e7", 'e8', 'e9', 'e10', 'e11', 'e12'));
        // $criteria->compare('stat', 10);
        $criteria->index = 'classid';
        $classModel      = IvyClass::model()->findAll($criteria);
        return $classModel;
    }

    // 根据时间判断是第一学期还是第二学期
    public function getSemesterIndex($yid, $timestamp)
    {
        Yii::import('common.models.calendar.CalendarSemester');
        $semesterInfo = CalendarSemester::model()->getSemesterTimeStamp($yid);
        $semester     = 1;
        if (isset($semesterInfo) && $timestamp >= $semesterInfo['spring_start']) {
            $semester = 2;
        }
        return $semester;
    }

    // 获取schedule缓存
    public function getScheduleCache($type, $targetId, $tid, $semesterId = 0)
    {
        $criteria = new CDbCriteria;
        $criteria->compare('type', $type);
        $criteria->compare('target_id', $targetId);
        $criteria->compare('tid', $tid);
        $criteria->index     = 'term';
        $scheduleCacheModels = TimetableWeeklyScheduleCache::model()->findAll($criteria);

        $scheduleCacheModel = NULL;
        if ($scheduleCacheModels) {
            $scheduleCacheModel = $scheduleCacheModels[0];
            if (isset($scheduleCacheModels[$semesterId])) {
                $scheduleCacheModel = $scheduleCacheModels[$semesterId];
            }
        }
        return $scheduleCacheModel;
    }

    public function getStudentCourseData($tid, $course_code, $semesterId = 0)
    {
        $criteria = new CDbCriteria;
        $criteria->compare('course_code', $course_code);
        $criteria->compare('tid', $tid);
        $criteria->compare('status', 1);
        $models      = TimetableStudentData::model()->findAll($criteria);
        $returnModel = NULL;
        foreach ($models as $model) {
            if (isset($returnModel[$model->child_id])) {
                if ($model->term == $semesterId) {
                    $returnModel[$model->child_id] = $model;
                }
            } else {
                $returnModel[$model->child_id] = $model;
            }
        }
        return $returnModel;
    }

    // 出了改变tid，其他参数全部不变
    public function createTidUrl($route, $params = array(), $ampersand = '&')
    {
        foreach ($_GET as $k => $v) {
            if (!isset($params[$k])) {
                $params[$k] = $v;
            }
        }

        return parent::createUrl($route, $params, $ampersand);
    }

    public function actionShowStudent($courseid = '', $code = '', $tid = '')
    {
        $ret = array();
        if ($courseid && $code && $tid) {
            $criteria = new CDbCriteria();
            $criteria->compare('course_id', $courseid);
            $criteria->compare('course_code', $code);
            $criteria->compare('tid', $tid);
            $items = TimetableStudentData::model()->findAll($criteria);
            foreach ($items as $item) {
                $ret[$item->child_id] = array(
                    'childid'      => $item->child_id,
                    'name'         => $item->childProfile->getChildName(false, true),
                    'className'    => $item->classObj->title,
                    'status'       => $item->status,
                    'child_status' => $item->childProfile->status,
                );
            }
        }

        $this->addMessage('state', 'success');
        $this->addMessage('data', $ret);
        $this->showMessage();
    }

    public function actionSaveStudent()
    {
        $id   = Yii::app()->request->getPost('id', 0);
        $code = Yii::app()->request->getPost('code', '');
        $sid  = Yii::app()->request->getPost('sid', 0);

        $this->addMessage('state', 'fail');
        $this->addMessage('message', 'Fail');
        if ($id && $code && $sid) {
            $criteria = new CDbCriteria();
            $criteria->compare('course_id', $id);
            $criteria->compare('course_code', $code);
            $criteria->compare('child_id', $sid);
            $count = TimetableStudentData::model()->count($criteria);
            if ($count == 0) {
                $child = ChildProfileBasic::model()->findByPk($sid);

                if ($child->schoolid == $this->branchId) {
                    $model              = new TimetableStudentData();
                    $model->tid         = $this->tid;
                    $model->class_id    = $child->classid;
                    $model->child_id    = $sid;
                    $model->course_id   = $id;
                    $model->course_code = $code;
                    $model->class_room  = 0;
                    $model->term        = 1;
                    $model->start_at    = time();
                    $model->status      = 1;
                    $model->created_at  = time();
                    $model->created_by  = Yii::app()->user->id;
                    $model->updated_at  = time();
                    $model->updated_by  = Yii::app()->user->id;
                    if ($model->save()) {
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', 'Success');
                        $this->addMessage('callback', 'cbSave');
                        $this->addMessage('data', array(
                            'childid'   => $sid,
                            'name'      => $child->getChildName(false, true),
                            'className' => $child->ivyclass->title,
                            'child_status'=>$child->status
                        ));
                    } else {
                        $this->addMessage('data', $model->getErrors());
                    }
                } else {
                    $this->addMessage('message', '请添加本校学生');
                }
            } else {
                $this->addMessage('message', '学生已存在');
            }
        } else {
            $this->addMessage('message', '请选择学生');
        }

        $this->showMessage();
    }

    public function actionDelStudent()
    {
        $id   = Yii::app()->request->getPost('id', 0);
        $code = Yii::app()->request->getPost('code', '');
        $sid  = Yii::app()->request->getPost('sid', 0);
        if ($id && $code && $sid) {
            //已有打分成绩不能删除
            $criteria = new CDbCriteria();
            $criteria->compare('timetable_records_id', $id);
            $criteria->compare('timetable_records_code', $code);
            $criteria->compare('childid', $sid);
            $criteria->compare('value', ">0");//这里的0不代表0分 大于0就代表已经打分
            $child_fraction_count = AchievementReportChildFraction::model()->count($criteria);
            if (!empty($child_fraction_count)) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '学生此课程已有评估成绩,不可删除');
                $this->showMessage();
            }
            //有评语不能删除
            $criteria = new CDbCriteria();
            $criteria->compare('timetable_records_id', $id);
            $criteria->compare('timetable_records_code', $code);
            $criteria->compare('childid', $sid);
            $criteria->compare('status', 1);
            $child_course = AchievementReportChildCourse::model()->findAll($criteria);
            if (!empty($child_course)) {
                foreach ($child_course as $item){
                    if(!empty($item->teacher_message_cn) || !empty($item->teacher_message_en) || !empty($item->student_message_cn) || !empty($item->student_message_en)){
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', '学生此课程已有评语,不可删除');
                        $this->showMessage();
                    }
                }
            }
            //删除分数数据
            $criteria = new CDbCriteria();
            $criteria->compare('timetable_records_id', $id);
            $criteria->compare('timetable_records_code', $code);
            $criteria->compare('childid', $sid);
            $model = AchievementReportChildFraction::model()->find($criteria);
            if (!empty($model)) {
                $model->delete();
            }
            //删除课程评语
            if (!empty($child_course)) {
                foreach ($child_course as $item){
                    $item->delete();
                }
            }
            $criteria = new CDbCriteria();
            $criteria->compare('course_id', $id);
            $criteria->compare('course_code', $code);
            $criteria->compare('child_id', $sid);
            $model = TimetableStudentData::model()->find($criteria);
            if ($model->delete()) {
                $this->addMessage('state', 'success');
            } else {
                $this->addMessage('state', 'fail');
            }
            $this->showMessage();
        }
    }



    //异常考勤list
    public function actionUnusualAttendanceList()
    {
        $day_number = Yii::app()->request->getParam("day_number");
        $type       = Yii::app()->request->getParam("type");
        $page       = Yii::app()->request->getParam("page", 1);
        $pageSize   = Yii::app()->request->getParam("pageSize", 20);
        $time_limit = Yii::app()->request->getParam("time_limit");
        $start_date = Yii::app()->request->getParam("start_date");
        $end_date   = Yii::app()->request->getParam("end_date");
        if ($time_limit == 'week') {
            $start_date = date("Y-m-d", mktime(0, 0, 0, date("m"), date("d") - date("w") + 1, date("Y")));
            $end_date   = date("Y-m-d", mktime(23, 59, 59, date("m"), date("d") - date("w") + 7, date("Y")));
        }
        if ($time_limit == 'month') {
            $start_date = date('Y-m-01', time());
            $end_date   = date('Y-m-d', strtotime("$start_date +1 month -1 day"));
        }
        if ($time_limit == 'semester') {
            //本学期的开始结束时间
            $semesterInfo = CalendarSemester::model()->getSemesterTimeStamp($this->timetable->yid);
            if ((time() >= $semesterInfo['fall_start'] && time() <= $semesterInfo['fall_end']) || (time() < $semesterInfo['fall_start'] && time() < $semesterInfo['fall_end'])) {
                $start_date = date('Y-m-d', $semesterInfo['fall_start']);
                $end_date   = date('Y-m-d', $semesterInfo['fall_end']);
            } else {
                $start_date = date('Y-m-d', $semesterInfo['spring_start']);
                $end_date   = date('Y-m-d', $semesterInfo['spring_end']);
            }
        }
        if ($time_limit == 'year') {
            $semesterInfo = CalendarSemester::model()->getCalendarRange($this->timetable->yid);
            $start_date   = date('Y-m-d', $semesterInfo[$this->timetable->yid]['starttime']);
            $end_date     = date('Y-m-d', $semesterInfo[$this->timetable->yid]['endtime']);
        }
        if ($time_limit == 'time') {
            if ($end_date < $start_date) {
                $this->addMessage('state', 'fail');
                $msg = Yii::t("attends", 'time error');
                $this->addMessage('message', $msg);
                $this->showMessage();
            }
            $semesterInfo      = CalendarSemester::model()->getCalendarRange($this->timetable->yid);
            $semester_end_date = date('Y-m-d', $semesterInfo[$this->timetable->yid]['endtime']);
            if ($end_date > $semester_end_date) {
                $this->addMessage('state', 'fail');
                $msg = Yii::t("attends", 'Cannot span school years');
                $this->addMessage('message', $msg);
                $this->showMessage();
            }
            $criteria = new CDbCriteria;
            $criteria->compare('branchid', $this->branchId);
            $criteria->compare('month', array(date('Ym', strtotime($start_date)), date('Ym', strtotime($end_date))));
            $CalendarSchool = CalendarSchool::model()->with('months')->findAll($criteria);
            foreach ($CalendarSchool as $value) {
                $select_yid[] = $value->yid;
            }
            if (count($select_yid) > 1) {
                $this->addMessage('state', 'fail');
                $msg = Yii::t("attends", 'Cannot span school years');
                $this->addMessage('message', $msg);
                $this->showMessage();
            }
        }
        $res = CommonUtils::requestDsOnline('attendance/getContinuous', array(
            'school_id'  => $this->branchId,
            'yid'        => $this->timetable->yid,
            'start_date' => $start_date,
            'end_date'   => $end_date,
            'day_number' => $day_number,
            'type'       => $type,
            'page'       => $page,
            'pageSize'   => $pageSize,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    #异常考勤的详情
    public function actionContinuousDetail()
    {
        $child_id   = Yii::app()->request->getParam("child_id");
        $day_number = Yii::app()->request->getParam("day_number");
        $type       = Yii::app()->request->getParam("type");
        $time_limit = Yii::app()->request->getParam("time_limit");
        $start_date = Yii::app()->request->getParam("start_date");
        $end_date   = Yii::app()->request->getParam("end_date");
        if ($time_limit == 'week') {
            $start_date = date("Y-m-d", mktime(0, 0, 0, date("m"), date("d") - date("w") + 1, date("Y")));
            $end_date   = date("Y-m-d", mktime(23, 59, 59, date("m"), date("d") - date("w") + 7, date("Y")));
        }
        if ($time_limit == 'month') {
            $start_date = date('Y-m-01', time());
            $end_date   = date('Y-m-d', strtotime("$start_date +1 month -1 day"));
        }
        if ($time_limit == 'semester') {
            //本学期的开始结束时间
            $semesterInfo = CalendarSemester::model()->getSemesterTimeStamp($this->timetable->yid);
            if (time() >= $semesterInfo['fall_start'] && time() <= $semesterInfo['fall_end']) {
                $start_date = date('Y-m-d', $semesterInfo['fall_start']);
                $end_date   = date('Y-m-d', $semesterInfo['fall_end']);
            } else {
                $start_date = date('Y-m-d', $semesterInfo['spring_start']);
                $end_date   = date('Y-m-d', $semesterInfo['spring_end']);
            }

        }
        if ($time_limit == 'year') {
            $semesterInfo = CalendarSemester::model()->getCalendarRange($this->timetable->yid);
            $start_date   = date('Y-m-d', $semesterInfo[$this->timetable->yid]['starttime']);
            $end_date     = date('Y-m-d', $semesterInfo[$this->timetable->yid]['endtime']);
        }
        $res = CommonUtils::requestDsOnline('attendance/getContinuousDetail', array(
            'child_id'   => $child_id,
            'start_date' => $start_date,
            'end_date'   => $end_date,
            'day_number' => $day_number,
            'type'       => $type,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    //获取所有可选班级
    public function actionSubjectAbsenceClass()
    {
        $res = CommonUtils::requestDsOnline('attendance/getSubjectAbsenceClass', array(
            'school_id' => $this->branchId
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    //科目缺勤的统计  学科考勤 学年 上下学期（AB CD） 班级
    public function actionSubjectAbsence()
    {
        $class_type = Yii::app()->request->getParam("class_type");
        $term       = Yii::app()->request->getParam("term");
        $yid        = Yii::app()->request->getParam("yid");
        $res        = CommonUtils::requestDsOnline('attendance/getSubjectAbsence', array(
            'school_id'  => $this->branchId,
            'class_type' => $class_type,
            'term'       => $term,
            'yid'        => $yid,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    //添加参会老师
    public function actionSaveMeetTeacher()
    {
        $tid         = Yii::app()->request->getParam("tid");
        $course_id   = Yii::app()->request->getPost('course_id', 0);
        $course_code = Yii::app()->request->getParam("course_code");
        $teacher_ids = Yii::app()->request->getParam("teacher_ids");

        $res = CommonUtils::requestDsOnline('timetable/saveMeetTeacher', array(
            'school_id'   => $this->branchId,
            'tid'         => $tid,
            'course_id'   => $course_id,
            'course_code' => $course_code,
            'teacher_ids' => $teacher_ids,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    //展示参会老师
    public function actionShowMeetTeacher()
    {
        $tid         = Yii::app()->request->getParam("tid");
        $course_id   = Yii::app()->request->getPost('course_id', 0);
        $course_code = Yii::app()->request->getParam("course_code");
        $res         = CommonUtils::requestDsOnline('timetable/showMeetTeacher', array(
            'school_id'   => $this->branchId,
            'tid'         => $tid,
            'course_id'   => $course_id,
            'course_code' => $course_code,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    //删除参会老师
    public function actionDelMeetTeacher()
    {
        $tid         = Yii::app()->request->getParam("tid");
        $course_id   = Yii::app()->request->getPost('course_id', 0);
        $course_code = Yii::app()->request->getParam("course_code");
        $teacher_id  = Yii::app()->request->getParam("teacher_id");
        $res         = CommonUtils::requestDsOnline('timetable/delMeetTeacher', array(
            'school_id'   => $this->branchId,
            'tid'         => $tid,
            'course_id'   => $course_id,
            'course_code' => $course_code,
            'teacher_id'  => $teacher_id,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }


}
