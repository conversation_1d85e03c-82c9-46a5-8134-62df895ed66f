<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Basic'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site', '课后课管理'), array('//mcampus/asainvoice/index')) ?></li>
        <li class="active">课后课账单</li>
    </ol>

    <div class="row">
        <!-- 左侧菜单 -->
        <div class="col-md-2 col-sm-2 mb10">
            <?php
            $this->widget('zii.widgets.CMenu',array(
                'items'=> $this->getMenu(),
                'id'=>'pageCategory',
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked background-gray'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>

        <div class="col-md-10 col-sm-12">
            <div class="col-md-2" id='classes'>
                <ul class="nav nav-pills nav-stacked background-gray">
                    <?php
                        $classData = array();
                        foreach ($classes as $class) {
                            $href = 'javascript:void(0);';
                            echo "<li class='{$active}'><a onclick='showChildren({$class->classid}, this)' href='{$href}'>";
                            echo $class->title;
                            echo '</a></li>';

                            $html = '';
                            foreach ($class->children as $child) {
                                $href = $this->createUrl('getInvoice', array('classid' => $child->childid));
                                $href = 'javascript:void(0);';
                                $html .= '<ul class="nav nav-pills nav-stacked background-gray">';
                                $html .= "<li class='{$active}'><a href='{$href}' class='J_ajax' onclick='showInvoice({$child->childid}, this)'>";
                                $html .= $child->getChildName();
                                $html .= '</a></li></ul>';
                                $classData[$class->classid] = $html;
                            }
                        }
                        // 新注册孩子
                        if ($newChildObj) {
                            $html = '';
                            foreach ($newChildObj as $newChild) {
                                $href = $this->createUrl('getInvoice', array('classid' => 0));
                                $href = 'javascript:void(0);';
                                $html .= '<ul class="nav nav-pills nav-stacked background-gray">';
                                $html .= "<li class='{$active}'><a href='{$href}' class='J_ajax' onclick='showInvoice({$newChild->childid}, this)'>";
                                $html .= $newChild->getChildName();
                                $html .= '</a></li></ul>';
                                $classData[0] = $html;
                            }
                            echo "<li class='{$active}'><a onclick='showChildren(0, this)' href='javascript:void(0);'>";
                            echo '新注册学生';
                            echo '</a></li>';
                        }
                    ?>
                </ul>
            </div>
            <div class="col-md-2" id="children">
                <ul class="nav nav-pills nav-stacked background-gray">
                    <?php 
                        // foreach ($classes[0]->children as $child) {
                        //     $href = $this->createUrl('getInvoice', array('classid' => $child->childid));
                        //     echo "<li class='{$active}'><a href='{$href}' class='J_ajax'>";
                        //     echo $child->name_cn;
                        //     echo '</a></li>';
                        // }
                    ?>
                </ul>
            </div>
            <div class="col-md-8" id="group" hidden="hidden">
                <div class="col-xs-12">
                    <h3 id="invoiceTitle" hidden="hidden">账单信息</h3>
                </div>
                <!-- 已支付账单 -->
                <div id="paidinvoice" class="col-md-12">
                    
                </div>
                <!-- 未支付账单 -->
                <div id="unpayinvoice" class="col-md-12">
                    
                </div>

                <form id="invoiceForm" class="J_ajaxForm  form-horizontal" method="post" action="<?php echo $this->createUrl('saveInvoice'); ?>">
                <!-- 课程组信息 -->
                <div class="col-xs-12">
                    <hr>
                    <h3>新开账单<small class="text-warning">（启用的课程组会显示在下面）</small></h3>
                </div>
                <div class="col-xs-5">
                    <select class="form-control" id="groups" name="group" onchange=showCourse(this)>
                        <option value="0">请选择课程组</option>
                        <?php 
                            $courseData = array();
                            foreach ($group as $v):
                                foreach ($v->course as $course) {
                                    if ($course->status == AsaCourse::STATUS_ACTIVE) {
                                        $courseData[$v->id] .= "<tr><td><input name='itemId[]' value='{$course->id}' type='checkbox'> {$course->title_cn}</td>";
                                        $courseData[$v->id] .= "<td>{$course->vendor}</td>";
                                        $courseData[$v->id] .= "<td>{$course->unit_price}</td>";
                                        $courseData[$v->id] .= "<td><input name='itemCount_{$course->id}' type='number' value='{$course->default_count}' class=''></td></tr>";
                                    }
                                }
                        ?>
                        <option value=<?php echo $v->id; ?> ><?php echo $v->title_cn; ?></option>
                        <?php endforeach; ?>
                    </select>
                    <br>
                </div>
                <div class="col-xs-12" id="courseInfo">
                </div>
                <br>
                <div class="col-xs-12" id="invoiceInfo" hidden="hidden">
                    <div class="form-group col-xs-8 form-inline">
                        <label for="invoice-title" class="control-label">账单标题：</label>
                        <input id="invoice-title" name="title" type="text" class="form-control" placeholder="账单标题" size="50">
                    </div>                    
                    <div class="form-group col-xs-8 form-inline" hidden=hidden id="discountBox">
                        <label for="discount" class="control-label">减免计划：</label>
                        <select class="form-control" id="discount" name="discount">
                        </select>
                    </div>

                    <div class="form-group col-xs-12">
                        <button type="submit" class="btn btn-primary J_ajax_submit_btn">开账单</button>
                    </div>
                </div>
                <input type="hidden" id="classid" name="classid" value="">
                <input type="hidden" id="childid" name="childid" value="">
                </form>
            </div>       
        </div>
    </div>
</div>

<!-- 微信支付模态框 -->
<div class="modal fade" id="modal" tabindex="-1" role="dialog" aria-labelledby="modal">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel">微信支付</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-lg-12">
                        <p class="text-info">提示信息：请让家长打开微信钱包中的收付款，用条码扫描枪扫描或手动输入条形码</p>
                    </div>
                    <div class="col-lg-3">
                    </div>
                    <div class="col-lg-6">
                        <div class="input-group">
                            <span class="input-group-addon">扫描授权码</span>
                            <input onkeypress="return wechatPay(event)" type="text" id="auth_code" name="auth_code" class="form-control">
                            <input type="hidden" id='wpInvoice'>
                        </div>
                    </div>
                    <div class="col-lg-3">
                    </div>
                    <!-- <h3>异常记录</h3> -->
                    <div class="col-lg-12" hidden="hidden">
                        <br>
                        <p class="text-danger">支付受阻，请用户在手机上输入支付密码后点击按钮继续</p>
                        <div class="confirm"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
            </div>
        </div>
    </div>
</div>

<script>
    var children = <?php echo json_encode($classData); ?>;
    var course = <?php echo json_encode($courseData); ?>;

    // 显示指定班级的学生
    function showChildren(classid, ele) {
        if (classid != undefined) {
            $('#classes li').removeClass('active');
            $(ele).parent().addClass('active');
            $('#children').html(children[classid]);
            $('#classid').val(classid);
        }
    }

    // 显示指定学生的未付账单信息
    function showInvoice(childid, ele) {
        if (childid != undefined) {
            if (ele) {
                $('#children li').removeClass('active');
                $(ele).parent().addClass('active');
            }
            $('#childid').val(childid);

            var url = '<?php echo $this->createUrl('getInvoice');?>';
            $.ajax({
                type: 'POST',
                dataType: 'JSON',
                url: url,
                data: {childid:childid},
                success: function (data) {
                    var table = '';
                    if (data != undefined && data != '') {
                        table += '<table class="table table-bordered"><colgroup><col width="100"><col width="150"><col width="50"><col width="150"></colgroup><thead class="text-danger"><th>未支付账单</th><th>账单明细</th><th>账单金额</th><th>账单操作</th></thead>';
                        $.each(data, function (i, e) {
                            table += '<tr><td>' + e.title + '</td>';
                            table += '<td>' + e.info + '</td>';
                            table += '<td>' + e.amount + '</td>';
                            table += '<td><button onclick="cashPay('+i+', '+e.amount+')" class="btn btn-default">现金支付</button> <button onclick="showModal('+i+')" class="btn btn-default">微信支付</button> <button onclick="delInvoice('+i+')" class="btn btn-danger">作废账单</button></td></tr>';
                        });
                        table += '</table>';
                    }
                    if (table == '') {
                        $('#unpayinvoice').hide();
                    }else{
                        $('#unpayinvoice').html(table);
                        $('#unpayinvoice').show();
                    }
                }
            }).always(function () {
                showPaidInvoice(childid);
                $('#group').show();
            });
        }
        changeInvoiceTitle();
    }

    // 显示已支付订单
    function showPaidInvoice(childid) {     
        if (childid != undefined) {
            $('#childid').val(childid);

            var url = '<?php echo $this->createUrl('paidInvoice');?>';
            $.ajax({
                type: 'POST',
                dataType: 'JSON',
                url: url,
                data: {childid:childid},
                success: function (data) {
                    var table = '';
                    if (data != undefined && data != '') {
                        table = '<table class="table table-bordered"><colgroup><col width="100"><col width="150"><col width="50"><col width="150"></colgroup><thead><th>已支付账单</th><th>账单明细</th><th>账单金额</th><th>操作人</th></thead>';
                        $.each(data, function (i, e) {
                            table += '<tr><td>' + e.title + '</td>';
                            table += '<td>' + e.info + '</td>';
                            table += '<td>' + e.amount + '</td>';
                            table += '<td>' + e.user +'——'+ e.date + '</td></tr>';
                        });
                        table += '</table>';
                    }
                    if (table == '') {
                        $('#paidinvoice').hide();
                    }else{
                        $('#paidinvoice').html(table);
                        $('#paidinvoice').show();
                    }
                }
            }).always(function () {
                if ($('#paidinvoice :visible').length == 0 && $('#unpayinvoice :visible').length == 0) {
                    $("#invoiceTitle").hide();
                }else{
                    $("#invoiceTitle").show();
                }
            }
            );
        }
    }

    // 下拉菜单显示课程
    function showCourse(ele) {
        var gid = $(ele).val();
        var html = '<table class="table table-bordered" ><thead><th>课程名称</th><th>课程提供方</th><th>单价</th><th>课时</th></thead>';
        html += course[gid] + '</table>';
        $('#courseInfo').html(course[gid] ? html : '暂无数据');
        if (gid == 0) {
            $("#invoiceInfo").hide();
        } else{
            $("#invoiceInfo").show();
            // 查询相关的减免计划
            $.ajax({
                type : 'POST',
                dataType: 'JSON',
                data: {gid:gid},
                url: '<?php echo $this->createUrl("discount"); ?>',
                success: function (data) {
                    var ele = $("#discount");
                    var eleBox = $("#discountBox");
                    if (data.state == "success") {
                        ele.empty();
                        // data = eval("("+ '{"2":"减免计划2","5":"减免计划5","9":"减免计划9"}' +")");
                        var html = "<option value='0'>选择减免计划</option>";
                        $.each(data.data, function (i, v) {
                            html += "<option value="+i+">"+v+"</option>"
                        });
                        ele.append(html);
                        eleBox.show();
                    } else{
                        eleBox.hide();
                    }
                }
            });
        }
        changeInvoiceTitle();
    }

    // 修改默认账单标题
    function changeInvoiceTitle() {
        var childName = $('#children .active a').text();
        var title = childName +' '+ $('#groups').find('option:selected').text();
        $('#invoice-title').val(title);
    }

    // 账单保存成功回调
    function cbShow(data) {
        showInvoice(data.childid, '');
    }

    // 现金付款
    function cashPay(id, amount) {
        var realAmount =  prompt('请输入现金付款的金额');
        if (realAmount != null) {
            if (realAmount != amount) {
                alert('付款金额错误');
            }else{
                $.ajax({
                    type : 'POST',
                    dataType: 'JSON',
                    data: {id:id, amount:amount},
                    url: '<?php echo $this->createUrl("cashPay"); ?>',
                    success: function (data) {
                        if (data.state == 'fail') {
                            alert('付款失败：' + data.message);
                        }
                        if (data.state == 'success') {
                            alert('支付成功。');
                            showInvoice(data.data.childid, '');
                        }
                    }
                });
            }
        }
    }

    // 显示微信支付的模态框
    function showModal(id) {
        $('.confirm').parent().hide();
        $('#wpInvoice').val(id);
        $.ajax({
            type : 'POST',
            dataType: 'JSON',
            data: {invoiceId:id},
            url: '<?php echo $this->createUrl("unpayWechat"); ?>',
            success: function (data) {
                if (data != '') {
                    var html = '';
                    $.each(data, function (i, v) {
                        html += '<button onclick="wechatConfirm(' +"'"+v+"'"+ ')" class="btn btn-default">点击此按钮继续</button> ';
                    });
                    $('.confirm').html(html);
                    $('.confirm').parent().show();
                }
            }
        });
        $('#modal').modal('show');
    }

    // 微信支付自动聚焦input
    $('#modal').on('shown.bs.modal', function (e) {
        $('#auth_code')[0].focus();
    });

    // 微信支付
    function wechatPay(ele) {
        if(ele.keyCode == 13){
            var authCode = $('#auth_code').val();
            var invoiceId = $('#wpInvoice').val();
            if (!authCode || !invoiceId) {
                return false;
            }
            $.ajax({
                type : 'POST',
                dataType: 'JSON',
                data: {authCode:authCode, invoiceId:invoiceId},
                url: '<?php echo $this->createUrl("wechatPay"); ?>',
                success: function (data) {
                    if(data.return_code == 'SUCCESS'){
                        if(data.result_code == 'SUCCESS'){
                            resultTip({msg: '付款成功'});
                            showInvoice($('#childid').val(), '');
                            $('#modal').modal('hide');
                        }
                        else{
                            if(data.err_code == 'AUTHCODEEXPIRE'){
                                resultTip({msg: data.err_code_des, error: 1});
                            }
                            else if(data.err_code == 'USERPAYING'){
                                showMicropay(data.out_trade_no);
                            }
                        }
                    }
                }
            });
        }
    }

    // 查询账单状态
    function showMicropay(orderid) {
        var html = '<button onclick="wechatConfirm(\'' +orderid+ '\')" class="btn btn-default">点击此按钮继续</button> ';
        $('.confirm').html(html);
        $('.confirm').parent().show();
    }

    // 微信付款确认
    function wechatConfirm(orderid) {
        var url = '<?php echo $this->createUrl("queryWechat"); ?>';
        $.ajax({
            type: 'POST',
            dataType: 'JSON',
            url: url,
            data: {orderid:orderid},
            success: function (data) {
                if (data.state == 'success') {
                    resultTip({msg: 'Payment success'});
                    showInvoice($('#childid').val(), '');
                    $('#modal').modal('hide');
                } else{
                    resultTip({msg: 'Payment failure', error: 'warning'});
                }
            }

        });
    }

    // 作废账单
    function delInvoice(invoiceId) {
        if (confirm('确认作废账单')) {
            $.ajax({
                type : 'POST',
                dataType: 'JSON',
                data: {invoiceId:invoiceId},
                url: '<?php echo $this->createUrl("delInvoice"); ?>',
                success: function (data) {
                    if (data.state == 'success') {
                        resultTip({msg: 'Payment success'});
                        showInvoice(data.data.childid);
                    }
                }
            });
        }
    }
</script>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>