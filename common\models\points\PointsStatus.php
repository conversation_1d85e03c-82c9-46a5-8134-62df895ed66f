<?php

/**
 * This is the model class for table "ivy_points_status".
 *
 * The followings are the available columns in table 'ivy_points_status':
 * @property integer $id
 * @property integer $itemid
 * @property string $memo
 * @property integer $status
 * @property integer $update_timestamp
 * @property integer $update_userid
 */
class PointsStatus extends CActiveRecord
{
	CONST STATS_CREATED = 10; //订单已提交
	CONST STATS_CONFIRMED = 20; //已确认
	CONST STATS_READYTOSHIP = 110; //准备发往校园
	CONST STATS_SHIPPING = 120; //已经发往校园
	CONST STATS_RECEIVED = 130; //校园已接收
	CONST STATS_COMPLETED = 210; //已发放
	
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PointsStatus the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_points_status';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('itemid, update_timestamp, update_userid', 'required'),
			array('itemid, status, update_timestamp, update_userid', 'numerical', 'integerOnly'=>true),
			array('memo', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, itemid, memo, status, update_timestamp, update_userid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'User'=>array(self::BELONGS_TO, 'User', 'update_userid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'itemid' => 'Itemid',
			'memo' => '备忘录',
			'status' => 'Status',
			'update_timestamp' => 'Update Timestamp',
			'update_userid' => 'Update Userid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('itemid',$this->itemid);
		$criteria->compare('memo',$this->memo,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('update_timestamp',$this->update_timestamp);
		$criteria->compare('update_userid',$this->update_userid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}