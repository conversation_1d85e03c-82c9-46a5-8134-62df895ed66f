<?php

/**
 * This is the model class for table "ivy_visits_config_item".
 *
 * The followings are the available columns in table 'ivy_visits_config_item':
 * @property integer $id
 * @property string $schoolid
 * @property string $day
 * @property integer $day_timestamp
 * @property string $visit_time
 * @property integer $visit_num
 * @property integer $status
 * @property integer $created_at
 * @property integer $created_by
 * @property integer $updated_at
 * @property integer $updated_by
 */
class VisitsConfigItem extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_visits_config_item';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('schoolid, day, day_timestamp, visit_time, visit_num, created_at, created_by, updated_at, updated_by', 'required'),
			array('day_timestamp, visit_num, status, created_at, created_by, updated_at, updated_by', 'numerical', 'integerOnly'=>true),
			array('schoolid, day, visit_time', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, schoolid, day, day_timestamp, visit_time, visit_num, status, created_at, created_by, updated_at, updated_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'schoolid' => 'Schoolid',
			'day' => 'Day',
			'day_timestamp' => 'Day Timestamp',
			'visit_time' => 'Visit Time',
			'visit_num' => 'Visit Num',
			'status' => 'Status',
			'created_at' => 'Created At',
			'created_by' => 'Created By',
			'updated_at' => 'Updated At',
			'updated_by' => 'Updated By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('day',$this->day,true);
		$criteria->compare('day_timestamp',$this->day_timestamp);
		$criteria->compare('visit_time',$this->visit_time,true);
		$criteria->compare('visit_num',$this->visit_num);
		$criteria->compare('status',$this->status);
		$criteria->compare('created_at',$this->created_at);
		$criteria->compare('created_by',$this->created_by);
		$criteria->compare('updated_at',$this->updated_at);
		$criteria->compare('updated_by',$this->updated_by);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return VisitsConfigItem the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * [getVisitItems 获取某个学校有效的时间大于今日的配置]
	 * @param  [type] $schoolid [description]
	 * @return [type]           [description]
	 */
	public function getVisitItems($schoolid)
	{
		$criteria = new CDbCriteria();
		$criteria->compare('schoolid', $schoolid);
		$criteria->compare('day_timestamp', '>=' . time());
		$criteria->compare('status', 1);
		$criteria->order = 'day_timestamp';
		$models = VisitsConfigItem::model()->findAll($criteria);
		$result = array();
		foreach ($models as $model) {
			$result[$model->day][$model->visit_time]['num'] = $model->visit_num;
			$result[$model->day][$model->visit_time]['id'] = $model->id;
		}
		return $result;
	}
}
