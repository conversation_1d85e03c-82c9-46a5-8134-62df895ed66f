<style>
    [v-cloak] {
        display: none;
    }

    .teacherImg {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        object-fit: cover;
    }

    .classlist {
        border: 1px solid #DBDBDB;
        border-radius: 4px;
        padding: 16px;
        margin-bottom: 16px;
        background: #fff
    }

    .font16 {
        font-size: 16px
    }

    .pt5 {
        padding-top: 3px
    }

    .repTeacher {
        background: #FAFAFA;
        border-radius: 4px;
        padding: 12px;
        margin-bottom: 10px
    }

    .table_wrap {
        width: 100%;
        overflow: auto;
        float: left;
        max-height: 600px
    }

    #classlist {
        max-height: 600px;
        overflow-y: auto

    }

    /* .table {
        table-layout: fixed;
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        border-left: 1px solid #DDDDDD;
        border-top: 1px solid #DDDDDD;
    }

    .table td, .table th {
        width: 250px;
        border-right: 1px solid #DDDDDD;
        border-bottom: 1px solid #DDDDDD;
        border-top: none !important;
        padding: 16px !important
    }

    /* 表头固定 */
    .table tr th {
        position: sticky;
        top: 0;
        background: #FAFAFA;
        z-index: 1
    } */

    .scrollbar::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 10px; /*高宽分别对应横竖滚动条的尺寸*/
        height: 10px;
    }

    .scrollbar::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background-color: skyblue;
        background-image: -webkit-linear-gradient(
                45deg,
                rgba(255, 255, 255, 0.2) 25%,
                transparent 25%,
                transparent 50%,
                rgba(255, 255, 255, 0.2) 50%,
                rgba(255, 255, 255, 0.2) 75%,
                transparent 75%,
                transparent
        );
    }

    .scrollbar::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
        border-radius: 10px;
        background: #ededed;
    }

    .reserved {
        background: #F6F9FF;
        border-radius: 4px;
        padding: 12px
    }

    .timeStart {
        border-bottom: 1px solid #E5E7EB;
    }

    .timeList {
        min-height: 100px
    }

    .green {
        color: #1FA11A
    }

    .avatar {
        width: 54px;
        height: 54px;
        border-radius: 50%;
        object-fit: cover;
    }

    .ml0 {
        margin-left: 0px !important
    }

    .reserveImg {
        width: 44px;
        height: 44px;
        border-radius: 50%;
        object-fit: cover;
    }
    
    .red {
        color: #D9534F
    }

    .colorc {
        color: #cccccc
    }

    .lineHeight {
        line-height: 100px
    }

    .border {
        border: 1px solid #E5E7EB;
        border-radius: 5px;
        padding: 10px
    }

    .unDate {
        display: inline-block;
        background: #DCE8F6;
        color: #4D88D2;
        /* padding: 4px 15px; */
        margin-left: -15px;
        font-size: 16px;
        font-weight:600;
        border-radius: 0 81px 81px 0;
        height:32px
    }
    .unDate .dateDAta{
        line-height: 32px;
        display: inline-block;
        padding-left: 15px;
    }
    .blue {
        background: rgba(77, 136, 210, 0.1);
        border: 1px solid #4D88D2
    }

    .blueColor {
        color: #4D88D2 !important
    }

    .loading {
        width: 98%;
        height: 95%;
        background: #fff;
        position: absolute;
        opacity: 0.5;
        z-index: 99
    }

    .loading span {
        width: 100%;
        height: 100%;
        display: block;
        background: url("<?php echo Yii::app()->theme->baseUrl?>/images/loading.gif") no-repeat center center;
    }

    .labelBg {
        background: rgba(0, 0, 0, 0.04);
        color: rgba(0, 0, 0, 0.65);
        padding: 5px 10px;
        border: 1px solid rgba(0, 0, 0, 0.15);
        margin-right: 10px;
        font-weight: 400;
        margin-bottom: 10px;
        display: inline-block
    }

    .maxWidth {
        width: 300px
    }

    .ghost {
        background: red !important
    }

    .alertPadding {
        padding: 20px 60px
    }

    .repeat {
        background: #4D88D2;
        color: #fff;
        width: 18px;
        height: 18px;
        text-align: center;
        line-height: 18px;
        border-radius: 50%;
    }

    .translate {
        position: absolute;
        width: 25px;
        height: 25px;
        bottom: -8px;
        left: 40px;
    }

    .example {
        width: 25px;
        height: 25px;
    }

    .exportHide {
        display: none;
        margin-left: -15px
    }

    .pl-0 {
        padding-left: 0;
    }

    .p-0 {
        padding: 0;
    }
    .p-8 {
        padding: 8px;
    }
    .del_day_btn {
        display: inline-block;
        background: #DCE8F6;
        color: #4D88D2;
        font-size: 16px;
        border: 0;
    }
    .attended_iconfont{
        display: inline-block;
        margin-top: 3px;
        margin-left: 6px;
    }
    .reset_padding{
        padding: 0;
    }
    .reset_margin{
        margin: 0;
    }
    .tooltip{
        width: 100px;
    }
    .ml9{
        margin-left: 9px;
    }
    .empty_title{
        margin: auto;
        font-size: 20px;
        font-weight: 500;
        color: #333333;
        line-height: 32px;
    }
    .empty_desc{
        margin: auto;
        font-size: 12px;
        font-weight: 400;
        color: #666666;
        line-height: 20px;
    }
    .add-child-list{
        display: flex;
        flex-wrap:wrap;
        justify-content:space-between;
    }
    .add-child-list-item {
        width: 45%;
        height: 60px;
        margin-top: 8px;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        background-color:#F7F7F8;
        border-radius: 3px;
        color: #333333;
    }

    .add-child-active{
        background-color: #4D88D2;
        border-radius: 3px;
        color: #fff;
    }
    .add-child-list-item-name{
        display: inline-block;
        line-height: 22px;
        font-weight: 500;
        font-size: 14px;
    }
    .add-child-list-item-group{
        margin-left: 10px;
    }
    .add-child-list-item-group span{
        margin-left: 10px;
    }
    .add-child-title{
        margin-bottom: 16px;
        height: 20px;
    }
    .add-child-title-time{
        font-weight: 500;
        color: #333333;
        line-height: 20px;
        font-size: 14px;
        margin-left: 8px;
        margin-right: 8px;
    }
    .add-child-desc{
        margin-bottom: 24px;
        font-weight: 400;
        color: #666666;
        line-height: 17px;
        font-size: 12px;
    }
    .add-child-title-online{
        display: inline-block;
        line-height: 20px;
        font-size: 12px;
        color: #F0AD4E;
        font-weight: 400;
        background: #FCF8E3;
        border-radius: 2px;
        text-align: center;
    }

    .subject-location{
        flex: 1;
        padding-right: 10px;
        border-right: 1px solid #E8EAED;
    }
    .location-time{
        flex:1;
        margin-left: 10px;
    }
    .ml16{
        margin-left: 16px;
    }
    .mb24{
        margin-bottom: 24px;
    }
    .mb20{
        margin-bottom: 20px;
    }
    .mt4{
        margin-top:4px;
    }
    .checkbox-flex{
        display: flex;
        flex-wrap:wrap;
        /*justify-content:space-between;*/
    }
    .set-location-title{
        height: 20px;
        font-size: 14px;
        font-weight: 500;
        color: #333333;
        margin-bottom: 24px;
    }
    .set-location-title-time{
        margin-left: 8px;
    }
    .set-location-item{
        margin-bottom: 24px;
    }
    .set-location-item-subject {
        font-weight: 600;
        color: #333333;
        line-height: 22px;
        font-size: 16px;
    }
    .set-location-item-teacher{
        height: 20px;
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        line-height: 20px;
        margin-top: 14px;
        margin-bottom: 14px;
    }
    .timeCss{
        background: #F7F7F8;
        border-radius: 2px;
        border: 1px solid #E8EAED;
        padding:2px 5px;
        margin-bottom: 5px;
        display: inline-block;
    }
    .timeClose{
        display: inline-block;
        width: 14px;
        height: 14px;
        color: #fff;
        background: #999999;
        font-size: 12px;
        text-align: center;
        line-height: 14px;
        border-radius: 50%;
        margin-left: 10px;
    }
    .copy-show-time-list{
        background-color: #F7F7F8;
        padding: 16px 16px 8px 16px;
    }
    .copy-time-count{
        display: inline-block;
        font-weight: 400;
        color: #333333;
        font-size: 12px;
        height: 20px;
        background: #EBEDF0;
        border-radius: 2px;
        padding: 4px 8px;
    }
    .copy-time-calendar{
        display: flex;
    }

    .copy-time-date-list{
        margin-left: 20px;
    }
    .add-time-period-tip{
        font-size: 14px;
        font-weight: 400;
        color: #666666;
        line-height: 20px;
    }
    .l-h40{
        line-height: 40px;
    }
    .set-time-item{
        margin-bottom: 15px;
    }
    .add-time-period-title{
        border-bottom: 1px solid #E4E7ED;
        padding-bottom: 16px;
        margin-bottom: 16px;
    }
    .existing-address{
        color: #5CB85C;
        margin-top: 3px;
        padding-left:17px
    }
    .existing-address::before{
        content: '';
        display: inline-block;
        height: 8px;
        width: 8px;
        border: 4px solid #5CB85C;
        border-radius:4px;
        margin-right: 2px;
    }
    .on-line{
        line-height: 20px;
        background: #FCF8E3;
        border-radius: 2px;
        padding: 0 8px;
        display: inline-block;
        color: #F0AD4E;
    }
    .p0{
        padding:0
    }
    .reserveInfo{
        line-height: 20px;
        background: #DFF0D8;
        border-radius: 2px;
        color: #5CB85C;
        padding:0 8px;
        display:inline-block
    }
    .vc-date{
        display:none !important
    }
    .core_pop_wrap{
        z-index:9999 !important
    }
    .dropdownLeft{
        right:0 !important;
        left:auto !important;
        min-width:auto !important
    }
    .font17{
        font-size:17px
    }
    .flexText{
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 0;
    }
    .translate20{
        position: absolute;
        width: 21px;
        height: 21px;
        bottom: -6px;
        left: 38px;
    }
    .reserveImg40 {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
    }
</style>

<div id="classPtcSet" v-cloak>
    <div class="tab-content">
        <div class="col-md-12 col-sm-12 mb20"></div>
        <!-- <div class="tab-pane active" id="reserve"> -->
            <div class='col-md-2 col-sm-12 '>
                <div class="list-group">
                    <span class="list-group-item " :class='classId==list.id?"active":""'
                          v-for='(list,index) in classListData' @click='classChild(list.id)'>{{list.title}}</span>
                </div>
            </div>
            
        <!-- </div> -->
        <div class='col-md-10 col-sm-12 mb20 p0'>
            <div class='loading ' v-if='classLoading'>
                <span></span>
            </div>
            <div v-if='classId!="" && Object.keys(overview).length!=0'>
                <!--翻译图标-->
                <div class='col-md-12 col-sm-12 mb20'>
                    <span class='font14 color3'><?php echo Yii::t('ptc', 'Legend: ') ?></span> <img
                            src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/A-E.png' ?>"
                            class='example' alt=""> <span class='color6'>{{overview.translationConfig[1].title}}</span>
                    <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/E-A.png' ?>"
                        class='example ml20' alt=""> <span
                            class='color6'>{{overview.translationConfig[2].title}}</span>
                    <button  type="button" class="btn btn-link ml20 pull-right" @click='classChild(classId)'><span class='glyphicon glyphicon-refresh'></span> <?php echo Yii::t('global','Refresh') ?></button>
                    <button  type="button" class="btn btn-primary ml20 pull-right" :disabled='exportBtn' @click='exportData'><?php echo Yii::t('ptc','Export Daily Visitors List') ?></button>
                </div>
                <!--预约列表-->
                <div class='col-md-8 col-sm-12'>
                    <div class="panel panel-default">
                        <div class="panel-heading font14 color3">
                            <?php echo Yii::t("ptc", "Scheduled"); ?>（{{overview.bookedChildIds.length}}）
                            <div class="pt10">
                                <span class='color6 mr20' v-for='(list,id,i) in overview.subjectConfig'>
                                    <span class='glyphicon glyphicon-user mr5'></span>{{list}}：
                                    <span  class='color3' v-if='overview.subjectTeacher[id]'>{{overview.teachersInfo[overview.subjectTeacher[id]].name}}</span>
                                    <span  class='color9' v-else> <?php echo Yii::t('user', 'No teacher assigned') ?></span>
                                </span>
                            </div>
                        </div>
                        <div class="panel-body">
                            <div v-if=' Object.keys(overview.subjectTeacher).length==3'>
                                <div class="mb24">
                                    <button type="button" class="btn btn-primary btn-sm " @click='setTime("show")'>
                                        <span class="iconfont icon-plus-circle mr4 font12"></span>
                                        <?php echo Yii::t("ptc", "Create PTC schedules"); ?>
                                    </button>
                                </div>
                                <div v-if=' Object.keys(overview.ptcData).length!=0'>
                                    <div v-for='(date,key,idx) in overview.ptcData'>
                                        <div class="flex align-items">
                                            <div class="flex1 pl-0">
                                                <span class='unDate' style="position: relative;padding-right: 6px;">
                                                    <span class='dateDAta'>{{date[0]['day_title']}}</span>  
                                                    <button class="del_day_btn" type="button" id="dropdownMenu1"
                                                            data-toggle="dropdown" aria-haspopup="true"
                                                            aria-expanded="true">
                                                        <span class="iconfont icon-ellipsis-vertical"
                                                            style="font-size: 13px"></span>
                                                    </button>
                                                    <ul class="dropdown-menu" style="min-width: auto;left: 90px"
                                                        aria-labelledby="dropdownMenu1">
                                                        <li><a href="javascript:;" @click="delDayPlan(date[0]['timestamp_start'])"><?php echo Yii::t("global", "Delete"); ?></a></li>
                                                    </ul>
                                                </span>
                                            </div>
                                            <div class="text-right">
                                                <span class="mr15 text-primary cur-p font14"  @click="reviseTime(date,'show')">
                                                    <span class="iconfont icon-clock font14"></span>
                                                    <?php echo Yii::t("ptc", "schedules"); ?>
                                                </span>
                                                <span class="ml15 text-primary cur-p font14" @click="copyTime(date,'show')">
                                                    <span class="iconfont icon-a-icon-copy font14"></span>
                                                    <?php echo Yii::t("ptc", "Copy schedules to"); ?>
                                                </span>
                                            </div>
                                        </div>
                                        <!--各个时间段-->
                                        <div v-for='(item,i) in date' class='mt20'>
                                            <div class=' font14 color3 mb16'>
                                                <span class="iconfont icon-clock"></span>
                                                <strong class='font14'>{{item.start}}-{{item.end}}</strong>
                                                <span v-if="item.meet_type == 0" class='font12 ml8 text-primary cur-p'  @click="setLocation(item,'show')"><?php echo Yii::t("ptc", "Set location"); ?></span>
                                                <span v-if="item.meet_type == 1" class=" font12 on-line ml8"><?php echo Yii::t("ptc", "Online Meet"); ?></span>
                                            </div>
                                            <div class='ml15 mb10'>
                                                <span class="label label-default labelBg"
                                                    v-for='(loca,sub,i) in item.location'>{{overview.subjectConfig[sub]}}：{{loca}}</span>
                                            </div>
                                            <div>
                                                <div class='col-md-4 col-sm-12' v-for='(list,index) in item.items'>
                                                    <div class="reset_padding reset_margin">
                                                        <div class="media-left mr12 pull-left media-middle">
                                                            <a href="javascript:void(0)" class=''>
                                                                <img :src="overview.childInfo[list[0].child_id].avatar"
                                                                    data-holder-rendered="true" class="reserveImg">
                                                                <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/E-A.png' ?>"
                                                                    class='translate' alt=""
                                                                    v-if='overview.translation[list[0].child_id]==3'>
                                                                <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/A-E.png' ?>"
                                                                    class='translate' alt=""
                                                                    v-if='overview.translation[list[0].child_id]==2'>
                                                            </a>
                                                        </div>
                                                        <div class="media-middle reset_padding reset_margin pull-left pt4">
                                                            <div class=" font14 color3">
                                                                <label class="pull-left" style='line-height:1'>{{overview.childInfo[list[0].child_id].name}} </label>
                                                                <span>
                                                                <div class="btn-group ml9 pull-left" >
                                                                    <button type="button" class="btn btn-default btn-xs dropdown-toggle"
                                                                            data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" style="padding: 0;line-height:1">
                                                                        <span class="iconfont icon-more" style="font-size: 15px"></span>
                                                                    </button>
                                                                    <ul class="dropdown-menu">
                                                                        <li><a href="javascript:;" @click="setAttendStatus(list[0].child_id,key,list[0]['start'],1)"><?php echo Yii::t("ptc", "PTC occured"); ?></a></li>
                                                                        <li><a href="javascript:;" @click="setAttendStatus(list[0].child_id,key,list[0]['start'],2)"><?php echo Yii::t("ptc", "PTC not occured"); ?></a></li>
                                                                        <li><a href="javascript:;" v-if="child_attended[key][i][list[0].child_id] != 0"  @click="setAttendStatus(list[0].child_id,key,list[0]['start'],0)"><?php echo Yii::t("ptc", "Reset status"); ?></a></li>
                                                                        <li role="separator" class="divider" v-if="child_attended[key][i][list[0].child_id] == 0"></li>
                                                                        <li><a href="javascript:;" @click="reset(list[0].child_id)" v-if="child_attended[key][i][list[0].child_id] == 0" ><?php echo Yii::t("reg", "Cancel student schedule"); ?></a></li>
                                                                    </ul>
                                                                </div>
                                                                </span>
                                                                <span class="ml10 pull-left">
                                                                    <span v-if="child_attended[key][i][list[0].child_id] == 1"  :key='count' class='pull-left' style='margin-top:-3px' >
                                                                        <span  :key='index' class="iconfont icon-check-circle-fill"   data-toggle="tooltip" data-placement="top" title="<?php echo Yii::t("ptc", "PTC occured"); ?>"></span>
                                                                    </span>
                                                                    <span v-if="child_attended[key][i][list[0].child_id] == 2"   :key='count'>
                                                                        <span  :key='index' style='color:#D9534F'   class="glyphicon glyphicon-exclamation-sign font17" data-toggle="tooltip" data-placement="top" title="<?php echo Yii::t("ptc", "PTC not occured"); ?>"></span>
                                                                    </span>
                                                                    
                                                                </span>
                                                                <div class='clearfix'></div>
                                                            </div>
                                                            <div class="color6 font12 mt4"><span v-for='(sub,len) in list'>{{sub.subject}} <span
                                                                            v-if='len!=list.length-1'>- </span> </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class='col-md-2 col-sm-12' v-if="Object.keys(item.items).length < 3 ">
                                                    <button type="button" class="btn btn-default btn-sm font14" @click="addChild(item,'show')">
                                                        <span class="iconfont icon-plus-circle pr4 font14" style="font-size:13px"></span>
                                                        <?php echo Yii::t("ptc", "Add student"); ?>
                                                    </button>
                                                </div>
                                                <div class='clearfix'></div>
                                            </div>
                                            <hr>
                                        </div>
                                    </div>
                                </div>
                                <div class="alert" role="alert" style="display: grid;" v-else>
                                    <div class="empty_title"><label><?php echo Yii::t("ptc", "Create PTC schedules"); ?></label> </div>
                                    <div class="empty_desc"><?php echo Yii::t("ptc", "Please set schedule time first"); ?></div>
                                    <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/date-ptc.png' ?>" style='width:80px;margin:0 auto' alt="" class='text-center mt16'>
                                </div>
                            </div>
                            <!----创建时间------>
                           <div v-else class="alert" role="alert" style="display: grid;">
                                <div class="empty_title"><label><?php echo Yii::t("ptc", "Class subject owners not taken"); ?></label> </div>
                                <div class="empty_desc"><?php echo Yii::t("ptc", "Owners are needed before creating schedules, to check time conflicts"); ?></div>
                                <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/PTCuser.png' ?>" style='width:80px;margin:0 auto' alt="" class='text-center mt16'>
                           </div>
                        </div>
                    </div>
                </div>
                <div class='col-md-4 col-sm-12'>
                    <!--未预约-->
                    <div class="panel panel-default">
                        <div class="panel-heading  font14 color3"><?php echo Yii::t("ptc", "Not scheduled "); ?>
                            （{{overview.unBookedChildIds.length}}）
                        </div>
                        <div class="panel-body">
                            <div v-if='overview.unBookedChildIds.length!=0' class='row'>
                                <div class='col-md-12 col-sm-12 col-lg-6 mb20' v-for='(list,index) in overview.unBookedChildIds'>
                                    <div class='flex align-items'>
                                        <img :src="overview.childInfo[list].avatar" alt="" class="reserveImg40">
                                        <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/E-A.png' ?>"
                                            class='translate20' alt="" v-if='overview.translation[list]==3'>
                                        <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/A-E.png' ?>"
                                            class='translate20' alt="" v-if='overview.translation[list]==2'>
                                        <span class="color3 font14 ml5 flex1 flexText">{{overview.childInfo[list].name}}</span>
                                        <div class="btn-group ml5">
                                            <button type="button" class="btn btn-link btn-xs dropdown-toggle"
                                                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" style="padding: 0;">
                                                <span class="iconfont icon-more" style="font-size: 15px"></span>
                                            </button>
                                            <ul class="dropdown-menu dropdownLeft">
                                                <li><a href="javascript:;" @click="setOnlineChild(list,1)"><?php echo Yii::t("ptc", "Mark online PTC request"); ?></a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="alert alert-warning" role="alert"
                                v-else><?php echo Yii::t("ptc", "No Data"); ?></div>
                        </div>
                    </div>
                    <!--申请线上家长会的学生-->
                    <div class="panel panel-default">
                        <div class="panel-heading  font14 color3"><?php echo Yii::t("ptc", "Students of online PTC request"); ?>
                            （{{overview.onlineChildIds.length}}）
                        </div>
                        <div class="panel-body">
                            <div>
                            <div class="alert alert-warning font14" role="alert">老师需要与要求线上家长会的家长沟通时间，在系统中增加该时段（勾选线上），并将学生分配到沟通好的时间段中。请注意家长不能自行预约线上PTC。</div>
                            
                            </div>
                            <div v-if='overview.onlineChildIds.length!=0' class='row'>
                                <div class='col-md-12 col-sm-12 col-lg-6 mb20' v-for='(list,index) in overview.onlineChildIds'>
                                    <div class='flex align-items'>
                                        <img :src="overview.childInfo[list].avatar" alt="" class="reserveImg40">
                                        <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/E-A.png' ?>"
                                            class='translate20' alt="" v-if='overview.translation[list]==3'>
                                        <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/A-E.png' ?>"
                                            class='translate20' alt="" v-if='overview.translation[list]==2'>
                                        <span class="color3 font14 ml5 flex1 flexText">{{overview.childInfo[list].name}}</span>
                                        <div class="btn-group ml5">
                                            <button type="button" class="btn btn-link btn-xs dropdown-toggle"
                                                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" style="padding: 0;">
                                                <span class="iconfont icon-more" style="font-size: 15px"></span>
                                            </button>
                                            <ul class="dropdown-menu dropdownLeft">
                                                <li><a href="javascript:;" @click="setOnlineChild(list,0)"><?php echo Yii::t("ptc", "Cancel online PTC request"); ?></a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="alert alert-warning" role="alert"
                                v-else><?php echo Yii::t("ptc", "No Data"); ?></div>
                        </div>
                    </div>
                </div>
            </div>
            <div v-else-if='classListData.length!=0' class='col-md-12 col-sm-12'>
                <div class="alert alert-warning"
                    role="alert"><?php echo Yii::t('ptc', 'Select a class to continue') ?></div>
            </div>
        </div>
    </div>
    <!--确认取消预约对话框-->
    <div class="modal fade" id="resetModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("ptc", "Cancel Schedule");?></h4>
                </div>
                <div class="modal-body">
                    <div ><?php echo Yii::t("ptc", "Confirm to cancel this schedule?");?></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>

                    <button type="button"  class="btn btn-primary" @click='reset("model")'><?php echo Yii::t("global", "OK");?></button>
                </div>
            </div>
        </div>
    </div>
    <!--确认删除对话框-->
    <div class="modal fade" id="delPTCModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("global", "Delete");?></h4>
                </div>
                <div class="modal-body">
                    <div ><?php echo Yii::t("ptc", "确认删除吗？");?></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>

                    <button type="button"  class="btn btn-primary" @click="delDayPlan('model')"><?php echo Yii::t("global", "OK");?></button>
                </div>
            </div>
        </div>
    </div>
    <!--提示窗-->
    <div class="modal fade" id="alertModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" style="z-index: 9999;">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel">
                        <?php echo Yii::t("ptc", "Warning"); ?>
                    </h4>
                </div>
                <div class="modal-body">
                    <div v-html='alertContent'>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "OK");?></button>
                </div>
            </div>
        </div>
    </div>
    <!--添加学生dialog-->
    <div class="modal fade" id="addChildModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("ptc", "Add student"); ?></h4>
                </div>
                <div class="modal-body">
                    <div class="add-child-title">
                        <span class="iconfont icon-clock"></span>
                        <span class="add-child-title-time">{{addChildDateItem.day_title}} {{addChildDateItem.start}}-{{addChildDateItem.end}}</span>
                        <span v-if="addChildDateItem.meet_type==1" class="add-child-title-online"><?php echo Yii::t("ptc", "Online Meet"); ?></span>
                    </div>
                    <div class="add-child-desc">
                        <span v-if="addChildDateItem.meet_type==1"><?php echo Yii::t("ptc", "Only students of online PTC request displayed, add one student each time"); ?></span>
                        <span v-else><?php echo Yii::t("ptc", "Only none scheduled students displayed, add one student each time"); ?></span>
                    </div>
                    <div v-if="canAddedChildIds.length != 0" class="add-child-list">
                        <div  v-for="(childid,index) in canAddedChildIds"  :id="'add-child-'+childid" class="add-child-list-item" @click="chooseChild(childid)"><!--add-child-active-->
                            <div class="add-child-list-item-group">
                                <img :src="overview.childInfo[childid].avatar" alt="" class="reserveImg">
                                <span class="add-child-list-item-name">{{overview.childInfo[childid].name}}</span>
                            </div>
                        </div>
                    </div>
                    <div class="alert alert-warning" role="alert" v-else >
                        <?php echo Yii::t("ptc", "No Data"); ?>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button"  class="btn btn-primary" @click='addChild("","commit")'><?php echo Yii::t("global", "OK");?></button>
                </div>
            </div>
        </div>
    </div>
    <!--创建时间dialog-->
    <div class="modal fade" id="setTimeModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("ptc", "Create PTC schedules"); ?></h4>
                </div>
                <div class="modal-body">
                    <div class="set-day-group block">
                        <p class="color3 font14"><label><?php echo Yii::t("ptc", "Select a date"); ?></label></p>
                        <v-date-picker v-model="setTimeDay">
                            <template v-slot="{ inputValue, inputEvents ,updateValue}">
                                <input class="form-control length_4":value="inputValue" v-on="inputEvents" />
                            </template>
                        </v-date-picker>
                    </div>
                    <div class="mt20 J_check_wrap">
                        <p class=""><label class='color3 font14'><?php echo Yii::t("ptc", "Add schedules"); ?></label> </p>
                        <div class="col-xs-12 reset_padding add-time-period-title">
                            <span class="col-xs-6 p0  add-time-period-tip"><?php echo Yii::t("ptc", "Set starting time, each schedule takes 60 minutes"); ?></span>
                            <span class="col-xs-4 p0 add-time-period-tip"></span>
                            <div class="col-xs-2 p0">
                                <label class="checkbox-inline">
                                    <input type="checkbox" v-model="J_check_all"  @change='handleCheckAllChange("add",$event)'>
                                    <span class="add-time-period-tip"><?php echo Yii::t("ptc", "All"); ?></span>
                                </label>
                            </div>
                        </div>
                        <div class="col-xs-12 reset_padding set-time-item" v-for="(value,key,index) in initSetTimeItem">
                            <div class="col-xs-6 p0">
                                <v-date-picker v-model="value.start" mode="time" is24hr>
                                    <template v-slot="{ inputValue, inputEvents }">
                                        <input class="form-control length_3 pull-left" :value="inputValue" v-on="inputEvents"/>
                                    </template>
                                </v-date-picker>
                                <span class="glyphicon glyphicon-trash pt10 ml10 text-primary cur-p"  @click="delTimeItem(key,'set')" v-if="value.reserve==false"></span>
                            </div>
                            <div class="col-xs-4 p0 l-h40"></div>
                            <div class="col-xs-2 p0 reset_padding l-h40">
                                <label class="checkbox-inline">
                                    <input type="checkbox" v-model="value.meet_type" @change="handleCheckedCitiesChange('add',$event)">
                                    <span class="ml-2"><?php echo Yii::t("ptc", "Online Meet"); ?></span>
                                </label>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                    </div>
                    <div>
                        <span class='text-primary cur-p font14' @click="addTimePeriod('create')"><span class="iconfont icon-plus-circle mr4"></span> <?php echo Yii::t("ptc", "Add more schedules"); ?></span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button"  class="btn btn-primary" @click='setTime("commit")'><?php echo Yii::t("global", "OK");?></button>
                </div>
            </div>
        </div>
    </div>
    <!--修改时间段-->
    <div  class="modal fade" id="reviseTimeModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" ria-hidden="false"  data-backdrop="static" >
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("ptc", "schedules"); ?></h4>
                </div>
                <div class="modal-body">
                    <div class="set-day-group block">
                        <p class="color3 font14"><label><?php echo Yii::t("ptc", "Select a date"); ?></label></p>
                        <v-date-picker v-model="initReviseTimeDay">
                            <template v-slot="{ inputValue, inputEvents }">
                                <input class="form-control length_4":value="inputValue" v-on="inputEvents" disabled/>
                            </template>
                        </v-date-picker>
                    </div>
                    <div class="mt20 J_check_wrap">
                        <p class="font14 color3 mb16"><label><?php echo Yii::t("ptc", "Add schedules"); ?></label> </p>
                        <div class="col-xs-12 reset_padding add-time-period-title">
                            <span class="col-xs-6 p0 add-time-period-tip"><?php echo Yii::t("ptc", "Set starting time, each schedule takes 60 minutes"); ?></span>
                            <span class="col-xs-4 p0 add-time-period-tip"> <?php echo Yii::t("admissions", "Schedule status"); ?></span>
                            <div class="col-xs-2 p0">
                                <label class="checkbox-inline">
                                    <input type="checkbox" v-model='checkAll' @change='handleCheckAllChange("set",$event)'>
                                    <span class="add-time-period-tip"><?php echo Yii::t("ptc", "All"); ?></span>
                                </label>
                            </div>
                        </div>
                        <div class="col-xs-12 reset_padding set-time-item" v-for="(value,key,index) in initReviseTimeItem">
                            <div class="col-xs-6 p0">
                                <v-date-picker v-model="value.start" mode="time"  ref='picker' popover.visibility="click"  is24hr>
                                    <template v-slot="{ inputValue, inputEvents }">
                                        <input class="form-control length_3 pull-left" :value="inputValue" v-on="inputEvents" :disabled="value.reserve"/>
                                    </template>
                                </v-date-picker>
                                <span class="glyphicon glyphicon-trash pt10 ml10 text-primary cur-p"  @click="delTimeItem(key,'revise')" v-if="value.reserve==false"></span>
                            </div>
                            <div class="col-xs-4 p0 ">
                                <span v-if="value.reserve==true" class="reserveInfo" ><?php echo Yii::t("ptc", "Already scheduled"); ?></span>
                            </div>
                            <div class="col-xs-2 reset_padding" v-if="value.reserve == false">
                                <label class="checkbox-inline">
                                    <input type="checkbox" v-model="value.meet_type"   @change="handleCheckedCitiesChange('set',$event)">
                                    <span class="ml-2"><?php echo Yii::t("ptc", "Online Meet"); ?></span>
                                </label>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                    </div>
                    <div>
                        <span class='text-primary cur-p font14' @click="addTimePeriod('revise')"><span class="iconfont icon-plus-circle mr4"></span> <?php echo Yii::t("ptc", "Add more schedules"); ?></span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button"  class="btn btn-primary" @click='reviseTime("","commit")'><?php echo Yii::t("global", "OK");?></button>
                </div>
            </div>
        </div>
    </div>
    <!--设置的地点-->
    <div class="modal fade" id="setLocationModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" ria-hidden="false"  data-backdrop="static" >
        <div class="modal-dialog" role="document"  style='width:800px'>
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("ptc", "Set location");?></h4>
                </div>
                <div class="modal-body" style="display: flex">
                    <div class="subject-location">
                        <div class="set-location-title">
                            <span class="iconfont icon-clock"></span>
                            <span class="set-location-title-time">{{setLocationItem.day_title}} {{setLocationItem.start}}-{{setLocationItem.end}}</span>
                        </div>
                        <div v-for="(teacherId,subjectId,index) in overview.subjectTeacher" class="set-location-item">
                            <p class="set-location-item-subject">{{overview.subjectConfig[subjectId]}}：</p>
                            <div class="set-location-item-teacher">
                                <img :src="overview.teachersInfo[teacherId]['photoUrl']" alt="" class="reserveImg" style="width: 24px;height: 24px">
                                <span>{{overview.teachersInfo[teacherId]['name']}}</span>
                            </div>
                            <input type="text" class="form-control ui-autocomplete-input" placeholder="<?php echo Yii::t("ptc", "Input meeting location"); ?>" autocomplete="off" v-model="setLocations[subjectId]" :value="setLocations[subjectId]">
                        </div>
                    </div>
                    <div class="location-time">
                        <label class='color3 font14'><?php echo Yii::t("ptc", "Sync locations to other schedules. (not applicable to online schedules)"); ?></label>
                        <div v-if="tree_data.length != 0" id="locationTimeList" class="J_check_wrap">
                            <div class='mb8 mt12'>
                                <label class="checkbox-inline">
                                    <input type="checkbox" v-model='locationAllData' @change='locationAll($event)'>
                                    <?php echo Yii::t('global','Select All');?>
                                </label>   
                            </div>
                            <div v-for="(value,key,index) in tree_data" class="mb16">
                                <div>
                                    <label class="checkbox-inline">
                                        <input type='checkbox' v-model='value.checked'  @change='locationCheckAllChange(value,$event)' />{{value.label}}
                                    </label>
                                </div>
                                <div class="checkbox-flex mt8">
                                    <div v-for="(v,k,i) in value['children']" class='ml16'>
                                        <label class="checkbox-inline">
                                            <input type='checkbox'v-model='v.checked' @change="locationCheckedCitiesChange(value,$event)" /> {{v.label}}
                                        </label>
                                        <div v-if="v.haveLocation == true" class="existing-address"><?php echo Yii::t("ptc", "Location exists"); ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="alert alert-warning" role="alert" v-else >
                            <?php echo Yii::t("ptc", "No Data"); ?>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button"  class="btn btn-primary" @click='setLocation("","commit")'><?php echo Yii::t("global", "OK");?></button>
                </div>
            </div>
        </div>

    </div>
    <!--复制时间段-->
    <div class="modal fade" id="copyTimeModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" ria-hidden="false"  data-backdrop="static" >
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("ptc", "Copy schedules to"); ?></h4>
                </div>
                <div class="modal-body">
                    <div class="copy-show-time-list">
                        <div class='flex'>
                            <div>
                                <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/time.png' ?>" class='mt2' >
                            </div>
                            <div class='flex1 ml8'>
                                <div class='color3'>
                                    <label class="color3 font14">{{copyItemFirst.day_title}}</label>
                                    <span class="copy-time-count ml8">{{copyItem.length}}<?php echo Yii::t("ptc", " slots assigned");?></span>
                                </div>
                                <div>
                                    <span v-for="(item,index) in copyItem" class="mr8 mb4 color6">{{item.start}}-{{item.end}}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class='color3 font14 mt20'><strong><?php echo Yii::t("ptc", "Copy to"); ?></strong></div>
                    <div class='mt8 mb8 color6 font14'><?php echo Yii::t("ptc", "Only none schedules dates can be copied to"); ?></div>
                    <div class="copy-time-calendar">
                        <div class="copy-time-calendar-container">
                        <v-calendar :attributes='addAttrsTime' @dayclick="pickDate" :disabled-dates='disabledDates' ></v-calendar>
                        </div>
                        <div class="copy-time-date-list">
                            <div  v-for="(list,index) in addTimeList" class='mb10'>
                                <span class='timeCss'>{{list}}
                                    <span class='timeClose' @click='closeTime(index)'>×</span>
                                </span>
                            </div>
                        </div>
                    </div>
                    
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button"  class="btn btn-primary" @click='copyTime("","commit")'><?php echo Yii::t("global", "OK");?></button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="exportModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("ptc", "Export Daily Visitors List");?></h4>
            </div>
            <div class="modal-body">      
                <div class='mb10 color6 font14'><?php echo Yii::t("ptc", "请选择要导出的名单");?></div>
                <div class='col-md-3 col-sm-3 mb10 ' v-for='(list,index) in exportDays'>
                    <div :id='"export"+index' class='loading exportHide'>
                        <span></span>
                    </div>
                    <div class='border text-center cur-p font14' @click='exportData(list.day,index)'>
                        <div class='color3'><label>{{list.day}}</label></div>
                        <div class='color6'>{{list.weekday}}</div>
                    </div>
                </div>
                <div class='clearfix'></div>
            </div>
            </div>
        </div>
    </div>
</div>
<div class='clearfix'></div>
<script>
    var startyear = '<?php echo $startyear?>';
    var semester = '<?php echo $semester?>';
    var class_list = '<?php echo json_encode($class_list)?>';
    class_list = JSON.parse(class_list);
    $(function () {
        $('body').tooltip({
            selector: '[data-toggle="tooltip"]'
        });
    })
    var container = new Vue({
        el: "#classPtcSet",
        data: {
            alertContent:'',
            classListData: [],
            classId: '',//选中的classid
            classLoading: false,//false展示右侧元素
            overview: {},//首页数据
            resetChildId:'',//重置预约状态的孩子id（取消预约）
            bookedChildIds:[],//已经预约的学生id
            unBookedChildIds:[],//未预约的学生id
            onlineChildIds:[],//选择线上会议的学生
            canAddedChildIds:[],//可以添加的学生id
            addChildDateItem:{},//添加孩子的时间段信息
            addChildId:'',//要添加的孩子id
            setTimeDay:new Date(),//设置新的时间段的天
            setTimeItem:[],//设置新的时间段的时间段信息
            initSetTimeItem:[],//创建时间内设置时间段item内的元素
            initSetTimeItemBase:{
                'start':'',//开始时间
                'meet_type':'',//
                'reserve':false,//是否有学生预约了
            },
            intSetTimeItemList:5,//设置新的时间段默认展示的时间段个数
            ptcDataList:'',//班级的所有时间段信息
            initReviseTimeDay:'',//修改时间
            initReviseTimeItem:[],//修改时间的时间段元素
            delTimeItemIds:[],//要删除的时间段
            setLocationItem: {},
            subjectIdMap:{
                1:'language',
                2:'math',
                3:'english'
            },
            locations:'',
            setLocations:[],
            tree_data:[],
            copyItem:[],
            copyItemFirst:{},
            fromData:{},
            addTimeList:[],
            commitTimeList:[],
            addAttrsTime:[],
            disabledDates:[],
            checkAll:false,
            J_check_all:false,
            locationAllData:false,
            delDate:{},
            count:0,
            child_attended:[],
            exportDays:[],
            exportBtn:false
        },
        created() {
            if (class_list.length != 0) {
                this.classListData = class_list
            }
        },
        mounted() {
            $('#setLocationModal').on('shown.bs.modal',
                function() {
                    head.Util.checkAll();
            })
            $('#reviseTimeModal').on('shown.bs.modal',
                function() {
                    head.Util.checkAll();
            })


        },
        watch:{
            //监听创建时间的日期数据
            setTimeDay(newValue,oldValue){
                var newDate = new Date(newValue)
                var year = newDate.getFullYear();
                var month = newDate.getMonth() ;
                var day = newDate.getDate();
                this.initSetTimeItemBase.start = null;
                // if(newValue !== oldValue){
                //     this.initSetTimeItem = [];
                // }
            }
        },
        methods: {
            classChild(id) {
                this.classId = id
                let that = this
                this.classLoading = true
                globalInitDataDisabled = false
                this.canAddedChildIds=[]
                $.ajax({
                    url: '<?php echo $this->createUrl("ptcSchedule/PrimarySchoolScheduleOverview") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        startyear: startyear,
                        semester: semester,
                        class_id: id
                    },
                    success: function (data) {
                        if (data.state == 'success') {
                            that.overview = data.data
                            that.unBookedChildIds = data.data.unBookedChildIds
                            that.onlineChildIds = data.data.onlineChildIds
                            that.bookedChildIds = data.data.bookedChildIds
                            that.ptcDataList = data.data.ptcData
                            that.classLoading = false
                            that.child_attended = data.data.child_attended
                        } else {
                            that.classLoading = false
                            // resultTip({error: 'warning', msg: data.message});
                            that.alertContent = data.message
                            $("#alertModal").modal('show')
                        }
                    },
                    error: function (data) {
                        that.classLoading = false
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            delDayPlan(dayTimestamp) {
                if(dayTimestamp!='model'){
                    this.delDate=dayTimestamp
                    $("#delPTCModal").modal('show')
                    return;
                }
                let that = this
                $.ajax({
                    url: '<?php echo $this->createUrl("ptcSchedule/delScheduleClass") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        startyear: startyear,
                        semester: semester,
                        class_id: that.classId,
                        date: this.delDate
                    },
                    success: function (data) {
                        if (data.state == 'success') {
                            resultTip({"msg": "<?php echo Yii::t('global', 'Success!'); ?>"})
                            that.classChild(that.classId)
                            that.classLoading = false
                            $("#delPTCModal").modal('hide')
                        } else {
                            that.classLoading = false
                            // resultTip({error: 'warning', msg: data.message});
                            that.alertContent = data.message
                            $("#alertModal").modal('show')
                        }
                    },
                    error: function (data) {
                        that.classLoading = false
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            setAttendStatus(child_id,day,start,attended) {
                let that = this
                $.ajax({
                    url: '<?php echo $this->createUrl("ptcSchedule/setAttendStatus") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        startyear: startyear,
                        semester: semester,
                        child_id: child_id,
                        day: day,
                        start:start,
                        attended:attended,
                        class_id:that.classId,
                    },
                    success: function (data) {
                        if (data.state == 'success') {
                            resultTip({"msg": "<?php echo Yii::t('global', 'Success!'); ?>"})
                            that.classChild(that.classId)
                            that.classLoading = false
                            that.count++
                        } else {
                            that.classLoading = false
                            // resultTip({error: 'warning', msg: data.message});
                            that.alertContent = data.message
                            $("#alertModal").modal('show')
                        }
                    },
                    error: function (data) {
                        that.classLoading = false
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            reset(data){
                if(data!='model'){
                    this.resetChildId=data
                    $('#resetModal').modal('show')
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("PtcSchedule/resetStudentPtc") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        dept: 1,
                        child_id: this.resetChildId,
                        class_id:that.classId,
                        semester: semester,
                    },
                    success: function(data) {
                        if(data.state=='success'){
                            that.classChild(that.classId)
                            resultTip({
                                msg: "<?php echo Yii::t('global', 'Success!'); ?>"
                            });
                            $('#resetModal').modal('hide')
                        }else{
                            // resultTip({error: 'warning', msg:data.message});
                            that.alertContent = data.message
                            $("#alertModal").modal('show')
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            chooseChild(childid){
                $(".add-child-list-item").removeClass('add-child-active')
                $("#add-child-"+childid).toggleClass("add-child-active");
                let that = this
                that.addChildId = childid;
            },
            addChild(item,type){
                let that=this
                if(type === 'show'){
                    that.addChildDateItem = item;
                    if(item.meet_type === 0){
                        //线下的显示未预约的
                        that.canAddedChildIds = that.unBookedChildIds
                    }else{
                        //线上家长会只展示预约线上的学生
                        let refineOnlineChildIds = []
                        that.onlineChildIds.forEach(function (val,index){
                            if(!that.bookedChildIds.includes(val.toString())){
                                refineOnlineChildIds.push(val)
                            }
                        })
                        that.canAddedChildIds = refineOnlineChildIds
                    }
                    $('#addChildModal').modal('show')
                    return;
                }
                if(type === 'commit'){
                    let data = {
                        "schedule_id":that.addChildDateItem.schedule_id,//首页接口schedule_id 字段的值
                        "startyear": startyear,
                        "semester": semester,
                        "child_id": that.addChildId
                    }
                    $.ajax({
                        url: '<?php echo $this->createUrl("PtcSchedule/addChildToItem") ?>',
                        type: "post",
                        dataType: 'json',
                        data: data,
                        success: function(data) {
                            if(data.state=='success'){
                                that.classChild(that.classId)
                                resultTip({
                                    msg: "<?php echo Yii::t('global', 'Success!'); ?>"
                                });
                                $('#addChildModal').modal('hide')
                                $(".add-child-list-item").removeClass('add-child-active')
                            }else{
                                // resultTip({error: 'warning', msg:data.message});
                                that.alertContent = data.message
                                $("#alertModal").modal('show')
                            }
                        },
                        error:function(data){
                            resultTip({error: 'warning', msg: '请求错误'});
                        }
                    })
                }
            },
            addTimePeriod(type){
                let that=this
                if(type === 'revise'){
                    that.initReviseTimeItem.push(JSON.parse(JSON.stringify(that.initSetTimeItemBase)))
                }
                if(type === 'create'){
                    that.initSetTimeItem.push(JSON.parse(JSON.stringify(that.initSetTimeItemBase)))
                }
                this.$nextTick(function (){
                    head.Util.checkAll();
                })
            },
            setTime(type) {
                let that=this
                if(type === 'show'){
                    that.setTimeDay = new Date()
                    that.initSetTimeItem = [
                        {
                            'start':'',//开始时间
                            'meet_type':'',//
                            'reserve':false,//是否有学生预约了
                        },
                        {
                            'start':'',//开始时间
                            'meet_type':'',//
                            'reserve':false,//是否有学生预约了
                        },
                        {
                            'start':'',//开始时间
                            'meet_type':'',//
                            'reserve':false,//是否有学生预约了
                        },
                        {
                            'start':'',//开始时间
                            'meet_type':'',//
                            'reserve':false,//是否有学生预约了
                        },
                        {
                            'start':'',//开始时间
                            'meet_type':'',//
                            'reserve':false,//是否有学生预约了
                        }
                    ]
                    $('#setTimeModal').modal('show')
                    return;
                }
                if(type === 'commit'){
                    let setCommitTimeDay =  Date.parse(that.setTimeDay) / 1000; //时间戳
                    let setCommitTimeItem = []
                    for (const setTimeDayKey in that.initSetTimeItem) {
                        if(that.initSetTimeItem[setTimeDayKey]['start']){
                            setCommitTimeItem.push({
                                start:Date.parse(that.initSetTimeItem[setTimeDayKey]['start']) / 1000,
                                meet_type:that.initSetTimeItem[setTimeDayKey]['meet_type'] ? 1 : 0
                            })
                        }
                    }
                    let data = {
                        startyear: startyear,
                        semester: semester,
                        day: setCommitTimeDay,
                        item: setCommitTimeItem,
                        class_id:that.classId,
                    }
                    $.ajax({
                        url: '<?php echo $this->createUrl("PtcSchedule/saveScheduleClass") ?>',
                        type: "post",
                        dataType: 'json',
                        data: data,
                        success: function(data) {
                            if(data.state=='success'){
                                that.classChild(that.classId)
                                resultTip({
                                    msg: "<?php echo Yii::t('global', 'Success!'); ?>"
                                });
                                $('#setTimeModal').modal('hide')
                            }else{
                                if(data.data.date){
                                    var teacherName=''
                                    for(var key in data.data.teacher_id){
                                        teacherName+='<div>'+that.overview.teachersInfo[data.data.teacher_id[key]]['name']+'</div>'
                                    }
                                    var className=''
                                    that.overview.classList.forEach((item,index) => {
                                        if (data.data.class_id==item.id) {
                                            className=item.title
                                        } 
                                    })
                                    var str='<div class="mb4"><?php echo Yii::t("ptc", "Found conflicts:"); ?></div>'+
                                        '<div class="mb4"><span>'+data.data.date+'</span><span class="ml20">'+className+'</span></div>'+teacherName
                                }else{
                                    str=data.message
                                }
                                
                                that.alertContent =str
                                $("#alertModal").modal('show')
                            }
                        },
                        error:function(data){
                            resultTip({error: 'warning', msg: '请求错误'});
                        }
                    })
                }
            },
            delTimeItem(index,type){
                let that=this
                if(type === 'set'){
                    that.initSetTimeItem.splice(index,1)
                }
                if(type === 'revise'){
                    if(that.initReviseTimeItem[index]['id']){
                        that.delTimeItemIds.push(that.initReviseTimeItem[index]['id'])
                    }
                    that.initReviseTimeItem.splice(index,1)
                }
            },
            //修改时间段
            handleCheckAllChange(type,val) {
                if(type=='set'){
                    this.initReviseTimeItem.forEach((item,index) => {
                        if (!item.reserve) {
                            if(val.target.checked){
                                item.meet_type=true
                            }else{
                                item.meet_type=false
                            }
                            
                        } 
                    })
                }else{
                    this.initSetTimeItem.forEach((item,index) => {
                        if (!item.reserve) {
                            if(val.target.checked){
                                item.meet_type=true
                            }else{
                                item.meet_type=false
                            }
                            
                        } 
                    })
                }
            },
            handleCheckedCitiesChange(type,value) {
                if(type=='set'){
                    let checkedLen=0;
                    let checkedCount = 0;
                    this.initReviseTimeItem.forEach((item,index) => {
                        if (!item.reserve) {
                            checkedCount++
                            if (item.meet_type) {
                                checkedLen++
                            } 
                        } 
                    })
                    this.checkAll = checkedCount === checkedLen;
                }else{
                    let checkedLen=0;
                    let checkedCount = 0;
                    this.initSetTimeItem.forEach((item,index) => {
                        if (!item.reserve) {
                            checkedCount++
                            if (item.meet_type) {
                                checkedLen++
                            } 
                        } 
                    })
                    this.J_check_all = checkedCount === checkedLen;
                }
                
                
            },
            reviseTime(item,type){
                let that=this
                if(type === 'show'){
                    that.initReviseTimeDay = item[0]['day_title']
                    that.initReviseTimeItem = [];
                    that.delTimeItemIds = [];
                    that.initSetTimeItemBase.start =null;
                    for (const itemKey in item) {
                        that.initReviseTimeItem.push({
                            id:item[itemKey]['schedule_id'],
                            start:item[itemKey]['timestamp_start']*1000,
                            meet_type:!!item[itemKey]['meet_type'],
                            reserve:Object.keys(item[itemKey]['items']).length > 0
                        })
                    }
                    $('#reviseTimeModal').modal('show')
                }
                if(type === 'commit'){
                    let reviseCommitTimeDay =  Date.parse(that.initReviseTimeDay) / 1000; //时间戳
                    let reviseCommitTimeItem = [];
                    for (const setTimeDayKey in that.initReviseTimeItem) {
                        if(that.initReviseTimeItem[setTimeDayKey]['start']){
                            reviseCommitTimeItem.push({
                                id:that.initReviseTimeItem[setTimeDayKey]['id'],
                                start:that.initReviseTimeItem[setTimeDayKey]['start'] / 1000,
                                meet_type:that.initReviseTimeItem[setTimeDayKey]['meet_type'] ? 1 : 0
                            })
                        }
                    }
                    let data = {
                        startyear: startyear,
                        semester: semester,
                        day: reviseCommitTimeDay,
                        item: reviseCommitTimeItem,
                        del_ids:that.delTimeItemIds,
                        class_id:that.classId,
                    }
                    $.ajax({
                        url: '<?php echo $this->createUrl("PtcSchedule/updateScheduleClass") ?>',
                        type: "post",
                        dataType: 'json',
                        data: data,
                        success: function(data) {
                            if(data.state=='success'){
                                that.classChild(that.classId)
                                resultTip({
                                    msg: "<?php echo Yii::t('global', 'Success!'); ?>"
                                });
                                $('#reviseTimeModal').modal('hide')
                            }else{
                                if(data.data){
                                    let teacher_id = data.data.teacher_id
                                    let teacher_name=''
                                    for(var key in data.data.teacher_id){
                                        teacher_name+='<div>'+that.overview.teachersInfo[data.data.teacher_id[key]]['name']+'</div>'
                                    }

                                    let class_id = data.data.class_id
                                    let time = data.data.date
                                    let class_name = '';
                                    that.classListData.forEach((item,key)=>{
                                        if(class_id == item.id){
                                            class_name = (item.title+' ')
                                        }
                                    })
                                    var str='<div class="mb4">'+data.message+'</div>'+
                                        '<div class="mb4"><span>'+class_name+'</span></div>'+
                                        '<div class="mb4"><span>'+time+'</span></div>'
                                        +teacher_name
                                }else{
                                    var str = data.message
                                }


                                that.alertContent = str
                                $("#alertModal").modal('show')
                            }
                        },
                        error:function(data){
                            resultTip({error: 'warning', msg: '请求错误'});
                        }
                    })
                }
            },
            locationCheckAllChange(data,e){
                if(e.target.checked){
                    data.children.forEach((item,index) => {
                        item.checked=true
                    })
                    let checkedCount = 0;
                    this.tree_data.forEach((item,index) => {
                        if(item.checked){
                            checkedCount++
                        }
                    })
                    this.locationAllData = checkedCount === this.tree_data.length;
                }else{
                    data.children.forEach((item,index) => {
                        item.checked=false
                    })
                    this.locationAllData=false
                }
            },
            locationCheckedCitiesChange(data,e){
                let checkedCount = 0;
                data.children.forEach((item,index) => {
                    if (item.checked) {
                        checkedCount++
                    } 
                })
                let checkedAll = 0;
                let childrenLen = 0;
                this.tree_data.forEach((item,index) => {
                    item.children.forEach((_item,idx) => {
                        if(_item.checked){
                            checkedAll++
                        }
                        childrenLen++
                    })
                })
                this.locationAllData = checkedAll === childrenLen;
                data.checked = checkedCount === data.children.length;
            },
            locationAll(e){
                if(e.target.checked){
                    this.tree_data.forEach((item,index) => {
                        item.checked=true
                        item.children.forEach((_item,idx) => {
                            _item.checked=true
                        })
                    })
                }else{
                    this.tree_data.forEach((item,index) => {
                        item.checked=false
                        item.children.forEach((_item,idx) => {
                            _item.checked=false
                        })
                    })
                }
            },
            setLocation(item,type) {
                let that=this
                if(type === 'show'){
                    that.setLocationItem = item;
                    that.locations = item.location
                    that.setLocations = JSON.parse(JSON.stringify(item.location))
                    let tree_data= []
                    for (const day in that.ptcDataList) {
                        let item_children = [];
                        that.ptcDataList[day].forEach((ptcDataListItem,index)=>{
                            let disabled = false
                            let checked = false
                            //当前时间段不可操作默认选中 //线上会议不可操作
                            if(ptcDataListItem.schedule_id != item.schedule_id && ptcDataListItem.meet_type != 1){
                                let push_data = {
                                    id: ptcDataListItem.schedule_id,
                                    label: ptcDataListItem.start +'-'+ ptcDataListItem.end,
                                    disabled: disabled,
                                    checked: checked,
                                    haveLocation: Object.values(ptcDataListItem.location).length ==0 ? false :true
                                }
                                item_children.push(push_data)
                            }
                        })
                        if(item_children.length != 0){
                            let push_data = {
                                'id':day,
                                'label':day,
                                'children':item_children,
                                'checked':false
                            };
                            tree_data.push(push_data)
                        }
                    }
                    this.locationAllData=false
                    that.tree_data = tree_data
                    $('#setLocationModal').modal('show')
                    return;
                }
                if(type === 'commit'){
                    let schedule_ids = [that.setLocationItem.schedule_id];
                    this.tree_data.forEach((item,index) => {
                        item.children.forEach((_item,idx) => {
                            if(_item.checked){
                                schedule_ids.push(_item.id)
                            }
                        })
                    })
                    if (Object.keys(that.setLocations).length < 3 || Object.values(that.setLocations).indexOf('')!=-1) {
                        that.alertContent="<?php echo Yii::t("ptc", "Input meeting location"); ?>!"
                        $("#alertModal").modal('show')
                        return;
                    }
                    let data = {
                        class_id: that.classId,
                        subject_location: that.setLocations,
                        schedule_ids: schedule_ids
                    };
                    $.ajax({
                        url: '<?php echo $this->createUrl("ptcSchedule/assignLocation") ?>',
                        type: "post",
                        dataType: 'json',
                        data: data,
                        success: function(data) {
                            if(data.state=='success'){
                                resultTip({
                                    msg: "<?php echo Yii::t('global', 'Success!'); ?>"
                                });
                                $('#setLocationModal').modal('hide')
                                that.classChild(that.classId)
                            }else{
                                that.alertContent = data.message
                                $("#alertModal").modal('show')
                            }
                        },
                        error:function(data){
                            resultTip({error: 'warning', msg: '请求错误'});
                        }
                    })
                }

            },
            //选择日历日期
            pickDate(date){
                let that=this
                for(var key in this.overview.ptcData){
                    if(date.date.getTime()==new Date(this.overview.ptcData[key][0].day_title).getTime()){
                        return
                    }
                }
                if (this.addTimeList.indexOf(date.ariaLabel) < 0) {
                    let push_commit_data=(date.id).replace(/-/g,'')
                    if(!that.addTimeList.includes(date.id)){
                        that.addTimeList.push(date.ariaLabel)//显示在右边的时间列表
                        that.commitTimeList.push(push_commit_data)//要提交的时间列表
                        that.addAttrsTime.push({
                            highlight: {
                                fillMode: 'solid',
                            },
                            dates: new Date(date.id),
                        })
                    }
                }
            },
            copyTime(item,type){
                let that=this
                if(type === 'show'){
                    that.copyItem = item
                    that.copyItemFirst = item[0]
                    that.addTimeList = []
                    that.commitTimeList = [];
                    that.addAttrsTime = [];
                    that.disabledDates=[]
                    for(var key in this.overview.ptcData){
                        that.disabledDates.push({
                            start: new Date(this.overview.ptcData[key][0].day_title),
                            end:  new Date(this.overview.ptcData[key][0].day_title),
                        })

                    }
                    $('#copyTimeModal').modal('show')
                    return ;
                }
                if(type === 'commit'){
                    if(that.commitTimeList.length <= 0){
                        that.alertContent = '<?php echo Yii::t("ptc", "Please select target dates"); ?>'
                        $("#alertModal").modal('show')
                        return;
                    }
                    let data = {
                        class_id:that.classId,
                        day: that.copyItem[0]['day'],
                        days: that.commitTimeList
                    }
                    $.ajax({
                        url: '<?php echo $this->createUrl("ptcSchedule/saveCopyScheduleClass") ?>',
                        type: "post",
                        dataType: 'json',
                        data: data,
                        success: function(data) {
                            if(data.state=='success'){
                                resultTip({
                                    msg: "<?php echo Yii::t('global', 'Success!'); ?>"
                                });
                                $('#copyTimeModal').modal('hide')
                                that.classChild(that.classId)
                            }else{
                                let teacher_id = data.data.teacher_id
                                let teacher_name = that.overview.teachersInfo[teacher_id]['name']
                                let class_id = data.data.class_id
                                let time = data.data.time
                                let class_name = '';
                                that.classListData.forEach((item,key)=>{
                                    if(class_id == item.id){
                                        class_name = (item.title+' ')
                                    }
                                })
                                var str='<div class="mb4">'+data.message+'</div>'+
                                    '<div class="mb4"><span>'+class_name+'</span></div>'+
                                    '<div class="mb4"><span>'+time+'</span></div>'
                                    +teacher_name
                                //提示冲突
                                that.alertContent = str
                                $("#alertModal").modal('show')
                                // resultTip({error: 'warning', msg:data.message+'teacher:'+teacher_name+'; class:'+class_name});
                            }
                        },
                        error:function(data){
                            resultTip({error: 'warning', msg: '请求错误'});
                        }
                    })
                }
            },
            closeTime(index){
                let that=this
                that.addTimeList.splice(index, 1);
                that.commitTimeList.splice(index, 1);
                that.addAttrsTime.splice(index, 1);
            },
            setOnlineChild(id,status){
                let that=this
                let data = {
                    class_id: that.classId,
                    subject_location: that.setLocations,
                    child_id:id,
                    status:status
                };
                $.ajax({
                    url: '<?php echo $this->createUrl("ptcSchedule/setOnlineChild") ?>',
                    type: "post",
                    dataType: 'json',
                    data: data,
                    success: function(data) {
                        if(data.state=='success'){
                            resultTip({
                                msg: "<?php echo Yii::t('global', 'Success!'); ?>"
                            });
                            $('#setLocationModal').modal('hide')
                            that.classChild(that.classId)
                        }else{
                            that.alertContent = data.message
                            $("#alertModal").modal('show')
                            // resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            exportTable(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("ptcSchedule/getPtcScheduleDay2") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        class_id: [this.classId], 
                    },
                    success: function(data) {
                        if(data.state=='success'){  
                           that.exportDays=data.data
                            $('#exportModal').modal('show')
                        }else{ 
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            exportData(){
                let that=this
                this.exportBtn=true
                var className=''
                that.overview.classList.forEach((item,index) => {
                    if (this.classId==item.id) {
                        className=item.title
                    } 
                })
                $.ajax({
                    url: '<?php echo $this->createUrl("ptcSchedule/GetStudentsByDate2") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        class_id: [this.classId], 
                    },
                    success: function(data) {
                        if(data.state=='success'){  
                            let dataList=data.data
                            const filename =className+'.xlsx';
                            const ws_name = "SheetJS";
                            var exportDatas = [];
                            for(var i=0;i<dataList.ptcData.length;i++){
                                let childId=parseInt(dataList.ptcData[i].child_id)
                                if(dataList.childInfo[childId]){
                                    if(dataList.parentsData[dataList.childInfo[childId].mid]){
                                        exportDatas.push(
                                        {
                                        '日期':dataList.ptcData[i].day,
                                        "会谈时间":dataList.ptcData[i].time,
                                        "学生姓名":dataList.childInfo[childId].name,
                                        "学生ID":childId,
                                        "班级":dataList.childInfo[childId].className,
                                        "参会人员":'母亲',
                                        "姓名":dataList.parentsData[dataList.childInfo[childId].mid].name,
                                        "电话":dataList.parentsData[dataList.childInfo[childId].mid].mphone,
                                        })
                                        if(dataList.parentsData[dataList.childInfo[childId].fid]){
                                            exportDatas.push(
                                            {
                                            '日期':'',
                                            "会谈时间":'',
                                            "学生姓名":'',
                                            "学生ID":'',
                                            "参会人员":'父亲',
                                            "姓名":dataList.parentsData[dataList.childInfo[childId].fid].name,
                                            "电话":dataList.parentsData[dataList.childInfo[childId].fid].mphone,
                                            })
                                        }
                                    }else{
                                        exportDatas.push(
                                            {
                                            '日期':dataList.ptcData[i].day,
                                            "会谈时间":dataList.ptcData[i].time,
                                            "学生姓名":dataList.childInfo[childId].name,
                                            "学生ID":childId,
                                            "班级":dataList.childInfo[childId].className,
                                            "参会人员":'父亲',
                                            "姓名":dataList.parentsData[dataList.childInfo[childId].fid].name,
                                            "电话":dataList.parentsData[dataList.childInfo[childId].fid].mphone,
                                        })
                                        if(dataList.parentsData[dataList.childInfo[childId].mid]){
                                            exportDatas.push(
                                                {
                                                '日期':'',
                                                "会谈时间":'',
                                                "学生姓名":'',
                                                "学生ID":'',
                                                "参会人员":'母亲',
                                                "姓名":dataList.parentsData[dataList.childInfo[childId].mid].name,
                                                "电话":dataList.parentsData[dataList.childInfo[childId].mid].mphone,
                                            })
                                        }
                                    }
                                }
                            }
                            var wb=XLSX.utils.json_to_sheet(exportDatas,{
                                origin:'A1',// 从A1开始增加内容
                                header: ['日期', '会谈时间', '学生姓名','学生ID','班级','参会人员','姓名','电话'],
                            });
                            const workbook = XLSX.utils.book_new();
                            XLSX.utils.book_append_sheet(workbook, wb, ws_name);
                            const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                            const blob = new Blob([wbout], {type: 'application/octet-stream'});
                            let link = document.createElement('a');
                            link.href = URL.createObjectURL(blob);
                            link.download = filename;
                            link.click();
                            setTimeout(function() {
                                // 延时释放掉obj
                                URL.revokeObjectURL(link.href);
                                link.remove();
                                that.exportBtn=false
                            }, 500);
                        }else{ 
                            that.exportBtn=false
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        that.exportBtn=false
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
        },

    })


</script>