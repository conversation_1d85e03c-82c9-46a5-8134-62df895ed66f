<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'draw-form',
	'enableAjaxValidation'=>false,
	'htmlOptions'=>array('class'=>'J_ajaxForm'),
));
?>
<div class="pop_cont pop_table" style="height:auto;">
    <div class="tips_light"><?php echo Yii::t("payment", '学费账单更改会遇到账单使用预缴学费情况，不需要考虑预缴学费，按照实际情况填写金额，系统会自动冲抵预缴学费。')?></div>
    <table class="table_list grid-view" width="100%">
        <thead>
            <tr>
                <th>账单标题</th>
                <th>账单金额</th>
                <th>账单区间</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td><?php echo $data->title;?></td>
                <td><?php echo OA::formatMoney($data->amount,2);?></td>
                <td><?php echo OA::formatDateTime($data->startdate).' / '.OA::formatDateTime($data->enddate);?></td>
            </tr>
        </tbody>
    </table>
    <div class="table_full">
        <table width="100%">
            <tbody class="items">
                <tr>
                    <th style="width: 100px;">账单标题</th>
                    <td>
                        <input class="mr10 input length_5" id="Invoice_title" name="Invoice[title]" type="text" value="<?php echo $data->title;?>">
                    </td>
                </tr>
                <tr>
                    <th>账单金额</th>
                    <td>
                        <input class="mr10 input length_5" id="Invoice_title" name="Invoice[amount]" type="text" value="<?php echo $data->amount;?>">
                    </td>
                </tr>
                <tr>
                    <th>开始日期</th>
                    <td>
                        <?php 
                            Yii::app()->getController()->widget('zii.widgets.jui.CJuiDatePicker', array(
                            'name' => 'Invoice[startdate]',
                            'value'=>OA::formatDateTime($data->startdate),
							"options"=>array(
								'changeMonth'=>true,
								'changeYear'=>true,
								'dateFormat'=>'yy-mm-dd',
							),
							'htmlOptions'=>array('class'=>'mr10 input length_5 specialDatePicker'),
						));
                        ?>
                    </td>
                </tr>
                <tr>
                    <th>结束日期</th>
                    <td>
                         <?php 
                            Yii::app()->getController()->widget('zii.widgets.jui.CJuiDatePicker', array(
                            'name' => 'Invoice[enddate]',
                            'value'=>OA::formatDateTime($data->enddate),
							"options"=>array(
								'changeMonth'=>true,
								'changeYear'=>true,
								'dateFormat'=>'yy-mm-dd',
							),
							'htmlOptions'=>array('class'=>'mr10 input length_5 specialDatePicker'),
						));
                        ?>
                    </td>
                </tr>
                <tr>
                    <th><?php echo Yii::t('workflow', 'Option:'); ?></th>
                    <td>
                         <?php 
                            echo CHtml::textArea('Invoice[memo]', '', array('class'=>'length_5'));
                        ?>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>
<div class="pop_bottom">
    <button type="button" class="btn fr" id="J_dialog_close"><?php echo Yii::t('global','Cancel');?></button>
    <button class="btn fr btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
</div>
<?php $this->endWidget(); ?>

