<?php

/**
 * This is the model class for table "ivy_survey_detail".
 *
 * The followings are the available columns in table 'ivy_survey_detail':
 * @property integer $id
 * @property integer $survey_id
 * @property integer $start_time
 * @property integer $end_time
 * @property string $schoolid
 * @property integer $is_published
 * @property integer $has_report
 * @property integer $open2school
 * @property integer $update_user
 * @property integer $update_time
 */
class WSurveyDetail extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return SurveyDetail the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_survey_detail';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('survey_id, start_time, end_time, is_published, has_report, open2school, update_user, update_time', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, survey_id, start_time, end_time, schoolid, is_published, has_report, open2school, update_user, update_time', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
				'survey'=>array(self::BELONGS_TO,'WSurvey','survey_id'),
				'fbInfo'=>array(self::HAS_MANY,'WSurveyFeedback',array('id'=>'survey_id'),'through'=>'survey'),
		);
	}
	
	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'survey_id' => 'Survey',
			'start_time' => 'Start Time',
			'end_time' => 'End Time',
			'schoolid' => 'Schoolid',
			'is_published' => 'Is Published',
			'has_report' => 'Has Report',
			'open2school' => 'Open2school',
			'update_user' => 'Update User',
			'update_time' => 'Update Time',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('survey_id',$this->survey_id);
		$criteria->compare('start_time',$this->start_time);
		$criteria->compare('end_time',$this->end_time);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('is_published',$this->is_published);
		$criteria->compare('has_report',$this->has_report);
		$criteria->compare('open2school',$this->open2school);
		$criteria->compare('update_user',$this->update_user);
		$criteria->compare('update_time',$this->update_time);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

}