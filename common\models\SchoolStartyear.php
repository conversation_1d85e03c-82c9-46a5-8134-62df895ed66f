<?php

/**
 * This is the model class for table "ivy_school_startyear".
 *
 * The followings are the available columns in table 'ivy_school_startyear':
 * @property integer $id
 * @property integer $childid
 * @property string $schoolid
 * @property string $classid
 * @property string $statryear
 * @property integer $from_invoiceid
 * @property integer $updated_by
 */
class SchoolStartyear extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_school_startyear';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('childid, schoolid, statryear, classid, from_invoiceid, updated_by', 'required'),
			array('childid, classid, from_invoiceid, updated_by', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>30),
			array('statryear', 'length', 'max'=>4),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, childid, schoolid, classid, statryear, from_invoiceid, updated_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'childid' => 'Childid',
			'classid' => 'Classid',
			'schoolid' => 'Schoolid',
			'statryear' => 'Statryear',
			'from_invoiceid' => 'From Invoiceid',
			'updated_by' => 'Updated By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('statryear',$this->statryear,true);
		$criteria->compare('from_invoiceid',$this->from_invoiceid);
		$criteria->compare('updated_by',$this->updated_by);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return SchoolStartyear the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
