<div>
    <div class='flex align-items  mb24 mt10'>
        <span class='font14 color3'>处理人：</span>
        <div class='flex1 flex align-items lib_class_handle'>
        </div>
    </div>
    <div class='reject redColor'>
        <div class='font14'><span class='el-icon-warning mr5 font16'></span>已驳回</div>
        <div class='mt8'>
        驳回理由：<span class='rejectMemo'></span>
        </div>
    </div>
    <form class="J_ajaxForm formStu" action="<?php echo $this->createUrl("saveNode");?>" method="post"  onsubmit="return check(this)">
        <div>
            <div class="radio mb16">
                <label class="radio-inline font14 color6 lableHeight ">
                    <input type="radio" name="balance" value='1'>已归还—状态完好
                </label>
            </div>
            <div class="radio mb16">
                <label class="radio-inline font14 color6 lableHeight">
                    <input type="radio" name="balance" value='2'>已归还—存在设备损坏
                </label>
            </div>
            <div class="radio ">
                <label class="radio-inline font14 color6 lableHeight ">
                    <input type="radio" name="balance" value='0'>未归还
                </label>
            </div>
            <div class='hide amountInput ml20'>
                <input type="number" min="0" step="0.01" class="form-control length_6" name="balance_amount"  placeholder="请输入金额" value=''>
            </div>
            <textarea class="form-control mb24 mt24" rows="3" placeholder="<?php echo Yii::t('withdrawal','Comment');?>" name="remark" id='remark'  class='font14'></textarea>
            <div class='mt20'><button type="button" class="btn btn-primary" id='pickfilesPhoto'><span class='el-icon-circle-plus-outline'></span><span class='uploadText ml5'>上传附件</span> </button></div>
            <div id='img' class='row'>
            </div>
        </div>
        <input type="hidden" name="type" id="type" value='it_equ'>
        <input type="hidden" name="applicationId" id="applicationId">
        <input type="hidden" name="node" id="node">
        <div class='modal-footer borderTopNone'>
            <label class="checkbox-inline">
                <input type="checkbox" name="status" value="1"> <?php echo Yii::t('withdrawal', 'Mark this step as completed'); ?>
            </label>
            <button type="submit" class="btn btn-primary ml24 submitSend"><?php echo Yii::t('global','Submit');?></button>
            <button type="button" class="btn btn-primary J_ajax_submit_btn ml24"><?php echo Yii::t('global','Submit');?></button>
        </div>
    </form>
</div>

<script>
     lib()
     $(document).ready(function() {
        $('input[name="amount"]').on('input', function() {
            var value = $(this).val();
            if (!/^[1-9]\d*|0$/.test(value)) {
                $(this).val('');
            }
        });
    });
    var imgList=[]
    var currentImg={}
    var config = {
        runtimes: 'html5,flash,html4', //上传模式,依次退化
        container: 'container', //上传区域DOM ID，默认是browser_button的父元素，
        browse_button: 'pickfilesPhoto', //上传选择的点选按钮，**必需**
        token: '',
        flash_swf_url: '/js/plupload/Moxie.swf', // flash的相对地址
        auto_start: true, //选择文件后自动上传，若关闭需要自己绑定事件触发上传
        multi_selection: false, //不允许多选
        url:'https://upload-z1.qiniup.com',
        
        init: {
            'FilesAdded': function(up, files) {
                container.disabledUpload=true
                up.start();
                $('.uploadText').text('附件上传中')
                $("#pickfilesPhoto").prop("disabled", true);
            },
            BeforeUpload: function(up, file) {
                //设置参数
                up.setOption({
                    multipart_params:{token:container.token}
                })
            },
            'FileUploaded': function(up, file, info) {
                var response = eval('('+info.response+')');
                if(info.status == 200){   
                    imgList.push(response.data)
                    currentImg=response.data
                    $('#img').append(
                        '<div class="col-md-4 col-sm-4 mt16">'+
                        '<div class="bgGrey flex align-items">'+
                            '<div class="flex1 nowrap text-primary font14">'+
                                '<a target="_blank" href='+currentImg.file_key+' class="flex1" style="line-height: 26px;">'+
                                    '<span class="el-icon-paperclip"></span>'+
                                    '<span class="ml4">'+currentImg.title+'</span>'+
                                    '<input type="hidden" name="attachments[]" value='+currentImg._id+' id="type">'+
                                '</a>'+
                            '</div>' +
                            '<span class="glyphicon glyphicon-trash font14 closeFile cur-p"  data-id='+currentImg._id+' onclick="delImg(this)"></span>'+
                        '</div>'+
                        '</div>'
                    );
                    $('.uploadText').text('上传附件')
                    $("#pickfilesPhoto").prop("disabled", false);
                }
            },
            'Error': function(up, err, errTip) {
                if(err.response){
                    var response = eval('('+err.response+')');
                }
                else{
                    var response = {message: err.message};
                }
                if(response.error == 'token not specified'){
                    up.stop();
                    $.ajax({
                        url: '<?php echo $this->createUrl("getQiniuToken") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            editNoticeId: container.editNotice._id
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                container.uptoken=data.data.data
                                config['uptoken'] = data.data.data;
                                up.setOption("multipart_params", {"token":data.data.token,})
                                up.start();
                                $('.uploadText').text('上传附件')
                                $("#pickfilesPhoto").prop("disabled", false);
                            } else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.msg
                                });
                            }
                        },
                        error: function(data) {

                        },
                    })
                }
            },
            'UploadComplete': function(up, file) {
                //队列文件处理完毕后,处理相关的事情
                $('.uploadText').text('上传附件')
                $("#pickfilesPhoto").prop("disabled", false);

            }
        }
    };
    function delImg(that){
       let attachmentId=$(that).attr('data-id')
        $.ajax({
            url: '<?php echo $this->createUrl("deleteAttachment") ?>',
            type: "post",
            dataType: 'json',
            data: {
                attachmentId:attachmentId,
            },
            success: function(data) {
                if (data.state == 'success') {
                    $(that).parent().parent().remove();
                } else {
                    resultTip({
                        error: 'warning',
                        msg: data.msg
                    });
                }
            },
            error: function(data) {

            },
        })
        
    }
    function viewImg(that){
        let url=$(that).prop('src');
        window.open(url)
    }
    function lib(){
        let forms=childDetails.forms.find(item2 =>item2.key === 'it_equ')
        for(var key in forms.owner_info){
            if (key !== '2' && key !== '5') {
            $('.lib_class_handle').append('<div class="flex align-items mr16"><img src='+forms.owner_info[key].photoUrl+' alt="" class="img28"/><div class="font14 color3 ml8">'+forms.owner_info[key].name+'</div></div>')
            }
        }
        $('#type').val(forms.key)
        if(forms.confirm_status==2){
            $('.reject').show()
            $('.rejectMemo').html(forms.reject_memo)
        }else{
            $('.reject').hide()
            $('.rejectMemo').html(forms.reject_memo)
        }
        $('input[name="balance"]').click(function () {
        if ($(this).val() == '1') {
                $('.amountInput').addClass('hide')
            }
            else {
                $('.amountInput').removeClass('hide')
            }
        })
        if(forms.status=='1'){
            $('input[type="checkbox"][name="status"][value='+forms.status+']').prop('checked', true);
        }
        if(forms.data.remark!=''){
            $('#remark').val(forms.data.remark);
        }
        if(forms.data.balance){
            $('input[type="radio"][name="balance"][value='+forms.data.balance+']').prop('checked', true);
            if (forms.data.balance != '1') {
                $('.amountInput').removeClass('hide')
            }
            $('input[name="balance_amount"]').val(forms.data.balance_amount)
        }
        if(forms.data.attachments_url && forms.data.attachments_url.length!=0){
            for(let i=0;i<forms.data.attachments_url.length;i++){
                $('#img').append(
                    '<div class="col-md-4 col-sm-4 mt16">'+
                        '<div class="bgGrey flex align-items">'+
                            '<div class="flex1 nowrap text-primary font14">'+
                                '<a target="_blank" href='+forms.data.attachments_url[i].file_key+' class="flex1" style="line-height: 26px;">'+
                                    '<span class="el-icon-paperclip font16"></span>'+
                                    '<span class="ml4">'+forms.data.attachments_url[i].title+'</span>'+
                                    '<input type="hidden" name="attachments[]" value='+forms.data.attachments_url[i]._id+' id="type">'+
                                '</a>'+
                            '</div>' +
                            '<span class="glyphicon glyphicon-trash font14 closeFile cur-p" data-id='+forms.data.attachments_url[i]._id+' onclick="delImg(this)"></span>'+
                        '</div>'+
                    '</div>'
                );
            }
        }
    }
    $('.submitSend').show()
    $('.J_ajax_submit_btn').hide()
    function check(_this){
        let balanceId=$('input[name="balance"]:checked').val()
        $('#J_fail_info').remove();
        var button = $(_this).find('button[type="submit"]');
        var flag = true, msg=[];
        if(!balanceId){
            flag = false;
            msg.push('<?php echo Yii::t("teaching","请选择归还状态");?>');
        }
        if(balanceId && balanceId!='1' && $('input[name="balance_amount"]').val()==''){
            flag = false;
            msg.push('<?php echo Yii::t("teaching","请输入金额");?>');
        }
        if(balanceId && balanceId=='1'){
            $('input[name="balance_amount"]').val(0)
        }
        if(flag){
            $('.submitSend').hide()
            $('.J_ajax_submit_btn').show()
            $('.formStu .J_ajax_submit_btn').click();
            return false;
        }
        else{
            $( '<span id="J_fail_info" class="text-warning"><i class="glyphicon glyphicon-remove text-warning"></i> ' + msg.join('、') + '！</span>' ).appendTo(button.parent()).fadeIn( 'fast' );
            return false;
        }
    }
</script>
<style>
    .img50{
        width: 70px;
        height: 70px;
        
    }
    .imgList{
        margin-top: 20px;
        margin-right: 20px;
        display:inline-block
    }
    .delImg{    
        position: absolute;
        right: -5px;
        top: -5px;
        font-size: 16px;
    }
    .delImg:hover{
        color:#D9534F;
        cursor: pointer;
    }
    .bgGrey {
        background: #FAFAFA;
        border-radius: 4px;
        padding: 10px;
        border: 1px solid #FAFAFA;
    }
    .nowrap {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .closeFile {
        display: none;
    }
    .bgGrey:hover .closeFile {
        display:block
    }
</style>
