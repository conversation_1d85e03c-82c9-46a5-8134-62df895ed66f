<?php

class SurveyController extends BranchBasedController{

    public function createUrl($route, $params = array(), $ampersand = '&', $parentOnly = false)
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = false;
        $this->branchSelectParams['urlArray'] = array('//mcampus/survey/reports');
    }

    public function actionReports(){
        Yii::import('common.models.survey.*');
        $crit = new CDbCriteria();
        $crit->compare('schoolid', $this->branchId);
        $crit->compare('is_published', 1);
        $crit->order = 'end_time DESC';
        $surveyDetails = WSurveyDetail::model()->with('survey')->findAll($crit);

        $template = CHtml::listData(WSurveyTemplate::model()->findAll(),
            'id', Yii::app()->language == 'zh_cn' ? 'title_cn' : 'title_en');

        $this->render('reports', array('surveyDetails' => $surveyDetails, 'template' => $template));
    }

    /**
     * @param $surveyId
     * 只有总部部门经理和管理团队可以查看员工调查问卷
     */
    public function actionViewStaffReport($surveyId) {
        $surveyId = intval($surveyId);
        if ($surveyId) {
            $noAccessMsg = "";
            Yii::import('common.models.survey.*');
            $cs = Yii::app()->clientScript;
            $cs->registerCoreScript('jquery.ui');

            $crit = new CDbCriteria();
            $crit->compare('survey_id', $surveyId);
            $crit->compare('schoolid', $this->branchId);
            $surveyDetail = WSurveyDetail::model()->with('survey')->find($crit);

            $access = false;
            if($surveyDetail) {
                //检查权限1. 总部工作人员
                if( $this->staff->isHQstaff()){
                    Yii::import('common.models.hr.DepPosLink');
                    $posDep = DepPosLink::model()->findByPk($this->staff->profile->occupation_en);

                    //部门领导或者管理团队
                    if( $posDep->is_lead > 0 || $posDep->department_id == 108 || Yii::app()->user->checkAccess('ivystaff_surveyedit')) {
                        $access = true;
                    }
                }

                //检查权限2. PD, 或特定人员
                if(!$access) {
                    $crit = new CDbCriteria();
                    $crit->compare('uid', $this->staff->uid);
                    $crit->compare('schoolid', $this->branchId);
                    $crit->compare('type', array('pd', 'staffsurvey', 'parentsurvey'));
                    $pdAdmin = AdmBranchLink::model()->find($crit);
                    if($pdAdmin) {
                        $access = true;
                    }
                }

                /**  检查权限3. 如果是园长，那么检查该报告是否已经开放给校园
                 * 54, 182, 208 幼儿园各种园长
                 * 151 总校长
                 * 340 执行总校长兼中学执行校长
                 * 251 校区长兼小学执行校长
                **/
                if(!$access) {
                    if(in_array($this->staff->profile->occupation_en, array(54, 182, 208, 151, )) &&
                        $surveyDetail->survey->respondents == 'parent' ) {
                        if( $surveyDetail->open2school == 0 ) {
                            $noAccessMsg = Yii::t('cadmin', 'Report is not open to campus.');
                        }else {
                            $access = true;
                        }
                    }
                }

                // 没有权限，显示错误信息，退出程序
                if(!$access) {
                    $this->render('noaccess', array('surveyDetail'=>$surveyDetail, 'noAccessMsg'=>$noAccessMsg));
                    Yii::app()->end();
                }


                //重新生成报告
                if(Yii::app()->request->isAjaxRequest && Yii::app()->request->isPostRequest) {
                    if(isset($_POST['genReport'])) {
                        Yii::import('common.components.general.CommonContentFetcher');
                        CommonContentFetcher::genSurveyReport($surveyId, $this->branchId);
                        $this->addMessage('state', 'success');
                        $this->addMessage('refresh', true);
                        $this->showMessage();
                    }
                }

                //开始准备调查问卷数据
                $templateId = $surveyDetail->survey->getOnlyTemplateId();

                $topicData = WSurveyTopic::model()->getSurveyTopics($templateId);
                $optionsData = WSurveyTopic::model()->getTopicOptions($templateId);

                $reportData = WSurveyReport::model()->getStaffReportData($surveyId, $this->branchId);
                $reportText = WSurveyFeedbackText::model()->getStaffSurveyText($surveyId, $this->branchId);

                $classData = array();
                if ($reportData) {
                    $crit = new CDbCriteria();
                    $crit->compare('classid', $reportData['classids']);
                    $crit->order = 'child_age ASC';
                    $classData = CHtml::listData(
                        IvyClass::model()->findAll($crit),
                        'classid','title'
                    );
                }

                if ( $surveyDetail->survey->respondents == 'parent' ) {
                    $nums = WSurveyFeedback::model()->getFeedBackStat($surveyId, $this->branchId);
                    $takenCount = sprintf('完成进度 %s/%s (%01.2f%%)', $nums[1], $nums[0], 100*$nums[1]/$nums[0]);
                } else {
                    $totalCount = WSurveyStaffFeedback::model()->getFeedBackTotalCount($surveyId, $this->branchId);
                    $count = WSurveyStaffFeedback::model()->getFeedBackCount($surveyId, $this->branchId);
                    $takenCount = sprintf('完成进度：%s/%s (%01.2f%%)', $count, $totalCount, 100*$count/$totalCount);
                }

                $tid = unserialize($surveyDetail->survey->template_id);
                $criteira = new CDbCriteria();
                $criteira->compare('id', $tid);
                $tModel = WSurveyTemplate::model()->findAll($criteira);

                $this->render('viewReport', array(
                    'surveyDetail'=>$surveyDetail,
                    'topicData' => $topicData,
                    'optionsData' => $optionsData,
                    'reportData' => $reportData,
                    'reportText' => $reportText,
                    'classData' => $classData,
                    'takenCount' => $takenCount,
                    'tModel' => $tModel,
                ));
            }
        }

    }

    public function actionPreview($surveyId)
    {
        
        $surveyId = intval($surveyId);
        if ($surveyId) {
            Yii::import('common.models.survey.*');
            $cs = Yii::app()->clientScript;
            $cs->registerCoreScript('jquery.ui');

            $survey = WSurvey::model()->findByPk($surveyId);
            if ($survey) {
                $templateId = $survey->getOnlyTemplateId();
                $topicData = WSurveyTopic::model()->getSurveyTopics($templateId);
                $optionsData = WSurveyTopic::model()->getTopicOptions($templateId);

                $this->render('preview', array(
                    'survey' => $survey,
                    'topicData' => $topicData,
                    'optionsData' => $optionsData,
                ));
            }
        }
    }
}
