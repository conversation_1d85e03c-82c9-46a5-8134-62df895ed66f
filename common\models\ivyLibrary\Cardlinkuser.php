<?php

/**
 * This is the model class for table "ivy_lib_cardlinkuser".
 *
 * The followings are the available columns in table 'ivy_lib_cardlinkuser':
 * @property integer $id
 * @property integer $cardtypeid
 * @property string $cardid
 * @property integer $userid
 * @property string $username
 * @property string $usertype
 * @property integer $start_time
 * @property integer $end_time
 * @property integer $borrower_num
 * @property string $schoolid
 * @property integer $status
 * @property integer $operator
 * @property integer $updatetime
 */
class Cardlinkuser extends CActiveRecord {

    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return Cardlinkuser the static model class
     */
    public static function model($className = __CLASS__) {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName() {
        return 'ivy_lib_cardlinkuser';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules() {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('cardtypeid, cardid, usertype, schoolid', 'required'),
            array('cardtypeid, userid, start_time, end_time, borrower_num, status, operator, updatetime', 'numerical', 'integerOnly' => true),
            array('cardid, username, schoolid', 'length', 'max' => 25),
            array('usertype', 'length', 'max' => 10),
            // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array('id, cardtypeid, cardid, userid, username, usertype, start_time, end_time, borrower_num, schoolid, status, operator, updatetime', 'safe', 'on' => 'search'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations() {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
            'cardType' => array(self::BELONGS_TO, 'BorrowingCard', 'cardtypeid'),
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels() {
        return array(
            'id' => Yii::t("library", "ID"),
            'cardtypeid' => Yii::t("library", "Card Type ID"),
            'cardid' => Yii::t("library", "Card NO."),
            'userid' => Yii::t("library", "User ID"),
            'username' => Yii::t("library", "User Name"),
            'usertype' => Yii::t("library", "User Type"),
            'start_time' => Yii::t("library", "Start Time"),
            'end_time' => Yii::t("library", "End Time"),
            'borrower_num' => Yii::t("library", "Borrowed Number"),
            'schoolid' => Yii::t("library", "School ID"),
            'status' => Yii::t("library", "Status"),
            'operator' => Yii::t("labels", "Operator"),
            'updatetime' => Yii::t("library", "Update Time"),
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search() {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('id', $this->id);
        $criteria->compare('cardtypeid', $this->cardtypeid);
        $criteria->compare('cardid', $this->cardid, true);
        $criteria->compare('userid', $this->userid);
        $criteria->compare('username', $this->username, true);
        $criteria->compare('usertype', $this->usertype, true);
        $criteria->compare('start_time', $this->start_time);
        $criteria->compare('end_time', $this->end_time);
        $criteria->compare('borrower_num', $this->borrower_num);
        $criteria->compare('schoolid', $this->schoolid, true);
        $criteria->compare('status', $this->status);
        $criteria->compare('operator', $this->operator);
        $criteria->compare('updatetime', $this->updatetime);

        return new CActiveDataProvider($this, array(
                    'criteria' => $criteria,
                    'sort' => array(
                                    'defaultOrder'=>'updatetime DESC',
                        ),
                ));
    }

}