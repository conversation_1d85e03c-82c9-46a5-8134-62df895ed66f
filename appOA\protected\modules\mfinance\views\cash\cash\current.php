<?php
Yii::import('common.models.visit.*');
//取默认交接截止日
$model = BranchVar::model()->find('branchid=:branchid and category=:category and subcategory=:subcategory',array(':branchid'=>$this->branchId,':category'=>$this->dcategory,':subcategory'=>$this->dsubcategory));
$defaultDate = 'N/A';
if (!empty($model)):
    $defaultDateTime = empty($model->data) ? 0 : $model->data;
    $defaultDate = empty($model->data) ? $defaultDate : OA::formatDateTime($model->data,'medium','medium');
endif;

//获取某学校未交接现金数据
$crite = new CDbCriteria;
$crite->compare('t.schoolid', $this->branchId);
$crite->compare('t.transactiontype', InvoiceTransaction::TYPE_CASH);
$crite->compare('t.inout', 'in');
$crite->compare('t.transfer_to_uid', 0);
$crite->compare('t.transfer_timestamp', 0);
$crite->addCondition('t.amount>0');
//$crite->compare('invoiceInfo.status', array(Invoice::STATS_PAID, Invoice::STATS_PARTIALLY_PAID));
$dataArr = InvoiceTransaction::model()->with('childInfo','userInfo', 'invoiceInfo')->findAll($crite);

$unHandoverAmount = 0; //未交接现金总和
$unHandoverDeadlineamount = 0;   //截止日之前总和
$unHandoverList = array(); //未交接现金列表
$unHandoverDeadlineList = array(); //截止日之前现金列表
$userName = array();
$childName = array();
$transactionIds = '';
$creditIds = '';
if (!empty($dataArr)):
    foreach ($dataArr as $val):
        //计算未交接总和
        $unHandoverAmount += $val->amount;
        //计算截止日之前总和
        if ($defaultDate != 'N/A' && $val->timestampe<=$defaultDateTime):
            $unHandoverDeadlineamount += $val->amount;
            $unHandoverDeadlineList[$val->operator_uid]['info'][$val->id] = $val->getAttributes();
            if(empty($val->childInfo)){
                $model_name = AdmissionsDs::model()->findByPk($val->invoiceInfo->admission_id);

            }
            $unHandoverDeadlineList[$val->operator_uid]['info'][$val->id]['name'] =  $val->childInfo ? $val->childInfo->getChildName()  : $model_name->getName();
            $unHandoverDeadlineList[$val->operator_uid]['info'][$val->id]['timestampe'] =  OA::formatDateTime($val->timestampe,'medium','short');
            $unHandoverDeadlineList[$val->operator_uid]['info'][$val->id]['amount'] = number_format($val->amount,2);
            $unHandoverDeadlineList[$val->operator_uid]['info'][$val->id]['date'] = ($val->startdate) ? OA::formatDateTime($val->startdate).' ~ '.OA::formatDateTime($val->enddate) : null;
            $unHandoverDeadlineList[$val->operator_uid]['amount'] = isset($unHandoverDeadlineList[$val->operator_uid]['amount']) ? $unHandoverDeadlineList[$val->operator_uid]['amount']+$val->amount : $val->amount;
            $unHandoverDeadlineList[$val->operator_uid]['fAmount'] = number_format($unHandoverDeadlineList[$val->operator_uid]['amount'],2);
            $transactionIds = empty($transactionIds) ? $val->id : $transactionIds.",".$val->id;
        endif;
        $userName[$val->operator_uid] = $val->userInfo->getName();
        if($val->childid){
            $childName[$val->childid] = ($val->childInfo->getChildName());
        }
    endforeach;
    unset($dataArr);

    //查询个人帐户
    $command = Yii::app()->db->createCommand();
    $command->from(ChildCredit::model()->tableName());
    $dataArr = $command->where('schoolid=:schoolid and transactiontype=:transactiontype and `inout`=:inout and transfer_to_uid=:transfer_to_uid and transfer_timestamp=:transfer_timestamp', array(
        ':schoolid'=>$this->branchId,'transactiontype'=>InvoiceTransaction::TYPE_CASH,':inout'=>'in',':transfer_to_uid'=>0,':transfer_timestamp'=>0
    ))->queryAll();
    if (count($dataArr)):
        $creditAmount = 0; //个人帐户未交接现金总和
        $creditDeadlineAmount = 0; //个人帐户截止日之前未交接现金总和
        $creditAmountList = array(); //个人帐户未交接现金列表
        $creditAmountDeadlineList = array(); //个人帐户截止日之未交接现金列表
        foreach ($dataArr as $val):
            //计算未交接总和
            $creditAmount += $val['amount'];
            //计算截止日之前总和
            if ($defaultDate != 'N/A' && $val['updated_timestamp']<=$defaultDateTime):
                $creditDeadlineAmount += $val['amount'];
                $creditAmountDeadlineList['info'][$val['cid']] = $val;
                $creditAmountDeadlineList['info'][$val['cid']]['updated_timestamp'] = OA::formatDateTime($val['updated_timestamp'],'medium','short');
                $creditIds = empty($creditIds) ? $val['cid'] : $creditIds.",".$val['cid'];
            endif;
        endforeach;
        $unHandoverAmount += $creditAmount;
        $unHandoverDeadlineamount += $creditDeadlineAmount;
        $creditAmountDeadlineList['fAmount'] = number_format($creditDeadlineAmount,2);
    endif;
endif;
?>

<?php echo CHtml::form($this->createUrl('//mfinance/cash/saveHandover'), 'post',array('class'=>'J_ajaxForm')); ?>
<div class="panel panel-default">
    <div class="panel-heading panel-title">未交接现金统计</div>
    <div class="panel-body">
        <span>交接截止日：</span>
        <span class="text-success"><strong><?php echo $defaultDate;?></strong></span>
        <?php if ($unHandoverDeadlineamount):?>
            <button type="button" class="btn btn-primary ml20 J_ajax_submit_btn">确认交接</button>
            <button type="button" class="btn btn-default" onclick="startPrint();">打印明细</button>
        <?php endif;?>
    </div>
    <!-- List group -->
    <div class="list-group" id="print-cash-list">
        <li class="list-group-item">
            未交接总金额 :  <span class="badge" style="float:none;">¥<?php echo number_format($unHandoverAmount,2);?></span>
        </li>
        <li class="list-group-item">
            <strong>此次交接金额 </strong>:
           <span class="badge text-warning" style="float:none;">¥<?php echo number_format($unHandoverDeadlineamount,2);?></span>
        </li>
        <li class="hidden" id="part-cash-list"></li>
        <?php echo CHtml::hiddenField('fTransactionIds', md5($transactionIds));?>
        <?php echo CHtml::hiddenField('fCreditIds', md5($creditIds));?>
    </div>
</div>
<?php echo CHtml::endForm();?>
<!--未交接现金列表模板-->
<script type="text/template" id="handover-list">
    <div class="panel-default">
        <div class="panel-heading"><%=userName[key]%> <span class="badge" style="float:right;"><%=values['fAmount']%></span></div>
        <div class="table-responsive">
        <table class="table table-hover">
                <tr>
                    <th>#</th>
                    <th>发票号</th>
                    <th><?php echo Yii::t('labels','Name');?></th>
                    <th>项目名称</th>
                    <th>付款日期</th>
                    <th>期间</th>
                    <th>金额</th>
                </tr>
                <%for(var vs in values['info']){%>
                <tr>
                <td><%=values['info'][vs].id%><?php echo CHtml::hiddenField("transactionIds[]", "<%=values['info'][vs].id%>",array('encode'=>false));?></td>
                    <td><%=values['info'][vs].invoice_number%></td>
                    <td><%=values['info'][vs].name%></td>
                    <td><%=values['info'][vs].payment_type%></td>
                    <td><%=values['info'][vs].timestampe%></td>
                    <td><%=values['info'][vs].date%></td>
                    <td><%=values['info'][vs].amount%></td>
                </tr>
                <%}%>
        </table>
        </div>
    </div>
</script>

<!--现金转入个人帐户列表模板-->
<script type="text/template" id="cash-credit-list">
    <div class="panel panel-default">
        <div class="panel-heading">个人帐户转入现金<span class="badge" style="float:right;"><%=creditAmountList.fAmount%></span></div>
        <table class="table table-hover">
            <tr>
                <th>#</th>
                <th><?php echo Yii::t('labels','Name');?></th>
                <th>项目名称</th>
                <th>金额</th>
                <th>收款人</th>
                <th>付款日期</th>
            </tr>
            <%for(var vs in creditAmountList['info']){%>
            <tr>
                <td><%=creditAmountList['info'][vs].transaction_id%><?php echo CHtml::hiddenField("creditIds[]", "<%=creditAmountList['info'][vs].cid%>",array('encode'=>false));?></td>
                <td><%=childName[creditAmountList['info'][vs].childid]%></td>
                <td><%=creditAmountList['info'][vs].itemname%></td>
                <td><%=creditAmountList['info'][vs].amount%></td>
                <td><%=userName[creditAmountList['info'][vs].userid]%></td>
                <td><%=creditAmountList['info'][vs].updated_timestamp%></td>
            </tr>
            <%}%>
        </table>
    </div>
</script>

<script>
var unHandoverDeadlineList = <?php echo CJSON::encode($unHandoverDeadlineList);?>;
var creditAmountDeadlineList = <?php echo CJSON::encode($creditAmountDeadlineList);?>;
var userName = <?php echo CJSON::encode($userName);?>;
var childName = <?php echo CJSON::encode($childName);?>;
var handoverList = _.template($('#handover-list').html());
var creditList = _.template($('#cash-credit-list').html());
var partCashList = $('#part-cash-list');
$(function(){
    viewDetails = function(){
        if (!_.isEmpty(unHandoverDeadlineList)){
            partCashList.empty();
            partCashList.removeClass('hidden');
            _.each(unHandoverDeadlineList, function(values,key){
                partCashList.append(handoverList({key:key,values:values}));
            });
        }
        if (!_.isEmpty(creditAmountDeadlineList)){

            partCashList.append(creditList({creditAmountList:creditAmountDeadlineList}));
        }
    }
    viewDetails();
    //print
    startPrint = function (){
        if ($.browser.msie) {
            $("#print-cash-list").printThis();
        }else{
            $("#print-cash-list").printThis();
        }
    }

    //confirm handover
    confirmHandover = function(_this){
        var btn = $(_this);
        head.load('/themes/base/js/dialog/dialog.js',function() {
        var params = {
                message	: '确定交接吗？',
                type	: 'confirm',
                isMask	: false,
                follow	: btn,//跟随触发事件的元素显示
                onOk	: function() {
                    btn.addClass('J_ajax_submit_btn');
                    btn.parents().find('form').addClass('J_ajaxForm');
                }
        };
        head.dialog(params);
       })
    }
})
</script>