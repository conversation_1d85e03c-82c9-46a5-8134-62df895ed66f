<?php
class RegStep10Form extends CFormModel
{
	public $idCardType; //身份证件类型
	public $idCard; //身份号或者证件号
	public $bloodType;  //  血型
	public $minzu;  //  民族
	public $placeOfBirth; // 出生所在地
	public $nativePlace; // 籍贯
	public $residentType; // 户口性质
	public $residentAddress; //  户口所在地
	public $guardianName; // 监护人姓名
	public $guardianIdCard; // 监护人身份证件号码
	public $guardianEelationship; // 监护人关系
	public $isOnlyChild; // 是否为独生子女
	public $address; // 现住址

    public $fatherName; // 监护人关系
    public $fatherTel; // 监护人关系
    public $fatherEmail; // 监护人关系
    public $motherName; // 监护人关系
    public $motherTel; // 监护人关系
    public $motherEmail; // 监护人关系

	public $id_type;
	public $allergy;
	public $filedata;
	public $medicalHistory;
	public $hospital;
	public $personName1;
	public $relationship1;
	public $contactNumber1;
	public $personName2;
	public $relationship2;
	public $contactNumber2;
	public $is_promote;


	public function rules()
	{
		return array(
            array('address, isOnlyChild, idCard, fatherName, fatherTel, fatherEmail, motherName, motherTel, motherEmail, allergy, medicalHistory, hospital, is_promote, guardianName, guardianIdCard, guardianEelationship', 'required', 'on'=>'regChildEn'),
            array('address, isOnlyChild, idCard, fatherName, fatherTel, fatherEmail, motherName, motherTel, motherEmail, idCardType, bloodType, minzu, placeOfBirth, nativePlace, residentType, bloodType, residentAddress, guardianIdCard, guardianEelationship, guardianName, allergy, medicalHistory, hospital, is_promote, guardianName, guardianIdCard, guardianEelationship', 'required', 'on'=>'regChildCn'),
            array('address, isOnlyChild, idCard, fatherName, fatherTel, fatherEmail, motherName, motherTel, motherEmail, PlaceOfBirth,idCardType, bloodType, minzu, placeOfBirth, residentType, nativePlace, residentAddress, guardianName, guardianIdCard, guardianEelationship, allergy, filedata, medicalHistory, hospital, is_promote, personName1, relationship1, contactNumber1, personName2, relationship2, contactNumber2', 'safe'),
		);
	}

    public function attributeLabels()
    {
        return array(
            'allergy' => Yii::t("reg", "Allergies(food,medications,etc.)"),
            'medicalHistory' => Yii::t("reg", "Medical History"),
            'hospital' => Yii::t('reg', 'Hospital'),
            'idCardType' => Yii::t('reg', '身份证件类型（有中国户⼝报中国户⼝）'),
            'idCard' => Yii::t('reg', 'Passport Number'),
            'bloodType' => Yii::t('reg', 'Blood Type'),
            'minzu' => Yii::t('reg', '民族'),
            'placeOfBirth' => Yii::t('reg', 'Birth Place'),
            'nativePlace' => Yii::t('reg', '籍贯'),
            'address' => Yii::t('reg', 'Current Address'),
            'residentType' => Yii::t('reg', '户口性质'),
            'residentAddress' => Yii::t('reg', '户口所在地'),
            'isOnlyChild' => Yii::t('reg', 'Only Child'),
            'guardianName' => Yii::t('reg', 'Guardian Name'),
            'guardianIdCard' => Yii::t('reg', 'Guardian Passport Number or Chinese ID Number'),
            'guardianEelationship' => Yii::t('reg', 'Guardian’s relationship with child'),
            'fatherName' => Yii::t('reg', 'Father Name'),
            'fatherTel' => Yii::t('reg', 'Father’s contact number'),
            'fatherEmail' => Yii::t('reg', 'Father’s Email'),
            'motherName' => Yii::t('reg', 'Mother Name'),
            'motherTel' => Yii::t('reg', 'Mother’s contact number'),
            'motherEmail' => Yii::t('reg', 'Mother’s Email'),
        );
    }

    public function uploadFile()
    {
        // 判断是否为 base64 图片
        if (strpos($this->filedata, 'base64') === false) {
            return true;
        }
        $data = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $this->filedata));

        $normalDir = rtrim(Yii::app()->params['xoopsVarPath'], '/') .'/';

        $fileName = uniqid() . '.png';

        $filePath = $normalDir . $fileName;
        if (file_put_contents($filePath, $data)) {
            $this->filedata = $fileName;
            if ($this->filedata) {
                // 上传到 OSS
                $objectPath = 'regextrainfo/';
                $oss = CommonUtils::initOSS('private');
                if ($oss->uploadFile($objectPath . $this->filedata, $filePath)) {
                    unlink($filePath);
                    return true;
                }
            }
        }
        return false;
    }


}
