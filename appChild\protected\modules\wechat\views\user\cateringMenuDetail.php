<script src="<?php echo Yii::app()->theme->baseUrl; ?>/js/vue.js"></script>
<script src="http://res.wx.qq.com/open/js/jweixin-1.0.0.js"></script>
<style>
    .fixedbot {
        position: fixed;
        left: 0;
        bottom: 0;
        height: 50px;
        background: #fff;
        width: 100%
    }
    .fixedbot button:first-child {
        margin-right: 10px
    }
    .lastbtn {
        margin-top: 0 !important;
    }
    .fixedbtn {
        height: 35px;
        line-height: 35px !important;
        font-size: 16px !important;
    }
    #menu {
        margin-bottom: 60px
    }
    .navbg {
        background: #fff;
        overflow: hidden;
        height: 53px;
        border-bottom: 1px solid #ddd;
    }
    pre {
        white-space: pre-wrap;
        white-space: -moz-pre-wrap;
        white-space: -pre-wrap;
        white-space: -o-pre-wrap;
        word-wrap: break-word;
        font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
    }
    .current {
        background-color: #04BE02;
        color: #fff !important;
    }
    [v-cloak] {
        display: none;
    }
    .btd {
        border-bottom: 1px solid #ddd
    }
    article {
        border-bottom: 1px solid #ddd
    }
    .fixedTop {
        position: fixed;
        left: 0;
        top: 0;
        width: 100%;
        z-index: 999;
        border-bottom:1px solid #ddd
    }
    .nav {
        /*height: 102px;*/
        background: #fff;
    }
    .mt0 {
        margin-top: 0
    }
    .weui-loadmore_line .weui-loadmore__tips {
        position: relative;
        top: -0.9em;
        padding: 0 .55em;
        background-color: #FFFFFF;
        color: #808080;
        font-size: 16px
    }
    .weui-loadmore__tips {
        display: inline-block;
        vertical-align: middle;
    }
    .weui-loadmore_line {
        border-top: 1px solid #ddd;
        margin-top: 2.4em;
    }
    .weui-loadmore {
        width: 65%;
        margin: 0.5em auto;
        line-height: 1.6em;
        font-size: 14px;
        text-align: center;
    }
    .p10 {
        padding: 10px 10px 0 10px;
        color: #808080
    }
    .weui_icon_waiting:before {
        color: #09BB07;
        margin-top: -3px
    }
    .weui_btn+.weui_btn {
        margin-top: 0;
    }
    .menuimg{
        box-shadow: 0 .5rem 1rem rgba(0, 0, 0, .15)!important;
        border-radius: .25rem!important;
    }
    #divselect {
        width: 100%;
        position: relative;
    }
    #divselect cite {
        width: 100%;
        height: 36px;
        line-height: 36px;
        display: block;
        color: #454545;
        cursor: pointer;
        font-style: normal;
        border: 1px solid #ddd;
        border-radius: 5px;
        text-indent: 1em;
    }
    #divselect cite:after {
        content: "";
        position: absolute;
        right: 10px;
        top: 45%;
        pointer-events: none;
        border-width: 5px 5px 0;
        border-style: solid;
        border-color: #333 transparent transparent;
    }
    #divselect ul {
        width: 100%;
        border: 1px solid #ddd;
        background-color: #ffffff;
        position: absolute;
        z-index: 1;
        margin-top: -1px;
        max-height: 300px;
        overflow-y: scroll;
    }
    #divselect ul li {
        height: 36px;
        line-height: 36px;
        list-style: none;
        text-indent: 1em;
    }
    #divselect ul li:last-child {
        border-bottom: none
    }
    #divselect ul li a {
        display: block;
        color: #333333;
        text-decoration: none;
    }
    #divselect ul li a:hover {
        background-color: #CCC;
    }
    .bgf {
        /*border-bottom: 1px solid #ddd*/
    }
    .expand-enter-active,.expand-leave-active {
        transition: all .5s;
    }
    .expand-enter,
    .expand-leave {
        opacity: 0;
    }
    .menu{
        background:#fef7cb;
        color: #ff722e;
        padding:10px 15px ;
        font-size:14px;
        display: flex;
    }
    .menu p{
        flex: 1;
        height:18px;
        line-height: 18px
    }
    .menu img{
        width:18px;
        margin-right:5px;
        display: inline-block;
        height:18px
    }
    .menu p a{
       color: #3070FF
    }
    .nav_mine {
    padding: 10px 20px;
    display: flex;
    height: 40px;
    align-items: center;
    overflow-y: hidden;
    flex-wrap: nowrap;
}

.nav_mine .nav_item {
    margin-right: 10px;
    padding:0 0.75em;
    white-space: nowrap;
}
</style>
<div class="title">
    <?php if(!in_array($this->childObj->schoolid, array("BJ_DS","BJ_SLT","BJ_QFF"))) {?>
        <div class="menu">
            <img src="<?php echo Yii::app()->theme->baseUrl; ?>/images/notice.png" alt="" style="">
            <p><?php echo Yii::t("portfolio", "Click <a href=':url'>here</a> to cancel lunch", array(':url' => $this->createUrl('contactUs/index', array('selectvalue' => 2))));?></p>
        </div>
    <?php } ?>
    <div class="weui_cells_title">
       <?php echo Yii::t('wechat', 'Menu'); ?>
    </div>
    <div class="weui_panel mt0">
        <?php
            $this->widget('ext.wechat.ChildSelector',array(
                'childObj'=>$this->childObj,
                'myChildObjs'=>$this->myChildObjs,
                'jumpUrl'=>'cateringMenuDetail',
            ));
        ?>
    </div>
</div>
<div id='menu' v-cloak>
    <div class="nav ">
        <div class="weui_cell bgf weeklist">

            <div id="divselect">
                <cite v-on:click="show = !show">{{weekname}}</cite>
                <transition name="expand">
                    <ul v-if="show">
                        <li v-for='(week,id) in schedule'>
                            <a href="javascript:;"  @click='getweekselect(week.id)'>{{week.title}}</a>
                        </li>
                    </ul>
                </transition>
            </div>

        </div>
        <?php if ($commonMemo):?>
            <div class="weui_cells_title" style="color: #ff722e;"><?php echo $commonMemo;?></div>
        <?php endif;?>
        <?php if ($allergyMemo):?>
            <div class="weui_cells_title" style="color: #ff722e;"><?php echo $allergyMemo;?></div>
        <?php endif;?>
        <div class="navbg flex-container weui-navbar"  v-if='detail.length!=0'>
            <div id='fixed' class="nav_mine">
                <a href="javascript:void(0)" class="weui_btn weui_btn_mini weui_btn_default nav1 nav_item" :class="idx==-1?'current':''" @click="jump(idx,$event)" :data-key='idx' v-for='(week,key,idx) in weeklist'>{{week}}</a>
                <div class="clearfix"></div>
            </div>
        </div>
    </div>
    <div v-if='detail.length==0'>
        <div class="weui_cells_title">暂无数据</div>
    </div>
    <div v-else>
        <div class="cont" v-for='(week,key,idx) in weeklist' :key="idx">
            <div class="weui_cells_title">{{week}}</div>
            <div class="weui_panel mt0">
                <article class="weui_article">
                    <div class="weui-loadmore weui-loadmore_line">
                        <span class="weui-loadmore__tips">正常餐</span>
                    </div>
                    <section v-for='(cont,keys,ind) in detail[key]'>
                        <div class="weui_cell_bd weui_cell_primary btd">
                            <p><i class="weui_icon_waiting"></i> {{food[keys]}}</p>
                        </div>
                        <section class="p10">
                            <div><pre>{{cont.content}}</pre></div>
                            <template v-if='cont.photo'>
                                <img :src="cont.photo" :alt="cont.photo" :id="'images'+idx" class="menuimg"  @click='funcReadImgInfo(cont.photo)'>
                            </template>
                        </section>
                    </section>

                </article>
            </div>
        </div>
    </div>
    <div class="fixedbot">
        <div class="weui_cell">
            <template v-if='weekselect==fristweek'>
                <button class="weui_btn weui_btn_disabled weui_btn_default fixedbtn"><?php echo Yii::t("portfolio", "Prev Week"); ?></button>
            </template>
            <template v-else>
                <button class="weui_btn weui_btn_default fixedbtn" @click='prev()'><?php echo Yii::t("portfolio", "Prev Week"); ?></button>
            </template>
            <template v-if='weekselect==lastweek'>
                <button class="weui_btn weui_btn_default weui_btn_disabled lastbtn fixedbtn"><?php echo Yii::t("portfolio", "Next Week"); ?></button>
            </template>
            <template v-else>
                <button class="weui_btn weui_btn_default lastbtn fixedbtn" @click='next()'><?php echo Yii::t("portfolio", "Next Week"); ?></button>
            </template>
        </div>
    </div>
</div>
<script>
    var commonDetails = <?php echo json_encode($commonDetails); ?>;
    var lunchs = <?php echo json_encode($lunchs); ?>;
    var weekDays = <?php echo json_encode($weekday); ?>;
    var schedule = <?php echo json_encode($weeks); ?>;
    var weekid = '<?php echo $week?>';
    function sortKey(array, key) {
        return array.sort(function(a, b) {
            var x = parseInt(a[key])
            var y = parseInt(b[key]);
            return((x > y) ? -1 : (x < y) ? 1 : 0)
        })
    }

    var menu = new Vue({
        el: "#menu",
        data: {
            schedule: '', //周
            weekselect: "",
            weeklist: weekDays, //星期几
            food: lunchs, //午餐或者间点
            detail: commonDetails, //详情
            lastweek: '', //最后一周
            isActive: -1, //class样式
            fristweek: '',
            scroll: '',
            weekname: '',
            show: false,
            len:''
        },
        created: function() {
            this.$nextTick(() =>{
                if($('.nav_item').length>5){
                    $('.nav_item').css('padding','0 2.75em 0 0.75em')
                }else{
                    $('.nav_item').css('padding','0 0.75em')
                }
            })
            this.weekname = schedule[weekid]
            this.weekselect = weekid
            var lastkey = "";
            for(key in schedule) {
                lastkey = key;
            }
            this.lastweek = lastkey
            var newcalendarArr = []
            for(key in schedule) {
                var calendarArrkey = {}
                calendarArrkey.id = key
                calendarArrkey.title = schedule[key]
                newcalendarArr.push(calendarArrkey)
            }
            var calendarArrs = sortKey(newcalendarArr, 'id')
            this.schedule = calendarArrs
            var frist = ''
            for(var i = 0; i < calendarArrs.length; i++) {
                frist = calendarArrs[i].id
            }
            this.fristweek = frist
        },
        watch: {
            scroll: function() {
                this.loadSroll()
            },
            show(){
                 $('.cont').click(function() {
                    $('#divselect ul').hide()
                    menu.show=false
                });
                $('#showChildern').click(function() {
                    $('#divselect ul').hide()
                    menu.show=false
                });
            },
            weeklist(){
               this.$nextTick(() =>{
                if($('.nav_item').length>5){
                    $('.nav_item').css('padding','0 2.75em 0 0.75em')
                }else{
                    $('.nav_item').css('padding','0 0.75em')
                }
             })
            }

        },
        mounted() {
            window.addEventListener('scroll', this.dataScroll, true);
        },
        methods: {
            getweekselect(id) {
                $('#divselect ul').hide()
                this.show=false
                menu.weekselect = id
                menu.weekname = schedule[id]
                $('.prev').addClass('weui_btn_disabled')
                $('.next').addClass('weui_btn_disabled')
                $.ajax({
                    url: "<?php echo $this->createUrl('cateringMenuDetail')?>",
                    type: 'post',
                    dataType: 'json',
                    data: {
                        week: id
                    },
                    success: function(data) {
                        $('.prev').removeClass('weui_btn_disabled')
                        $('.next').removeClass('weui_btn_disabled')
                        menu.weeklist = data.weekday
                        menu.food = data.lunchs
                        menu.detail = data.commonDetails
                    }
                })
            },
            prev() {
                $('.prev').addClass('weui_btn_disabled')
                this.isActive = -1
                var prevdatas=''
                for(var i=0;i<menu.schedule.length;i++){
                    if(menu.weekselect==menu.schedule[i].id){
                         prevdatas=menu.schedule[i+1]
                    }
                }
                document.querySelector(".container").scrollTop = 0
                this.getweekselect(prevdatas.id)
            },
            next() {
                var next = parseInt(this.weekselect) + 1
                $('.next').addClass('weui_btn_disabled')
                this.isActive = -1
                var nextdatas=''
                for(var i=0;i<menu.schedule.length;i++){
                    if(menu.weekselect==menu.schedule[i].id){
                         nextdatas=menu.schedule[i-1]
                    }
                }
                document.querySelector(".container").scrollTop = 0
                this.getweekselect(nextdatas.id)
            },
            dataScroll: function() {
                this.scroll = document.querySelector(".container").scrollTop;
            },
            funcReadImgInfo(cont) {
                wx.previewImage({
                    "urls": [cont],
                    "current": cont
                });
            },
            jump(index,e) {
                var jump = document.getElementsByClassName('cont');
                var total = jump[index].offsetTop - $('.weui-navbar').height();
                var dom = document.querySelector(".container")
                var distance = document.querySelector(".container").scrollTop
                // 平滑滚动，时长500ms，每10ms一跳，共50跳

                if(total > distance) {
                    var downTotal = total - distance
                    var steps = downTotal / 50
                    smoothDown()
                } else {
                    var newTotal = distance - total
                    var step = newTotal / 50
                    smoothUp()
                }
                function smoothDown() {
                    if(distance < total) {
                        distance += steps　　　　　　
                        dom.scrollTop = distance
                        setTimeout(smoothDown, 10)
                    } else {
                        dom.scrollTop = total
                    }
                }
                function smoothUp() {
                    if(distance > total) {
                        distance -= step　　　　　　
                        dom.scrollTop = distance
                        setTimeout(smoothUp, 10)
                    } else {
                        dom.scrollTop = total
                    }
                }
            },
            loadSroll: function() {
                var self = this;
                var $navs = $(".nav1");
                var sections = document.getElementsByClassName('cont');
                var height = (document.documentElement.clientHeight || document.body.clientHeight) / 2
                for(var i = sections.length - 1; i >= 0; i--) {
                    if(self.scroll >= sections[i].offsetTop - height) {
                        $navs.eq(i).addClass("current").siblings().removeClass("current")
                        break;
                    }
                    if(self.scroll <= $('.weui-navbar').height() + $('.title').height()) {
                        $navs.eq(i).siblings().removeClass("current")
                        break;
                    }
                }
            },
        }
    });
    $('.container').scroll(function() {
        if($('.container').scrollTop() >= $('.title').height() + $('.weeklist').height() + 25) {
            $(".weui-navbar").addClass('fixedTop')
        } else {
            $(".weui-navbar").removeClass('fixedTop')
        }
    });
    // $(function() {
    //     divselect("#divselect", "#inputselect");
    // });
    // divselect = function(divselectid, inputselectid) {
    //     var inputselect = $(inputselectid);
    //     $(divselectid + " cite").click(function() {
    //         var ul = $(divselectid + " ul");
    //         if(ul.css("display") == "none") {
    //             ul.slideDown("fast");
    //         } else {
    //             ul.slideUp("fast");
    //         }
    //     });
    //     $(divselectid + " ul li a").click(function() {
    //         $(divselectid + " ul").hide();
    //     });
    //     $('.cont').click(function() {
    //         $(divselectid + " ul").slideUp("fast");
    //     });
    //     $('#showChildern').click(function() {
    //         $(divselectid + " ul").slideUp("fast");
    //     });
    // };
</script>
