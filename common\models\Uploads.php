<?php

/**
 * This is the model class for table "ivy_uploads".
 *
 * The followings are the available columns in table 'ivy_uploads':
 * @property integer $id
 * @property integer $link_id
 * @property string $mod_name
 * @property string $func_name
 * @property string $file_name
 * @property string $notes
 * @property integer $update_time
 * @property integer $update_user
 */
class Uploads extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return Uploads the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_uploads';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('link_id, update_time, update_user', 'numerical', 'integerOnly'=>true),
			array('mod_name, func_name, file_name, notes', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, link_id, mod_name, func_name, file_name, notes, update_time, update_user', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'link_id' => 'Link',
			'mod_name' => 'Mod Name',
			'func_name' => 'Func Name',
			'file_name' => 'File Name',
			'notes' => 'Notes',
			'update_time' => 'Update Time',
			'update_user' => 'Update User',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('link_id',$this->link_id);
		$criteria->compare('mod_name',$this->mod_name,true);
		$criteria->compare('func_name',$this->func_name,true);
		$criteria->compare('file_name',$this->file_name,true);
		$criteria->compare('notes',$this->notes,true);
		$criteria->compare('update_time',$this->update_time);
		$criteria->compare('update_user',$this->update_user);

		return new CActiveDataProvider(get_class($this), array(
			'criteria'=>$criteria,
		));
	}
}