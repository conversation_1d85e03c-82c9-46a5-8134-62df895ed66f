<?php

// uncomment the following to define a path alias
// Yii::setPathOfAlias('local','path/to/local-folder');
// This is the main Web application configuration. Any writable
// CWebApplication properties can be configured here.
$dbname = "mims2";
$subdbname = "mimssub";
$dbuser = "root";
$dbpass = "";

$senderConfig = array(
    'host' => 'mail.ivygroup.cn',
    'port' => 25,
    'username' => 'no-reply',
    'password' => 'barney',
    'sender' => '<EMAIL>',
    'from' => '<EMAIL>',
    'fromname' => 'IvySchools',
);

$cookieDomain = ".ivynew.cn";
$baseUrl = "http://apps.ivynew.cn";
$OABaseUrl = "http://oa.ivyonline.cn";
$uploadBaseUrl = $baseUrl . "/uploads/";
$OABaseUploadPath = "D:\\www\\ivyoffline\\trunk\\htdocs\\uploads";
$uploadPath = "E:\worksoft\child.mims\appOA\uploads";

$OAUploadBaseUrl = "http://oa.ivynew.cn/uploads";
$OAUploadBasePath = "S:/Webdev/mims/trunk/oasrc/htdocs/uploads";
$xoopsVarPath = 'D:/php/mims/trunk/oasrc/XOOPS-data';
$YeepayRespondeUrl= $baseUrl.'/responder/yeepayResponder';

$loginCheckUrl = "http://oa.ivynew.cn/login.php";
$logoutUrl = "http://oa.ivynew.cn/user.php?op=logout";

$ccPreviewUrl = "http://apps-child.ivynew.cn/";

$basePath = dirname(__FILE__) . DIRECTORY_SEPARATOR . '..';

return array(
    'basePath' => $basePath,
    'name' => 'IvyOnline WebApp',
    // preloading 'log' component
    'preload' => array('log'),
    'defaultController' => 'backend/apps',
    'theme' => 'classic',
    // autoloading model and component classes
    'import' => array(
        'common.models.*',
		'common.components.*',
		'application.components.*',
		'application.modules.srbac.controllers.SBaseController'
    ),
    'modules' => array(
        'backend',
        'child',
	'teaching',
        'srbac' => array(
            'userclass' => 'User',
            'userid' => 'uid',
            'username' => 'email',
            'delimeter' => '@',
            'debug' => false,
            'pageSize' => 50,
            'superUser' => 'superDude',
            'css' => 'srbac.css',
            'ipFilters' => array('127.0.0.1', '**************', '::1'),
        ),
        'webapi',
    ),
    // application components
    'components' => array(
        'clientScript' => array(
            'class' => 'IvyClientScript',
        ),
        'session' => array(
            'autoCreateSessionTable' => false,
            'class' => 'application.components.CXoDbHttpSession',
            'cookieParams' => array('domain' => $cookieDomain, 'lifetime' => 0, 'path' => '/'),
            'connectionID' => 'db',
            'timeout' => 28800,
            'sessionTableName' => 'ivy_session',
            'sessionName' => 'XOSESSIONID2', //sessionId的名字，必须跟XOOPS那边的一样，不可更改
        ),
        'authManager' => array(
            'class' => 'application.modules.srbac.components.SDbAuthManager',
            'connectionID' => 'db',
            'itemTable' => 'auth_oa_items',
            'assignmentTable' => 'auth_oa_assignments',
            'itemChildTable' => 'auth_oa_itemchildren',
        ),
        'user' => array(
            // enable cookie-based authentication
            'class' => 'application.components.CXoWebUserStaff',
            'loginUrl' => $loginCheckUrl,
            'allowAutoLogin' => true,
			'logoutUrl' =>$logoutUrl,
        ),
        // uncomment the following to enable URLs in path-format
        'urlManager' => array(
            'baseUrl'=>$baseUrl,
            'showScriptName' => false,
            'urlFormat' => 'path',
            'rules' => array(
                'previewCC/<id:.*>' => 'backend/frontend/index',
                '<module:\w+>/<controller:\w+>/<action:\w+>' => '<module>/<controller>/<action>',
                '<controller:\w+>/<id:\d+>' => '<controller>/view',
                '<controller:\w+>/<action:\w+>/<id:\d+>' => '<controller>/<action>',
                '<controller:\w+>/<action:\w+>' => '<controller>/<action>',
            ),
        ),
        'db' => array(
            'connectionString' => 'mysql:host=localhost;dbname=' . $dbname,
            'emulatePrepare' => true,
            'username' => $dbuser,
            'password' => $dbpass,
            'charset' => 'utf8',
        ),
        'subdb'=>array(
            'class'=>'CDbConnection',
            'connectionString' => 'mysql:host=localhost;dbname='.$subdbname,
            'emulatePrepare' => true,
            'username' => $dbuser,
            'password' => $dbpass,
            'charset' => 'utf8',
//			'schemaCachingDuration' => 30000,
        ),
        'errorHandler' => array(
            // use 'site/error' action to display errors
            'errorAction' => 'backend/default',
        ),
        'cache' => array(
            'class' => 'CFileCache',
        ),
        'log' => array(
            'class' => 'CLogRouter',
            'routes' => array(
                array(
                    'class' => 'CFileLogRoute',
                    'levels' => 'error, warning',
                ),
                array(
                    'class'=>'CDbLogRoute',
                    'autoCreateLogTable'=>true,
                    'levels'=>'info',
                    'connectionID'=>'subdb',
                    'logTableName'=>'mimslog',
                ),

                // uncomment the following to show log messages on web pages
            /*
              array(
              'class'=>'CWebLogRoute',
              ),
             */
            ),
        ),
        'image'=>array(
          'class'=>'application.extensions.image.CImageComponent',
            // GD or ImageMagick
            'driver'=>'GD',
            // ImageMagick setup path
            'params'=>array('directory'=>'/opt/local/bin'),
        ),
    ),
    // application-level parameters that can be accessed
    // using Yii::app()->params['paramName']
    'params' => array(
        // this is used in contact page
        'adminEmail' => '<EMAIL>',
        'ccPreviewUrl' => $ccPreviewUrl,
        'OABaseUrl' => $OABaseUrl,
        'OABaseUploadPath' => $OABaseUploadPath,
		
        'OAUploadBaseUrl' => $OAUploadBaseUrl,
        'OAUploadBasePath' => $OAUploadBasePath,		
		
    	'uploadPath'=>$uploadPath,
    	'uploadBaseUrl'=>$uploadBaseUrl,
        'productionDomain' => 'apps.ivyonline.cn',
        'xoopsVarPath' => $xoopsVarPath,
        'YeepayRespondeUrl' => $YeepayRespondeUrl,
        'assetsVersion' => '2013726',
	    'mediaNotifyUrl' => 'http://apps.ivynew.cn/ivyMediaResponder/weeklyPhoto',
        'mediaTestingUids' => array(), # 上传图片测试人员的ID
        'siteFlag' => 'ivygroup',
        'senderConfig' => $senderConfig,
    ),
);