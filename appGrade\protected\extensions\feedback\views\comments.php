<?php if ($comments_replay):?>
<div class="ajax-re">
    <ul>
        <?php foreach ($comments_replay as $ukey=>$rep){?>
        <li class="root">
            <div class="fl imgBlock_img">
                <?php echo CHtml::image($rep['photo'], $rep['name'], array('title'=>$rep['name']))?>
            </div>
            <div class="fl">
                <div class="f_info"><?php echo $rep['name'].addColon('').nl2br($rep['com_content'])?></div>
                <p class="f_detail"><?php echo addColon(Yii::t("portfolio", 'Publish Date'));?><?php echo Mims::formatDateTime($rep['com_created_time'], 'medium', 'short')?></p>
                <?php if (isset($reply[$ukey])){?>
                <div class="f_ang_t">
                    <div class="ang_i ang_t_d"></div>
                    <div class="ang_i ang_t_u"></div>
                </div>
                <div class="comment-r">
                    <ul>
                        <?php foreach ($reply[$ukey] as $r){?>
                        <li class="root-reply">
                            <div class="fl ui_avatar"><?php echo CHtml::image($r['photo'], $r['name'], array('title'=>$r['name']))?></div>
                            <div class="r_info fl">
                                <?php echo $r['name'].addColon('').nl2br($r['com_content'])?>
                                <p class="f_detail"><?php echo addColon(Yii::t("portfolio", 'Publish Date'));?><?php echo Mims::formatDateTime($r['com_created_time'], 'medium', 'short')?></p>
                            </div>
                            <div class="c"></div>
                        </li>
                        <?php }?>
                    </ul>
                    <?php //if (!Yii::app()->user->getIsGuest()):?>
                    <?php if (Yii::app()->user->checkAccess('adminChild', array("childid" => $this->getController()->getChildId()))): ?>
                    <ul>
                        <li>
                            <div id="cinput" class="textinput" onclick="cArea(this);"><?php echo Yii::t("portfolio", 'Make a comment')?></div>
                            <div id="carea" style="display: none;">
                                <?php $form=$this->beginWidget('CActiveForm', array(
                                    'id'=>'reply_form',
                                    'enableAjaxValidation'=>true,
                                    'action'=>Yii::app()->getController()->createUrl("//portfolio/portfolio/savefeedback"),
                                    'clientOptions'=>array('validateOnSubmit'=>true, 'validateOnType'=>false),
                                )); ?>
                                <?php echo $form->textArea($model, 'com_content', array('class'=>'reply-con', 'onblur'=>'showinput()'))?>
                                <?php echo $form->hiddenField($model, 'com_class_id', array('value'=>$rep['com_class_id']))?>
                                <?php echo $form->hiddenField($model, 'com_week_num', array('value'=>$rep['com_week_num']))?>
                                <?php echo $form->hiddenField($model, 'com_root_id', array('value'=>$rep['id']))?>
                                <div class="rbtn">
                                <?php
                                    echo CHtml::ajaxSubmitButton(Yii::t("portfolio", 'Submit'), Yii::app()->getController()->createUrl("//portfolio/portfolio/savefeedback"), array('dataType'=>'json', 'success'=>'function(data){}'), array('id'=>'asub2'))
                                ?>
                                </div>
                                <?php $this->endWidget(); ?>
                            </div>
                        </li>
                    </ul>
                    <?php endif;?>
                </div>
                <?php }?>
            </div>
            <div class="c"></div>
        </li>
        <?php }?>
    </ul>
</div>
<?php endif;?>
<script>
    $('#asub2').click(function () {
        $.ajax({
            'complete':function(){clte()},
            'dataType':'json',
            'success':function(data){successFun(data)},
            'type':'POST',
            'url':'<?php echo Yii::app()->getController()->createUrl("//portfolio/portfolio/savefeedback"); ?>',
            'cache':false,
            'data':$('#reply_form').serialize()
        });
        return false;
    });
</script>