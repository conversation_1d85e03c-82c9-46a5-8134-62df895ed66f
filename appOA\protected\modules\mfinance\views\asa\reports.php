<style>
    [v-cloak] {
        display: none;
    }

    .tableHr {
        margin-top: 2px;
        margin-bottom: 2px;
    }

    .reportTbody > tr > td, .reportTbody > tr > th {
        vertical-align: middle !important;
        text-align: center;
    }
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo Yii::t('site', 'Finance Management'); ?></li>
        <li class="active">课后课财务</li>
    </ol>

    <div class="row">
        <!-- 左侧菜单 -->
        <div class="col-md-2 col-sm-2 mb10">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->getMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
                'itemCssClass' => ''
            ));
            ?>
        </div>

        <div class="col-md-10 col-sm-12">
            <div class="tab-content">
                <div id="" class="col-md-12 mb10">
                    <select autocomplete="off" class="form-control" style="width: auto" id="selectstartyear">
                        <option value="">请选择学年</option>
                        <?php
                        if(empty($_GET['year'])){
                            //默认最新的学年
                            $getUrlYear = $yearList[0];
                        }else{
                            $getUrlYear = $_GET['year'];
                        }
                        foreach ($yearList as $value){
                            if(Yii::app()->language == 'zh_cn'){
                                $msg = ' 学年';
                            }else{
                                $msg =' School Year';
                            }
                            $schoolYear = $value.'-'.($value+1).$msg;
                            $href = $this->createUrl('report', array('year'=>$value));
                            if($value == $getUrlYear){
                                echo "<option value='$value' selected>$schoolYear</option>";
                            }else{
                                echo "<option value='$value'>$schoolYear</option>";
                            }
                        }
                        ?>
                    </select>
                    <br>
                    <span>
                         <?php
                         if ($list) {
                            foreach ($list as $time => $val) {
                         ?>
                        <?php
                        $year = substr($time, 0, 4);
                        $month = substr($time, 4, 2);
                        ?>
                                <div class="panel panel-default year<?php echo $year;?>">
                                <div class="panel-heading">
                                    <b>
                                        <?php
                                        echo $year . '年' . $month . '月';
                                        ?>
                                    </b>
                                </div>
                                <div class="panel-body">
                                    <?php foreach ($val as $schoold => $item) { ?>
                                        <div class="btn-group dropup mr10 mb10">
                                            <button type="button"
                                                    onclick="showModal('<?php echo $schoold ?>','<?php echo $time ?>', <?php echo $item['id']; ?>)"
                                                    class="btn btn-<?php echo ($item['status'] == 30) ? 'success' : 'default'; ?>">
                                                <?php echo $schoolList[$schoold]; ?><span><?php echo '：¥' . $item['amount']; ?></span>
                                            </button>
                                            <?php if($item['status'] != AsaMonthReport::CONFIRM): ?>
                                                <button type="button" data-toggle="dropdown" aria-haspopup="true"
                                                        aria-expanded="false"
                                                        class="btn btn-<?php echo ($item['status'] == 30) ? 'success' : 'default'; ?> dropdown-toggle">
                                                <span class="caret"></span>
                                            </button>
                                                <ul class="dropdown-menu">
                                                <li>
                                                    <a href="javascript:review(30,'<?php echo $item['id'] ?>')">通过（考勤已确认）</a>
                                                </li>
                                                <li>
                                                    <a href="javascript:review(10,'<?php echo $item['id'] ?>')"
                                                       style="color: #a94442">驳回（需校园重新提交）</a>
                                                </li>
                                            </ul>
                                            <?php endif; ?>
                                        </div>
                                    <?php } ?>
                                </div>
                            </div>
                        <?php
                        }
                        }else{
                          ?>
                             <div class="weui-loadmore weui-loadmore_line">
                            <span class="weui-loadmore__tips"
                                  style="background: #f8f8f8"><?php echo Yii::t("message", "暂无数据"); ?></span>
                            </div>
                        <?php }?>
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="attendReportVue" tabindex="-1" role="dialog"
     data-backdrop="static" data-keyboard="false" aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-lg" style="width: 90%" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"
                        aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title"><span id="myModalLabel"></span>月 [<a :href=printLink target="_blank">打印</a>]</h4>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-bordered" style="vertical-align: middle;">
                        <thead>
                        <tr>
                            <th colspan="4"><h4 class="text-center">校园：{{schoolid}}</h4>
                            </th>
                            <th colspan="5"><h4 class="text-center">
                                    考勤区间：{{tableData.month}}</h4></th>
                            <th><h4 class="text-center">最后更新：{{tableData.update_time}}</h4>
                            </th>
                        </tr>
                        </thead>
                        <tbody class="reportTbody">
                        <tr>
                            <th width="50">NO.</th>
                            <td width="100">姓名<br/>Chinese Name</td>
                            <td width="100">身份证号码 / 护照号码<br/>ID Card # / Password #</td>
                            <td width="100">性质<br/>Source</td>
                            <td width="100">老师职位</td>

                            <td width="100">课程<br/>Activity</td>
                            <td width="150">课时小计<br>
                                课时费 × 课时 = 小计<br/>
                                (Cost × Activities = Total Cost)
                            </td>
                            <td width="100">调整金额<br/>Modify Amount</td>
                            <td width="100">总计<br/>Total Cost</td>
                            <td width="100">开户行预留姓名 / 银行账号<br/>Bank Name / Bank Account</td>
                        </tr>
                        <tr v-for="(tData,index) in tableData.vendors">
                            <th width="50">{{ index+1}}</th>
                            <td width="100">{{tData.name}}<br/>{{tData.name_en}}</td>
                            <td width="100">'{{tData.identify}}</td>
                            <td width="100">{{tData.type}}<br/>{{tData.type_en}}</td>
                            <td width="100">{{tData.position}}
                                 <div v-for="(course,cid,index) in tData.courses">
                                    <div v-for="(data,index) in course">
                                    <p>{{data.position}}</p>

                                    </div>
                                       <hr v-if="index + 1 < arrLength(tData.courses)"
                                            class="tableHr">
                                </div>
                            </td>
                            <td width="100">
                               <div v-for="(course,cid,index) in tData.courses">
                                    <div v-for="(data,index) in course">
                                    <p  v-if='cid == tableData.month' :title="data.group_cn">{{data.course_name}}</p>
                                    <p  v-else :title="data.group_cn"> {{cid}}  : {{data.course_name}}</p>

                                    </div>
                                    <hr v-if="index + 1 < arrLength(tData.courses)"
                                            class="tableHr">
                                </div>

                            </td>
                            <td width="100">
                                <div v-for="(course,cid,index) in tData.courses">
                                    <div v-for="(data,index) in course">
                                    <div v-if='data.course_count.constructor!=Array'>
                                       <p v-if='data.assistant_pay>0'>
                                         {{data.unit_price}} × {{len1(data.course_count)}} - {{data.assistant_pay}}=
                                        {{parsefloat(data.unit_price) * data.course_count - parsefloat(data.assistant_pay)}}
                                        </p>
                                        <p v-else> {{data.unit_price}} × {{len1(data.course_count)}} =
                                         {{parsefloat(data.unit_price) * data.course_count}}</p>
                                    </div>
                                    <div v-else>
                                    <p v-if='data.assistant_pay>>0'>{{data.unit_price}} × {{and(data.course_count)}}  - {{data.assistant_pay}}={{parsefloat(data.unit_price) *add(data.course_count)  - parsefloat(data.assistant_pay)}}</p>
                                    <p v-else>
                                        {{data.unit_price}} × {{and(data.course_count)}}={{parsefloat(data.unit_price) *add(data.course_count)}}
                                    </p>

                                    </div>
                                   </div>
                                    <hr v-if="index + 1 < arrLength(tData.courses)"
                                            class="tableHr">
                                </div>
                            </td>
                             <td width="150">

                                <div  v-for="(course,cid,index) in tData.variation_amout">
                                 <div v-for="(courses,cids,indexs) in tData.variation_memo">
                                 <div v-if='cid == cids'>
                                     <div v-if="course==0 || course==0.00"></div>
                                     <div v-else>
                                          <p>金额： {{course}}</p>
                                           <p v-html="'备注：'+courses+''" ></p>
                                     </div>

                                         <hr v-if="index + 1 < arrLength(tData.variation_amout)"
                                            class="tableHr">
                                 </div>
                                 </div>
                                </div>
                            </td>
                            <td width="100">{{tData.amount}}</td>

                            <td width="100">{{tData.bank_name}}<br/>'{{tData.bank_account}}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div style="clear: both"></div>
            </div>
        </div>
    </div>
</div>
<script>
    var modal = '<div class="modal fade" id="modal" class="1123" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog modal-lg" role="document"><div class="modal-content"></div></div></div>';
    $('body').append(modal);

    var attendReportVue = new Vue({
        el: "#attendReportVue",
        data: {
            schoolList: <?php echo CJSON::encode($schoolList) ?>,
//            reportData: reportData,
//            yearData: yearData,
            reportDataCheck: [],    //选中的补签数据
            years: '',
            month: '',       //操作的是哪个月
            tableData: '',  //列表数据
            reportId: '',  //列表ID
            canSub: false,  //是否让用户提交
            general: true,    //判断是否仅查看列表
            checkYear: '',
            schoolid:'',
            printLink:'',
        },
        created: function () {

        },
        updated: function () {

        },
        methods: {
            isEmptyObj: function (obj) {
                if (Object.keys(obj).length > 0) {
                    return true;
                }
                return false;
            },
            arrLength: function (arr) {
                return Object.keys(arr).length;
            },
            parsefloat: function (num) {
                return parseFloat(num);
            },
              add(add) {
                  var sum = 0;
                  for(var i= 0 ;i< add.length; i++) {
                        sum += parseInt(add[i]);
                  }
                  return sum;
                },
                and(add) {
                  var sum=''
                  for(var i= 0 ;i< add.length; i++) {
                          sum+=add[i]+'+'
                     }
                     var and='('+sum.substring(0,sum.length-1)+')'
                  return and;
                },
             len1(add) {
                  var sum=''
                  for(var i= 0 ;i< add.length; i++) {
                          sum+=add[i]
                     }
                  return sum;
                }
        },
        computed: {}
    });
    function cbUpdateHandover() {
        $('#modal').modal('hide');
        $.fn.yiiGridView.update('cashHandober');
    }
    function showModal(schoolid, date, id) {
    	attendReportVue.reportId = id;
        $.ajax({
            url: '<?php echo $this->createUrl('/masa/pay/getTableData'); ?>',
            data: {branchId: schoolid, month: date},
            type: 'get',
            dataType: 'json',
            success: function (data) {

                attendReportVue.tableData = data.data;
                attendReportVue.schoolid=schoolid;
                attendReportVue.month=data.data.month
                attendReportVue.printLink="<?php echo $this->createUrl("//masa/attend/printreport"); ?>?branchId=" + attendReportVue.schoolid + "&month=" + attendReportVue.month;
                $('#myModalLabel').text(data.data.month);
                $('#attendReportVue').modal();
            },
            error: function (data) {
                resultTip({"msg": "失败"})
            }
        });
    }
    function review(status, reportid) {
        if (status == 30) {
            var r = confirm("此操作不可逆，请务必确认工资已发放。")
            if (r != true) {
                return false;
            }
        }
        $.ajax({
            url: '<?php echo $this->createUrl('reviewReport'); ?>',
            data: {status, reportid},
            type: 'post',
            dataType: 'json',
            success: function (data) {
                if (status == 30) {
                    resultTip({"msg": "审核通过!"})
                } else {
                    resultTip({"msg": "驳回成功!"})
                }
                setTimeout(function () {
                    location.reload();
                }, 2000)
            },
            error: function (data) {
                resultTip({"msg": "失败"})
            }
        });
    }

    $("#selectstartyear").change(function (){
        var id = $(this).val();
        var newUrl =  "/mfinance/asa/report?year=" + id;
        window.location.href = newUrl;
        // $(".year"+id).siblings().hide()
        // $(".year"+id).show()
    })
</script>