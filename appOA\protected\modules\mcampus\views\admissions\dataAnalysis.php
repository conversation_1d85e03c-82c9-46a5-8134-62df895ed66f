<style>
    .table{
        table-layout: fixed;
    }
    th,td{
        text-align:center
    }
    .loading{
        width:100%;
        height:100%;
        background:#fff;
        position: absolute; 
        opacity: 0.5;
        z-index: 99
    }
    .loading span{
        width:100%;
        height:100%;
        display:block;
        background: url("<?php echo Yii::app()->theme->baseUrl?>/images/loading.gif")no-repeat center center ;
    }
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Basic'), array('//mcampus/default/index')) ?></li>
        <li class="active">招生管理</li>
    </ol>

    <div class="row">
        <!-- 左侧菜单 -->
        <div class="col-md-2 col-sm-2">
            <?php
$this->widget('zii.widgets.CMenu', array(
    'items' => $this->getMenu(),
    'id' => 'pageCategory',
    'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
    'activeCssClass' => 'active',
    'itemCssClass' => '',
));
?>
        </div>
        <div class="col-md-10 col-sm-10 ">
            <!-- <div class='charts'>

            </div> -->

            <div id='container'>
                <div>
                    <p><strong>在校数据</strong></p>
                    <p>
                        <select class="form-control  select_3 inline-block" v-model='type' @change='showDate()'>
                            <option value='' disabled >请选择</option>
                            <option value='1'>年筛选</option>
                            <option value='2'>月筛选</option>
                            <option value='3'>周筛选</option>
                        </select>
                        <select class="form-control  select_3 inline-block" v-show='type==1'  v-model='yearDate'>
                            <option value='' disabled >请选择</option>
                            <option :value='list.year' v-for='(list,index) in schoolYear'>{{list.title}}</option>
                        </select>
                        <input type="text" v-show='type==2' autocomplete="off" class="form-control  select_3 inline-block" id='monthDate'  v-model='monthDate'  placeholder="<?php echo Yii::t("newDS", "Select a date"); ?>">

                        <input type="text" v-show='type==3' autocomplete="off" class="form-control  select_3 inline-block" id='weekDate'  v-model='weekDate'  placeholder="<?php echo Yii::t("newDS", "Select a date"); ?>" >
                        <button type="button" class="btn btn-primary ml20" v-if='type!=""' @click='getSchoolData'>搜索</button>
                    </p>
                    <div>                    
                        <div v-if='loading' class='loading' ><span></span></div>
                        <table class='table table-bordered' v-if='schoolData.length!=0'>
                            <thead>
                                <tr>
                                    <th>Campus </th>
                                    <th colspan='5' class='text-center'>{{schoolDataPeriod.start}} - {{schoolDataPeriod.end}}</th>
                                </tr>
                                <tr>
                                    <th>Year Group  </th>
                                    <th >Opening Balance {{schoolDataPeriod.start}}</th>
                                    <th >New Intake </th>
                                    <th >Withdrawal </th>
                                    <th >Net Intake</th>
                                    <th >Closing Balance {{schoolDataPeriod.end}}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for='(list,index) in schoolData'>
                                    <td>{{list.classTitle}}</td>
                                    <td>{{list.first}}</td>
                                    <td>{{list.enter}}</td>
                                    <td>{{list.quit}}</td>
                                    <td>{{list.net}}</td>
                                    <td>{{list.last}}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class='mt20'>
                    <p><strong>返校数据</strong></p>
                    <p> 
                        <select class="form-control  select_3 inline-block"   v-model='returnYid' @change='returnData'>
                            <option value='' disabled >请选择学年</option>
                            <option :value='list.yid' v-for='(list,index) in returnSchoolYear'>{{list.year_title}}</option>
                        </select>
                        <!-- <button type="button" class="btn btn-primary ml20"  @click='returnData'>搜索</button> -->
                    </p>
                    <div v-if='returnLoading' class='loading' ><span></span></div>
                    <table class='table table-bordered'>
                        <thead>
                            <tr>
                                <th>Current Grade</th>
                                <th >Current  Student</th>
                                <th >Paid</th>
                                <th >Unpaid </th>
                                <th >Total Return</th>
                                <th >Undecided</th>
                                <th >Not Return</th>
                                <th v-for='(item,key,index) in returnList.from_to_school_title'>{{item}}</th>
                                <th >Not Filled</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for='(list,index) in returnList.statistical_list'>
                                <td>{{list.class_title}}</td>
                                <td>{{list.child}}</td>
                                <td>{{list.paid}}</td>
                                <td>{{list.unpaid}}</td>
                                <td>{{list.return_school}}</td>
                                <td>{{list.pending}}</td>
                                <td>{{list.not_return}}</td>
                                <td  v-for='(item,key,i) in returnList.from_to_school_title'>{{list[key]}}</td>
                                <td>{{list.unmarked}}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <!-- <div>
                    <p>新学年数据（返校+新生）  </p>
                    <table class='table table-bordered'>
                        <thead>
                            <tr>
                                <th colspan='9' class='text-center'>22-23 DT New Students Data</th>
                                <th colspan='5' class='text-center'>DT Returning Data </th>
                            </tr>
                            <tr>
                                <th>Current Grade 2021-2</th>
                                <th >Applications</th>
                                <th >Interviewed</th>
                                <th >Accepted </th>
                                <th >Reject</th>
                                <th >Paid</th>
                                <th >Unpaid</th>
                                <th >Not Enroll </th>
                                <th >Total New Students </th>

                                <th >SLT Campus</th>
                                <th >Transfer from DS</th>
                                <th >Transfer From Ivy </th>
                                <th >DT Returning</th>
                                <th >Total Return </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for='(list,index) in schoolData'>
                                <td>{{list.grade}}</td>
                                <td>{{list.applications}}</td>
                                <td>{{list.interviewed}}</td>
                                <td>{{list.accepted}}</td>
                                <td>{{list.reject}}</td>
                                <td>{{list.unpaid}}</td>
                                <td>{{list.unpaid}}</td>
                                <td>{{list.unpaid}}</td>
                                <td>{{list.unpaid}}</td>

                                <td>{{list.reject}}</td>
                                <td>{{list.unpaid}}</td>
                                <td>{{list.unpaid}}</td>
                                <td>{{list.unpaid}}</td>
                                <td>{{list.unpaid}}</td>
                            </tr>
                        </tbody>
                    </table>
                </div> -->
            </div>
        </div>

    </div>
</div>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>

<script src="//cdn.bootcss.com/highcharts/4.2.5/highcharts.js"></script>
<script src="//cdn.bootcss.com/highcharts/4.2.5/modules/exporting.js"></script>
<script>
$(function () {
    var data = <?php echo $knowusList; ?>;
    console.log(data);
    $(document).ready(function () {

        // Build the chart
        $('.charts').highcharts({
            chart: {
                plotBackgroundColor: null,
                plotBorderWidth: null,
                plotShadow: false,
                type: 'pie'
            },
            title: {
                text: '如何知道艾毅'
            },
            tooltip: {
                pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b>'
            },
            plotOptions: {
                pie: {
                    allowPointSelect: true,
                    cursor: 'pointer',
                    dataLabels: {
                        enabled: true
                    },
                    showInLegend: true
                }
            },
            series: [{
                name: 'Brands',
                colorByPoint: true,
                data: data,
            }]
        });
    });
});

    
    var container = new Vue({
        el: "#container",
        data: {
            type:3,
            weekDate:'',
            monthDate:'',
            yearDate:'',
            schoolData:[],
            schoolDataPeriod:[],
            weekStartDate:"",
            weekEndDate:'',
            schoolYear:[],
            loading:false,

            returnYid:'',
            returnSchoolYear:[],
            returnList:{},
            returnLoading:false
        },
        watch:{
           
        },
        created:function(){
            this.getCurrentWeekStartTimeAndEndTime()

            let that=this
            this.$nextTick(() => {
                $( "#monthDate").datepicker({
                    showWeek: false,
                    changeYear: true,
                    showButtonPanel: true,
                    changeMonth: true,
                    dateFormat: 'yy-mm',
                    onClose: function(dateText, inst) {
                        container.monthDate=dateText
                    }
                });
                $("#weekDate").datepicker({
                    changeMonth: true,
                    showWeek: true,
                    changeYear: true,
                    showButtonPanel: true,
                    dateFormat: 'yy-mm-dd',
                    onSelect: function(date, inst) {
                        var date = $(this).datepicker('getDate');
                        that.getCurrentWeekStartTimeAndEndTime(date)
                    }
                });
            })
            this.getSchoolData()
            this.returnData()
        },
        methods: {
            getCurrentWeekStartTimeAndEndTime(time) {
                const current = time ? time : new Date();
                // const current =  new Date();
                // current是本周的第几天
                let nowDayOfWeek = current.getDay();
                if (nowDayOfWeek === 0) nowDayOfWeek = 7;
                const dayNum = 1 * 24 * 60 * 60 * 1000;
                // 获取本周星期一的时间，星期日作为一周的第一天
                const firstDate = new Date(current.valueOf() - nowDayOfWeek * dayNum);
                const lastDate = new Date(new Date(firstDate).valueOf() + 6 * dayNum);
                // 获取本周星期天的时间，星期天作为一周的最后一天
                this.weekStartDate =firstDate.getFullYear()+'-'+(firstDate.getMonth()+1)+'-'+ firstDate.getDate()
                this.weekEndDate = lastDate.getFullYear()+'-'+(lastDate.getMonth()+1)+'-'+ lastDate.getDate()
                this.weekDate=this.weekStartDate+'/'+this.weekEndDate
            },
            showDate(){
                $("#ui-datepicker-div").unbind('mouseover').unbind('mouseout').unbind('mousemove');
                if(this.type==3){
                    this.$nextTick(()=>{
                        $('#ui-datepicker-div').on('mousemove', '.ui-datepicker-calendar  tr', function() { $(this).find('td a').addClass('ui-state-hover'); });
                        $('#ui-datepicker-div').on('mouseleave', '.ui-datepicker-calendar tr', function() { $(this).find('td a').removeClass('ui-state-hover'); });
                    })
                }
                if(this.type==2){
                    this.$nextTick(() => {
                        $('#ui-datepicker-div').on('mousemove', '.ui-datepicker-calendar tbody', function() { $(this).find('td a').addClass('ui-state-hover'); });
                        $('#ui-datepicker-div').on('mouseleave', '.ui-datepicker-calendar tbody', function() { $(this).find('td a').removeClass('ui-state-hover'); });
                    });
                }
                if(this.type==1){
                    if(this.schoolYear.length!=0){
                        return
                    }
                    $.ajax({
                        url: '<?php echo $this->createUrl("getSchoolYear") ?>',
                        type: "post",
                        dataType: 'json',
                        success: function(data) {
                            if (data.state == 'success') {
                                container.schoolYear=data.data.list
                            } else {
                                resultTip({  
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                        },
                        error: function(data) {

                        },
                    })
                }
            },
            getSchoolData(){
                var dataList={}
                if(this.type==1){
                    dataList={
                        "startdate":this.yearDate,
	                    "enddate":this.yearDate
                    }
                }else if(this.type==2){
                    dataList={
                        "startdate":this.monthDate,
	                    "enddate":this.monthDate
                    }
                }else if(this.type==3){
                    dataList={
                        "startdate":this.weekStartDate,
	                    "enddate":this.weekEndDate
                    }
                }
                if(dataList.startdate=='' || dataList.enddate=='' ){
                    resultTip({  
                        error: 'warning',
                        msg: '请选择日期'
                    });
                    return
                }
                let that=this
                this.loading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("schoolData") ?>',
                    type: "post",
                    dataType: 'json',
                    data:dataList,
                    success: function(data) {
                        if (data.state == 'success') {
                            var firstNum=0
                            var lastNum=0
                            var enterNum=0
                            var netNum=0
                            var quitNum=0
                            for (var i = 0; i < data.data.list.length; i++) {
                                firstNum+=parseInt(data.data.list[i].first) 
                                lastNum+=parseInt(data.data.list[i].last) 
                                enterNum+=parseInt(data.data.list[i].enter) 
                                netNum+=parseInt(data.data.list[i].net) 
                                quitNum+=parseInt(data.data.list[i].quit) 
                            }
                            data.data.list.push({
                                enter:enterNum,
                                first: firstNum,
                                last: lastNum,
                                net: netNum,
                                quit: quitNum,
                                classTitle:'合计'
                            })
                            that.schoolData=data.data.list
                            that.schoolDataPeriod=data.data.period

                        } else {
                            resultTip({  
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.loading=false
                    },
                    error: function(data) {
                        that.loading=false
                    },
                })
            },
            returnData(){
                let that=this
                this.returnLoading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("returnSchoolData") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        yid:this.returnYid
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.returnSchoolYear=data.data.year_list
                            that.returnYid=data.data.select_yid
                            var childNum=0
                            var paidNum=0
                            var unpaidNum=0
                            var return_schoolNum=0
                            var not_returnNum=0
                            var pendingNum=0
                            var unmarkedNum=0
                            var from_DSNum=0
                            var from_QFNum=0
                            var from_SLTNum=0
                            for (var i = 0; i < data.data.statistical_list.length; i++) {
                                childNum+=parseInt(data.data.statistical_list[i].child) 
                                paidNum+=parseInt(data.data.statistical_list[i].paid) 
                                unpaidNum+=parseInt(data.data.statistical_list[i].unpaid) 
                                return_schoolNum+=parseInt(data.data.statistical_list[i].return_school) 
                                not_returnNum+=parseInt(data.data.statistical_list[i].not_return) 
                                pendingNum+=parseInt(data.data.statistical_list[i].pending) 
                                unmarkedNum+=parseInt(data.data.statistical_list[i].unmarked) 
                                
                                from_DSNum+=parseInt(data.data.statistical_list[i].from_DS) 
                                from_QFNum+=parseInt(data.data.statistical_list[i].from_QF)
                                from_SLTNum+=parseInt(data.data.statistical_list[i].from_SLT) 
                            }
                            data.data.statistical_list.push({
                                child: childNum,
                                paid: paidNum,
                                unpaid:unpaidNum,
                                return_school: return_schoolNum,
                                not_return: not_returnNum,
                                pending: pendingNum,
                                unmarked: unmarkedNum,
                                from_DS:from_DSNum,
                                from_QF:from_QFNum,
                                from_SLT:from_SLTNum,
                                class_title:'合计'
                            })
                            that.returnList=data.data
                        }
                        that.returnLoading=false

                    },
                    error: function(data) {
                        that.returnLoading=false
                    },
                })
            }
        }
    })
</script>