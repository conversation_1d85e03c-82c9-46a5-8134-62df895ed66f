<?php

/**
 * This is the model class for table "ivy_feemgt_fee_other".
 *
 * The followings are the available columns in table 'ivy_feemgt_fee_other':
 * @property integer $id
 * @property string $type
 * @property string $branchid
 * @property integer $yid
 * @property string $en_title
 * @property string $cn_title
 * @property double $amount
 * @property integer $userid
 * @property integer $status
 * @property string $ufida_number
 * @property integer $updated_timestamp
 */
class FeemgtFeeOther extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return FeemgtFeeOther the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_feemgt_fee_other';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('type, branchid, yid, en_title, cn_title, amount, userid, updated_timestamp', 'required'),
			array('yid, userid, status, updated_timestamp', 'numerical', 'integerOnly'=>true),
			array('amount', 'numerical'),
			array('type', 'length', 'max'=>20),
			array('branchid', 'length', 'max'=>10),
			array('en_title, cn_title', 'length', 'max'=>255),
			array('ufida_number', 'length', 'max'=>225),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, type, branchid, yid, en_title, cn_title, amount, userid, status, ufida_number, updated_timestamp', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'type' => 'Type',
			'branchid' => 'Branchid',
			'yid' => 'Yid',
			'en_title' => 'En Title',
			'cn_title' => 'Cn Title',
			'amount' => 'Amount',
			'userid' => 'Userid',
			'status' => 'Status',
			'ufida_number' => 'Ufida Number',
			'updated_timestamp' => 'Updated Timestamp',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('type',$this->type,true);
		$criteria->compare('branchid',$this->branchid,true);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('en_title',$this->en_title,true);
		$criteria->compare('cn_title',$this->cn_title,true);
		$criteria->compare('amount',$this->amount);
		$criteria->compare('userid',$this->userid);
		$criteria->compare('status',$this->status);
		$criteria->compare('ufida_number',$this->ufida_number,true);
		$criteria->compare('updated_timestamp',$this->updated_timestamp);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}