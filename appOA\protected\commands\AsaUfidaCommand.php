<?php

/**
 * 生成 NC 数据（生成八月数据）, 不传日期则生成当月数据
 * yiic asaufida all 20210801
 */
class AsaUfidaCommand extends CConsoleCommand
{

    protected $schoolIds = array();
    protected $timeStart = "";
    protected $timeEnd = "";
    protected $date = "";
    protected $startYear = 2021;

    public function init()
    {
        Yii::import('common.models.asainvoice.*');
        parent::init();
    }

    public function useHelp()
    {
        echo "--------------------------------------------------------------------------------" . "\r\n";
        echo "Usage: yiic asaufida general_all 2021-03 Param1<string> Param2<date(y-m)>" . "\r\n";
        echo "--------------------------------------------------------------------------------" . "\r\n";
        echo "general_payment 处理付款数据\r\n";
        echo "general_refund 处理退费数据\r\n";
        echo "general_exchange 处理退费数据\r\n";
        echo "all 全部生成\r\n";
        echo "--------------------------------------------------------------------------------" . "\r\n";
        Yii::app()->end();
    }

    public function run($args)
    {
        error_reporting(E_ALL);
        if (!isset($args[0])) {
            $this->useHelp();
        }

        $timeStamp = isset($args[1]) ? strtotime($args[1]) : false;
        if (isset($args[1]) && $timeStamp) {
            $this->date = date("Y-m", $timeStamp);
        } else {
            $this->date = date("Y-m", time());
        }

        $this->timeStart = strtotime($this->date);
        $this->timeEnd = mktime(0, 0, 0, date("n", $this->timeStart) + 1, 1, date("Y", $this->timeStart)) - 1;

        switch ($args[0]) {
            case 'general_payment':
                echo "general_payment 处理" . $this->date . "付款数据\r\n";
                $this->generatePayment();
                break;
            case 'general_refund':
                $this->generateRefund();
                echo "general_refund 处理" . $this->date . "退费数据\r\n";
                break;
            default:
                $this->generatePayment();
                $this->generateRefund();
                $this->processExchange();
                echo "all 处理" . $this->date . "付款及退费数据\r\n";
                break;
        }
    }

    // ******************************************* 处理收费 ********************************************
    // 处理收费数据
    public function generatePayment()
    {
        // 查找处理时间段内账单的数量
        $crit = new CDbCriteria();
        $crit->compare('status', AsaInvoice::STATS_PAID);
        // created_from = 4 为差价账单
        $crit->compare('created_from', '<>' . "4");
        $crit->compare('pay_type', array(AsaInvoice::CASH, AsaInvoice::WECHAT));
        $crit->compare('amount_actual', '>0');
        $crit->compare('updated', '>=' . $this->timeStart);
        $crit->compare('updated', '<=' . $this->timeEnd);
        $crit->order = "updated ASC";
        $count = AsaInvoice::model()->count($crit);
        $limit = 200;
        $cycle = ceil($count / $limit);
        echo "账单数量：" . $count . "\r\n";
        // 查找处理时间段内账单的详情
        for ($i = 0; $i < $cycle; $i++) {
            $crit->limit = $limit;
            $crit->offset = $i * $limit;
            $invoiceModels = AsaInvoice::model()->findAll($crit);
            foreach ($invoiceModels as $invoiceModel) {
                // 处理收入
                $this->processPaymentIncome($invoiceModel);
                // 处理分摊
                $this->processPaymentShare($invoiceModel);
            }
        }
    }

    // 处理收费数据的 income
    public function processPaymentIncome($invoiceModel)
    {
        // 判断是否已生成过收入
        if ($invoiceModel->income_status == 1) {
            return;
        }
        $transaction = Yii::app()->subdb->beginTransaction();
        try {
            foreach ($invoiceModel->item as $item) {
                // 过滤以前的学年
                if ($item->asaCourseGroup->startyear < $this->startYear) {
                    continue;
                }
                // 判断金额是否大于0
                if ($item->actual_total <= 0) {
                    continue;
                }
                // pay_type 1 现金，2 微信
                $incomeModel = new AsaUfidaIncome();
                $incomeModel->invoice_id = $item->id;
                $incomeModel->order_id = $item->order_id;
                $incomeModel->school_id = $item->schoolid;
                $incomeModel->child_id = $invoiceModel->childid;
                $incomeModel->group_id = $item->course_group_id;
                $incomeModel->group_name = $item->asaCourseGroup->title_cn;
                $incomeModel->course_id = $item->course_id;
                $incomeModel->course_name = $item->asacourse->title_cn;
                $incomeModel->type = AsaUfidaIncome::UFIDA_TYPE_PAY;
                $incomeModel->payment_timestamp = $invoiceModel->updated;
                $incomeModel->transactiontype = 0;
                $incomeModel->transferid = 0;
                $incomeModel->fee_type = $item->asaCourseGroup->season;
                $incomeModel->loan = AsaUfidaIncome::LOAN;
                $incomeModel->loan_accounts_code = AsaUfidaIncome::LOAN_CODE;
                $incomeModel->status = 0;
                $incomeModel->timestmp = $this->timeEnd;
                $incomeModel->memo = $invoiceModel->title;
                if ($invoiceModel->pay_type == AsaInvoice::CASH) {
                    $incomeModel->borrow = AsaUfidaIncome::BANK_BORROW;
                    $incomeModel->borrow_accounts_code = AsaUfidaIncome::BANK_BORROW_CODE;
                    $incomeModel->amount = $item->actual_total;
                }
                if ($invoiceModel->pay_type == AsaInvoice::WECHAT) {
                    $incomeModel->borrow = AsaUfidaIncome::WECHAT_BORROW;
                    $incomeModel->borrow_accounts_code = AsaUfidaIncome::WECHAT_BORROW_CODE;
                    $incomeModel->amount = $item->actual_total;
                }
                if ($incomeModel->save()) {
                    echo $invoiceModel->id . " 收入处理成功\r\n";
                } else {
                    $error = current($incomeModel->getErrors());
                    throw new Exception($invoiceModel->id . " 数据保存失败 {$error[0]} \r\n");
                }
            }
            $invoiceModel->income_status = 1;
            $invoiceModel->save();
            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();
            echo $e->getMessage();
            Yii::app()->end();
        }
    }

    // 处理收费数据的 share
    public function processPaymentShare($invoiceModel)
    {
        // 判断是否已生成过分摊
        if ($invoiceModel->share_status == 1) {
            return;
        }
        $transaction = Yii::app()->subdb->beginTransaction();
        try {
            foreach ($invoiceModel->item as $item) {
                // 过滤以前的学年
                if ($item->asaCourseGroup->startyear < $this->startYear) {
                    continue;
                }
                // 判断金额是否大于0
                if ($item->actual_total <= 0) {
                    continue;
                }
                // 判断购买的课程是否当月退费，退全部
                $flag = false;
                if ($item->refund_status > 0) {
                    $refundModel = AsaRefund::model()->findByPk($item->refund_status);
                    if ($refundModel && abs($refundModel->refund_total_amount - $item->actual_total) < 0.01) {
                        $flag = true;
                        $this->processRefundIncome($refundModel);
                        $refundModel->share_status = 1;
                        $refundModel->income_status = 1;
                        $refundModel->save();
                    }
                }
                if (!$flag) {
                    $scheduleId = $item->asacourse->schedule_id;
                    // 查找课程持续的时间
                    $scheduleCrit = new CDbCriteria();
                    $scheduleCrit->compare("schedule_id", $scheduleId);
                    $scheduleCrit->order = "schedule_date ASC";
                    $scheduleItems = AsaScheduleItem::model()->findAll($scheduleCrit);
                    $monthItem = array();
                    foreach ($scheduleItems as $k => $scheduleItem) {
                        // 付款节数小于课程实际节数取后面的课程时间
                        if (count($scheduleItems) > ($k + $item->class_count)) {
                            continue;
                        }
                        $monthItem[] = substr($scheduleItem->schedule_date, 0, 6);
                    }
                    $monthToAmount = $this->generateMonthToAmount($monthItem, $item->actual_total);
                    if (count($monthToAmount) > 0) {
                        // 按照月份分摊
                        $grand = 0;
                        foreach ($monthToAmount as $month => $amount) {
                            // 如果之前月份未分摊则分摊到当月
                            if (date("Ym", $this->timeStart) > $month) {
                                $grand += $amount;
                                continue;
                            }

                            if (date("Ym", $this->timeStart) == $month) {
                                $amount += $grand;
                            }

                            $shareModel = new AsaUfidaShare();
                            $shareModel->month = $month;
                            $shareModel->month_stamp = strtotime($month . "01");
                            $shareModel->invoice_id = $item->id;
                            $shareModel->child_id = $invoiceModel->childid;
                            $shareModel->order_id = $item->order_id;
                            $shareModel->borrow = AsaUfidaIncome::LOAN;
                            $shareModel->loan = AsaUfidaIncome::SHARE;
                            $shareModel->borrow_accounts_code = AsaUfidaIncome::LOAN_CODE;
                            $shareModel->loan_accounts_code = AsaUfidaIncome::SHARE_CODE;
                            $shareModel->amount = $amount;
                            $shareModel->fee_type = $item->asaCourseGroup->season;
                            $shareModel->school_id = $item->schoolid;
                            $shareModel->group_id = $item->course_group_id;
                            $shareModel->group_name = $item->asaCourseGroup->title_cn;
                            $shareModel->startyear = $item->asaCourseGroup->startyear;
                            $shareModel->course_id = $item->course_id;
                            $shareModel->course_name = $item->asacourse->title_cn;
                            $shareModel->memo = "结转收入-" . $shareModel->course_name . "-" . $month;
                            $shareModel->status = 0;
                            $shareModel->timestmp = time();
                            if ($shareModel->save()) {
                                echo $invoiceModel->id . " 分摊处理成功\r\n";
                            } else {
                                $error = current($shareModel->getErrors());
                                throw new Exception($invoiceModel->id . " 数据保存失败 {$error[0]} \r\n");
                            }
                        }
                    }
                }
            }
            $invoiceModel->share_status = 1;
            $invoiceModel->save();
            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();
            echo $e->getMessage();
            Yii::app()->end();
        }
    }

    // ******************************************* 处理退费 ********************************************

    // 处理退费数据
    public function generateRefund()
    {
        // 查找处理时间段内退费的数量
        $crit = new CDbCriteria();
        $crit->compare('status', 5);
        $crit->compare('refund_total_amount', ">0");
        $crit->compare('updated_done', ">=" . $this->timeStart);
        $crit->compare('updated_done', "<=" . $this->timeEnd);
        // 退款方式，1微信，3个人账户（refund_reason：3换课自动退费，4换课后的课程退费，都需要处理）
        $crit->compare('refund_method', 1);
        $refundModels = AsaRefund::model()->findAll($crit);
        foreach ($refundModels as $refundModel) {
            // 过滤以前的学年
            if ($refundModel->asaCourseGroup->startyear < $this->startYear) {
                continue;
            }
            // 换课另外处理
            if ($refundModel->refund_type == 5) {
                continue;
            }
            // 处理收入
            $this->processRefundIncome($refundModel);
            // 处理分摊
            $this->processRefundShare($refundModel);
        }
    }

    // 处理退费数据的 income
    public function processRefundIncome($refundModel)
    {
        if ($refundModel->income_status == 0) {
            $incomeModel = new AsaUfidaIncome();
            $incomeModel->invoice_id = $refundModel->invoice_item_id;
            $incomeModel->order_id = $refundModel->id;
            $incomeModel->child_id = $refundModel->childid;
            $incomeModel->school_id = $refundModel->site_id;
            $incomeModel->group_id = $refundModel->program_id;
            $incomeModel->group_name = $refundModel->asaCourseGroup->title_cn;
            $incomeModel->course_id = $refundModel->course_id;
            $incomeModel->course_name = $refundModel->asaCourse->title_cn;
            $incomeModel->type = AsaUfidaIncome::UFIDA_TYPE_REFUND;
            $incomeModel->payment_timestamp = $refundModel->updated;
            $incomeModel->transactiontype = 0;
            $incomeModel->fee_type = $refundModel->asaCourseGroup->season;
            $incomeModel->transferid = 0;
            $incomeModel->status = 0;
            $incomeModel->memo = $incomeModel->course_name . "-退费";
            $incomeModel->timestmp = $this->timeEnd;
            $incomeModel->borrow = AsaUfidaIncome::LOAN;
            $incomeModel->borrow_accounts_code = AsaUfidaIncome::LOAN_CODE;
            $incomeModel->loan = AsaUfidaIncome::WECHAT_BORROW;
            $incomeModel->loan_accounts_code = AsaUfidaIncome::WECHAT_BORROW_CODE;
            $incomeModel->amount = $refundModel->refund_total_amount;
            if ($incomeModel->save()) {
                $refundModel->income_status = 1;
                $refundModel->save();
                echo $refundModel->id . " 退费收入处理成功\r\n";
            } else {
                $error = current($incomeModel->getErrors());
                echo $refundModel->id . " 数据保存失败 {$error[0]} \r\n";
                Yii::app()->end();
            }
        }
    }

    // 处理退费数据的 share
    public function processRefundShare($refundModel)
    {
        if ($refundModel->share_status == 0) {
            // 查找账单相对应的已分摊记录
            $crit = new CDbCriteria();
            $crit->compare("invoice_id", $refundModel->invoice_item_id);
            $crit->compare("amount", ">=0");
            $sharedModels = AsaUfidaShare::model()->findAll($crit);

            if (count($sharedModels) > 0) {
                // 判断是全部退款还是部分退款
                $refundMonth = array();
                if (count($refundModel->asaRefunditem) > 0) {
                    $scheduleItemIds = array();
                    foreach ($refundModel->asaRefunditem as $item) {
                        $scheduleItemIds[] = $item->schedule_item_id;
                    }
                    $scheduleItemModels = AsaScheduleItem::model()->findAllByPk($scheduleItemIds);
                    foreach ($scheduleItemModels as $scheduleItemModel) {
                        $refundMonth[] = substr($scheduleItemModel->schedule_date, 0, 6);
                    }
                } else {
                    $scheduleCrit = new CDbCriteria();
                    $scheduleCrit->compare("schedule_id", $refundModel->asaCourse->schedule_id);
                    $scheduleCrit->order = "schedule_date ASC";
                    $scheduleItemModels = AsaScheduleItem::model()->findAll($scheduleCrit);
                    foreach ($scheduleItemModels as $k => $scheduleItemModel) {
                        if (count($scheduleItemModels) > ($k + $refundModel->refund_class_count)) {
                            continue;
                        }
                        $refundMonth[] = substr($scheduleItemModel->schedule_date, 0, 6);
                    }
                }
                $monthToAmount = $this->generateMonthToAmount($refundMonth, $refundModel->refund_total_amount);
                $sharedTotal = 0;
                foreach ($sharedModels as $sharedModel) {
                    if (isset($monthToAmount[$sharedModel->month])) {
                        // 已导入
                        if ($sharedModel->status == 1) {
                            $sharedTotal += $sharedModel->amount;
                        } else {
                            $refundShare = new AsaUfidaShare();
                            $refundShare->setAttributes($sharedModel->getAttributes());
                            $refundShare->amount = (0 - $monthToAmount[$sharedModel->month]);
                            $refundShare->order_id = $refundModel->id;
                            $refundShare->memo = "冲分摊已结转收入";
                            $refundShare->save();
                        }
                    }
                    if ($sharedTotal > 0) {
                        $newShareModel = new AsaUfidaShare();
                        $newShareModel->setAttributes($sharedModels[0]->getAttributes());
                        $newShareModel->amount = (0 - $sharedTotal);
                        $newShareModel->month = date("Ym", $this->timeStart);
                        $newShareModel->month_stamp = strtotime($newShareModel->month . "01");
                        $newShareModel->borrow = AsaUfidaIncome::WECHAT_BORROW;
                        $newShareModel->borrow_accounts_code = AsaUfidaIncome::WECHAT_BORROW_CODE;
                        $newShareModel->loan = AsaUfidaIncome::SHARE;
                        $newShareModel->loan_accounts_code = AsaUfidaIncome::SHARE_CODE;
                        $newShareModel->invoice_id = $refundModel->invoice_item_id;
                        $newShareModel->order_id = $refundModel->id;
                        $newShareModel->memo = "冲分摊已结转收入";
                        $newShareModel->status = 0;
                        $newShareModel->timestmp = time();
                        if ($newShareModel->save()) {
                            echo $refundModel->id . ": " . $sharedModel->month . " 退费分摊处理成功\r\n";
                        } else {
                            $error = current($newShareModel->getErrors());
                            echo " 数据保存失败 {$error[0]} \r\n";
                            Yii::app()->end();
                        }
                    }
                }
            }

            $refundModel->share_status = 1;
            $refundModel->save();
        }
    }

    // ******************************************* 处理换课 **********  **********************************
    /**
     * 处理换课的情况
     *
     * @return void
     */
    public function processExchange()
    {
        // 查找已完成的换课
        $crit = new CDbCriteria();
        $crit->compare("status", 1);
        $crit->compare("updated_time", ">=" . $this->timeStart);
        $crit->compare("updated_time", "<=" . $this->timeEnd);
        $exchangeModels = AsaCourseExchange::model()->findAll($crit);
        foreach ($exchangeModels as $model) {
            // 判断是否已处理过数据
            if ($model->ufida_status == 1) {
                continue;
            }
            // 旧课信息
            $oldCourse = AsaCourse::model()->findByPk($model->old_cid);
            // 新课信息
            $newCourse = AsaCourse::model()->findByPk($model->new_cid);
            // 过滤以前的学年
            if ($newCourse->coursesgroup->startyear < $this->startYear) {
                continue;
            }
            $transaction = Yii::app()->subdb->beginTransaction();
            try {
                // 处理收入（一退一进）
                $incomeModel = new AsaUfidaIncome();
                // 换课ID
                $incomeModel->invoice_id = $model->id;
                // 关联账单ID
                $incomeModel->child_id = $model->childid;
                $incomeModel->order_id = $model->link_id;
                $incomeModel->school_id = $model->schoolid;
                $incomeModel->group_id = $oldCourse->gid;
                $incomeModel->group_name = $oldCourse->coursesgroup->title_cn;
                $incomeModel->course_id = $oldCourse->id;
                $incomeModel->course_name = $oldCourse->title_cn;
                $incomeModel->type = AsaUfidaIncome::UFIDA_TYPE_REFUND;
                $incomeModel->fee_type = $oldCourse->coursesgroup->season;
                $incomeModel->payment_timestamp = $model->updated_time;
                $incomeModel->transactiontype = 0;
                $incomeModel->transferid = 0;
                $incomeModel->borrow = AsaUfidaIncome::LOAN;
                $incomeModel->borrow_accounts_code = AsaUfidaIncome::LOAN_CODE;
                $incomeModel->loan = AsaUfidaIncome::WECHAT_BORROW;
                $incomeModel->loan_accounts_code = AsaUfidaIncome::WECHAT_BORROW_CODE;
                $incomeModel->amount = $model->old_course_amount;
                $incomeModel->status = 0;
                $incomeModel->memo = "换课旧课程退费";
                $incomeModel->timestmp = $this->timeEnd;
                if (!$incomeModel->save()) {
                    $error = current($incomeModel->getErrors());
                    throw new Exception($model->id . " 换课退费数据保存失败 {$error[0]} \r\n");
                }
                $payIncomeModel = new AsaUfidaIncome();
                $payIncomeModel->setAttributes($incomeModel->getAttributes());
                $payIncomeModel->group_id = $newCourse->gid;
                $payIncomeModel->group_name = $newCourse->coursesgroup->title_cn;
                $payIncomeModel->course_id = $newCourse->id;
                $payIncomeModel->type = AsaUfidaIncome::UFIDA_TYPE_PAY;
                $payIncomeModel->course_name = $newCourse->title_cn;
                $payIncomeModel->loan = AsaUfidaIncome::LOAN;
                $payIncomeModel->loan_accounts_code = AsaUfidaIncome::LOAN_CODE;
                $payIncomeModel->borrow = AsaUfidaIncome::WECHAT_BORROW;
                $payIncomeModel->borrow_accounts_code = AsaUfidaIncome::WECHAT_BORROW_CODE;
                $payIncomeModel->amount = $model->new_course_amount;
                $payIncomeModel->memo = "换课新课程费用";
                if (!$payIncomeModel->save()) {
                    $error = current($payIncomeModel->getErrors());
                    throw new Exception($model->id . " 换课支付数据保存失败 {$error[0]} \r\n");
                }
                // 处理分摊，原课程处理退费分摊，新课程新增分摊
                // 处理旧课
                // 查找账单相对应的已分摊记录
                $crit = new CDbCriteria();
                $crit->compare("child_id", $model->childid);
                $crit->compare("course_id", $model->old_cid);
                $crit->compare("amount", ">=0");
                $sharedModels = AsaUfidaShare::model()->findAll($crit);

                $refundMonth = array();
                $scheduleCrit = new CDbCriteria();
                $scheduleCrit->compare("schedule_id", $oldCourse->schedule_id);
                $scheduleCrit->order = "schedule_date ASC";
                $scheduleItemModels = AsaScheduleItem::model()->findAll($scheduleCrit);
                foreach ($scheduleItemModels as $k => $scheduleItemModel) {
                    if ($k < $model->old_course_num) {
                        continue;
                    }
                    $refundMonth[] = substr($scheduleItemModel->schedule_date, 0, 6);
                }
                $monthToAmount = $this->generateMonthToAmount($refundMonth, $model->old_course_amount);
                $sharedTotal = 0;
                foreach ($sharedModels as $sharedModel) {
                    if (isset($monthToAmount[$sharedModel->month])) {
                        // 已导入
                        if ($sharedModel->status == 1) {
                            $sharedTotal += $sharedModel->amount;
                        } else {
                            $refundShare = new AsaUfidaShare();
                            $refundShare->setAttributes($sharedModel->getAttributes());
                            $refundShare->amount = (0 - $monthToAmount[$sharedModel->month]);
                            $refundShare->order_id = $model->link_id;
                            $refundShare->memo = "换课旧课程退费";
                            if (!$refundShare->save()) {
                                $error = current($refundShare->getErrors());
                                throw new Exception($refundShare->id . " 换课支付数据保存失败 {$error[0]} \r\n");
                            }
                        }
                    }
                    if ($sharedTotal > 0) {
                        $newShareModel = new AsaUfidaShare();
                        $newShareModel->setAttributes($sharedModels[0]->getAttributes());
                        $newShareModel->amount = (0 - $sharedTotal);
                        $newShareModel->month = date("Ym", $this->timeStart);
                        $newShareModel->month_stamp = strtotime($newShareModel->month . "01");
                        $newShareModel->borrow = AsaUfidaIncome::WECHAT_BORROW;
                        $newShareModel->borrow_accounts_code = AsaUfidaIncome::WECHAT_BORROW_CODE;
                        $newShareModel->loan = AsaUfidaIncome::SHARE;
                        $newShareModel->loan_accounts_code = AsaUfidaIncome::SHARE_CODE;
                        $newShareModel->invoice_id = $model->id;
                        $newShareModel->order_id = $model->link_id;
                        $newShareModel->memo = "换课旧课程费用冲已结转收入";
                        $newShareModel->status = 0;
                        $newShareModel->timestmp = time();
                        if (!$newShareModel->save()) {
                            $error = current($newShareModel->getErrors());
                            throw new Exception($newShareModel->id . " 换课支付数据保存失败 {$error[0]} \r\n");
                        }
                    }
                }
                // 新课分摊
                // 查找课程持续的时间
                $scheduleCrit = new CDbCriteria();
                $scheduleCrit->compare("schedule_id", $newCourse->schedule_id);
                $scheduleCrit->order = "schedule_date ASC";
                $scheduleItems = AsaScheduleItem::model()->findAll($scheduleCrit);
                $monthItem = array();
                foreach ($scheduleItems as $k => $scheduleItem) {
                    // 付款节数大于课程实际节数取后面的课程时间
                    if (count($scheduleItems) > ($k + $model->new_course_num)) {
                        continue;
                    }
                    $monthItem[] = substr($scheduleItem->schedule_date, 0, 6);
                }
                $monthToAmount = $this->generateMonthToAmount($monthItem, $model->new_course_amount);
                if (count($monthToAmount) > 0) {
                    // 按照月份分摊
                    foreach ($monthToAmount as $month => $amount) {
                        $shareModel = new AsaUfidaShare();
                        $shareModel->month = $month;
                        $shareModel->month_stamp = strtotime($month . "01");
                        $shareModel->invoice_id = $model->id;
                        $shareModel->child_id = $model->childid;
                        $shareModel->order_id = $model->link_id;
                        $shareModel->borrow = AsaUfidaIncome::LOAN;
                        $shareModel->loan = AsaUfidaIncome::SHARE;
                        $shareModel->borrow_accounts_code = AsaUfidaIncome::LOAN_CODE;
                        $shareModel->loan_accounts_code = AsaUfidaIncome::SHARE_CODE;
                        $shareModel->amount = $amount;
                        $shareModel->fee_type = $newCourse->coursesgroup->season;
                        $shareModel->school_id = $newCourse->schoolid;
                        $shareModel->group_id = $newCourse->gid;
                        $shareModel->group_name = $newCourse->coursesgroup->title_cn;
                        $shareModel->startyear = $newCourse->coursesgroup->startyear;
                        $shareModel->course_id = $newCourse->id;
                        $shareModel->course_name = $newCourse->title_cn;
                        $shareModel->memo = "换课新课程结转收入";
                        $shareModel->status = 0;
                        $shareModel->timestmp = time();
                        if ($shareModel->save()) {
                            echo $model->id . " 新课分摊处理成功\r\n";
                        } else {
                            $error = current($shareModel->getErrors());
                            throw new Exception($model->id . " 新课数据保存失败 {$error[0]} \r\n");
                        }
                    }
                }
                $model->ufida_status = 1;
                if (!$model->save()) {
                    $error = current($model->getErrors());
                    throw new Exception($model->id . " 换课支付数据保存失败 {$error[0]} \r\n");
                }
                $transaction->commit();
            } catch (Exception $e) {
                $transaction->rollBack();
                echo $e->getMessage();
                Yii::app()->end();
            }
        }
    }


    // ******************************************* 公共方法 ********************************************

    /**
     * 生成月份对应金额的数据
     *
     * @param array $monthArray 需要处理的月份数组 array("202103", "202104")
     * @param double $totalAmount 总金额
     * @return array
     */
    public function generateMonthToAmount($monthArray, $totalAmount)
    {
        $monthToAmount = array();
        $monthCount = count($monthArray);
        if ($totalAmount == 0 || $monthCount == 0) {
            return $monthToAmount;
        }
        $averageAmount = round($totalAmount / $monthCount, 2);

        // 根据月份权重处理每个月分摊的金额
        foreach ($monthArray as $k => $v) {
            if (!isset($monthToAmount[$v])) {
                $monthToAmount[$v] = 0;
            }
            $monthToAmount[$v] += $averageAmount;
        }
        ksort($monthToAmount);
        if (abs(array_sum($monthToAmount) - $totalAmount) >= 0.01) {
            $i = 1;
            $grand = 0;
            foreach ($monthToAmount as $k => $v) {
                if ($i == count($monthToAmount)) {
                    $monthToAmount[$k] = $totalAmount - $grand;
                }
                $grand += $v;
                $i++;
            }
        }
        return $monthToAmount;
    }
}
