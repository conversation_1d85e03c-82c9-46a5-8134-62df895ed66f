<?php

/**
 * This is the model class for table "ivy_child_transfer_school_history".
 *
 * The followings are the available columns in table 'ivy_child_transfer_school_history':
 * @property integer $id
 * @property integer $childid
 * @property string $from_schoolid
 * @property string $to_schoolid
 * @property double $balance
 * @property integer $transfer_time
 * @property integer $classid
 * @property integer $yid
 * @property integer $rank
 * @property integer $status
 * @property string $remark
 */
class TransferSchoolHistory extends CActiveRecord
{
    const TRANSFER_STATUS_ONE = 0; //转入方申请
    const TRANSFER_STATUS_TWO = 1; //转出方同意
    const TRANSFER_STATUS_THREE = 2; //输入方同意
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_child_transfer_school_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('from_schoolid, to_schoolid, childid', 'required','on'=>'save'),
			array('childid, yid, classid, from_schoolid, to_schoolid, timestamp', 'required', 'on'=>'app'),
			array('childid, transfer_time, classid, yid, rank, status, timestamp', 'numerical', 'integerOnly'=>true),
			array('balance', 'numerical'),
			array('from_schoolid, to_schoolid', 'length', 'max'=>255),
			array('remark', 'length', 'max'=>500),
            array('timestamp','safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, childid, from_schoolid, to_schoolid, balance, transfer_time, classid, yid, rank, status, remark, timestamp', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'child'=>array(self::BELONGS_TO,'ChildProfileBasic','childid'),
            'class'=>array(self::BELONGS_TO,'IvyClass','classid'),
            'calendar'=>array(self::BELONGS_TO,'Calendar','yid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'childid' => Yii::t('campus', 'Student name'),
			'from_schoolid' => Yii::t('campus', 'Original campus'),
			'to_schoolid' => Yii::t('campus', '转入学校'),
			'balance' => Yii::t('campus', 'Personal account'),
			'transfer_time' => Yii::t('campus', 'Student start date'),
			'classid' => Yii::t('campus', 'Assigned class'),
			'yid' => Yii::t('campus', 'Student start date'),
			'rank' => Yii::t('campus', '级别'),
			'status' => Yii::t('campus', 'Status'),
			'remark' => Yii::t('campus', 'Comments'),
			'timestamp' => Yii::t('campus', 'Transfer initiated'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('t.id',$this->id);
		$criteria->compare('t.childid',$this->childid);
		$criteria->compare('t.from_schoolid',$this->from_schoolid,true);
		$criteria->compare('t.to_schoolid',$this->to_schoolid,true);
		$criteria->compare('t.balance',$this->balance);
		$criteria->compare('t.transfer_time',$this->transfer_time);
		$criteria->compare('t.classid',$this->classid);
		$criteria->compare('t.yid',$this->yid);
		$criteria->compare('t.rank',$this->rank);
		$criteria->compare('t.status',$this->status);
		$criteria->compare('t.remark',$this->remark,true);
        $criteria->with = array('child','class');
		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
            'sort'=>array(
                'defaultOrder'=>'t.status asc,t.transfer_time Desc'
            ),
            'pagination'=>array(
                'pageSize'=>15,
            ),
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return TransferSchoolHistory the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
    
    public static function renderStatus($data = null,$format = 0){
        $ret = array(
            self::TRANSFER_STATUS_ONE=> Yii::t('campus', 'Pending confirmation'),
            self::TRANSFER_STATUS_TWO=> Yii::t('campus', 'Confirmed'),
            self::TRANSFER_STATUS_THREE=> Yii::t('campus', 'Accepted')
        );
        if ($data ===null){
            $formatData = $ret;
        }else{
            $formatData = ($format && !$data) ? "<span style='color:red;'>".$ret[$data]."</span>" : $ret[$data];
        }
        
        return $formatData;
    }
    
    /*
     * 发送邮件（转校申请、转校同意、转校同意[命令行]）
     * @param   $transferModel   转校Model
     */
    public static function sendEmail($transferModel,$command=false,$app=false){
        Yii::import('common.models.calendar.Calendar');
        $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
        $flag = 'transfer';
        $startYear = $transferModel->calendar->startyear.'-'.($transferModel->calendar->startyear+1).'学年';
        $url = 'http://apps.ivyonline.cn';
        if ($command === false){
            $isProduct = OA::isProduction();
        }else{
            $isProduct = Yii::app()->params['isProduction'];
        }
        $teststr = ($isProduct===false) ? '【测试】' : '';
        if ($app === true){
            $branchData = Branch::model()->getTitleEmail();
            if ($transferModel->status == self::TRANSFER_STATUS_ONE){
                $mailer->pushTo($flag,$transferModel->from_schoolid);
                $mailer->Subject = $teststr.sprintf('%s申请< %s >转入该校', $branchData[$transferModel->to_schoolid]['title'],$transferModel->child->getChildName());
                $body = sprintf('%s 申请< %s >%s转入该校，请及时登陆IVYONLINE系统“转校升学管理” - <a href="%s" target="_blank">“转出学生列表”</a>中确认操作。', $branchData[$transferModel->to_schoolid]['title'],$transferModel->child->getChildName(),$startYear,$url."/mcampus/transfer/index?type=out&branchId={$transferModel->from_schoolid}");
            }elseif($transferModel->status == self::TRANSFER_STATUS_THREE){
                $mailer->pushTo($flag,$transferModel->to_schoolid);
                $mailer->Subject = $teststr.sprintf('%s同意< %s >转出该校', $branchData[$transferModel->from_schoolid]['title'],$transferModel->child->getChildName());
                $body =  sprintf('%s同意< %s >转出该校，您可以到“转校升学管理” - <a href="%s" target="_blank">“转入学生列表”</a>下找到孩子后，点击孩子名字，开据账单相关操作。', $branchData[$transferModel->from_schoolid]['title'],$transferModel->child->getChildName(),$url."/mcampus/transfer/index?type=in&branchId={$transferModel->to_schoolid}");
            }else{
                $mailer->pushTo($flag,$transferModel->to_schoolid);
                $mailer->Subject = $teststr.sprintf('%s同意< %s >转出该校', $branchData[$transferModel->from_schoolid]['title'],$transferModel->child->getChildName());
                $body =  sprintf('%s同意< %s >转出该校，等到转校日期< %s >时，系统会自动转孩子相关信息到该校，您可以到“转校升学管理” - <a href="%s" target="_blank">“转入学生列表”</a>下找到孩子后，点击孩子名字，开据账单相关操作。', $branchData[$transferModel->from_schoolid]['title'],$transferModel->child->getChildName(),date('Y-m-d',$transferModel->transfer_time),$url."/mcampus/transfer/index?type=in&branchId={$transferModel->to_schoolid}");
            }
        }else{
            $branchData = Branch::model()->getTitleEmail();
            $mailer->pushTo($flag,$transferModel->from_schoolid);
            $mailer->Subject = $teststr.sprintf('%s撤销< %s >转入该校操作', $branchData[$transferModel->to_schoolid]['title'],$transferModel->child->getChildName());
            $body =  sprintf('%s撤销< %s >转入本校操作，请忽略校园申请。', $branchData[$transferModel->to_schoolid]['title'],$transferModel->child->getChildName());
        }
        if ($command === true){
            $mailer->iniMail($isProduct);
            $mailer->getCommandView('transfer',array('body'=>$body),'main');
        }else{
            $mailer->iniMail($isProduct);
            $mailer->getView('transfer',array('body'=>$body),'main');
        }
        if ($mailer->Send()){
            return true;
        }else{
            return $mailer->ErrorInfo();
        }
    }
    
    /*
     * 判断学生转校是否在有效期内
     */
    public static function getTransferPeriod($schoolId=null, $childId=0){
        if ($schoolId === null || !$childId){
            return null;
        }
        $model = self::model()->find('to_schoolid=:schoolid and childid=:childid and status>:status and transfer_time>=:transfer_time',array(
            'schoolid'=>$schoolId,
            'childid'=>$childId,
            'status'=>  self::TRANSFER_STATUS_ONE,
            'transfer_time'=> strtotime('today'),
        ));
        return (empty($model)) ? null : $model;
    }
}
