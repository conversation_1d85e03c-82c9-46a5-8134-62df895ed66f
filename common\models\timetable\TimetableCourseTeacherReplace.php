<?php

/**
 * This is the model class for table "ivy_timetable_course_teacher_replace".
 *
 * The followings are the available columns in table 'ivy_timetable_course_teacher_replace':
 * @property integer $id
 * @property string $schoolid
 * @property integer $tid
 * @property integer $course_id
 * @property string $course_code
 * @property integer $weeknum
 * @property integer $weekday
 * @property integer $period
 * @property string $category
 * @property string $source_day
 * @property integer $old_teacher
 * @property integer $new_teacher
 * @property integer $created_at
 * @property integer $created_by
 * @property integer $updated_at
 * @property integer $updated_by
 * @property integer $deleted_at
 */
class TimetableCourseTeacherReplace extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_timetable_course_teacher_replace';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('schoolid, tid, course_id, course_code, weeknum, weekday, period, category, old_teacher, new_teacher, created_at, created_by, updated_at, updated_by', 'required'),
			array('tid, course_id, weeknum, weekday, period, old_teacher, new_teacher, created_at, created_by, updated_at, updated_by, deleted_at', 'numerical', 'integerOnly'=>true),
			array('schoolid, course_code', 'length', 'max'=>255),
			array('category', 'length', 'max'=>16),
			array('source_day', 'length', 'max'=>15),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, schoolid, tid, course_id, course_code, weeknum, weekday, period, category, source_day, old_teacher, new_teacher, created_at, created_by, updated_at, updated_by, deleted_at', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'schoolid' => 'Schoolid',
			'tid' => 'Tid',
			'course_id' => 'Course',
			'course_code' => 'Course Code',
			'weeknum' => 'Weeknum',
			'weekday' => 'Weekday',
			'period' => 'Period',
			'category' => 'Category',
			'source_day' => 'Source Day',
			'old_teacher' => 'Old Teacher',
			'new_teacher' => 'New Teacher',
			'created_at' => 'Created At',
			'created_by' => 'Created By',
			'updated_at' => 'Updated At',
			'updated_by' => 'Updated By',
			'deleted_at' => 'Deleted At',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('tid',$this->tid);
		$criteria->compare('course_id',$this->course_id);
		$criteria->compare('course_code',$this->course_code,true);
		$criteria->compare('weeknum',$this->weeknum);
		$criteria->compare('weekday',$this->weekday);
		$criteria->compare('period',$this->period);
		$criteria->compare('category',$this->category,true);
		$criteria->compare('source_day',$this->source_day,true);
		$criteria->compare('old_teacher',$this->old_teacher);
		$criteria->compare('new_teacher',$this->new_teacher);
		$criteria->compare('created_at',$this->created_at);
		$criteria->compare('created_by',$this->created_by);
		$criteria->compare('updated_at',$this->updated_at);
		$criteria->compare('updated_by',$this->updated_by);
		$criteria->compare('deleted_at',$this->deleted_at);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return TimetableCourseTeacherReplace the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
