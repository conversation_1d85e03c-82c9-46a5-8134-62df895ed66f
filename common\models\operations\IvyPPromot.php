<?php

/**
 * This is the model class for table "ivy_p_promot".
 *
 * The followings are the available columns in table 'ivy_p_promot':
 * @property integer $id
 * @property integer $pid
 * @property integer $main_pid
 * @property integer $min_amount
 * @property integer $expire_timestamp
 */
class IvyPPromot extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return IvyPPromot the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_p_promot';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('pid, main_pid, min_amount, expire_timestamp', 'numerical', 'integerOnly'=>true),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, pid, main_pid, min_amount, expire_timestamp', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'Product' => array(self::BELONGS_TO,'IvyPProduct','pid','select'=>'title'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'pid' => 'Pid',
			'main_pid' => 'Main Pid',
			'min_amount' => 'Min Amount',
			'expire_timestamp' => 'Expire Timestamp',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('pid',$this->pid);
		$criteria->compare('main_pid',$this->main_pid);
		$criteria->compare('min_amount',$this->min_amount);
		$criteria->compare('expire_timestamp',$this->expire_timestamp);

		return new CActiveDataProvider(get_class($this), array(
			'criteria'=>$criteria,
		));
	}
	
	/**
	 * 根据产品的标识查询赠品信息
	 * @param string $mainPid
	 * @return ArrayObject
	 * <AUTHOR>
	 * @copyright IvyGroup
	 */
	public function getProduct($mainPid)
	{
		return $this->model()->with('Product')->findAll('main_pid=:main_pid',array(':main_pid'=>$mainPid));
	}
}