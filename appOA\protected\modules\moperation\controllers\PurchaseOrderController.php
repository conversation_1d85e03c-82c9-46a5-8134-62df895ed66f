<?php

class PurchaseOrderController extends ProtectedController
{
	public $actionAccessAuths = array(
		'Index'=>'oOperationsView',
		'Orders'=>'oOperationsView',
		'GetOrder'=>'oOperationsView',
		'edit'=>'oOperationsAdmin',
		'Delete'=>'oOperationsAdmin',
		'DeleteItem'=>'oOperationsAdmin',
	);

	public $dialogWidth = 600;
	public $branchList = array();

	public function init() 
	{
	    parent::init();
	    Yii::import('common.models.purchase.*');
	    Yii::import('common.models.vendor.*');
	    Yii::import('common.models.User');
	    Yii::import('common.models.Branch');
	    Yii::import('common.models.BranchInfo');

	    Yii::app()->theme = 'blue';
	    $this->layout = "//layouts/column1";

	    $this->modernMenuFlag = 'hqOp';
	    $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
	    $this->modernMenuCategoryTitle = Yii::t('site','HQ Operations');

	    $cs = Yii::app()->clientScript;
		$cs->registerCoreScript('jquery.ui');
		$cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/jquery.printThis.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue2.js');
	}

	public function actionIndex()
	{
		$criteria = new CDbCriteria();
		$criteria->compare('status',PurchaseApplication::AGREE);
		$application = new CActiveDataProvider('PurchaseApplication',array(
		    'criteria'=>$criteria,
		    'sort'=>array('defaultOrder'=>'t.update_timestamp DESC'),
		    )
		);

		$branchModel = Branch::model()->findAll();

		foreach ($branchModel as $val)
        {
            $this->branchList[$val->branchid] = $val->title;
        }

		$this->render('index',array('application'=>$application));
	}

	public function actionGetProducts()
	{
		$orderModel = new PurchaseOrder();
		if (isset($_POST['PurchaseOrder'])) {
		    if($_POST['products']){
                $status = 0;
                $purchaseApplicationItemModel = PurchaseApplicationItem::model()->findAllByPk($_POST['products']);
                if($purchaseApplicationItemModel){
                    foreach ($purchaseApplicationItemModel as $val){
                        $num = 0;
                        $criteria = new CDbCriteria();
                        $criteria->compare('t.aid', $val->aid);
                        $criteria->compare('item.pid', $val->id);
                        $criteria->with = 'item';
                        $purchaseModel = PurchaseOrder::model()->findAll($criteria);
                        if($purchaseModel){
                            foreach ($purchaseModel as $items){
                                foreach ($items->item as $value){
                                    $num += $value->num;
                                }
                            }
                        }

                        if($val->num - $num < $_POST[$val->id.'_num']){
                            $status = 1;
                        }
                    }
                }

                if($status){
                    $this->addMessage('state','fail');
                    $this->addMessage('message', Yii::t('message','选择的商品总数大于以生成订单的总数!'));
                    $this->showMessage();
                }
            }else{
                $this->addMessage('state','fail');
                $this->addMessage('message', Yii::t('message','请选择商品!'));
                $this->showMessage();
            }
			$orderModel->attributes = Yii::app()->request->getParam('PurchaseOrder');
			$orderModel->delivery_timestamp = strtotime($orderModel->delivery_timestamp);
			$orderModel->add_user = $this->staff->uid;
			$orderModel->add_timestamp = time();
			$orderModel->status = 0;

			if ($orderModel->save()) {
				foreach ($_POST['products'] as $pid) {
					$orderItemModel = new PurchaseOrderItem();
					$orderItemModel->oid = $orderModel->id;
					$orderItemModel->pid = $pid;
					$orderItemModel->num = $_POST[$pid.'_num'];
					$orderItemModel->save();
				}
				// 发送订单信息给行政
				$oaStaff = User::model()->findByPk($orderModel->application->add_user);
				Yii::import('application.components.OA');
				$mailer = Yii::createComponent('common.extensions.mailer.EMailer');
				$subject = '采购订单信息';
				$mailer->Subject = $subject;
				$mailer->AddAddress($oaStaff->email);
				$mailer->iniMail( OA::isProduction() ); // 此行代码要放到AddAddress, AddCC方法下面
				$mailer->getView('purchaseOrder', array('orderModel' => $orderModel), 'main');
				$mailer->Send();

				$this->addMessage('state', 'success');
				$this->addMessage('callback', 'cbOrder');
				$this->addMessage('message', Yii::t('message','Data saved!'));
			} else{
				$this->addMessage('state','fail');
				$errs = current($orderModel->getErrors());
				$this->addMessage('message', $errs?$errs[0]:Yii::t('message','Saving Failed!'));
			}
			$this->showMessage();
		}
		$cateArr = $this->getCate();
		$data = array();
		$aid = Yii::app()->request->getParam('aid', '');
		$criteria = new CDbCriteria();
		$criteria->compare('t.aid', $aid);
		$criteria->compare('t.type', array(PurchaseApplicationItem::PURCHASE_PRODUCT));
		$criteria->with = 'products';
		$criteria->order = 'commodity_code DESC';
		$products = PurchaseApplicationItem::model()->findAll($criteria);

		$productsData = array();
		// 获取自定义商品
		$criteria = new CDbCriteria();
		$criteria->compare('aid', $aid);
		$criteria->compare('type', array(PurchaseApplicationItem::PURCHASE_CUSTOM));
		$customProducts = PurchaseApplicationItem::model()->findAll($criteria);
		foreach ($customProducts as $val) {
			$criteria = new CDbCriteria();
			$criteria->compare('t.aid', $aid);
			$criteria->compare('item.pid', $val->id);
			$criteria->with = 'item';
			$purchaseModel = PurchaseOrder::model()->findAll($criteria);
			$order = array();
			$orderNum = 0;
			if($purchaseModel){
			    foreach ($purchaseModel as $items){
			        foreach ($items->item as $value){
			            $order[] = array(
			                'numbering' => $items->pid,
			                'num' => $value->num,
			                'url' => $this->createUrl('orders', array('showId' => $items->id)),
			            );
			            $orderNum += $value->num;
			        }
			    }
			}

			$productsData[$val->cid][] = array(
			    'id' => $val->id,
			    'aid' => $val->aid,
			    'name' => $val->title,
			    'supplier' => array(),
			    'price' => $val->price,
			    'num' => $val->num,
			    'orderNum' => $val->num - $orderNum,
			    'numData' => $val->num,
			    'order' => $order,
			);
		}

		$application = PurchaseApplication::model()->findByPk($aid);
		$schoolId = User::model()->findByPk($application->add_user)->profile->branch;
		$schoolInfo = Branch::model()->findByPk($schoolId)->info;

		$vendorArr = array();
		$vendorTypeArr = array('请选择');
		foreach (VendorType::model()->findAll() as $vendorType) {
			foreach (Vendor::model()->findAll('vendor_type_id=:vendor_type_id',array(':vendor_type_id'=>$vendorType->vendor_type_id)) as $vendor) {
				$vendorArr[$vendorType->vendor_type_id][$vendor->vendor_id] = $vendor->cn_title;
			}
			$vendorTypeArr[$vendorType->vendor_type_id]=$vendorType->cn_title;
		}

        $company = array();
		if($products){
		    foreach ($products as $val){
		        $supplier = array();
		        if($val->company){
                    foreach ($val->company as $item){
                        $supplier[] = $item->company_id;
                        $company[$item->company_id] = $item->company_id;
                    }
                }

                $criteria = new CDbCriteria();
                $criteria->compare('t.aid', $aid);
                $criteria->compare('item.pid', $val->id);
                $criteria->with = 'item';
                $purchaseModel = PurchaseOrder::model()->findAll($criteria);
                $order = array();
                $orderNum = 0;
                if($purchaseModel){
                    foreach ($purchaseModel as $items){
                        foreach ($items->item as $value){
                            $order[] = array(
                                'numbering' => $items->pid,
                                'num' => $value->num,
                                'url' => $this->createUrl('orders', array('showId' => $items->id)),
                            );
                            $orderNum += $value->num;
                        }
                    }
                }

                $productsData[$val->cid][] = array(
                    'id' => $val->id,
                    'aid' => $val->aid,
                    'name' => $val->title,
                    'supplier' => $supplier,
                    'price' => $val->price,
                    'num' => $val->num,
                    'orderNum' => $val->num - $orderNum,
                    'numData' => $val->num,
                    'order' => $order,
                );
            }
        }
		$companyData = array();
		if($company){
            $vendorMode = Vendor::model()->findAllByPk($company);
            if($vendorMode) {
                foreach ($vendorMode as $val) {
                    $companyData[] = array(
                        'id' => $val->vendor_id,
                        'title' => Yii::app()->language == 'zh_cn' ? $val->cn_title : $val->en_title,
                    );
                }
            }
        }

		$this->renderPartial('products',array(
			'application'=>$application, 
			'products'=>$products, 
			'cateArr'=>$cateArr, 
			'orderModel'=>$orderModel,
			'schoolInfo'=>$schoolInfo,
			'vendorArr'=>$vendorArr,
			'vendorTypeArr'=>$vendorTypeArr,
			'productsData'=>$productsData,
			'companyData'=>$companyData,
			));
	}

	//所有订单
	public function actionOrders()
	{
		Yii::import('common.model.User');
		$criteria = new CDbCriteria();
		$orders = new CActiveDataProvider('PurchaseOrder',array(
		    'criteria'=>$criteria,
		    'sort'=>array('defaultOrder'=>'t.add_timestamp DESC'),
		    )
		);
		$this->render('orders',array('orders'=>$orders));
	}

	// 单个订单
	public function actionGetOrder()
	{
		$id = Yii::app()->request->getParam('id', '');
		$orderModel = PurchaseOrder::model()->findByPk($id);
		if ($orderModel) {
			$this->renderPartial('order', array('orderModel'=>$orderModel));
		}
	}

	// 编辑订单
	public function actionEdit($id)
	{
		$id = Yii::app()->request->getParam('id', '');
		$orderModel = PurchaseOrder::model()->findByPk($id);
		if (!$orderModel) {
			Yii::app()->end();
		}
		if (isset($_POST['PurchaseOrder'])) {
			$products = Yii::app()->request->getParam('products',array());
            if($products){
                $status = 0;
                $purchaseApplicationItemModel = PurchaseApplicationItem::model()->findAllByPk($products);
                if($purchaseApplicationItemModel){
                    foreach ($purchaseApplicationItemModel as $val){
                        $num = 0;
                        $criteria = new CDbCriteria();
                        $criteria->compare('t.aid', $val->aid);
                        $criteria->compare('item.pid', $val->id);
                        $criteria->with = 'item';
                        $purchaseModel = PurchaseOrder::model()->findAll($criteria);
                        if($purchaseModel){
                            foreach ($purchaseModel as $items){
                                foreach ($items->item as $value){
                                    if($value->oid != $orderModel->id) {
                                        $num += $value->num;
                                    }
                                }
                            }
                        }

                        if($val->num - $num < $_POST[$val->id.'_num'] || $val->num - $_POST[$val->id.'_num'] < 0){
                            $status = 1;
                        }
                    }
                }

                if($status){
                    $this->addMessage('state','fail');
                    $this->addMessage('message', Yii::t('message','选择的商品总数大于以生成订单的总数!'));
                    $this->showMessage();
                }
            }

			$orderModel->attributes = Yii::app()->request->getParam('PurchaseOrder');
			$orderModel->delivery_timestamp = strtotime($orderModel->delivery_timestamp);
			$orderModel->add_user = $this->staff->uid;
			$orderModel->add_timestamp = time();
			$orderModel->status = 0;
			if ($orderModel->save()) {
				foreach ($orderModel->item as $item) {
					if (in_array($item->pid, $products)) {
						$item->num = Yii::app()->request->getParam($item->pid.'_num');
						$item->save();
						continue;
					}
					$item->delete();
				}
				// 发送订单信息给行政
				// $oaStaff = User::model()->findByPk($orderModel->application->add_user);
				// Yii::import('application.components.OA');
				// $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
				// $subject = '采购订单信息';
				// $mailer->Subject = $subject;
				// $mailer->AddAddress($oaStaff->email);
				// $mailer->getView('purchaseOrder', array('orderModel' => $orderModel), 'main');
				// $mailer->iniMail( OA::isProduction() ); // 此行代码要放到AddAddress, AddCC方法下面
				// $mailer->Send();

				$this->addMessage('state', 'success');
				$this->addMessage('callback', 'cbOrder');
				$this->addMessage('message', Yii::t('message','Data saved!'));
			} else{
				$this->addMessage('state','fail');
				$errs = current($orderModel->getErrors());
				$this->addMessage('message', $errs?$errs[0]:Yii::t('message','Saving Failed!'));
			}
			$this->showMessage();
		}
		$data = array();
		$vendorArr = array();
		$vendorTypeArr = array('请选择');
		foreach (VendorType::model()->findAll() as $vendorType) {
			foreach (Vendor::model()->findAll('vendor_type_id=:vendor_type_id',array(':vendor_type_id'=>$vendorType->vendor_type_id)) as $vendor) {
				$vendorArr[$vendorType->vendor_type_id][$vendor->vendor_id] = $vendor->cn_title;
			}
			$vendorTypeArr[$vendorType->vendor_type_id]=$vendorType->cn_title;
		}
        $nums = array();
		if($orderModel && $orderModel->item){
            $productsArr  = array();
            foreach ($orderModel->item as $val) {
                $productsArr[$val->pid] = $val->pid;
            }
            $purchaseemModel = PurchaseApplicationItem::model()->findAllByPk($productsArr);
            if($purchaseemModel){
                foreach ($purchaseemModel as $val){
                    $nums[$val->id] = $val->num;
                    $criteria = new CDbCriteria();
                    $criteria->compare('t.aid', $val->aid);
                    $criteria->compare('item.pid', $val->id);
                    $criteria->with = 'item';
                    $purchaseModel = PurchaseOrder::model()->findAll($criteria);
                    if($purchaseModel){
                        foreach ($purchaseModel as $items){
                            foreach ($items->item as $value){
                                if($value->oid != $orderModel->id) {
                                    $nums[$item->pid] -= $item->num;
                                }
                            }
                        }
                    }
                }
            }
        }
        
		$this->renderPartial('edit',array(
			'orderModel'=>$orderModel,
			'vendorArr'=>$vendorArr,
			'vendorTypeArr'=>$vendorTypeArr,
			'nums'=>$nums,
			));
	}

	// 查看订单
	public function actionView()
	{
		$id = Yii::app()->request->getParam('id', '');
		$orderModel = PurchaseOrder::model()->findByPk($id);
		if ($orderModel) {
			$this->render('order', array('orderModel'=>$orderModel));
		}
	}

	public function getSchool($data)
    {
		Yii::import('common.models.workflow.*');
        
		$models = WorkflowDefination::model()->findAllByAttributes(array('defination_handle' => 'Purchase'));
		$definationIds = array();
		foreach ($models as $model) {
			$definationIds[] = $model->defination_id;
		}
		$school = '';
		if ($definationIds) {
			// 查找对应的工作流
			$crit = new CDbCriteria();
			$crit->compare('operation_object_id', $data->id);
			$crit->compare('defination_id', $definationIds);
			$workflowModel = WorkflowOperation::model()->find($crit);
			if ($workflowModel) {
				$school = $workflowModel->branchid;
			}
		}
		if (!$school) {
			$school = User::model()->findByPk($data->add_user)->profile->branch;
		}

        return $this->branchList[$school];
    }

	/**
	 * 删除订单
	 * @param  string $value [description]
	 * @return [type]        [description]
	 */
	public function actionDelete()
	{
		if (Yii::app()->request->isAjaxRequest) {
			$id = Yii::app()->request->getParam('id','');
			if ($id) {
				$model = PurchaseOrder::model()->findByPk($id);

				if ($model->delete()) {
                    $criteria = new CDbCriteria();
                    $criteria->compare('oid', $model->id);
                    $itemModel = PurchaseOrderItem::model()->find($criteria);
                    if($itemModel){
                        $itemModel->delete();
                    }
					$this->addMessage('state', 'success');
					$this->addMessage('callback', 'cbOrder');
					$this->addMessage('message', '删除成功');
				} else{
					$this->addMessage('state', 'fail');
					$this->addMessage('message', '删除失败');
				}
				$this->showMessage();
			}
		}
	}

	/**
	 * 获取商品的所有分类
	 * @return [array] [description]
	 */
	public function getCate()
	{
		Yii::import('common.models.Diglossia');
		Yii::import('common.models.DiglossiaCategory');

		$cate = array();
		$cate[0] = '自定义商品';
		$criteria = new CDbCriteria;
		$criteria->compare('category_sign', 'productcate');
		$diglossiaCateModels = DiglossiaCategory::model()->findAll($criteria);
		foreach ($diglossiaCateModels as $diglossiaCateModel) {
			foreach ($diglossiaCateModel->diglossia as $diglossia) {
				$cate[$diglossia->diglossia_id] = ('zh_cn' == Yii::app()->language)?$diglossia->cntitle:$diglossia->entitle;
			}
		}
		return $cate;
	}
}
