<?php
$this->breadcrumbs=array(
	Yii::t("navigations", 'Support')=>array('/child/support'),
	Yii::t("navigations", 'Email Support'),
);?>


	<h1><label><?php echo Yii::t("navigations","Email Support") ?></label></h1>
	<p><?php echo Yii::t("support","You will receive the response within one working day.");?></p>

<?php
	foreach(Yii::app()->user->getFlashes() as $key => $message) {
        echo '<div class="flash-' . $key . '">' . $message . "</div>\n";
    }
?>

<div class="form">
<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'support-form',
)); ?>
	
	<?php echo CHtml::errorSummary($model); ?>
<div style="display:none;">	
	<dl class="row">
		<dt>
			<?php echo CHtml::activeLabelEx($model,'to') ?>
		</dt>
		<dd>
			<?php echo CHtml::encode($model->to);?>
		</dd>
	<dl>
	
	<dl class="row">
		<dt>
			<?php echo CHtml::activeLabelEx($model,'cc') ?>
		</dt>
		<dd>
			<?php echo CHtml::encode($model->cc);?>
		</dd>
	<dl>

	<dl class="row">
		<dt>
			<?php echo CHtml::activeLabelEx($model,'subject') ?>
		</dt>
		<dd>
			<?php echo CHtml::encode($model->subject);?>
		</dd>
	<dl>
	
	<dl class="row">
		<dt>
			<?php echo CHtml::activeLabelEx($model,'content') ?>
		</dt>
		<dd>
			<?php echo $model->content;?>
		</dd>
	<dl>	
</div>
	<div class="row submit rememerMe">
		<?php echo CHtml::Link(Yii::t("global", "Back"), array("onclick"=>"window.history.back();")); ?>
	</div>
	
	
<?php $this->endWidget(); ?>
</div><!-- form -->