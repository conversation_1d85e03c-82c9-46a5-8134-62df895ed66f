<?php
 if ($this->branchObj->type == 50 || $this->branchId == 'BJ_QFF') {
 $borderNone='borderNone';
 $logoWdith='width:200px';
} else {
	$borderNone='ivy';
	$logoWdith='height:60px';

} 	
?>
<style>
	.border{
		border:1px solid #ccc;
		border-radius:5px
	}
	.pb5{
		padding-bottom: 15px
	}
	.ptintImg{
		max-height: 120px;
		max-width:120px
	}
	.borderNone{
		border:1px solid #ccc;
		background: url(http://m2.files.ivykids.cn/cloud01-file-8025768FtLJ88p3PQvUtBNeUSKGQPXQBynO.jpg) no-repeat;
		background-size:100% 100%;
		/* background:url('http://m2.files.ivykids.cn/cloud01-file-8025768FhElG0oo8VA01MzYMy9Wa9jXD4YB.jpg') no-repeat 100% 100% */
	}
	.ivy{
		border:1px solid #ccc;
		background: url(http://m2.files.ivykids.cn/cloud01-file-8025768FqoRUgyX5mkN_M9pWPUH9fEAKZvK.jpg) no-repeat;
		background-size:100% 100%;
	}
	.borderRight{
		border-right:1px solid #ccc;
	}
	.padd0{
		padding: 0 !important
	}
	[v-cloak] {
		display: none;
	}
	.h100{
		height: 100%
	}
	.chilidPto{
		max-height:150px
	}
	.odd{
		padding-top:10px;
		border-top: 1px solid #ddd;
	}
	.classWid{
		display: inline-block;
		width:20%;
		margin-left: 0!important;
		margin-bottom:10px
	}
	.pl0{
		padding-left: 0
	}
	.relative{
		position: relative;
	}
	.absolute{
		position: absolute;
    bottom: 15px;
    left: 15px;
    font-size: 16px;
    color: #fff;
	}
	.datalist{
		float: left;
		width: 50%;
		padding-top:10px;
		margin:0 auto
	}
	.datalist div{
		font-size:12px
	}
	.list{
		width:140px;
		margin:0 auto;
	}
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li class="active"><a href="<?php echo $this->createUrl('index', array('branchId' => $this->branchId)); ?>">资料收集</a></li>
        <li class="active"><?php echo Yii::t('site', '详情') ?></li>
    </ol>
	<div class="row">
        <div class="col-md-2 col-sm-2">
            <ul class="nav nav-pills nav-stacked text-left background-gray" id="pageCategory">
                <li class="" ><a href="<?php echo $this->createUrl('index', array('branchId' => $this->branchId)); ?>">资料收集</a></li>
                <li class="" ><a href="<?php echo $this->createUrl('details', array('branchId' => $this->branchId)); ?>">详情</a></li>
            </ul>
        </div>

		<div class="col-md-10 col-sm-10" id='print' v-cloak>
            <p class='font14'><a href="<?php echo $this->createUrl('index', array('branchId' => $this->branchId)); ?>"><span class='glyphicon glyphicon-chevron-left font12' ></span> 资料收集</a> | <a   :href ="'<?php echo $this->createUrl('details', array('branchId' => $this->branchId)); ?>&coll_id='+coll_id+''" >详情</a> | <span>打印接送卡</span> </p>
            <div style="color: #F0AD4E;margin: 10px auto 0 auto"><span class="glyphicon glyphicon-info-sign"></span> 只可以打印审核通过的接送卡</div>
            <div v-if='classData.length!=0 && showData'>
                <p class="mb15 mt20">
                    <template v-for='(classdata,index) in classData'>
                        <label class="checkbox-inline classWid">
                            <input type="checkbox" id="inlineCheckbox1" :value="classdata.classid" @click='pritData(classdata.classid,$event)'>{{classdata.title}}
                        </label>
                    </template>
                </p>
                <form action="<?php echo $this->createUrlReg('ivyPrintCard', array('branchId' => $this->branchId,'coll_id'=>Yii::app()->request->getParam('coll_id', ''))); ?>" method="post">
                    <p v-if='arrChild.length!=0'>
                        <button class="btn btn-primary">打印</button>
                        <label class="ml15"><input type="checkbox" v-model="selectAll"  @change="toggleSelectAll">全选</label>
                    </p>
                    <div class="col-md-12 padd0" >
                        <div class="col-md-6 pb5 odd" v-for='(list,index) in arrChild'>
                            <div class="">
                                <label class="padd0  col-md-12">
                                    <p><input type="checkbox" :value='list.child_id' name='childid[]' v-model="selectedItems"></p>
                                    <div class="<?php echo $borderNone;?> padd0" style="height:400px;">
                                        <div class="col-md-6 borderRight padd0 h100">
                                        <div class="">
                                            <div class="datalist" v-for='(parentlist,idx) in list.pickups' :class="(idx + 1) % 2==0?'pl0':''">
                                                <div class='list'>
                                                <img :src="parentlist.head_url" alt="" class="ptintImg">
                                                <div class="clearfix"></div>
                                                <div><?php echo Yii::t('global','Name') ?><?php echo Yii::t('global',': ') ?>{{parentlist.name}}</div>
                                                <div><?php echo Yii::t('asa','Phone') ?><?php echo Yii::t('global',': ') ?>{{Trim(parentlist.phone)}}</div>
                                                <div><?php echo Yii::t('site','Relationship') ?><?php echo Yii::t('global',': ') ?>{{parentlist.relation}}</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="clearfix"></div>
                                        </div>
                                        <div class="col-md-6 pt10 h100 text-center relative">
                                            <p class="mt15 pt10"><img :src="logo" alt="" style="<?php echo $logoWdith;?>;"></p>
                                            <p class="mt15"><img :src="list.child_avatar" class="center-block chilidPto " alt=""></p>
                                            <div class="text-center pt10">{{list.child_name_cn}}</div>
                                            <p class="text-center">{{list.child_name_en}}</p>
                                            <div class='text-left absolute'>
                                                    <div>{{ajaxData.school_year}}</div>
                                                    <div>{{ajaxData.school_title}}</div>
                                            </div>
                                        </div>
                                        <div class="clearfix"></div>
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div v-else class="alert alert-warning mt20" role="alert">暂无数据</div>
		</div>
	</div>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    var coll_id='<?php echo Yii::app()->request->getParam('coll_id', ''); ?>'
    var step_id='<?php echo Yii::app()->request->getParam('step_id', ''); ?>'
    var logo = '<?php echo $logo ?>';
    console.log(logo)
	var print = new Vue({
		el: "#print",
		data: {
            coll_id:coll_id,
            datas:{},
			startYearList:{},
			yid:'',
			YearList:'',
			classData:[],
			printchild:{},
			arrChild:[],
			logo:'',
			yearData:'',
			branchTitle:'',
            showData:false,
            ajaxData:{},
            selectedItems: []
		},
		created: function() {
            let that=this
            $.ajax({
                    url:'<?php echo $this->createUrlReg('ivyPickupCardList'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:{
                        coll_id:this.coll_id,
                        step_id:step_id,
                    },
                    success:function(data){
                        if(data.state=="success"){
                           that.ajaxData=data.data
                           that.classData=data.data.class_list
                           that.printchild=data.data.card_list
                           that.showData=true
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
		},
        computed: {
            selectAll:{
                get() {
                    // 如果所有项都已选中，则全选框为选中状态，否则为未选中状态
                    return this.arrChild.every(item => this.selectedItems.includes(item.childId));
                },
                set(value) {
                    // 如果全选框被选中，则选中所有项，否则取消选中所有项
                    this.selectedItems = value ? this.arrChild.map(item => item.childId) : [];
                }
            }
        },
		methods: {
            toggleSelectAll(event) {
                this.selectAll = event.target.checked;
            },
            Trim(str) { 
                if(str=='' || str==null){
                    return
                }
				var is_global='g'
				var result;
                
				result = str.replace(/(^\s+)|(\s+$)/g,"");
				if(is_global.toLowerCase()=="g")
				{
				result = result.replace(/\s/g,"");
				}
			  var tel=result.replace("+86",'')
				return tel;
		    },
			pritData(id,e){
				var checked=e.target.checked
				if(checked){
					if(this.printchild[id]){
						 for(key in this.printchild[id]){
			        		Vue.set(this.printchild[id][key], 'childId', key)
					        print.arrChild.push(this.printchild[id][key])
			        	}
					}
				}else{
			        for(key in this.printchild[id]){
		        		for(var i=0;i<print.arrChild.length;i++){
		        			if(key==print.arrChild[i].childId){
		        				print.arrChild.splice(i,1);
		        			}
		        		}
		        	}
				}
                this.selectedItems = this.arrChild.map(item => item.childId);//默认全选
			}
		},
	})  
</script>