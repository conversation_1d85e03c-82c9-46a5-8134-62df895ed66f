<?php $_currentChild = $this->myChildObjs[$this->currentCid];?>
<div id="child-selector">
	<div class="span-5">
	<h2 title="<?php echo $_currentChild->getChildName();?>">
	<?php
		echo $_currentChild->getChildName();
	?>
	</h2>
	<ul class="basic-info">
		<li><label><?php echo $_currentChild->getAttributeLabel('gender');?></label><span><?php echo $cfgs['gender'][$_currentChild->gender]; ?></span></li>
		<li><label><?php echo $_currentChild->getAttributeLabel('country');?></label><span><?php echo $country; ?></span></li>
		<li><label><?php echo $_currentChild->getAttributeLabel('birthday');?></label><span><?php echo Mims::formatDateTime($_currentChild->birthday, 'medium'); ?></span></li>
	</ul>
	</div>
	<div class="span-3 last">
	<?php
		echo CHtml::openTag("div",array("class"=>"photobox"));
			echo CHtml::openTag("a", array(
					"href"=>$this->getController()->createUrl("//child/profile/welcome", array("childid"=>$this->currentCid)),
					"class"=>"face"
					)
			);
			echo CHtml::image(CommonUtils::childPhotoUrl($_currentChild->photo), $_currentChild->getChildName() );
			echo CHtml::closeTag("a");
			echo CHtml::openTag("div",array("class"=>"clear"));
			echo CHtml::closeTag("div");
	
			if(Yii::app()->user->checkAccess('adminChild',array("childid"=>$this->currentCid))){
			echo CHtml::openTag("div", array("class"=>"cs-edit","id"=>"child-operations"));
				$hasMany = !empty($this->myChildObjs) && count($this->myChildObjs)-1;
				echo CHtml::link(Yii::t("userinfo","Edit"), $this->getController()->createUrl("//child/profile/profile"), array("class"=>$hasMany?"":"becenter"));
				if($hasMany){
					echo CHtml::link(Yii::t("userinfo","Switch"), Yii::app()->createUrl("//user/home"), array("class"=>"switch"));
				}			
			echo CHtml::closeTag("div");
			
			}
		
		echo CHtml::closeTag("div");
		
		?>
	</div>

	<ul>
		<li></li>
	</ul>
	<div class="clear"></div>
</div>