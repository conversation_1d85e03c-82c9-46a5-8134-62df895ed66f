<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','System Management'), array('/msettings'))?></li>
        <li class="active">系统权限</li>
    </ol>
    <div class="row panel-group">
        <div class="col-md-12">
            <?php
            $this->renderPartial('_menu');
            ?>
        </div>
        <?php if($this->module->getMessage() != ""){ ?>
            <div id="srbacError">
                <?php echo $this->module->getMessage();?>
            </div>
        <?php } ?>

    </div>

    <div class="row">
        <?php
        $criteria = new CDbCriteria();
        $criteria->condition = "type=2";
        $criteria->order = "name";
        $model = AuthItem::model();
        ?>
        <?php echo SHtml::beginForm(array('assign', 'revokeOpers'=>1), 'post', array('class'=>'J_ajaxForm')); ?>
        <div class="col-md-2">
            <?php
            $_roles = array();
            foreach(AuthItem::model()->findAll($criteria) as $role){
                $_roles[$role->name] = $role->name.' '.$role->description;
            }
            echo SHtml::activeDropDownList(
                AuthItem::model(),
                'name[0]',
                $_roles,
                array(
                    'size'=>$this->module->listBoxNumberOfLines,
                    'class'=>'form-control',
                    'onchange' =>'getUserDetail(this)',
                )
            ); ?>
        </div>
        <div class="col-md-10">
            <div id="tasks">

            </div>
        </div>
        <?php echo SHtml::endForm(); ?>
    </div>

</div>


<script>
    $(function() {
        $('#assign-dropdown').addClass('active');
    });
    function callback(data)
    {
        getUserDetail(document.getElementById('AuthItem_name_0'));
    }

    function getUserDetail(_this)
    {
        jQuery.ajax({'type':'POST','url':'/srbac/authitem/showTasks','beforeSend':function(){
            $("#loadMessRole").addClass("srbacLoading");
        },'complete':function(){
            $("#loadMessRole").removeClass("srbacLoading");
            head.Util.ajaxForm();
        },'cache':false,'data':jQuery(_this).parents("form").serialize(),'success':function(html){jQuery("#tasks").html(html)}});
    }
</script>