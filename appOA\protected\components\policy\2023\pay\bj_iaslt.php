<?php
if(!defined("IVY_POLICY"))
    throw new CException('DO NOT USE THIS FILE DIRECTLY');

return array( 'policy'=>array(

    //是否老生老办法
    ENABLE_CONSTANT_TUITION => true,
    FENTAN_FACTOR => DAILY,

    //是否开放课外活动账单
    ENABLE_AFTERSCHOOLS => true,

    //图书馆工本费
    FEETYPE_LIBCARD => 10,

    //学期四个时间点；取前后两端
    TIME_POINTS   => array(202308,202402,202402,202406),

    //年付开通哪些缴费类型
//   PAYGROUP_ANNUAL => array(
//       ITEMS => array(
//           FEETYPE_TUITION,
//           FEETYPE_LUNCH,
//           FEETYPE_SCHOOLBUS,
//       ),
//       TUITION_CALC_BASE => BY_ANNUAL,
//   ),
//
//   //学期付开通哪些缴费类型
  PAYGROUP_SEMESTER => array(
      ITEMS => array(
          FEETYPE_TUITION,
          FEETYPE_LUNCH,
          FEETYPE_SCHOOLBUS,
      ),
      TUITION_CALC_BASE => BY_SEMESTER,
  ),

   //月付开通哪些缴费类型
   // PAYGROUP_MONTH => array(
   //     ITEMS => array(
   //         // FEETYPE_TUITION,
   //         // FEETYPE_LUNCH,
   //         FEETYPE_SCHOOLBUS,
   //     ),
   //     TUITION_CALC_BASE => BY_MONTH,
   // ),

    //计算基准：BY_MONTH, 或者 BY_SETTING, 前者表示所有的学费都基于月费用计算，后者表示所有的学费都基于学期或学年
    TUITION_CALCULATION_BASE => array(

        BY_MONTH => array(

            //月收费金额
            /*
            AMOUNT => array(
                FULL5D => 15055,
                HALF5D => 10840,
            ),
             *
             */

            //收费月份，及其权重
            MONTH_FACTOR => array(
                202309=>1,
                202310=>1,
                202311=>1,
                202312=>1,
                202401=>1,
                202402=>1,
                202403=>1,
                202404=>1,
                202405=>1,
                202406=>1,
                202407=>0,
            ),

            //折扣

            GLOBAL_DISCOUNT => array(
                DISCOUNT_ANNUAL => '1:1:2023-12-01'
            )


        ),

        //东湖配置实例

        BY_ANNUAL => array(
            AMOUNT => array(
                FULL5D => 225000,
                // HALF2D => 63286,
                // HALF3D => 91000,
                HALF5D => 162000,
            ),
        ),

        BY_SEMESTER1 => array(
            AMOUNT => array(
                FULL5D => 111264,
                // HALF2D => 32800,
                // HALF3D => 47163,
                // HALF5D => 84560,
                //HALF4D => 41731,
            ),
            // 自定义结束日期时，需要把所有收费类型配置上
            ENDDATE => array(
                // 学费
                // FULL5D => 1706803200,
                // HALF5D => 1706803200,
                // // 校车费
                // 41772 => 1706630400,
                // 41773 => 1706630400,
                // 41774 => 1706630400,
                // 41775 => 1706630400,
                // 41776 => 1706630400,
                // 41777 => 1706630400,
                // // 餐费
                // LUNCH_PERDAY => 1706803200
            )
        ),

        BY_SEMESTER2 => array(
            AMOUNT => array(
                FULL5D => 113736,
                // HALF2D => 32443,
                // HALF3D => 46651,
                // HALF5D => 77440,
                //HALF4D => 62597,
            ),
        ),

    ),

    //每餐费用
    LUNCH_PERDAY => 45,

    //账单到期日和账单开始日之差
    DUEDAYS_OFFSET => 1,

));