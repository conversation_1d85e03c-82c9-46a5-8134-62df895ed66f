<?php
class ReportsController extends BranchBasedController
{
    // 访问action的初级权限
    public $actionAccessAuths = array(
        'index'  => 'o_A_reportMgt',
        'GetTemplate'           => 'o_A_reportMgt',
        'EditCate'              => 'o_A_reportMgt',
        'SaveCategory'          => 'o_A_reportMgt',
        'GetCateGory'           => 'o_A_reportMgt',
        'DelCateGory'           => 'o_A_reportMgt',
        'EditItem'              => 'o_A_reportMgt',
        'SaveItem'              => 'o_A_reportMgt',
        'GetItem'               => 'o_A_reportMgt',
        'DelItem'               => 'o_A_reportMgt',
        'Option'                => 'o_A_reportMgt',
        'GetOption'             => 'o_A_reportMgt',
        'SaveOption'            => 'o_A_reportMgt',
        'EditOption'            => 'o_A_reportMgt',
        'UpdateSort'            => 'o_A_reportMgt',
        'SaveItembat'           => 'o_A_reportMgt',
        'SaveOptionGroup'       => 'o_A_reportMgt',
        'DelOptionGroup'        => 'o_A_reportMgt',
        'UpdateSortOption'      => 'o_A_reportMgt',
    );

    public function createUrl($route, $params = array(), $ampersand = '&', $parentOnly = false)
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mgrades/reports/index');

        Yii::import('common.models.reportCards.*');
    }

    public function actionIndex()
    {
        Yii::import('common.models.calendar.*');
        Yii::import('common.models.grades.*');

        if ( Yii::app()->request->isAjaxRequest && Yii::app()->request->isPostRequest ) {
            if(isset($_POST['ReportCardTemplate'])) {
                $attrs = $_POST['ReportCardTemplate'];
                if($attrs['id']) {
                    $templateModel = ReportCardTemplate::model()->findByAttributes(
                        array(
                            'id'=>intval($attrs['id']),
                            'branch_id'=>$this->branchId
                        ));
                }
                if( is_null($templateModel) ){
                    $templateModel = new ReportCardTemplate();
                }
                $templateModel->setAttributes(array(
                    'title_en' => $attrs['title_en'],
                    'title_cn' => $attrs['title_cn'],
                    'for_age' => '',
                    'active' => isset($attrs['active']) ? 1 : 0,
                    'branch_id' => $this->branchId,
                    'update_user' => Yii::app()->user->id,
                    'updated' => time()
                ));

                if(!$templateModel->save()){
                    $this->addMessage('state', 'fail');
                    $errs = current($templateModel->getErrors());
                    $this->addMessage('message', $errs[0]);
                    $this->showMessage();
                }else{
                    if(intval($templateModel->id)){
                        ReportCardTemplateClass::model()->deleteAllByAttributes(array(
                            'branch_id' => $this->branchId,
                            'template_id' => intval($templateModel->id)
                        ));
                        if(isset($attrs['for_age'])){
                            $linkAttrs = array(
                                'template_id' => $templateModel->id,
                                'branch_id' => $this->branchId,
                                'update_user' => Yii::app()->user->id,
                                'updated' => time()
                            );
                            foreach($attrs['for_age'] as $forage) {
                                $model = new ReportCardTemplateClass();
                                $model->setAttributes($linkAttrs);
                                $model->for_age = $forage;
                                if(!$model->save()){
                                    $this->addMessage('state', 'fail');
                                    $errs = current($templateModel->getErrors());
                                    $this->addMessage('message', $errs[0]);
                                    $this->showMessage();
                                }
                            }
                        }
                    }
                }
            }
            $ages = $this->getAgeSelected();
            $this->addMessage('state', 'success');
            $this->addMessage('message', 'success!');
            $this->addMessage('callback', 'cbSuccess');
            $this->addMessage('data', $ages);
            $this->showMessage();
        }

        $criteria = new CDbCriteria();
        $criteria->compare('branch_id', $this->branchId);
        $criteria->order = 'active desc, updated desc';

        $dataProvider=new CActiveDataProvider('ReportCardTemplate', array(
            'criteria'=>$criteria,
        ));

        $ages = $this->getAgeSelected();
        //学校课程
        $cfgCampusProgram = CommonUtils::LoadConfig('CfgCampusProgram');
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');

        $this->render('index', array('dataProvider'=>$dataProvider, 'ages'=>$ages,'cfgCampusProgram'=>$cfgCampusProgram[Branch::TYPE_CAMPUS_DAYSTAR]));
    }

    private function getAgeSelected()
    {
        $ages = array();
        $items = ReportCardTemplate::model()->with('forAges')->findAllByAttributes(array('branch_id'=> $this->branchId,'active'=>1));
        foreach($items as $item){
            foreach($item->forAges as $age){
                $ages[] = $age->for_age;
            }
        }
        return $ages;
    }

    public function getAge($data, $row)
    {
        $classTypes = IvyClass::getClassTypes(true, $this->branchObj->type);
        $ret = array();
        foreach($data->forAges as $age){
            $ret[] = $classTypes[$age->for_age];
        }
        echo implode(', ', $ret);
    }

    public function getActive($data, $row)
    {
        echo $data->active == 1 ? Yii::t('report', 'Active') : Yii::t('report', 'Disabled');
    }

    public function getButton($data, $row)
    {
        echo sprintf('<button type="button" class="btn btn-primary btn-sm" onclick="createTemplate('.$data->id.')"
        title="%s"><span class="glyphicon glyphicon-wrench"></span></button> ', Yii::t('global', 'Edit'));
        echo sprintf('<button type="button" class="btn btn-primary btn-sm" onclick="editCategory('.$data->id.')"
        title="%s"><span class="glyphicon glyphicon-th-list"></span></button> ', Yii::t('report', 'Categories Edit'));
        echo sprintf('<a role="button" class="btn btn-primary btn-sm" title="%s" target=_blank href="%s"><span
        class="glyphicon glyphicon-pencil"></span></a> ',
            Yii::t('report', 'Items Edit'),
            $this->createUrl('editItem',array('id'=>$data->id)));
    }

    public function actionGetTemplate($id=0)
    {
        if($id){
            $model = ReportCardTemplate::model()->with('forAges')->findByPk($id);
            $ret = $model->attributes;
            foreach($model->forAges as $age){
                $ret['for_age'][] = $age->for_age;
            }
            echo CJSON::encode($ret);
        }
    }

    public function actionEditCate($id=0)
    {
        if($id){
            $model = ReportCardTemplate::model()->with('forAges')->findByPk($id);

            $this->render('editcate', array('model'=>$model));
        }
    }

    public function actionSaveCategory()
    {
        $tid = Yii::app()->request->getPost('tid', 0);
        $new = Yii::app()->request->getPost('new', array());
        $old = Yii::app()->request->getPost('old', array());
        $root_title = Yii::app()->request->getPost('root_title', array());
        $program = Yii::app()->request->getPost('program', array());
        $sub_title = Yii::app()->request->getPost('sub_title', array());
        $root_weight = Yii::app()->request->getPost('root_weight', array());
        $sub_weight = Yii::app()->request->getPost('sub_weight', array());
        if($tid){
            $ridarr = array();
            $newroot = isset($new['root']) ? $new['root'] : array();
            $newsub = isset($new['sub']) ? $new['sub'] : array();
            foreach($newroot as $newid){
                $model = new ReportsCategory();
                $model->template_id = $tid;
                $model->parent_id = 0;
                $model->title_en = trim($root_title[$newid]['en']);
                $model->title_cn = trim($root_title[$newid]['cn']);
                $model->program = $program[$newid]['program'];
                $model->options = '';
                $model->weight = $root_weight[$newid];
                $model->update_user = Yii::app()->user->id;
                $model->updated = time();
                $model->save();
                $ridarr[$newid] = $model->id;
            }
            foreach($newsub as $newid){
                $model = new ReportsCategory();
                $model->template_id = $tid;
                $model->parent_id = $ridarr[$sub_title[$newid]['pid']] ? $ridarr[$sub_title[$newid]['pid']] : $sub_title[$newid]['pid'];
                $model->title_en = trim($sub_title[$newid]['en']);
                $model->title_cn = trim($sub_title[$newid]['cn']);
                $model->options = '';
                $model->weight = $sub_weight[$newid];
                $model->update_user = Yii::app()->user->id;
                $model->updated = time();
                $model->save();
            }
            $oldroot = isset($old['root']) ? $old['root'] : array();
            $oldsub = isset($old['sub']) ? $old['sub'] : array();
            foreach($oldroot as $oldid){
                $model = ReportsCategory::model()->findByPk($oldid);
                $model->template_id = $tid;
                $model->parent_id = 0;
                $model->title_en = trim($root_title[$oldid]['en']);
                $model->title_cn = trim($root_title[$oldid]['cn']);
                $model->program = $program[$oldid]['program'];
//                $model->options = '';
                $model->weight = $root_weight[$oldid];
                $model->update_user = Yii::app()->user->id;
                $model->updated = time();
                $model->save();
                $ridarr[$oldid] = $model->id;
            }
            foreach($oldsub as $oldid){
                $model = ReportsCategory::model()->findByPk($oldid);
                $model->template_id = $tid;
                $model->parent_id = $ridarr[$sub_title[$oldid]['pid']] ? $ridarr[$sub_title[$oldid]['pid']] : $sub_title[$oldid]['pid'];
                $model->title_en = trim($sub_title[$oldid]['en']);
                $model->title_cn = trim($sub_title[$oldid]['cn']);
//                $model->options = '';
                $model->weight = $sub_weight[$oldid];
                $model->update_user = Yii::app()->user->id;
                $model->updated = time();
                $model->save();
            }
            $this->addMessage('state', 'success');
            $this->addMessage('message', '成功');
            $this->addMessage('callback', 'cbSave');
        }
        else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '失败');
        }
        $this->showMessage();
    }

    public function actionGetCateGory($id)
    {
        if($id){
            $ret = array();
            $criteria = new CDbCriteria();
            $criteria->compare('template_id', $id);
            $criteria->order='weight';
            $items = ReportsCategory::model()->findAll($criteria);
            foreach($items as $item){
                if($item->parent_id == 0){
                    $ret['root'][] = array(
                        'id' => $item->id,
                        'type' => 'old',
                        'pid' => $item->parent_id,
                        'program' => $item->program,
                        'title_en' => $item->title_en,
                        'title_cn' => $item->title_cn,
                        'weight' => intval($item->weight),
                    );
                }
                else{
                    $ret['sub'][] = array(
                        'id' => $item->id,
                        'type' => 'old',
                        'pid' => $item->parent_id,
                        'title_en' => $item->title_en,
                        'title_cn' => $item->title_cn,
                        'weight' => intval($item->weight),
                    );
                }
            }
            echo CJSON::encode($ret);
        }
    }

    public function actionDelCateGory()
    {
        $id = Yii::app()->request->getPost('id');
        if($id){
            $model = ReportsCategory::model()->findByPk($id);
            if($model->parent_id == 0){
                $count = ReportsCategory::model()->countByAttributes(array('parent_id'=>$id));
                if($count){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('report', 'Please clear sub categories');
                    $this->showMessage();
                }
            }
            if($model->delete()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('global', 'Data deleted'));
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('global', 'Deletion failed'));
            }
            $this->showMessage();
        }
    }

    public function actionEditItem($id=0, $cid=0)
    {
        if($id){
            $cfgCampusProgram = CommonUtils::LoadConfig('CfgCampusProgram');

            $model = ReportCardTemplate::model()->findByPk($id);

            $criteria = new CDbCriteria();
            $criteria->compare('t.template_id', $id);
            $criteria->compare('t.parent_id', 0);
            $criteria->order='t.weight';
            $criteria->index='id';
            $items = ReportsCategory::model()->findAll($criteria);
            $subs = null;
            $options = array();
            if($cid){
                $criteria = new CDbCriteria();
                $criteria->compare('t.template_id', $id);
                $criteria->compare('t.parent_id', $cid);
                $criteria->order='t.weight';
                $subs = ReportsCategory::model()->with('items')->findAll($criteria);

                $groups = ReportsOptionGroup::model()->findAll();
                $criteria = new CDbCriteria();
                $criteria->order='weight';
                $optionModel = ReportsOption::model()->findAll($criteria);
                foreach($optionModel as $pModel){
                    $options[$pModel->group_id][] = $pModel;
                }
            }
            $cs = Yii::app()->clientScript;
            $cs->registerCoreScript('jquery.ui');
            $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');

            $this->render('edititem', array(
                'model'=>$model,
                'items'=>$items,
                'subs'=>$subs,
                'cid'=>$cid,
                'options'=>$options,
                'groups'=>$groups,
                'programs'=>$cfgCampusProgram[Branch::TYPE_CAMPUS_DAYSTAR]['programs']
            ));
        }
    }

    public function actionSaveItem()
    {
        if(Yii::app()->request->isPostRequest && isset($_POST['ReportsItem'])){
            $id = Yii::app()->request->getPost('id', 0);
            $model = ReportsItem::model()->findByPk($id);
            if($model == null)
                $model = new ReportsItem();

            $model->attributes = $_POST['ReportsItem'];
            $model->title_cn = trim($_POST['ReportsItem']['title_cn']);
            $model->title_en = trim($_POST['ReportsItem']['title_en']);
            $model->updated = time();
            $model->update_user = Yii::app()->user->id;
            if($model->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', 'Data Saved!'));
                $this->addMessage('refresh', true);
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', 'Data Saving Failed!'));
            }
            $this->showMessage();
        }
    }

    public function actionGetItem($id=0)
    {
        if($id){
            $model = ReportsItem::model()->findByPk($id);
            echo CJSON::encode(array('title_en'=>$model->title_en, 'title_cn'=>$model->title_cn));
        }
    }

    public function actionDelItem()
    {
        if(Yii::app()->request->isPostRequest){
            $id = Yii::app()->request->getPost('id', 0);
            if($id){
                $model = ReportsItem::model()->findByPk($id);
                if($model->delete()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('global', 'Data deleted'));
                    $this->addMessage('refresh', true);
                }
                else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('global', 'Deletion failed'));
                }
            }
        }
        $this->showMessage();
    }

    public function actionOption($gid=0)
    {
        $options = array();
        if($gid){
            $criteria = new CDbCriteria();
            $criteria->compare('group_id', $gid);
            $criteria->order='weight';
            $options = ReportsOption::model()->findAll($criteria);
        }

        $groups = ReportsOptionGroup::model()->findAll();

        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');

        $this->render('editoption', array('groups' => $groups, 'options'=>$options, 'gid'=>$gid));
    }

    public function actionGetOption($id=0)
    {
        if($id){
            $ret = array();
            $model = ReportsOption::model()->findByPk($id);
            $ret['id'] = $model->id;
            $ret['label'] = $model->label;
            $ret['title_en'] = $model->title_en;
            $ret['title_cn'] = $model->title_cn;
            $ret['desc_en'] = $model->desc_en;
            $ret['desc_cn'] = $model->desc_cn;
            echo CJSON::encode($ret);
        }
    }

    public function actionSaveOption()
    {
        if(Yii::app()->request->isPostRequest && isset($_POST['ReportsOption'])){
            $id = isset($_POST['ReportsOption']['id']) ? $_POST['ReportsOption']['id'] : 0;
            $model = ReportsOption::model()->findByPk($id);
            if($model == null)
                $model = new ReportsOption();
            $model->attributes = $_POST['ReportsOption'];
            $model->update_user = Yii::app()->user->id;;
            $model->updated = time();
            if($model->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', 'Data Saved!'));
                $this->addMessage('refresh', true);
            }
            else{
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err[0]);
            }
            $this->showMessage();
        }
    }

    public function actionDelOption()
    {
        if(Yii::app()->request->isPostRequest){
            $id = Yii::app()->request->getPost('id', 0);
            if($id){
                $model = ReportsOption::model()->findByPk($id);
                if($model->delete()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('refresh', true);
                    $this->addMessage('message', Yii::t('global', 'Data deleted'));
                }
                else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('global', 'Deletion failed'));
                }
            }
        }
        $this->showMessage();
    }

    public function actionEditOption()
    {
        if(Yii::app()->request->isPostRequest){
            $cid = Yii::app()->request->getPost('cid', 0);
            $groupid = Yii::app()->request->getPost('groupid', 0);
            if($cid){
                $model = ReportsCategory::model()->findByPk($cid);
                $model->option_groupid = $groupid;
                if($model->save()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', '成功');
                }
                else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '失败');
                }
            }
        }
        $this->showMessage();
    }

    public function actionUpdateSort()
    {
        if(Yii::app()->request->isPostRequest){
            $id = Yii::app()->request->getPost('id', 0);
            $sort = Yii::app()->request->getPost('sort', array());

            foreach($sort as $key=>$val){
                ReportsItem::model()->updateByPk($key, array('weight'=>$val));
            }
        }
    }

    public function actionSaveItembat()
    {
        if(Yii::app()->request->isPostRequest){
            $category_id = Yii::app()->request->getPost('category_id', 0);
            $template_id = Yii::app()->request->getPost('template_id', 0);
            $standard_code = Yii::app()->request->getPost('standard_code', array());
            $title_en = Yii::app()->request->getPost('title_en', array());
            $title_cn = Yii::app()->request->getPost('title_cn', array());

            if($category_id && $template_id){
                $criteria = new CDbCriteria();
                $criteria->compare('category_id', $category_id);
                $criteria->compare('template_id', $template_id);
                $count = ReportsItem::model()->count($criteria);

                foreach($title_en as $key=>$ten){
                    $model = new ReportsItem();
                    $model->template_id = $template_id;
                    $model->category_id = $category_id;
                    $model->weight = $count++;
                    $model->title_en = trim($ten);
                    $model->title_cn = trim($title_cn[$key]);
                    $model->standard_code = trim($standard_code[$key]);
                    $model->updated = time();
                    $model->update_user = Yii::app()->user->id;
                    $model->save();
                }
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', 'Data Saved!'));
                $this->addMessage('refresh', true);
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', 'Data Saving Failed!'));
            }
            $this->showMessage();
        }
    }

    public function actionSaveOptionGroup()
    {
        if(Yii::app()->request->isPostRequest){
            $old = Yii::app()->request->getPost('old', array());
            $new = Yii::app()->request->getPost('new', array());
            foreach($old as $oid=>$val){
                $model = ReportsOptionGroup::model()->findByPk($oid);
                $model->title_cn = $val['title_cn'];
                $model->title_en = $val['title_en'];
                $model->active = isset($val['active'])?1:0;
                $model->updated = time();
                $model->update_user = Yii::app()->user->id;
                $model->save();
            }

            foreach($new['title_en'] as $nid=>$val){
                if($val || $new['title_cn'][$nid]){
                    $model = new ReportsOptionGroup;
                    $model->title_cn = $new['title_cn'][$nid];
                    $model->title_en = $val;
                    $model->active = isset($new['active'][$nid])?1:0;
                    $model->updated = time();
                    $model->update_user = Yii::app()->user->id;
                    $model->save();
                }
            }
            $this->addMessage('state', 'success');
            $this->addMessage('refresh', true);
            $this->addMessage('message', Yii::t('global', 'Data Saved!'));
            $this->showMessage();
        }
    }

    public function actionDelOptionGroup()
    {
        if(Yii::app()->request->isPostRequest){
            $id = Yii::app()->request->getPost('id', 0);
            $model = ReportsOptionGroup::model()->findByPk($id);
            $hasOptions = ReportsOption::model()->countByAttributes(array('group_id'=>$id));
            if($hasOptions >0) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('report', 'Group is not empty, cannot be deleted'));
            }else {
                if($hasOptions == 0 && $model->delete()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('refresh', true);
                    $this->addMessage('message', Yii::t('global', 'Data deleted'));
                }
                else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('global', 'Deletion failed'));
                }
            }
            $this->showMessage();
        }
    }

    public function actionUpdateSortOption()
    {
        if(Yii::app()->request->isPostRequest){
            $sort = Yii::app()->request->getPost('sort', array());
            foreach($sort as $key=>$val){
                ReportsOption::model()->updateByPk($key, array('weight'=>$val));
            }
        }
    }
}