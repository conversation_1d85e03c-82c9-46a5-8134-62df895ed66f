<?php

/**
 * This is the model class for table "ivy_grades_schedule_link".
 *
 * The followings are the available columns in table 'ivy_grades_schedule_link':
 * @property integer $id
 * @property integer $schedule_id
 * @property integer $class_id
 * @property string $schedule_code
 */
class GradesScheduleLink extends SubDBActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_grades_schedule_link';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('schedule_id, class_id, schedule_code', 'required'),
			array('schedule_id, class_id, yid', 'numerical', 'integerOnly'=>true),
			array('schedule_code', 'length', 'max'=>8),
			array('schoolid', 'length', 'max'=>10),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, schedule_id, class_id, schedule_code, yid, schoolid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => '主键',
			'schedule_id' => '课表ID',
			'class_id' => '班级ID',
			'schedule_code' => '时间表代码，比如A或B，以配置文件为准',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('schedule_id',$this->schedule_id);
		$criteria->compare('class_id',$this->class_id);
		$criteria->compare('schedule_code',$this->schedule_code,true);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('schoolid',$this->schoolid,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return GradesScheduleLink the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
    
    /*
     * 查询已分配班级
     * @param schoolid
     * @param yid
     * @return array
     */
    public static function getAssignedClass($schoolid,$yid){
        $data = array();
        $result = Yii::app()->subdb->createCommand()
                ->select('class_id')
                ->from('ivy_grades_schedule_link')
                ->where('schoolid=:schoolid and yid=:yid', array(':schoolid'=>$schoolid,':yid'=>$yid))
                ->queryAll();
        if (count($result)) {
            foreach ($result as $val) {
                $data[$val['class_id']] = $val['class_id'];
            }
        }
        return $data;
    }
    
    /*
     * 查询已分配班级
     * @param id
     * @return array
     */
    public static function getAssignedClassByScheduleId($id){
        $data = array();
        $result = Yii::app()->subdb->createCommand()
                ->select('class_id')
                ->from('ivy_grades_schedule_link')
                ->where('schedule_id=:schedule_id', array(':schedule_id'=>$id))
                ->queryAll();
        if (count($result)) {
            foreach ($result as $val) {
                $data[$val['class_id']] = $val['class_id'];
            }
        }
        return $data;
    }
}
