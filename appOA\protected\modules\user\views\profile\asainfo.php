<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <ol class="breadcrumb">
                <li><?php echo CHtml::link(Yii::t('site','User Center'), array('/backend/apps'));?></li>
                <li class="active"><?php echo Yii::t('site','ASA Salary');?></li>
            </ol>
            <div class="well well-sm"><?php echo Yii::t('site','Staff Name') ?>: <?php echo $userModel->getName(); ?></div>
            <div class="alert alert-warning" role="alert"><?php echo Yii::t('site','Data is provided by Mammoth, if you have any question, please contact campus ASA coordinator.') ?></div>
            <div class="row">
            <?php
            foreach ($data as $k => $items):
                $total = 0;
                ?>
                <div class="col-md-12">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h3 class="panel-title"><?php echo $k; ?> <span class="label label-<?php echo (current($items)->report->status == 10) ? "warning" : (current($items)->report->status == 20 ? "info" : "success" )?>">
                                    <?php echo current($items)->report->getReportStatus(); ?></span></h3>
                        </div>
                        <h2 class="text-default" style="padding: 0 8px;">￥<span id="total_<?php echo $k?>"></span></h2>
                        <table class="table table-bordered">
                            <colgroup>
                                <col width="50%">
                                <col width="30%">
                                <col width="20%">
                            </colgroup>
                            <?php foreach ($items as $item):
                                $total += $item->unit_price * $item->course_count;
                                $url = $this->createUrl('coursedetail', array(
                                    'report_id' => $item->report_id,
                                    'vendor_id' => $item->vendor_id,
                                    'course_id' => $item->course_id,
                                ));
                                ?>
                                <tr>
                                    <td><a class="J_modal drop" href="<?php echo $url; ?>" title="<?php echo Yii::t('site','Click for detail') ?>"><?php echo ($item->course) ? $item->course->getTitle() : $item->group->getName(); ?></a></td>
                                    <td><?php echo (Yii::app()->language == 'zh_cn') ? $typeList['job_type'][$item->position]['cn'] : $typeList['job_type'][$item->position]['en'] ; ?></td>
                                    <td align="right"><?php echo $item->course_count .' * '. $item->unit_price; ?></td>
                                </tr>
                            <?php endforeach;?>
                            <?php if(isset($variation[$items[0]->report_id])): $total += $variation[$items[0]->report_id]->variation_amout;?>
                                <tr class="warning">
                                    <td><?php echo Yii::t('site','Adjustment') ?></td>
                                    <td><?php echo Yii::t('site','Memo') ?>：<?php echo $variation[$items[0]->report_id]->memo; ?></td>
                                    <td align="right"><?php echo $variation[$items[0]->report_id]->variation_amout; ?></td>
                                </tr>
                            <?php endif; ?>

                        </table>
                    </div>
                </div>
                <script>
                    var total_month = "<?php echo sprintf("%1\$.2f", $total); ?>";
                    $('#total_<?php echo $k?>').text(total_month)
                </script>
            <?php endforeach; ?>
        </div>
    </div>
</div>

<script>
    var modal = '<div class="modal fade" id="modal" tabindex="1" role="dialog" aria-labelledby="modal"><div class="modal-dialog" role="document"><div class="modal-content"></div></div></div>';
    $('body').append(modal);

    function cbSuccess() {
        location.reload();
    }
</script>