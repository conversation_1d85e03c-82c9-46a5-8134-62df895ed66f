<?php

/**
 * This is the model class for table "ivy_asa_invoice_item_extra".
 *
 * The followings are the available columns in table 'ivy_asa_invoice_item_extra':
 * @property integer $id
 * @property integer $invoice_id
 * @property integer $course_id
 * @property integer $invoice_item_id
 * @property integer $course_group_id
 * @property string $extra_type
 * @property string $extra_date
 * @property string $status
 * @property integer $update
 * @property integer $update_by
 * @property string $schoolid
 */
class AsaInvoiceItemExtra extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_asa_invoice_item_extra';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('invoice_id, course_id, invoice_item_id, course_group_id, update, update_by', 'numerical', 'integerOnly'=>true),
			array('extra_type, extra_date, status, schoolid', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, invoice_id, course_id, invoice_item_id, course_group_id, extra_type, extra_date, status, update, update_by, schoolid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'asainvoiceitem' => array(self::HAS_ONE, 'AsaInvoiceItem', array('id'=>'invoice_item_id')),
			'asainvoice' => array(self::HAS_ONE, 'AsaInvoice', array('id'=>'invoice_id')),
			'asacourse' => array(self::HAS_ONE, 'AsaCourse', array('id'=>'course_id')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'invoice_id' => 'Invoice',
			'course_id' => 'Course',
			'invoice_item_id' => 'Invoice Item',
			'course_group_id' => 'Course Group',
			'extra_type' => 'Extra Type',
			'extra_date' => 'Extra Date',
			'status' => 'Status',
			'update' => 'Update',
			'update_by' => 'Update By',
			'schoolid' => 'Schoolid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('invoice_id',$this->invoice_id);
		$criteria->compare('course_id',$this->course_id);
		$criteria->compare('invoice_item_id',$this->invoice_item_id);
		$criteria->compare('course_group_id',$this->course_group_id);
		$criteria->compare('extra_type',$this->extra_type,true);
		$criteria->compare('extra_date',$this->extra_date,true);
		$criteria->compare('status',$this->status,true);
		$criteria->compare('update',$this->update);
		$criteria->compare('update_by',$this->update_by);
		$criteria->compare('schoolid',$this->schoolid,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AsaInvoiceItemExtra the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
