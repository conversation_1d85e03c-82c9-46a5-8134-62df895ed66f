<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
    </button>
    <h4 class="modal-title" id="exampleModalLabel"><?php echo $proudctsModel->getTitle(); ?></h4>
</div>
<div class="modal-body">
    <div class="form-horizontal">
        <?php
        $config = Products::config();
        if ($proudctsModel->school_id == $branchId):
            ?>
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo Yii::t("teaching", "Photo Upload") ?></label>
                <div class="col-xs-1">
                    <button class="btn btn-primary mb15" id="select-photo"
                            type="button"><?php echo Yii::t('user', 'Select a Photo');?></button>
                </div>
                <label class="col-xs-2 control-label" style="margin-left: 10px">建议尺寸500*500</label>
               
            </div>
        <?php endif; ?>
        <div class="form-group">
            <label class="col-xs-2 control-label"><?php echo Yii::t("asa", "已传图片") ?></label>

            <div class="col-xs-10" id="container">
            <button class="btn btn-primary mb15" onclick="sortList('<?php echo $proudctsModel->id?>')" type="button" data-pid="<?php echo $proudctsModel->id?>" id="photo"><?php echo Yii::t('user', '排序');?></button>
                <div id="showPhoto">
                    <?php if ($productsImgModel): ?>
                        <?php foreach ($productsImgModel as $imgItem): ?>
                            <form id="<?php echo $imgItem->id . '_photo' ?>" class="avatar-form J_ajaxForm" method="post"
                                  action="<?php echo $this->createUrl('tag'); ?>">
                                <p>
                                        <span style="display: inline-block;position: relative">
                                            <?php  if ($proudctsModel->school_id == $branchId): ?>
                                                <a href="<?php echo $this->createUrl('deletePhoto',array('imgId' => $imgItem->id))?>" class="glyphicon glyphicon-trash J_ajax_del" style="position: absolute;right: 1em;top: 1em;cursor: pointer;color: #5bc0de;font-size:18px"></a>
                                            <?php endif; ?>
                                            <img src="<?php echo Yii::app()->params['OAUploadBaseUrl'] . '/products/' . $imgItem->img ?>">
                                        </span>
                                </p>

                                <div class="row">
                                    <?php
                                    if ($proudctsModel->school_id == $branchId):
                                        $i = 0;
                                        $tag = array();
                                        if ($imgItem->tag) {
                                            $tag = explode(";", $imgItem->tag);
                                        }
                                        foreach ($config[$proudctsModel->type]['item'] as $key => $types) {
                                            $tagM = array();
                                            foreach ($types['option'] as $item) {
                                                $tagM[$item] = $item;
                                            }
                                            echo '<div class="col-md-2">';
                                            echo CHtml::dropDownList($key, ($tag) ? $tag[$i] : "", $tagM, array('empty' => $key, 'class' => 'form-control mb10'));
                                            echo '</div>';
                                            $i++;
                                        }?>
                                        <?php echo CHtml::HiddenField("imgId", $imgItem->id); ?>
                                        <div class="clearfix"></div>
                                        <div class="col-md-2">
                                            <button type="submit"
                                                    class="btn btn-primary J_ajax_submit_btn mb10"><?php echo Yii::t('global', 'Submit');?></button>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <hr/>
                            </form>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
</div>
<script>
    $(function () {
        uploader = new plupload.Uploader({
            runtimes: 'html5,flash,silverlight,html4',
            browse_button: 'select-photo',
            container: document.getElementById('container'),
            url: '<?php echo $this->createUrl('updatePhoto') ?>',
            flash_swf_url: '<?php echo Yii::app()->themeManager->baseUrl . '/base/js/plupload/Moxie.swf' ?>',
            silverlight_xap_url: '<?php echo Yii::app()->themeManager->baseUrl . '/base/js/plupload/Moxie.xap' ?>',
            multipart_params: {pid:<?php echo $pid; ?>},
            resize: {
                /* width: 500,
                 height: 500*/
            },
            filters: {
                max_file_size: '5mb',
                mime_types: [
                    {title: "<?php echo Yii::t('teaching', 'Image files'); ?>", extensions: "jpg,gif,png,jpeg"}
                ]
            },

            init: {
                QueueChanged: function (up) {
                    uploader.start();
                },
                UploadProgress: function (up, file) {

                },
                FileUploaded: function (up, file, info) {
                    var response = eval('(' + info.response + ')');
                    var arrtConfig = <?php echo CJSON::encode($config[$proudctsModel->type]);?>;
                    var attrHtml = '<form class="avatar-form J_ajaxForm" method="post" id="'+ response.id +'_photo" action="<?php echo $this->createUrl('tag'); ?>">';
                    attrHtml += '<p><span style="display: inline-block;position: relative"><a href="<?php echo $this->createUrl('deletePhoto')?>&imgId='+ response.id +'" class=" J_ajax_del glyphicon glyphicon-trash J_ajax_del" style="color: #5bc0de;font-size:18px;position: absolute;right: 1em;top: 1em;cursor: pointer;"></a><img src="' + response.url + '"></span></p>';
                    attrHtml += '<div class="row">'
                    for (var ckey in arrtConfig['item']) {
                        attrHtml += '<div class="col-md-2"><select  class="form-control mb10" name=' + ckey + '>';
                        attrHtml += '<option value="">' + ckey + '</option>';
                        for (var opt in arrtConfig['item'][ckey]['option']) {
                            attrHtml += '<option value="' + arrtConfig['item'][ckey]['option'][opt] + '">' + arrtConfig['item'][ckey]['option'][opt] + '</option>';
                        }
                        attrHtml += '</select></div>';
                    }
                    attrHtml += '<div class="clearfix"></div><input type="hidden" value="' + response.id + '" name="imgId">';
                    attrHtml += '<div class="col-md-2"><button type="submit" class="btn btn-primary J_ajax_submit_btn mb10">提交</button></div></div><hr/>';
                    attrHtml += '</form>';
                    $("#container").append(attrHtml)
                    head.Util.ajaxForm();
                    head.Util.ajax();
                    head.Util.ajaxDel();
                }
            }
        });
        uploader.init();
    })

    head.Util.ajaxDel();

    function cbSuccess(data) {
        $('#'+data.imgId).remove();
    }
    function sortList(){
        $('#sort').modal('show')
    }
</script>
