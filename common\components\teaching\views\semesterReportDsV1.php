<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="language" content="en" />
    <title><?php echo CHtml::encode($this->pageTitle); ?></title>
</head>

<body>

    <?php if($withToolBar):?>
        <div class="navbar navbar-fixed-top navbar-inverse" role="navigation">
            <?php
            echo CHtml::form( $downloadActionUrl );
            echo CHtml::hiddenField('schoolid',     $params['schoolid']);
            echo CHtml::hiddenField('childid',      $params['childid']);
            echo CHtml::hiddenField('classid',      $params['classid']);
            echo CHtml::hiddenField('id',           $params['id']);
            echo CHtml::submitButton('Download PDF / 下载 PDF', array('style'=>'font-family: Microsoft Yahei'));
            echo CHtml::endForm();
            ?>
        </div>
    <?php endif;?>

    <div class="report-wrapper web-view">
        <!--page one-->
        <div id="page-1" class="report-page page-break-after">
            <!-- page content-->
            <div class="page-first-content">

                <?php
                if($data['child']->photo){
                    echo CHtml::image(CommonUtils::childPhotoUrl($data['child']->photo),
                        null,
                        array('class' => 'profile-photo')
                    );
                }
                ?>

                <div class="profile-box">
                    <dl>
                        <dt><span class="cn"><?php echo Yii::t('report', 'Name');?></span></dt>
                        <dd><?php
                            $childName = $data['child']->getChildName();
                            $hasCNChars = CommonUtils::hasCNChars( $childName );

                            if( $hasCNChars > 0){
                                echo '<span class="cn">';
                            }else{
                                echo '<span class="en">';
                            }

                            echo $childName . '</span>';

                            ?></dd>

                        <dt><span class="cn"><?php echo Yii::t('report', 'DOB');?></span></dt>
                        <dd><?php echo $data['child']->birthday_search?></dd>

                        <dt><span class="cn"><?php echo Yii::t('report', 'Campus');?></span></dt>
                        <dd><?php
                            $title = $data['campusTitle'];
                            $title = str_replace('(', '(<span class="cn">', $title);
                            $title = str_replace(')', '</span>)', $title);
                            echo $title;
                            ?></dd>

                        <dt><span class="cn"><?php echo Yii::t('report', 'Class');?></span></dt>
                        <dd><?php echo $data['classTitle'];?></dd>

                        <dt><span class="cn"><?php echo Yii::t('report', 'Class Teachers');?></span></dt>
                        <dd>
                            <div class="teacher-list">
                                <?php
                                if ($data['teachers']):?>
                                    <?php foreach ($data['teachers'] as $tName):?>
                                        <span class="item"><?php echo $tName['name'] . ' - ' . $tName['position'];?></span>
                                    <?php endforeach;?>
                                <?php endif;?>
                            </div>
                        </dd>
                    </dl>
                </div>
            </div><!-- page content -->
            <div class="top-title">
                <div class="cn"><?php echo IvyClass::formatSchoolYear($data['report_base'][$params['id']]['startyear']);?>评估报告</div>
                <div class="en"><?php echo IvyClass::formatSchoolYear($data['report_base'][$params['id']]['startyear'])?> Progress Report</div>
            </div>
            <div class="logo-top-center"></div>
        </div>
        <div id="page-2" class="report-page page-break-after">
            <div style="height: 120px;"></div>
            <div class="pull-content">
                <table class="table table-bordered">
                    <tr>
                        <td width="20%"></td>
                        <td width="40%"><?php echo Yii::t('report', 'Term 1');?></td>
                        <td width="40%"><?php echo Yii::t('report', 'Term 2');?></td>
                    </tr>
                    <tr>
                        <td><?php echo Yii::t('report', 'Days Absent');?></td>
                        <td><?php echo $data['report_attendance'][1]['absent']?></td>
                        <td><?php echo $data['report_attendance'][2]['absent']?></td>
                    </tr>
                    <tr>
                        <td><?php echo Yii::t('report', 'Days Tardy');?></td>
                        <td><?php echo $data['report_attendance'][1]['tardy']?></td>
                        <td><?php echo $data['report_attendance'][2]['tardy']?></td>
                    </tr>
                    <tr>
                        <td><?php echo Yii::t('report', 'Additional Support and Services');?></td>
                        <td><?php echo $data['report_attendance'][1]['support']?></td>
                        <td><?php echo $data['report_attendance'][2]['support']?></td>
                    </tr>
                </table>
                <?php if(Yii::app()->language == 'zh_cn'):?>
                    <table class="table cn">
                        <thead>
                        <tr>
                            <th colspan="3">孩子作为一名学习者</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td colspan="3">
                                启明星学校笃行中西合璧之教育模式，博采东西方文化之长，秉承服务全社会之宗旨，致力于培养中英文学业卓越，具有创造性思维及高贵品格的世界公民。启明星学校的学生努力发展成为:
                            </td>
                        </tr>
                        <tr>
                            <td style="border-top:0;">双语卓越的人</td>
                            <td style="border-top:0;">心胸宽广的人</td>
                            <td style="border-top:0;">有原则的人</td>
                        </tr>
                        <tr>
                            <td style="border-top:0;">具有全球视野的人</td>
                            <td style="border-top:0;">有爱心的人</td>
                            <td style="border-top:0;">善于反思的人</td>
                        </tr>
                        <tr>
                            <td style="border-top:0;">全面发展的人</td>
                            <td style="border-top:0;">知识渊博的人</td>
                            <td style="border-top:0;">交流者</td>
                        </tr>
                        <tr>
                            <td style="border-top:0;">思考者</td>
                            <td style="border-top:0;">探究者</td>
                            <td style="border-top:0;">勇于尝试的人</td>
                        </tr>
                        </tbody>
                    </table>
                <?php else:?>
                    <table class="table en">
                        <thead>
                        <tr>
                            <th colspan="3">The Child As A Learner</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td colspan="3">
                                Daystar Academy develops world citizens by embracing Chinese and Western culture, through its integrated education model.  Daystar students strive for distinction in Chinese and English studies, creative thinking and character development for the purpose of serving the community at large.  Daystar learners strive to be:
                            </td>
                        </tr>
                        <tr>
                            <td style="border-top:0;">Bilingual</td>
                            <td style="border-top:0;">Open-Minded</td>
                            <td style="border-top:0;">Principled</td>
                        </tr>
                        <tr>
                            <td style="border-top:0;">Global-Minded</td>
                            <td style="border-top:0;">Caring</td>
                            <td style="border-top:0;">Reflective</td>
                        </tr>
                        <tr>
                            <td style="border-top:0;">Balanced</td>
                            <td style="border-top:0;">Knowledgeable</td>
                            <td style="border-top:0;">Communicators</td>
                        </tr>
                        <tr>
                            <td style="border-top:0;">Critical Thinkers</td>
                            <td style="border-top:0;">Inquirers</td>
                            <td style="border-top:0;">Courageous</td>
                        </tr>
                        </tbody>
                    </table>
                <?php endif;?>
                <table class="table">
                    <thead>
                    <tr>
                        <th><?php echo Yii::t('report', 'Grade-Level Proficiency')?></th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php foreach ($optionsList as $group):
                        if($group['title'] != 'ESL'):
                        ?>
                        <tr>
                            <td align="center" style="border-top:0;">
                                <?php echo $group['title']; ?>
                                <table class="table" style="margin-bottom:2px;">
                                    <tr>
                                        <?php foreach ($group['options'] as $option): ?>
                                            <td style="border-top:0;text-align: center;"><strong><?php echo $option['label']; ?></strong><br/><?php echo CommonUtils::autoLang($option['title_cn'], $option['title_en']); ?></td>
                                        <?php endforeach; ?>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    <?php endif;endforeach; ?>
                    </tbody>
                </table>
            </div>
            <div class="logo-contact-left"></div>
            <div class="top-small-title">
                <div class="cn">Progress Report 评估报告</div>
                <div class="cn">Daystar Academy启明星学校</div>
                <div class="cn"><?php echo IvyClass::formatSchoolYear($data['report_base'][$params['id']]['startyear']);?></div>
            </div>
        </div>
        <!--page one-->
        <div id="page-6" class="report-page">
            <div style="height: 20px;"></div>
            <div class="pull-content">
                <h3><?php echo Yii::t('report', 'Student Achievement Summary')?></h3>
                <?php
                    ksort($optionsList);
                    foreach ($optionsList as $optionKey => $options):
                        if($options['title'] != 'ESL'):
                ?>
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th><?php echo $options['title'];?></th>
                            <?php foreach ($data['reportSemester'] as $semester):?>
                                <th colspan="<?php echo count($options['options']);?>"><?php echo $semester;?></th>
                            <?php endforeach;?>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td></td>
                            <?php foreach ($data['reportSemester'] as $semester):?>
                                <?php foreach ($optionsList[$optionKey]['options'] as $option):?>
                                <td><?php echo $option['label'];?></td>
                                <?php endforeach;?>
                            <?php endforeach;?>
                        </tr>
                        <?php foreach ($data['root'] as $root):?>
                        <?php if ($optionKey == $root['option_groupid']):?>
                        <tr>
                            <td style="width:280px;"><?php echo $root['title'];?></td>
                            <?php foreach ($data['reportSemester'] as $srKey=>$semester):?>
                                <?php foreach ($optionsList[$optionKey]['options'] as $option):?>
                                <td align="center">
                                    <?php
                                        if (isset($data['report'][$srKey][$root['id']]['option'][$root['id']]) && $data['report'][$srKey][$root['id']]['option'][$root['id']] == $option['id'] ):
                                            echo CHtml::image('http://m1.files.ivykids.cn/images/sreport/booked.png','',array('width'=>10,'height'=>10));
                                        else:
                                            echo '';
                                        endif;
                                    ?>
                                </td>
                                <?php endforeach;?>
                            <?php endforeach;?>
                        </tr>
                        <?php endif;?>
                        <?php endforeach;?>
                    </tbody>
                </table>
                <?php endif;endforeach;?>
                <?php foreach ($data['root'] as $root):?>
                    <h3><span class="cn"><?php echo $root['title']?></span></h3>
                    <?php
                        $i=0;
                        foreach ($data['subs'][$root['id']] as $subs):
                        $i++;
                    ?>
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th><?php echo $subs['title'];?></th>
                                    <?php foreach ($data['reportSemester'] as $semester):?>
                                        <th colspan="<?php echo count($optionsList[$subs['option_groupid']]['options']);?>" style="width:50px;"><?php echo $semester;?></th>
                                    <?php endforeach;?>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td></td>
                                    <?php foreach ($data['reportSemester'] as $semester):?>
                                        <?php foreach ($optionsList[$subs['option_groupid']]['options'] as $option):?>
                                        <td><?php echo $option['label'];?></td>
                                        <?php endforeach;?>
                                    <?php endforeach;?>
                                </tr>
                                <?php
                                foreach ($data['items'][$subs['id']] as $keyItem=>$items):?>
                                <tr>
                                    <td style="width:280px;"><?php echo $items;?></td>
                                    <?php foreach ($data['reportSemester'] as $srKey=>$semester):?>
                                        <?php foreach ($optionsList[$subs['option_groupid']]['options'] as $option):?>
                                        <td align="center">
                                            <?php
                                                if (isset($data['report'][$srKey][$root['id']]['option'][$subs['id']][$keyItem]) && $data['report'][$srKey][$root['id']]['option'][$subs['id']][$keyItem] == $option['id'] ):
                                                    echo CHtml::image('http://m1.files.ivykids.cn/images/sreport/booked.png','',array('width'=>10,'height'=>10));
                                                else:
                                                    echo '';
                                                endif;
                                            ?>
                                        </td>
                                        <?php endforeach;?>
                                    <?php endforeach;?>
                                </tr>
                                <?php endforeach;?>
                            </tbody>
                        </table>
                    <?php endforeach;?>
                <?php endforeach;?>

                <div>
                    <table class="table table table-bordered">
                        <?php foreach ($data['reportSemester'] as $srKey=>$semester):?>
                            <tr>
                                <th align="left"><?php echo $semester. ' '. Yii::t('report', 'Teacher\'s Comment / Next Step');?></th>
                            </tr>
                            <?php
                            if(isset($data['report'][$srKey])):
                            foreach($data['report'][$srKey] as $r_key => $r_value):
                                if(isset($r_value['memo']) && $r_value['memo']):
                            ?>
                            <tr>
                                <td><?php echo $data['root'][$r_key]['title'].CommonUtils::addColon().$data['report'][$srKey][$r_key]['memo'];?></td>
                            </tr>
                            <?php
                            endif;
                            endforeach;
                            endif;
                            ?>
                        <?php endforeach;?>
                    </table>
                </div>

                <?php if(Yii::app()->language == 'zh_cn'):?>
                    <div class="cn">
                        <h3>评估报告注释</h3>
                        <p>启明星学校首要目标是让每位学生通过学习取得学业上的成功，发展人际交往能力。评估报告的目的是根据年级评定标准，反映学生进步的信息。在语言艺术、社会学、科学等学科上，使用专业标准来评估每位学生。</p>
                        <h3>学业</h3>
                        <h5>处于年级水平起步阶段</h5>
                        <p>学生取得一些进步，但还低于年级期望水平。学生还没有完全掌握应有知识和技能。学生的表现低于应有的年级水平，可能需要更多的支持和努力。</p>
                        <h5>处于年级水平发展阶段</h5>
                        <p>表明学生正在取得进步，学年末有足够的能力达到年级水平。处于该阶段学生已经达到年级水平的所有标准。学生已经取得了预期的进步，但还未达到学年末水平。和您的孩子沟通，说明D表示他/她正在朝学年末达标标准进步，这很重要。</p>
                        <h5>处于年级水平达标阶段</h5>
                        <p>表明学生已经达到本年级学年末标准。目标是让所有学生在年末达到该标准。</p>
                        <h5>超出年级水平阶段</h5>
                        <p>表明学生已经超过了该年级应有标准。这个阶段的学生能够将所学知识运用到新的领域，并且独立使用知识技能，完成更高年级的工作。</p>
                        <h3>非学业</h3>
                        <p>学生表现是基于老师观察发现的行为频率来测评。需要注意的是，非学术分级评定参照的是行为数量，而不是质量。老师只汇报观察到的行为出现频率，而非对此行为作出评价。</p>
                        <h5>有待提高</h5>
                        <p>表明学生很少或有时表现良好</p>
                        <h5>经常</h5>
                        <p>表明学生大部分时间表现良好</p>
                        <h5>一贯</h5>
                        <p>表明学生一如既往表现良好</p>
                        <h3>ESL</h3>
                        <p>在此水平的ESL学生可以阅读、理解、模仿或运用</p>
                        <table class="table table-bordered">
                            <tr>
                                <td>1</td>
                                <td>
                                    •	相关学科的图形语言<br>
                                    •	单词或词组来回应一步式指令、指示、是非问句或陈述<br>
                                    •	口语回应基本的口语指令或直接提问，语言使用会出现错误，通常会阻碍意思表达
                                </td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>
                                    •	相关学科的通用语言<br>
                                    •	词组或短句<br>
                                    •	口语或书面语，语言使用会出现错误，通常会阻碍意思表达
                                </td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>
                                    •	相关学科的通用语言和一些专用语<br>
                                    •	延展句子用于口语交际或段落写作中<br>
                                    •	口语或书面语，语言使用会出现错误，可能会阻碍意思表达，但能保持大意
                                </td>
                            </tr>
                            <tr>
                                <td>4</td>
                                <td>
                                    •	相关学科的专用语和一些术<br>
                                    •	各种复杂长短句用于口头论述、复句、相关句子或段落中<br>
                                    •	口语或书面语，语言使用会出现错误，但不影响整体意思表达，能保持大意
                                </td>
                            </tr>
                            <tr>
                                <td>5</td>
                                <td>
                                    •	相关学科的特定或专用术语<br>
                                    •	各种复杂长短句用于扩展口头论述或写作论述中，包括故事和短文<br>
                                    •	学习同年级水平教学资料和内容，口语或书面语使用水平与英语熟练使用者相当
                                </td>
                            </tr>
                        </table>
                    </div>
                <?php else:?>
                    <div class="en">
                        <h3>Explanation of the Progress Report</h3>
                        <p>The primary goal of Daystar Academy is to enable each student to learn and achieve academic success and develop effective interpersonal skills. The purpose of the Progress Report is to give more information about each child’s progress in relation to the grade-level standards. Each grade-level assesses students on the most significant standards in the areas of language arts, social studies, and science.</p>
                        <h3>Academic</h3>
                        <h5>Beginning progress toward grade-level standards:</h5>
                        <p>The student is making some progress, but is below the level that is expected at that time of the marking period. The student shows limited understanding of the end of year concepts and skills. Student performance is “below” grade level and may need more support to catch up.</p>
                        <h5>Developing progress toward grade-level standards:</h5>
                        <p>Indicates that the student is progressing at a sufficient rate to be able to meet the end-of-the-year grade-level expectations. This grade would be appropriate for an “on-level’ student for most standards. The student is making expected progress but is not yet at end-of-year standards. It is important to communicate with your child that D indicates he or she is “on track” to achieving a “Proficient” by the end of the school year. </p>
                        <h5>Proficient in meeting grade-level standards:</h5>
                        <p>Indicates a student has met the end-of-the-year standards for the grade. The goal is for all students to receive this grade by the end of the year.</p>
                        <h5>Exceeding grade-level standards:</h5>
                        <p>Indicates a student has gone beyond the end-of-the-year standards for that grade. Students who are exceeding are able to apply learning to new situations and independently use strategies and skills to do work that are expected of students in higher grades.</p>
                        <h3>Non-Academic</h3>
                        <p>Performance is assessed based on the FREQUENCY of desired behaviors, as informed by teacher observation. Thus the teachers are reporting the frequency with which these behaviors are being observed, rather than judging the quality of the behavior. </p>
                        <h5>Improvement Needed</h5>
                        <p>Indicates that the student seldom or sometimes displays desired behavior.</p>
                        <h5>Often</h5>
                        <p>Indicates that the student displays desired behavior most of the time.</p>
                        <h5>Consistently</h5>
                        <p>Indicates a student is consistent in displaying desired behavior.</p>
                        <h3>ESL</h3>
                        <table class="table table-bordered">
                            <tr>
                                <td>1</td>
                                <td>
                                    •	pictorial representation of the language of content areas<br>
                                    •	words, phrases, or chunks of language when presented with one-step commands, directions, yes or no questions, or statements<br>
                                    •	oral language with errors that often impede meaning when presented with basic oral commands or direct questions
                                </td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>
                                    •	general language related to content area<br>
                                    •	phrases or short sentences<br>
                                    •	oral or written language with errors that often impede the meaning of the communication
                                </td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>
                                    •	general and some specific language of content areas<br>
                                    •	expanded sentences in oral interaction or written paragraph<br>
                                    •	oral or written language with errors that may impede the meaning of the communication but retain much of its meaning
                                </td>
                            </tr>
                            <tr>
                                <td>4</td>
                                <td>
                                    •	specific and some technical language of content areas<br>
                                    •	a variety of sentence lengths of varying linguistic complexity in oral discourse or multiple, related sentences or paragraphs<br>
                                    •	oral or written language with errors that do not impede the overall meaning of the communication but retain much of its meaning
                                </td>
                            </tr>
                            <tr>
                                <td>5</td>
                                <td>
                                    •	specialized or technical language of the content areas<br>
                                    •	a variety of sentence lengths of varying linguistic complexity in extended oral discourse or written discourse, including stories, essays<br>
                                    •	oral or written language approaching comparability to that of English-proficient peers when presented with grade-level material
                                </td>
                            </tr>
                        </table>
                    </div>
                <?php endif;?>
                <div style="height: 20px;"></div>
            </div>
            <!-- page content -->
            <!-- 结尾 -->
        </div>
    </div>


    <style>
        .en{font-family: Tahoma,'Trebuchet','Utopia';}
        .cn{font-family:'Microsoft Yahei'}
        .report-page{
            min-height:877px;
            position: relative;
            //background: url('http://m1.files.ivykids.cn/images/sreport/header.gif') no-repeat;
            background-size: 100%;
            background-color: #ffffff;
        }
        .clear{
            clear:both;
        }
        .report-wrapper{width:620px; margin: 0px auto; padding: 0;}
        .logo-top-center{
            width: 160px;
            height: 158px;
            background: url('https://m2.files.ivykids.cn/cloud01-file-8035133FueFp-X_Mzk0IJPxInAZ_vikTFWW.png') no-repeat;
            background-size: 100%;
            position: absolute;
            left: 225px;
            top: 0;
        }
        .top-title{
            width: 620px;
            height: 60px;
            position: absolute;
            top: 160px;
            color: #000;
            font-size: 16px;
            text-align: center;
        }
        .top-small-title{
            width: 260px;
            height: 60px;
            position: absolute;
            top: 38px;
            left: 180px;
            color: #000;
            font-size: 14px;
            text-align: center;
            font-weight: bolder;
        }
        .top-school{
            width: 220px;
            height: 60px;
            position: absolute;
            top: 160px;
            left: 200px;
            font-size: 16px;
            text-align: center;
        }
        .logo-contact-left{
            width: 110px;
            height: 104px;
            background: url('https://m2.files.ivykids.cn/cloud01-file-8035133FueFp-X_Mzk0IJPxInAZ_vikTFWW.png') no-repeat;
            background-size: 100%;
            position: absolute;
            left: 14px;
            top: 5px;
        }
        .page-content{
            margin: 0 auto;position: absolute;top: 125px; width: 640px;
            position: relative;
        }

        .pull-content{
            margin: 0 auto;
            width: 580px;
        }

        .page-first-content{
            margin: 0 60px;position: absolute;top: 90px; width: 500px;
            position: relative;
        }
        .page-first-content .profile-photo{
            position: absolute;
            right: 0;
            top: 170px;
            width: 150px;
        }

        .page-first-content .profile-box{
            padding-top: 150px;
        }

        .page-first-content .profile-box dl{line-height: 25px;}
        .page-first-content .profile-box dt{float: left; width: 90px; clear: left;color: #999;text-align: right}
        .page-first-content .profile-box dt:after{content: ': '}
        .page-first-content .profile-box dd{margin-left: 100px;border-bottom: 1px dotted #efefef; width: 220px;}
        .page-first-content .profile-box dd:before{display: table}
        .page-first-content .profile-box dd:after{clear: both;}
        .teacher-list span.item{display: inline-block; text-align: left;color: #666666;}
        .teacher-list-a span.item{width: 310px;display: block; }
        .teacher-list-b span.item{width: 110px;float: left; height: 25px;}

        .page-content .cover-bottom{
            position: absolute;
            width: 100%;
            top: 395px;
        }

        span.ld{
            color: #000;
            font-size: 110%;
            padding: 1px 3px;
            margin-right: 4px;
            background: #efefef;
        }

        span.ld:after{
        }

        hr.sep{
            margin: 5em 0 3em;
            border: 0;
            height: 1px;
            background: #333;
            background-image: -webkit-linear-gradient(left, #efefef, #ccc, #efefef);
            background-image:    -moz-linear-gradient(left, #efefef, #999, #ccc);
            background-image:     -ms-linear-gradient(left, #efefef, #999, #ccc);
            background-image:      -o-linear-gradient(left, #efefef, #999, #ccc);
        }

        ul.mission li{line-height: 1.5em;}
        /*.web-view .report-page{margin: 1em 0;}*/

        .tac{text-align: center}
        .page-break-after{page-break-after: always;}
        img.middle-size{
            max-width: 360px;
            max-height: 272px;
            margin: 10px auto;
            display: block;
            border: 4px solid #efefef;
        }
        body {
            font-family: Utopia, "Trebuchet MS", Arial, "Microsoft Yahei", "微软雅黑", STXihei, "华文细黑", sans-serif;
            font-size: 10px;
            line-height: 1.42857143;
            color: #333333;
            margin: 0;
            padding: 0;
            background-color: #efefef;
        }

        @media print {
            /**.web-view .report-page {background: none;}**/
            /*.web-view .report-page{margin: 0;}*/
        }
        .navbar-inverse {
            background-color: #333333;
            border-color: #efefef;
        }
        .navbar-inverse form {
            line-height: 50px;
            height: 50px;
            text-align: center;
        }
        .navbar-inverse form input {
            margin-top: 13px;
        }
        .navbar-fixed-top {
            top: 0;
            position: fixed;
            right: 0;
            left: 0;
            z-index: 1030;
            -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
        }
        .navbar-inverse a{
            color: #f2f2f2;
        }

        .table td,
        .table th {
            background-color: #fff !important;
        }
        .table {
            border-collapse: collapse !important;
        }
        .table-bordered th,
        .table-bordered td {
            border: 1px solid #ddd !important;
        }
        table {
            background-color: transparent;
        }
        th {
            text-align: left;
        }
        .table {
            width: 100%;
            max-width: 100%;
            margin-bottom: 17px;
        }
        .table > thead > tr > th,
        .table > tbody > tr > th,
        .table > tfoot > tr > th,
        .table > thead > tr > td,
        .table > tbody > tr > td,
        .table > tfoot > tr > td {
            padding: 8px;
            line-height: 1.42857143;
            vertical-align: top;
            border-top: 1px solid #dddddd;
        }
        .table > thead > tr > th {
            vertical-align: bottom;
            border-bottom: 2px solid #dddddd;
        }
        .table > caption + thead > tr:first-child > th,
        .table > colgroup + thead > tr:first-child > th,
        .table > thead:first-child > tr:first-child > th,
        .table > caption + thead > tr:first-child > td,
        .table > colgroup + thead > tr:first-child > td,
        .table > thead:first-child > tr:first-child > td {
            border-top: 0;
        }
        .table > tbody + tbody {
            border-top: 2px solid #dddddd;
        }
        .table-bordered {
            border: 2px solid #dddddd;
        }
        .table-bordered > thead > tr > th,
        .table-bordered > tbody > tr > th,
        .table-bordered > tfoot > tr > th,
        .table-bordered > thead > tr > td,
        .table-bordered > tbody > tr > td,
        .table-bordered > tfoot > tr > td {
            border: 1px solid #dddddd;
        }
        .table-bordered > thead > tr > th,
        .table-bordered > thead > tr > td {
            border-bottom-width: 2px !important;
            background-color: #eeeeee !important;
        }
        .table-striped > tbody > tr:nth-child(odd) > td,
        .table-striped > tbody > tr:nth-child(odd) > th {
            background-color: #f9f9f9;
        }
        .table-hover > tbody > tr:hover > td,
        .table-hover > tbody > tr:hover > th {
            background-color: #f5f5f5;
        }
        table col[class*="col-"] {
            position: static;
            float: none;
            display: table-column;
        }
        table td[class*="col-"],
        table th[class*="col-"] {
            position: static;
            float: none;
            display: table-cell;
        }
        .table > thead > tr > td.active,
        .table > tbody > tr > td.active,
        .table > tfoot > tr > td.active,
        .table > thead > tr > th.active,
        .table > tbody > tr > th.active,
        .table > tfoot > tr > th.active,
        .table > thead > tr.active > td,
        .table > tbody > tr.active > td,
        .table > tfoot > tr.active > td,
        .table > thead > tr.active > th,
        .table > tbody > tr.active > th,
        .table > tfoot > tr.active > th {
            background-color: #f5f5f5;
        }
    </style>
</body>
</html>
