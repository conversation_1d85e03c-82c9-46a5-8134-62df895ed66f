body
{
	margin: 0;
	padding: 0;
	color: #555;
	font: normal 12px "Microsoft Yahei",Arial,Helvetica,sans-serif;
	background: white;
}

#page
{
	margin: 0;
	background: white;
}

#header
{
	margin: 0;
	padding: 0;
	border-bottom: 2px solid #dedede;
	height: 80px;
	width: 100%;
}

#content
{
    padding: 20px;
}

#sidebar
{
	padding: 20px 20px 20px 0;
}

#footer
{
	padding: 10px;
	margin: 10px 20px;
	font-size: 0.8em;
	text-align: center;
	border-top: 1px solid #C9E0ED;
}

#logo
{
	padding: 10px 20px;
	font-size: 200%;
}

#mainmenu
{
	background:white url(bg.gif) repeat-x left top;
}

#mainmenu ul
{
	padding:6px 20px 5px 20px;
	margin:0px;
}

#mainmenu ul li
{
	display: inline;
}

#mainmenu ul li a
{
	color:#ffffff;
	background-color:transparent;
	font-size:12px;
	font-weight:bold;
	text-decoration:none;
	padding:5px 8px;
}

#mainmenu ul li a:hover, #mainmenu ul li.active a
{
	color: #6399cd;
	background-color:#EFF4FA;
	text-decoration:none;
}

div.flash-error, div.flash-notice, div.flash-success
{
	padding:.8em;
	margin-bottom:1em;
	border:2px solid #ddd;
}

div.flash-error
{
	background:#FBE3E4;
	color:#8a1f11;
	border-color:#FBC2C4;
}

div.flash-notice
{
	background:#FFF6BF;
	color:#514721;
	border-color:#FFD324;
}

div.flash-success
{
	background:#E6EFC2;
	color:#264409;
	border-color:#C6D880;
}

div.flash-error a
{
	color:#8a1f11;
}

div.flash-notice a
{
	color:#514721;
}

div.flash-success a
{
	color:#264409;
}

div.form .rememberMe label
{
	display: inline;
}

div.view
{
	padding: 10px;
	margin: 10px 0;
	border: 1px solid #C9E0ED;
}

div.breadcrumbs
{
	font-size: 0.9em;
	padding: 5px 20px;
}

div.breadcrumbs span
{
	font-weight: bold;
}

div.search-form
{
	padding: 10px;
	margin: 10px 0;
	background: #eee;
}

.portlet
{

}

.portlet-decoration
{
	padding: 3px 8px;
	background: #B7D6E7;
	border-left: 5px solid #6FACCF;
}

.portlet-title
{
	font-size: 12px;
	font-weight: bold;
	padding: 0;
	margin: 0;
	color: #298dcd;
}

.portlet-content
{
	font-size:0.9em;
	margin: 0 0 15px 0;
	padding: 5px 8px;
	background:#EFFDFF;
}

.portlet-content ul
{
	list-style-image:none;
	list-style-position:outside;
	list-style-type:none;
	margin: 0;
	padding: 0;
}

.portlet-content li
{
	padding: 2px 0 4px 0px;
}

.operations
{
	list-style-type: none;
	margin: 0;
	padding: 0;
}

.operations li
{
	padding-bottom: 2px;
}

.operations li a
{
	font: bold 12px Arial;
	color: #0066A4;
	display: block;
	padding: 2px 0 2px 8px;
	line-height: 15px;
	text-decoration: none;
}

.operations li a:visited
{
	color: #0066A4;
}

.operations li a:hover
{
	background: #80CFFF;
}
/*************************CUSTOM STYLE************************/
.clear{clear:both;}
#mainMenu, #subMenu, #branchSelector{
	padding-left: 20px;
}
#mainMenu{
	margin: 8px 0;
}
#subMenu{
	border-bottom: 1px solid #fff8d1;
	background-color: #fff3bb;
	height: 26px;
}
#mainMenu li, #subMenu li{
	float:left;
	margin-right: 1em;
	border-bottom: 4px solid #fff;
	padding: 0 10px;
}
#mainMenu li a, #subMenu li a{
	text-decoration: none;
}
#mainMenu li.active, #mainMenu li:hover{
	border-bottom-color: #f50;
}
#mainMenu li.active a, #mainMenu li:hover a{
	color:#f50;
}
#mainMenu li a{
	line-height: 32px;
	font-size: 16px;
}
#mainMenu li a span{
	display: block;
}
#subMenu li a{
	line-height: 26px;
	margin-right: 1em;
	font-size: 14px;
	color:#000;
}
#branchSelector{
	clear: both;
	height: 50px;
	background:#f2f2f2;
	border-bottom: 3px solid #f27b04;
}
i.sh{
    display: block;
    width: 16px;
    height: 16px;
    cursor: pointer;
    background-image: url(../images/cancel_.png);
}
i.show{
    background-image: url(../images/select_.png);
}
table.normalData{

}
table.normalData th, table.normalData td{
	border-bottom: 1px dotted #dedede;
}

#branch-selector{float: left; position: absolute;}
#branch-selector .selected{padding:8px 30px 8px 12px;color:#fff;font-weight: bold;font-size:16px;display: block; white-space:nowrap;margin-top:4px; background: url(./images/icons/dropdown.png) no-repeat right 16px #333;cursor:pointer;}
#branch-selector .selected:hover{background-position: right -84px; color:#F27B04;}
#branch-selector .list{position: absolute; left: 0; top: 40px; width: 280px;background:#333; padding: 10px; position: absolute;z-index: 999;}
#branch-selector .list em{position: absolute; right:4px; top: 2px;font-style:normal;color:#fff;cursor:pointer;}
#branch-selector .list a{text-decoration: none;color:#ddd;}
#branch-selector .list a{margin-right: 10px;line-height: 20px; display: inline-block; padding: 0 4px; border-radius: 2px; margin-bottom: 6px;}
#branch-selector .list a.current{background:#fff;color:#333;}
#branch-selector .list a:hover{color:#fff;background-color:#F27B04;}

.mulit-box ul li.mulit-item{
    background-color: #333333;
    padding: 10px;
    margin: 10px;
    position: relative;
}
.mulit-box ul li.mulit-item a{
    color: #fff !important;
}
.mulit-box ul li.mulit-item .alert-num{
    position: absolute;
    display: block;
    top: -10px;
    right: 10px;
    width: 24px;
    height: 24px;
    background-color: red;
    line-height: 24px;
    text-align: center;
    color: #fff;
    border-radius: 12px;
    font-weight: bold;
}