<?php $form=$this->beginWidget('CActiveForm', array(
    'id'=>'dept-edit-form',
    'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
)); ?>
    <div class="pop_cont">
        <div class="form-group">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'category'); ?></label>
            <div class="col-xs-9">
                <?php
                echo $form->dropDownList($model, 'category', Branch::branchTypes(), array('empty'=>Yii::t('global','Please Select'), 'class'=>'form-control'));
                ?>
            </div>
        </div>
        <div class="form-group">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'en_name'); ?></label>
            <div class="col-xs-9">
                <?php echo $form->textField($model, 'en_name', array('class'=>'form-control'));?>
            </div>
        </div>
        <div class="form-group">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'cn_name'); ?></label>
            <div class="col-xs-9">
                <?php echo $form->textField($model, 'cn_name', array('class'=>'form-control'));?>
            </div>
        </div>
        <div class="form-group">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'weight'); ?></label>
            <div class="col-xs-9">
                <?php echo $form->textField($model, 'weight', array('class'=>'form-control'));?>
            </div>
        </div>
        <div class="form-group">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'status'); ?></label>
            <div class="col-xs-9">
                <?php echo $form->checkBox($model, 'status', array('value'=>1));?>
            </div>
        </div>
    </div>
    <div class="pop_bottom">
        <button id="J_dialog_close" type="button" class="btn btn-default pull-right"><?php echo Yii::t('global','Cancel');?></button>
        <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
    </div>
<?php $this->endWidget(); ?>