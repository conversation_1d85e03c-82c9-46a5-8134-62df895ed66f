<?php

/**
 * This is the model class for table "ivy_points_order".
 *
 * The followings are the available columns in table 'ivy_points_order':
 * @property integer $id
 * @property string $category
 * @property integer $productid
 * @property integer $childid
 * @property string $schoolid
 * @property integer $created_timestamp
 * @property integer $created_userid
 * @property integer $credits
 * @property integer $status
 * @property integer $pack_id
 * @property string $shipinfo
 * @property integer $update_timestamp
 * @property integer $update_userid
 */
class PointsOrder extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return PointsOrder the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_points_order';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('category, schoolid, quantity, created_timestamp, created_userid, update_timestamp, update_userid', 'required'),
			array('productid, childid, quantity, created_timestamp, created_userid, credits, status, pack_id, update_timestamp, update_userid', 'numerical', 'integerOnly'=>true),
			array('category', 'length', 'max'=>5),
			array('schoolid', 'length', 'max'=>10),
			array('shipinfo', 'length', 'max'=>64),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, category, productid, childid, schoolid, created_timestamp, created_userid, credits, status, pack_id, shipinfo, update_timestamp, update_userid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'Product'=>array(self::BELONGS_TO,'PointsProduct','productid','select'=>'en_title,cn_title,cover','with'=>'Image'),
			'ChildProfile'=>array(self::BELONGS_TO,'ChildProfileBasic','childid'),
            'school'=>array(self::BELONGS_TO, 'Branch', 'schoolid'),
            'orderUser'=>array(self::BELONGS_TO, 'User', 'created_userid', 'with'=>array('profile')),
            'updateUser'=>array(self::BELONGS_TO, 'User', 'update_userid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'category' => '类别',
			'productid' => '产品',
			'childid' => '孩子',
			'schoolid' => '学校',
			'created_timestamp' => '创建时间',
			'created_userid' => '创建人',
			'credits' => '积分',
			'status' => '状态',
			'pack_id' => '包裹',
			'shipinfo' => '物流信息',
			'update_timestamp' => '更新时间',
			'update_userid' => '更新人',
            'quantity' => '订单个数',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('category',$this->category,true);
		$criteria->compare('productid',$this->productid);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('created_timestamp',$this->created_timestamp);
		$criteria->compare('created_userid',$this->created_userid);
		$criteria->compare('credits',$this->credits);
		$criteria->compare('status',$this->status);
		$criteria->compare('pack_id',$this->pack_id);
		$criteria->compare('shipinfo',$this->shipinfo,true);
		$criteria->compare('update_timestamp',$this->update_timestamp);
		$criteria->compare('update_userid',$this->update_userid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
	
	/**
	 * 统计某孩子已经使用的积分
	 * @param int $childid
	 * <AUTHOR>
	 * @copyright ivygroup
	 * @return number
	 */
	public function getUsedCredits($childid)
	{
//		$creditObj = $this->findAllBySql('select sum(credits) as credits from '.$this->tableName().' where childid=:childid', array(':childid'=>$childid));
//		return (!empty($creditObj[0]->credits)) ? $creditObj[0]->credits : 0;
        
        /**
         * 2013/6/19 修改
         * 统计加入数量
         */
        $credit = 0;
        $item = Yii::app()->db->createCommand()
                ->select('sum(credits*quantity) as cq')
                ->from($this->tableName())
                ->where('childid=:childid', array(':childid'=>$childid))
                ->queryRow();
        $credit = $item['cq'];
        return $credit;
	}
	
	
	public function getUsedCreditDetail($childid)
	{
		$criteria = new CDbCriteria;
		$criteria->select = 'credits,productid,created_timestamp';
		$criteria->compare('t.category', 'order');
		$criteria->compare('t.childid', $childid);
		$criteria->order = 't.created_timestamp DESC';
		return $this->with('Product')->findAll($criteria);
	}
	
	/**
	 * 查询某一孩子的订单历史
	 * @param int $childid
	 * @return ArrayObject
	 * <AUTHOR>
	 * @copyright ivygroup
	 */
	public function getOrderList($childid)
	{
		$criteria = new CDbCriteria;
		$criteria->compare('childid', $childid);
		$criteria->order = 'order_timestamp Desc';
		return $this->model()->with('Product')->findAll($criteria);
	}
	
	/**
	 * 订单当前状态
	 * <AUTHOR>
	 * @copyright ivygroup
	 * @return string
	 */
	public function getStatus()
	{
		$statusList = $this->statusList();
		return $statusList[$this->status];
	}
	
	/**
	 * 订单状态列表
	 * @return array
	 * <AUTHOR>	
	 * @copyright ivygroup
	 */
	public function statusList()
	{
		return array(
			PointsStatus::STATS_CREATED=>Yii::t('payment', 'Order submitted'),
			PointsStatus::STATS_CONFIRMED=>Yii::t('payment', 'Order confirmed'),
			PointsStatus::STATS_READYTOSHIP=>Yii::t('payment', 'Preparing to ship'),
			PointsStatus::STATS_SHIPPING=>Yii::t('payment', 'On route to campus'),
			PointsStatus::STATS_RECEIVED=>Yii::t('payment', 'Delivered to campus'),
			PointsStatus::STATS_COMPLETED=>Yii::t('payment', 'Gift delivered'),
		);
	}
	
	/**
	 * 取消订单的参数
	 * @return number
	 */
	public function cancleOrderParam()
	{
		return 24*3600;
	}
	
	public function getBranchList()
	{
		$branch = Branch::model()->getBranchList();
		return $branch[$this->schoolid];
	}
	
	
	/**
	 * 统计产品被订购的次数
	 * @param int $productId
	 * @return number
	 */
	public function getProductOrder($productId)
	{
//		$productObj = $this->findAllBySql('select sum(quantity) as quantity from '.$this->tableName().' where productid=:productid', array(':productid'=>$productId));
//		return (!empty($productObj[0]->quantity)) ? $productObj[0]->quantity : 0;
        
        /**
         * 2013/6/19 修改
         * 统计加入数量
         */
        $number = 0;
        $item = Yii::app()->db->createCommand()
                ->select('sum(quantity) as quantity')
                ->from($this->tableName())
                ->where('productid=:productid', array(':productid'=>$productId))
                ->queryRow();
        $number = $item['quantity'];
        return $number;
	}
	
}
