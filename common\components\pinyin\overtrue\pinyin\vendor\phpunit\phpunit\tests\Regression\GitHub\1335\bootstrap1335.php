<?php
$globalString                          = 'Hello';
$globalIntTruthy                       = 1;
$globalIntFalsey                       = 0;
$globalFloat                           = 1.123;
$globalBoolTrue                        = true;
$globalBoolFalse                       = false;
$globalNull                            = null;
$globalArray                           = array('foo');
$globalNestedArray                     = array(array('foo'));
$globalObject                          = (object) array('foo'=> 'bar');
$globalObjectWithBackSlashString       = (object) array('foo'=> 'back\\slash');
$globalObjectWithDoubleBackSlashString = (object) array('foo'=> 'back\\\\slash');
