<?php

/**
 * This is the model class for table "ivy_survey_report".
 *
 * The followings are the available columns in table 'ivy_survey_report':
 * @property integer $id
 * @property integer $survey_id
 * @property integer $topic_id
 * @property string $schoolid
 * @property integer $votes
 * @property integer $status
 * @property integer $update_user
 * @property integer $update_time
 */
class WSurveyReport extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_survey_report';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('survey_id, topic_id, votes, status, update_user, update_time', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, survey_id, topic_id, schoolid, votes, status, update_user, update_time', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'detail' => array(self::HAS_MANY,'WSurveyReportDetail','report_id'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'survey_id' => 'Survey',
			'topic_id' => 'Topic',
			'schoolid' => 'Schoolid',
			'votes' => 'Votes',
			'status' => 'Status',
			'update_user' => 'Update User',
			'update_time' => 'Update Time',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('survey_id',$this->survey_id);
		$criteria->compare('topic_id',$this->topic_id);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('votes',$this->votes);
		$criteria->compare('status',$this->status);
		$criteria->compare('update_user',$this->update_user);
		$criteria->compare('update_time',$this->update_time);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return WSurveyReport the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public function getStaffReportData($surveyId=0, $branchId=null){
        if($surveyId && $branchId) {
            $reportData = array();
            $crit = new CDbCriteria();
            $crit->compare('survey_id', $surveyId);
            $crit->compare('schoolid', $branchId);
            $reportModels = self::model()->with('detail')->findAll($crit);
            foreach($reportModels as $report){
                foreach($report->detail as $detail) {
                    if (!isset($reportData['detail'][$report->topic_id][$detail->classid][$detail->option_answer])) {
                        $reportData['detail'][$report->topic_id][$detail->classid][$detail->option_answer] = 0;
                    }
                    $reportData['detail'][$report->topic_id][$detail->classid][$detail->option_answer] += $detail->vote;
                    $reportData['total'][$report->topic_id][$detail->option_answer] += $detail->vote;
                    $reportData['classids'][$detail->classid] = $detail->classid;
                }
            }
            return $reportData;
        }

    }
}
