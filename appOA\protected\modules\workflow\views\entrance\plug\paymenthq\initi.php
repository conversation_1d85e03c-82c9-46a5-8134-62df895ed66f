<?php
$optionsHtml = '';
$options2Html = '';
$options = array();
$options2 = array();
$options2All = array();
foreach ($data['categoryList'] as $i => $v) {
    foreach ($v['items'] as $v2) {
        if ($i == 0) {
            $options2Html .= '<option value="' . $v2['code'] . '">' . $v2['title'] . '</option>';
            $options2[] = array(
                'code' => $v2['code'],
                'title' => $v2['title'],
            );
        }
        $options2All[$v['code']][] = array(
            'code' => $v2['code'],
            'title' => $v2['title'],
        );
    }
    $optionsHtml .= '<option value="' . $v['code'] . '">' . $v['title'] . '</option>';
    $options[] = array(
        'code' => $v['code'],
        'title' => $v['title'],
    );
}
?>
<!-- Modal -->
<?php echo CHtml::form('', 'post', array('class' => 'J_ajaxForm')); ?>
<div class="modal fade" id="<?php echo $data['modalNameId'] ?>" tabindex="-1" role="dialog" aria-labelledby="workflowModalLabel" data-backdrop="static">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id=""><?php echo CommonUtils::autoLang($data['workflow']->definationObj->defination_name_cn, $data['workflow']->definationObj->defination_name_en); ?> - <?php echo CommonUtils::autoLang($data['workflow']->nodeObj->node_name_cn, $data['workflow']->nodeObj->node_name_en); ?></h4>
            </div>
            <div class="modal-body" id="workflow-modal-body">
                <?php if ($data['workflow']->useRole != 1) : ?>
                    <div class="justify-content-center">
                        <div class="col-md-8">
                            <!-- 使用Bootstrap的警告框样式 -->
                            <div class="alert alert-danger text-center" role="alert">
                                <h2> 您无提交权限。</h2>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                <div class="row">
                    <!-- 申请标题 -->
                    <div class="col-xs-12">
                        <label class="col-xs-4">付款方 *：</label>
                        <div class="col-xs-4 checkbox">
                            <label>
                                <input <?php if ($data['checked']) echo 'checked'; ?> name="payinfo" type="checkbox"> <?php echo $data['branchObj']->title; ?>
                            </label>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <label class="col-xs-4">申请标题 *：</label>
                        <div class="col-xs-4">
                            <input name="title" class="form-control" type="text" value="<?php echo $data['paymentModel']->title; ?>">
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <br>
                        <label class="col-xs-4">收款方 *：</label>
                        <div class="col-xs-4">
                            <input id="vendor_id" name="vendor_id" value="<?php echo $data['paymentModel']->vendor_id; ?>" hidden>
                            <input id="bank_user" name="bank_user" class="form-control ui-autocomplete-input" value="<?php echo $data['paymentModel']->bank_user; ?>" placeholder="输入关键字搜索供应商" autocomplete="off">
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <br>
                        <label class="col-xs-4">银行账号 *：</label>
                        <div class="col-xs-4">
                            <input id="bank_account" name="bank_account" class="form-control" type="text" value="<?php echo $data['paymentModel']->bank_account; ?>">
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <br>
                        <label class="col-xs-4">开户银行 *：</label>
                        <div class="col-xs-4">
                            <input id="bank_name" name="bank_name" class="form-control" type="text" value="<?php echo $data['paymentModel']->bank_name; ?>">
                        </div>
                    </div>
                    <!-- 选择是否为预算标准 -->
                    <div class="col-xs-12">
                        <br>
                        <label class="col-xs-4">预算标准 *：</label>
                        <div class="col-xs-4 input-group">
                            <div class="col-xs-6">
                                <label>
                                    <input <?php if (!$data['paymentApply']->id > 0 || $data['paymentApply']->budget == 1) echo 'checked'; ?> type="radio" name="budget" value="1">
                                    预算内
                                </label>
                            </div>
                            <div class="col-xs-6">
                                <label>
                                    <input <?php if ($data['paymentApply']->id > 0 && $data['paymentApply']->budget == 0) echo 'checked'; ?> type="radio" name="budget" value="0">
                                    预算外
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <br>
                        <label class="col-xs-4">备注 *：</label>
                        <div class="col-xs-4">
                            <textarea class="form-control" name="memo" rows="3"></textarea>
                        </div>
                    </div>
                    <!-- 关联合同信息 -->
                    <?php if (isset($data['contractModel'])) : ?>
                        <div class="col-xs-12">
                            <br>
                            <label class="col-xs-4">关联合同信息：</label>
                            <div class="col-xs-8" id="constract_link">
                                <?php echo $data['contractLink']; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    <!-- 显示列表 -->
                    <div class="col-xs-12">
                        <br>
                        <table id="list" class="table table-hover">
                            <thead>
                                <tr>
                                    <th class="length_3">付款明细</th>
                                    <th class="length_2">付款金额</th>
                                    <th>分类</th>
                                    <th>二级分类</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($data['paymentItemModel']) : ?>
                                    <?php foreach ($data['paymentItemModel'] as $k => $item) : ?>
                                        <tr>
                                            <td><input class="form-control" name="itemArray[<?php echo $k; ?>][desc]" type="text" value="<?php echo $item->desc; ?>"></td>
                                            <td><input class="form-control" name="itemArray[<?php echo $k; ?>][price]" value="<?php echo $item->price / 100; ?>" onchange="calculatePrice()" type="number" step="0.01"></td>
                                            <td>
                                                <select name="itemArray[<?php echo $k; ?>][category1]" class="form-control" onchange="updateCategoryOptions(this)">
                                                    <?php foreach ($options as $option) : ?>
                                                        <option <?php if ($option['code'] == $item->category1) echo "selected"; ?> value="<?php echo $option['code']; ?>"><?php echo $option['title']; ?></option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </td>
                                            <td>
                                                <select name="itemArray[<?php echo $k; ?>][category2]" class="form-control">
                                                    <?php foreach ($options2All[$item->category1] as $option2) : ?>
                                                        <option <?php if ($option2['code'] == $item->category2) echo "selected"; ?> value="<?php echo $option2['code']; ?>"><?php echo $option2['title']; ?></option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </td>
                                            <td><button class="btn btn-xs btn-danger" type="button" onclick="deleteList(this)">删除</button></td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else : ?>
                                    <tr>
                                        <td><input class="form-control" name="itemArray[0][desc]" type="text"></td>
                                        <td><input class="form-control" name="itemArray[0][price]" value="0" onchange="calculatePrice()" type="number" step="0.01"></td>
                                        <td>
                                            <select name="itemArray[0][category1]" class="form-control" onchange="updateCategoryOptions(this)">
                                                <?php foreach ($options as $option) : ?>
                                                    <option value="<?php echo $option['code']; ?>"><?php echo $option['title']; ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                        </td>
                                        <td>
                                            <select name="itemArray[0][category2]" class="form-control">
                                                <?php foreach ($options2 as $option2) : ?>
                                                    <option value="<?php echo $option2['code']; ?>"><?php echo $option2['title']; ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                        </td>
                                        <td><button class="btn btn-xs btn-danger" type="button" onclick="deleteList(this)">删除</button></td>
                                    </tr>
                                <?php endif; ?>
                                <tr>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td>
                                        <button class="btn btn-primary btn-xs" type="button" onclick="addCustom()">新增</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td>
                                        <p>总计：<span id="total_price"><?php echo $data['paymentModel']->price_total > 0 ? number_format($data['paymentModel']->price_total / 100, 2) : 0; ?></span><input type="hidden" name="total_price" text="<?php echo $data['paymentModel']->price_total > 0 ? $data['paymentModel']->price_total : 0; ?>"></p>
                                    </td>
                                </tr>
                        </table>
                    </div>
                    <div class="col-xs-12">
                        <!-- 显示附件 -->
                        <div id="fileList">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>已上传资料</th>
                                        <th>资料说明</th>
                                        <th>操作</th>
                                    </tr>
                                    <?php foreach ($data['workflow']->getAttachmentList() as $uploadFile) : ?>
                                        <tr>
                                            <td><a target="_blank" href="<?php echo $uploadFile['bigimages']; ?>"><?php echo $uploadFile['notes']; ?></a></td>
                                            <td><input type="text" onchange="changFileName(this,<?php echo $uploadFile['id'] ?>)" class="form-control" placeholder="输入资料说明(XX班级XX申请单)" /></td>
                                            <td>
                                                <a class="btn btn-danger btn-xs" onclick="deleteFile(this,<?php echo $uploadFile['id'] ?>)"><span class="glyphicon glyphicon-trash"> </span></a>
                                            </td>
                                            <input type="hidden" name="files[]" value="<?php echo $uploadFile['id'] ?>">
                                        </tr>
                                    <?php endforeach; ?>
                                </thead>
                            </table>
                        </div>
                        <!-- 上传 -->
                        <div class="btn-group btn-group-justified" role="group">
                            <input id="file" type="file" name="file" multiple="true" style="display:none">
                            <div class="btn-group" role="group">
                                <a id="filebtn" class="btn btn-default" onclick="$('#file').click();">上传新资料</a>
                            </div>
                        </div>
                        <br>
                        <span class="text-warning" id="spaninfo"></span>
                    </div>

                </div>
            </div>
            <div class="retshow">
                <h3><?php echo Yii::t('workflow', 'Comments:'); ?></h3>
                <?php
                $processList = $data['workflow']->getWorkflowProcess();
                $processLabelList = array(
                    WorkflowOperation::WORKFLOW_STATS_STEP => '退回修改',
                    WorkflowOperation::WORKFLOW_STATS_RESET => '退回修改',
                    WorkflowOperation::WORKFLOW_STATS_UNOP =>  '通过',
                    WorkflowOperation::WORKFLOW_STATS_OPDENY =>  '拒绝并作废',
                    WorkflowOperation::WORKFLOW_STATS_OPED =>  '通过',
                );
                foreach ($processList as $process) : $processLabel = $processLabelList[$process->state];
                    if ($data['workflow']->nodeIds[0] == $process->current_node_index) {
                        $processLabel = '提交';
                    } ?>
                    <h4>
                        <em><?php echo Yii::t('workflow', 'Date submitted:'); ?> <?php echo date("Y-m-d H:i:s", $process->start_time); ?></em>
                        <span><?php echo $process->user->getName(); ?></span>
                    </h4>
                    <div class="clearfix"></div>
                    <div class="comment">
                        <label><?php echo $processLabel . ': '; ?></label>
                        <p><?php echo nl2br(htmlspecialchars($process->process_desc)); ?></p>
                    </div>
                <?php endforeach; ?>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
                <button type="button" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
            </div>
        </div>
    </div>
</div>
<?php CHtml::endForm(); ?>
<script>
    const definationId = <?php echo $data['workflow']->definationObj->defination_id; ?>;
    const operationId = '<?php echo $data['workflow']->getOperateId(); ?>';
    const nodeId = '<?php echo $data['workflow']->nodeObj->node_id; ?>';
    const branchId = '<?php echo $data['workflow']->schoolId; ?>';
    const action = 'search';

    const categoryList = <?php echo json_encode($data['categoryList']); ?>;
    // 更新分类选项
    function updateCategoryOptions(selectElement) {
        const category2Select = selectElement.parentElement.nextElementSibling.querySelector("select");
        const selectedProvince = selectElement.value;

        // 清空分类选项
        category2Select.innerHTML = "";

        categoryList.forEach(category1 => {
            if (category1['code'] == selectedProvince) {
                category1['items'].forEach(item => {
                    const option = document.createElement("option");
                    option.value = item['code'];
                    option.textContent = item['title'];
                    category2Select.appendChild(option);
                })
            }
        });
    }

    $('#date').datepicker({
        'dateFormat': 'yy-mm-dd'
    });
    var i = <?php echo count($data['paymentItemModel']) + 1 ?>;

    //添加付款明细
    function addCustom(btn) {
        var con = '<tr><td><input class="form-control" name="itemArray[' + i + '][desc]" type="text" class="length_3"/></td>';
        con += '<td><input class="form-control" name="itemArray[' + i + '][price]" value="0" onchange="calculatePrice()" type="number" step="0.01" class="length_1" /></td>';
        con += '<td><select name="itemArray[' + i + '][category1]" class="form-control" onchange="updateCategoryOptions(this)"> ' + '<?php echo $optionsHtml; ?>' + ' </select><td><select name="itemArray[' + i + '][category2]" class="form-control">' + '<?php echo $options2Html; ?>' + '</select> </td>';
        con += '<td><button class="btn btn-xs btn-danger" type="button" onclick="deleteList(this)">删除</button></td>';
        i++;
        $('#list tbody').prepend(con);
        calculatePrice();
    }

    //删除显示列表项
    function deleteList(btn) {
        $(btn).parent().parent().remove();
        calculatePrice();
    }

    //计算价格
    function calculatePrice() {
        var total_price = 0;
        $('#list tbody tr').each(function() {
            var price = $(this).find('input').eq(1).val();
            if (price != undefined) {
                total_price += parseFloat(price);
            }
        });

        $('#total_price').text(total_price.formatMoney());
        $('#total_price').next().val(total_price);
    }
    //转换货币格式
    Number.prototype.formatMoney = function(places, symbol, thousand, decimal) {
        places = !isNaN(places = Math.abs(places)) ? places : 2;
        symbol = symbol !== undefined ? symbol : "¥ ";
        thousand = thousand || ",";
        decimal = decimal || ".";
        var number = this,
            negative = number < 0 ? "-" : "",
            i = parseInt(number = Math.abs(+number || 0).toFixed(places), 10) + "",
            j = (j = i.length) > 3 ? j % 3 : 0;
        return symbol + negative + (j ? i.substr(0, j) + thousand : "") + i.substr(j).replace(/(\d{3})(?=\d)/g, "$1" + thousand) + (places ? decimal + Math.abs(number - i).toFixed(places).slice(2) : "");
    };

    //上传文件
    $(function() {
        head.Util.ajax($('#constract_link'));
        const searchUrl = '<?php echo Yii::app()->createUrl("/workflow/index/searchVendor", array('branchId' => $data['workflow']->schoolId)); ?>';

        $("#bank_user").autocomplete({
            source: searchUrl,
            // minLength: 2,
            select: function(event, ui) {
                if (ui.item.value <= 0) {
                    return false;
                }
                $("#vendor_id").val(ui.item.vendor_id);
                $("#bank_user").val(ui.item.bank_user);
                $("#bank_name").val(ui.item.bank_name);
                $("#bank_account").val(ui.item.bank_account);
            },
        });

        $('#file').change(function() {
            $('#spaninfo').text("");
            var action = 'saveUploadFile';
            var url = '<?php echo Yii::app()->createUrl("/workflow/entrance/index") . "?definationId=' +definationId+ '&nodeId=' +nodeId+ '&branchId=' +branchId+ '&operationId=' +operationId+ '&action=' +action+ '"; ?>';
            // 循环 this.files
            var _this = this
            for (var i = 0; i < this.files.length; i++) {
                var formData = new FormData();
                var _file = this.files[i]
                var fileName = _file.name
                formData.append('file', _file);

                $.ajax({
                    url: url,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(data) {
                        var msg = eval('(' + data + ')');
                        if (msg.state == 'success') {
                            var html = '<tr><td><a target="_blank" href="' + msg.data.url + '">' + msg.data.fileName + '</a></td>';
                            html += '<td><input type="text" onchange=changFileName(this,' + msg.data.fileId + ') class="form-control" placeholder="输入资料说明(XX班级XX申请单)"></td>'
                            html += '<td><a class="btn btn-danger btn-xs" onclick="deleteFile(this,' + msg.data.fileId + ')"><span class="glyphicon glyphicon-trash"> </span></a></td></tr>';
                            html += '<input type="hidden" name="files[]" value=' + msg.data.fileId + ' />';
                            $('#fileList > table').append(html);
                            _this.value = '';
                        } else {
                            var oldText = $('#spaninfo').text();
                            $('#spaninfo').text(msg.message + oldText);
                        }
                    }
                });
            }
        });
    });
    //删除文件
    function deleteFile(btn, id) {
        if (confirm('确定要删除这个文件吗?')) {
            var action = 'deleteUploadFile';
            var url = '<?php echo Yii::app()->createUrl("/workflow/entrance/index") . "?definationId=' +definationId+ '&nodeId=' +nodeId+ '&branchId=' +branchId+ '&operationId=' +operationId+ '&action=' +action+ '"; ?>';

            $.ajax({
                url: url,
                type: 'POST',
                data: {
                    id: id
                },
                success: function(data) {
                    var msg = eval('(' + data + ')');
                    if (msg.state == 'success') {
                        $(btn).parent().parent().remove();
                    };
                    $('#spaninfo').text(msg.message);
                }
            });
        };
    }
    //修改文件名
    function changFileName(btn, id) {
        var notes = $(btn).val();
        if (notes == "") {
            return false
        };
        var action = 'changeFileName';
        var url = '<?php echo Yii::app()->createUrl("/workflow/entrance/index") . "?definationId=' +definationId+ '&nodeId=' +nodeId+ '&branchId=' +branchId+ '&operationId=' +operationId+ '&action=' +action+ '"; ?>';

        $.ajax({
            url: url,
            type: 'POST',
            data: {
                id: id,
                notes: notes
            },
            success: function(data) {
                var msg = eval('(' + data + ')');
                if (msg.state == 'success') {
                    var con = $(btn).parent().prev().children().text(notes);
                }
                $('#spaninfo').text(msg.message);
            }
        });
    }
</script>