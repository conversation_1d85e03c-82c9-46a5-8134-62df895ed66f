<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<title><?php echo CHtml::encode($this->pageTitle);?></title>
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
	<link href="<?php echo Yii::app()->theme->baseUrl; ?>/css/weui.min.css?v<?php echo Yii::app()->params['refreshAssets'];?>" type="text/css" rel="stylesheet" media="screen">
	<link href="<?php echo Yii::app()->theme->baseUrl; ?>/css/style.css?v<?php echo Yii::app()->params['refreshAssets'];?>" type="text/css" rel="stylesheet" media="screen">
	<link href="<?php echo Yii::app()->theme->baseUrl; ?>/css/<?php echo Yii::app()->language;?>.css?v<?php echo Yii::app()->params['refreshAssets'];?>" type="text/css" rel="stylesheet" media="screen">
    <script>
        var _hmt = _hmt || [];
        _hmt.push(['_setCustomVar', 1, 'app', 'wechat']);
        _hmt.push(['_setCustomVar', 2, 'campus', '<?php echo $this->campusId;?>']);
        _hmt.push(['_setCustomVar', 3, 'router', '<?php echo $this->campusId.'|'.$this->id.'|'.$this->action->id;?>']);
        (function() {
            var hm = document.createElement("script");
            hm.src = "https://hm.baidu.com/hm.js?6f3075fb3e7c4d750ce828023a439e7b";
            var s = document.getElementsByTagName("script")[0];
            s.parentNode.insertBefore(hm, s);
        })();
    </script>
</head>
<body ontouchstart>
	<script src="<?php echo Yii::app()->theme->baseUrl;?>/js/zepto.min.js"></script>
	<script src="<?php echo Yii::app()->theme->baseUrl;?>/js/jweixin-1.0.0.js"></script>
	<script>
		wx.config({
			debug: false,
			appId: '<?php echo $this->signPackage["appId"];?>',
			timestamp: '<?php echo $this->signPackage["timestamp"];?>',
			nonceStr: '<?php echo $this->signPackage["nonceStr"];?>',
			signature: '<?php echo $this->signPackage["signature"];?>',
			jsApiList: [
				'hideOptionMenu',
				'showOptionMenu',
				'hideMenuItems',
				'showMenuItems',
				'chooseWXPay',
			]
		});
	</script>
	<div class="container">
		<?php echo $content; ?>
	</div>
	<!-- loading toast -->
	<div id="loadingToast" class="weui_loading_toast" style="display:none;">
	    <div class="weui_mask_transparent"></div>
	    <div class="weui_toast">
	        <div class="weui_loading">
	            <div class="weui_loading_leaf weui_loading_leaf_0"></div>
	            <div class="weui_loading_leaf weui_loading_leaf_1"></div>
	            <div class="weui_loading_leaf weui_loading_leaf_2"></div>
	            <div class="weui_loading_leaf weui_loading_leaf_3"></div>
	            <div class="weui_loading_leaf weui_loading_leaf_4"></div>
	            <div class="weui_loading_leaf weui_loading_leaf_5"></div>
	            <div class="weui_loading_leaf weui_loading_leaf_6"></div>
	            <div class="weui_loading_leaf weui_loading_leaf_7"></div>
	            <div class="weui_loading_leaf weui_loading_leaf_8"></div>
	            <div class="weui_loading_leaf weui_loading_leaf_9"></div>
	            <div class="weui_loading_leaf weui_loading_leaf_10"></div>
	            <div class="weui_loading_leaf weui_loading_leaf_11"></div>
	        </div>
	        <p class="weui_toast_content"><?php echo Yii::t('global', 'Saving'); ?>...</p>
	    </div>
	</div>
	<!--END toast-->

	<!--BEGIN toast-->
	<div id="toast" style="display: none;">
	    <div class="weui_mask_transparent"></div>
	    <div class="weui_toast">
	        <i class="weui_icon_toast"></i>
	        <p class="weui_toast_content"><?php echo Yii::t('wechat', 'Completed'); ?></p>
	    </div>
	</div>
	<!--END toast-->

	<!--BEGIN dialog2-->
	<div class="weui_dialog_alert" id="dialog2" style="display: none;">
	    <div class="weui_mask"></div>
	    <div class="weui_dialog">
	        <div class="weui_dialog_hd"><strong class="weui_dialog_title"></strong></div>
	        <div class="weui_dialog_bd"></div>
	        <div class="weui_dialog_ft">
	            <a href="javascript:" onclick="$('#dialog2').hide()" class="weui_btn_dialog primary"><?php echo Yii::t('wechat', 'OK'); ?></a>
	        </div>
	    </div>
	</div>
	<!--END dialog2-->
	<script src="<?php echo Yii::app()->theme->baseUrl;?>/js/js.js?v1<?php echo Yii::app()->params['refreshAssets'];?>"></script>
</body>
</html>