html {margin:0;padding:0;border:0;}
li, ul, li, ol {margin:0;padding: 0}
ul {list-style-type:none;}
ol {list-style-type:none;}
li {list-style-type:none;}
#schedule{/*width: 900px;*/}
.sh{font-size: 1.6em;font-weight: bold;color: #009193;font-family: Georgia,serif;margin: 0 0px 10px 0px;border-bottom: 1px solid #F2F2F2;height: 40px;line-height: 40px;}
#schedule #sLeft{width: 60px; float: left;}
#schedule #sRight{/*width: 800px;*/ float: left;}
#schedule #sLeft .tb{height: 20px; position: relative;}
#schedule #sLeft .tb .tbt{z-index: 999;text-align:center;text-shadow:2px 1px 1px rgba(255, 255, 255, 0.6);color:#999;}
.bg1{background-color: 	#CDBFAC;}
.bg2{background-color: #E1D4C0;}
.hli{text-align: center;}
.thx{width: 180px; margin-left: 1px;}
.sT{overflow-y:auto;}
.sT-tit{font-weight: bold;text-align: center;padding: 5px}
.sT-txt{padding: 0 5px;}
.tbend{height: 0 !important; position: /*absolute !important;*/ bottom: 13px;}
.hul{position: relative;}
.hli{height: 25px;line-height: 25px;background-color: #FF7D0D; font-weight: bold;color: #FFF;font-size: 14px;}
.fr{float: right;}
.clear {clear:both;height: 0;border: 0;font-size: 0;line-height: 0;overflow: hidden;padding: 0;margin: 0;}
.bg-ffffff{background:url(bg_ffffff.png) repeat;}
.bg-f0f0f0{background:url(bg_f0f0f0.png) repeat;}
.bg-cccccc{background:url(bg_cccccc.png) repeat;}
.bg-ffdbdb{background:url(bg_ffdbdb.png) repeat;}
.bg-ffffdb{background:url(bg_ffffdb.png) repeat;}
.bg-d5ffc6{background:url(bg_d5ffc6.png) repeat;}
.bg-dbf0ff{background:url(bg_dbf0ff.png) repeat;}
.bg-d2d6ff{background:url(bg_d2d6ff.png) repeat;}
.sbtn{
    float: left;
    display: block;
    width: 16px;
    height: 16px;
    margin-right: 4px;
    background: url('../../images/schedule_btn.png') no-repeat left top transparent;
    text-indent: -9999px;
    overflow: hidden;
}
.s_d_copy{background-position: 0 0}
.s_d_clear{background-position: -32px 0}
.s_d_paste{background-position: -16px 0}
.s_i_edit{background-position: 0 -16px}
.s_i_delete{background-position: -16px -16px}
.s_i_prepend{background-position: -32px -16px}
.s_i_append{background-position: -48px -16px}
#add-sde{
    height: 119px;
    background: url("plus.png") no-repeat scroll center center, url("noise.png") repeat scroll 0 0 rgba(242, 242, 242, 0.49);
    border: 1px dashed #D8D8D8;
    display: block;
}
.timeslot{
    position: relative;
    overflow-y: auto;
    opacity: 0.8;
}
.timeslot:hover h3{
    color: #FF7D0D;
}
.timeslot:hover{
    background-color: #fafafa !important;
    opacity: 1;
}
.timeslot .toolbar{
    position: absolute;
    background:#efefef;
    top: 0;
    right: 0;
    visibility: hidden;
    padding: 2px 4px;
    border-left: 1px solid #dedede;
    border-bottom: 1px solid #ddd;
}
.timeslot:hover .toolbar{
    visibility: visible;
}
.timeslot .text{
    padding: 6px 2px;
}
.daytitle{
    position: relative;
}
.daytitle .toolbar{
    position: absolute;
    bottom: 4px !important;
    right: 0 !important;
    visibility:hidden;
}
.daytitle:hover .toolbar{
    visibility: visible;
}
.daydata{
    min-height: 800px;
}
.fl{
    float: left !important;
    display: inline;
}

//*********************************

@charset 'UTF-8';
/* Starter CSS for Menu */
#cssmenu {
  padding: 0;
  margin: 0;
  border: 0;
}
#cssmenu ul,
#cssmenu li {
  list-style: none;
  margin: 0;
  padding: 0;
}
#cssmenu ul {
  position: relative;
  z-index: 1;
}
#cssmenu ul li {
  float: left;
  min-height: 1px;
  vertical-align: middle;
}
#cssmenu ul li.hover,
#cssmenu ul li:hover {
  position: relative;
  z-index: 599;
  cursor: default;
}
#cssmenu ul ul {
  visibility: hidden;
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 598;
  width: 100%;
}
#cssmenu ul ul li {
  float: none;
}
#cssmenu ul ul ul {
  top: 0;
  left: 100%;
}
#cssmenu ul li:hover > ul {
  visibility: visible;
}
#cssmenu ul ul {
  margin-top: 0;
}
#cssmenu a {
  display: block;
  line-height: 1em;
  text-decoration: none;
}
#cssmenu ul li.last ul {
  left: auto;
  right: 0;
}
#cssmenu ul li.last ul ul {
  left: auto;
  right: 99.5%;
}
#cssmenu:after,
#cssmenu ul:after {
  content: '';
  display: block;
  clear: both;
}
/* Custom CSS Styles */
#cssmenu {
  width: auto;
  margin-bottom: 15px;
}
#cssmenu:before {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAAICAIAAAC3eAIWAAAAA3NCSVQICAjb4U/gAAAAI0lEQVQImWPwj0hh+v//PxPD//9M////Z/rP8J/p//9/MD4AGUETB+SFfCsAAAAASUVORK5CYII=);
  background-color: #606a77;
  background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0, #4f5864), color-stop(1, #49515b));
  background-image: -webkit-linear-gradient(top, #4f5864, #49515b);
  background-image: -moz-linear-gradient(top, #4f5864, #49515b);
  background-image: -o-linear-gradient(top, #4f5864, #49515b);
  background-image: linear-gradient(#4f5864, #49515b);
  -moz-box-shadow: inset 0 2px 0 #586270, inset 0 1px 0 #6b7888;
  -webkit-box-shadow: inset 0 2px 0 #586270, inset 0 1px 0 #6b7888;
  box-shadow: inset 0 2px 0 #586270, inset 0 1px 0 #6b7888;
  content: '';
  display: block;
  height: 8px;
}
#cssmenu > ul {
  border-bottom: 1px solid #252A30;
  border-top: 1px solid #252A30;
  -moz-box-shadow: inset 0 1px 0 #8799a9, 0 1px 1px rgba(0, 0, 0, 0.5);
  -webkit-box-shadow: inset 0 1px 0 #8799a9, 0 1px 1px rgba(0, 0, 0, 0.5);
  box-shadow: inset 0 1px 0 #8799a9, 0 1px 1px rgba(0, 0, 0, 0.5);
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAABNCAIAAADo7ZnJAAAAA3NCSVQICAjb4U/gAAAAUUlEQVQYlXWPyRGAMAwDd1wwHVADJS+POERk4OVD1mGO8yq1wFIKLXHsJLDGH8wSou8q0bfGxplYcpaHRerG/J/zS/edLTnrjvDo7PHv1Nhy3lZMnHg0MO2JAAAAAElFTkSuQmCC);
  background-color: #566171;
  background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0, #6e7d8f), color-stop(1, #404854));
  background-image: -webkit-linear-gradient(top, #6e7d8f, #404854);
  background-image: -moz-linear-gradient(top, #6e7d8f, #404854);
  background-image: -o-linear-gradient(top, #6e7d8f, #404854);
  background-image: linear-gradient(#6e7d8f, #404854);
  height: 27px;
  padding: 10px 5px 10px 5px;
}
#cssmenu > ul > li {
  margin: 0 10px;
}
#cssmenu > ul > li.has-sub:hover > a {
  -moz-border-radius: 3px 3px 0 0;
  -webkit-border-radius: 3px 3px 0 0;
  border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}
#cssmenu > ul > li:hover > a {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAAbCAIAAAAyOnIjAAAAA3NCSVQICAjb4U/gAAAAGElEQVQImWP4//8/079//0jGf//+JVUPAADfUJPhbDTaAAAAAElFTkSuQmCC);
  background-color: #e2e2e2;
  background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0, #ffffff), color-stop(1, #c8c8c8));
  background-image: -webkit-linear-gradient(top, #ffffff, #c8c8c8);
  background-image: -moz-linear-gradient(top, #ffffff, #c8c8c8);
  background-image: -o-linear-gradient(top, #ffffff, #c8c8c8);
  background-image: linear-gradient(#ffffff, #c8c8c8);
}
#cssmenu > ul > li.active:hover > a {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAAbCAIAAAAyOnIjAAAAA3NCSVQICAjb4U/gAAAAJklEQVQImWP4MruP6d+/f0z//v5Fo/8x/fv3F41GyP8lUf2/v38BoDRPnb8AZS4AAAAASUVORK5CYII=);
  background-color: #cb7b72;
  background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0, #f49b8e), color-stop(1, #bd584d));
  background-image: -webkit-linear-gradient(top, #f49b8e, #bd584d);
  background-image: -moz-linear-gradient(top, #f49b8e, #bd584d);
  background-image: -o-linear-gradient(top, #f49b8e, #bd584d);
  background-image: linear-gradient(#f49b8e, #bd584d);
}
#cssmenu ul a {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAAbCAIAAAAyOnIjAAAAA3NCSVQICAjb4U/gAAAAIUlEQVQImWP4+PEj09+/f5n+/fvH9PfvXzhG5uNik6gOAOTaUDaAXrIOAAAAAElFTkSuQmCC);
  background-color: #c2c2c2;
  background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0, #f1f1f1), color-stop(1, #a8a8a8));
  background-image: -webkit-linear-gradient(top, #f1f1f1, #a8a8a8);
  background-image: -moz-linear-gradient(top, #f1f1f1, #a8a8a8);
  background-image: -o-linear-gradient(top, #f1f1f1, #a8a8a8);
  background-image: linear-gradient(#f1f1f1, #a8a8a8);
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
  border-radius: 3px;
  -moz-background-clip: padding;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  -moz-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 1px 1px 1px rgba(0, 0, 0, 0.5);
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 1px 1px 1px rgba(0, 0, 0, 0.5);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 1px 1px 1px rgba(0, 0, 0, 0.5);
  color: #3c444d;
  font-size: 12px;
  line-height: 27px;
  padding: 0 20px;
  position: relative;
  text-align: center;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.4);
}
#cssmenu ul ul {
  width: 170px;
}
#cssmenu ul ul a {
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  border-radius: 0;
  -moz-background-clip: padding;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  line-height: 150%;
}
#cssmenu ul .active > a {
  color: #FFF;
  text-shadow: 0 1px 0 rgba(0, 0, 0, 0.4);
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAAbCAIAAAAyOnIjAAAAA3NCSVQICAjb4U/gAAAANUlEQVQImXXMsQ0AIRTD0FMmvRlYnAm+TQEIGronxcrX2x80hUEDpNx2em0lx9wNj37+rX4AhN5PdtvsqRUAAAAASUVORK5CYII=);
  background-color: #c46a60;
  background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0, #ef7260), color-stop(1, #b04c41));
  background-image: -webkit-linear-gradient(top, #ef7260, #b04c41);
  background-image: -moz-linear-gradient(top, #ef7260, #b04c41);
  background-image: -o-linear-gradient(top, #ef7260, #b04c41);
  background-image: linear-gradient(#ef7260, #b04c41);
}
#cssmenu ul .has-sub {
  position: relative;
}
#cssmenu ul .has-sub ul {
  -moz-border-radius: 0 3px 3px 3px;
  -webkit-border-radius: 0 3px 3px 3px;
  border-radius: 0 3px 3px 3px;
  -moz-background-clip: padding;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  -moz-box-shadow: 0 2px 1px 1px rgba(0, 0, 0, 0.5);
  -webkit-box-shadow: 0 2px 1px 1px rgba(0, 0, 0, 0.5);
  box-shadow: 0 2px 1px 1px rgba(0, 0, 0, 0.5);
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAA2CAMAAAAxtAOuAAAAolBMVEXp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enp6enCAApGAAAANXRSTlP9+vf08Ozp5eDc19POycS+ubOuqKOdl5GLhYB6dG5oYlxXUUxGQTs2MSwoIx8aFhMPCwgFAqv7N0MAAABMSURBVHheBcCDEcMAAADAj1Hbtr3/aj0/BEKRWCKVyRVKFVU1dQ1NLW0dXT19A0MjYxNTM3MLSytrG1s7ewdHJ2cXVzd3D08vbx/fP9L5BZigzasGAAAAAElFTkSuQmCC) repeat-x;
  background-color: #c3c3c3;
  background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0, #e9e9e9), color-stop(1, #aaaaaa));
  background-image: -webkit-linear-gradient(top, #e9e9e9, #aaaaaa);
  background-image: -moz-linear-gradient(top, #e9e9e9, #aaaaaa);
  background-image: -o-linear-gradient(top, #e9e9e9, #aaaaaa);
  background-image: linear-gradient(#e9e9e9, #aaaaaa);
  padding: 3px 0;
}
#cssmenu ul .has-sub ul a {
  background: none;
  padding: 8px 8px 8px 16px;
  border-bottom: 1px solid transparent;
  text-align: left;
}
#cssmenu ul .has-sub ul .has-sub a:after {
  content: none;
}
#cssmenu ul .has-sub li:hover > a {
  border-bottom: 1px solid #1D2024;
  color: #FFF;
  background-color: #55616f;
  background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0, #56606f), color-stop(1, #3f4852));
  background-image: -webkit-linear-gradient(top, #56606f, #3f4852);
  background-image: -moz-linear-gradient(top, #56606f, #3f4852);
  background-image: -o-linear-gradient(top, #56606f, #3f4852);
  background-image: linear-gradient(#56606f, #3f4852);
  -moz-box-shadow: inset 1px 2px 0 #5c6778, inset 0 1px 0 #4e5866;
  -webkit-box-shadow: inset 1px 2px 0 #5c6778, inset 0 1px 0 #4e5866;
  box-shadow: inset 1px 2px 0 #5c6778, inset 0 1px 0 #4e5866;
  position: relative;
  text-shadow: 0 1px 0 rgba(0, 0, 0, 0.4);
}
#cssmenu ul .has-sub li:hover > a:after {
  border-left: 0 none;
  background-color: #c35f54;
  background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0, #ea5f51), color-stop(1, #a9463b));
  background-image: -webkit-linear-gradient(top, #ea5f51, #a9463b);
  background-image: -moz-linear-gradient(top, #ea5f51, #a9463b);
  background-image: -o-linear-gradient(top, #ea5f51, #a9463b);
  background-image: linear-gradient(#ea5f51, #a9463b);
  -moz-box-shadow: inset -1px 2px 0 rgba(255, 255, 255, 0.2), inset 0 1px 0 #ce5448;
  -webkit-box-shadow: inset -1px 2px 0 rgba(255, 255, 255, 0.2), inset 0 1px 0 #ce5448;
  box-shadow: inset -1px 2px 0 rgba(255, 255, 255, 0.2), inset 0 1px 0 #ce5448;
  content: '';
  height: 100%;
  width: 6px;
  position: absolute;
  right: 0;
  top: 0;
}
#cssmenu ul .has-sub > a {
  padding-right: 0;
}
#cssmenu ul .has-sub > a:after {
  content: '▼';
  border-left: 1px solid rgba(100, 100, 100, 0.2);
  color: #5D6A7A;
  -moz-box-shadow: -1px 0 0 rgba(255, 255, 255, 0.2);
  -webkit-box-shadow: -1px 0 0 rgba(255, 255, 255, 0.2);
  box-shadow: -1px 0 0 rgba(255, 255, 255, 0.2);
  display: inline-block;
  font-size: 9px;
  margin-left: 5px;
  text-align: center;
  height: 25px;
  width: 24px;
  text-shadow: 0 -1px 0 #101417;
}
#cssmenu ul .active > a:after {
  color: #FFF;
}
#cssmenu ul ul a {
  font-size: 12px;
}
li.save-btn{position:relative}
li.save-btn em{position: absolute; width:16px;height:16px;display:block;background:url(../../images/schedule_btn.png) no-repeat 999px 999px transparent;right:0; top:-10px;text-indent:-9999px;overflow:hidden;}
li.save-btn em.pending{background-position:-48px 0}
li.save-btn em.saved{background-position:-64px 0}
h3{
    font-size: 14px;
}