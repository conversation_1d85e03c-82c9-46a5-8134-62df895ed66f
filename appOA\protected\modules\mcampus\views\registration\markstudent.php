<style>
	.speed{
		display: inline-block;
		width:10px;
		height:10px;
		border-radius:50%
	}
	/*.orangeText{
		background: orange;
		display: inline-block;
	    width: 15px;
	    height: 15px;
	    border-radius: 50%;
	    text-align: center;
	    line-height: 15px;
	    color: #fff;
	}*/
	.orange{
		background: orange;
	}
	.gray{
		background:#ccc;
	}
	.blue{
		background:#428BCA;
	}
	.green{
		background:green;
	}
	.red{
		background:red;
	}
	.table tr td{
        vertical-align:middle !important;
    }
    .checkbox-custom {
        position: relative;
        margin-top: 0;
        display: inline-block;
    }
    .checkbox-custom input[type="checkbox"] {
        opacity: 0;/*将初始的checkbox隐藏起来*/
        position: absolute;
        cursor: pointer;
        z-index: 2;
        margin: -6px 0 0 0;
        top: 50%;
        left: 3px;
    }
    .checkbox-custom label:before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        margin-top: -9px;
        width:16px;
        height:16px;
        display: inline-block;
        border-radius: 2px;
        border: 1px solid #bbb;
        background: #fff;
    }
    .checkbox-custom input[type="checkbox"]:checked +label:after {
        position: absolute;
        display: inline-block;
        font-family: 'Glyphicons Halflings';
        content: "\e013";
        top:34%;
        left:0px;
        margin-top: -5px;
        font-size: 11px;
        line-height: 1;
        color: #333;
        width: 16px;
        height:16px;
        padding-top: 1px;
        padding-left: 2px;
        border-radius: 2px;
    }
    .mb0{
        margin-bottom: 0 !important
    }
    .bodbotnone{
        border-top: none;
        padding: 0;
        padding-bottom: 15px
    }
    .addchild tr td,.childname tr td{
        word-wrap:break-word;word-break:break-all
    }
    .mt5{
        margin-bottom: 0
    }
    .imgbig{
        width:2rem;
        height:2rem;
    }
    .sendlist{
        position: relative;
    }
    .textsend{
        position: absolute;
        right:10px;
        top: 50%;
        margin-top: -8px;
    }
    .pr0{
        padding-right: 0 !important
    }
    [v-cloak] {
		display: none;
	}
	.break{
		word-break:break-all
	}
</style>
<div class="container-fluid">
	<ol class="breadcrumb">
	    <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
	    <li><?php echo CHtml::link(Yii::t('site', '新生注册'), array('//mcampus/registration/index')) ?></li>
		<li class="active"><?php echo Yii::t('site','标记学生') ?></li>
	</ol>
	<div class="row">
		<div class="col-md-2 col-sm-2">
			<ul class="nav nav-pills nav-stacked text-left background-gray" id="pageCategory">
                <?php foreach ($this->getSubMenu() as $val){ ?>
                    <li class="<?php echo ($this->getAction()->getId() == $val['url']) ? "active " : ""; ?>" ><a href="<?php echo $this->createUrlReg($val['url']) ?>"><?php echo $val['label'] ?></a></li>
                <?php } ?>
            </ul>
		</div>
		<div class="col-md-10 col-sm-10" id='mark' v-cloak>
			<div class="row">
				<div class="col-md-2">
					<div class="btn-group mb15 wid"><button type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" class="btn btn-default dropdown-toggle  wid">{{startYearList[yid]}} <span class="caret"></span></button>
						<ul class="dropdown-menu wid">
							<li v-for='(data,index) in YearList'>
								<a :href="'<?php echo $this->createUrl('/mcampus/registration/markstudent', array('branchId' => $this->branchId)); ?>&yid='+data.id+''">{{data.title}}</a>
							</li>
						</ul>
					</div>
				</div>
			</div>
			<form id='students'>
			<div class="row mb15">
				<div class="form-inline col-md-12">
					<div class="form-inline">
					<label>开始时间：</label>
					<input type="text" class="form-control form-group" id="startTime" placeholder="请选择开始日期" name="startTime"  :value='startdate'>
					<label> 结束时间：</label>
					<div class="form-group">
						<input type="text" class="form-control form-group" id="endTime" placeholder="请选择结束日期" name='startEnd' :value='enddate'>
					</div>
				</div>
				</div>
			</div>
			<p>只有标记过的新生才会进入注册流程</p>
			<div class="mb15">
				<label class="mr15" v-for='(list,index) in studentConfig'><span class="speed" :class='color[index]'> </span> {{list}}</label>
                <div class="bs-example pull-right" data-example-id="single-button-dropdown">
                    <div class="btn-group">
                        <a href="javascript:;" class="btn btn-primary" @click='selectChild()' ><?php echo Yii::t('user','添加新学生') ?></a>
                    </div>
                    <div class="btn-group">
                        <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <?php echo Yii::t('reg','提取邮件地址')?> <span class="caret"></span>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a href="javascript:;" @click='email(1)'><?php echo Yii::t('reg','全部家长邮件地址') ?></a></li>
                            <li><a href="javascript:;" @click='email(2)'><?php echo Yii::t('reg','未绑定微信家长邮件地址') ?></a></li>
                        </ul>
                    </div>
                    <div class="btn-group">
                        <a href="javascript:;" class="btn btn-primary" @click='send()'  v-if='data.length!=0'><?php echo Yii::t('user','发送微信通知') ?></a>
                    </div>
                </div>
			</div>
			<div class="clearfix"></div>
			<template v-if='data.length!=0'>
				<div class="panel panel-default"  v-for='(list,index) in classList'>
					<div class="panel-heading">{{list.title}} （ {{list.count}}人）</div>
					<ul class="list-group listdata">
						<template v-if='data[list.id].paid'>
							<li class="list-group-item ">
								<p>
									<div class="checkbox">
										<label>
											<input type="checkbox" v-if='list.title!="unClass"'  v-model="data[list.id].checked" @change="selectItem1(data[list.id])">已付款
			                            </label>
		                            </div>
	                            </p>
								<div class="col-xs-3 col-sm-3 col-md-3 col-lg-3" v-for='(paid,idx) in data[list.id].paid'>
									<div class="checkbox">
										<label>
											<template v-if='list.id!="unClass"'>
												<template v-if='regStudentList[paid.childid]'>
													<template v-if='regStudentList[paid.childid].is_selected=="1"'>
														<input type="checkbox" v-if='regStudentList[paid.childid].status=="3"||regStudentList[paid.childid].status=="4"' :name="'students['+list.id+'][]'"  :value="paid.childid"  onclick="return false;" checked class="emailStu">
														<input type="checkbox" v-else :name="'students['+list.id+'][]'"  :value="paid.childid"  @change="selectItem2(data[list.id])"  v-model="paid.checked" class="emailStu">
													</template>
													<template v-else>
														<input type="checkbox" :name="'students['+list.id+'][]'" :value="paid.childid"  @change="selectItem2(data[list.id])" v-model="paid.checked" class="emailStu">
						                            </template>
												</template>
												<template v-else>
													<input type="checkbox" :name="'students['+list.id+'][]'" :value="paid.childid"  @change="selectItem2(data[list.id])" v-model="paid.checked" class="emailStu">
					                            </template>
											</template>
                                            {{paid.childName}}
				                            <template  v-if='regStudentList[paid.childid]'>
				                            	<span class="speed" v-if='regStudentList[paid.childid].is_selected=="1"' :class='color[regStudentList[paid.childid].status]'></span>
				                            </template>
			                            </label>
                                        <a onclick="overallss(this)" :data-id='paid.childid' :data-url='paid.childUrl' :data-name='paid.childName' :data-class='list.title' ><span class="glyphicon glyphicon-link"></span></a>
		                            </div>
								</div>
								<div class="clearfix"></div>
							</li>
						</template>
						<template  v-if='data[list.id].unPaid'>
							<li class="list-group-item ">
								<p><div class="checkbox">未付款</p>
								<div class="col-xs-3 col-sm-3 col-md-3 col-lg-3"  v-for='(unpaid,idxs) in data[list.id].unPaid'>
									<div class="checkbox">
										<label>
											<template v-if='list.id!="unClass"'>
												<template v-if='regStudentList[unpaid.childid]'>
													<template v-if='regStudentList[unpaid.childid].is_selected=="1"'>
														<input type="checkbox" v-if='regStudentList[unpaid.childid].status=="3"||regStudentList[unpaid.childid].status=="4"' :name="'students['+list.id+'][]'"  :value="unpaid.childid"  onclick="return false;" checked class="emailStu">
														<input type="checkbox" v-else :name="'students['+list.id+'][]'"  :value="unpaid.childid" checked class="emailStu">
													</template>
													<template v-else>
														<input type="checkbox"  :name="'students['+list.id+'][]'" :value="unpaid.childid" class="emailStu">
													</template>
												</template>
												<template v-else>
													<input type="checkbox"  :name="'students['+list.id+'][]'" :value="unpaid.childid" class="emailStu">
					                            </template>
											</template>
											{{unpaid.childName}} <span v-if='unpaid.invoiceStatus'>（{{unpaid.invoiceStatus}}）</span>
											<template  v-if='regStudentList[unpaid.childid]'>
												<span class="speed" v-if='regStudentList[unpaid.childid].is_selected=="1"' :class='color[regStudentList[unpaid.childid].status]'></span>
											</template>
		                                </label>
                                        <a onclick="overallss(this)" :data-id='unpaid.childid' :data-url='unpaid.childUrl'  :data-name='unpaid.childName' :data-class='list.title' ><span class="glyphicon glyphicon-link"></span></a>
		                            </div>
								</div>
								<div class="clearfix"></div>
							</li>
						</template>
					</ul>
				</div>
			</template>
			<template v-else>
				<div class="alert alert-warning" role="alert">暂无可标记学生</div>
			</template>
			</form>
			<p><button class="btn btn-primary" id='keep' @click='keep()'  v-if='data.length!=0'>保存</button></p>
			<div class="modal fade bs-example-modal-lg" id="release" tabindex="-1" role="dialog" data-backdrop="static">
				<div class="modal-dialog modal-lg">
					<div class="modal-content">
						<div class="modal-header">
							<button type="button" class="close" data-dismiss="modal" aria-hidden="true">
							<span onclick="closesend()">&times;</span>
							</button>
							<h4 class="modal-title" id="myModalLabel">发送通知</h4>
						</div>
						<div class="modal-body">
							<div class="col-md-6">
								<div class="panel panel-info">
			                        <div class="panel-heading">可发送列表</div>
			                        <div class="J_check_wrap">
			                            <table class="table  table-striped mb0">
			                                <thead>
			                                    <tr style="height: 33px">
			                                        <th width="40" class="textcen">
			                                            
			                                            <div class="checkbox-custom checkbox-default">
			                                                 <input type="checkbox" class="J_check_all " data-checklist="J_check_c1" data-direction="y"   v-model="checked" @click="changeAllChecked()">
			                                                <label></label>
			                                            </div>
			                                            
			                                        </th>
			                                        <th width="150">名字</th>
			                                        <th><img style="width: 20px" src="/themes/base/images/weixin.png" alt=""></th>
			                                    </tr>
			                                </thead>
			                            </table>
			                            <div class="pre-scrollable" style="height:340px;margin-top:-1px">
			                                <table class="table mb0">
			                                    <tbody class="childname">
		                                        	<tr  v-for='(sendChild,index) in sends' :id="'tr'+sendChild.id">
			                                            <td width="40" :id="'check'+sendChild.id" class="textcen">
			                                                <div class="checkbox-custom checkbox-default">
			                                                     <input class="J_check" data-yid="J_check_c1" type="checkbox" name='send' :value="sendChild.id"  v-model="checkedNames">
			                                                    <label></label>
			                                                </div>
			                                            </td>
			                                            <td width="40" class="pr0">
			                                                  <img class="media-object child-face img-thumbnail pull-left" :src="sendChild.photo" :title="sendChild.name">
			                                            </td>
			                                            <td width="100">
			                                                <span>{{sendChild.name}}</span>
			                                            </td>
			                                            <td :id="sendChild.id" class="sendlist">
			                                                <p class="mt5">
			                                                    <template v-for='(parent,idx) in sendChild.parent'>
			                                                     	 <img class="img-circle imgbig ml5" :src="parent.headimgurl" :alt="parent.headimgurl" :title="parent.name">
			                                                    </template>
			                                                </p>
			                                                <p class="mt5 textsend" :id="'text'+sendChild.id"></p>
			                                            </td>
			                                        </tr>
			                                    </tbody>
			                                </table>
			                            </div>
			                        </div>
			                    </div>
			                    <div class="modal-footer bodbotnone">
			                        <button type="submit" class="btn btn-primary sendbtn" onclick='sendChild()'><?php echo Yii::t('global', 'Send')?></button>
			                    </div>
							</div>
							<div class="col-md-6">
								<div class="panel panel-default">
			                        <div class="panel-heading"><?php echo Yii::t('teaching', 'Unavailable to send')?></div>
			                        <table class="table table-striped mb0">
			                            <thead>
			                                <tr>
			                                    <th width="150">名字</th>
			                                    <th><img style="width: 20px" src="/themes/base/images/weixin.png" alt=""></th>
			                                    <th width="78">状态</th>
			                                </tr>
			                            </thead>
			                        </table>
			                        <div class="pre-scrollable" style="height:340px;margin-top:-1px">
			                            <table class="table mb0">
			                                <tbody class="addchild">
			                                
	                                        	<tr v-for='(sendChild,index) in sent'>
		                                            <td width="40" class="pr0">
		                                                  <img class="media-object child-face img-thumbnail pull-left" :src="sendChild.photo" :title="sendChild.name">
		                                            </td>
		                                            <td width="100">
		                                                <span>{{sendChild.name}}</span>
		                                            </td>
		                                            <td :id="sendChild.id" class="sendlist">
		                                                <p class="mt5">
		                                                    <template v-for='(parent,idx) in sendChild.parent'>
		                                                     	 <img class="img-circle imgbig ml5" :src="parent.headimgurl" :alt="parent.headimgurl" :title="parent.name">
		                                                    </template>
		                                                </p>
		                                                <p class="mt5 textsend" :id="'text'+sendChild.id"></p>
		                                            </td>
		                                            <td  width="60"><?php echo Yii::t('teaching', 'Sent')?></td>
		                                        </tr>
		                                         <tr  v-for='(Wechat,index) in noWechat'>
				                                    <td width="40" class="pr0"> 
				                                        <img class="media-object child-face img-thumbnail pull-left" :src="Wechat.photo" :title="Wechat.name">
				                                    </td>
			                                        <td width="100"> 
			                                           <span>{{Wechat.name}}</span>
			                                        </td>
			                                        <td><?php echo Yii::t('teaching', 'No wechat account linked')?></td>
			                                        <td width="50"></td>
			                                    </tr>
			                                </tbody>
			                            </table>
			                        </div>
			                    </div>
			                    <div class="modal-footer bodbotnone">
			                        <span>24小时内不可向同一用户重复发送通知</span>
			                    </div>
							</div>
						</div>
						<div class="clearfix"></div>
					</div>
				</div>
			</div>
			<div class="modal fade" tabindex="-1" role="dialog" id='emailData' data-backdrop="static">
			  <div class="modal-dialog" role="document">
			    <div class="modal-content">
			      <div class="modal-header">
			        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
			        <h4 class="modal-title">邮件</h4>
			      </div>
			      <div class="modal-body">
			        <p class="break">
			        	<textarea class="form-control" rows="10" id="emailText" :value='parentEmail'></textarea>
			        	<!-- <input type="email" class="form-control" id="exampleInputEmail1" placeholder="Email"> -->
			        	<!-- {{parentEmail}} -->
			        </p>
			      </div>
			      <div class="modal-footer">
			        <button type="button" class="btn btn-default" onclick="updateEmailText(1)">使用分号分割</button>
			        <button type="button" class="btn btn-default" onclick="updateEmailText(2)">使用逗号分割</button>
			        <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
			      </div>
			    </div>
			  </div>
			</div>


            <div class="modal fade" tabindex="-1" role="dialog" id='overallModel2'>
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title">整体情况</h4>
                        </div>
                        <div class="modal-body">
                            <p>孩子信息</p>
                            <table class="table table-bordered">
                                <thead>
                                <tr>
                                    <th>名字</th>
                                    <th>班级</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td>{{name}}</td>
                                    <td>{{class_title}}</td>
                                </tr>
                                </tbody>
                            </table>
                            <p>完成情况</p>
                            <table width="100%" >
                                <thead>
                                <tr style="height:30px">
                                    <!-- -1驳回 0未操作 1通过 2重新提交 -->
                                    <td align="center" valign="middle" v-for='(data,key,index) in getOverAll.steps' width='100'>
                                    <span v-if='getOverAll.childSteps[key]'>
                                        <span v-if='getOverAll.childSteps[key].complete==-1' class="label label-danger bg">{{index+1}}</span>
                                        <span v-if='getOverAll.childSteps[key].complete==0' class="label label-warning bg">{{index+1}}</span>
                                        <span v-if='getOverAll.childSteps[key].complete==1' class="label label-success bg">{{index+1}}</span>
                                        <span v-if='getOverAll.childSteps[key].complete==2' class="label label-info bg">{{index+1}}</span>
                                    </span>
                                        <span v-else class="label label-default bg">{{index+1}}</span>
                                    </td>
                                </tr>
                                </thead>
                                <tbody>
                                <tr style="height:30px">
                                    <td align="center" valign="middle" v-for='(all,num,index) in getOverAll.steps'>
                                        <small v-if='getOverAll.childSteps[num]'>
                                            {{completeConfig[getOverAll.childSteps[num].complete]}}
                                        </small>
                                        <small v-else>
                                            未填写
                                        </small>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <p>家长信息</p>
                            <table class="table table-bordered">
                                <thead>
                                <tr>
                                    <th></th>
                                    <th>父亲</th>
                                    <th>母亲</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td>姓名</td>
                                    <td v-for='(father,num,index) in getOverAll.parents'>{{father.name}}</td>
                                </tr>
                                <tr>
                                    <td>电话</td>
                                    <td v-for='(father,num,index) in getOverAll.parents'>{{father.mphone}}</td>
                                </tr>
                                <tr>
                                    <td>邮件</td>
                                    <td v-for='(father,num,index) in getOverAll.parents'>{{father.email}}</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>

		</div>
		<div class="modal fade" tabindex="-1" role="dialog" id='selectChild' data-backdrop="static">
			  <div class="modal-dialog" role="document">
			    <div class="modal-content">
			      <div class="modal-header">
			        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
			        <h4 class="modal-title"><?php echo Yii::t('user','添加新学生') ?></h4>
			      </div>
			      <?php $form=$this->beginWidget('CActiveForm', array(
					    'id'=>'courseGroup-form',
					    'enableAjaxValidation'=>false,
					    'htmlOptions'=>array('class'=>'J_ajaxForm', 'role'=>'form'),
					    'action'=>$this->createUrlReg('updateChild')
					)); ?>
			      <div class="modal-body">

			      	<div class="form-group">
				        <label for="searchChild">查询孩子</label>
				        <?php $this->widget('ext.search.ChildSearchBox', array(
				            'acInputCSS' => 'form-control',
				            'allowMultiple' => false,
				            'simpleDisplay' => false,
				            'extendCss' => false,
				            'useModel' => false,
				            'withAlumni' => true,
				            'name' => 'childid',
				            'htmlOptions' => array('class'=>'form-control'),
				        )) ?>
						<input type="text" class="hidden" name='start_time' value='<?php echo $startdate ?>'>
						<input type="text" class="hidden" name='end_time' value='<?php echo $enddate ?>'>
						<input type="text" class="hidden" name='yid' value='<?php echo $yid ?>'>
				    </div>
					
			      </div>
			      <div class="modal-footer">
			         <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
			         <button type="button" class="btn btn-primary J_ajax_submit_btn">提交</button>
			      </div>
			      <?php $this->endWidget(); ?>
			    </div>
			  </div>
			</div>
	</div>
</div>
<?php $this->renderPartial('//layouts/common/branchSelectBottom'); ?>
<script>
	var data = <?php echo json_encode($data) ?>;//班级 孩子数据
	var startYearList = <?php echo json_encode($startYearList) ?>;//年月
	var regStudentList = <?php echo json_encode($regStudentList) ?>;//标记过的学生
	var studentConfig = <?php echo json_encode($studentConfig) ?>;//标记过的学生
	var completeConfig = <?php echo json_encode($completeConfig) ?>;//标记过的学生
	var classList = <?php echo json_encode($classList) ?>;//班级
	var startdate = '<?php echo $startdate ?>';
	var enddate = '<?php echo $enddate ?>';
	var yid = '<?php echo $yid ?>';
	$(function() {
		$("#startTime").datepicker({
			dateFormat: "yy-mm-dd",
		});
		$("#endTime").datepicker({
			dateFormat: "yy-mm-dd",
		});
	});
	function sortKey(array, key) {
        return array.sort(function(a, b) {
            var x = parseInt(a[key])
            var y = parseInt(b[key]);
            return((x > y) ? -1 : (x < y) ? 1 : 0)
        })
    }
	var mark = new Vue({
		el: "#mark",
		data: {
			color:{
				'0':'gray',
				'1':'orange',
				'2':'green',
				'3':'blue',
				'4':'red'
			},
			studentConfig:studentConfig,
			YearList:'',
			data:data,
			classList:classList,
			yid:parseInt(yid),
			startdate:startdate,
			enddate:enddate,
			startYearList:startYearList,
			regStudentList:regStudentList,//0：默认；1：进行中；2：完成；3：审核完成；4：驳回
			sendAll:[],//发送全部人员
			sends:[],//可发送
			sent:[],//已发送
			checked: true,
            checkedNames:[],//
            completeConfig:completeConfig,
            sendArr:[],//全选
            noWechat:[],//未绑定微信
            parentEmail:'',
            getOverAll:'',
            name:'',
            class_title:'',
            childUrl:''
		},
		watch:{
			"checkedNames":function(){ 
				if(this.checkedNames.length!=this.sendArr.length){
					this.checked=false 
				}else{
					this.checked=true
				}
			}
		},
		created: function() {
		    console.log(classList)
            var newcalendarArr = []
            for(key in startYearList) {
                var calendarArrkey = {}
                calendarArrkey.id = parseInt(key)
                calendarArrkey.title = startYearList[key]
                newcalendarArr.push(calendarArrkey)
            }
            this.YearList = sortKey(newcalendarArr, 'id')
		    if(data.length==0){
		        return
            }
			for(var j=0;j<classList.length;j++){
				if(this.data[classList[j].id].paid){
					for(var i=0;i<this.data[classList[j].id].paid.length;i++){
						if(this.regStudentList[this.data[classList[j].id].paid[i].childid]){
							if(this.regStudentList[this.data[classList[j].id].paid[i].childid].is_selected==1){
								Vue.set(this.data[classList[j].id].paid[i], 'checked', true)
							}else{
								Vue.set(this.data[classList[j].id].paid[i], 'checked', false)
							}
						}else{
							Vue.set(this.data[classList[j].id].paid[i], 'checked', false)
						}
					}	
					var lengths = this.data[classList[j].id].paid.length;
	                var checkeds = this.data[classList[j].id].paid.filter(i => {
	                    return i.checked == true;
	                });
	                if (lengths == checkeds.length) {
	                   Vue.set(this.data[classList[j].id], 'checked', true)
	                } else {
	                    Vue.set(this.data[classList[j].id], 'checked', false)
	                }
				}
			}

		},
		computed: {},
		methods: {
			changeAllChecked: function () { 
				var self=this; 
				if (!self.checked) { 
					self.checkedNames=[] 
				} else {
					self.checkedNames=self.sendArr; 
				} 
			},
			send(){
				mark.sends=[]
				mark.sendArr=[]
				mark.checkedNames=[]
				mark.sent=[]
				mark.noWechat=[]
				$.ajax({
			        url:'<?php echo $this->createUrlReg('taskData'); ?>',
			        type: 'post',
			        dataType:'json',
			        data:{},
			        success:function(data){
			        	mark.sendAll=data.children
		        		$('#release').modal('show')
		        		for(var i=0;i<data.children.length;i++){
		        			if(!data.expired[data.children[i].id]){
		        				if(data.children[i].bindingStatus==1){
		        					mark.sends.push(data.children[i])
		        					mark.sendArr.push(data.children[i].id)
		        					mark.checkedNames.push(data.children[i].id)
		        				}else{
		        					mark.noWechat.push(data.children[i])
		        				}
		        			}else{
		        				mark.sent.push(data.children[i])
		        			}
		        		}
			        },error:function(data){
			             resultTip({error: 'warning', msg: '请求错误'});
			        }
			    })
			},
			email(type){
				$.ajax({
			        url:'<?php echo $this->createUrlReg('childsParentEmail'); ?>',
			        type: 'post',
			        dataType:'json',
			        data:{
			        	yid:yid,
                        type:type
			        },
			        success:function(data){
			        	if(data.state="success"){
			        		mark.parentEmail=data.data
			        		$('#emailData').modal('show')
			        	}
			        },error:function(data){
			             resultTip({error: 'warning', msg: '请求错误'});
			        }
			    })
			},
			selectItem1: function (item1) { 
				for(var i=0;i<item1.paid.length;i++){
					 item1.paid[i].checked=item1.checked;
				}
			},
			selectItem2(item1) {
                var lengths = item1.paid.length;
                var checkeds = item1.paid.filter(i => {
                    return i.checked == true;
                });
                if (lengths == checkeds.length) {
                    item1.checked = true;
                } else {
                    item1.checked = false;
                }
            },
			keep(){
				$("#keep").attr('disabled',true);
				$.ajax({
                    url:'<?php echo $this->createUrlReg('savemark'); ?>',
                    type: 'post',
                    dataType:'json',
                    data:$('#students').serialize(),
                    success:function(data){
                    	$("#keep").attr('disabled',false);
                    	if(data.state=='success'){
                    		resultTip({msg: data.message});
                    		setTimeout(function(){ 
								window.location.reload();
							},1500);
                    	}else{
                    		resultTip({error: 'warning',msg: data.message});
                    	}
                       
                    },error:function(data){
                    	$("#keep").attr('disabled',true);
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
			},
			selectChild(){
				$('#selectChild').modal('show')
			}
		},
	})
	function cbUpdateChild(data){
		setTimeout(function(){ 
			window.location.reload()
		},1500);
	}

    function updateEmailText(data){
        var emailDate = $("#emailText").val();
        if(data == 1){
            re=new RegExp(",","g");
            var newstart=emailDate.replace(re,";");
        }else{
            re=new RegExp(";","g");
            var newstart=emailDate.replace(re,",");
        }
        $("#emailText").val(newstart);
    }
	closeindex=0
    read=''
    function sendChild(){
        read=''
        var chaildfiler=[]
        $.each($('input[name="send"]:checked'),function(){
            chaildfiler.push($(this).val());
        })
        if(chaildfiler.length==''){
             resultTip({msg:'<?php echo Yii::t('teaching', 'Please select')?>', error: 1});
        }else{
            sendAjaxS(chaildfiler);
        }
    }
    function sendAjaxS(id) {
        var index = 0;
        var result = [];
        var childindex='';
        var sendsucc=[]
        var successData=''
        sendAjax(index);
        function sendAjax(index) {
            closeindex=1
            $('.sendbtn').attr("disabled", true);
            $('#text'+id[index]).css('visibility','visible')
            $('#text'+id[index]).html('<?php echo Yii::t('teaching', 'Sending...')?>')
            if(childindex!=''){
                var html=''
                for(var i=0;i<mark.sendAll.length;i++){
                    if(childindex==mark.sendAll[i].id){
	                    $('#tr'+childindex).fadeToggle(1000)
	                    mark.sent.unshift(mark.sendAll[i])
                    }
                }
            }
            if(index >= id.length) {
                doSomething(id,result);
                return;
            }
            if(read=='close'){
                colsesend(sendsucc)
                return;
            }
            $.ajax({
                url: '<?php echo $this->createUrlReg("SendMessage")?>',
                type: "POST",
                async: true,
                dataType: 'json',
                data: {
                    'childid': id[index],
                },
                success: function(data) {
                    if(data.state == 'success') {
                        result.push(data);
                        sendsucc.push(id[index])
                        $('#check'+id[index]).find('div').remove()
                        $('#text'+id[index]).html(' <span class="glyphicon glyphicon-ok" aria-hidden="true"></span> <?php echo Yii::t('teaching', 'Sent')?>')
                        childindex=id[index]
                        successData=data
                        setTimeout(function(){
                            index++;
                            sendAjax(index);
                        }, 1000);
                    } else {
                        $('#'+id[index]).html(data.message)
                        closeindex=0
                        $('.sendbtn').attr("disabled", true);
                        resultTip({msg: data.message, error: 1});
                    }
                },
                error: function() {
                    alert("请求错误")
                }
            });
         }
    }
    function doSomething(id,data) {
        closeindex=0
        $('.sendbtn').attr("disabled", false);
    }
    function colsesend(sendsucc){
        closeindex=0
        $('.sendbtn').attr("disabled", false);
    }
    function closesend(){
        if(closeindex==0){
            $('#release').modal('hide');
        }else{
            var con;
            con=confirm("<?php echo Yii::t('teaching', 'Sending task is not completed, are you sure to stop it?')?>"); //在页面上弹出对话框
            if(con){
                $('#release').modal('hide');
                read='close'
            }
        }
    }

    function overallss(obj){
        var id=$(obj).attr('data-id')
        var name=$(obj).attr('data-name')
        var url=$(obj).attr('data-url')
        var class_title=$(obj).attr('data-class')
        mark.name=name
        mark.class_title=class_title
        mark.childUrl=url
        $.ajax({
            url: "<?php echo $this->createUrlReg('overall')?>",
            type: 'get',
            dataType: 'json',
            data: {
                childid:id
            },
            success: function(data) {
                if(data.state == "success") {
                    mark.getOverAll=data.data
                    $('#overallModel2').modal('show')
                } else {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                }
            },
            error: function(data) {
                alert('请求错误')
            }
        })
    }
</script>