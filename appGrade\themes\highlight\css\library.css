#card-info{
	display: block;
	width: 340px;
	height: 180px;
	background: url("../images/lib_card.png") no-repeat transparent;
	float: right;
	position: relative;
	z-index:1;
	margin-left: 20px;
}
#card-info h3{
	position: absolute;
	top: 26px;
	left: 40px;
	text-shadow: 2px 1px 1px rgba(20, 20, 20, 0.2);
}
#card-info .info{
	top: 60px;
	left: 60px;
	width: 180px;
	position: absolute;
}
#card-info .info dl{
	margin-bottom: 0.8em;
}
#card-info .info dl dt{
	float: left;
	color: #999;
	font-weight: normal;
	width: 60px;
	text-align: right;
	padding-right: 10px;
}

#card-info .info dl dd{
	border-bottom: 1px dotted #999;
	margin-left: 70px;
}
#card-info .status{
    margin-left: 100px;
    position: absolute;
    right: 40px;
    top: 60px;
	color: #999;
	font-size: 12px;
}
#card-info .status span{
	font-size: 38px;
	color: #333;
	font-weight: bold;
	padding: 0 4px;
	font-family: serif;
	font-style: italic;
}
#card-info .desc{
}
#card-info .no-card{
    color: #999999;
    font-size: 18px;
    left: 60px;
    position: absolute;
    top: 80px;
}