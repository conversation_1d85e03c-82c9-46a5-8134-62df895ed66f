<style>
    .time-picker{
        width:100% !important
    }
    .el-date-editor .el-range-separator{
        width:10%
    }
    .stepUpImg{
        max-width:200px;
        margin-right:10px;
        margin-bottom:20px;
    }
</style>
<div id='container'>
    <div class="progress">
        <div class="progress-bar progress-bar-success" :style="'width:'+ parsefloat(audited/total) +'%;'">
            <span v-if='audited!=0' :title="audited+'/'+total">{{parsefloat(audited/total)}}% ({{audited}}/{{total}})</span>
            <span v-else>0% ({{audited}}/{{total}})</span>
        </div>
    </div>
    <div class='pull-left' id="nameSearchComp"></div>
    <p class='pull-right'>
<!--        <button type="button" class="btn btn-default btn-sm"  v-if='type=="waitAudited"'  :disabled='btnCon' @click='batchPass("modal")'>批量通过</button>-->
        <button type="button" class="btn btn-default btn-sm ml10"  @click='exportTab()'>导出表格</button>
        <button type="button" class="btn btn-primary btn-sm ml10" :disabled='btnCon'  @click='print(type)'>打印</button>
    </p>
    <div class='clearfix'></div>
    <div class='relative'>
        <div class='loading' v-show='loading'>
            <span></span>
        </div>
        <ul class="nav nav-tabs" role="tablist">
            <li role="presentation" :class = "{active: type == 'waitAudited'}"><a href="#home" aria-controls="home" role="tab" data-toggle="tab"  @click='getInfo("waitAudited")'>待审核 <span class="badge">{{waitAudited}}</span></a></li>
            <li role="presentation" :class = "{active: type == 'audited'}"><a href="#unstart" aria-controls="unstart" role="tab" data-toggle="tab" @click='getInfo("audited")'>已审核 <span class="badge badge-error">{{audited}}</span></a></li>
        </ul>
        <div class="tab-content">
            <div role="tabpanel" class="tab-pane" :class="{active: type == 'waitAudited'}" id="home">
<!--                待审核数据-->
                <table class="table table-bordered mt20" id='waitAudited' v-show='list.length!=0'>
                    <thead>
                    <tr>
                        <th width='30' class="nosort">
                            <input type="checkbox" id="inlineCheckbox1" value="option1"  v-model='allWaitAudited'  @change='checkAll($event)'>
                        </th>
                        <th width='150'><?php echo Yii::t('global', 'Name') ?></th>
                        <th width='200'><?php echo Yii::t('labels', 'Class') ?></th>
                        <th width='150'><?php echo Yii::t('labels', 'Date of Birth') ?></th>
                        <th width='150'><?php echo Yii::t('labels', 'Status') ?></th>
                        <th width='150'>提交时间</th>
                        <th width='80' class="nosort"><?php echo Yii::t('global', 'Action') ?></th>
                    </tr>
                    </thead>
                    <tbody v-if='isShow'>
                    <tr v-for='(item,index) in list'>
                        <th>
                            <input type="checkbox" id="inlineCheckbox1" :value="item.childid" v-model='WaitAuditedChildIds'  @change='childCheck($event)'>
                        </th>
                        <td >
                            <a href="javascript:;"  @click="overalls(item.childid)">
                                {{item.name}}
                            </a>
                        </td>
                        <td > {{item.class_name}}</td>
                        <td >{{item.birthday}}</td>
                        <td >
                            <i class='glyphicon glyphicon-remove-sign' v-if='item.step_status==3' style='color:#D5514E'></i>
                            <i class='glyphicon glyphicon-ok-sign'  v-if='item.step_status==4' style='color:#5cb85c'></i>
                            <i class='glyphicon glyphicon-registration-mark'  v-if='item.step_status==1' style='color:#f0ad4e'></i>
                            <i class='glyphicon glyphicon-question-sign'  v-if='item.step_status==0' style='color:#f0ad4e'></i>
                            {{statusList[item.step_status]}}</td>
                        <td >{{item.step_data.submitted_at}} </td>
                        <td >
                            <a  class="btn btn-primary btn-xs" href='javascript:;' @click='auditList(item)'><?php echo Yii::t('global', 'View Detail') ?></a>
                            <a class="btn btn-primary btn-xs" :href="'<?php echo $this->createUrlReg('batchPrint', array('branchId' => $this->branchId,'coll_id'=>$coll_id)); ?>&childid='+item.childid+'&step_id=exemptPE'" target="_blank">
                                <?php echo Yii::t('global', 'Print') ?>
                            </a>
                        </td>
                    </tr>
                    </tbody>
                </table>

                <div class="mt15" v-show='list.length==0'>
                    <div class="alert alert-warning" role="alert"><?php echo Yii::t('ptc', 'No Data') ?></div>
                </div>
            </div>
            <!-- 已审核数据 -->
            <div role="tabpanel" class="tab-pane " :class="{active: type == 'audited'}" id="unstart">
                <table class="table table-bordered mt20" id='audited' v-show='unList.length!=0'>
                    <thead>
                    <tr>
                        <th width='30' class="nosort">
                            <input type="checkbox" id="inlineCheckbox1" value="option1"  v-model='allAudited'  @change='checkAllAudited($event)'>
                        </th>
                        <th width='150'><?php echo Yii::t('global', 'Name') ?></th>
                        <th width='200'><?php echo Yii::t('labels', 'Class') ?></th>
                        <th width='150'><?php echo Yii::t('labels', 'Date of Birth') ?></th>
                        <th width='150'><?php echo Yii::t('labels', 'Status') ?></th>
                        <th width='150'>提交时间</th>
                        <th width='80' class="nosort"><?php echo Yii::t('global', 'Action') ?></th>
                    </tr>
                    </thead>
                    <tbody v-if='isShow'>
                    <tr v-for='(item,index) in unList'>
                        <th>
                            <input type="checkbox" id="inlineCheckbox1" :value="item.childid" v-model='AuditedChildIds'  @change='childCheckAudited($event)'>
                        </th>
                        <td >
                            <a href="javascript:;"  @click="overalls(item.childid)">
                                {{item.name}}
                            </a>
                        </td>
                        <td > {{item.class_name}}</td>
                        <td >{{item.birthday}}</td>
                        <td >
                            <i class='glyphicon glyphicon-remove-sign' v-if='item.step_status==3' style='color:#D5514E'></i>
                            <i class='glyphicon glyphicon-ok-sign'  v-if='item.step_status==4' style='color:#5cb85c'></i>
                            <i class='glyphicon glyphicon-registration-mark'  v-if='item.step_status==1' style='color:#f0ad4e'></i>
                            <i class='glyphicon glyphicon-question-sign'  v-if='item.step_status==0' style='color:#f0ad4e'></i>
                            {{statusList[item.step_status]}}</td>
                        <td >{{item.step_data.submitted_at}} </td>
                        <td >
                            <a  class="btn btn-primary btn-xs" href='javascript:;' @click='auditList(item)'><?php echo Yii::t('global', 'View Detail') ?></a>
                            <a class="btn btn-primary btn-xs" :href="'<?php echo $this->createUrlReg('batchPrint', array('branchId' => $this->branchId,'coll_id'=>$coll_id)); ?>&childid='+item.childid+'&step_id=exemptPE'" target="_blank">
                                <?php echo Yii::t('global', 'Print') ?>
                            </a>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <div class="mt15" v-show='unList.length==0'>
                    <div class="alert alert-warning" role="alert"><?php echo Yii::t('ptc', 'No Data') ?></div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" tabindex="-1" role="dialog" id='auditDialog' data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title"><?php echo Yii::t('global', 'View Detail') ?></h4>
                </div>
                <div class="modal-body" v-if='Object.keys(rejectionList).length != 0'>
                    <div class="media col-md-12 ">
                        <div class="media-left pull-left media-middle">
                            <a href="javascript:void(0)">
                                <img :src="rejectionList.avatar" data-holder-rendered="true" class="media-object img-circle avatarImage">
                            </a>
                        </div>
                        <div class="media-body pt10 media-middle">
                            <h4 class="media-heading font14">{{rejectionList.name}}</h4>
                            <div class="text-muted">{{rejectionList.class_name}}</div>
                        </div>
                    </div>
                    <div class='col-md-12 mt20'>
                        <span class='color3 mr10'><?php echo Yii::t('coll', 'Please describe in detail the medical reasons why your child needs to apply for exemption from PE') ?> ：{{rejectionList.step_data.medical_reasons}}
                    </div>
                    <div class='col-md-12 mt20'>
                        <span class='color3 mr10'><?php echo Yii::t('coll', 'Please fill in the application dates for your child exemption from PE') ?> ：{{rejectionList.step_data.application_dates[0]}} <?php echo Yii::t('coll', 'To')?> {{rejectionList.step_data.application_dates[1]}}
                    </div>
                    <div class='col-md-12 mt20'>
                        <span class='color3 mr10'><?php echo Yii::t('coll', 'Others') ?> ：{{rejectionList.step_data.other}}
                    </div>
                    <div class='col-md-12 mt20'>
                        <p><?php echo Yii::t('coll', 'Excuse PE for more than 3 days, doctor’s note will be required to upload (Diagnosis certificate with "medical excuse from physical education and dates" written on it or Medical Excuse Form From Physical Education)') ?> ：</p>
                        <img v-for='(item,idx) in rejectionList.step_data.medical_certificate'  @click='bigImg(item)'  :src="item" alt="" class='stepUpImg'>
                    </div>

                    <div class='col-md-12 mt20 mb20' v-if='rejectionList.sign_url!=""'>
                        <p><?php echo Yii::t('event', 'Signature') ?>：</p>
                        <img :src="rejectionList.sign_url" alt="" class='signImgDetails'>
                    </div>
                    <div class='clearfix'></div>
                    <hr>
                    <div class='col-md-12 col-sm-12 font14'>
                        <div class='col-md-6 col-sm-6 borderRight scroll-box' style='max-height:250px;overflow-y:auto'>
                            <p><strong>当前审核状态</strong></p>

                            <div>
                                {{statusList[rejectionList.step_status]}}
                                <button type="button" class="btn btn-primary  btn-xs pull-right" @click='resetStatus()'  v-if='rejectionList.step_status==3 || rejectionList.step_status==4'><?php echo Yii::t('global', 'Reset') ?></button>
                            </div>
                            <p class='mt10'><strong>审核记录</strong></p>
                            <div>
                                <div v-for='(list,index) in rejectionList.audit_log' class='ml10' style='border-left:1px dashed #ccc'>
                                    <p style='margin-left:-7px;background: #fff;' >
                                <span v-if='list.status==4'>
                                <span class='glyphicon glyphicon-ok-sign greenIcon'></span><span> <?php echo Yii::t('global', 'Confirmed') ?></span>
                                </span>
                                        <span v-if='list.status==3'>
                                <span class='glyphicon glyphicon-info-sign redIcon'></span> <span> <?php echo Yii::t('reg', 'Rejected') ?></span>
                                </span>
                                        <span v-if='list.status==0'>
                                <span class='glyphicon glyphicon-question-sign yellowIcon'></span> <span> <?php echo Yii::t('global', 'Reset') ?></span>
                                </span>
                                    </p>
                                    <p class='ml10' style='background:#F7F7F8;line-height:22px'  v-if='list.comment!="" && list.comment!=null'><?php echo Yii::t('reg', '备注：') ?>{{list.comment}}</p>
                                    <p class='pl10 font12'>{{list.date}} {{list.user}}</p>
                                </div>
                            </div>
                        </div>
                        <div class='col-md-6 col-sm-6' >
                            <p><strong>审核</strong></p>
                            <div class='p10 grey'>
                                <label class="radio ml20">
                                    <input type="radio" id="inlineradio1" v-model='audit_status' value="4"> <?php echo Yii::t('labels', 'Yes') ?>
                                </label>
                                <label class="radio ml20">
                                    <input type="radio" id="inlineradio2" v-model='audit_status' value="3"> <?php echo Yii::t('labels', 'No') ?>
                                </label>
                                <textarea class="form-control" rows="3" placeholder='请输入备注' v-model='comment'></textarea>
                                <div v-if='audit_status==3 || type_status==2'>
                                    <p class='mt10'>修改建议将直接显示在家长端，请准确措辞。</p>
                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox"  v-model='send_wechat'>将审核结果发送至家长微信
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class='mt20'>
                                <button type="button"  class="btn btn-primary pull-right" :disabled='btnCon' @click='saveInfo()'><?php echo Yii::t("global", "OK");?></button>
                                <button type="button" class="btn btn-default  pull-right mr10" data-dismiss="modal"><?php echo Yii::t("global", "Close"); ?></button>
                            </div>
                        </div>
                    </div>
                    <div class='clearfix'></div>
                    <!--护士审核和汇总-->
                    <div class="panel panel-default mt20">
                        <div class="panel-heading">
                            <h3 class="panel-title"><?php echo Yii::t('reg', 'Summary of Nurse Review Status') ?></h3>
                        </div>
                        <div class="panel-body">
                            <div class='mt20'>
                                <div><label for=""><?php echo Yii::t('coll', 'Confirm whether students need to exemption from physical education') ?></label></div>
                                <label class="radio-inline">
                                    <input type="radio" name="inlineRadioOptions"  value="1" v-model='remarks.nonPhysicalEducation'> <?php echo Yii::t('coll', 'Yes') ?>
                                </label>
                                <label class="radio-inline">
                                    <input type="radio" name="inlineRadioOptions"  value="2"  v-model='remarks.nonPhysicalEducation'> <?php echo Yii::t('coll', 'No') ?>
                                </label>
                            </div>
                            <div v-if="remarks.nonPhysicalEducation == 1">
                                <div class='mt10'>
                                    <div><label for="">免体原因</label></div>
                                    <textarea class="form-control" rows="3" v-model='remarks.reasons_cn' placeholder="请仅用中文填写"></textarea>
                                </div>
                                <div class='mt10'>
                                    <div><label for="">Reasons of exemption from PE</label></div>
                                    <textarea class="form-control" rows="3" v-model='remarks.reasons_en' placeholder="Please fill in English only"></textarea>
                                </div>
                                <div class='mt20'>
                                    <div><label for=""><?php echo Yii::t('coll', 'Exemption Date') ?></label></div>
                                    <div class="flex mt8'">
                                        <el-date-picker
                                                placement="bottom-start"
                                                v-model="remarks.exemptionDate"
                                                type="daterange"
                                                range-separator="<?php echo Yii::t('coll', 'To') ?>"
                                                value-format="yyyy-MM-dd"
                                                start-placeholder="<?php echo Yii::t("campus", "Start Date"); ?>"
                                                end-placeholder="<?php echo Yii::t("campus", "End Date"); ?>">">
                                        </el-date-picker>
                                    </div>
                                </div>
                            </div>
                            <div class='mt20'>
                                <div><label for=""><?php echo Yii::t('reg', '通知人') ?></label></div>
                                <div class='staffInput' @click='showStaff("add")'>
                                    <span class='flex1' v-if='staff_ids.length==0'>请选择通知人员</span>
                                    <span class='flex1' v-else>
                                    <span class="el-tag el-tag--info el-tag--small el-tag--light mr10 mb5 mt5" v-for='(list,index) in staff_ids'>
                                        <span class="el-select__tags-text" v-if='allDept[list]'>{{allDept[list].name}}</span>
                                        <i class="el-tag__close el-icon-close" @click.stop='staffUnassign(list,index)'></i>
                                    </span>
                                </span>
                                    <span class='el-icon-arrow-down'></span>
                                </div>
                            </div>
                            <div class='mt20'>
                                <div><label for="">其它情况说明</label></div>
                                <textarea class="form-control" rows="3" v-model='remarks.other_cn' placeholder="请仅用中文填写"></textarea>
                            </div>
                            <div class='mt20'>
                                <div><label for="">Other Information</label></div>
                                <textarea class="form-control" rows="3" v-model='remarks.other_en' placeholder=" Please fill in English only"></textarea>
                            </div>

                            <div class='mt20'>
                                <div class="pull-left">
                                    <button type="button"  class="btn btn-primary pull-left" :disabled='btnCon' @click='saveRemarks("send")'><?php echo Yii::t("newDS", "保存并发送邮件");?></button>
                                    <div class='clearfix'></div>
                                    <p style="margin-top: 5px;margin-bottom: 0px" v-if="rejectionList.step_data.last_remarks_send_time">
                                        <span>最近通知时间：{{rejectionList.step_data.last_remarks_send_time}}</span>
                                    </p>
                                </div>
                                <div class="pull-right">
                                    <button type="button"  class="btn btn-primary pull-right ml10" :disabled='btnCon' @click='saveRemarks("save")'><?php echo Yii::t("global", "仅保存");?></button>
                                    <button type="button" class="btn btn-default  pull-right" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                                    <div class='clearfix'></div>
                                    <p style="margin-top: 5px;margin-bottom: 0px" v-if="rejectionList.step_data.last_remarks_time">
                                        <span>最近保存时间：{{rejectionList.step_data.last_remarks_time}}</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="stuDetails" tabindex="-1" role="dialog" >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" ><?php echo Yii::t('attends', '整体情况') ?> <span id="remarks_t"></span></h4>
                </div>
                <div class="modal-body color6" v-if='childData.child'>
                    <p><strong class='font14 color3'>学生资料</strong></p>
                    <div class='flex'>
                        <div style='width:80px'><img :src="childData.child.avatar" class='stuImg'></div>
                        <div class='flex1'>
                            <p class='mt10'><strong class='font14 color3 studentName'>{{childData.child.name}}</strong> </p>
                            <p class='mb20'>{{childData.child.class_name}}</p>
                            <div  v-if='childData.parents.finfo!=null'>
                                <div class='col-md-4'>
                                    <strong class='color3'><?php echo Yii::t('global', 'Father') ?>：</strong>
                                    <span class='glyphicon glyphicon-user'></span> {{childData.parents.finfo.name}}
                                </div>
                                <div class='col-md-4'>
                                    <span class='glyphicon glyphicon-phone'></span> {{childData.parents.finfo.mphone}}
                                </div>
                                <div class='col-md-4'>
                                    <span class='glyphicon glyphicon-envelope'></span> {{childData.parents.finfo.email}}
                                </div>
                            </div>
                            <div class='clearfix'></div>
                            <div class='mt10' v-if='childData.parents.minfo!=null'>
                                <div class='col-md-4'>
                                    <strong class='color3'><?php echo Yii::t('global', 'Mother') ?>：</strong>
                                    <span class='glyphicon glyphicon-user'></span> {{childData.parents.minfo.name}}
                                </div>
                                <div class='col-md-4'>
                                    <span class='glyphicon glyphicon-phone'></span> {{childData.parents.minfo.mphone}}
                                </div>
                                <div class='col-md-4'>
                                    <span class='glyphicon glyphicon-envelope'></span> {{childData.parents.minfo.email}}
                                </div>
                            </div>
                        </div>
                    </div>
                    <ul class="nav nav-wizard pb20 mt20">
                        <li v-for='(list,index) in childData.step_status'>
                            <a href="javascript::" class='color6' :data-tab="list.step_id" :data-status="list.status" :data-name="childData.child.name"><span class="number">{{index+1}}</span>{{list.title}}</a>
                            <p class='mt10 color6' >
                                <span :class="list.status==4?'greenStep':list.status==3?'redStep':list.status==1 || list.status==2?'orgStep':''">{{ statusList[list.status]}}</span></p>
                        </li>
                    </ul>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel') ?></button>
                </div>
            </div>
        </div>
    </div>

    <!-- 选择成员 -->
    <div class="modal fade" id="addStaffModal" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("newDS", "选择成员");?></h4>
                </div>
                <div class="modal-body relative" style='padding:0'>
                    <span class='borderLeftpos'></span>
                    <div style='max-height:600px;' class='p24 row'>
                        <div class='col-md-6 col-sm-6'>
                            <div>
                                <select v-model="school_id"  size='small' class="form-control" style='width:100%' placeholder="<?php echo Yii::t('teaching', 'Please select') ?>" @change='showStaff()'>
                                    <option
                                        v-for="(item,key,index) in schoolList"
                                        :key="key"
                                        :label="item.title"
                                        :value="key">
                                    </option>
                                </select>
                            </div>
                            <div class='mt10'>
                                <input type="text"
                                       placeholder="搜索"
                                       class="form-control"
                                       v-model='searchText' >
                            </div>
                            <div  class="tab-pane active mt15 scroll-box" id="class" v-if='searchText==""'  style='max-height:460px;overflow-y:auto'>
                                <div v-for='(list,index) in currentDept.list' class='relative mb16'>
                                    <p  @click='showDepname(list)'>
                                        <span  class='font14 color606 cur-p'>{{list.dep_name}} </span>
                                        <span class='el-icon-arrow-down ml5' v-if='dep_name!=list.dep_name'></span>
                                        <span class='el-icon-arrow-up ml5' v-else></span>
                                    </p>
                                    <div  class='border scroll-box mr10 childList' v-if='dep_name==list.dep_name'>
                                        <div class="flex align-items listMedia" v-for='(item,idx) in list.user'>
                                            <div class='flex flex1' v-if='currentDept.user_info[item.uid]'>
                                                <img :src="currentDept.user_info[item.uid].photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                                <div class="flex1 ml10 flex1Text">
                                                    <div class=" font14 mt4 color3 text_overflow">{{currentDept.user_info[item.uid].name}}</div>
                                                    <div class="font12 color6 text_overflow">{{currentDept.user_info[item.uid].hrPosition}}</div>
                                                </div>
                                            </div>
                                            <div >
                                                <span class='cur-p font16 text-primary  el-icon-circle-plus-outline' v-if='!currentDept.user_info[item.uid].disabled' @click='assignStaff(item,index,idx)'></span>
                                                <span v-else><?php echo Yii::t('directMessage', 'selected') ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else>
                                <div v-if='searchStaffList.length!=0' class='mt24 scroll-box'  style='max-height:460px;overflow-y:auto'>
                                    <div class="flex align-items listMedia" v-for='(item,idx) in searchStaffList'>
                                        <div class='flex flex1' >
                                            <img :src="item.photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                            <div class="flex1 ml10 flex1Text">
                                                <div class=" font14 mt4 color3 text_overflow">{{item.name}}</div>
                                                <div class="font12 color6 text_overflow">{{item.hrPosition}}</div>
                                            </div>
                                        </div>
                                        <div >
                                            <span class='cur-p font16 text-primary  el-icon-circle-plus-outline' v-if='!currentDept.user_info[item.uid].disabled' @click='assignStaff(item,idx,"search")'></span>
                                            <span v-else><?php echo Yii::t('directMessage', 'selected') ?></span>
                                        </div>
                                    </div>
                                </div>
                                <div v-else-if='searchText!=""'>
                                    <div class='font14 color6 text-center mt20'>暂无数据</div>
                                </div>
                            </div>
                        </div>
                        <div class='col-md-6 col-sm-6 borderLeft'>
                            <p class='mt10 font14 color606'>
                                <?php echo Yii::t("newDS", " ");?>{{staffSelected.length}}<?php echo Yii::t("newDS", "成员");?>
                                <button class="btn btn-link pull-right btn-xs font14" v-if='staffSelected.length!=0' type="button" @click='batchDel("staff")'><?php echo Yii::t("newDS", "清空");?></button>
                            </p>
                            <div class='scroll-box p10 overflow-y' style='height:500px'>
                                <div class="flex align-items listMedia" v-for='(item,idx) in staffSelected'>
                                    <div class='flex flex1' v-if='allDept[item]'>
                                        <img :src="allDept[item].photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                        <div class="flex1 ml10 flex1Text">
                                            <div class=" font14 mt4 color3 text_overflow">{{allDept[item].name}}</div>
                                            <div class="font12 color6 text_overflow">{{allDept[item].hrPosition}}</div>
                                        </div>
                                    </div>
                                    <div @click='Unassign(item,idx)'>
                                        <span class='closeChild cur-p mt10 font16 el-icon-circle-close'></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" @click='confirmSatff()'>确定</button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="tipsModal" tabindex="-1" role="dialog" >
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" ><?php echo Yii::t('attends', '批量通过') ?> <span id="remarks_t"></span></h4>
                </div>
                <div class="modal-body color6" >
                    <div class='color3 font14'>确认批量通过审核吗？</div> 
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel') ?></button>
                    <button type="button" class="btn btn-primary" :disabled='btnTips' @click='batchPass()'><?php echo Yii::t('global', '确定') ?></button>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var step_id = '<?php echo $step_id; ?>';
    var coll_id = '<?php echo $coll_id; ?>';
    var type = '<?php echo $type; ?>';
    var type_status = '<?php echo $type_status; ?>';
    var  registrations = new Vue({
        el: "#container",
        data: {
            type_status:type_status,
            list:[],
            audited:0,
            coll_id:coll_id,
            waitAudited:0,
            total:0,
            type:type,
            logList:[],
            statusList:[],
            audit_status:'',
            comment:'',
            child_id:'',
            childData:{},
            WaitAuditedChildIds:[],
            AuditedChildIds:[],
            rejectionList:{},
            unList:{},
            btnCon:false,
            send_wechat:true,
            isShow:true,
            loading:false,
            allWaitAudited:false,
            allAudited:false,
            remarks :{
                reasons:'',
                exemptionDate:[],
                other:'',
                nonPhysicalEducation:'',
            },
            searchText:'',
            currentDept:{},
            staffSelected:[],
            dep_name:'',
            staff_ids:[],
            DetailId:{},
            school_id:'',
            schoolList:[],
            btnTips:false
        },
        created: function() {
            this.getInfo(this.type)
            this.getSchool()
            eventBus.$on('listChanged', list => {
                if(sessionStorage.getItem('tabType')  === 'audited'){
                    this.unList = list
                }else{
                    this.list = list;
                }
            });
        },
        watch: {
        },
        computed: {
            searchStaffList: function() {
                var search = this.searchText;
                var searchVal = ''; //搜索后的数据
                if(search) {
                    searchVal =Object.values(this.currentDept.user_info).filter(function(product) {
                        return Object.keys(product).some(function(key) {
                            return String(product['name'].toLowerCase()).indexOf(search.toLowerCase()) !== -1;
                        })
                    })
                    return searchVal;
                }
                return this.searchStaffList;
            },
        },
        methods: {
            overalls(id){
                let that=this
                $.ajax({
                    url:'<?php echo $this->createUrlReg('getStudentOverall'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:{
                        child_id:id,
                        coll_id:coll_id,
                    },
                    success:function(data){
                        if(data.state=="success"){
                            that.childData=data.data
                            $('#stuDetails').modal('show')
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            parsefloat(num) {
                return Number(num * 100).toFixed(2);
            },
            getInfo(type){
                let that = this
                this.type=type
                this.isShow=false
                $.ajax({
                    url:'<?php echo $this->createUrlReg('stepTemplateInfo'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:{
                        coll_id:coll_id,
                        step_id:step_id,
                        type:type
                    },
                    success:function(data){
                        if(data.state=="success"){
                            if(type=='audited'){
                                that.unList = data.data.list
                            }else{
                                that.list = data.data.list
                            }
                            that.audited = data.data.audited
                            that.waitAudited = data.data.waitAudited
                            that.total = data.data.count
                            that.statusList=data.data.statusList
                            that.logList=data.data.logList
                            that.isShow=true
                            sessionStorage.setItem('tabType', type);
                            sessionStorage.setItem('listUpdated',  JSON.stringify(data.data.list));
                            that.$forceUpdate();
                            that.$nextTick(()=>{
                                that.DataTable(data)
                            })
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            DataTable(data){
                let that=this
                that.$nextTick(()=>{
                    $('#stuDetails').modal('hide');
                    $('#nameSearchComp').html(data.data.nameSearchComp)
                    if(that.type==='waitAudited'){
                        var table = $('#waitAudited').DataTable({
                            aaSorting: [5, 'desc'], // 默认排序
                            paging: false,
                            info: false,
                            searching: false,
                            "destroy": true,
                            columnDefs: [ {
                                "targets": 'nosort',
                                "orderable": false
                            }],
                        });
                    }else{
                        var table = $('#audited').DataTable({
                            aaSorting: [1, 'desc'], // 默认排序
                            paging: false,
                            info: false,
                            "destroy": true,
                            searching: false,
                            columnDefs: [ {
                                "targets": 'nosort',
                                "orderable": false
                            }],
                        });
                    }
                })

            },
            auditList(list){
                this.rejectionList=list
                this.child_id=list.childid
                this.audit_status=''
                this.comment=''
                if(list.step_data.remarks){
                    this.remarks = list.step_data.remarks
                    this.staff_ids=this.rejectionList.step_data.remarks.email_to ?? []
                    this.allDept=this.rejectionList.step_data.email_staff_list
                }else{
                    this.staff_ids=[]
                    this.allDept={}
                }
                $('#auditDialog').modal('show')

            },
            exportTab() {
                var exportData = [];
                const ws_name = "SheetJS";
                if(this.type=='waitAudited'){
                    var filename ='医疗免体_未审核.xlsx'
                    var list = this.list
                }else{
                    var filename='医疗免体_已审核.xlsx'
                    var list = this.unList
                }
                for(let i=0;i<list.length;i++){
                    var data={
                        'ID':list[i].childid,
                        '<?php echo Yii::t('global', 'Name') ?>':list[i].name,
                        "<?php echo Yii::t('labels', 'Class') ?>":list[i].className,
                        "<?php echo Yii::t('labels', 'Date of Birth') ?>":list[i].birthday,
                        "<?php echo Yii::t('labels', 'Status') ?>":this.statusList[list[i].step_status],
                        "<?php echo Yii::t('labels', 'Barcode') ?>":list[i].barcode,
                        "<?php echo Yii::t('labels', 'Educational Student ID') ?>":list[i].educational_id,
                        "<?php echo Yii::t("labels",'中文法定名')?>": list[i].is_legel_cn_name==1 ? '是':'否',
                        "<?php echo Yii::t("labels",'Chinese Name')?>":list[i].cn_name,
                        "<?php echo Yii::t("labels",'Legal First Name')?>":list[i].first_name_en,
                        "<?php echo Yii::t("labels",'Legal Middle Name')?>":list[i].middle_name_en,
                        "<?php echo Yii::t("labels",'Legal Last Name')?>":list[i].last_name_en,
                        "<?php echo Yii::t("labels",'Preferred Name')?>":list[i].nick,
                        "<?php echo Yii::t("labels",'Gender')?>":list[i].gender == 1 ? '男' : '女',
                        "<?php echo Yii::t("labels",'Nationality')?>":list[i].country,
                        "<?php echo Yii::t("labels",'Language')?>":list[i].lang,
                        "<?php echo Yii::t("labels",'Father Name')?>":list[i].father_name,
                        "<?php echo Yii::t("labels",'Father Email')?>":list[i].father_email,
                        "<?php echo Yii::t("labels",'Father Mobile')?>":list[i].father_phone,
                        "<?php echo Yii::t("labels",'Mother Name')?>":list[i].mather_name,
                        "<?php echo Yii::t("labels",'Mother Email')?>":list[i].mather_email,
                        "<?php echo Yii::t("labels",'Mother Mobile')?>":list[i].mather_phone,
                        "<?php echo Yii::t("labels",'ID NO./Passport NO.')?>":list[i].identity,
                        "提交时间":list[i].step_data.submitted_at,
                        "<?php echo Yii::t('coll','Reasons of exemption from PE')?>":list[i].step_data.medical_reasons,
                        "<?php echo Yii::t('coll','Exemption Date')?>":list[i].step_data.application_dates[0]+'至'+list[i].step_data.application_dates[1],
                        "<?php echo Yii::t('coll','Others')?>":list[i].step_data.other,
                    }
                    let medicineLen={}
                    data=Object.assign(data,medicineLen)
                    exportData.push(data)
                }
                let xlslHeader= [
                    'ID',
                    '<?php echo Yii::t('global', 'Name') ?>',
                    '<?php echo Yii::t('labels', 'Class') ?>',
                    '<?php echo Yii::t('labels', 'Date of Birth') ?>',
                    '<?php echo Yii::t('labels', 'Status') ?>',
                    '<?php echo Yii::t('labels', 'Barcode') ?>',
                    '<?php echo Yii::t('labels', 'Educational Student ID') ?>',
                    '<?php echo Yii::t('labels','中文法定名')?>',
                    '<?php echo Yii::t('labels','Chinese Name')?>',
                    '<?php echo Yii::t('labels','Legal First Name')?>',
                    '<?php echo Yii::t('labels','Legal Middle Name')?>',
                    '<?php echo Yii::t('labels','Legal Last Name')?>',
                    '<?php echo Yii::t('labels','Preferred Name')?>',
                    '<?php echo Yii::t('labels','Gender')?>',
                    '<?php echo Yii::t('labels','Nationality')?>',
                    '<?php echo Yii::t('labels','Language')?>',
                    '<?php echo Yii::t('labels','Father Name')?>',
                    '<?php echo Yii::t('labels','Father Email')?>',
                    '<?php echo Yii::t('labels','Father Mobile')?>',
                    '<?php echo Yii::t('labels','Mother Name')?>',
                    '<?php echo Yii::t('labels','Mother Email')?>',
                    '<?php echo Yii::t('labels','Mother Mobile')?>',
                    '<?php echo Yii::t('labels','ID NO./Passport NO.')?>',
                    '提交时间',
                    "<?php echo Yii::t('coll','Reasons of exemption from PE')?>",
                    "<?php echo Yii::t('coll','Exemption Date')?>",
                    "<?php echo Yii::t('coll','Others')?>",
                ]
                var wb=XLSX.utils.json_to_sheet(exportData,{
                    origin:'A1',// 从A1开始增加内容
                    header: xlslHeader
                });
                const workbook = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(workbook, wb, ws_name);
                const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                const blob = new Blob([wbout], {type: 'application/octet-stream'});
                let link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = filename;
                link.click();
                setTimeout(function() {
                    // 延时释放掉obj
                    URL.revokeObjectURL(link.href);
                    link.remove();
                }, 500);
            },
            checkAll(e){
                this.WaitAuditedChildIds=[]
                if(e.target.checked){
                    for(var i=0;i<this.list.length;i++){
                        this.WaitAuditedChildIds.push(this.list[i].childid)
                    }
                    this.allWaitAudited=true
                }else{
                    this.allWaitAudited=false

                }
            },
            checkAllAudited(e){
                this.AuditedChildIds=[]
                if(e.target.checked){
                    for(var i=0;i<this.unList.length;i++){
                        this.AuditedChildIds.push(this.unList[i].childid)
                    }
                    this.allAudited=true
                }else{
                    this.allAudited=false

                }
            },
            childCheck(e){
                if(e.target.checked){
                    if(this.WaitAuditedChildIds.length==this.list.length){
                        this.allWaitAudited=true
                    }else{
                        this.allWaitAudited=false
                    }
                }else{
                    this.allWaitAudited=false
                }
            },
            childCheckAudited(e){
                if(e.target.checked){
                    if(this.AuditedChildIds.length==this.unList.length){
                        this.allAudited=true
                    }else{
                        this.allAudited=false
                    }
                }else{
                    this.allAudited=false
                }
            },
            batchPass(){
                let that = this
                if(this.WaitAuditedChildIds.length==0){
                    resultTip({error: 'warning', msg: '请选择学生'});
                    return
                }
                this.loading=true
                this.btnCon=true
                $.ajax({
                    url:'<?php echo $this->createUrlReg('batchPass'); ?>',
                    type: 'post',
                    dataType:'json',
                    data:{
                        child_ids:this.WaitAuditedChildIds,
                        step_id:step_id,
                        coll_id:this.coll_id
                    },
                    success:function(data){
                        if(data.state=="success"){
                            resultTip({
                                msg: data.state
                            });
                            that.getInfo(that.type)
                            $('#auditDialog').modal('hide')
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                        that.btnCon=false
                        that.loading=false
                    },
                    error:function(data){
                        that.btnCon=false
                        that.loading=false
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            resetStatus(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrlReg("stepAudit") ?>',
                    type: "get",
                    dataType: 'json',
                    data:{
                        child_id:this.child_id,
                        step_id:step_id,
                        audit_status:0,
                        coll_id:this.coll_id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg: data.state
                            });
                            that.getInfo(that.type)
                            $('#auditDialog').modal('hide')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                    },
                })
            },
            saveInfo(){
                let that = this
                if(this.audit_status==''){
                    resultTip({
                        error: 'warning',
                        msg: '请选择审核状态'
                    });
                    return
                }
                that.btnCon=true
                var dataList={
                    child_id:this.child_id,
                    step_id:step_id,
                    audit_status:this.audit_status,
                    coll_id:this.coll_id,
                    comment: this.comment,
                }
                if(this.audit_status==3 || this.type_status==2){
                    var dataList=Object.assign({send_wechat:that.send_wechat?1:0},dataList)
                }
                //通过审核默认发送通知
                if(this.audit_status==4){
                    var dataList=Object.assign({send_wechat:1},dataList)
                }
                $.ajax({
                    url:'<?php echo $this->createUrlReg('stepAudit'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:dataList,
                    success:function(data){
                        if(data.state=="success"){
                            resultTip({
                                msg: data.state
                            });
                            that.getInfo(that.type)
                            // $('#auditDialog').modal('hide')
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                        that.btnCon=false
                    },
                    error:function(data){
                        that.btnCon=false
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            bigImg(list){
                var opacityBottom = '<div class="opacityBottom" style = "display:none"><img class="bigImg" src="' + list + '"></div>';
                $(document.body).append(opacityBottom);
                this.toBigImg();//变大函数
            },
            toBigImg() {
                $(".opacityBottom").addClass("opacityBottom");//添加遮罩层
                $(".opacityBottom").show();
                $("html,body").addClass("none-scroll");//下层不可滑动
                $(".bigImg").addClass("bigImg");//添加图片样式
                $(".opacityBottom").click(function () {//点击关闭
                    $("html,body").removeClass("none-scroll");
                    $(".opacityBottom").remove();
                });
            },
            getSchool(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrlReg("schoolList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        step_id:step_id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.schoolList=data.data.list
                            that.school_id=data.data.school_id
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            showStaff(list){
                let that=this
                $.ajax({
                    url:'<?php echo $this->createUrlReg('getAllDepartment'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:{
                        step_id:step_id,
                        school_id:this.school_id
                    },
                    success:function(data){
                        if(data.state=="success"){
                            if(list){
                                that.staffSelected= JSON.parse( JSON.stringify (that.staff_ids))
                                $("#addStaffModal").modal('show')
                            }
                            that.currentDept=data.data
                            that.allDept=Object.assign( that.allDept, data.data.user_info)
                            for(let key in that.currentDept.user_info){
                                if(that.staffSelected.indexOf(key+'')!=-1){
                                    Vue.set(that.currentDept.user_info[key], 'disabled', true);
                                }
                            }
                            that.dep_name=''
                            that.searchText=''

                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            staffUnassign(list,index){
                if(this.allDept[list]){
                    Vue.set(this.allDept[list], 'disabled', false);
                }
                this.staff_ids.splice(index,1)
            },
            assignStaff(list,index,idx){
                if(idx=='search'){
                    this.searchStaffList[index].disabled=true
                }else{
                    Vue.set(this.currentDept.user_info[list.uid], 'disabled', true);
                }
                this.staffSelected.push(list.uid)
            },
            Unassign(list,index){
                if(this.currentDept.user_info[list]){
                    Vue.set(this.currentDept.user_info[list], 'disabled', false);
                }
                this.staffSelected.splice(index,1)
            },
            showDepname(list){
                if(this.dep_name==list.dep_name){
                    this.dep_name=''
                    return
                }
                this.dep_name=list.dep_name
                for(let i=0;i<list.user.length;i++){
                    if(this.staffSelected.indexOf(list.user[i].uid)!=-1){
                        Vue.set(this.currentDept.user_info[list.user[i].uid], 'disabled', true);
                    }
                }
            },
            confirmSatff(){
                this.staff_ids= JSON.parse( JSON.stringify (this.staffSelected))
                $("#addStaffModal").modal('hide')
            },
            selectAll(list,index){
                list.user.forEach(item => {
                    if(!this.currentDept.user_info[item.uid].disabled && item.group_id==0){
                        this.staffSelected.push(item.uid)
                        Vue.set(this.currentDept.user_info[item.uid], 'disabled', true);
                    }
                });
            },
            batchDel(type){
                this.staffSelected=[]
                for(var key in this.currentDept.user_info){
                    if(this.staffSelected.indexOf(key)!=-1){
                        Vue.set(this.currentDept.user_info[key], 'disabled', true);
                    }else{
                        Vue.set(this.currentDept.user_info[key], 'disabled', false);
                    }
                }
            },
            print(type){
                let that = this
                if(type == 'audited'){
                    if(this.AuditedChildIds.length==0){
                        resultTip({error: 'warning', msg: '请选择学生'});
                        return
                    }
                    var child_ids = this.AuditedChildIds.toString()
                }else{
                    if(this.WaitAuditedChildIds.length==0){
                        resultTip({error: 'warning', msg: '请选择学生'});
                        return
                    }
                    var  child_ids = this.WaitAuditedChildIds.toString()
                }
                window.open('<?php echo $this->createUrlReg('batchPrint'); ?>&childid='+child_ids+'&step_id='+step_id+'&coll_id='+this.coll_id,'_blank');
            },
            saveRemarks(type){
                let that = this
                this.remarks.email_to=this.staff_ids
                if( this.remarks.nonPhysicalEducation == 1){
                    if(this.remarks.reasons_cn=='' || this.remarks.reasons_cn==null || this.remarks.reasons_en == '' || this.remarks.reasons_en == null ){
                        resultTip({
                            error: 'warning',
                            msg: '请填写免体原因'
                        });
                        return
                    }
                    if(this.remarks.exemptionDate.length != 2 ){
                        resultTip({
                            error: 'warning',
                            msg: '请填写免体日期'
                        });
                        return
                    }
                }
                $.ajax({
                    url:'<?php echo $this->createUrlReg('saveRemarks'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:{
                        child_id:this.child_id,
                        step_id:step_id,
                        remarks:this.remarks,
                        coll_id:this.coll_id,
                        type:type
                    },
                    success:function(data){
                        if(data.state=="success"){
                            resultTip({
                                msg: data.state
                            });
                            that.getInfo(that.type)
                            // $('#auditDialog').modal('hide')
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
        }
    })
</script>
<style>
    .staffInput{
        border: 1px solid #ccc;
        padding: 8px;
        border-radius: 4px;
        color: #333;
        display:flex;
        align-items:center;
        cursor: pointer;
    }
    .borderLeftpos {
        display: inline-block;
        border-left: 1px solid #E4E7ED;
        height: 100%;
        width: 1px;
        position: absolute;
        left: 50%;
    }
    .allCheck{
        position: absolute;
        right: 10px;
        top: 0;
    }
    .border{
        border: 1px solid #DCDFE6;
        padding: 16px;
        border-radius: 4px;
    }
    .listMedia{
        padding:8px;
        border: 1px solid #fff;
    }
    .listMedia:hover{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        border: 1px solid #4D88D2;
        cursor: pointer;
    }
    .img42{
        width: 42px;
        height: 42px;
        object-fit: cover;
        border-radius:50%;
    }
     a[data-status="0"] {
         cursor: default;
         text-decoration: none;
     }
</style>
