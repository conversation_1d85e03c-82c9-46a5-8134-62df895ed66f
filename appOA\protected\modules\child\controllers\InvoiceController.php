<?php

class InvoiceController extends InvoiceBasedController
{
    public $dialogWidth=650;
    public $policyApi;
    public $payDateToggle=false; #启明星类校园可选付款日期
    public $specialSchool='BJ_OG-PH';
    public function init() {
        parent::init();
        Yii::import('common.models.invoice.*');
        Yii::import('common.models.calendar.*');
        Yii::import('application.components.policy.*');
        Yii::import('application.components.models.*');
    }
	//Action 权限 KV值
    protected $needValidateAction = array(
        'finance' => 'oChildFinanceView', //查看财务信息
        'processsavefapiao' => 'oChildAdminAndFinanceAdmin', //保存发票
        'processusecredit' => 'oChildGeneralAdmin',			//使用个人账户
		'processpartialcashpay' => 'oChildGeneralAdmin',	//处理现金部分付清
		'processcashpay' => 'oChildGeneralAdmin',			//全部或超额现金付款（超额部分进入个人账户）
		'processvoucher' => 'oChildGeneralAdmin',			//处理代金卷
        'processcancelPay' => 'oChildGeneralAdmin',
    );

	//需要检查是否有保存权限的action
	private $needFinalPostCheck = array(
		'processcancelinvoice', 	//取消账单
		'geninvoicefromtask',		//从任务生成账单
		'processplanruntime',		//取消某一恢复账单生成任务
		'createtasks',				//生成账单任务
		'geninvoice',				//生成账单
	);
	private $validPost =false;

    public function beforeAction(CAction $action) {
        parent::beforeAction($action);
		$actionId = strtolower($action->getId());
        if ($schoolyear = Yii::app()->request->getParam('schoolyear', '')) {
            $this->childObj->schoolid = $this->getNextSchoolid($schoolyear);
        }
        if( Yii::app()->request->isPostRequest && in_array($actionId, $this->needFinalPostCheck) ) {
			$valid = Yii::app()->user->checkAccess('oChildGeneralAdmin');
			if(!$valid){
				if($actionId != 'geninvoice'){
					if (Yii::app()->request->isAjaxRequest) {
						$ret['state'] = 'fail';
						$ret['message'] = Yii::t("message", 'No permission');
						echo CJSON::encode($ret);
						Yii::app()->end();
					} else {
						Yii::app()->user->setFlash('error', Yii::t("message", "No permission"));
					}

				}
			}
			$this->validPost = $valid;
        }
        Yii::import('application.components.policy.*');
        $this->policyApi = new PolicyApi($this->childObj->schoolid);
        return true;
    }

	public function actionIndex($schoolyear=0)
	{
        $childid = $this->childId;
        Yii::import('application.components.policy.*');
        if ($childid){
            $policyApi = new PolicyApi($this->childObj->schoolid);
            $availableYears = PolicyApi::getAvailableYears($this->childObj->schoolid);
            $years[''] = Yii::t('global', 'Please Select');
            foreach ($availableYears as $year){
                $years[$year] = $year.' - '.($year+1).' 学年';
            }

            $childNextyear = ChildReserve::model()->countByAttributes(array('childid'=>$childid));
            if($childNextyear && count($years) == 2){
                $currentYear = end(array_keys($years)) + 1;
                $years[$currentYear] = $currentYear.' - '.($currentYear+1).' 学年';
            }

            $schoolPolicy = false;
            $plans = array();

            if($schoolyear){
                $paymentMappings = $GLOBALS['paymentMappings'];
                $_dbFeeType = array_flip($paymentMappings['dbFeeType']);
                $schoolPolicy = OA::getPolicy(OA::POLICY_PAY, $schoolyear, $this->childObj->schoolid);

                $criteria = new CDbCriteria();
                $criteria->compare('childid', $childid);
                $criteria->compare('startyear', $schoolyear);
                $planModel = MonthlyFeePlanner::model()->findAll($criteria);
                foreach ($planModel as $model){
                    $plans[$model->feetype] = Yii::t("payment", $paymentMappings['titleFeeType'][$_dbFeeType[$model->feetype]]);
                }
            }

            $this->render('index', array('child'=>$this->childObj, 'schoolyear'=>$schoolyear, 'years'=>$years, 'schoolPolicy'=>$schoolPolicy, 'plans'=>$plans));
        }
	}

    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['childid'])) {
            $params['childid'] = isset($_GET['childid']) ? $_GET['childid'] : '';
        }
        if (empty($params['schoolyear'])) {
            $params['schoolyear'] = isset($_GET['schoolyear']) ? $_GET['schoolyear'] : '';
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    /**
     * 根据公共FORM数据生成预览账单信息
     * @param	Object	$cmodel		公共FORM
     * @param	Object	$t			付款时限种类 年付、学期付、月付
     * @param	Object	$policy		收费政策实例
     * @param	Object	$childid	孩子id
     * @return	Object				Invoices数组，主键为费用类型：学费、餐费、校车费……
     */
    public function prepareViewModels($cmodel, $t, $policy, $childid){
        $result = array();

        $commenSettings = array(
            'language' => $cmodel->language,
            'startdate' => strtotime($cmodel->startdate),
            'enddate' => strtotime($cmodel->enddate),
            'childId' => $childid,
            'byType' => $t,
        );

        foreach( $cmodel->itemModels as $fkey=>$model ){
            if($model->feetype){
                $itemSettings = array(
                    'feeType' => $fkey,
                    'feeProgram' => $model->feeamount,
                    'discountId' => $model->discount_id,
                    'serviceDay' => $model->child_service_info,
                    'classId'    => $policy->getClassId($this->childId, $this->childObj->classid),
                    'project_num'=> $model->project_num,
                );

                $userInput = array_merge($commenSettings, $itemSettings);
                if ($t == 'PAYGROUP_MONTH'){
                    foreach ($policy->configs['TUITION_CALCULATION_BASE']['BY_MONTH']['MONTH_FACTOR'] as $monthkey=>$w){
                        $monthtimestamp = strtotime($monthkey.'01');
                        $startM = mktime(0, 0, 0, date('m', $userInput['startdate']), 1, date('Y', $userInput['startdate']));
                        if ( $startM <= $monthtimestamp ){
                            if ( $startM < $monthtimestamp ){
                                $userInput['startdate'] = $monthtimestamp;
                            }
                            $result[$fkey][$monthkey] = $policy->genPlanFromCommonSetting($userInput);
                        }
                    }
                }
                else {
                    $result[$fkey] = $policy->genInvoiceFromCommonSetting($userInput);
                }
            }
        };
        return $result;
    }

    /**
     * 根据预览账单的Formd得到准备保存成Invoice的model们
     * @param	Object	$postData	提交的Form数据
     * @return	Object				Invoice的Model们
     */
    public function prepareSaveModels($postData, $policy){
        $result = array();
        foreach($postData as $key => $val){
            $commonSetting = array(
                'startdate'=>$val['startdate'],
                'enddate'=>$val['enddate'],
                'byType'=>$val['fee_type'],
                'feeType'=>$val['payment_type'],
                'feeProgram'=>$val['amountid'],
                'childId'=>$this->childId,
                'discountId'=>isset($val['discount_id']) ? $val['discount_id'] :0,
                'serviceDay'=>$val['child_service_info'],
                'duetime'=>$val['duetime'],
                'classId'=>$policy->getClassId($this->childId, $this->childObj->classid),
                'title'=>$val['title'],
                'memo'=>$val['memo'],
                'project_num'=>$val['project_num'],
            );
            $result[$key] = $policy->genInvoiceFromCommonSetting($commonSetting);
        }
        return $result;
    }

    public function actionAsaInvoice($childid){
        $classid = $this->childObj->classid;
        $branchId = $this->childObj->schoolid;
        Yii::import('common.models.asainvoice.*');

        $criteria = new CDbCriteria();
        $criteria->compare('asainvoice.status', array(10, 20));
        $criteria->compare('asainvoice.childid', $childid);
        $criteria->with = 'asainvoice';
        $childModel = AsaInvoiceItem::model()->findAll($criteria);
        $invoiceInfo = array();
        $groupInfo = array();
        if($childModel){
            $groupIds = array();
            $return = array();
            foreach ($childModel as $val){
                $groupIds[] = $val->course_group_id;
                if($val->refund_status){
                    $return[] = $val->refund_status;
                }
            }

            $criteria = new CDbCriteria();
            $criteria->compare('id', $groupIds);
            $criteria->order = 'open_date_start DESC, id ASC';
            $courseGroupModel = AsaCourseGroup::model()->findAll($criteria);
            foreach ($courseGroupModel as $val){
                $groupInfo[] = array(
                    'id' => $val->id,
                    'title' => $val->getName(),
                );
            }
            $refund_info = array();
            if($return){
                $retundType = array(1=>'开课后退全部', 2=>'开课后退部分（退班）' ,3=>'特殊退费（不退班）');
                $mrthod = array(1=>'微信', 2=>'银行卡' ,3=>'课后课个人账户');
                $reason= array(1=>'课程质量不佳', 2=>'老师频繁更换' ,3=>'其它特殊原因');
                $criteria = new CDbCriteria();
                $criteria->compare('id', $return);
                $refundModel = AsaRefund::model()->findAll($criteria);

                foreach ($refundModel as $info){
                    if ($info->status > 5) {
                        continue;
                    }
                    $refund_info[$info->id][] = array(
                        'refund_class_count' => $info->refund_class_count,
                        'refund_total_amount' => $info->refund_total_amount,
                        'dropout_date' => date("Y-m-d", $info->dropout_date),
                        'refund_reason' => $reason[$info->refund_reason],
                        'refund_type' => $retundType[$info->refund_type],
                        'refund_method' => $mrthod{$info->refund_method},
                        'status' => $info->status,
                        'memo' => $info->memo,
                    );
                }
            }

            foreach ($childModel as $val){
                $invoiceInfo[$val->course_group_id][] = array(
                    'courseId' => $val->course_id,
                    'courseTitle' => $val->asacourse->getTitle(),
                    'class_count' => $val->class_count,
                    'unit_price' => $val->unit_price,
                    'invoice_status' => $val->asainvoice->status == 10 ? "未付款" : "款付清",
                    'actual_total' => $val->actual_total,
                    'refund_status' => ($val->refund_status) ? '有退费':'无退费',
                    'refund_id' => $val->refund_status,
                    'refund_info' => $refund_info && $refund_info[$val->refund_status] ? $refund_info[$val->refund_status] : array(),
                );
            }
        }

        $this->render('asainvoice', array(
            'invoiceInfo' => $invoiceInfo,
            'groupInfo' => $groupInfo,
            'childId' => $childid,
            'classid' => $classid,
            'branchId' => $branchId,
        ));
    }

	//生成账单
    public function actionGenInvoice($schoolyear=0, $t='')
    {
        $childid = $this->childId;

        $schoolid = $this->childObj->schoolid;
        $policy = new IvyPolicy("pay", $schoolyear, $schoolid);
        $policyApi = new PolicyApi($schoolid);

        $classid = $policy->getClassId($childid, $this->childObj->classid);
        if (!$classid && in_array($t, array('PAYGROUP_ANNUAL', 'PAYGROUP_SEMESTER', 'HOLIDAY_MONTH', 'FREE_LUNCH'))){
            echo '<div class="not_content_mini"><i></i>';
			echo Yii::t('child', 'Please :assignClassLink first before creating invoices.', array(':assignClassLink'=>CHtml::link(Yii::t('child','assign class'), array('//child/index/status', 'childid'=>$this->childId))));
            echo '</div>';
            Yii::app()->end();
        }

        $cmodel = new InvoiceCommonForm;
        $cmodel->initialItem($t, $policy);

        $cmodel->setScenario($t);
        if ( isset($_POST['InvoiceCommonForm']) ){
            $valid=true;
            $cmodel->attributes = $_POST['InvoiceCommonForm'];

            foreach (array_keys($_POST['InvoiceItemForm']) as $fkey):
                $model = new InvoiceItemForm;
                $model->attributes=$_POST['InvoiceItemForm'][$fkey];
                $model->feename=$fkey;
                $cmodel->itemModels[$fkey] = $model;
            endforeach;

            //验证用户输入
            $valid = $cmodel->validate();
            if($valid) {
                $annualdue = true;
				if (in_array($t, array(PAYGROUP_ANNUAL,PAYGROUP_SEMESTER))){
					$formatStartDate = date('Ym',strtotime($cmodel->startdate));
					$globalDiscount = null;
					if ($t ==PAYGROUP_SEMESTER && $formatStartDate<=$policy->configs[TIME_POINTS][1]){
						$globalDiscount = DISCOUNT_SEMESTER1;
						$payTypeTxt = '上学期付费';
					}elseif($t ==PAYGROUP_SEMESTER && $formatStartDate>$policy->configs[TIME_POINTS][1]){
						$globalDiscount = DISCOUNT_SEMESTER2;
						$payTypeTxt = '下学期付费';
					}elseif($t ==PAYGROUP_ANNUAL){
						$globalDiscount = DISCOUNT_ANNUAL;
						$payTypeTxt = '年付费';
					}
                    //学校多收费模式是否开启
                    if (isset($policy->configs[MORE_FEE_BY_SCHOOL]) && $policy->configs[MORE_FEE_BY_SCHOOL] === true){
                        //查询孩子的班级类型
                        $classtype = $policy->getSchoolFeeflag($childid);
                        $configs = $policy->configs[TUITION_CALCULATION_BASE][$classtype];
                    }else{
                        $configs = $policy->configs[TUITION_CALCULATION_BASE];
                    }
					$pannc = isset($configs[BY_MONTH][GLOBAL_DISCOUNT][$globalDiscount]) ? explode(':',$configs[BY_MONTH][GLOBAL_DISCOUNT][$globalDiscount]) : array();
					if (isset($pannc[2]) && (strtotime($cmodel->startdate) >= strtotime($pannc[2]))) {
						$annualdue = $pannc[2];
					}
				}

                $tuitioncheck = null;
                $previewModels = $this->prepareViewModels($cmodel, $t, $policy, $childid);
                if (in_array(FEETYPE_TUITION, array_keys($previewModels))){
                    $tuitioncheck = $policy->checkTuitionCreation($childid);
                }
                $viewfile = 'preinvoice';

                $html = $this->renderPartial($viewfile, array(
                    'imodel'=>$previewModels,
                    'tuitioncheck'=>$tuitioncheck,
                    'paymentMappings'=>$GLOBALS['paymentMappings'],
                    'annualdue'=>$annualdue,
                    'payTypeTxt'=>$payTypeTxt,
                ), true);
                $jsonOut = array(
                    "state" => $viewfile,
                    "data"  => $html
                );
                echo CJSON::encode($jsonOut);
                Yii::app()->end();
            }
        }

        if ( isset($_POST['Invoice'])){
			if( $this->validPost ){
				$valid = true;
				$toSaveInvoices = $this->prepareSaveModels($_POST["Invoice"], $policy);
				foreach($toSaveInvoices as $_invoice){
					$valid = $_invoice->validate() && $valid;
				}
				if(!$valid){
					$html = $this->renderPartial('preinvoice', array(
						'imodel'=>$toSaveInvoices,
					), true, true);
					$jsonOut = array(
						"state" => 'preinvoice',
						"data"  => $html
					);
					echo CJSON::encode($jsonOut);
					Yii::app()->end();
				}else{
					foreach($toSaveInvoices as $_key => $_invoice){
						$_invoice->duetime = strtotime($_invoice->duetime);
						$createdInvoices[$_key] = $policy->saveInvoice($_invoice, $_key);
						$createdInvoices[$_key] = $_invoice;
					}
					$html = $this->renderPartial('postInvoiceSave', array(
						'invoices'=>$createdInvoices,
					), true, true);
					$jsonOut = array(
						"state" => 'postInvoiceSave',
						"data"  => $html
					);
					echo CJSON::encode($jsonOut);
					Yii::app()->end();
				}
			}else{
				echo CJSON::encode(array("state"=>"fail","message"=>Yii::t("message","No permission") ));
				Yii::app()->end();
			}

        }

        $discounts = array();

        $amounts = $policyApi->getPaymentAmounts($schoolyear, array_keys($cmodel->itemConfig['INVOICE']), $t, $childid);
        if (in_array($t, array('PAYGROUP_ANNUAL','PAYGROUP_SEMESTER', 'PAYGROUP_MONTH', 'HOLIDAY_MONTH')) ){
            $discounts = PolicyApi::getDiscountList($schoolid);
            $bind_dis = $policy->getBindDiscount($childid);
            if (is_array($bind_dis) && $bind_dis){
                $discounts = array($bind_dis['discoundId']['id']=>CommonUtils::autoLang($bind_dis['discoundId']['title_cn'], $bind_dis['discoundId']['title_en']))+$discounts;
            }
        }

        $feeamounts = array();
        foreach ( array_keys($cmodel->itemConfig['INVOICE']) as $fee){
            if (isset($amounts[$fee])){
                foreach ($amounts[$fee] as $feeid=>$feea){
                    $feeamounts[$fee][$feeid]=$feea['amount'].' '. CommonUtils::autoLang($feea['cn_title'], $feea['en_title']);
                }
            }
        }

        # 取缔某些收费，此功能也可以加到上面的循环中。
        if(isset($policy->configs[$t]['EXCEPTS'])){
            foreach($policy->configs[$t]['EXCEPTS'] as $except){
                unset($feeamounts['FEETYPE_TUITION'][$except]);
            }
        }

        $html = $this->renderPartial('geninvoice', array(
            'paymentMappings'=>$GLOBALS['paymentMappings'],
            'feeamounts'=>$feeamounts,
            'schoolyear'=>$schoolyear,
            'cmodel'=>$cmodel,
            'discounts'=>$discounts,
            'childid'=>$childid,
            'bind_dis'=>$bind_dis,
        ), true);
        if ( isset($_POST['InvoiceCommonForm']) ){

            echo CJSON::encode(array(
                'state'=>'error',
                'data'=>$html,
            ));
            Yii::app()->end();
        }
        echo $html;
    }

    public function planHandler($childid=0, $schoolyear=0, $imodel=null){
    	$schoolId = $this->childObj->schoolid;
    	$classId = ($this->childObj->classid) ? $this->childObj->classid : 0;
    	$params = array(
    			'childId'=>$childid,
    			'classId'=>$classId,
    		);
    	$policy = new IvyPolicy(OA::POLICY_PAY, $schoolyear, $schoolId);

        foreach ($_POST['InvoiceItemForm'] as $fkey=>$fval):
            if ($fval['feetype']){
                $feetype = $GLOBALS['paymentMappings']['dbFeeType'][$fkey];
                $criteria = new CDbCriteria();
                $criteria->compare('childid', $childid);
                $criteria->compare('startyear', $schoolyear);
                $criteria->compare('feetype', $feetype);
                $pModel = MonthlyFeePlanner::model()->find($criteria);
                if ($pModel === null){
                    $pModel = new MonthlyFeePlanner;
                }
                $pModel->childid = $childid;
                $pModel->startyear = $schoolyear;
                $pModel->feetype = $feetype;
                $pModel->settings = serialize($_POST['InvoiceCommonForm']+$fval);
                $pModel->userid = Yii::app()->user->id;
                $pModel->updated = time();
                if (!$pModel->save()){
                    print_r($pModel->getErrors());die;
                }
                foreach ($imodel[$fkey] as $pkey=>$plan){
                    $plan->payment_type=$feetype;
                    $plan->planid=$pModel->id;
                    if (!$plan->runtime){
                        if (!$plan->save()){
                            print_r($plan->getErrors());die;
                        }
                    }
                }
            }
        endforeach;
    }

    /**
     *
     * @param type $schoolyear
     * @param type $childid
     * @return type
     */
    public function getPlaneds($childid=0, $schoolyear=0)
    {
        $planeds = array();
        $criteria = new CDbCriteria();
        $criteria->compare('t.childid', $childid);
        $criteria->compare('t.startyear', $schoolyear);
        $planned = MonthlyFeePlanner::model()->with(array('task'=>array('params'=>array(":schoolid"=>$this->childObj->schoolid))))->findAll($criteria);
        foreach ($planned as $pl){
            $planeds[$pl->feetype] = $pl;
        }
        return $planeds;
    }

	//查看已经制定的任务计划
    public function actionTask($schoolyear=0)
    {
        $childid = $this->childId;
        $schoolId = $this->childObj->schoolid;
        $policy = new IvyPolicy(OA::POLICY_PAY, $schoolyear, $schoolId);

        $classid = $policy->getClassId($childid, $this->childObj->classid);
        if (!$classid){
            echo '<div class="not_content_mini"><i></i>';
			echo Yii::t('child', 'Please :assignClassLink first before creating invoices.', array(':assignClassLink'=>CHtml::link(Yii::t('child','assign class'), array('//child/index/status', 'childid'=>$this->childId))));
            echo '</div>';
            die;
        }

        $planeds = $this->getPlaneds($childid, $schoolyear);

        $clientScript = Yii::app()->getClientScript();
        $clientScript->registerCoreScript('jquery');
        $clientScript->registerScriptFile(Yii::app()->request->baseUrl.'/js/dev/head.js');

        $this->renderPartial('task', array(
            'paymentMappings'=>$GLOBALS['paymentMappings'],
            'planeds'=>$planeds,
            'policy'=>$policy,
        ), false);
    }

	/**
	 * Summary 设置任务属性并生成任务
	 * @param	Number	$schoolyear	Description
	 * @param	String	$fee		Description
	 * @return	Object				Description
	 */
    public function actionCreateTasks($schoolyear=0, $fee='')
    {
        $childid = $this->childId;
        $this->layout='//layouts/dialog';

        $schoolId = $this->childObj->schoolid;
        $policy = new IvyPolicy(OA::POLICY_PAY, $schoolyear, $schoolId);
        $policyApi = new PolicyApi($schoolId);

        $cmodel = new InvoiceCommonForm;
        $model = new InvoiceItemForm;

        $cmodel->policy = $policy;

        $cmodel->setScenario('PAYGROUP_MONTH');
        if ( isset($_POST['InvoiceCommonForm']) && $this->validPost ){
            $valid=true;
            $cmodel->attributes = $_POST['InvoiceCommonForm'];

            $model = new InvoiceItemForm;
            $model->attributes=$_POST['InvoiceItemForm'][$fee];
            $cmodel->itemModels[$fee] = $model;

            //验证用户输入
            $valid = $cmodel->validate();
            $this->addMessage('data', $cmodel->getErrors());
            if($valid) {
                $previewModels = $this->prepareViewModels($cmodel, 'PAYGROUP_MONTH', $policy, $childid);
                $this->planHandler($childid, $schoolyear, $previewModels);
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('callback', 'cb');
            }
            else {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message','Failed!'));
            }
            $this->showMessage();
        }

        $discounts = array();

        $amounts = $policyApi->getPaymentAmounts($schoolyear, array($fee), 'PAYGROUP_MONTH', $childid);

        if ($fee == 'FEETYPE_TUITION'){
            $discounts = PolicyApi::getDiscountList($schoolId);
            $bind_dis = $policy->getBindDiscount($childid);
            if (is_array($bind_dis) && $bind_dis){
                $discounts = array($bind_dis['discoundId']['id']=>CommonUtils::autoLang($bind_dis['discoundId']['title_cn'], $bind_dis['discoundId']['title_en']))+$discounts;
            }
        }

        $feeamounts = array();
        foreach ($amounts[$fee] as $feeid=>$feea){
            $feeamounts[$fee][$feeid]=$feea['amount'].' '. CommonUtils::autoLang($feea['cn_title'], $feea['en_title']);
        }

        $this->render('tasksetting', array(
            'cmodel'=>$cmodel,
            'model'=>$model,
            'fee'=>$fee,
            'feeamounts'=>$feeamounts,
            'discounts'=>$discounts,
            'bind_dis'=>$bind_dis,
        ));
    }

	/**
	 * Summary 取消/恢复账单任务
	 * @param	Number	$schoolyear	Description
	 * @return	Object				Description
	 */
    public function actionProcessPlanRuntime($schoolyear=0)
    {
		if ( Yii::app()->request->isPostRequest && $this->validPost ){
			$id = Yii::app()->request->getParam('id', 0);
			$model=MonthlyFeeTask::model()->findByPk($id);
			if ($model->plantime){
				$model->plantime=0;
			}
			else {
				$model->plantime=PolicyApi::getPlandate($model->startdate);
			}
			$model->save();
            $this->addMessage('state', 'success');
            $this->showMessage();
		}
    }

    /**
     * 某月计划生成学费账单
     * @param int $childid
     * @param int $taskId
     */
    public function actionGenInvoiceFromTask($schoolyear=0)
    {
    	if ( $schoolyear && Yii::app()->request->isPostRequest )
    	{
			if($this->validPost){
				$taskId = Yii::app()->request->getParam('taskId', 0);

				$schoolId = $this->childObj->schoolid;
				$policy = new IvyPolicy(OA::POLICY_PAY,$schoolyear,$schoolId);
				$taskModel = MonthlyFeeTask::model()->findByPk($taskId);
				if (!empty($taskModel))
				{
					if ($taskModel->childid == $this->childId && $taskModel->schoolid == $schoolId)
					{
						$status = false;
						global $paymentMappings;
						$dbFeeType = $paymentMappings['dbFeeType'];
						if ($taskModel->payment_type == $dbFeeType[FEETYPE_TUITION])
						{
							  $status = $policy->checkTuitionCreation($this->childId);
						}
						if ( ($status=== true && $taskModel->payment_type == $dbFeeType[FEETYPE_TUITION]) || in_array($taskModel->payment_type, array($dbFeeType[FEETYPE_LUNCH],$dbFeeType[FEETYPE_SCHOOLBUS])))
						{
							$invoiceModel = $policy->genPlanModelSetting($taskModel, Yii::app()->user->id);
							$dbFeeType = array_flip($dbFeeType);
							$resultModel = $policy->saveInvoice($invoiceModel,$dbFeeType[$taskModel->payment_type]);
							if (!$resultModel->getErrors())
							{
								$taskModel->runtime = date('Ymd',time());
								$taskModel->invoiceid = ($taskModel->invoiceid) ? $taskModel->invoiceid.','.$resultModel->invoice_id : $resultModel->invoice_id;
								$taskModel->save();
							}
							$this->addMessage('state', 'success');
						}
						else
						{
							$this->addMessage('state', 'fail');
							$this->addMessage('message', RC::getTip($status));
						}
					}
                    else {
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', '请检查孩子和学校是否于计划相同');
                    }
					$this->showMessage();
				}
			}
    	}
    }

	//取消账单，需孩子管理权限
    public function actionprocessCancelInvoice()
    {
		if(Yii::app()->request->isPostRequest){
			if( $this->validPost){
				$invoiceid = Yii::app()->request->getParam('id');
				$ret = PolicyApi::cancelInvoice($invoiceid);
				if ($ret == PolicyApi::E_CANCEL_INVOICE_SUCCESS){
					$this->addMessage('state', 'success');
				}
				else{
					$this->addMessage('state', 'fail');
					$this->addMessage('message', Yii::t('message','System Error'));
				}
				$this->showMessage();
			}
		}
    }

	//查看账单，无特殊权限
    public function actionViewInvoice($invoiceid=0){
        $childid = $this->childId;
        $this->layout='//layouts/dialog';
        $this->dialogWidth = 1000;
        $this->render('viewinvoice', array("childId"=>$childid, "invoiceId"=>$invoiceid));
    }


	//打印账单，无特殊权限限制
    public function actionPrintInvoice($invoice_id=0, $schoolyear=0)
    {
        $childid = $this->childId;
        $this->layout='//layouts/blank';
        $this->dialogWidth = 'auto';

        $lang = Yii::app()->request->getParam('lang', Yii::app()->language);
        Yii::import('common.models.ivyLibrary.BorrowingCard');

        $invoice_info = null;
        $invoice_cri = new CDbCriteria();
        $invoice_cri->addInCondition('t.status', array(10, 30));
        $invoice_cri->compare('t.childid', $childid);
        $invoice_cri1 = new CDbCriteria();
        $invoice_cri1->compare('t.invoice_id', $invoice_id);
        $invoice_cri->mergeWith($invoice_cri1, false);
        $invoice_cri->index = "invoice_id";
        $invoice_info = Invoice::model()->findAll($invoice_cri);

        $schoolId = $invoice_info[$invoice_id]->schoolid;
        $policy = new IvyPolicy(OA::POLICY_PAY, $schoolyear, $schoolId);
        $policyAip = new PolicyApi($schoolId);

        $class_id = $policy->getClassId($childid, $invoice_info[$invoice_id]->classid);

        $school_info = Branch::model()->findByPk($schoolId);
        $calendar_id = null;
        $school_logo_url = '';

        if ($school_info->schcalendar) {
            $calendar_id = $school_info->schcalendar;
        }
        if ($school_info->logo) {
            $school_logo_url = Yii::app()->params["OAUploadBaseUrl"]."/branch/thumbs/".$school_info->logo;
        }
        $class_info = IvyClass::model()->findByPk($class_id);
        $credit = empty($this->childObj->credit) ? '0.00' : $this->childObj->credit;
        $deposit = DepositHistory::model()->getChildDepositBalance($childid);
        $school_desc = $school_info->getPrintHeader($lang);
        $nf = new CNumberFormatter('zh-cn');

        $base_info = array(
            "child_id" => $childid,
            "class_id" => $class_id,
            "school_id" => $schoolId,
            "child_name" => $this->childObj->getChildName(),
            "class_name" => empty($class_info->title) ? null : $class_info->title,
            "credit" => $nf->format('#,##0.00', $this->childObj->credit),
            "school_desc" => $school_desc,
            "school_logo_url" => $school_logo_url,
            "deposit" => $nf->format('#,##0.00', $deposit)
        );



        $invoice_list = null;
        $unpaid_total = 0;
        $paid_total = 0;
        if (!empty($invoice_info)) {
            $invoiceConfig['fee_type'] = $policyAip->getInvoiceType();

            foreach ($invoice_info as $ik => $invoice) {
                //到期日期
                if (isset($invoice->duetime)) {
                    $invoice->dueDate = OA::formatDateTime($invoice->duetime);
                }
                // 账单区间
                if (!empty($invoice->startdate) && !empty($invoice->enddate) && empty($invoice->installment)) {
                    $invoice->durDate = OA::formatDateTime($invoice->startdate) . " - " . OA::formatDateTime($invoice->enddate);
                } else if (!empty($invoice->installment)) {
                    $invoice->durDate = $invoice->installment;
                } else {
                    $invoice->durDate = 'N/A';
                }
                // 应付金额
                $invoice->dueAmount = Invoice::model()->renderDueAmount($invoice, null);
                // 使用的押金 金额
                $invoice->depositAmount = Invoice::model()->getDepositAmount($invoice);
                // 2013.7.25 Invoice->amount 已经是减过押金的钱数
//                if ($invoice->depositAmount > 0) {
//                    $invoice->dueAmount -= $invoice->depositAmount;
//                }

                // 账单类型 学费 校车费
                if (isset($invoiceConfig['fee_type'][$invoice->payment_type])) {
                    $pt = $invoiceConfig['fee_type'][$invoice->payment_type];
                    $invoice->paymentTypeTitle = $policyAip->renderFeeType($invoice->payment_type, $lang);

                    // 如果是 课外活动 获取 课外活动的名称
                    if ($pt == $invoiceConfig['fee_type']['afterschool']) {
                        $invoice->paymentTypeTitle = '';
                    } else if ($pt == $invoiceConfig['fee_type']['library_card']) {
                        // 如果是 借书卡 加上 借书卡的卡名称
                        $bc_info = BorrowingCard::model()->findByPk($invoice->fee_other_id);
                        if (!empty($bc_info)) {
                            $invoice->paymentTypeTitle .= ' - ' . $bc_info->cardname;
                        }
                    }
                }
                // 如果是 折扣账单
                if (!empty($invoice->discount_id)) {
                    if (!empty($invoice->discount)) {
                        // 获取折扣百分比
                        $discount_info = $invoice->discount;
                        $invoice->discountPercent = floor($discount_info->discount) . "％";
                    }
                    // 获取 折扣名称
                    if (!empty($discount_info->discountTitle)) {
                        $invoice->discountTitle = CommonUtils::autoLang($discount_info->discountTitle->title_cn, $discount_info->discountTitle->title_en);
                    }
                }
                $invoice_list[$ik] = $invoice;

                if ($invoice->invoice_id == $invoice_id)
                    $unpaid_total += floatval($invoice->dueAmount);
                $paid_total += floatval($invoice->amount);
            }
            unset($invoice_info);
        }

        $bank_info = BranchVar::model()->bankInfo($schoolId);

        Yii::app()->clientScript->registerCoreScript('jquery');
        Yii::app()->clientScript->registerScriptFile( Yii::app()->request->baseUrl.'/themes/base/js/jquery.jPrintArea.js' );
        Yii::app()->clientScript->registerCssFile( Yii::app()->theme->baseUrl.'/css/admin_print.css' );

        $view_data = array(
            'base_info' => $base_info,
            'invoice_list' => $invoice_list,
            'unpaid_total' => $unpaid_total,
            'paid_total' => $paid_total,
            'bank_info' => $bank_info,
            'invoice_id' => $invoice_id,
            'lang'=>$lang,
            'school_info' => $school_info,
        );
        $this->render('printInvoice', $view_data);
    }


    //打印收据，无特殊权限限制
    public function actionPrintReceipt($invoice_id=0, $schoolyear=0)
    {
        $childid = $this->childId;
        $this->layout='//layouts/blank';
        $this->dialogWidth = 'auto';

        $lang = Yii::app()->request->getParam('lang', Yii::app()->language);
        Yii::import('common.models.ivyLibrary.BorrowingCard');

        $schoolId = $this->childObj->schoolid;
        $policy = new IvyPolicy(OA::POLICY_PAY, $schoolyear, $schoolId);
        $policyAip = new PolicyApi($schoolId);

        $class_id = $policy->getClassId($childid, $this->childObj->classid);

        $school_info = Branch::model()->findByPk($schoolId);
        $calendar_id = null;
        $school_logo_url = '';

        if ($school_info->schcalendar) {
            $calendar_id = $school_info->schcalendar;
        }
        if ($school_info->logo) {
            $school_logo_url = Yii::app()->params["OAUploadBaseUrl"]."/branch/thumbs/".$school_info->logo;
        }
        $class_info = IvyClass::model()->findByPk($class_id);
        $credit = empty($this->childObj->credit) ? '0.00' : $this->childObj->credit;
        $deposit = DepositHistory::model()->getChildDepositBalance($childid);
        $school_desc = $school_info->getPrintHeader($lang);
        $nf = new CNumberFormatter('zh-cn');

        $base_info = array(
            "child_id" => $childid,
            "class_id" => $class_id,
            "school_id" => $schoolId,
            "child_name" => $this->childObj->getChildName(),
            "class_name" => empty($class_info->title) ? null : $class_info->title,
            "credit" => $nf->format('#,##0.00', $this->childObj->credit),
            "school_desc" => $school_desc,
            "school_logo_url" => $school_logo_url,
            "deposit" => $nf->format('#,##0.00', $deposit)
        );
        // 显示选取账单最近的六条记录
        $invoice_info = null;
        $invoice_cri = new CDbCriteria();
        $invoice_cri->addInCondition('t.status', array(20));
        $invoice_cri->compare('t.inout', Invoice::INVOICE_INOUT_IN);
        $invoice_cri->compare('t.invoice_id', '<=' . $invoice_id);
        $invoice_cri->compare('t.payment_type', '<>uniform');
        $invoice_cri->group = 't.invoice_id desc';
        $invoice_cri->limit = '6';
        $invoice_cri->compare('t.childid', $childid);
        // $invoice_cri1 = new CDbCriteria();
        // $invoice_cri1->compare('t.invoice_id', $invoice_id);
        // $invoice_cri->mergeWith($invoice_cri1, false);
        $invoice_info = Invoice::model()->findAll($invoice_cri);

        $invoice_list = null;
        $paid_total = 0;
        if (!empty($invoice_info)) {
            $invoiceConfig['fee_type'] = $policyAip->getInvoiceType();

            foreach ($invoice_info as $ik => $invoice) {
                // 账单区间
                if (!empty($invoice->startdate) && !empty($invoice->enddate) && empty($invoice->installment)) {
                    $invoice->durDate = OA::formatDateTime($invoice->startdate) . " - " . OA::formatDateTime($invoice->enddate);
                } elseif (!empty($invoice->installment)) {
                    $invoice->durDate = $invoice->installment;
                } else {
                    $invoice->durDate = 'N/A';
                }
                // 应付金额
                $invoice->dueAmount = Invoice::model()->renderDueAmount($invoice, null);
                // 使用的押金 金额
                // $invoice->depositAmount = Invoice::model()->getDepositAmount($invoice);
                $invoice->depositAmount = $invoice->original_amount - $invoice->amount;

                // 账单类型 学费 校车费 入学材料费
                if (isset($invoiceConfig['fee_type'][$invoice->payment_type])) {
                    $pt = $invoiceConfig['fee_type'][$invoice->payment_type];
                    $invoice->paymentTypeTitle = $policyAip->renderFeeType($invoice->payment_type, $lang);

                    // 如果是 课外活动 获取 课外活动的名称
                    if ($pt == $invoiceConfig['fee_type']['afterschool']) {
                        $invoice->paymentTypeTitle = '';
                    } elseif ($pt == $invoiceConfig['fee_type']['library_card']) {
                        // 如果是 借书卡 加上 借书卡的卡名称
                        $bc_info = BorrowingCard::model()->findByPk($invoice->fee_other_id);
                        if (!empty($bc_info)) {
                            $invoice->paymentTypeTitle .= ' - ' . $bc_info->cardname;
                        }
                    } elseif ($pt == $invoiceConfig['fee_type']['registration']) {
                        $invoice->paymentTypeTitle = '';
                    }
                }
                // 如果是 折扣账单
                if (!empty($invoice->discount_id)) {
                    if (!empty($invoice->discount)) {
                        // 获取折扣百分比
                        $discount_info = $invoice->discount;
                        $invoice->discountPercent = floor($discount_info->discount) . "％";
                    }
                    // 获取 折扣名称
                    if (!empty($discount_info->discountTitle)) {
                        $invoice->discountTitle = CommonUtils::autoLang($discount_info->discountTitle->title_cn, $discount_info->discountTitle->title_en);
                    }
                }
                if ($invoice->invoice_id == $invoice_id){
                    $paid_total += floatval($invoice->amount);
                }
                $invoice_list[$ik] = $invoice;
            }
            unset($invoice_info);
            // array_splice($invoice_list, 5);
        }

        Yii::app()->clientScript->registerCoreScript('jquery');
        Yii::app()->clientScript->registerScriptFile( Yii::app()->request->baseUrl.'/themes/base/js/jquery.jPrintArea.js' );
        Yii::app()->clientScript->registerCssFile( Yii::app()->theme->baseUrl.'/css/admin_print.css' );
        $view_data = array(
            'base_info' => $base_info,
            'invoice_list' => $invoice_list,
            'paid_total' => $paid_total,
            'invoice_id' => $invoice_id,
            'lang'=>$lang,
            'school_info' => $school_info,
        );
        $this->render('printReceipt', $view_data);
    }


    public function actionRenewTask()
    {
        $this->layout='//layouts/dialog';
        $this->dialogWidth = 600;

        Yii::import('common.models.invoice.*');

        $id = Yii::app()->request->getParam('id', 0);

        $model = MonthlyFeeTask::model()->findByPk($id);
        if ($model->invoiceid != 0){
            $iArr = explode(',', $model->invoiceid);
            $criteria = new CDbCriteria();
            $criteria->compare('invoice_id', $iArr);
            $criteria->index='invoice_id';
            $items = Invoice::model()->findAll($criteria);
        }

        if (Yii::app()->request->isPostRequest && Yii::app()->request->isAjaxRequest){
            $iarr = array();
            if ($items){
                foreach ($items as $item){
                    if ($item->status == Invoice::STATS_UNPAID){
                        if (PolicyApi::cancelInvoice($item->invoice_id)){
                            $this->addMessage('state', 'fail');
                            $this->addMessage('message', '作废失败');
                            $this->showMessage();
                        }
                    }
                    else{
                        $iarr[$item->invoice_id] = $item->invoice_id;
                    }
                }
            }
            $model->runtime = 0;
            $model->invoiceid = $iarr ? implode(',', $iarr) : 0;
            if ($model->save()):
                $this->addMessage('state', 'success');
                $this->addMessage('message', '成功');
                $this->addMessage('callback', 'cb');
            else:
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '失败');
            endif;
            $this->showMessage();
        }

        $this->render('renewtask', array('items'=>$items, 'id'=>$id));
    }

    //查看各种财务状况
    public function actionFinance($childid=0, $t='balance')
    {
        Yii::import('common.models.calendar.*');
        $siblings = array();
		$_GET['t'] = $t;
        $cMod = null;
        $dataProvider = null;
        switch ($t)
        {
            case 'invoice':
			default:
                $itype = Yii::app()->request->getParam('iType', null);
                $invalid = Yii::app()->request->getParam('invalid', null);
                // $cond = 't.childid=:childid AND t.inout=:inout AND t.status != :change_archived';
                $cond = 't.childid=:childid AND t.inout=:inout';
                if ($itype)
                    $cond .= " and t.payment_type in ('".implode("','", $itype)."')";
                if (!$invalid) {
                    $cond .= " and t.status != 99";
                    $cond .= " and t.status != " . Invoice::STATS_CHANGE_ARCHIVED;
                }

				$dataProvider = new CActiveDataProvider('Invoice', array(
					'criteria' => array(
						'condition' => $cond,
                        // 'params' => array("childid"=>$this->childId, ":inout"=>'in', ':change_archived'=>Invoice::STATS_CHANGE_ARCHIVED),
						'params' => array("childid"=>$this->childId, ":inout"=>'in'),
//						'order' => 't.timestamp DESC',
						'with' => 'workflowRefund',
					),
					'pagination'=>array(
						'pageSize'=>20,
					),
                    'sort' => array(
                        'defaultOrder' => 't.timestamp desc',
                    ),
				));
            break;
			case 'credit':
				$dataProvider = new CActiveDataProvider('ChildCredit', array(
					'criteria' => array(
						'condition' => 't.childid=:childid',
						'params' => array("childid"=>$this->childId),
						'order' => 't.updated_timestamp DESC',
					),
					'pagination'=>array(
						'pageSize'=>20,
					),
				));
			break;
			case 'lunch':
				$dataProvider = new CActiveDataProvider('RefundLunch', array(
					'criteria' => array(
						'condition' => 't.childid=:childid',
						'params' => array("childid"=>$this->childId),
						'order' => 't.target_date DESC',
						'with' => array('classInfo', 'schoolInfo')
					),
					'pagination'=>array(
						'pageSize'=>20,
					),
				));
			break;
			case 'balance':
				$depositList = PolicyApi::getDepositAmount($this->childId,0,true);
                if($this->childObj->credit > 0.01)
                    $siblings = PolicyApi::getSiblings($this->childId);
                $criteria = new CDbCriteria();
                $criteria->compare('branchid', $this->childObj->schoolid);
                $criteria->compare('is_selected', 1);
                $cMod = CalendarSchool::model()->find($criteria);
			break;
			case 'fapiao':
				$dataProvider = new CActiveDataProvider('InvoiceTransaction', array(
					'criteria' => array(
						'condition' => 't.childid=:childid AND t.inout=:inout AND transactiontype !=:transactiontype',
						'params' => array("childid"=>$this->childId, ":inout"=>'in', ":transactiontype"=> '40'),
						'order' => 't.timestampe DESC',
					),
					'pagination'=>array(
						'pageSize'=>20,
					),
				));
			break;
        }
        $this->render('finance', array("dataProvider"=>$dataProvider, "depositList"=>$depositList, "t"=>$t, 'siblings'=>$siblings, 'cMod'=>$cMod));
    }

    public function invoiceLink($data, $row)
    {
        if($data->invoice_id){
            echo CHtml::link(Yii::app()->controller->policyApi->renderFeeType($data->itemname), array("/child/invoice/viewInvoice", "invoiceid"=>$data->invoice_id), array('class'=>'J_dialog'));
        }
        else{
            echo Yii::app()->controller->policyApi->renderFeeType($data->itemname);
        }
    }

    //POS机支付 无需权限控制
	public function actionUnpaid($t='pos'){
		$yeePayCfg = CommonUtils::LoadConfig('CfgYeePayGlobal');
		$wxPayCfg = CommonUtils::LoadConfig('CfgWxPayGlobal');
        $schoolid = $this->childObj->schoolid;
        $reserveSchoolid = $this->getNextReserveSchoolid($this->childId);
        // 判断下学年分班的学校与当前学校是否相同
        $transferSchool = fasle;
        $nextSchool = Yii::app()->request->getParam('nextSchool', '');
        if ($reserveSchoolid != $this->childObj->schoolid) {
            $transferSchool = true;
            if ($nextSchool != 'nextSchool') {
                $schoolid = $this->childObj->schoolid;
            } else{
                $schoolid = $reserveSchoolid;
            }

        }

		$POSenabled = ( $t == 'pos' && isset($yeePayCfg[$schoolid])) ? true : false;
		$wxPayenabled = ( $t == 'micropay' && isset($wxPayCfg[$schoolid])) ? true : false;

		Yii::import('common.models.invoice.*');

		$usePartial = Yii::app()->request->getParam('usePartial','');
		$usePartial = ($usePartial == 'usePartial') ? true : false;

		$dataProvider = new CActiveDataProvider('Invoice', array(
			'criteria' => array(
				'condition' => 't.schoolid=:schoolid AND t.childid=:childid AND t.inout=:inout AND t.status in(10, 30) AND t.payment_type != :preschool_subsidy',
				'params' => array("childid"=>$this->childId, ":inout"=>'in',':preschool_subsidy'=>'preschool_subsidy', ':schoolid'=>$schoolid),
				'order' => 't.timestamp DESC',
				'with' => 'paidSum',
			),
			'pagination'=>array(
				'pageSize'=>2000,
			),
		));

        $items = array();
        if($t == 'micropay'){
            Yii::import('common.models.wxpay.*');
            $criteria = new CDbCriteria();
            $criteria->compare('schoolid', $schoolid);
            $criteria->compare('childid', $this->childId);
            $criteria->compare('type', 'MICROPAY');
            $criteria->compare('status', 0);
            $criteria->compare('order_time', '>'.strtotime('-30 day'));
            $criteria->order='order_time desc';
            $items = WechatPayOrder::model()->findAll($criteria);
        }

		$this->render('unpaid', array(
            "POSenabled"=>$POSenabled,
            "wxPayenabled"=>$wxPayenabled,
            "dataProvider"=>$dataProvider,
            'usePartial'=>$usePartial,
            't'=>$t,
            'items'=>$items,
            'reserveSchoolid'=>$reserveSchoolid,
            'transferSchool'=>$transferSchool,
            'nextSchool'=>$nextSchool,
            ));
	}

    //使用个人账户
    public function actionProcessUseCredit()
    {
        if (Yii::app()->request->isPostRequest && Yii::app()->request->isAjaxRequest){
            $id = Yii::app()->request->getParam('id', 0);
            $amount = Yii::app()->request->getParam('amount', 0);
            if ($amount <= 0) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '金额必须大于零');
                $this->showMessage();
            }
            Yii::import('common.models.invoice.*');

            $model = Invoice::model()->findByPk($id);
			$ret = $this->policyApi->Pay(array($model), InvoiceTransaction::TYPE_USERCREDIT, $amount);
            if ( !$ret ) {
                $this->addMessage('state', 'success');
            }
            else {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', $ret);
            }
            $this->showMessage();
        }
    }

    /**
	 * 将未付账单（包括部分付清）选中之后提交到这里生成账单信息
	 * @return	Object		Description
	 */
	public function actionProcessYeePayOrderCreation(){
		if (Yii::app()->request->isPostRequest && Yii::app()->request->isAjaxRequest){
			Yii::import('common.models.invoice.*');
			Yii::import('common.models.yeepay.*');
			extract($_POST);
			//$posInvoiceId_cid 账单id;
			//$finalAmount 账单id=>j金额;
			//$usePartial 是否使用分拆账单
			//$invoiceCount 选中的账单数量
			//$invoiceTotalAmount 选中的账单数量的金额

			sort($posInvoiceId_cid);
			$invoices = Invoice::model()->with('paidSum')->findAllByPk($posInvoiceId_cid);

			$yeePayCfg = CommonUtils::LoadConfig('CfgYeePayGlobal');
			//print_r($yeePayCfg);

			$canProcced = true;
			$count = $totalAmount = 0;

            if ($usePartial && count($invoices) !== 1){
                $canProcced = false;
            }

			foreach($invoices as $invoice){
				//账单状态已经不是待付款状态
				if(!in_array($invoice->status, array(Invoice::STATS_UNPAID, Invoice::STATS_PARTIALLY_PAID))){
					$canProcced = false;
					break;
				}
				//需付款于数据库中j计算出的需付款金额不一致
                if (!$usePartial){
                    if(!isset($finalAmount[$invoice->invoice_id]) || ( OA::formatMoney($finalAmount[$invoice->invoice_id]) != (OA::formatMoney($invoice->amount - $invoice->paidSum)) ) ){
                        $canProcced = false;
                        break;
                    }
                }
                else {
                    if ( $finalAmount[$invoice->invoice_id] >= ($invoice->amount - $invoice->paidSum) ){
                        $canProcced = false;
                        break;
                    }
                }
				$count++;
				$totalAmount += $finalAmount[$invoice->invoice_id];
			}
			if( ($invoiceCount != $count) || abs($invoiceTotalAmount - $totalAmount) > 0.001 ){
				$canProcced = false;
			}
			if(!$canProcced){
				$this->addMessage('state','fail');
				$this->addMessage('message',Yii::t("payment", "Page expired, please reload."));
				$this->showMessage();
			}

			$orderPrefix = strval($yeePayCfg[$this->childObj->schoolid]['number_code']) . sprintf('%06s', $posInvoiceId_cid[0]);
//			$orderId = YeepayOrder::model()->genOrderID($orderPrefix);
//			echo $orderId;

            $yeeOrder = new YeepayOrder;
            $yeeOrder->orderId = $yeeOrder->genOrderID($orderPrefix);
            $yeeOrder->payable_amount = $totalAmount;
            $yeeOrder->rb_BankId = 'POS-NET';
            $yeeOrder->schoolId = $invoices[0]->schoolid;
            $yeeOrder->childId = $this->childId;
            $yeeOrder->operatorId = Yii::app()->user->id;
            $yeeOrder->payment_method = InvoiceTransaction::TYPE_POS;
            $yeeOrder->status = 0;
            $yeeOrder->orderTime = time();
            $yeeOrder->updateTime = time();
            $yeeOrder->save();

            foreach( $posInvoiceId_cid as $key){
                $model = new YeepayOrderItem;
                $model->it_orderId = $yeeOrder->orderId;
                $model->status = 0;
                $model->orderTime = time();
                $model->updateTime = time();
                $model->invocieId = $key;
                $model->payable_amount = $finalAmount[$key];
                $model->save();
            }

            $this->addMessage('state', 'success');
            $this->addMessage('data', $yeeOrder->orderId);
            $this->showMessage();
		}
	}

	//提交到易宝，生成POS机账单
    public function actionPostYeePay($orderId='')
    {
        $this->layout='//layouts/dialog';
        $this->dialogWidth = 600;
        Yii::import('common.models.yeepay.*');
        Yii::import('common.components.yeepay.*');

        $yeeOrder = YeepayOrder::model()->findByPk($orderId);
        if ($yeeOrder->status == 0){
            $yeePay = new YeepayCommon;
            $yeePay->init($this->childObj->schoolid);
            $url = $yeePay->getReqUrlOnline();
            $ret = array(
                "p0_Cmd"=>$yeePay->getP0Cmd(),
                "p1_MerId"=>$yeePay->getP1MerId(),
                "p2_Order"=>$yeeOrder->orderId,
                "p3_Amt"=>$yeeOrder->payable_amount,
                "p4_Cur"=>$yeePay->getP4Cur(),
                "p5_Pid"=>'',
                "p6_Pcat"=>'',
                "p7_Pdesc"=>'',
                "p8_Url"=>$yeePay->getP8Url(),
                "p9_SAF"=>$yeePay->getP9Saf(),
                "pd_FrpId"=>'POS-NET',
                "pr_NeedResponse"=>$yeePay->getPrNeedResponse(),
                "hmac"=>$yeePay->getReqHmacString($yeeOrder->orderId, $yeeOrder->payable_amount, '', '', '', '', 'POS-NET'),
            );

            $this->render('postyeepay', array('url'=>$url, 'ret'=>$ret));
        }
    }

    //操作发票信息
    public function actionProcessSavefapiao()
    {
        if(Yii::app()->request->isPostRequest){
            $id             = Yii::app()->request->getParam('id', 0);
            $number         = Yii::app()->request->getParam('number', '');
            $title          = Yii::app()->request->getParam('title', 0);
            $savetobasic    = Yii::app()->request->getParam('savetobasic', 0);

            Yii::import('common.models.invoice.*');

            $model = InvoiceTransaction::model()->findByPk($id);
            $model->invoice_number = $number;
            $model->invoice_title = $title;
            if ($model->save()){
                if ($savetobasic){
                    ChildProfileBasic::model()->updateByPk($this->childId, array('invoice_title'=>$title));
                }
                $this->addMessage('state', 'success');
                $this->addMessage('data', array('number'=>$model->invoice_number, 'title'=>$model->invoice_title));
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '保存失败');
            }
            $this->showMessage();
        }
    }

    //处理现金部分付清
    public function actionProcessPartialCashPay()
    {
        if (Yii::app()->request->isPostRequest && Yii::app()->request->isAjaxRequest){
            $id = Yii::app()->request->getParam('id', 0);
            $amount = Yii::app()->request->getParam('amount', 0);

            Yii::import('common.models.invoice.*');

            $model = Invoice::model()->findByPk($id);

            if ($amount > 0.001 && $amount < ($model->amount - $model->paidSum)){
                if($this->childObj->schoolid == $this->specialSchool){
                    $this->addMessage('state', 'fail');
                    $payType = isset($_POST['transactiontype']) ? $_POST['transactiontype'] : '';
                    $paydate = isset($_POST['paydate']) ? $_POST['paydate'] : '';
                    if(!in_array($payType, array_keys($this->getDsPayType()))){
                        $this->addMessage('message', '请选择付款类型');
                        $this->showMessage();
                    }
                    if($this->payDateToggle == true && empty($paydate)){
                        $this->addMessage('message', '请选择付款日期');
                        $this->showMessage();
                    }
                    else{
                        $model->payDate = strtotime($paydate);
                    }
                }
                else{
                    $payType = InvoiceTransaction::TYPE_CASH;
                }
                if ( !$this->policyApi->Pay(array($model), $payType, $amount ) ) {
                    $this->addMessage('state', 'success');
                }
                else {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '付款失败');
                }
            }
            else {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '输入金额请小于账单需支付金额并且大于0');
            }
            $this->showMessage();
        }
    }

    //全部或超额现金付款（超额部分进入个人账户）
    public function actionProcessCashPay()
    {
        if (Yii::app()->request->isPostRequest && Yii::app()->request->isAjaxRequest){
            Yii::import('common.models.invoice.*');
            extract($_POST);

            $this->addMessage('state', 'fail');
            if (isset($posInvoiceId_cid) && $posInvoiceId_cid){
                $schoolType = $this->childObj->school->type;
                $criteria = new CDbCriteria;
                $criteria->compare('invoice_id', $posInvoiceId_cid);
                $models = Invoice::model()->findAll($criteria);
                foreach ($models as $model){
                    $tAmount += ($model->amount - $model->paidSum);
                    if($this->childObj->schoolid == $this->specialSchool && isset($paydate) && $paydate){
                        $model->payDate = strtotime($paydate);
                    }
                }

                if ( ($amount-$tAmount) >= 0 || abs($amount-$tAmount) < 0.001){
                    if($this->childObj->schoolid == $this->specialSchool){
                        $payType = isset($_POST['transactiontype']) ? $_POST['transactiontype'] : '';
                        if(!in_array($payType, array_keys($this->getDsPayType()))){
                            $this->addMessage('message', '请选择付款类型');
                            $this->showMessage();
                        }
                        if($this->payDateToggle==true && isset($paydate) && empty($paydate)){
                            $this->addMessage('message', '请选择付款日期');
                            $this->showMessage();
                        }
                    }
                    else{
                        $payType = InvoiceTransaction::TYPE_CASH;
                    }
                    if (!$this->policyApi->Pay($models, $payType, $amount )){
                        $this->addMessage('state', 'success');
                    }
                    else {
                        $this->addMessage('message', '付款失败');
                    }
                }
                else {
                    $this->addMessage('message', '实际付款不够，请使用组合付款');
                }
            }
            else {
                $this->addMessage('message', Yii::t("payment","Please select at least one invoice"));
            }

            $this->showMessage();
        }
    }

     /*
     * 处理代金卷业务
     */
    public function actionProcessVoucher(){
        $this->layout='//layouts/dialog';
        Yii::import('application.components.policy.*');
        Yii::import('common.models.invoice.*');

        $modelForm = new VoucherForm;

        $id = Yii::app()->request->getParam('id', 0);
        $childid = Yii::app()->request->getParam('childid', 0);
        $model = Invoice::model()->findByPk($id);
        $voucherAmount=0;
        $flag = Invoice::showVoucher($model,$model->invoice_id,$voucherAmount);

        if (isset($_POST['VoucherForm'])){
            if ($_POST['VoucherForm']['confirm']){
                if ($childid == $model->childid && $flag == TRUE){
                    //处理账单的业务（代金卷、预缴学费）
                    if (!$this->policyApi->processVoucher($model)) {
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', Yii::t('message','success'));
                        $this->addMessage('refresh', true);
                    } else {
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message','Failed!'));
                    }
                }else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '非法提交！');
                }
            }
            else {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '请勾选确认继续');
            }
            $this->showMessage();
        }

        $this->render('voucher', array('model'=>$modelForm, 'voucherAmount'=>$voucherAmount));
    }

    public function actionProcessCancelPay()
    {
        if(Yii::app()->request->isAjaxRequest && Yii::app()->request->isPostRequest){
            Yii::import('common.models.invoice.*');
            $id = Yii::app()->request->getPost('id', 0);
            $model = Invoice::model()->with('invoiceTransaction')->findByAttributes(array('invoice_id'=>$id, 'childid'=>$this->childId));
            if($model != null && !$this->policyApi->unPartPay($model)){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message','Failed!'));
            }
            $this->showMessage();
        }
    }

    # 返回DayStart/OG-PH的可选收款类型
    public function getDsPayType()
    {
        $payType = $GLOBALS['paymentMappings']['transactionType'];
        unset($payType[InvoiceTransaction::TYPE_USERCREDIT]);
        unset($payType[InvoiceTransaction::TYPE_POS]);
        unset($payType[InvoiceTransaction::TYPE_ONLINEPAYMENT]);
        unset($payType[InvoiceTransaction::TYPE_TOCREDIT]);
        unset($payType[InvoiceTransaction::TYPE_VOUCHER]);
        unset($payType[InvoiceTransaction::TYPE_POS_ALLINPAY]);
        unset($payType[InvoiceTransaction::TYPE_TRANSFER]);
        unset($payType[InvoiceTransaction::TYPE_BANKCARD]);
        unset($payType[InvoiceTransaction::TYPE_WX_NATIVE]);
        unset($payType[InvoiceTransaction::TYPE_WX_MICROPAY]);
        unset($payType[InvoiceTransaction::TYPE_EDUFEE]);
        unset($payType[InvoiceTransaction::TYPE_CHECK]);
        return $payType;
    }

    public function actionWxmicropay()
    {
        if(Yii::app()->request->isPostRequest){
            Yii::import('common.models.invoice.*');
            Yii::import('common.models.wxpay.*');

            $wxPayCfg = CommonUtils::LoadConfig('CfgWxPayGlobal');

            $posInvoiceId_cid = Yii::app()->request->getPost('posInvoiceId_cid', array());
            $finalAmount = Yii::app()->request->getPost('finalAmount', array());
            $usePartial = Yii::app()->request->getPost('usePartial', 0);
            $invoiceCount = Yii::app()->request->getPost('invoiceCount', 0);
            $invoiceTotalAmount = Yii::app()->request->getPost('invoiceTotalAmount', 0);
            $auth_code = Yii::app()->request->getPost('auth_code', '');

            $canProcced = true;
            $count = $totalAmount = 0;

            if(!$posInvoiceId_cid){
                $canProcced = false;
            }

            sort($posInvoiceId_cid);
            $invoices = Invoice::model()->with('paidSum')->findAllByPk($posInvoiceId_cid);

            if ($usePartial && count($invoices) !== 1){
                $canProcced = false;
            }

            $detail = array();
            foreach($invoices as $invoice){
                //账单状态已经不是待付款状态
                if(!in_array($invoice->status, array(Invoice::STATS_UNPAID, Invoice::STATS_PARTIALLY_PAID))){
                    $canProcced = false;
                    break;
                }
                //需付款于数据库中j计算出的需付款金额不一致
                if (!$usePartial){
                    if(!isset($finalAmount[$invoice->invoice_id]) || ( OA::formatMoney($finalAmount[$invoice->invoice_id]) != (OA::formatMoney($invoice->amount - $invoice->paidSum)) ) ){
                        $canProcced = false;
                        break;
                    }
                }
                else {
                    if ( $finalAmount[$invoice->invoice_id] >= ($invoice->amount - $invoice->paidSum) ){
                        $canProcced = false;
                        break;
                    }
                }
                $count++;
                $totalAmount += $finalAmount[$invoice->invoice_id];

                $detail[] = $invoice->title;
            }
            if( ($invoiceCount != $count) || ($invoiceTotalAmount != $totalAmount) ){
                $canProcced = false;
            }
            if(!$canProcced){
                $this->addMessage('state','fail');
                $this->addMessage('message',Yii::t("payment", "Page expired, please reload."));
                $this->showMessage();
            }

            $orderPrefix = strval($wxPayCfg[$this->childObj->schoolid]['number_code']) . sprintf('%06s', $posInvoiceId_cid[0]);

            $model = new WechatPayOrder;
            $model->orderid = $model->genOrderID($orderPrefix);
            $model->payable_amount = $totalAmount;
            $model->schoolid = $invoices[0]->schoolid;
            $model->childid = $this->childId;
            $model->type = 'MICROPAY';
            $model->order_time = time();
            $model->update_timestamp = time();
            $model->uid = Yii::app()->user->id;
            if($model->save()){
                foreach( $posInvoiceId_cid as $key){
                    $item = new WechatPayOrderItem();
                    $item->orderid = $model->orderid;
                    $item->invoice_id = $key;
                    $item->amount = $finalAmount[$key];
                    $item->save();
                }

                $pay = $this->beginWidget('common.extensions.wxPay.MicroPay', array(
                    'auth_code' => $auth_code,
                    'body' => $detail[0] . (count($detail)>1?'等':''),
                    'detail' => implode(' ', $detail),
                    'amount' => $totalAmount*100,
                    'orderid' => $model->orderid,
                    'cfg' => $wxPayCfg[$this->childObj->schoolid],
                ));
                $data = $pay->pay();
                $data = $pay->hideAppid($data);
                if($data['return_code'] == 'SUCCESS'){
                    if($data['result_code'] == 'SUCCESS'){
                        if($model->orderid == $data['out_trade_no']){
                            $model = WechatPayOrder::model()->findByPk($data['out_trade_no']);
                            $model->payInvoice();
                        }
                    }
                    else{
                        $data['out_trade_no'] = $model->orderid;
                    }
                    echo CJSON::encode($data);
                }

            }
        }
    }

    public function actionShowmicropay($id='')
    {
        $this->layout='//layouts/dialog';
        $this->dialogWidth = 600;

        Yii::import('common.models.invoice.*');
        Yii::import('common.models.wxpay.*');

        $wxPayCfg = CommonUtils::LoadConfig('CfgWxPayGlobal');
        $micropay = $this->beginWidget('common.extensions.wxPay.MicroPay', array(
            'orderid' => $id,
            'cfg' => $wxPayCfg[$this->childObj->schoolid],
        ));
        $data = $micropay->query();
        $data = $micropay->hideAppid($data);
        if($data['return_code'] == 'SUCCESS'){
            if($data['result_code'] == 'SUCCESS'){
                if($data['trade_state'] == 'SUCCESS'){
                    if($id == $data['out_trade_no']){
                        $model = WechatPayOrder::model()->findByPk($data['out_trade_no']);
                        $model->payInvoice();
                    }
                }
            }
        }

        $this->render('showmicropay', array('data'=>$data));
    }

    // 根据时间获取孩子下个学期的schoolid
    public function getNextSchoolid($schoolyear)
    {
       $schoolid = $this->childObj->schoolid;
       $reserve = $this->getNextReserve($this->childId);
       $reserveSchoolid = $reserve->schoolid;
       if ($reserve && $reserveSchoolid != $schoolid) {
           Yii::import('common.models.calendar.*');
           $nextCalendar = Calendar::model()->findByPk($reserve->calendar);
           if ($nextCalendar->startyear == $schoolyear) {
               $schoolid = $reserveSchoolid;
           }
       }
       return $schoolid;
    }

    public function getNextReserveSchoolid($childid)
    {
        $reserve = $this->getNextReserve($childid);
        if ($reserve) {
            return $reserve->schoolid;
        }
        return $this->childObj->schoolid;
    }

    public function getNextReserve($childid)
    {
        Yii::import('common.models.invoice.*');
        $criteria = new CDbCriteria;
        $criteria->compare('childid', $childid);
        $criteria->compare('stat', array(0, 10 ,20));
        $reserve = ChildReserve::model()->find($criteria);
        return $reserve;
    }
}
