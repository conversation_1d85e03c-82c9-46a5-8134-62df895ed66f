<h1>
<?php echo Yii::t("navigations","Resources"); ?>
</h1>
<?php if (Yii::app()->language == 'en_us'): ?>
<p>
	This category provides useful resources for parents.
</p>
<p>
	Click "Next" to see sub navigations of this category.
</p>
<p>
	Or jump to guide of other categories by clicking the links below:
</p>
<?php else: ?>
<p>
	该目录下提供了实用的家长资源。
</p>
<p>
	请点击“下一步”查看该目录的具体项目。
</p>
<p>
	或者点击以下链接跳转到其他页面的向导：
</p>
<?php endif ?>
<ul class="list">
<?php foreach(Mims::Nav() as $key=>$nav):?>
	<li>
		<?php
		if($key == "portfolio"){
			$nav['url'] = array('//portfolio/portfolio/classes');
		}
		$url = array_merge($nav['url'], array("demo"=>"guide"));
		echo CHtml::link($nav['label'], $url);
		?>
	</li>
<?php endforeach;?>
</ul>