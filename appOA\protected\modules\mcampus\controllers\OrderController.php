<?php

class OrderController extends BranchBasedController
{
    public $actionAccessAuths = array(
        'admin'             => 'o_A_Access',
        'Orderlist'         => 'o_A_Access',
        'Receive'           => 'o_A_Adm_Support',
        'done'              => 'o_A_Adm_Support',
    );
    
    public $defaultAction = 'admin';
    public $dialogWidth=500;

    public $cAdmin = false;    //是否有校园管理权限
    public $oAdmin = false; //是否总部后勤部权限

    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init() {
        parent::init();

        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";
        Yii::import('common.models.points.*');

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Campus Operations');

        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mcampus/order/admin');
        
        $this->cAdmin = Yii::app()->user->checkAccess('o_A_Adm_Support');
    }
    
    public function actionSelect()
    {
        $this->render('//layouts/common/branchSelect');
    }

    /**
     * 积分兑换订单管理主页面
     */
    public function actionAdmin($branchId='', $type='opgcount')
    {
        $branchId = $this->branchId;
        $this->branchSelectParams["urlArray"] = array("//mcampus/order/admin", 'type'=>$type);

        $criteria=new CDbCriteria;
        $criteria->compare('t.status', array(PointsStatus::STATS_CREATED, PointsStatus::STATS_CONFIRMED, PointsStatus::STATS_READYTOSHIP, PointsStatus::STATS_SHIPPING, PointsStatus::STATS_RECEIVED));
        if ($branchId){
            $criteria->compare('t.schoolid', $branchId);
        }
        $allOrders = PointsOrder::model()->with(array('Product', 'ChildProfile', 'school', 'orderUser', 'updateUser'))->findAll($criteria);

        $sData10 = array();
        $sData20 = array();
        $sData110 = array();
        $sData120 = array();
        $sData130 = array();
        $sData210 = array();

        foreach ($allOrders as $order){
            if ($order->status == PointsStatus::STATS_CREATED && $order->category == 'order'){
                $sData10[$order->id] = $order;
            }
            elseif ($order->status == PointsStatus::STATS_CONFIRMED && $order->category == 'order'){
                $sData20[$order->id] = $order;
            }
            elseif ($order->status == PointsStatus::STATS_READYTOSHIP && $order->category == 'pack'){
                $sData110[$order->id] = $order;
            }
            elseif ($order->status == PointsStatus::STATS_SHIPPING && $order->category == 'pack'){
                $sData120[$order->id] = $order;
            }
            elseif ($order->status == PointsStatus::STATS_RECEIVED && $order->category == 'order'){
                $sData130[$order->id] = $order;
            }
        }

        $criteria = new CDbCriteria;
        $criteria->compare('t.status', PointsStatus::STATS_COMPLETED);
        $criteria->compare('t.schoolid', $branchId);
        $criteria->order='t.update_timestamp desc';
        $criteria->with=array('Product', 'ChildProfile', 'school', 'orderUser', 'updateUser');
        $sData210 = new CActiveDataProvider('PointsOrder', array(
            'criteria'=>$criteria,
        ));

        $schoolsCount = array();
        if($type == 'opgcount')
            $sql = 'select schoolid, count(*) as c from '.PointsOrder::model()->tableName().' where status in ('.PointsStatus::STATS_CREATED.','.PointsStatus::STATS_CONFIRMED.','.PointsStatus::STATS_READYTOSHIP.') and category="order" group by schoolid';
        else
            $sql = 'select schoolid, count(*) as c from '.PointsOrder::model()->tableName().' where status in ('.PointsStatus::STATS_SHIPPING.','.PointsStatus::STATS_RECEIVED.') and category="order" group by schoolid';
        $sc=Yii::app()->db->createCommand($sql)->queryAll();
        foreach ($sc as $_sc){
            $schoolsCount[$_sc['schoolid']]=$_sc['c'];
        }

        $this->render('moperation.views.order.admin',array(
            'branchId'=>$branchId,
            'sData10'=>$sData10,
            'sData20'=>$sData20,
            'sData110'=>$sData110,
            'sData120'=>$sData120,
            'sData130'=>$sData130,
            'sData210'=>$sData210,
            'schoolsCount'=>CJSON::encode($schoolsCount),
        ));
    }

    /**
     * 包裹里的订单列表
     * @param int $id
     */
    public function actionOrderlist($id=0)
    {
        if ($id){
            $this->dialogWidth = 800;
            $this->layout='//layouts/dialog';
            $model = PointsOrder::model()->findByPk($id);
            
            $orders = PointsOrder::model()->findAllByAttributes(array('pack_id'=>$id));
            
            Yii::app()->clientScript->registerScriptFile( Yii::app()->request->baseUrl.'/themes/base/js/jquery.jPrintArea.js' );
            
            $this->render('moperation.views.order.orderlist', array( 'model'=>$model, 'orders'=>$orders ));
        }
    }

    /**
     * 接收包裹
     * @param int $id
     */
    public function actionReceive($id=0)
    {
        $ret = array('status'=>'fail');
        if ($this->cAdmin && $id){
            $criteria = new CDbCriteria;
            $criteria->condition = "(id = :id or pack_id = :id) and status=:s";
            $criteria->params = array(':id'=>$id, ':s'=>PointsStatus::STATS_SHIPPING);
            
            $items = PointsOrder::model()->findAll($criteria);
            foreach ($items as $item){
                $item->status = PointsStatus::STATS_RECEIVED;
                $item->update_timestamp = time();
                $item->update_userid = Yii::app()->user->id;
                $item->save();
            }
            $mpoint = new PointsStatus;
            $mpoint->itemid = $item->id;
            $mpoint->status = PointsStatus::STATS_RECEIVED;
            $mpoint->update_timestamp = time();
            $mpoint->update_userid = Yii::app()->user->id;
            $mpoint->save();
            
            $ret['status'] = 'success';
        }
        echo CJSON::encode($ret);
    }

    /**
     * 礼品发放家长
     */
    public function actionDone()
    {
        $oIds = Yii::app()->request->getPost('orderid', array());
        
        $this->addMessage('state', 'fail');
        $this->addMessage('message', Yii::t('message','Failed!'));
        if ($this->cAdmin && $oIds){
            
            foreach ($oIds as $id){
                $model = PointsOrder::model()->findByPk($id);
                if ($model->status == PointsStatus::STATS_RECEIVED){
                    $model->status = PointsStatus::STATS_COMPLETED;
                    $model->update_timestamp = time();
                    $model->update_userid = Yii::app()->user->id;
                    $model->save();
                    
                    $status = new PointsStatus;
                    $status->itemid = $id;
                    $status->status = PointsStatus::STATS_COMPLETED;
                    $status->update_timestamp = time();
                    $status->update_userid = Yii::app()->user->id;
                    $status->save();
                }
            }
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message','success'));
            $this->addMessage('refresh', true);
        }
        $this->showMessage();
    }
}
