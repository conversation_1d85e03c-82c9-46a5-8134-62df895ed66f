<?php
class ConstantTuitionCommand extends CConsoleCommand
{
	
	public $data1= '2013-05-14';
	public $data2= '2013-06-14';
	public $data3= '2013-07-08';
	
	public function init(){
		Yii::setPathOfAlias('common', dirname(__FILE__) . '/../../../common');
		Yii::import('common.models.*');
		Yii::import('common.models.child.*');
		Yii::import('common.models.invoice.ChildConstantTuition');
	}
	/**
	 * 获取某一学校的在读孩子的信息
	 * @param string $schoolid
	 * @return array
	 * <AUTHOR>
	 */
	public function getData($schoolid)
	{
		$childIds1 = StatsChildCount::model()->getChild($schoolid,strtotime($this->data1));
		$childIds2 = StatsChildCount::model()->getChild($schoolid,strtotime($this->data2));
		$childIds3 = StatsChildCount::model()->getChild($schoolid,strtotime($this->data3));
		return $childIds1+$childIds2+$childIds3;
	}
	
	public function actionCreate($schoolid,$amount,$type, $replace=false)
	{
		if ($schoolid && $amount && $type)
		{
			if($replace){
				ChildConstantTuition::model()->deleteAllByAttributes(array('schoolid'=>$schoolid));
			}
			$childIds = $this->getData($schoolid);
			if (is_array($childIds) && count($childIds))
			{
				foreach ($childIds as $value)
				{
					$model = ChildConstantTuition::model()->findByPk($value);
					if (empty($model))
					{
						$model = new ChildConstantTuition;
						$model->childid = $value;
						$model->amount = $amount;
						$model->schoolid = $schoolid;
						$model->tuition_type = $type;
						if ($model->save())
						{
							echo $value."....Importing ok...\r\n";
						}
						else
						{
							print_r($model->getErrors());exit;
						}
					}
					else
					{
						echo $value."....system exsit...\r\n";
					}
				}
			}
		}
		else
		{
			echo 'Parameter Error...';
		}
	}
}