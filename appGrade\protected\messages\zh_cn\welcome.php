<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yiic message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE, this file must be saved in UTF-8 encoding.
 *
 * @version $Id: $
 */
return array (
  'This' => '本周',
  'Week' => '信息',
  'No key reminders this week.' => '本周没有要事提醒。',
  'Parent and Student Handbook' => '家长与学生手册',
  'Daystar Teachers Contact List' => '启明星老师联系表',
  '2016-2017 School Calendar' => '2016-2017 校历',
  '2017-2018 School Calendar' => '2017-2018 校历',
  'MAP Assessment Information Session' => 'MAP 测评的信息介绍',
);
