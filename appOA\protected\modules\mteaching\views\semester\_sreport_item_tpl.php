<!--学生列表：学生名单模版-->
<script type="text/template" id="childlist-item-template">

    <li  id='child<%- id %>' class="media class-child loadViewByChild" onclick='loadReportViewByChild(<%= id%>)' childid=<%- id %>>
        <a class="pull-left" href="javascript:loadReportViewByChild(<%= id%>)">
            <img class="media-object child-face img-thumbnail" src="<?php echo Yii::app()->params['OAUploadBaseUrl']?>/childmgt/<%= photo %>" title="<%- name %>">
        </a>
        <div class="media-body flex1">
            <h5 class="media-heading" style='margin:0'><%- name %></h5>
        </div>
        <h5 class="flag"><span class="glyphicon glyphicon-ok-sign"></span></h5>
    </li>

</script>

<!--学生列表：学生名单模版-->
<script type="text/template" id="child-item-template">
    <div class="row">
        <div class="pull-left ml15" style='width:300px'> 
            <div>
                <% _isOnline = ( _.indexOf(currentData.onlined, parseInt(id)) == -1) ? false : true; %>
                <% _isSubmits = ( _.indexOf(currentData.submits, parseInt(id)) == -1) ? false : true; %>
                <% _isPass = ( _.indexOf(currentData.pass, parseInt(id)) == -1) ? false : true; %>
                <% _isOverrule = ( _.indexOf(currentData.overrule, parseInt(id)) == -1) ? false : true; %>
                <% _pdfFile = currentData.report.pdf_file; %>
                <% _lang = currentData.report.lang; %>
                <% if(_isOnline){ %>
                <!-- 状态为20 -->
                    <div class="alert alert-success text-center">
                        <h4><%- name %> <%- id %></h4>
                        <div class="p20"> <span class="glyphicon glyphicon-ok-sign"></span>
                            <?php echo Yii::t('teaching','Report is online');?>
                            <% if(_pdfFile != 1) { %>
                                (<?php echo Yii::t('teaching','PDF being generated');?>)
                            <% } %>
                        </div>
                        <!-- <% if(schoolGroup == 10){ %>
                        <div class="p20">
                            <span id="viewReportPDF">
                                <% if(currentData.report.custom_pdf){ %>
                                <a class="btn btn-info" target="_blank" href="<%= currentData.report.pdfUrl%>"><span class="glyphicon glyphicon-file"></span> PDF</a>
                                <% } %>
                            </span>
                        </div>
                        <br>
                        <% } %> -->
                        <button type="button" class="btn btn-info" onclick="reportPreview()">
                            <?php echo Yii::t('teaching','Preview');?></button>
                        <button type="button" class="btn btn-info" onclick="clearPDF(<%= currentData.report.id%>)">
                            <?php echo Yii::t('teaching','Clear PDF Cache');?>
                        </button>
                        <button type="button" class="btn btn-danger" onclick="setReportStatus('offline', <%- id %>, this)">
                            <?php echo Yii::t('teaching','Make Offline');?>
                        </button>
                        <div class='mt15 langShow'>
                            <span class='color6'>
                            <?php echo Yii::t('teaching', 'Language of report: '); ?> 
                            </span>
                            <span class='color3' id='lang_text'>
                            <% if(_lang == 'cn') { %>
                                <?php echo Yii::t('teaching', 'Chinese'); ?>
                            <% }else{ %>
                                <?php echo Yii::t('teaching', 'English'); ?>
                            <% } %>
                            </span>
                            <a href="javascript:;" class='ml10' onclick='showLang()'><?php echo Yii::t('global', 'Edit'); ?></a>
                        </div>
                        <div class='mt15 langRdio' style='width: 250px;margin: 0 auto;text-align:left'>
                            <p class='color3'><?php echo Yii::t('teaching', 'Language of report: '); ?> </p>
                            <div style='background: #fff;padding: 5px;border-radius: 3px;' >
                                <label class="radio-inline color3">
                                    <input type="radio" name="langRadio" value="cn" <% if(_lang == 'cn'){ print('checked')} %>> <?php echo Yii::t('teaching', 'Chinese'); ?>
                                </label>
                                <label class="radio-inline color3">
                                    <input type="radio" name="langRadio" value="en" <% if(_lang == 'en'){print('checked')} %>> <?php echo Yii::t('teaching', 'English'); ?>
                                </label>
                                <button  type="button" class="btn btn-primary btn-xs pull-right  ml10" onclick="setReportLang()"><?php echo Yii::t('global', 'Save'); ?></button>
                                <button  type="button" class="btn btn-default btn-xs  pull-right ml10" onclick="hideLang()"><?php echo Yii::t('global', 'Cancel'); ?></button>
                            </div>
                        </div>
                    </div>
                <% }else if(_isPass){ %>
                <!-- 状态为16 -->
                    <div class="alert alert-info text-center">
                        <h4><%- name %> <%- id %></h4>
                        <div class="p20"> <span class="glyphicon glyphicon-ok-sign"></span>
                            <?php echo Yii::t('teaching','Approved, report is offline');?>
                        </div>
                        <button type="button" class="btn btn-info" onclick="reportPreview()">
                            <?php echo Yii::t('teaching','Preview');?></button>
                        <button type="button" class="btn btn-info" onclick="clearPDF(<%= currentData.report.id%>)">
                            <?php echo Yii::t('teaching','Clear PDF Cache');?>
                        </button>
                        <button type="button" class="btn btn-danger" onclick="setReportStatus('pass', <%- id %>, this)">
                            <?php echo Yii::t('teaching','Make Online');?>
                        </button>
                        <div class='mt15 langShow'>
                            <span class='color6'>
                            <?php echo Yii::t('teaching', 'Language of report: '); ?> 
                            </span>
                            <span class='color3' id='lang_text'>
                            <% if(_lang == 'cn') { %>
                                <?php echo Yii::t('teaching', 'Chinese'); ?>
                            <% }else{ %>
                                <?php echo Yii::t('teaching', 'English'); ?>
                            <% } %>
                            </span>
                            <a href="javascript:;" class='ml10' onclick='showLang()'><?php echo Yii::t('global', 'Edit'); ?></a>
                        </div>
                        <div class='mt15 langRdio' style='width: 250px;margin: 0 auto;text-align:left'>
                            <p class='color3'><?php echo Yii::t('teaching', 'Language of report: '); ?> </p>
                            <div style='background: #fff;padding: 5px;border-radius: 3px;' >
                                <label class="radio-inline color3">
                                    <input type="radio" name="langRadio" value="cn" <% if(_lang == 'cn'){ print('checked')} %>> <?php echo Yii::t('teaching', 'Chinese'); ?>
                                </label>
                                <label class="radio-inline color3">
                                    <input type="radio" name="langRadio" value="en" <% if(_lang == 'en'){print('checked')} %>> <?php echo Yii::t('teaching', 'English'); ?>
                                </label>
                                <button  type="button" class="btn btn-primary btn-xs pull-right  ml10" onclick="setReportLang()"><?php echo Yii::t('global', 'Save'); ?></button>
                                <button  type="button" class="btn btn-default btn-xs  pull-right ml10" onclick="hideLang()"><?php echo Yii::t('global', 'Cancel'); ?></button>
                            </div>
                        </div>
                    </div>
                <% }else if(_isSubmits){ %>
                <!-- 状态为15 -->
                    <div class="alert alert-info text-center">
                        <h4><%- name %> <%- id %></h4>
                        <div class="p20"> <span class="glyphicon glyphicon-ok-sign"></span>
                            <?php echo Yii::t('teaching','Pending Approve');?>
                        </div>
                        <button type="button" class="btn btn-info" onclick="reportPreview()">
                            <?php echo Yii::t('teaching','Preview');?></button>
                        <button type="button" class="btn btn-info" onclick="clearPDF(<%= currentData.report.id%>)">
                            <?php echo Yii::t('teaching','Clear PDF Cache');?>
                        </button>
                        <button type="button" class="btn btn-danger" onclick="setReportStatus('quxiao', <%- id %>, this)">
                            <?php echo Yii::t('teaching','Cancel the Submission');?>
                        </button>
                    </div>
                <% }else if(_isOverrule){ %>
                <!-- 状态为14 -->
                    <div class="alert alert-info text-center">
                        <h4><%- name %> <%- id %></h4>
                        <div class="p20"> <span class="glyphicon glyphicon-ok-sign"></span>
                            <?php echo Yii::t('teaching','Please resubmit');?>
                        </div>
                        <div class="p20">驳回原因：</span>
                            <%- currentData.overruleText[id] %>
                        </div>
                        <button type="button" class="btn btn-info" onclick="reportPreview()">
                            <?php echo Yii::t('teaching','Preview');?></button>
                        <button type="button" class="btn btn-info" onclick="clearPDF(<%= currentData.report.id%>)">
                            <?php echo Yii::t('teaching','Clear PDF Cache');?>
                        </button>
                        <button type="button" class="btn btn-danger" onclick="setReportStatus('online', <%- id %>, this)">
                            <?php echo Yii::t('reg','Re-submitted');?>
                        </button>
                        <div class='mt15 langShow'>
                            <span class='color6'>
                            <?php echo Yii::t('teaching', 'Language of report: '); ?> 
                            </span>
                            <span class='color3' id='lang_text'>
                            <% if(_lang == 'cn') { %>
                                <?php echo Yii::t('teaching', 'Chinese'); ?>
                            <% }else{ %>
                                <?php echo Yii::t('teaching', 'English'); ?>
                            <% } %>
                            </span>
                            <a href="javascript:;" class='ml10' onclick='showLang()'><?php echo Yii::t('global', 'Edit'); ?></a>
                        </div>
                        <div class='mt15 langRdio' style='width: 250px;margin: 0 auto;text-align:left'>
                            <p class='color3'><?php echo Yii::t('teaching', 'Language of report: '); ?> </p>
                            <div style='background: #fff;padding: 5px;border-radius: 3px;' >
                                <label class="radio-inline color3">
                                    <input type="radio" name="langRadio" value="cn" <% if(_lang == 'cn'){ print('checked')} %>> <?php echo Yii::t('teaching', 'Chinese'); ?>
                                </label>
                                <label class="radio-inline color3">
                                    <input type="radio" name="langRadio" value="en" <% if(_lang == 'en'){print('checked')} %>> <?php echo Yii::t('teaching', 'English'); ?>
                                </label>
                                <button  type="button" class="btn btn-primary btn-xs pull-right  ml10" onclick="setReportLang()"><?php echo Yii::t('global', 'Save'); ?></button>
                                <button  type="button" class="btn btn-default btn-xs  pull-right ml10" onclick="hideLang()"><?php echo Yii::t('global', 'Cancel'); ?></button>
                            </div>
                        </div>
                    </div>
                <% }else{ %>
                    <div class="alert alert-danger text-center">
                        <h4><%- name %> <%- id %></h4>
                        <div class="p20"> <span class="glyphicon glyphicon-exclamation-sign"></span>
                            <?php echo Yii::t('teaching','Report is offline');?>
                        </div>
                        <div>
                            <button type="button" class="btn btn-info" onclick="reportPreview()">
                                <?php echo Yii::t('teaching','Preview');?>
                            </button>
                            <button type="button" class="btn btn-info" onclick="clearPDF(<%= currentData.report.id%>)">
                                <?php echo Yii::t('teaching','Clean PDF cache');?>
                            </button>
                            <button type="button" class="btn btn-primary" onclick="setReportStatus('online', <%- id %>, this)">
                                <?php echo Yii::t('reg','Submit');?>
                            </button>
                        </div>
                        <div class='mt15 langShow'>
                            <span class='color6'>
                            <?php echo Yii::t('teaching', 'Language of report: '); ?> 
                            </span>
                            <span class='color3' id='lang_text'>
                            <% if(_lang == 'cn') { %>
                                <?php echo Yii::t('teaching', 'Chinese'); ?>
                            <% }else{ %>
                                <?php echo Yii::t('teaching', 'English'); ?>
                            <% } %>
                            </span>
                            <a href="javascript:;" class='ml10' onclick='showLang()'><?php echo Yii::t('global', 'Edit'); ?></a>
                        </div>
                        <div class='mt15 langRdio' style='width: 250px;margin: 0 auto;text-align:left'>
                            <p class='color3'><?php echo Yii::t('teaching', 'Language of report: '); ?> </p>
                            <div style='background: #fff;padding: 5px;border-radius: 3px;' >
                                <label class="radio-inline color3">
                                    <input type="radio" name="langRadio" value="cn" <% if(_lang == 'cn'){ print('checked')} %>> <?php echo Yii::t('teaching', 'Chinese'); ?>
                                </label>
                                <label class="radio-inline color3">
                                    <input type="radio" name="langRadio" value="en" <% if(_lang == 'en'){print('checked')} %>> <?php echo Yii::t('teaching', 'English'); ?>
                                </label>
                                <button  type="button" class="btn btn-primary btn-xs pull-right  ml10" onclick="setReportLang()"><?php echo Yii::t('global', 'Save'); ?></button>
                                <button  type="button" class="btn btn-default btn-xs  pull-right ml10" onclick="hideLang()"><?php echo Yii::t('global', 'Cancel'); ?></button>
                            </div>
                        </div>
                    </div>
                <% }%>
            </div>
        </div>
        <div class="col-sm-4">
            <img class="img-thumbnail" src="<?php echo Yii::app()->params['OAUploadBaseUrl']?>/childmgt/<%= photo %>" alt="<%- name %>">
        </div>
    </div>
</script>

<!--模版内容项模版-->
<script type="text/template" id="report-content-item-template">
    <div class="content-item" data-content-type="<%- content %>" sign="<%- sign %>">
        <h4><span class="item-title"> <%- title %></span>
            <a href="javascript:" onclick="editContent(this)" class="edit-btn">
                <span class="glyphicon glyphicon-edit"></span>
            </a>
        </h4>
        <div class="preview">

        </div>
        <div class="editing">

        </div>
    </div>
</script>

<!--模版附加内容项模版 IA专用-->
<script type="text/template" id="report-extra-content-item-template">
    <div class="content-item" data-content-type="<%- content %>" sign="<%- sign %>">
        <h5><span class="item-title"> <%- title %></span>
            <a href="javascript:" onclick="editContent(this)" class="edit-btn">
                <span class="glyphicon glyphicon-edit"></span>
            </a>
        </h5>
        <div class="preview">

        </div>
        <div class="editing">

        </div>
    </div>
</script>

<script type="text/template" id="rptform-item-preview-content">
    <div class="panel panel-success">
        <div class="panel-heading">
            <% if(ld_id > 0 ){ %>
            <h4><span class="glyphicon glyphicon-tags"></span> <% print(currentData.learningDomains[ld_id]); %></h4>
            <% } %>

            <div style="padding: 6px 0">
                <% if(media_id > 0){ %>
                <img class="img-thumbnail maxWidth" data-imageId=<%- media_id %> 
                src="<% print(currentData.bigPhotoCache[media_id]); %>" />
                <% } %>

                <% if(!_.isEmpty(content)){ %>
                <p><%= content %></p>
                <% } %>
            </div>
            <div class="clearfix"></div>
        </div>
    </div>
</script>

<!-- 表单hidden信息部分-->
<script type="text/template" id="rptform-item-content">

    <div class="alert alert-danger user-input-guide" role="alert">

    </div>
    <?php
    $attrs = array_keys( SReportItem::model()->getAttributes() );
    $labels = SReportItem::model()->attributeLabels();

    foreach(array('id','report_id','category','subcategory', 'media_id','img_cropper') as $attr):
        echo CHtml::hiddenField(
            sprintf("SReportItem[%s]", $attr),
            "<%- ".$attr. " %>",
            array("encode"=>false));
    endforeach;?>

    <?php echo CHtml::hiddenField('childid', '<%- currentData.childId %>', array('encode'=>false));?>

    <% if(_.indexOf(contentType, 'learningDomain')>=0){%>
    <div class="form-group">
        <?php echo CHtml::label($labels['ld_id'], CHtml::getIdByName('SReportItem[ld_id]'), array('class'=>'control-label col-sm-2')); ?>
        <div class="col-sm-10">
            <?php echo CHtml::dropDownList('SReportItem[ld_id]', 0, $template->getLearningDomains(),array('class'=>'form-control length_4'));?>
        </div>
    </div>
    <% } %>

    <% if(_.indexOf(contentType, 'photo')>=0){%>
    <div class="form-group">
        <?php echo CHtml::label($labels['media_id'], CHtml::getIdByName('SReportItem[media_id]'), array('class'=>'control-label col-sm-2')); ?>
        <% if(category=='Avatar'){ %>
            <div class="col-sm-6 photo-select avatarCropper">
        <% }else{%>
            <div class="col-sm-10 photo-select">
        <% }%>
            <% if(media_id>0){ %>
                <img class="img-thumbnail" data-imageId="<%- media_id %>" src="<% print(currentData.photoCache[media_id]);%>" />
            <% }%>
            <button type="button" class="btn btn-default" onclick="selectPhoto(this);">
                <span class="glyphicon glyphicon-picture"></span> <?php echo Yii::t('teaching','Select Photo');?>
            </button>

            <% if(_.indexOf(contentType, 'batchPhoto')>=0){%>
            <div style="clear: right">
                <button type="button" class="btn btn-default" onclick="applyPhotoToOthers(this, 'BackCover')">
                    <span class="glyphicon glyphicon-th"></span> <?php echo Yii::t('teaching','Apply to others');?>
                </button>
            </div>
            <% }%>

            <% if(_.indexOf(contentType, 'sighPhoto')>=0){%>
            <div style="clear: right">
                <button type="button" class="btn btn-default" onclick="applyPhotoToOthers(this, 'TeacherSign')">
                    <span class="glyphicon glyphicon-th"></span> <?php echo Yii::t('teaching','Apply to others');?>
                </button>
            </div>
            <% }%>
        </div>
    </div>
    <% } %>

    <% if(_.indexOf(contentType, 'photos')>=0){%>
    <div class="form-group">
        <?php echo CHtml::label($labels['media_id'], CHtml::getIdByName('SReportItem[media_id]'), array('class'=>'control-label col-sm-2')); ?>
        <div class="col-sm-10 photo-select">
            <button type="button" class="btn btn-default" onclick="selectPhoto(this);">
                <span class="glyphicon glyphicon-picture"></span> <?php echo Yii::t('teaching','Select Photos');?>
            </button>
        </div>
    </div>
    <% } %>

    <% if(_.indexOf(contentType, 'text')>=0){%>
    <div class="form-group">
        <?php echo CHtml::label($labels['content'], CHtml::getIdByName('SReportItem[content]'), array('class'=>'control-label col-sm-2')); ?>
        <div class="col-sm-10">
            <?php echo CHtml::textArea('SReportItem[content]', '<%- content %>',
                array('encode'=>false,'class'=>'form-control', 'rows'=>6));?>
        </div>
    </div>
    <% } %>

    <div class="form-group">
        <div class='control-label col-sm-2'> </div>
        <div class="col-sm-10">
            <button type="button" class="btn btn-primary J_ajax_submit_btn">
                <?php echo Yii::t('global','Submit');?></button>
            <button type="button" class="btn btn-default" onclick="cancelEdit(this)">
                <?php echo Yii::t('global','Cancel');?></button>
        </div>
    </div>
</script>

<!-- 封底分配到其他孩子列表模版-->
<script type="text/template" id="assign-cover-template">
    <div class="pull-left mr15 mb10 child-select-box">
        <label>
            <img class="img-thumbnail w80" id="children_<%= id%>" src="<?php echo Yii::app()->params['OAUploadBaseUrl']?>/childmgt/<%= photo%>" title="<%= name%>">
            <input type="checkbox" name="children[]" value="<%= id%>" class="J_child" <% if(selected == 1){%>checked<% }%>>
        </label>
    </div>
</script>

<script>
    var childTpl = _.template( $('#childlist-item-template').html() ); //孩子列表模版
    var childInfoTpl = _.template( $('#child-item-template').html() ); //孩子个人信息模版
    var rptFormData = _.template( $('#rptform-item-content').html() ); //FORM模版
    var rptPreviewData = _.template( $('#rptform-item-preview-content').html() ); //PREVIEW模版
    var contentItemTpl = _.template( $('#report-content-item-template').html() );
    var extraContentItemTpl = _.template( $('#report-extra-content-item-template').html() );

    var _isOnline;
    function showLang(){
        $('.langRdio').show()
        $('.langShow').hide()
    }
    function hideLang(){
        $('.langRdio').hide()
        $('.langShow').show()
    }
</script>

<style>
    .langRdio{
        display:none
    }
    .tree ul.top{
        padding: 0;
    }
    .tree li {
        list-style-type:none;
        margin:0;
        padding:10px 5px 0 5px;
        position:relative
    }
    .tree li::before, .tree li::after {
        content:'';
        left:-20px;
        position:absolute;
        right:auto
    }
    .tree li::before {
        border-left:1px solid #999;
        bottom:50px;
        height:100%;
        top:0;
        width:1px
    }
    .tree li::after {
        border-top:1px solid #999;
        height:20px;
        top:25px;
        width:25px
    }
    .tree li span.item {
        -moz-border-radius:5px;
        -webkit-border-radius:5px;
        border:1px solid #999;
        border-radius:5px;
        display:inline-block;
        padding:3px 8px;
        text-decoration:none
    }
    .tree li.parent_li>span {
        cursor:pointer
    }
    .tree>ul>li::before, .tree>ul>li::after {
        border:0
    }
    .tree li:last-child::before {
        height:30px
    }
    .loadViewByChild .flag{
        top: 6px !important;
        color: #d1d1d1 !important;
        padding-left: 4px;
    }
    .loadViewByChild{
        padding:4px;
        cursor: pointer;
        border-radius:4px;
        border: 1px solid #fff;
        display:flex;
        align-items:center
    }
    .loadViewByChild:hover,.loadViewByChilded{
        background: rgba(77, 136, 210, 0.1);
        border: 1px solid #4D88D2;
    }
    .img-item{background: #f2f2f2;border: 1px solid #d5d5d5}
    .child-face{max-width: 40px;}
    #semester-report-child-list .media{padding-left: 22px; position: relative}
    #semester-report-child-list h5.flag{position: absolute;top: 2px; left: 0px; font-size:16px;color: #f2f2f2}
    #semester-report-child-list .online h5.flag{color: #008000}

    .content-item .img-thumbnail{margin: 0 8px 8px 0;}
    .content-item .panel, .content-item h4{margin-bottom: 0px;}
    .editing .panel, .preview .panel{margin-top: 8px;}

    .child-select-box{position: relative;}
    .child-select-box input.J_child{position: absolute; top: -2px; left: 2px;}
    .maxWidth{
        max-width:400px;
        height:auto
    }
    .cropper-container{
        margin-bottom:20px
    }
    .avatarCropper{
        max-width:400px;
        height:auto
    }
</style>
