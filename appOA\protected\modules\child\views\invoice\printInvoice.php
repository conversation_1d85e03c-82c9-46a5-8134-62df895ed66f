<div class="print-info-wrapper">
    <div class="btn_wrap1">
        <div class="btn_wrap_pd">
            <ul class="switch_list">
                <?php
                echo CHtml::radioButtonList('lang', $lang, array('zh_cn'=>'打印中文账单', 'us_en'=>'Print English Invoices'), array('template'=>'<li>{input} {label}</li>', 'separator'=>''));
                ?>
            </ul>
        </div>
    </div>
    <div class="wp">
        <div class="print-info" id="print_info" style="width:700px;">
            <div class="school-info">
                <ul>
                    <li class="address">
                        <?php echo addColon(OA::t('labels', 'Tel', $lang)).$base_info['school_desc']['tel']?><br>
                        <?php echo addColon(OA::t('labels', 'Fax', $lang)).$base_info['school_desc']['fax']?><br>
                        <?php echo addColon(OA::t('labels', 'Email', $lang)).$base_info['school_desc']['email']?><br>
                        <?php echo addColon(OA::t('labels', 'Address', $lang)).$base_info['school_desc']['address']?><br>
                    </li>
                    <li class="logo"><?php echo CHtml::image($base_info['school_logo_url']); ?>
                    </li>
                </ul>
                <div class="c"></div>
                <ul>
                    <li class="line bolder2"></li>
                </ul>
            </div>
            <div class="c"></div>
            <div class="child-info">
                <ul class="title">
                    <li><?php echo OA::t("payment", 'Invoice', $lang); ?></li>
                </ul>
                <ul class="content">
                    <li><span><?php echo OA::t("labels", 'Child Name', $lang); ?> </span> <?php echo $base_info['child_name']; ?>
                    </li>
                    <li><span><?php echo OA::t("payment", 'General Credit', $lang); ?> </span>
                        <?php echo addColon(OA::t("payment", "Balance", $lang)) . $base_info['credit']; ?>
                    </li>
                    <li><span><?php echo OA::t("labels", 'Class', $lang); ?> </span> <?php echo $base_info['class_name']; ?>
                    </li>
                    <?php if (!in_array($base_info['school_id'], array('BJ_DS', 'BJ_SLT', 'BJ_QFF'))) : ?>
                    <li><span><?php echo OA::t("payment", 'Tuition Deposit', $lang); ?> </span> <?php echo addColon(OA::t("payment", "Balance", $lang)) . $base_info['deposit']; ?>
                    </li>
                    <?php endif;?>
                </ul>
                <div class="c"></div>

            </div>
            <div class="c"></div>
            <div class="invoice-wrapper">
                <?php
                if (!empty($invoice_list)) {
                    $nf = new CNumberFormatter('zh-cn');
                    foreach ($invoice_list as $invoice) {
                        ?>
                        <div class="invoice-info" id="i_<?php echo $invoice->invoice_id ?>" <?php if ($invoice->invoice_id != $invoice_id): ?>style="display:none;"<?php endif; ?>>
                            <ul>
                                <li class="line bolder2"></li>
                            </ul>
                            <ul>
                                <li><span style="width:auto;">
                                        <?php
                                        if (!empty($invoice->paymentTypeTitle)) {
                                            echo '[' . $invoice->paymentTypeTitle . ']';
                                        }
                                        ?>
                                    </span> <?php echo $invoice->title; ?>
                                </li>
                                <li><span><?php echo OA::t("payment", 'Due Date', $lang, $lang); ?> </span> <?php echo $invoice->dueDate; ?>
                                </li>
                                <div class="c"></div>
                            </ul>
                            <ul>
                                <li class="line"></li>
                            </ul>
                            <ul>
                                <li><span><?php echo sprintf("%010d", $invoice->invoice_id); ?> </span>
                            </li>
                            <!-- 启明星隐藏账单区间 -->
                            <?php if (!in_array($invoice->schoolid, array('BJ_DS', 'BJ_SLT', 'BJ_QFF'))) : ?>
                                <li><span><?php echo OA::t("payment", 'Duration', $lang); ?> </span>
                                    <?php echo $invoice->durDate; ?>
                                </li>
                            <?php endif ?>
                                <div class="c"></div>
                            </ul>
                            <ul>
                                <li class="line"></li>
                            </ul>
                            <?php if ($invoice->discount_id) { ?>
                                <ul>
                                    <li><?php echo $invoice->discountTitle . '<' . $invoice->discountPercent . '>'; ?>
                                    </li>
                                    <li><span><?php echo OA::t("payment", 'Amount after discount', $lang); ?> </span>
                                        <?php echo $nf->format('#,##0.00', $invoice->original_amount); ?></li>
                                    <div class="c"></div>
                                </ul>
                            <?php } else { ?>

                                <ul>
                                    <li></li>
                                    <li><span><?php echo OA::t("payment", 'Amount', $lang); ?> </span> <?php echo $nf->format('#,##0.00', $invoice->original_amount); ?>
                                    </li>
                                    <div class="c"></div>
                                </ul>

                                <?php
                            }
                            if ($invoice->depositAmount) {
                                ?>
                                <ul>
                                    <li><span>[<?php echo OA::t("payment", 'Deposit', $lang); ?>]</span></li>
                                    <li><span><?php echo OA::t("payment", 'Amount Paid', $lang); ?> </span> <?php echo $nf->format('#,##0.00', $invoice->depositAmount); ?>
                                    </li>
                                    <div class="c"></div>
                                </ul>

                                <?php
                            }
                            ?>
                            <?php if ($invoice->amount - $invoice->dueAmount) : ?>
                            <ul>
                                <li></li>
                                <li><span><?php echo OA::t("payment", 'Paid Amount', $lang); ?></span> <?php echo $nf->format('#,##0.00', $invoice->amount - $invoice->dueAmount);?>
                                </li>
                                <div class="c"></div>
                            </ul>
                            <?php endif ?>
                            <ul>
                                <li class="line shorter"></li>
                            </ul>
                            <ul>
                                <li></li>
                                <li><span><?php echo OA::t("payment", 'Subtotal', $lang); ?> </span> <?php echo $nf->format('#,##0.00', $invoice->dueAmount); ?>
                                    <font id="subtotal_<?php echo $invoice->invoice_id?>" style='display:none;'><?php echo $invoice->dueAmount?></font>
                                </li>
                                <div class="c"></div>
                            </ul>
                        </div>
                        <?php
                    }
                    ?>
                    <div>
                        <ul>
                            <li class="line shorter bolder2"></li>
                        </ul>
                        <ul>
                            <li></li>
                            <li><span><?php echo OA::t("payment", 'Total', $lang); ?> </span> <font id='cTot'><?php echo $nf->format('#,##0.00', $unpaid_total); ?></font>
                            </li>
                            <div class="c"></div>
                        </ul>
                        <?php if($school_info->type != 50):?>
                        <ul>
                            <li style="width: 240px; margin: 10px; float: right;"><?php echo addColon(OA::t("payment", 'Office Signature', $lang)); ?></li>
                            <li style="width: 240px; margin: 10px; float: right;"><?php echo addColon(OA::t("payment", 'Parent Signature', $lang)); ?></li>
                            <div class="c"></div>
                        </ul>
                        <?php endif;?>
                    </div>

                    <?php
                } else {
                    ?>
                    <div class="no-data"></div>
                <?php } ?>

            </div>

            <?php if (!empty($bank_info)) { ?>
                <div class="transfer-accounts">
                    <ul>
                        <li class="line bolder2"></li>
                    </ul>
                    <!--银行信息-->
                    <?php foreach ($bank_info as $bank) { ?>
                        <ul>
                            <li class="bi_title">
                                <?php
                                if ($bank['title_en']) {
                                    echo '<b>' . $bank['title_en'] . '</b>';
                                }
                                ?> <br> <?php
                                if ($bank['title_cn']) {
                                    echo '<b>' . $bank['title_cn'] . '</b>';
                                }
                                ?>
                            </li>
                            <li class="bi_content">
                                <?php
                                echo $bank['content'];
                                ?>
                            </li>
                            <div class="c"></div>
                        </ul>
                    <?php } ?>
                </div>

            <?php } ?>

            <style type="text/css">
                .print-info .line {
                    background: #000000;
                    width: 98%;
                    border-bottom: 1px solid #999999;
                    margin: 5px;
                    padding: 0px;
                    clear: both;
                    height: 0;
                    _height: 0;
                    line-height: 0;
                    font-size: 0;
                    overflow: hidden;
                    margin: 5px !important;
                    padding: 0px !important;
                }

                .print-info .bolder2 {
                    border-bottom: 2px solid #0A0A0A;
                    margin: 10px 5px;
                }

                .print-info .bolder3 {
                    border-bottom: 3px solid #0A0A0A;
                    margin: 10px 5px;
                }

                .print-info .bolder5 {
                    border-bottom: 5px solid #0A0A0A;
                    margin: 10px 5px;
                }

                .print-info .shorter {
                    width: 42%;
                    float: right;
                }

                .school-info {
                    margin: 10px;
                }

                .school-info li {
                    margin-bottom: 10px;
                }

                .school-info .address {
                    float: left;
                    width: 440px;
                    text-align: left;
                }

                .school-info .logo {
                    float: right;
                }

                .child-info {
                    margin: 10px;
                }

                .child-info .title {
                    font-size: 18px;
                    font-weight: bolder;
                    text-align: center;
                    clear: both;
                }

                .child-info .content li {
                    width: 300px;
                    float: left;
                    margin: 10px;
                    height: 18px;
                }

                .child-info .content li span,.invoice-wrapper ul li span {
                    display: inline-block;
                    width: 130px;
                    font-weight: bolder;
                    text-align: right;
                    padding-right: 10px;
                }

                .invoice-info {
                    margin: 10px;
                }

                .invoice-wrapper ul li {
                    float: left;
                    width: 320px;
                    margin: 10px 0;
                    height: 18px;
                }

                /***     bank account       **/
                .transfer-accounts li {
                    float: left;
                    margin: 10px;
                }

                .transfer-accounts .bi_title {
                    width: 120px;
                    text-align: left;
                }

                .transfer-accounts .bi_content {
                    width: 420px;
                    text-align: left;
                }
                .c{
                    clear:both;
                    height:0;
                    font:0/0 Arial;
                    overflow:hidden;
                    width:0;
                }
            </style>
        </div>
    </div>
    <div class="btn_wrap">
        <div class="btn_wrap_pd">
            <ul class="switch_list fl">
                <?php foreach ($invoice_list as $invoice): ?>
                    <li>
                        <input class="J_invo" type="checkbox" id="<?php echo $invoice->invoice_id ?>" <?php if ($invoice->invoice_id == $invoice_id): ?>checked<?php endif; ?>/>
                        <label for="<?php echo $invoice->invoice_id ?>" title="<?php echo $invoice->title; ?>"><?php echo $invoice->title; ?></label>
                    </li>
                <?php endforeach; ?>
            </ul>
            <div class="fr">
                <button type="button" class="btn fr btn_submit mr10" onclick="_print();">打印</button>
            </div>
            <div class="c"></div>
        </div>
    </div>
</div>

<script type="text/javascript">
    function number_format(num,n)
    {
        var numb = new Number(num);
        numc = String(numb.toFixed(n));
        var re = /(-?\d+)(\d{3})/;
        while(re.test(numc)) numc = numc.replace(re,"$1,$2")
        return numc;
    }
        $('.J_invo').change(function() {
            $('.invoice-info').hide();
            var total = 0;
            $('.J_invo').each(function() {
                if (!!$(this).attr('checked')) {
                    var id = $(this).attr('id');
                    $('#i_' + id).show();
                    total += parseFloat($('#subtotal_'+id).text());
                }
            });
            $('#cTot').text(number_format(total, 2));
        });
        $('input[name=lang]').change(function() {
            window.location.href = '<?php echo $this->createUrl('/child/invoice/printInvoice', array('invoice_id' => $invoice_id)) ?>&lang=' + $(this).val()
        });
        function _print()
        {
            if ($.browser.msie) {
                $("#print_info").printArea({
                    mode: 'popup', popClose: true
                });
            }
            else {
                $("#print_info").printArea();
            }
            return false;
        }
</script>