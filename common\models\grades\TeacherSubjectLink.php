<?php

/**
 * This is the model class for table "ivy_teacher_subject_link".
 *
 * The followings are the available columns in table 'ivy_teacher_subject_link':
 * @property string $schoolid
 * @property integer $teacher_uid
 * @property string $subject_flag
 * @property integer $status
 * @property integer $updated
 * @property integer $updated_uid
 */
class TeacherSubjectLink extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return TeacherSubjectLink the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_teacher_subject_link';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('schoolid, subject_flag', 'required'),
			array('teacher_uid, status, updated, updated_uid', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>10),
			array('subject_flag', 'length', 'max'=>32),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('schoolid, teacher_uid, subject_flag, status, updated, updated_uid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'user'=>array(self::BELONGS_TO,'User','teacher_uid')
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'schoolid' => 'Schoolid',
			'teacher_uid' => 'Teacher Uid',
			'subject_flag' => 'Subject Flag',
			'status' => 'Status',
			'updated' => 'Updated',
			'updated_uid' => 'Updated Uid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('teacher_uid',$this->teacher_uid);
		$criteria->compare('subject_flag',$this->subject_flag,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('updated',$this->updated);
		$criteria->compare('updated_uid',$this->updated_uid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}