<?php

class ServiceController extends ProtectedController
{
    public $batchNum = 50;
    public $printFW;
    public $actionAccessAuths = array(
        'index' => 'oOperationsView',
    );

    public function init()
    {
        parent::init();
        Yii::import('common.models.service.*');

        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'hqOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'HQ Operations');
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/vue2.js');
        // $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');
        // $cs->registerScriptFile($cs->getCoreScriptUrl() . '/jui/js/jquery-ui.min.js');
    }

    public function actionIndex()
    {
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/datatables/jquery.dataTables.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/datatables/dataTables.bootstrap.min.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/datatables/dataTables.bootstrap.min.css');

        $school_id =  Yii::app()->request->getParam('school_id', '');
        $page = Yii::app()->request->getParam('page', 1);

        // 所有校园
        $branch = Branch::model()->getBranchList();

        $serviceInfoConfig = Service::getServiceInfo();
        $configId = array();
        foreach ($serviceInfoConfig as $key => $val) {
            foreach ($val['targets'] as $schoolid) {
                $serviceInfoConfig[$key]['schoollist'][$schoolid] = $branch[$schoolid];
            }
            if (in_array($this->staff->uid, $val['manager']) || in_array($this->staff->uid, $val['vendor'])) {
                $configId[] = $key;
            }
        }


        $offset = ($page - 1) * $this->batchNum;

        $criteria = new CDbCriteria;
        $criteria->compare('service_id', $configId);
        $criteria->compare('status', array(1, 2));
        if ($school_id) $criteria->compare('school_id', $school_id);
        $criteria->limit = $this->batchNum;    //取1条数据，如果小于0，则不作处理
        $criteria->offset = $offset;   //两条合并起来，则表示 limit 10 offset 1,或者代表了。limit 1,10
        $criteria->order = "status ASC, start_time ASC";
        $serviceModel = Service::model()->findAll($criteria);

        $data = array();
        if ($serviceModel) {
            foreach ($serviceModel as $val) {
                $serviceField = array();
                if ($val->serviceField) {
                    foreach ($val->serviceField as $item) {
                        $fileld_content = $item->fileld_content;
                        if ($serviceInfoConfig[$val->service_id]['requestFields'][$item->fileld]['type'] == 'timestamp') {
                            $fileld_content = date("Y-m-d", $fileld_content);
                        }
                        $serviceField[$item->fileld] = $fileld_content;
                    }
                }
                $serviceExpense = array();
                if ($val->serviceExpense) {
                    foreach ($val->serviceExpense as $item) {
                        $expenseFileldContent = $item->fileld_content;
                        if(in_array($item->fileld, array(1,2,3))){
                            $expenseFileldContent = sprintf("%.2f", $item->fileld_content);
                        }
                        $serviceExpense[$item->fileld] = $expenseFileldContent;
                    }
                }
                $data[$val->id] = array(
                    'id' => $val->id,
                    'service_id' => $val->service_id,
                    'school_title' => $branch[$val->school_id],
                    'service_title' => $val->service_title,
                    'start_time' => date('Y-m-d', $val->start_time),
                    'end_time' => date('Y-m-d', $val->end_time),
                    'status' => $val->status,
                    'serviceField' => $serviceField,  // 校园提交得服务数据
                    'serviceExpense' => $serviceExpense, // 负责人提交得报销数据
                );
            }
        }

        $criteria = new CDbCriteria;
        $criteria->compare('status', array(1, 2));
        $total = Service::model()->count($criteria);
        $totalPages = ($total) ? ceil($total / $this->batchNum) : 0;

        $this->render('index', array(
            'data' => $data,
            'config' => $serviceInfoConfig,
            'branch' => $branch,
            'school_id' => $school_id,
            'totalPages' => $totalPages,
            'page' => $page,
        ));
    }

    /**
     *  服务人员增加费用
     * @return void
     */
    public function actionNew()
    {
        $id = Yii::app()->request->getParam('id', '');
        $service_data = Yii::app()->request->getParam('service_data', array());

        $transaction = Yii::app()->subdb->beginTransaction();
        $serviceInfo = Service::getServiceInfo();
        try {
            $model = Service::model()->findByPk($id);
            if($model->status == 0){
                $transaction->rollback();
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '校园已经取消！请联系校园。');
                $this->showMessage();
            }
            $model->status = 2;
            $model->vendor_status = 1;
            $model->vendor_by = $this->staff->uid;
            $model->vendor_at = time();
            if (!$model->save()) {
                $transaction->rollback();
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err ? $err[0] : '失败');
                $this->showMessage();
            }
            $config = $serviceInfo[$model->service_id]['expenseFields'];

            $judge = Service::judge($config, $service_data);
            if (!$judge['status']) {
                $transaction->rollback();
                $this->addMessage('state', 'fail');
                $this->addMessage('message', $judge['message']);
                $this->showMessage();
            }

            if ($service_data) {
                foreach ($service_data as $key => $val) {
                    $configStatus = $serviceInfo[$model->service_id]['expenseFields'][$key];
                    $ruleJudge = Service::ruleJudge($configStatus, $val);
                    if (!$ruleJudge['status']) {
                        $transaction->rollback();
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', $ruleJudge['message']);
                        $this->showMessage();
                    }

                    $modelServiceField = new ServiceExpense();
                    $modelServiceField->sid = $model->id;
                    $modelServiceField->fileld = $key;
                    $modelServiceField->fileld_content = $ruleJudge['date'];

                    if (!$modelServiceField->save()) {
                        $transaction->rollback();
                        $this->addMessage('state', 'fail');
                        $err = current($modelServiceField->getErrors());
                        $this->addMessage('message', $err ? $err[0] : '失败');
                        $this->showMessage();
                    }
                }
            }

            $transaction->commit();
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message', 'success'));
            $this->showMessage();
        } catch (Exception $e) {
            $transaction->rollBack();
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '操作错误');
            $this->showMessage();
        }
    }

    /*
	 * 总部人员确定服务  // 暂时无用
	 */
    public function actionFix()
    {
        $service_id =  Yii::app()->request->getParam('service_id', '');
        $model = Service::model()->findByPk($service_id);
        if ($model) {
            $model->vendor_id = $model->vendor_id == 1 ? 0 : 1;
            if (!$model->save()) {
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err ? $err[0] : '失败');
                $this->showMessage();
            }
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '确认成功');
            $this->showMessage();
        }
        $this->addMessage('state', 'fail');
        $this->addMessage('message', '参数错误');
        $this->showMessage();
    }


    /*
     * 报销明细
     *
     * 月份  校园
     */

    public function actionPrintExpenses()
    {
        $this->layout = "//layouts/print";
        $month =  Yii::app()->request->getParam('month', '');
        $schoolid =  Yii::app()->request->getParam('schoolid', '');

        if($month) {
            $year = substr($month,0,4);
            $months = substr($month,4);
            $stratTime =  $year . '-' . $months . '-01';
            $strat_time = strtotime($stratTime);
            $end_time = strtotime("$stratTime +1 month -1 day");

        }else {
            $year = date("Y", time());
            $months = date("m", time());
            $allday = date("t", time());
            $strat_time = strtotime($year . "-" . $months . "-1");
            $end_time = strtotime($year . "-" . $months . "-" . $allday);
        }

        $criteria = new CDbCriteria;
        $criteria->compare('status', 2);
        $criteria->compare('start_time', ">=$strat_time");
        $criteria->compare('start_time', "<=$end_time");
        if ($schoolid) $criteria->compare('school_id', $schoolid);
        $criteria->order = 'start_time ASC';
        $model = Service::model()->findAll($criteria);

        $data = array();
        if ($model) {
            $serviceInfo = Service::getServiceInfo();
            $branchModle = new Branch();
            $branch = $branchModle->getBranchList();
            foreach ($model as $val) {
                foreach ($val->serviceExpense as $item) {
                    if ($item->fileld_content && in_array($item->fileld, array(1,2,3))) {
                        $data[] = array(
                            'service_id' => $item->sid,
                            'service_title' => $val->service_title,
                            'time' => date("Y-m-d" , $val->start_time),
                            'schoolid' => $branch[$val->school_id],
                            'title' => $serviceInfo[$val->service_id]['expenseFields'][$item->fileld]['title'],
                            'money' => $item->fileld_content,
                            'fileld' => $item->fileld,
                        );
                    }
                }
            }
        }

        $this->render('prints', array(
            'data' => $data,
            'strat_time' => $strat_time,
        ));
    }
}
