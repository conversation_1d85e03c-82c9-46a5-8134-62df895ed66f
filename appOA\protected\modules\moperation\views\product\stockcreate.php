<?php $form=$this->beginWidget('CActiveForm', array(
    'id'=>'points-stock-form',
    'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
));
$productModel = PointsProduct::model()->findByPk($model->product_id);
?>
<div class="pop_cont" style="height:auto;">
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'product_id'); ?></label>
        <div class="col-xs-9">
            <?php echo $productModel->getContent(); ?><?php echo $form->hiddenField($model,'product_id');?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'num'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->numberField($model,'num', array('maxlength'=>255,'class'=>'form-control length_2')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'memo'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textArea($model,'memo', array('class'=>'form-control', 'rows'=>3)); ?>
        </div>
    </div>
</div>
<div class="pop_bottom">
    <button id="J_dialog_close" type="button" class="btn btn-default pull-right"><?php echo Yii::t('global','Cancel');?></button>
    <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
</div>
<?php $this->endWidget(); ?>

<div style="height:200px;overflow-y:auto;">
<?php $this->widget('ext.ivyCGridView.BsCGridView', array(
	'id'=>'points-stock-grid',
	'dataProvider'=>$model->search(),
//	'filter'=>$model,
    'colgroups'=>array(
        array(
            "colwidth"=>array(null,80,null,100,50),
        )
    ),
	'columns'=>array(
		array(
            'name'=>'product_id',
            'value'=>'$data->Product->getContent()',
        ),
        array(
        	'name'=>'num',
            'value'=>'$data->num',
        ),
        array(
        	'name'=>'memo',
            'value'=>'$data->memo',
        ),
 		array(
            'name'=>'update_timestamp',
            'value'=>'OA::formatDateTime($data->update_timestamp)',
        ),
		array(
			'class'=>'BsCButtonColumn',
		 	'template' => '{delete}',
            'deleteButtonUrl'=>'Yii::app()->createUrl("/moperation/product/stockDelete", array("id" => $data->id))',
		),
	),
)); 
?>
</div>
<script type="text/javascript">
function callback(data)
{
    $.fn.yiiGridView.update('points-stock-grid');
}
</script>