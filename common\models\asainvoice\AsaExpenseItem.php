<?php

/**
 * This is the model class for table "ivy_asa_expense_item".
 *
 * The followings are the available columns in table 'ivy_asa_expense_item':
 * @property integer $id
 * @property integer $eid
 * @property string $content
 * @property string $class
 * @property string $subject
 * @property string $files
 * @property integer $groupid
 * @property integer $courseid
 * @property double $amount
 * @property integer $uid
 * @property integer $updated
 */
class AsaExpenseItem extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_asa_expense_item';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('amount, groupid, uid, updated', 'required'),
			array('eid, groupid, courseid, uid, updated', 'numerical', 'integerOnly'=>true),
			array('amount', 'numerical'),
			array('content, class, subject', 'length', 'max'=>255),
			array('files', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, eid, content, class, subject, files, groupid, courseid, amount, uid, updated', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'asaExpense' => array(self::HAS_ONE, 'AsaExpense', array('id'=>'eid')),
			'asaCourse' => array(self::HAS_ONE, 'AsaCourse', array('id'=>'courseid')),
			'asaCourseGroup' => array(self::HAS_ONE, 'AsaCourseGroup', array('id'=>'groupid')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'eid' => 'Eid',
			'content' => 'Content',
			'class' => 'Class',
			'subject' => 'Subject',
			'files' => 'Files',
			'groupid' => Yii::t('asa','课程组'),
			'courseid' => 'Courseid',
			'amount' => Yii::t('asa','金额'),
			'uid' => 'Uid',
			'updated' => 'Updated',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('eid',$this->eid);
		$criteria->compare('content',$this->content,true);
		$criteria->compare('class',$this->class,true);
		$criteria->compare('subject',$this->subject,true);
		$criteria->compare('files',$this->files,true);
		$criteria->compare('groupid',$this->groupid);
		$criteria->compare('courseid',$this->courseid);
		$criteria->compare('amount',$this->amount);
		$criteria->compare('uid',$this->uid);
		$criteria->compare('updated',$this->updated);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AsaExpenseItem the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
