<?php
$this->pageTitle = Yii::app()->name . ' - ' . Yii::t("user", "Login");
$this->breadcrumbs = array(
    Yii::t("user", "Login"),
);
?>

<h1><?php echo Yii::t("user", "Login"); ?></h1>

<?php if(isset(Yii::app()->params['preventDirectLogin']) && Yii::app()->params['preventDirectLogin'] === true):?>

    <p>
    <?php echo Yii::t("message","Sorry, this site is not allow to login."); ?>
    </p>

<?php else: ?>

        <?php if (Yii::app()->user->hasFlash('loginMessage')): ?>
        
            <div class="success">
                <?php echo Yii::app()->user->getFlash('loginMessage'); ?>
            </div>
        
        <?php endif; ?>
        
        <div class="form">
            <?php echo CHtml::beginForm(); ?>
        
            <?php
            foreach (Yii::app()->user->getFlashes() as $key => $message) {
                echo '<div class="flash-' . $key . '">' . $message . "</div>\n";
            }
            ?>
        
            <?php echo CHtml::errorSummary($model,''); ?>
        
            <div class="row">
                <?php echo CHtml::activeLabelEx($model, 'username'); ?>
                <?php echo CHtml::activeTextField($model, 'username', array('class' => 'wider')) ?>
            </div>
        
            <div class="row">
                <?php echo CHtml::activeLabelEx($model, 'password'); ?>
                <?php echo CHtml::activePasswordField($model, 'password', array('class' => 'wider')) ?>
            </div>
        
            <div class="row">
                <p class="hint">
                    <?php echo CHtml::link(Yii::t("user", "Lost Password?"), Yii::app()->getModule('user')->recoveryUrl); ?>
                </p>
            </div>
        
            <div class="row rememberMe">
                <?php echo CHtml::activeCheckBox($model, 'rememberMe'); ?>
                <?php echo CHtml::activeLabelEx($model, 'rememberMe'); ?>
            </div>
        
            <div class="row submit">
                <?php echo CHtml::submitButton(Yii::t("user", "Login"), array('class' => 'wider bigger')); ?>
            </div>
        
            <?php echo CHtml::endForm(); ?>
        </div><!-- form -->
        
        
        <?php
        $form = new CForm(array(
                    'elements' => array(
                        'username' => array(
                            'type' => 'text',
                            'maxlength' => 32,
                        ),
                        'password' => array(
                            'type' => 'password',
                            'maxlength' => 32,
                        ),
                        'rememberMe' => array(
                            'type' => 'checkbox',
                        )
                    ),
                    'buttons' => array(
                        'login' => array(
                            'type' => 'submit',
                            'label' => 'Login',
                        ),
                    ),
                        ), $model);
        ?>
        
<?php endif;?>