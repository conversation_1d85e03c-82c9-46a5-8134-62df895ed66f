<?php

class Schedule2Controller extends BranchBasedController
{
    // 访问action的初级权限
    public $actionAccessAuths = array(
        'saveSchedulePlan' => 'o_A_scheduleMgt',
        'schedule'         => 'o_A_scheduleMgt',
        'getAssignClass'   => 'o_A_scheduleMgt',
        'delScheduleItem'  => 'o_A_scheduleMgt',
    );

    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Campus Workspace');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mgrades/schedule2/index');
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/element/vue.global.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl.'/base/js/element/index.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/element/index.js');
        Yii::import('common.models.calendar.*');
    }

    public function actionIndex()
    {
        $this->render('index');
    }

    public function actionDetail()
    {
        $this->render('detail');
    }

    public function actionOnduty()
    {
        $this->render('onduty');
    }

    //课表首页数据
    public function actionIndexData()
    {
        $yid = Yii::app()->request->getParam('yid', 0);
        $url = 'schedule/indexData';
        $this->remote2($url, array('yid' => $yid));
    }

    //时间段数据
    public function actionSlotList()
    {
        $startyear = Yii::app()->request->getParam('startyear', 0);
        $yid = Yii::app()->request->getParam('yid', 0);
        $url = 'schedule/slotList';
        $this->remote2($url, array('startyear' => $startyear, 'yid' => $yid));
    }

    //添加时间段
    public function actionStoreSlot()
    {
        $data = Yii::app()->request->getParam('data', 0);
        $startyear = Yii::app()->request->getParam('startyear', 0);
        $url = 'schedule/storeSlot';
        $this->remote2($url, array('data' => $data, 'startyear' => $startyear),'post');
    }

    //获取科目老师
    public function actionSubjectTeacher()
    {
        $url = 'schedule/subjectTeacher';
        $this->remote2($url);
    }

    //课表适用的班级
    public function actionApplyClass()
    {
        $url = 'schedule/applyClass';
        $schedule_id = Yii::app()->request->getParam('schedule_id', 0);
        $this->remote2($url, array('schedule_id' => $schedule_id));
    }

    //课表下的执教的老师
    public function actionApplyTeacher()
    {
        $url = 'schedule/applyTeacher';
        $startyear = Yii::app()->request->getParam('startyear', 0);
        $this->remote2($url, array('startyear' => $startyear));
    }

    //按照班级获取课程值日安排
    public function actionListByClass()
    {
        $url = 'schedule/listByClass';
        $classid = Yii::app()->request->getParam('class_id', 0);
        $startyear = Yii::app()->request->getParam('startyear', 0);
        $this->remote2($url, array('startyear' => $startyear, 'classid' => $classid));
    }

    //按照老师获取课程值日安排
    public function actionListByTeacher()
    {
        $url = 'schedule/listByTeacher';
        $startyear = Yii::app()->request->getParam('startyear', 0);
        $teacher_id = Yii::app()->request->getParam('teacher_id', 0);
        $this->remote2($url, array('startyear' => $startyear, 'teacher_id' => $teacher_id));
    }

    //保存课程安排
    public function actionSaveCourse()
    {
        $url = 'schedule/saveCourse';
        $data = Yii::app()->request->getParam('data', array());
        $this->remote2($url, array('data' => $data), 'post');
    }

    /*
     * 清空班级所有课程安排
    */
    public function actionClearCourse()
    {
        $url = 'schedule/clearCourse';
        $classid = Yii::app()->request->getParam('classid', 0);
        $schedule_id = Yii::app()->request->getParam('schedule_id', 0);
        $this->remote2($url, array('classid' => $classid, 'schedule_id' => $schedule_id), 'delete');
    }

    //删除指定课程安排
    public function actionDeleteCourse()
    {
        $url = 'schedule/deleteCourse';
        $item_id = Yii::app()->request->getParam('item_id', 0);
        $this->remote2($url, array('item_id' => $item_id), 'delete');
    }

    public function remote2($requestUrl, $requestData = array(), $method = 'get')
    {
        if (empty($requestData['school_id'])) {
            $requestData['school_id'] = $this->branchId;
        }
        $res = CommonUtils::requestDsOnline2($requestUrl, $requestData, $method);
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        }
        $this->showMessage();
    }
}