<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Campus Support'), array('//mcampus/default/index'))?></li>
        <li class="active"><?php echo Yii::t('site','Catering Management');?></li>
    </ol>
    <div class="row">
        <div class="col-md-2">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items'=>$this->leftMenu,
                'htmlOptions' => array('class'=>'nav nav-pills nav-stacked text-right background-gray')
            ));
            ?>
        </div>
        <div class="col-md-10">
            <div class="mb10">
                <a href="<?php echo $this->createUrl('edit')?>" class="btn btn-primary">
                    <span class="glyphicon glyphicon-plus"></span>
                    <?php echo Yii::t('lunch', 'Add New Menu');?>
                </a>
            </div>
            <?php
                $this->widget('ext.ivyCGridView.BsCGridView', array(
                    'id'=>'lunch-grid',
                    'dataProvider'=>$dataProvider,
                    'afterAjaxUpdate'=>'js:function(){head.Util.ajaxDel()}',
                    'template'=>"<div class='table_list'>{items}</div><div class='table_list'>{pager}</div>",
                    'colgroups'=>array(
                        array(
                            "colwidth"=>array(null, 300),
                        )
                    ),
                    'columns'=>array(
                        'title_cn',
                        array(
                            'value' => array($this, 'getButton'),
                        )
                    ),
                ));
            ?>
        </div>
    </div>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>

<div class="modal fade" id="copyModal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title"><?php echo Yii::t('lunch', 'Copy The Menu');?></h4>
            </div>
            <form action="<?php echo $this->createUrl('copy')?>" method="post" class="J_ajaxForm form-horizontal">
                <?php echo CHtml::hiddenField('oldid');?>
                <div class="modal-body">
                    <p>
                        <div class="form-group">
                            <label class="col-sm-3 control-label"><?php echo Yii::t('lunch', 'Orignial Title');?></label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="copy-old"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="newMenu"><?php echo Yii::t('lunch', 'New Title');?></label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="newMenu" name="newName">
                            </div>
                        </div>
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel');?></button>
                    <button type="button" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'OK');?></button>
                </div>
            </form>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<script>
function copy(_this, id)
{
    var oldName = $(_this).parents('tr').find('td:first').html();
    $('#copyModal #copy-old').html(oldName);
    $('#oldid').val(id);
    $('#copyModal').modal();
}
function cbReload()
{
    $.fn.yiiGridView.update("lunch-grid");
}

function edits() {
    resultTip({error: 'warning', msg: '已审核！不可修改！'});
}
</script>