<?php

class VisitController extends BranchBasedController
{
//	public $layout='//layouts/column2';
    
    public $dialogWidth = 500;
    public function init() {
        parent::init();
        Yii::import('common.models.visit.*');
    }
    
    public function beforeAction($action){
        parent::beforeAction($action);
		if($action->getId() != 'nopermit'){
			if(!Yii::app()->user->checkAccess('tVisitAdmin')){
				$this->forward('nopermit');
			}
		}
		return true;
	}
	
	public function actionNopermit(){
		echo 'No Permission';
	}
    
	public function actionIndex($branchId='', $type='pending')
	{
        $this->branchSelectParams["urlArray"] = array("//operations/visit/index", 'type'=>$type);
        
		$model=new IvyschoolsVisit('search');
		$model->unsetAttributes();  // clear any default values
		if(isset($_GET['IvyschoolsVisit']))
			$model->attributes=$_GET['IvyschoolsVisit'];
        
        if ($branchId){
            $model->schoolid = $branchId;
        }
        else{
            $model->schoolid=$this->branchId;
        }
        $model->status=0;
        
        $mdataProvider = $model->searchDate();
        
        $model = new IvyVisitsRecord;
        $model->unsetAttributes();
        if(isset($_GET['IvyVisitsRecord']))
			$model->attributes=$_GET['IvyVisitsRecord'];
        
        if ($branchId){
            $model->schoolid = $branchId;
        }
        else{
            $model->schoolid=$this->branchId;
        }
        $model->category = 'appointment';

        $criteria=new CDbCriteria;

		$criteria->compare('category', 'appointment');
        if ($branchId){
            $criteria->compare('schoolid', $branchId);
        }
        
        $dataProvider = $model->searchDate();

        $schoolsCount = array();
        if ($type == 'pending')
            $sql = 'select schoolid, count(*) as c from '.IvyschoolsVisit::model()->tableName().' where status=0 group by schoolid';
        else
            $sql = "select schoolid, count(*) as c from ".IvyVisitsRecord::model()->tableName()." where category='appointment' and appointment_date=".strtotime("today")." group by schoolid";
        $sc=Yii::app()->db->createCommand($sql)->queryAll();
        foreach ($sc as $_sc){
            $schoolsCount[$_sc['schoolid']]=$_sc['c'];
        }

        $assignData = array(
			'mdataProvider'=>$mdataProvider,
            'dataProvider'=>$dataProvider,
            'schoolsCount'=>CJSON::encode($schoolsCount),
		);
        
        $this->render('index',$assignData);
	}
	
    public function actionSelect($type='pending')
	{
        $schools = $this->getAllBranch();

        if ($type == 'pending')
            $sql = 'select schoolid, count(*) as c from '.IvyschoolsVisit::model()->tableName().' where status=0 group by schoolid';
        else
            $sql = "select schoolid, count(*) as c from ".IvyVisitsRecord::model()->tableName()." where category='appointment' and appointment_date=".strtotime("today")." group by schoolid";
        $sc=Yii::app()->db->createCommand($sql)->queryAll();
        foreach ($sc as $_sc){
            if (in_array($_sc['schoolid'], array_keys($schools))){
                $schools[$_sc['schoolid']]['c']=$_sc['c'];
            }
        }
        
		$this->branchSelectParams["urlArray"] = array("//operations/visit/index", 'type'=>$type);
		$this->render('select', array('schools'=>$schools, 'type'=>$type));
	}
	
	/**
	 * Updates a particular model.
	 * If update is successful, the browser will be redirected to the 'view' page.
	 * @param integer $id the ID of the model to be updated
	 */
	public function actionUpdate($id=0)
	{
        $this->layout='//layouts/dialog';
        $this->dialogWidth = 550;
		$model=$this->loadModel($id);
		$model->visit_date = OA::formatDateTime($model->visit_date);
		$model->birthdate = OA::formatDateTime($model->birthdate);
		// Uncomment the following line if AJAX validation is needed
		// $this->performAjaxValidation($model);

		if(isset($_POST['IvyschoolsVisit']))
		{
			$model->attributes=$_POST['IvyschoolsVisit'];
			$model->visit_date = strtotime($model->visit_date);
			$model->birthdate = strtotime($model->birthdate);
			$model->create_date = time();
			if($model->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('callback', 'updateCB');
            }
            else {
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err ? $err[0] : '失败');
            }
            $this->showMessage();
		}
		$model->visit_date = OA::formatDateTime($model->visit_date);
		$model->birthdate = OA::formatDateTime($model->birthdate);
		$this->render('update',array(
			'model'=>$model,
		));
	}

	/**
	 * Deletes a particular model.
	 * If deletion is successful, the browser will be redirected to the 'index' page.
	 * @param integer $id the ID of the model to be deleted
	 */
	public function deleteInfo($id)
	{
		IvyschoolsVisit::model()->updateByPk($id, array('status'=>3));
        return 'ok';

		// if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
//		if(!isset($_GET['ajax']))
//			$this->redirect(isset($_POST['returnUrl']) ? $_POST['returnUrl'] : array('admin'));
	}
	
	/**
	 * Returns the data model based on the primary key given in the GET variable.
	 * If the data model is not found, an HTTP exception will be raised.
	 * @param integer the ID of the model to be loaded
	 */
	public function loadModel($id)
	{
		$model=IvyschoolsVisit::model()->findByPk((int)$id);
		if($model===null)
			throw new CHttpException(404,'The requested page does not exist.');
		return $model;
	}
	
	public function saveReadyInfo($id)
	{
		$model = $this->loadModel($id);
        $cfgs = OA::LoadConfig('CfgVisit');
		if (!empty($model))
		{
			$basicModel = IvyVisitsBasicInfo::model()->getOneVisitBasicInfo($model->phone,$model->email);
			if (empty($basicModel))
			{
				$basicModel = new IvyVisitsBasicInfo();
			}
			else
			{
				$bakBasicModel = new IvyVisitsBasicInfoBak();
				$bakBasicModel->setAttributes($basicModel->getAttributes());
				$bakBasicModel->save();
			}
			$basicModel->child_name = $model->child_name;
			$basicModel->parent_name = $model->parent_name;
			$basicModel->birth_timestamp = $model->birthdate;
			$basicModel->tel = $model->phone;
			$basicModel->email = $model->email;
			$basicModel->address = $model->address;
			$basicModel->communication = 0;
			$basicModel->child_enroll = 0;
			$basicModel->country = 0;
			$basicModel->knowus = $model->knowus;
			$basicModel->memo = $model->memo;
			$basicModel->register_timestamp = $model->create_date;
			$basicModel->status = 10;
			$basicModel->childid = 0;
			$basicModel->receive_language = $cfgs['language'][$model->receive_language]['cn'];
			$basicModel->update_timestamp = time();
			$basicModel->state_timestamp = time();
			$basicModel->recent_log = time();
			$basicModel->update_user = Yii::app()->user->id;
			if ($basicModel->save())
			{
				$basicModel->refresh();
				$visitModel = new IvyVisitsRecord();
				$visitModel->basic_id = $basicModel->id;
				$visitModel->event_id = 0;
				$visitModel->category = 'appointment';
				$visitModel->schoolid = $model->schoolid;
				$visitModel->visit_timestamp = $model->visit_date;
				$visitModel->parent_num = 0;
				$visitModel->child_num = 0;
				$visitModel->is_ivyparent = 0;
				$visitModel->appointment_date = $model->visit_date;
				$visitModel->appointment_time = $model->visit_time;
				$visitModel->status = 0;
				$visitModel->update_timestamp = time();
				$visitModel->update_user = Yii::app()->user->id;
				if ($visitModel->save())
				{
					$visitModel->refresh();
                    //$this->deleteInfo($id);
					return "ok";
				}
			}
		}
		else
			throw new CHttpException(400,'Invalid request. Please do not repeat this request again.');
	}
	
	Public function saveUnreadyInfo($id)
	{
		$model = $this->loadModel($id);
        $cfgs = OA::LoadConfig('CfgVisit');
		if (!empty($model))
		{
			$basicModel = IvyVisitsBasicInfo::model()->getOneVisitBasicInfo($model->phone,$model->email);
			if (empty($basicModel))
			{
				$basicModel = new IvyVisitsBasicInfo();
			}
			else
			{
				$bakBasicModel = new IvyVisitsBasicInfoBak();
				$bakBasicModel->setAttributes($basicModel->getAttributes());
				$bakBasicModel->save();
			}
			$basicModel->child_name = $model->child_name;
			$basicModel->parent_name = $model->parent_name;
			$basicModel->birth_timestamp = $model->birthdate;
			$basicModel->tel = $model->phone;
			$basicModel->email = $model->email;
			$basicModel->address = $model->address;
			$basicModel->communication = 0;
			$basicModel->child_enroll = 0;
			$basicModel->country = 0;
			$basicModel->knowus = $model->knowus;
			$basicModel->memo = $model->memo;
			$basicModel->register_timestamp = $model->create_date;
			$basicModel->status = 10;
			$basicModel->childid = 0;
            $basicModel->receive_language = $cfgs['language'][$model->receive_language]['cn'];
			$basicModel->update_timestamp = time();
			$basicModel->state_timestamp = time();
			$basicModel->recent_log = time();
			$basicModel->update_user = Yii::app()->user->id;
			if ($basicModel->save())
			{
				$basicModel->refresh();
                //$this->deleteInfo($id);
				return "ok";
			}
		}
		else
			throw new CHttpException(400,'Invalid request. Please do not repeat this request again.');
	}
    
    public function actionAview($id)
    {
        $this->layout='//layouts/dialog';
        $this->dialogWidth=580;
        if ($id){
            $model = IvyschoolsVisit::model()->findByPk($id);

            if(isset($_POST['IvyschoolsVisit'])){
                if($model != null){
                    $model->setScenario('aview');
                    $model->attributes=$_POST['IvyschoolsVisit'];
                    $model->visit_date = strtotime($model->visit_date);
                    if ($model->save()){
                        $tmp = '';
                        OA::LoadConfig('CfgVisit');
                        if ($model->status == VISIT_USELESS_INFO)
                        {
                            $tmp = $this->deleteInfo($id);
                        }
                        elseif ($model->status == VISIT_READY_INFO)
                        {
                            $tmp = $this->saveReadyInfo($id);
                        }
                        elseif ($model->status == VISIT_UNREADY_INFO)
                        {
                            $tmp = $this->saveUnreadyInfo($id);
                        }
                        if ($tmp == 'ok'){
                            $this->addMessage('state', 'success');
                            $this->addMessage('message', Yii::t('message','success'));
                            $this->addMessage('callback', 'updateCB');
                        }
                        else{
                            $this->addMessage('state', 'fail');
                            $this->addMessage('message', '失败');
                        }
                    }
                    else{
                        $this->addMessage('state', 'fail');
                        $err = current($model->getErrors());
                        $this->addMessage('message', $err ? $err[0] : '失败');
                    }
                }
                else{
                    $this->addMessage('state', 'success');
                    $this->addMessage('callback', 'updateCB');
                    $this->addMessage('message','对象丢失！');
                }
                $this->showMessage();
            }

            $model->visit_date = OA::formatDateTime($model->visit_date);
            $this->render('aview',array('model'=>$model));
        }
    }

    public function actionTargetDate($type='pending')
    {
        $tdate = array();
        if ($type == 'pending'){
            $sql = "select visit_date from ivy_ivyschools_visit where status=0 and schoolid='".$this->branchId."'";
            $rows = Yii::app()->db->createCommand($sql)->queryAll();
            foreach ($rows as $date){
                $vistimestamp = date('Ynj', $date['visit_date']);
                $tdate[$vistimestamp] = $vistimestamp;
            }
        }
        else{
            $sql = "select appointment_date from ivy_visits_record where schoolid='".$this->branchId."' and appointment_date>=".strtotime('-60 day');
            $rows = Yii::app()->db->createCommand($sql)->queryAll();
            foreach ($rows as $date){
                $vistimestamp = date('Ynj', $date['appointment_date']);
                $tdate[$vistimestamp] = $vistimestamp;
            }
        }
        echo CJSON::encode($tdate);
    }

    public function actionChangeDate($id=0)
    {
        $this->layout='//layouts/dialog';
        if ($id){
            $model = IvyVisitsRecord::model()->findByPk($id);

            if(isset($_POST['IvyVisitsRecord'])){
                if ($model != null){
                    $model->setScenario('changedate');
                    $model->appointment_date = strtotime($_POST['IvyVisitsRecord']['appointment_date']);
                    $model->appointment_time = $_POST['IvyVisitsRecord']['appointment_time'];
                    if ( $model->save() ){
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', Yii::t('message','success'));
                        $this->addMessage('callback', 'updateCB');
                    }
                    else{
                        $this->addMessage('state', 'fail');
                        $err = current($model->getErrors());
                        $this->addMessage('message', $err ? $err[0] : '失败');
                    }
                }
                else{
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', '对象丢失！');
                    $this->addMessage('callback', 'updateCB');
                }
                $this->showMessage();
            }

            $model->appointment_date = OA::formatDateTime($model->appointment_date);
            $this->render('changedate', array('model'=>$model));
        }
    }
}