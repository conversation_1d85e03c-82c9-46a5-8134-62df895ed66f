body, .ui-widget
{
	font: normal 10pt "Microsoft Yahei",Arial,Helvetica,sans-serif !important;
}

.sf-menu ul.subitem {
    width: 100%;
}
.sf-menu .item-4 ul {
    width: 130px !important;
}
.sf-menu .mainitem{
    width: 110px;
    text-align: left;
}
.sf-menu a.sf-with-ul {
	padding-left: 1.5em;
	text-align: left;
}
.key-reminder h5.title{
	margin:0;
	padding:0;
	height: 35px;
	width: 180px;
	position: absolute;
	background:url("../images/keyreminder/title_cn_bg.png") no-repeat left top transparent;
	left: 46px;
	top: 20px;
}
.key-reminder:hover h5.title{
	background:url("../images/keyreminder/title_cn_bg.png") no-repeat left -35px transparent;
}

.week-misc{
	background: url("../images/weekmisc_cn.png") no-repeat 1px top transparent;
}
.week-misc .title{
	background: url("../images/week_title_cn.png") no-repeat 1px top transparent;
}
.week-misc:hover .title{
	background: url("../images/week_title_cn.png") no-repeat 1px -38px transparent;
}
.week-misc .menu:hover a{
	background: url("../images/weekmisc_hover_cn.png") no-repeat left top transparent;	
}
.week-misc .calendar:hover a{
	background: url("../images/weekmisc_hover_cn.png") no-repeat left -60px transparent;	
}
#birthday-countdown{
	background:url("../images/birthday_bg_cn.png") no-repeat left top transparent;
}
#birthday-countdown .num-days{
    left: 155px;
	top: 2px;
}
#birthday-countdown .num-year{
    left: 350px;
    position: absolute;
    top: 40px;
}