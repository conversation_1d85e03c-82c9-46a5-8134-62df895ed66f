<?php $typeList = CommonUtils::LoadConfig('CfgASA');
$jobType = array();
foreach ($typeList['job_type'] as $k => $job_type) {
    $jobType[$k] = (Yii::app()->language == 'zh_cn') ? $job_type['cn'] : $job_type['en'];
}

$type = array();
foreach ($typeList['type'] as $k => $job_type) {
    $type[$k] = (Yii::app()->language == 'zh_cn') ? $job_type['cn'] : $job_type['en'];
}

?>
    <style>
        .bggray {
            background: #eee;
            border-radius: 4px;
        }

        .course_attendance {
            color: #fff;
            background: gray;
            padding: 10px;
            border-radius: 50%;
        }

        .course_attendanced {
            color: #fff;
            background: #5cb85c;
            padding: 10px;
            border-radius: 50%;
        }

        .teacher_notsignin {
            border-radius: 4px;
            background: #eee;
            text-decoration: line-through
        }

        .teacher_spare {
            border-radius: 4px;
            background: #f7ecb5;
        }

        .teacher_spare > .not_attendance_teacher {
            text-decoration: line-through
        }
    </style>
    <div class="container-fluid">
        <ol class="breadcrumb">
            <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
            <li><?php echo CHtml::link(Yii::t('site', 'Basic'), array('//mcampus/default/index')) ?></li>
            <li><?php echo CHtml::link(Yii::t('site', '课后课管理'), array('//mcampus/asainvoice/index')) ?></li>
            <li class="active">课后课管理</li>
        </ol>

        <div class="row">
            <!-- 左侧菜单 -->
            <div class="col-md-2 col-sm-2 mb10">
                <?php
                $this->widget('zii.widgets.CMenu', array(
                    'items' => $this->getMenu(),
                    'id' => 'pageCategory',
                    'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                    'activeCssClass' => 'active',
                    'itemCssClass' => ''
                ));
                ?>
            </div>

            <div class="col-md-10 col-sm-12">
                <!-- Nav tabs -->
                <ul class="nav nav-tabs" role="tablist">
                    <li role="presentation" class="active"><a href="#day_attendance" data-toggle="tab">考勤管理</a></li>
                    <li role="presentation"><a href="#month_attendance" aria-controls="profile" role="tab"
                                               data-toggle="tab">考勤汇总</a></li>
                </ul>

                <!-- Tab panes -->
                <div class="tab-content">
                    <div role="tabpanel" class="tab-pane pt10 active" id="day_attendance">
                        <div class="col-md-2 form-group">
                            <label class="control-label" for="inputSuccess1">请选择课程日期</label>

                            <div class="datepicker mb10" name="" id="attendance_date_start"></div>
                            <ul class="nav nav-pills nav-stacked bggray" id="coursegroupVueModal">
                                <li v-for="data in coursegroup[0]" class="" :_value="data.courseGroupId"><a
                                        href="javascript:void(0)">{{data.courseGroupName}}</a></li>
                            </ul>
                        </div>
                        <div class="col-md-10" id="attendanceVueModal">
                            <div class="panel panel-default" v-for="list in course_list[0]">
                                <div class="panel-heading">
                                    <div class="row">
                                        <h5 class="col-md-9">
                                            <i class="glyphicon glyphicon-ok mr10"
                                               v-bind:class="[list.state ? 'course_attendanced' : 'course_attendance']"></i>
                                            <span
                                                class="mr10">{{list.courseName}}</span><span class="mr10">{{courseDate[0].date}}</span><span>{{list.courseData}}</span>
                                        </h5>

                                        <div class="button-column col-md-3">
                                            <a v-show="!list.state"
                                               :href="'javascript:attendanceModal(\''+ list.scheduleId +'\')'"
                                               class="btn btn-primary pull-right">签到</a>
                                            <a v-show="list.state"
                                               :href="'<?php echo $this->createUrl('delAttendance') ?>&scheduleId='+list.scheduleId + '&serviceTime=' + courseDate[0].date"
                                               class="btn btn-danger pull-right J_ajax_del" data-msg="确认取消签到吗?">取消签到</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="panel-body">
                                    <ul class="nav nav-pills">
                                        <li v-for="l in list.teacher_list"><a
                                                v-bind:class="{ 'bg-success': l.status == 1 , 'teacher_notsignin': l.status == 2 ,'teacher_spare': l.status == 3 }"
                                                href="javascript:void(0)"><span class="not_attendance_teacher">{{l.teacherName}}</span>
                                                <span v-if="l.spare_teacherName">({{l.spare_teacherName}})</span></a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane" id="month_attendance">2</div>
                </div>

            </div>
        </div>
    </div>
    <div class="modal fade" id="teacherlistVueModel" tabindex="-1" data-backdrop="static">
        <div class="modal-dialog" role="document">
            <form action="<?php echo $this->createUrl('addAttendance') ?>" method="post" class="J_ajaxForm">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="myModalLabel">录入考勤</h4>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="form-group form-inline">
                                <input type="hidden" id="courseId" name="scheduleId"/>
                                <input type="hidden" id="" name="serviceTime" :value="courseDate[0].date"/>

                                <div class="col-md-12 mb10" v-for="(teacher,index) in teacher_lists[0]">
                                    <h5 class="col-md-2">{{teacher.teacherName}}</h5>

                                    <div class="col-md-10">
                                        <label class="radio-inline">
                                            <input type="radio" @change="notspareclick(index)"
                                                   :name="'status['+ teacher.teacherId +']'"
                                                   checked id="" value="1">出勤
                                        </label>
                                        <label class="radio-inline">
                                            <input type="radio" @change="notspareclick(index)"
                                                   :name="'status['+ teacher.teacherId +']'" id=""
                                                   value="2">缺勤无替补
                                        </label>
                                        <label class="radio-inline">
                                            <input type="radio" :name="'status['+ teacher.teacherId +']'" id=""
                                                   value="3" @change="spareclick(index)">缺勤有替补
                                        </label>
                                        <select class="form-control ml10 spare_select"
                                                :name="'substituterId['+ teacher.teacherId +']'"
                                                v-if="spareflag[index].flag !== false">
                                            <optgroup :label="spare_teacher.title"
                                                      v-for="spare_teacher in spare_teacher_list[0]">
                                                <option v-for="teacherdata in spare_teacher.data"
                                                        :value="teacherdata.teacherId">{{teacherdata.teacherName}}
                                                </option>
                                            </optgroup>
                                        </select>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary J_ajax_submit_btn">保存</button>
                        <input type="reset" class="_reset" style="display: none"/>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <script>
        var returndata;
        $('.datepicker').datepicker({
            dateFormat: 'yy-mm-dd',
            onSelect: function (dateText, inst) {    //选中事件 第一个参数是选中时间 第二个参数是选中的对象
                $.ajax({
                    url: '<?php echo $this->createUrl('attendance') ?>',
                    data: {weekday: dateText},
                    type: 'get',
                    timeout: 5000,
                    dataType: 'json',
                    global: false
                }).done(function (data, status, xhr) {

                    $('#coursegroupVueModal li').removeClass('active');
                    coursegroup.splice(0, coursegroup.length);
                    course_list.splice(0, course_list.length);
                    returndata = data;
                    coursegroup.push(returndata.course_group);
                    courseDate.splice(0, courseDate.length, {date: dateText});
                }).fail(function (xml, status, text) {
                    coursegroup.splice(0, coursegroup.length);
                    course_list.splice(0, course_list.length);
                });
            }
        });

        var courseDate = [{date: ''}];    //课程日期
        var coursegroup = [];
        var coursegroupVue = new Vue({      //课程组名字模型
            el: "#coursegroupVueModal",
            data: {
                coursegroup
            },
            created: function () {
            },
            updated: function () {
            },
            methods: {}
        });
        $(document).on('click', '#coursegroupVueModal li', function () {
            var courseGroupId = $(this).attr('_value');
            course_list.splice(0, course_list.length, returndata.course_list[courseGroupId]);
            $(this).siblings().removeClass('active');
            $(this).addClass('active');
        });
        var course_list = [];
        var attendanceVue = new Vue({   //课程组模型
            el: "#attendanceVueModal",
            data: {
                courseDate,
                course_list
            },
            updated: function () {
                head.Util.ajaxDel();
            },
            methods: {}
        });
        var teacher_lists = [];
        var spare_teacher_list = [];//替补老师数组
        var spareflag = [];//替补老师显示判断

        var teacherlistVue = new Vue({  //老师列表模型
            el: "#teacherlistVueModel",
            data: {
                teacher_lists,
                spare_teacher_list,
                spareflag,
                courseDate
            },
            updated: function () {
            },
            methods: {
                spareclick: function (data) {
                    this.spareflag[data].flag = true;
                },
                notspareclick: function (data) {
                    this.spareflag[data].flag = false;
                }
            }
        });

        function attendanceModal(id) {      //呼出模态框
            spareflag.splice(0, spareflag.length);
            $('._reset').click();           //重置表单
            $('#courseId').val(id);
            $('#teacherlistVueModel').modal();
            spare_teacher_list.splice(0, spare_teacher_list.length, returndata.spare_teacher_list);      //加载替补老师
            $.each(course_list, function (i, list) {
                $.each(list, function (i, l) {
                    if (l.scheduleId == id) {
                        teacher_lists.splice(0, teacher_lists.length, l.teacher_list);   //加载老师列表
                        $.each(l.teacher_list, function () {
                            spareflag.push({flag: false})
                        })
                    }
                });
            });

        }

        $('.spare_radio').select(function () {

        });
        function attendanced(data) {
            $('#teacherlistVueModel').modal('hide');
            var coursekey;
            var tcourse;
            $.each(data, function (i, thiscourse) { // i:组合id
                coursekey = i;
                tcourse = thiscourse;
            });
            attendanceVue.$set(course_list[0], coursekey, tcourse)
        }
        function cancelAttendance(id) {
            $.each(course_list, function (i, lists) {
                $.each(lists, function (i, list) {
                    if (list.scheduleId == id) {
                        list.state = 0;
                        $.each(list.teacher_list, function (i, l) {
                            l.status = 0;
                            l.spare_teacherId = '';
                            l.spare_teacherName = '';
                        })
                    }
                })
            })
        }
    </script>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>