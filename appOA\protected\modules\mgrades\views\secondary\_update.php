<?php $form=$this->beginWidget('CActiveForm', array(
    'id'=>'visits-form',
    'enableAjaxValidation'=>false,
    'action' => $this->createUrl('addreport',array("id"=>$model->id)),
    'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
));

?>
<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
    <h4 class="modal-title"><?php echo Yii::t('site','Reports Templates')?></h4>
</div>
<div class="modal-body">

    <!-- 中文名称 -->
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'report_title_cn'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'report_title_cn',array('maxlength'=>255,'class'=>'form-control datepicker')); ?>
        </div>
    </div>
    <!-- 英文名称 -->
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'report_title_en'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'report_title_en',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>

    <!-- 班级 -->
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo Yii::t('asa','班级'); ?></label>
        <div class="col-xs-9">
            <?php echo CHtml::checkBoxList('class', $classType, $classTypes, array('maxlength'=>255, 'template'=>'{input} {label}')); ?>
        </div>
    </div>

    <!-- 状态 -->
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'stat'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->checkbox($model,'stat', array('1'=>"开启"),array('template'=>'<label class="radio-inline">{input}{label}</label>','separator'=>'')); ?>
        </div>
    </div>
</div>
<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
    <button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
</div>

<?php $this->endWidget(); ?>
<script>
    // 回调：添加成功
    function cbVisit() {
        $('#modal').modal('hide');
        $.fn.yiiGridView.update('reports_secondary');
        location.reload()
    }
</script>
