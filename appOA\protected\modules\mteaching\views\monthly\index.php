<script>
    var openSelectTaskModal;
    var classid = <?php echo $this->selectedClassId?>;
    var month = '<?php echo $this->selectedMonth?>';
    var task = '<?php echo $this->selectedTask?>';
</script>

<?php
$weeklyArrcn = array(
    "learntargets" => "http://oa.ivyonline.cn/modules/docscenter/doc.php?action=view_pdf&fn=docs_cn_2021_09_613ee100d04c6.pdf",
);
$weeklyArren = array(
    "learntargets" => "http://oa.ivyonline.cn/modules/docscenter/doc.php?action=view_pdf&fn=docs_en_2021_09_613ee100d116f.pdf",
)
?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><button type="button" id="showSelectTaskModal" class="btn btn-info"><?php echo Yii::t('teaching','Unit Plan');?> <span class="glyphicon glyphicon-chevron-right"></span></button></li>
        <?php if($this->selectedClassId):?>
        <li><?php echo $this->branchObj->title;?></li>
        <li><?php echo $this->schoolClasses['items'][$this->selectedClassId];?></li>
        <?php endif;?>
        <?php if($this->selectedMonth):?>
            <li><?php echo $this->months[$this->selectedMonth];?></li>
        <?php endif;?>
    </ol>

    <?php if($this->selectedClassId):?>
    <ul class="nav nav-pills navbar">
        <?php
        foreach($this->monthlyTasks['mi'] as $_k => $_t){
            echo CHtml::openTag('li', array('class'=>$_k==$this->selectedTask?'active':''));
            echo CHtml::link($_t, array('index', 'classid'=>$this->selectedClassId, 'month'=>$this->selectedMonth, 'task'=>$_k));
            echo CHtml::closeTag('li');
        }
        ?>

        <?php if(isset($_GET["task"]) ){?>
            <li class="pull-right">
                <a target="_blank" href="<?php echo Yii::app()->language == 'zh_cn' ? $weeklyArrcn[$_GET["task"]]:$weeklyArren[$_GET["task"]] ; ?>">
                <span class="glyphicon glyphicon-question-sign" style="font-size: 16px;padding-right:6px;top:3px"></span><?php echo Yii::t('teaching','Instruction Guidance');?>
                </a>
            </li>
        <?php }?>
    </ul>
    <?php endif;?>

    <?php if($this->selectedClassId && $this->selectedMonth && $this->selectedTask):?>

        <div class="row">
            <div class="col-md-12">
                <?php if($taskData['stopping']): ?>
                    <div class="alert alert-warning" role="alert">
                        <span class="glyphicon glyphicon-info-sign"></span>
                        <?php echo Yii::t('teaching','Sorry, this function is not available for this campus/class.');?>
                    </div>
                <?php else: ?>
                    <?php $this->renderPartial('_'.$this->selectedTask, array('taskData'=>$taskData));?>
                <?php endif; ?>
            </div>
        </div>
    <?php endif;?>
</div>


<!-- Modal -->
<div class="modal" id="selectTaskModal" tabindex="-1" role="dialog" aria-labelledby="selectTaskModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel"><?php echo Yii::t('teaching','Select teaching task');?> <small><?php echo $this->branchObj->title;?></small></h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="well">
                            <ul class="nav nav-pills" id="cless-selector">
                                <?php
                                foreach($this->schoolClasses['items'] as $_classid=>$title){
                                    if($this->myClasses){
                                        if(in_array($_classid, $this->myClasses)){
                                            echo CHtml::openTag('li');
                                            echo CHtml::link($title, '#', array('data-classid'=>$_classid));
                                            echo CHtml::closeTag('li');
                                        }
                                    }
                                    else{
                                        echo CHtml::openTag('li');
                                        echo CHtml::link($title, '#', array('data-classid'=>$_classid));
                                        echo CHtml::closeTag('li');
                                    }
                                }
                                ?>
                            </ul>
                        </div>

                        <div class="well">
                            <ul class="nav nav-pills" id="month-selector">
                                <?php
                                foreach($this->months  as $key => $month){
                                    echo CHtml::openTag('li');
                                    echo CHtml::link($month, '#', array('data-month'=>$key));
                                    echo CHtml::closeTag('li');
                                }
                                ?>
                            </ul>
                        </div>

                        <div class="well">
                            <ul class="nav nav-pills" id="task-selector">
                                <?php
                                foreach($this->monthlyTasks['mi'] as $_k => $_t):
                                ?>
                                    <li><a href="#" data-task="<?php echo $_k;?>"><?php echo $_t; ?></a></li>
                                <?php
                                endforeach;
                                ?>
                            </ul>
                        </div>

                        <?php echo CHtml::form($this->createUrl('index'), 'get', array('class'=>'form-horizontal', 'onsubmit'=>'return checkSubmit(this)'));?>
                            <div id="form-data">
                                <?php echo CHtml::hiddenField('classid');?>
                                <?php echo CHtml::hiddenField('month');?>
                                <?php echo CHtml::hiddenField('task');?>
                            </div>

                            <div class="pop_bottom">
                                <button onclick="$('#selectTaskModal').modal('hide')" type="button" class="btn btn-default pull-right"><?php echo Yii::t('global','Cancel');?></button>
                                <button type="submit" class="btn btn-primary pull-right mr10"><?php echo Yii::t('global','OK');?></button>
                            </div>
                        <?php echo CHtml::endForm();?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    $(function(){

        openSelectTaskModal = function(){
            $('#selectTaskModal').modal();//{backdrop:'static', keyboard:false}
        };

        $('#showSelectTaskModal').click(function(){
            openSelectTaskModal();
        });

        if(!classid || !month || !task)
            openSelectTaskModal();

        if(classid){
            var obj = $('#cless-selector li a[data-classid="'+classid+'"]');
            if( obj.length ){
                obj.parent().addClass('active');
                $('#form-data input#classid').val( classid );
            }
        }
        if(month){
            var obj = $('#month-selector li a[data-month="'+month+'"]');
            if( obj.length ){
                obj.parent().addClass('active');
                $('#form-data input#month').val( month );
            }
        }
        if(task){
            var obj = $('#task-selector li a[data-task="'+task+'"]');
            if( obj.length ){
                obj.parent().addClass('active');
                $('#form-data input#task').val( task );
            }
        }

        $('#cless-selector').on('click', 'a', function(e){
            e.preventDefault();
            $('#cless-selector li').removeClass('active');
            $(this).parent().addClass('active');
            $('#form-data input#classid').val( $(this).data('classid') );
        });

        $('#month-selector').on('click', 'a', function(e){
            e.preventDefault();
            $('#month-selector li').removeClass('active');
            $(this).parent().addClass('active');
            $('#form-data input#month').val( $(this).data('month') );
        });

        $('#task-selector').on('click', 'a', function(e){
            e.preventDefault();
            $('#task-selector li').removeClass('active');
            $(this).parent().addClass('active');
            $('#form-data input#task').val( $(this).data('task') );
        });

    });

    function checkSubmit(_this) {
        $('#J_fail_info').remove();
        var button = $(_this).find('button[type="submit"]');
        var flag = true, msg=[];

        if( $(_this).find('input#classid').val() == '' ){
            flag = false;
            msg.push('<?php echo Yii::t("teaching","class");?>');
        }
        if( $(_this).find('input#month').val() == '' ){
            flag = false;
            msg.push('<?php echo Yii::t("teaching","month");?>');
        }
        if( $(_this).find('input#task').val() == '' ){
            flag = false;
            msg.push('<?php echo Yii::t("teaching","task");?>');
        }
        if(flag){
            return true;
        }
        else{
            $( '<span id="J_fail_info" class="text-warning"><i class="glyphicon glyphicon-remove text-warning"></i> <?php echo Yii::t("global","Please Select");?> ' + msg.join('、') + '！</span>' ).appendTo(button.parent()).fadeIn( 'fast' );
            return false;
        }
    }
</script>

<?php
$this->branchSelectParams['extraUrlArray'] = array('//mteaching/monthly/index');
$this->renderPartial('//layouts/common/branchSelectBottom');
?>