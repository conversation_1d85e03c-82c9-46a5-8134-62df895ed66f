<div>
    <div class='flex align-items  mb24 mt10'>
        <span class='font14 color3'>处理人：</span>
        <div class='flex1 flex align-items lib_class_handle'>
        </div>
    </div>
    <div >    
        <label class="checkbox-inline">
            <input type="checkbox" name="closed" value="1" disabled> <?php echo Yii::t('global','Close');?>
        </label>
    </div>
</div>

<script>
    lib()
    $(function () {
        $('[data-toggle="tooltip"]').tooltip()
    })
    function lib(){
        let forms=childDetails.forms.find(item2 =>item2.key === 'academic')
        for(var key in forms.owner_info){
            if (key !== '2' && key !== '5') {
                if(forms.implementer==key && forms.data._id){
                    $('.lib_class_handle').append('<div class="flex align-items mr16 relative"><img src='+forms.owner_info[key].photoUrl+' alt="" class="avatar38"/><div class="ml8"><div class="font14 color3 ">'+forms.owner_info[key].name+'</div><div class="font12 color6">处理时间：'+forms.implementAt+'</div></div></div>')
                }else if(!forms.data._id){
                    $('.lib_class_handle').append('<div class="flex align-items mr16"><img src='+forms.owner_info[key].photoUrl+' alt="" class="img28"/><div class="font14 color3 ml8">'+forms.owner_info[key].name+'</div></div>')
                }
            }
        }
        $('#type').val(forms.key)
        if(forms.data.closed && forms.data.closed=='1'){
            $('input[type="checkbox"][name="closed"][value='+forms.data.closed+']').prop('checked', true);
        }
    }
</script>
