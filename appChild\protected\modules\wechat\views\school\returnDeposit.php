<style>
.mt0{
    margin-top:0
}
#viewAgreement{
    display:none
}
</style>
<div class="msg">
<div class="weui_msg">
    <div class="weui_icon_area"><i class="weui_icon_success weui_icon_msg"></i></div>
    <div class="weui_text_area">
        <h2 class="weui_msg_title"><?php echo Yii::t("reg", "The Re-enrollment Confirmation has been completed"); ?></h2>
        <p class="weui_msg_desc"><?php echo $message; ?></p>
    </div>
    <div class="weui_opr_area">
        <p class="weui_btn_area">
            <a href="javascript:;" class="weui_btn weui_btn_primary" onclick="pay()"><?php echo Yii::t("reg", "To Pay"); ?></a>
            <a href="javascript:;" class="weui_btn weui_btn_default" id='viewAgreement' onclick='previewMoreImage()'><?php echo Yii::t("reg", "Tuition Policy and Agreement"); ?></a>
            <a href="javascript:;" class="weui_btn weui_btn_default" onclick="closes()"><?php echo Yii::t("survey", "Close"); ?></a>
            
        </p>
    </div>
</div>
</div>

<script>
function closes(){
    WeixinJSBridge.call('closeWindow');
}
function pay(){
    location.href = '<?php echo $payUrl; ?>';
}
var img
$.ajax({
    url: "<?php echo $this->createUrl('protocol') ?>",
    type: 'post',
    dataType: 'json',
    data: {
        lastqid:'<?php echo $lastqid; ?>',
    },
    success: function(data) {
        console.log(data)
        if (data.state == 'success') {
            img=data.data
            if(img!=''){
                document.getElementById("viewAgreement").style.display="block";
            }
        }
    }
})
function previewMoreImage() {
    let urlarr = [];
    urlarr.push(img)
    wx.previewImage({
    current: img,
    urls: urlarr
    })    
}
</script>