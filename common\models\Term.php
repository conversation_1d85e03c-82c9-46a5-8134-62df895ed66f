<?php

/**
 * This is the model class for table "ivy_diglossia".
 *
 * The followings are the available columns in table 'ivy_diglossia':
 * @property integer $diglossia_id
 * @property integer $category_id
 * @property string $entitle
 * @property string $cntitle
 * @property integer $weight
 */
class Term extends CActiveRecord
{

    public $cacheIds = array('langList', 'ageList', 'miList', 'ldList', 'culturalList');
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return MimsTerm the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_diglossia';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('category_id, weight', 'numerical', 'integerOnly'=>true),
			array('entitle, cntitle', 'length', 'max'=>150),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('diglossia_id, category_id, entitle, cntitle, weight', 'safe', 'on'=>'search'),
		);
	}
	
	public function scopes(){
		return array(
			'city'=>array(
				'condition'=>'category_id=7',
				'order'=>'entitle ASC',
			),
			'lang'=>array(
				'condition'=>'category_id=1',
				'order'=>'entitle ASC',
			),
            'age'=>array(
                'condition'=>'category_id=3',
                'order'=>'weight ASC',
            ),
            'mi'=>array(
                'condition'=>'category_id=4',
                'order'=>'weight ASC',
            ),
            'ld'=>array(
                'condition'=>'category_id=5',
                'order'=>'weight ASC',
            ),
            'cultural'=>array(
                'condition'=>'category_id=9',
                'order'=>'weight ASC',
            ),
            'bankinfo'=>array(
                'condition'=>'category_id=13',
                'order'=>'weight ASC',
            ),
            'lunch'=>array(
                'condition'=>'category_id=6',
                'order'=>'weight ASC',
            ),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'campuses'=>array(self::HAS_MANY, 'Branch', 'city', 'alias'=>'c', 'select'=>'c.branchid,c.group,c.abb,c.title,info.*', 'order'=>'c.abb ASC', 'together'=>false, 'condition'=>'c.type=20'),
			'campusesWithGmap'=>array(self::HAS_MANY, 'MimsBranch', 'city', 'alias'=>'c', 'order'=>'c.abb ASC', 'together'=>false, 'condition'=>'c.type=20', 'with'=>array("info","gmapinfo")),
			'campuslist'=>array(self::HAS_MANY, 'Branch', 'city', 'alias'=>'c', 'order'=>'c.abb ASC', 'together'=>false, 'condition'=>'c.type=20 and c.status=10', 'with'=>"info"),
			'gmapinfo'=>array(self::HAS_ONE, 'MimsGmapCity', 'id'),
            'cat'=>array(self::BELONGS_TO, 'DiglossiaCategory', 'category_id'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'diglossia_id' => 'Diglossia',
			'category_id' => 'Category',
			'entitle' => 'Entitle',
			'cntitle' => 'Cntitle',
			'weight' => 'Weight',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('diglossia_id',$this->diglossia_id);
		$criteria->compare('category_id',$this->category_id);
		$criteria->compare('entitle',$this->entitle,true);
		$criteria->compare('cntitle',$this->cntitle,true);
		$criteria->compare('weight',$this->weight);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
	
	public function getLangList(){
		$cacheIds = CommonUtils::LoadConfig('CfgCacheId');
		$cacheId = $cacheIds['langList']['id'];
		$langList = Yii::app()->cache->get($cacheId);
		if(false===$langList){
			$langs = $this->model()->lang()->findAll();
			foreach($langs as $lang){
				$langList[$lang->diglossia_id] = $lang->entitle . ' ' . $lang->cntitle;
			}
			Yii::app()->cache->set($cacheId, $langList);
		}
		return $langList;
	}

	public function getAgeList(){
		$cacheIds = CommonUtils::LoadConfig('CfgCacheId');
		$cacheId = $cacheIds['ageList']['id'];
		$List = Yii::app()->cache->get($cacheId);
		if(false===$List){
			$items = $this->model()->age()->findAll();
			foreach($items as $item){
				$List[$item->diglossia_id] = $item->entitle . ' ' . $item->cntitle;
			}
			Yii::app()->cache->set($cacheId, $List);
		}
		return $List;
	}

	public function getMiList(){
		$cacheIds = CommonUtils::LoadConfig('CfgCacheId');
		$cacheId = $cacheIds['miList']['id'];
		$List = Yii::app()->cache->get($cacheId);
		if(false===$List){
			$items = $this->model()->mi()->findAll();
			foreach($items as $item){
				$List[$item->diglossia_id] = $item->entitle . ' ' . $item->cntitle;
			}
			Yii::app()->cache->set($cacheId, $List);
		}
		return $List;
	}

	public function getLdList($useLang=false){
		$cacheIds = CommonUtils::LoadConfig('CfgCacheId');
		$lang = Yii::app()->language;
		$cacheId = $useLang ? $cacheIds['ldList']['id'] . $lang : $cacheIds['ldList']['id'];
		$List = Yii::app()->cache->get($cacheId);
		if(false===$List){
			$items = $this->model()->ld()->findAll();
			foreach($items as $item){
				if($useLang){
					$List[$item->diglossia_id] = ($lang == 'en_us')? $item->entitle:$item->cntitle;
				}else{
					$List[$item->diglossia_id] = $item->entitle . ' ' . $item->cntitle;
				}
			}
			Yii::app()->cache->set($cacheId, $List);
		}
		return $List;
	}

	public function getCulturalList(){
		$cacheIds = CommonUtils::LoadConfig('CfgCacheId');
		$cacheId = $cacheIds['culturalList']['id'];
		$List = Yii::app()->cache->get($cacheId);
		if(false===$List){
			$items = $this->model()->cultural()->findAll();
			foreach($items as $item){
				$List[$item->diglossia_id] = $item->entitle . ' ' . $item->cntitle;
			}
			Yii::app()->cache->set($cacheId, $List);
		}
		return $List;
	}

    /**
     * @param string $id
     * 清除cache
     */
    public function clearCaches($id='all'){
        if($id=='all'){
            foreach( $this->cacheIds as $_cacheId){
                Yii::app()->cache->delete($_cacheId);
            }
        }else{
            Yii::app()->cache->delete($id);
        }
    }

	public function getContent(){
		return (Yii::app()->language == 'en_us') ? ucfirst(strtolower($this->entitle)) : $this->cntitle;
	}
}