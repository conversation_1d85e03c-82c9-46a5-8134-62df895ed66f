<?php

class AppsController extends BranchBasedController
{
    public $showWorkflow = false;
    public function init(){
        parent::init();
        $this->defaultToUserBranch = true;
    }
    public function actionIndex()
    {
        Yii::app()->theme = 'blue';
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile($cs->getCoreScriptUrl().'/jui/js/jquery-ui-i18n.min.js');
        $cs->registerCssFile(Yii::app()->theme->baseUrl.'/css/calendar.css');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl.'/modern/css/wizard/bootstrap-nav-wizard.min.css');

        Yii::import('common.models.hr.*');
        Yii::import('common.models.classTeacher.InfopubStaffExtend');
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'main';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        //每日公告
        Yii::import('common.models.content.Dailymail');

        $today = strtotime('today');
        $criteria = new CDbCriteria();
        if($this->staff->admtype == 0 && !in_array($this->staff->profile->branch, array('BJ_TYG', 'BJ_SS', 'BJ_BU'))){
            $criteria->compare('schoolid', $this->branchId);
        }
        $criteria->compare('stat', 10);
        $criteria->compare('startdate', '<='.$today);
        $criteria->compare('duedate', '>='.$today);
        $criteria->order = 'priority desc, updated_timestamp DESC';
        $items = Dailymail::model()->findAll($criteria);
        $cates = Dailymail::getCate('all');
        $baseUrl = Yii::app()->params['OABaseUrl'] . "/modules/myspace/task/getthumb2.php?img=";
        $baseThumbUrl = Yii::app()->params['OABaseUrl'] . "/modules/myspace/task/getthumb2.php?type=thumb&img=";
        $dailyBulletins = array();
        $i = 0;
        foreach($items as $item){
            $item->updated_timestamp = date('Y-m-d H:i:s', $item->updated_timestamp);
            $dailyBulletins[$item->schoolid][$item->cate]['items'][$i] = $item->attributes;
            $dailyBulletins[$item->schoolid][$item->cate]['items'][$i]['files'] = '';
            foreach ($item->files as $file) {
                $fileInfo = pathinfo($file->file_name);
                $fileType = $fileInfo['extension'];
                if (in_array($fileType, array('jpg', 'png', 'jpeg'))) {
                    $dailyBulletins[$item->schoolid][$item->cate]['items'][$i]['files'] .= '<p><a href="'.$baseUrl . $file->file_name.'" target="_blank"><img class="img-responsive" src="'.$baseThumbUrl . $file->file_name.'" alt="'. $file->notes.'.'.$fileType.'"></a></p>';
                } else {
                    $dailyBulletins[$item->schoolid][$item->cate]['items'][$i]['files'] .= '<p><a class="btn btn-default btn-sm" href="'.$baseUrl . $file->file_name.'" target="_blank"><span class="glyphicon glyphicon-file" aria-hidden="true"></span> '.$file->notes.'.'.$fileType.'</a></p>';
                }
            }
            if (isset($cates[$item->schoolid][$item->cate])) {
                $dailyBulletins[$item->schoolid][$item->cate]['name'] = $cates[$item->schoolid][$item->cate];
            } else{
                $dailyBulletins[$item->schoolid][$item->cate]['name'] = Yii::t('', '每日公告');
            }
            $i++;
        }

        # 首页员工介绍 暂时注释
        $staffs = array();
//        $ckey = 'randomStaff';
//        $staffs = Yii::app()->cache->get($ckey);
//        if(!$staffs){
//            $limit = 5;
//            $criteria = new CDbCriteria();
//            $criteria->compare('isstaff', 1);
//            $criteria->compare('level', 1);
//            $criteria->addCondition("bio <>''");
//            if(isset(Yii::app()->params['defaultCampusId'])){
//                $criteria->compare('profile.branch', Yii::app()->params['defaultCampusId']);
//                $count = User::model()->with('profile')->count($criteria);
//            }
//            else{
//                $count = User::model()->count($criteria);
//            }
//            $randnum = rand(1, $count-$limit);
//            $criteria->offset = $randnum;
//            $criteria->limit = $limit*10;
//            $criteria->index = 'uid';
//            $users = User::model()->with('profile')->findAll($criteria);
//            $_staffs = array_rand($users, $limit);
//            foreach($_staffs as $uid){
//                $staffs[$uid] = array(
//                    'name' => $users[$uid]->getName(),
//                    'bio' => $users[$uid]->bio,
//                    'photo' => $users[$uid]->user_avatar,
//                );
//            }
//            Yii::app()->cache->set($ckey, $staffs, 600);
//        }

        //所有需要审批的任务
        Yii::import('common.models.workflow.*');
        $criter = new CDbCriteria;
        $criter->compare('t.assign_user', Yii::app()->user->getId());
        $criter->compare('t.current_operator', WorkflowRole::WORKFLOW_ROLE_CURRENT_CHECK_USER);
        $waits = WorkflowRole::model()->count($criter);

        //未签到的孩子数
        $absent = array('count'=>0, 'style'=>'btn-primary');
        $CampusAdm = Yii::app()->user->checkAccess('t_A_CampusAdm');
        if($CampusAdm){
            Yii::import('common.models.child.ChildDailySign');
            $absent = ChildDailySign::getAbsent($this->branchId);
        }
        // 是否为课后课老师
        Yii::import('common.models.asainvoice.AsaVendor');
        $isAsa = AsaVendor::model()->findByAttributes(array('ivy_uid'=>$this->staff->uid));

        // 获取指定的云文件
        Yii::import('common.models.online.OnlineFile');
        $okrUrl = OnlineFile::getOkrUrl();
        // 查找 journal 未回复信息
        $unRepiedData = array(
            'items' => array(),
            'total' => 0
        );
        $requestUrl = "directMessage/unRepliedSchool";
        $res = CommonUtils::requestDsOnline($requestUrl, array('uid' => Yii::app()->user->getId()));
        if (isset($res['code']) && $res['code'] == 0 && $res['data']) {
            foreach ($res['data'] as $key => $value) {
                $unRepiedData['total'] += $value['num'];
                $value['url'] = $this->createUrl('/mteaching/directMessage/feedback', array('branchId' => $key));
                $unRepiedData['items'][] = $value;
            }
        }
        //等待处理的学生行为单
        $requestUrl = 'behavior/untreated';
        $behavior = CommonUtils::requestDsOnline($requestUrl, array('school_id' => $this->branchId));
        $untreatedBehavior = array(
            'total' => 0,
            'school_total'=>array()
        );
        if(isset($behavior['code']) && $behavior['code'] == 0 && $behavior['data']){
            $untreatedBehavior['total'] = $behavior['data']['total'];
            foreach ($behavior['data']['school_total'] as $school_id=>$item){
                $untreatedBehavior['school_total'][$school_id]['url'] = $this->createUrl('/mteaching/student/index', array('branchId'=>$school_id,'category'=>'allBehavior','needMe'=>1));
                $untreatedBehavior['school_total'][$school_id]['title'] = $behavior['data']['school_list'][$school_id]['title'];
                $untreatedBehavior['school_total'][$school_id]['total'] = $item;
            }
        }
        // 需要处理的请假数量
        $requestUrl = 'leave/flow/processCount';
        $res = CommonUtils::requestDsOnline($requestUrl, array('school_id' => $this->branchId));
        $leaveCount = 0;
        if (isset($res['code']) && $res['code'] == 0 && $res['data']) {
            $leaveCount = $res['data'];
        }

        $this->render('overall', array(
            'dailyBulletins'=>$dailyBulletins,
            'waits'=>$waits,
            'staffs'=>$staffs,
            'absent'=>$absent,
            'isAsa'=>$isAsa,
            'okrUrl'=>$okrUrl,
            'unRepiedData'=>$unRepiedData,
            'untreatedBehavior'=>$untreatedBehavior,
            'leaveCount'=>$leaveCount,
        ));
    }

    public function actionLogout()
    {
        Yii::app()->user->logout();
        $this->render("logout", array("url"=>Yii::app()->user->loginUrl));
        Yii::app()->end();
    }

    public function actionShortcutLink()
    {
        $sid = Yii::app()->session->sessionId;
        $res = CommonUtils::requestDsOnline('shortcuts/links?sid='.$sid, null, 'get');
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
        }
        else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'Fail');
        }
        $this->showMessage();
    }

    public function actionAddShortcut()
    {
        $id = Yii::app()->request->getParam('id', 0);
        $link = Yii::app()->request->getParam('input_link', '');
        $prefixLink = Yii::app()->request->getParam('prefix_url_hide', '');
        $name = Yii::app()->request->getParam('input_name', '');

        $res = CommonUtils::requestDsOnline('shortcuts/add', array(
            'id' => $id,
            'link' => $prefixLink.$link,
            'name' => $name,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            if ($id == 0) {
                $this->addMessage('callback', 'cbShortcut');
            }
            else {
                $this->addMessage('callback', 'cbShortcut2');
            }
            $this->addMessage('message', 'Success!');
            $this->addMessage('data', array(
                'id' => $res['data']['id'],
                'link' => base64_encode($prefixLink.$link).Yii::app()->session->sessionId,
                'name' => $name,
            ));
        }
        else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    public function actionRemoveShortcut()
    {
        $id = Yii::app()->request->getParam('id', '');

        $res = CommonUtils::requestDsOnline('shortcuts/remove', array(
            'id' => $id
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('callback', 'cbShortcut');
            $this->addMessage('message', 'Success!');
        }
        else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'Fail');
        }
        $this->showMessage();
    }
}