<?php

/**
 * This is the model class for table "ivy_asa_month_report".
 *
 * The followings are the available columns in table 'ivy_asa_month_report':
 * @property integer $id
 * @property string $schoolid
 * @property integer $report_month
 * @property integer $status
 * @property integer $create_time
 * @property integer $update_time
 * @property integer $uid
 */
class AsaMonthReport extends CActiveRecord
{
	
	const PREPAR = 10; // 已生成未提交
	const UNCHECK = 15; // 已提交未审核
	const SUBMIT = 20; // 已提交
	const CONFIRM = 30; // 已提交

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_asa_month_report';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('schoolid, report_month, status, total_amount, uid', 'required'),
			array('report_month, status, create_time, update_time, uid', 'numerical', 'integerOnly'=>true),
			array('total_amount', 'numerical'),
			array('schoolid', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, schoolid, report_month, status, total_amount, create_time, update_time, uid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'item' => array(self::HAS_MANY, 'AsaMonthReportItem', array('report_id'=>'id')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'schoolid' => 'Schoolid',
			'report_month' => 'Report Month',
			'total_amount' => 'total_amount',
			'status' => 'Status',
			'create_time' => 'Create Time',
			'update_time' => 'Update Time',
			'uid' => 'Uid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('report_month',$this->report_month);
		$criteria->compare('status',$this->status);
		$criteria->compare('total_amount',$this->total_amount);
		$criteria->compare('create_time',$this->create_time);
		$criteria->compare('update_time',$this->update_time);
		$criteria->compare('uid',$this->uid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AsaMonthReport the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	public function beforeSave() {

	    if ($this->isNewRecord) {
	        $this->create_time = time();
	        $this->update_time = time();
	    } else {
	        $this->update_time = time();
	    }

	    return parent::beforeSave();
	}

	/**
	 * [generalData 生成数据]
	 * @return [type]           [description]
	 */
	public function generalData()
	{
		$typeList = CommonUtils::LoadConfig('CfgASA');

		// 删除所有item
		AsaMonthReportItem::model()->deleteAllByAttributes(array('report_id'=>$this->id));
		$nMonth = date('Ym', strtotime($this->report_month .'01 '. '+1 month'));

		$criteria = new CDbCriteria();
		$criteria->with = array('course', 'vendorTeacher');
		$criteria->compare('course.site_id', $this->schoolid);
		$criteria->compare('course.unit_type', array(1,2,3,4));
		// 只处理内部个人
		$criteria->compare('vendorTeacher.type', 1);
		$criteria->compare('t.status', array(1,3,10));
		$criteria->compare('t.service_end', '>=' . $this->report_month . '01');
		$criteria->compare('t.service_end', '<' . $nMonth . '01');

		// 计算补签到的数据
		$repatch = AsaMonthReportRepatch::model()->findAllByAttributes(array('report_id'=>$this->schoolid.$this->report_month), array('index'=>'attendance_id'));
		$repatchId = array_keys($repatch);
		if (count($repatchId)>0) {
			$criteria->addInCondition('t.id', $repatchId, 'OR');
		}

		$criteria->index = "id";
		$attendances = AsaStaffAttendance::model()->findAll($criteria);

		$data = array();
		$vendorid = array();
		$courseid = array();
        $reportRepatchArr = array();
		foreach ($attendances as $attendanceId => $attendance) {
            $timeData = "";
		    if(isset($attendance->repatch)){
                $timeData = substr($attendance->repatch->report_id,-6);
            }
		    if(empty($timeData) || $timeData == $this->report_month){
                if (!in_array($attendance->status, array(1,3,10))) {
                    continue;
                }
                $courseid[] = $attendance->course_id;
                $attendCourseId = $attendance->course_id;
                // 额外判断延时看护
                if ($attendance->status == 10) {
                    $attendCourseId = 'a';
                }
                // 判断是否有替补
                if ($attendance->substituter) {
                    $vendorid[] = $attendance->substituter;
                    $data[$attendance->substituter][$attendCourseId]['substituter'][$attendance->course_staff_id][] = $attendance->program_id;
                    // $data[$attendance->substituter][$attendCourseId]['course_staff_id'] = $attendance->course_staff_id;
                } else {
                    $vendorid[] = $attendance->vendor_id;
                    $data[$attendance->vendor_id][$attendCourseId]['program_id'][] = $attendance->program_id;
                    $data[$attendance->vendor_id][$attendCourseId]['course_staff_id'] = $attendance->course_staff_id;
                }
                $attendance->report_id = $this->id;
                $attendance->save();
            }else{
                $reportRepatchArr[$timeData][$attendance->vendor_id] = array(
                    'title' => (isset($attendance->courseTitle)) ? $attendance->courseTitle->getTitle() : $attendance->groupTitle ->getName() ,
                    'name' => $attendance->vendorTeacher->getName(),
                );
            }
		}

		$vendors = AsaVendor::model()->findAllByPk($vendorid, array('index'=>'vendor_id'));
		$courses = AsaCourse::model()->findAllByPk($courseid, array('index'=>'id'));
		$amount = 0;
		$unsettle = 0;
		$variation = 0;
		foreach ($vendors as $vendor) {
			$variationId = $this->id;
			$variationModel = AsaMonthReportVariation::model()->findByAttributes(array('vendor_id'=>$vendor->vendor_id, 'report_id'=>$variationId));
			foreach ($data[$vendor->vendor_id] as $k=>$v) {
				// 处理替补
				if (isset($v['substituter'])) {
					foreach ($v['substituter'] as $subid => $group) {
						$courseStaff = AsaCourseStaff::model()->findByPk($subid);
						$course_count = count($group);
						if (!$courseStaff) {
							continue;
						}
						$item = new AsaMonthReportItem();
						$item->report_id = $this->id;
						$item->vendor_id = $vendor->vendor_id;
						$item->vendor_name = json_encode(array($vendor->name_cn, $vendor->name_en));
						$item->position = $courseStaff->job_type;
						$item->group_id = $group[0];
						$item->course_id = isset($courses[$k])?$courses[$k]->id:0;
						$item->identity = $vendor->vendorInfo->identify?$vendor->vendorInfo->identify:' ';
						$item->unit_price = $courseStaff->unit_salary;
						$item->course_count = $course_count;
						$item->bank_title = $vendor->vendorInfo->bank_name?$vendor->vendorInfo->bank_name:' ';
						$item->bank_account = $vendor->vendorInfo->bank_account?$vendor->vendorInfo->bank_account:' ';
						$item->fee_merge = $vendor->fee_merge;
						if($item->save()) {
							if ($variationModel && $variationModel->settlement != 1) {
								$unsettle += $course_count * $courseStaff->unit_salary;
							} else {
								$amount += $course_count * $courseStaff->unit_salary;
							}
						}
					}
				}
				if (isset($v['program_id'])) {
					// 正常情况
					$courseStaff = AsaCourseStaff::model()->findByPk($v['course_staff_id']);
					$course_count = count($v['program_id']);
					if (!$courseStaff) {
						continue;
					}
					$item = new AsaMonthReportItem();
					$item->report_id = $this->id;
					$item->vendor_id = $vendor->vendor_id;
					$item->vendor_name = json_encode(array($vendor->name_cn, $vendor->name_en));
					$item->position = $courseStaff->job_type;
					$item->group_id = $v['program_id'][0];
					$item->course_id = isset($courses[$k])?$courses[$k]->id:0;
					$item->identity = $vendor->vendorInfo->identify?$vendor->vendorInfo->identify:' ';
					$item->unit_price = $courseStaff->unit_salary;
					$item->course_count = $course_count;
					$item->bank_title = $vendor->vendorInfo->bank_name?$vendor->vendorInfo->bank_name:' ';
					$item->bank_account = $vendor->vendorInfo->bank_account?$vendor->vendorInfo->bank_account:' ';
					$item->fee_merge = $vendor->fee_merge;
					if($item->save()) {
						if ($variationModel && $variationModel->settlement != 1) {
							$unsettle += $course_count * $courseStaff->unit_salary;
						} else {
							$amount += $course_count * $courseStaff->unit_salary;
						}
					}
				}
			}
			// 计算未结算的浮动金额
			if ($variationModel && $variationModel->settlement != 1) {
				$unsettle += $variationModel->variation_amout;
			}
			// 计算浮动金额
			if ($variationModel && $variationModel->settlement == 1) {
				$variation += $variationModel->variation_amout;
			}
		}

		$this->total_amount = $amount + $variation;
		$this->unsettle_amount = $unsettle;
		$this->save();

		return $reportRepatchArr;
 	}

 	public function generaFreelancerData()
 	{
 		$schoolid = $this->schoolid;
 		$month = $this->report_month;
 		$nMonth = date('Ym', strtotime($month .'01 '. '+1 month'));
 		// 删除所有item
 		AsaMonthReportItem::model()->deleteAllByAttributes(array('report_id'=>$this->id));
 		// 查找签到的数据
 		$criteria = new CDbCriteria();
 		$criteria->with = array('course', 'vendorTeacher', 'vendor');
 		$criteria->compare('course.site_id', $schoolid);
 		// $criteria->compare('course.unit_type', array(1,2,3,4));
 		$criteria->compare('t.status', array(1,3,10));
 		$criteria->compare('t.service_end', '>=' . $month . '01');
 		$criteria->compare('t.service_end', '<' . $nMonth . '01');
 		$attendances = AsaStaffAttendance::model()->findAll($criteria);

 		// 查找结算设置数据
 		$criteria = new CDbCriteria();
 		$criteria->compare('report_id', $this->id);
 		$criteria->index = 'vendor_id';
 		$variations = AsaMonthReportVariation::model()->findAll($criteria);

 		$settleData = array();
 		$variationAmout = array();
 		$settleIds = array_keys($variations);
 		foreach ($attendances as $attendance) {
 			// 实际上课教师
 			$actualVendorId = $attendance->vendor_id;
 			$actualVendor = $attendance->vendorTeacher;
 			if ($attendance->substituter) {
	 			$actualVendorId = $attendance->substituter;
	 			$actualVendor = $attendance->vendor;
 			}
 			// 判断是否结算
 			if (in_array($actualVendorId, $settleIds)) {
 				if ($attendance->course->unit_type == 99) {
 					continue;
 				}
 				$variationAmout[$actualVendorId] = $variations[$actualVendorId];
				$key = $attendance->program_id .'_'. $attendance->course_id;
 				if (!isset($settleData[$actualVendorId][$key])) {
		 			$settleData[$actualVendorId][$key]['course_id'] = $attendance->course_id;
		 			$settleData[$actualVendorId][$key]['group_id'] = $attendance->program_id;

		 			// 分配课程信息
		 			$settleData[$actualVendorId][$key]['position'] = $attendance->course->job_type;
		 			$settleData[$actualVendorId][$key]['unit_price'] = $attendance->course->unit_salary;
		 			$settleData[$actualVendorId][$key]['course_count'] = 1;

		 			// 实际上课教师信息
		 			$settleData[$actualVendorId][$key]['vendor_id'] = $actualVendorId;
		 			$settleData[$actualVendorId][$key]['fee_merge'] = $actualVendor->fee_merge;
		 			$settleData[$actualVendorId][$key]['vendor_name'] = json_encode(array($actualVendor->name_cn, $actualVendor->name_en));
		 			$settleData[$actualVendorId][$key]['identify'] = $actualVendor->vendorInfo?$actualVendor->vendorInfo->identify:' ';
		 			$settleData[$actualVendorId][$key]['bank_name'] = $actualVendor->vendorInfo?$actualVendor->vendorInfo->bank_name:' ';
		 			$settleData[$actualVendorId][$key]['bank_account'] = $actualVendor->vendorInfo?$actualVendor->vendorInfo->bank_account:' ';
 				} else {
		 			$settleData[$actualVendorId][$key]['course_count'] += 1;
 				}
 			}
			$attendance->report_id = $this->id;
			$attendance->save();
 		}
 		$amount = 0;
 		foreach ($settleData as $data) {
 			foreach ($data as $course_id => $v) {
	 			$item = new AsaMonthReportItem();
	 			$item->report_id = $this->id;
	 			$item->vendor_id = $v['vendor_id'];
	 			$item->vendor_name = $v['vendor_name'];
	 			$item->position = $v['position'];
	 			$item->group_id = $v['group_id'];
	 			$item->course_id = $v['course_id'];
	 			$item->identity = $v['identify'];
	 			$item->unit_price = $v['unit_price'];
	 			$item->course_count = $v['course_count'];
	 			$item->bank_title = $v['bank_name'];
	 			$item->bank_account = $v['bank_account'];
	 			$item->fee_merge = $v['fee_merge'];
	 			if($item->save()) {
	 				$amount += $v['unit_price'] * $v['course_count'];
	 				if (isset($variationAmout[$v['vendor_id']])) {
		 				$amount += $variationAmout[$v['vendor_id']]->variation_amout;
	 				}
	 			}
 			}
 		}
 		$this->total_amount += $amount;
 		$this->save();
 	}

	/**
	 * [showData 查看固定收入数据]
	 * @return [type]           [description]
	 */
	public function showData()
	{
		$typeList = CommonUtils::LoadConfig('CfgASA');
		$month = $this->report_month;
		$variation = AsaMonthReportVariation::model()->findAllByAttributes(array('report_id'=>$this->id, 'settlement'=>1), array('index'=>'vendor_id'));

		$data = array();
		// $data['school'] = $this->schoolid;
		// $data['report_id'] = $this->id;
		// $data['report_month'] = $this->report_month;
		$data['total_amount'] = $this->total_amount;
		$data['vendors'] = array();
		// $data['unsettle_amount'] = $this->unsettle_amount;
		// $data['update'] = date('Y-m-d', $this->update_time);

		foreach ($this->item as $item) {
			$name = json_decode($item->vendor_name);
			$name[0] = trim($name[0], ' ');
			$name[1] = trim($name[1], ' ');
			if ($name[0] && $name[1]) {
				if ($name[0] == $name[1]) {
					$name[1] = '';
				} else {
					$name[0] = $name[0] .'，'. $name[1];
				}
			} else {
				if (!$name[0]) {
					$name[0] = $name[1];
				}
			}
			if (!isset($variation[$item->vendor_id])) {
				continue;
			}
			$data['vendors'][$item->vendor_id]['vendor_id'] = $item->vendor_id;
			$data['vendors'][$item->vendor_id]['name'] = $name[0];
			$data['vendors'][$item->vendor_id]['name_en'] = $name[1];
			$data['vendors'][$item->vendor_id]['cellphone'] = $item->vendor->cellphone;
			$data['vendors'][$item->vendor_id]['type'] = $typeList['type'][$item->vendor->type]['cn'];
			$data['vendors'][$item->vendor_id]['type_en'] = $typeList['type'][$item->vendor->type]['en'];
			$data['vendors'][$item->vendor_id]['identify'] = $item->identity;
			$data['vendors'][$item->vendor_id]['bank_name'] = $item->bank_title;
			$data['vendors'][$item->vendor_id]['bank_account'] = $item->bank_account;
			$data['vendors'][$item->vendor_id]['fee_merge'] = $item->fee_merge;
			if (!isset($data['vendors'][$item->vendor_id]['amount'])) {
				$data['vendors'][$item->vendor_id]['amount'] = 0;
			}
			$data['vendors'][$item->vendor_id]['amount'] += ($item->unit_price * $item->course_count);


			$data['vendors'][$item->vendor_id]['variation_amout'][$month] = $variation[$item->vendor_id]->variation_amout;
			$data['vendors'][$item->vendor_id]['variation_memo'][$month] = $variation[$item->vendor_id]->memo;

			$courseId = $item->course_id == 0 ? $item->group_id : $item->course_id;
			$groupCn = $item->group->title_cn .'-'. $item->group->id;
			$course_cn = isset($item->course)?$item->course->title_cn:$item->group->title_cn;
			$course_en = isset($item->course)?$item->course->title_en:$item->group->title_en;
			if ($course_cn == $course_en) {
				$course_en = '';
			}
			// 获取每门课程的调整信息
			// $attributes = array('variation_id'=>$variation[$item->vendor_id]->id, 'course_id'=>$courseId);
			// $variationItem = AsaMonthReportVariationItem::model()->findByAttributes($attributes);
			// $data['vendors'][$item->vendor_id]['courses'][$month][$courseId]['variation_amout'] = 0;
			// $data['vendors'][$item->vendor_id]['courses'][$month][$courseId]['variation_memo'] = '';
			// if ($variationItem) {
			// 	$data['vendors'][$item->vendor_id]['courses'][$month][$courseId]['variation_amout'] = $variationItem->variation_amout;
			// 	$data['vendors'][$item->vendor_id]['courses'][$month][$courseId]['variation_memo'] = $variationItem->memo;
			// }

			$data['vendors'][$item->vendor_id]['courses'][$month][$courseId]['group_cn'] = $groupCn;
			$data['vendors'][$item->vendor_id]['courses'][$month][$courseId]['course_name'] = $course_cn;
			$data['vendors'][$item->vendor_id]['courses'][$month][$courseId]['assistant_pay'] = 0;
			$data['vendors'][$item->vendor_id]['courses'][$month][$courseId]['course_name_en'] = $course_en;
			$data['vendors'][$item->vendor_id]['courses'][$month][$courseId]['course_count'] = $item->course_count;
			$data['vendors'][$item->vendor_id]['courses'][$month][$courseId]['unit_price'] = $item->unit_price;
			$data['vendors'][$item->vendor_id]['courses'][$month][$courseId]['position'] = $typeList['job_type'][$item->position]['cn'];
			$data['vendors'][$item->vendor_id]['courses'][$month][$courseId]['position_en'] = $typeList['job_type'][$item->position]['en'];
		}
		return $data;
	}

	// 显示分成收入的数据
	public function showDividedData()
	{
		$typeList = CommonUtils::LoadConfig('CfgASA');
		$variation = AsaMonthReportVariation::model()->findAllByAttributes(array('report_id'=>$this->id, 'settlement'=>1), array('index'=>'vendor_id'));
		$schoolid = $this->schoolid;
		$month = $this->report_month;

		$criteria = new CDbCriteria();
		$criteria->with = array('course', 'vendor', 'courseStaff');
		$criteria->compare('t.report_month', $month);
		$criteria->compare('t.school_id', $schoolid);
		$thirdParts = AsaThirdpartReport::model()->findAll($criteria);
		$data = array('total_amount'=>0, 'vendors'=>array());
		foreach ($thirdParts as $thirdPart) {
			$courseId = $thirdPart->course_id;
			$vendorId = $thirdPart->course_staff_id;

			// 查找课程分配信息
			if (isset($variation[$vendorId]) && $variation[$vendorId]->settlement) {
				$data['vendors'][$vendorId]['variation_amout'][$month] = $variation[$vendorId]->variation_amout;
				$data['vendors'][$vendorId]['variation_memo'][$month] = $variation[$vendorId]->memo;
			} else {
				continue;
			}
			$data['vendors'][$vendorId]['vendor_id'] = $vendorId;
			$courseStaff = AsaCourseStaff::model()->findByAttributes(array('course_id'=>$courseId, 'vendor_id'=>$vendorId));
			$data['vendors'][$vendorId]['type'] = $typeList['type'][$thirdPart->vendor->type]['cn'];
			$data['vendors'][$vendorId]['type_en'] = $typeList['type'][$thirdPart->vendor->type]['en'];
			$data['vendors'][$vendorId]['name'] = $thirdPart->vendor->getName();
			$data['vendors'][$vendorId]['cellphone'] = $thirdPart->vendor->cellphone;
			$data['vendors'][$vendorId]['fee_merge'] = $thirdPart->vendor->fee_merge;
			$data['vendors'][$vendorId]['identify'] = $thirdPart->vendor->vendorInfo->identify;
			$data['vendors'][$vendorId]['bank_name'] = $thirdPart->vendor->vendorInfo->bank_name;
			$data['vendors'][$vendorId]['bank_account'] = $thirdPart->vendor->vendorInfo->bank_account;
			if (!isset($data['vendors'][$vendorId]['amount'])) {
				$data['vendors'][$vendorId]['amount'] = 0;
			}
			// 课程信息
			$data['vendors'][$vendorId]['courses'][$month][$courseId]['unit_price'] = $thirdPart->unit_price;
			// 课程的名称
			$course_cn = isset($thirdPart->course)?$thirdPart->course->title_cn:$thirdPart->group->title_cn;
			$course_en = isset($thirdPart->course)?$thirdPart->course->title_en:$thirdPart->group->title_en;
			if ($course_cn == $course_en) {
				$course_en = '';
			}
			// 获取每门课程的调整信息
			// $attributes = array('variation_id'=>$variation[$vendorId]->id, 'course_id'=>$courseId);
			// $variationItem = AsaMonthReportVariationItem::model()->findByAttributes($attributes);
			// $data['vendors'][$vendorId]['courses'][$month][$courseId]['variation_amout'] = 0;
			// $data['vendors'][$vendorId]['courses'][$month][$courseId]['variation_memo'] = '';
			// if ($variationItem) {
			// 	$data['vendors'][$vendorId]['courses'][$month][$courseId]['variation_amout'] = $variationItem->variation_amout;
			// 	$data['vendors'][$vendorId]['courses'][$month][$courseId]['variation_memo'] = $variationItem->memo;
			// }

			$data['vendors'][$vendorId]['courses'][$month][$courseId]['course_name'] = $course_cn;
			$data['vendors'][$vendorId]['courses'][$month][$courseId]['assistant_pay'] = $thirdPart->assistant_pay;
			$data['vendors'][$vendorId]['courses'][$month][$courseId]['course_name_en'] = $course_en;
			if (isset($thirdPart->courseStaff)) {
				$data['vendors'][$vendorId]['courses'][$month][$courseId]['position'] = $typeList['job_type'][$thirdPart->courseStaff->job_type]['cn'];
				$data['vendors'][$vendorId]['courses'][$month][$courseId]['position_en'] = $typeList['job_type'][$thirdPart->courseStaff->job_type]['en'];
			} else {
				$data['vendors'][$vendorId]['courses'][$month][$courseId]['position'] = '';
				$data['vendors'][$vendorId]['courses'][$month][$courseId]['position_en'] = '';
			}

			// 课程的数量
			$course_month = $courseId .'_'. $month;
			$items = AsaThirdpartReportItem::model()->findAllByAttributes(array('course_month' => $course_month));
			foreach ($items as $item) {
				$data['vendors'][$vendorId]['courses'][$month][$courseId]['course_count'][] = $item->number;
				$data['vendors'][$vendorId]['amount'] += $item->number * $thirdPart->unit_price;
			}
		}
		return $data;
	}

	// 更新计算报表总金额
	 
	public function countAmount()
	{
		$totalAmount = 0;
		foreach ($this->item as $item) {
			$totalAmount += $item->unit_price *  $item->course_count;
		}
		// 计算浮动金额
		$variationId = $this->id;
		$variationModel = AsaMonthReportVariation::model()->findByAttributes(array('report_id'=>$variationId));
		foreach ($variationModel as $variation) {
			$totalAmount += $variation->variation_amout;
		}
		// $this->total_amount = $totalAmount;
		// $this->save();
		return $totalAmount;
	}

	// 计算某个老师在指定月份的工资
	static public function getVendorAmount($reportId, $vendorId)
	{
		$amount = 0;
		$reportItem = AsaMonthReportItem::model()->findAllByAttributes(array('report_id'=>$reportId, 'vendor_id'=>$vendorId));
		foreach ($reportItem as $item) {
			$amount += $item->course_count * $item->unit_price;
		}
		return $amount;
	}

	public function getReportStatus()
	{
		$status = array(
			'10' => Yii::t('site','Awaiting to Submit'),
			'20' => Yii::t('site','Approved'),
			'30' => Yii::t('site','Paid'),
		);
		return $status[$this->status];
	}

	// 根据签到记录计算固定收费的数据
	public function getFreelancerFee()
	{
		$typeList = CommonUtils::LoadConfig('CfgASA');

		$schoolid = $this->schoolid;
		$month = $this->report_month;
		$nMonth = date('Ym', strtotime($month .'01 '. '+1 month'));

		// 查找签到的数据
		$criteria = new CDbCriteria();
		$criteria->with = array('course', 'vendor', 'vendorTeacher', 'course', 'courseTitle', 'groupTitle');
		$criteria->compare('course.site_id', $schoolid);
		$criteria->compare('course.unit_type', array(1,2,3,4));
		$criteria->compare('t.status', array(1,3,10));
		$criteria->compare('t.service_end', '>=' . $month . '01');
		$criteria->compare('t.service_end', '<' . $nMonth . '01');
		$criteria->index = "id";
		$attendances = AsaStaffAttendance::model()->findAll($criteria);

		// 查找当前月是否有结算数据
		$criteria2 = new CDbCriteria();
		$criteria2->index = 'vendor_id';
		$criteria2->compare('report_id', $this->id);
		$variations = AsaMonthReportVariation::model()->findAll($criteria2);

		$data = array();
		foreach ($attendances as $attendance) {
			$vendorId = $attendance->vendorTeacher->vendor_id;
			$name = $attendance->vendorTeacher->getName();
			$vendorType = $attendance->vendorTeacher->type;
			$feeMerge = $attendance->vendorTeacher->fee_merge;
			// 判断是否有替补
			if (isset($attendance->substituter) && $attendance->substituter) {
				$vendorId = $attendance->vendor->vendor_id;
				$name = $attendance->vendor->getName();
				$feeMerge = $attendance->vendor->fee_merge;
			}

			$data[$vendorType][$vendorId]['vendor_id'] = $vendorId;
			if (!isset($data[$vendorType][$vendorId]['amount'])) {
				$data[$vendorType][$vendorId]['amount'] = 0;
			}
			$data[$vendorType][$vendorId]['name'] = $name;
			$data[$vendorType][$vendorId]['fee_merge'] = $feeMerge;
			$data[$vendorType][$vendorId]['type'] = $typeList['type'][$vendorType]['cn'];
			$data[$vendorType][$vendorId]['type_en'] = $typeList['type'][$vendorType]['en'];
			$data[$vendorType][$vendorId]['amount'] += $attendance->course->unit_salary;
			if (isset($attendance->vendorTeacher->vendorInfo)) {
				$data[$vendorType][$vendorId]['identify'] = $attendance->vendorTeacher->vendorInfo->identify;
				$data[$vendorType][$vendorId]['bank_name'] = $attendance->vendorTeacher->vendorInfo->bank_name;
				$data[$vendorType][$vendorId]['bank_account'] = $attendance->vendorTeacher->vendorInfo->bank_account;
			} else {
				$data[$vendorType][$vendorId]['identify'] = '';
				$data[$vendorType][$vendorId]['bank_name'] = '';
				$data[$vendorType][$vendorId]['bank_account'] = '';
				$data[$vendorType][$vendorId]['fee_merge'] = 0;
			}
			$data[$vendorType][$vendorId]['variation_amout'] = 0;
			$data[$vendorType][$vendorId]['variation_memo'] = '';
			$data[$vendorType][$vendorId]['settle'] = 0;  // 结算状态，0当月不结算，1当月结算，2未设置
			if (!isset($data[$vendorType][$vendorId]['courses'])) {
				$data[$vendorType][$vendorId]['courses'] = array();
			}
			$course_id = $attendance->course->course_id;
			$real_course_id = $attendance->course->course_id;
			$program_id = $attendance->course->program_id;
			if ($course_id == 0) {
				$course_id = $program_id;
			}
			// 获取每门课程的调整信息
			if (!isset($data[$vendorType][$vendorId]['courses'][$course_id])) {
				$data[$vendorType][$vendorId]['courses'][$course_id]['program_id'] = $program_id;
				$data[$vendorType][$vendorId]['courses'][$course_id]['variation_amout'] = 0;
				$data[$vendorType][$vendorId]['courses'][$course_id]['variation_memo'] = '';
			}
			if (isset($variations[$vendorId])) {
				$data[$vendorType][$vendorId]['settle'] = $variations[$vendorId]->settlement;
				$data[$vendorType][$vendorId]['variation_amout'] = $variations[$vendorId]->variation_amout;
				$data[$vendorType][$vendorId]['variation_memo'] = $variations[$vendorId]->memo;
				$attributes = array('variation_id'=>$variations[$vendorId]->id, 'course_id'=>$real_course_id);
				$variationItem = AsaMonthReportVariationItem::model()->findByAttributes($attributes);
				if ($variationItem) {
					$data[$vendorType][$vendorId]['courses'][$course_id]['variation_amout'] = $variationItem->variation_amout;
					$data[$vendorType][$vendorId]['courses'][$course_id]['variation_memo'] = $variationItem->memo;
				}
			}
			$course_cn = isset($attendance->courseTitle)?$attendance->courseTitle->title_cn:$attendance->groupTitle->title_cn;
			$course_en = isset($attendance->courseTitle)?$attendance->courseTitle->title_en:$attendance->groupTitle->title_en;
			$data[$vendorType][$vendorId]['courses'][$course_id]['course_id'] = $real_course_id;
			$data[$vendorType][$vendorId]['courses'][$course_id]['course_name'] = $course_cn;
			$data[$vendorType][$vendorId]['courses'][$course_id]['course_name_en'] = $course_en;
			$data[$vendorType][$vendorId]['courses'][$course_id]['assistant_pay'] = 0;
			if (!isset($data[$vendorType][$vendorId]['courses'][$course_id]['course_count'])) {
				$data[$vendorType][$vendorId]['courses'][$course_id]['course_count'][0] = 1;
			} else {
				$data[$vendorType][$vendorId]['courses'][$course_id]['course_count'][0] ++;
			}
			$data[$vendorType][$vendorId]['courses'][$course_id]['unit_price'] = $attendance->course->unit_salary;
			$data[$vendorType][$vendorId]['courses'][$course_id]['position'] = $typeList['job_type'][$attendance->course->job_type]['cn'];
			$data[$vendorType][$vendorId]['courses'][$course_id]['position_en'] = $typeList['job_type'][$attendance->course->job_type]['en'];
		}

		return $data;
	}


	// 根据分成考勤记录计算分成收费的数据
	public function getFreelancerDividedFee()
	{
		$typeList = CommonUtils::LoadConfig('CfgASA');

		$schoolid = $this->schoolid;
		$month = $this->report_month;

		$criteria = new CDbCriteria();
		$criteria->with = array('vendor', 'course', 'group', 'courseStaff', 'items');
		$criteria->compare('t.report_month', $month);
		$criteria->compare('t.school_id', $schoolid);
		$thirdParts = AsaThirdpartReport::model()->findAll($criteria);
		$criteria2 = new CDbCriteria();
		$criteria2->index = 'vendor_id';
		$criteria2->compare('report_id', $this->id);
		$variations = AsaMonthReportVariation::model()->findAll($criteria2);

		$data = array();
		foreach ($thirdParts as $thirdPart) {
			$vendorId = $thirdPart->course_staff_id;
			$vendorType = $thirdPart->vendor->type;
			$course_id = $thirdPart->course_id;
			$program_id = $thirdPart->program_id;
			$data[$vendorType][$vendorId]['vendor_id'] = $vendorId;
			if (!isset($data[$vendorType][$vendorId]['amount'])) {
				$data[$vendorType][$vendorId]['amount'] = 0;
			}
			$data[$vendorType][$vendorId]['name'] = $thirdPart->vendor->getName();
			$data[$vendorType][$vendorId]['type'] = $typeList['type'][$vendorType]['cn'];
			$data[$vendorType][$vendorId]['type_en'] = $typeList['type'][$vendorType]['en'];
			$data[$vendorType][$vendorId]['identify'] = $thirdPart->vendor->vendorInfo->identify;
			$data[$vendorType][$vendorId]['bank_name'] = $thirdPart->vendor->vendorInfo->bank_name;
			$data[$vendorType][$vendorId]['bank_account'] = $thirdPart->vendor->vendorInfo->bank_account;
			$data[$vendorType][$vendorId]['fee_merge'] = $thirdPart->vendor->fee_merge;
			$data[$vendorType][$vendorId]['variation_amout'] = 0;
			$data[$vendorType][$vendorId]['variation_memo'] = '';
			$data[$vendorType][$vendorId]['settle'] = 0;  // 结算装填，0当月不结算，1当月结算，2未设置
			if (!isset($data[$vendorType][$vendorId]['courses'])) {
				$data[$vendorType][$vendorId]['courses'] = array();
			}
			// 获取每门课程的调整信息
			if (!isset($data[$vendorType][$vendorId]['courses'][$course_id])) {
				$data[$vendorType][$vendorId]['courses'][$course_id]['program_id'] = $program_id;
				$data[$vendorType][$vendorId]['courses'][$course_id]['variation_amout'] = 0;
				$data[$vendorType][$vendorId]['courses'][$course_id]['variation_memo'] = '';
			}
			if (isset($variations[$vendorId])) {
				$data[$vendorType][$vendorId]['settle'] = $variations[$vendorId]->settlement;
				$data[$vendorType][$vendorId]['variation_amout'] = $variations[$vendorId]->variation_amout;
				$data[$vendorType][$vendorId]['variation_memo'] = $variations[$vendorId]->memo;
				$attributes = array('variation_id'=>$variations[$vendorId]->id, 'course_id'=>$course_id);
				$variationItem = AsaMonthReportVariationItem::model()->findByAttributes($attributes);
				if ($variationItem) {
					$data[$vendorType][$vendorId]['courses'][$course_id]['variation_amout'] = $variationItem->variation_amout;
					$data[$vendorType][$vendorId]['courses'][$course_id]['variation_memo'] = $variationItem->memo;
				}
			}
			$course_cn = isset($thirdPart->course)?$thirdPart->course->title_cn:$thirdPart->group->title_cn;
			$course_en = isset($thirdPart->course)?$thirdPart->course->title_en:$thirdPart->group->title_en;
			if ($course_cn == $course_en) {
				$course_en = '';
			}
			$data[$vendorType][$vendorId]['courses'][$course_id]['course_id'] = $course_id;
			$data[$vendorType][$vendorId]['courses'][$course_id]['course_name'] = $course_cn;
			$data[$vendorType][$vendorId]['courses'][$course_id]['course_name_en'] = $course_en;
			$data[$vendorType][$vendorId]['courses'][$course_id]['assistant_pay'] = $thirdPart->assistant_pay;
			foreach ($thirdPart->items as $item) {
				$data[$vendorType][$vendorId]['amount'] += $item->number * $thirdPart->unit_price;
				$data[$vendorType][$vendorId]['courses'][$course_id]['course_count'][] = $item->number;
			}
			$data[$vendorType][$vendorId]['courses'][$course_id]['unit_price'] = $thirdPart->unit_price;
			if (isset($thirdPart->courseStaff)) {
				$data[$vendorType][$vendorId]['courses'][$course_id]['position'] = $typeList['job_type'][$thirdPart->courseStaff->job_type]['cn'];
				$data[$vendorType][$vendorId]['courses'][$course_id]['position_en'] = $typeList['job_type'][$thirdPart->courseStaff->job_type]['en'];
			} else {
				$data[$vendorType][$vendorId]['courses'][$course_id]['position'] = '';
				$data[$vendorType][$vendorId]['courses'][$course_id]['position_en'] = '';
			}
		}

		return $data;
	}

	// 保存结算及调整信息
	public function saveVariation($data)
	{
		$month = $this->report_month;
		$uid = $this->uid;
		$reportid = $this->id;
		$vendorIds = array_keys($data);
		// 查找当前月所有是否结算数据
		$criteria = new CDbCriteria();
		$criteria->compare('vendor_id', $vendorIds);
		$criteria->compare('report_id', $reportid);
		$criteria->index = 'vendor_id';
		$variations = AsaMonthReportVariation::model()->findAll($criteria);
		foreach ($data as $vendorId => $settlement) {
			$variation = isset($variations[$vendorId]) ? $variations[$vendorId] : '';
			if (!$variation) {
				$variation = new AsaMonthReportVariation();
				$variation->report_id = $reportid;
				$variation->vendor_id = $vendorId;
			}
			$variation->update_time = time();
			$variation->uid = $uid;
			$variation->settlement = $settlement['settle'];
			$variation->variation_amout = $settlement['amount'];
			$variation->memo = $settlement['memo'];
			$variation->settle_report_id = 0;
			$variation->expense_id = 0;
			if ($variation->save()) {
				// 保存调整详情表
				AsaMonthReportVariationItem::model()->deleteAllByAttributes(array('variation_id'=>$variation->id));
				foreach ($settlement['amount_item'] as $course_id => $item) {
					if ($item['amount'] == 0) {
						continue;
					}
					$model = new AsaMonthReportVariationItem();
					$model->report_id = $this->id;
					$model->variation_id = $variation->id;
					$model->program_id = $item['program_id'];
					$model->course_id = $item['course_id'];
					$model->variation_amout = $item['amount'];
					$model->memo = $item['memo'];
					$model->update_time = time();
					$model->uid = $this->uid;
					$model->save();
				}
			}
		}
	}

	// 获取其它月结算到当月的数据
	public function otherMonthData()
	{
		$typeList = CommonUtils::LoadConfig('CfgASA');
		$reportid = $this->id;

		$criteria = new CDbCriteria();
		$criteria->compare('settle_report_id', $reportid);
		$criteria->compare('settlement', 0);
		$variations = AsaMonthReportVariation::model()->findAll($criteria);
	
		$data = array();
		foreach ($variations as $variation) {
			$vendorId = $variation->vendor_id;
			$month = $variation->report->report_month;

			$data[$vendorId]['vendor_id'] = $vendorId;
			$data[$vendorId]['type'] = $typeList['type'][$variation->vendor->type]['cn'];
			$data[$vendorId]['type_en'] = $typeList['type'][$variation->vendor->type]['en'];
			$data[$vendorId]['name'] = $variation->vendor->getName();
			$data[$vendorId]['fee_merge'] = $variation->vendor->fee_merge;
			$data[$vendorId]['identify'] = $variation->vendor->vendorInfo->identify;
			$data[$vendorId]['bank_name'] = $variation->vendor->vendorInfo->bank_name;
			$data[$vendorId]['variation_amout'][$month] = $variation->variation_amout;
			$data[$vendorId]['variation_memo'][$month] = $variation->memo;
			if (!isset($data[$vendorId]['amount'])) {
				$data[$vendorId]['amount'] = 0;
			}
			// 计算固定费用
			$crit = new CDbCriteria();
			$crit->compare('report_id', $variation->report_id);
			$crit->compare('vendor_id', $variation->vendor_id);
			$items = AsaMonthReportItem::model()->findAll($crit);
			foreach ($items as $item) {
				$courseId = $item->course->id;
				$course_cn = isset($item->course)?$item->course->title_cn:$item->group->title_cn;
				$course_en = isset($item->course)?$item->course->title_en:$item->group->title_en;
				if ($course_cn == $course_en) {
					$course_en = '';
				}
				// 获取每门课程的调整信息
				// $attributes = array('variation_id'=>$variation->id, 'course_id'=>$courseId);
				// $variationItem = AsaMonthReportVariationItem::model()->findByAttributes($attributes);
				// $data[$vendorId]['courses'][$month][$courseId]['variation_amout'] = 0;
				// $data[$vendorId]['courses'][$month][$courseId]['variation_memo'] = '';
				// if ($variationItem) {
				// 	$data[$vendorId]['courses'][$month][$courseId]['variation_amout'] = $variationItem->variation_amout;
				// 	$data[$vendorId]['courses'][$month][$courseId]['variation_memo'] = $variationItem->memo;
				// }

				$data[$vendorId]['courses'][$month][$courseId]['course_name'] = $course_cn;
				$data[$vendorId]['courses'][$month][$courseId]['assistant_pay'] = 0;
				$data[$vendorId]['courses'][$month][$courseId]['course_name_en'] = $course_en;
				$data[$vendorId]['courses'][$month][$courseId]['course_count'] = $item->course_count;
				$data[$vendorId]['courses'][$month][$courseId]['unit_price'] = $item->unit_price;
				$data[$vendorId]['courses'][$month][$courseId]['position'] = $typeList['job_type'][$item->position]['cn'];
				$data[$vendorId]['courses'][$month][$courseId]['position_en'] = $typeList['job_type'][$item->position]['en'];
				$data[$vendorId]['amount'] += $item->course_count * $item->unit_price;
			}
			// 计算分成费用
			$crit = new CDbCriteria();
			$crit->with = array('course', 'vendor');
			$crit->compare('t.report_month', $month);
			$crit->compare('t.course_staff_id', $vendorId);
			$thirdParts = AsaThirdpartReport::model()->findAll($crit);
			foreach ($thirdParts as $thirdPart) {
				$courseId = $thirdPart->course_id;
				// 课程信息
				$data[$vendorId]['courses'][$month][$courseId]['unit_price'] = $thirdPart->unit_price;
				// 课程的名称
				$course_cn = isset($thirdPart->course)?$thirdPart->course->title_cn:$thirdPart->group->title_cn;
				$course_en = isset($thirdPart->course)?$thirdPart->course->title_en:$thirdPart->group->title_en;
				if ($course_cn == $course_en) {
					$course_en = '';
				}
				$data[$vendorId]['courses'][$month][$courseId]['course_name'] = $course_cn;
				$data[$vendorId]['courses'][$month][$courseId]['course_name_en'] = $course_en;
				$data[$vendorId]['courses'][$month][$courseId]['assistant_pay'] = $thirdPart->assistant_pay;
				if (isset($thirdPart->courseStaff)) {
					$data[$vendorId]['courses'][$month][$courseId]['position'] = $typeList['job_type'][$thirdPart->courseStaff->job_type]['cn'];
					$data[$vendorId]['courses'][$month][$courseId]['position_en'] = $typeList['job_type'][$thirdPart->courseStaff->job_type]['en'];
				} else {
					$data[$vendorId]['courses'][$month][$courseId]['position'] = '';
					$data[$vendorId]['courses'][$month][$courseId]['position_en'] = '';
				}
				// 课程的数量
				$course_month = $courseId .'_'. $month;
				$items = AsaThirdpartReportItem::model()->findAllByAttributes(array('course_month' => $course_month));
				foreach ($items as $item) {
					$data[$vendorId]['amount'] += $item->number * $thirdPart->unit_price;
					$data[$vendorId]['courses'][$month][$courseId]['course_count'][] = $item->number;
				}
			}
		}

		return $data;
	}
}
