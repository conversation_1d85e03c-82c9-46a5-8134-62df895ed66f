<?php

class FeedbackController extends ProtectedController
{
	public $actionAccessAuths = array(
		'index'=>'ivystaff_opgeneral',
	);

	public $dialogWidth = 600;
	
	public function init() 
	{
	    parent::init();

	    Yii::app()->theme = 'blue';
	    $this->layout = "//layouts/column1";

	    $this->modernMenuFlag = 'hqOp';
	    $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
	    $this->modernMenuCategoryTitle = Yii::t('site','HQ Operations');
	}

	public function actionIndex()
	{
		Yii::import('common.models.comment.Comment');
		$model = new Comment();
		$this->render('index',array(
			'model' => $model,
			));
		//$this->render('index');
	}
	public function actionDelete()
	{
		if ($_POST['id']) {
			Yii::import('common.models.comment.Comment');
			$model = new Comment();
			$model = $model -> findByPk($_POST['id']);
			if($model -> delete()){
				$this -> addMessage('state', 'success');
				$this -> addMessage('callback', 'cbFeedbacks');
				$this -> addMessage('message', Yii::t('message','Data saved!'));
			}else{
				$this -> addMessage('state', 'fail');
				$this -> addMessage('message', '');
			}
			$this->showMessage();
		}
	}
	
	public function actionSaveFeedback(){
		Yii::import('common.models.comment.Comment');
		$model = new Comment();
		$content= $_POST['Comment'];
		$model->content = $content['content'];
		$model->times    = time();
		$model->uid = Yii::app()->user->id;
		if($model->save()){
			$mailer = Yii::createComponent('common.extensions.mailer.EMailer');
			$mailer->Subject = sprintf('%s反馈建议', "");
			$mailer->AddAddress('<EMAIL>');
			$mailer->AddReplyTo($this->staff->email);
			$mailer->getView("feedback", array('feedback_content' => $model->content, 'schoolid' =>$this->staff->profile->branch, 'name' => $this->staff), 'main');
			$mailer->iniMail( OA::isProduction()); // 此行代码要放到AddAddress, AddCC方法下面
			$mailer->Send();

			$this->addMessage('state', 'success');
			$this->addMessage('callback', 'cbFeedback');
			$this->addMessage('message', Yii::t('message','Data saved!'));
		}else {
			$this->addMessage('state', 'fail');
			$errs = current($model->getErrors());
			$this->addMessage('message', $errs?$errs[0]:Yii::t('message','Saving Failed!'));
		}
		$this->showMessage();
	}
	
	public function showBtn($data)
	{
		echo '<a href="'.$this->createUrl('delete', array('id'=>$data->id)).'" class="btn btn-danger btn-xs J_ajax_del" ><span class="glyphicon glyphicon-trash"></span></a>';
	}
}
