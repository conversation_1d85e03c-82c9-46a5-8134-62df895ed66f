{"name": "phpunit/phpunit", "description": "The PHP Unit Testing framework.", "type": "library", "keywords": ["phpunit", "xunit", "testing"], "homepage": "https://phpunit.de/", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues"}, "require": {"php": ">=5.3.3", "phpunit/php-file-iterator": "~1.4", "phpunit/php-text-template": "~1.2", "phpunit/php-code-coverage": "~2.1", "phpunit/php-timer": "^1.0.6", "phpunit/phpunit-mock-objects": "~2.3", "phpspec/prophecy": "^1.3.1", "symfony/yaml": "~2.1|~3.0", "sebastian/comparator": "~1.2.2", "sebastian/diff": "~1.2", "sebastian/environment": "~1.3", "sebastian/exporter": "~1.2", "sebastian/global-state": "~1.0", "sebastian/version": "~1.0", "ext-dom": "*", "ext-json": "*", "ext-pcre": "*", "ext-reflection": "*", "ext-spl": "*"}, "config": {"platform": {"php": "5.3.3"}}, "suggest": {"phpunit/php-invoker": "~1.1"}, "bin": ["phpunit"], "autoload": {"classmap": ["src/"]}, "autoload-dev": {"classmap": ["tests/"], "files": ["src/Framework/Assert/Functions.php", "tests/_files/CoveredFunction.php"]}, "extra": {"branch-alias": {"dev-master": "4.8.x-dev"}}}