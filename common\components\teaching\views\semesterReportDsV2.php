<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="language" content="en" />
    <title><?php echo CHtml::encode($this->pageTitle); ?></title>
</head>

<body>

    <?php if($withToolBar):?>
        <div class="navbar navbar-fixed-top navbar-inverse" role="navigation">
            <?php
            echo CHtml::form( $downloadActionUrl );
            echo CHtml::hiddenField('schoolid',     $params['schoolid']);
            echo CHtml::hiddenField('childid',      $params['childid']);
            echo CHtml::hiddenField('classid',      $params['classid']);
            echo CHtml::hiddenField('id',           $params['id']);
            echo CHtml::submitButton('Download PDF / 下载 PDF', array('style'=>'font-family: Microsoft Yahei'));
            echo CHtml::endForm();
            ?>
        </div>
    <?php endif;?>

    <div class="report-wrapper web-view">
        <!--page one-->
        <div id="page-1" class="report-page page-break-after">
            <!-- page content-->
            <div class="page-first-content">

                <?php
                if($data['child']->photo){
                    echo CHtml::image(CommonUtils::childPhotoUrl($data['child']->photo),
                        null,
                        array('class' => 'profile-photo')
                    );
                }

                $idschoolyear = is_array($params['id']) ? current($params['id']) : $params['id'];
                ?>

                <div class="profile-box">
                    <dl>
                        <dt><span class="cn"><?php echo Yii::t('report', 'Name');?></span></dt>
                        <dd><?php
                            $childName = $data['child']->getReportChildName();
                            $hasCNChars = CommonUtils::hasCNChars( $childName );

                            if( $hasCNChars > 0){
                                echo '<span class="cn">';
                            }else{
                                echo '<span class="en">';
                            }

                            echo $childName . '</span>';

                            ?></dd>

                        <dt><span class="cn"><?php echo Yii::t('report', 'DOB');?></span></dt>
                        <dd><?php echo $data['child']->birthday_search?></dd>

                        <dt><span class="cn"><?php echo Yii::t('report', 'Campus');?></span></dt>
                        <dd><?php
                            $title = $data['campusTitle'];
                            $title = str_replace('(', '(<span class="cn">', $title);
                            $title = str_replace(')', '</span>)', $title);
                            echo $title;
                            ?></dd>

                        <dt><span class="cn"><?php echo Yii::t('report', 'Class');?></span></dt>
                        <dd><?php echo $data['classTitle'];?></dd>

                        <dt><span class="cn"><?php echo Yii::t('report', 'Class Teachers');?></span></dt>
                        <dd>
                            <div class="teacher-list">
                                <?php
                                if ($data['teachers']):?>
                                    <?php foreach ($data['teachers'] as $tName):?>
                                        <span class="item"><?php echo $tName['name'] . ' - ' . $tName['position'];?></span>
                                    <?php endforeach;?>
                                <?php endif;?>
                            </div>
                        </dd>
                    </dl>
                </div>
            </div><!-- page content -->
            <div class="top-title">
                <div class="cn"><?php echo IvyClass::formatSchoolYear($data['report_base'][$idschoolyear]['startyear']);?>评估报告</div>
                <div class="en"><?php echo IvyClass::formatSchoolYear($data['report_base'][$idschoolyear]['startyear'])?> Progress Report</div>
            </div>
            <div class="logo-top-center"></div>
        </div>
        <div id="page-2" class="report-page page-break-after">
            <div style="height: 120px;"></div>
            <div class="pull-content">
                <table class="table table-bordered">
                    <tr>
                        <td width="50%"></td>
                        <td width="25%"><?php echo Yii::t('report', 'Term 1');?></td>
                        <td width="25%"><?php echo Yii::t('report', 'Term 2');?></td>
                    </tr>
                    <tr>
                        <td><?php echo Yii::t('report', 'Days Absent');?></td>
                        <td><?php echo $data['report_attendance'][1]['absent']?></td>
                        <td><?php echo $data['report_attendance'][2]['absent']?></td>
                    </tr>
                    <tr>
                        <td><?php echo Yii::t('report', 'Days Tardy');?></td>
                        <td><?php echo $data['report_attendance'][1]['tardy']?></td>
                        <td><?php echo $data['report_attendance'][2]['tardy']?></td>
                    </tr>
                    <tr>
                        <td><?php echo Yii::t('report', 'Additional Support and Services');?></td>
                        <td><?php echo $data['report_attendance'][1]['support']?></td>
                        <td><?php echo $data['report_attendance'][2]['support']?></td>
                    </tr>
                </table>
                <?php if(Yii::app()->language == 'zh_cn'):?>
                    <table class="table cn">
                        <thead>
                        <tr>
                            <th colspan="3">孩子作为一名学习者</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td colspan="3">
                            <p>启明星学校笃行中西合璧之教育模式，博采东西方文化之长，秉承服务全社会之宗旨，致力于培养中英文学业卓越，具有创造性思维及高贵品格的世界公民。启明星学校的学生努力发展成为:
                            </p>
                            <p>全球贡献者 理解并欣赏多元文化和视角，具有同理心、同情心和负责任的社会企业家精神。</p>
                            <p>批判思考者 是信息的批判主义消费者，运用技能和流程为真实世界的问题提供创新方法和解决方案。</p>
                            <p>有效交流者 是掌握多种语言的人，自信、尊重他人，努力和他人达成共识。</p>
                            <p>主动学习者 展示出天生的好奇心，内在动力以及强烈的自我效能感，激励终身学习。 </p>
                            <p>有原则的公民 致力于个人的发展，在行动中注重正直、体谅他人和有目的性的重要性。</p>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                <?php else:?>
                    <table class="table en">
                        <thead>
                        <tr>
                            <th colspan="3">The Child As A Learner</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td colspan="3">

                                <p>Daystar Academy develops world citizens by embracing Chinese and Western culture, through its integrated education model.Daystar students strive for distinction in Chinese and English studies, creative thinking and character development for the purpose of serving the community at large. Daystar learners strive to be:</p>
                                <p>Global Contributors who understand and appreciate diverse cultures and perspectives and engage in empathetic, compassionate, and responsible social entrepreneurship.</p>
                                <p>Critical Thinkers who are critical consumers of information and apply skills and processes to provide innovative approaches and solutions to real-world issues.</p>
                                <p>Effective Communicators who are multilingual, confident, and respectful and who strive for reaching consensus and understanding.</p>
                                <p>Empowered Learners who demonstrate a natural curiosity, intrinsic motivation, and strong sense of self-efficacy to inspire life-long learning.</p>
                                <p>Principled Citizens who are committed to one’s personal development and emphasize the significance of integrity, thoughtfulness, and intentionality in one’s actions.</p>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                <?php endif;?>
                <table class="table">
                    <thead>
                    <tr>
                        <th><?php echo Yii::t('report', 'Grade-Level Proficiency')?></th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php foreach ($optionsList as $group):
                        if($group['title'] != 'ESL'):
                        ?>
                        <tr>
                            <td align="center" style="border-top:0;">
                                <?php echo $group['title']; ?>
                                <table class="table" style="margin-bottom:2px;">
                                    <tr>
                                        <?php foreach ($group['options'] as $option): ?>
                                            <td style="border-top:0;text-align: center;"><strong><?php echo $option['label']; ?></strong><br/><?php echo CommonUtils::autoLang($option['title_cn'], $option['title_en']); ?></td>
                                        <?php endforeach; ?>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    <?php endif;endforeach; ?>
                    </tbody>
                </table>
            </div>
            <div class="logo-contact-left"></div>
            <div class="top-small-title">
                <div class="cn">Progress Report 评估报告</div>
                <div class="cn">Daystar Academy启明星学校</div>
                <div class="cn"><?php echo IvyClass::formatSchoolYear($data['report_base'][$idschoolyear]['startyear']);?></div>
            </div>
        </div>
        <!--page one-->
        <div id="page-6" class="report-page">
            <div style="height: 20px;"></div>
            <div class="pull-content">
                <?php foreach ($data['root'] as $root):?>
                    <h3><span class="cn"><?php echo $root['title']?></span></h3>
                    <?php
                        $i=0;
                        foreach ($data['subs'][$root['id']] as $subs):
                        $i++;
                    ?>
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th><?php echo $subs['title'];?></th>
                                    <?php foreach ($data['reportSemester'] as $semester):?>
                                        <th colspan="<?php echo count($optionsList[$subs['option_groupid']]['options']);?>" style="width:50px;"><?php echo $semester;?></th>
                                    <?php endforeach;?>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td></td>
                                    <?php foreach ($data['reportSemester'] as $semester):?>
                                        <?php foreach ($optionsList[$subs['option_groupid']]['options'] as $option):?>
                                        <td><?php echo $option['label'];?></td>
                                        <?php endforeach;?>
                                    <?php endforeach;?>
                                </tr>
                                <?php
                                foreach ($data['items'][$subs['id']] as $keyItem=>$items):?>
                                <tr>
                                    <td style="width:280px;"><?php echo $items;?></td>
                                    <?php foreach ($data['reportSemester'] as $srKey=>$semester):?>
                                        <?php foreach ($optionsList[$subs['option_groupid']]['options'] as $option):?>
                                        <td align="center">
                                            <?php
                                                if (isset($data['report'][$srKey][$root['id']]['option'][$subs['id']][$keyItem]) && $data['report'][$srKey][$root['id']]['option'][$subs['id']][$keyItem] == $option['id'] ):
                                                    echo CHtml::image('http://m1.files.ivykids.cn/images/sreport/booked.png','',array('width'=>10,'height'=>10));
                                                else:
                                                    echo '';
                                                endif;
                                            ?>
                                        </td>
                                        <?php endforeach;?>
                                    <?php endforeach;?>
                                </tr>
                                <?php endforeach;?>
                            </tbody>
                        </table>
                    <?php endforeach;?>
                <?php endforeach;?>

                <div class='page-break-after pt20'>
                    <table class="table table table-bordered">
                        <?php foreach ($data['reportSemester'] as $srKey=>$semester):?>
                            <tr>
                                <th align="left"><?php echo $semester. ' '. Yii::t('report', 'Teacher\'s Comment / Next Step');?></th>
                            </tr>
                            <?php
                            if(isset($data['report'][$srKey])):
                            foreach($data['report'][$srKey] as $r_key => $r_value):
                                if(isset($r_value['memo']) && $r_value['memo']):
                            ?>
                            <tr>
                                <td><?php echo $data['root'][$r_key]['title'].CommonUtils::addColon().$data['report'][$srKey][$r_key]['memo'];?></td>
                            </tr>
                            <?php
                            endif;
                            endforeach;
                            endif;
                            ?>
                        <?php endforeach;?>
                    </table>
                </div>
                <?php if($data['report_base'][$idschoolyear]['startyear'] < 2021):?>
                    <?php if(Yii::app()->language == 'zh_cn'):?>
                        <div class="cn">
                            <h3>评估报告注释</h3>
                            <p>启明星学校首要目标是让每位学生通过学习取得学业上的成功，发展人际交往能力。评估报告的目的是根据年级评定标准，反映学生进步的信息。在语言艺术、社会学、科学等学科上，使用专业标准来评估每位学生。</p>
                            <h3>学业</h3>
                            <h5>处于年级水平起步阶段</h5>
                            <p>学生取得一些进步，但还低于年级期望水平。学生还没有完全掌握应有知识和技能。学生的表现低于应有的年级水平，可能需要更多的支持和努力。</p>
                            <h5>处于年级水平发展阶段</h5>
                            <p>表明学生正在取得进步，学年末有足够的能力达到年级水平。处于该阶段学生已经达到年级水平的所有标准。学生已经取得了预期的进步，但还未达到学年末水平。和您的孩子沟通，说明D表示他/她正在朝学年末达标标准进步，这很重要。</p>
                            <h5>处于年级水平达标阶段</h5>
                            <p>表明学生已经达到本年级学年末标准。目标是让所有学生在年末达到该标准。</p>
                            <h5>超出年级水平阶段</h5>
                            <p>表明学生已经超过了该年级应有标准。这个阶段的学生能够将所学知识运用到新的领域，并且独立使用知识技能，完成更高年级的工作。</p>
                            <?php
                                if (in_array($yid, array(117, 135))) {
                                    echo '<h5>没有证据</h5>';
                                    echo '<p>没有足够的证据对学生的学习情况做出判断</p>';
                                }
                            ?>
                            <h3>非学业</h3>
                            <p>学生表现是基于老师观察发现的行为频率来测评。需要注意的是，非学术分级评定参照的是行为数量，而不是质量。老师只汇报观察到的行为出现频率，而非对此行为作出评价。</p>
                            <h5>有待提高</h5>
                            <p>表明学生很少或有时表现良好</p>
                            <h5>经常</h5>
                            <p>表明学生大部分时间表现良好</p>
                            <h5>一贯</h5>
                            <p>表明学生一如既往表现良好</p>
                            <?php
                            if (in_array($yid, array(117, 135))) {
                                    echo '<h5>没有证据</h5>';
                                    echo '<p>没有足够的证据对学生的学习情况做出判断</p>';
                                }
                            ?>
                        </div>
                    <?php else:?>
                        <div class="en">
                            <h3>Explanation of the Progress Report</h3>
                            <p>The primary goal of Daystar Academy is to enable each student to learn and achieve academic success and develop effective interpersonal skills. The purpose of the Progress Report is to give more information about each child’s progress in relation to the grade-level standards. Each grade-level assesses students on the most significant standards in the areas of language arts, social studies, and science.</p>
                            <h3>Academic</h3>
                            <h5>Beginning progress toward grade-level standards:</h5>
                            <p>The student is making some progress, but is below the level that is expected at that time of the marking period. The student shows limited understanding of the end of year concepts and skills. Student performance is “below” grade level and may need more support to catch up.</p>
                            <h5>Developing progress toward grade-level standards:</h5>
                            <p>Indicates that the student is progressing at a sufficient rate to be able to meet the end-of-the-year grade-level expectations. This grade would be appropriate for an “on-level’ student for most standards. The student is making expected progress but is not yet at end-of-year standards. It is important to communicate with your child that D indicates he or she is “on track” to achieving a “Proficient” by the end of the school year. </p>
                            <h5>Proficient in meeting grade-level standards:</h5>
                            <p>Indicates a student has met the end-of-the-year standards for the grade. The goal is for all students to receive this grade by the end of the year.</p>
                            <h5>Exceeding grade-level standards:</h5>
                            <p>Indicates a student has gone beyond the end-of-the-year standards for that grade. Students who are exceeding are able to apply learning to new situations and independently use strategies and skills to do work that are expected of students in higher grades.</p>
                            <?php
                            if (in_array($yid, array(117, 135))) {
                                    echo '<h5>No Evidence</h5>';
                                    echo "<p>Not enough evidence is available to make a determination about student's learning</p>";
                                }
                            ?>
                            <h3>Non-Academic</h3>
                            <p>Performance is assessed based on the FREQUENCY of desired behaviors, as informed by teacher observation. Thus the teachers are reporting the frequency with which these behaviors are being observed, rather than judging the quality of the behavior. </p>
                            <h5>Improvement Needed</h5>
                            <p>Indicates that the student seldom or sometimes displays desired behavior.</p>
                            <h5>Often</h5>
                            <p>Indicates that the student displays desired behavior most of the time.</p>
                            <h5>Consistently</h5>
                            <p>Indicates a student is consistent in displaying desired behavior.</p>
                            <?php
                            if (in_array($yid, array(117, 135))) {
                                    echo '<h5>No Evidence</h5>';
                                    echo "<p>Not enough evidence is available to make a determination about student's learning</p>";
                                }
                            ?>
                        </div>
                     <?php endif;?>
                <?php else:?>
                    <?php if(Yii::app()->language == 'zh_cn'):?>
                        <div class="cn color3">
                            <div class='pt20 page-break-after'>
                                <div class='font14 mt10'><strong>评估报告注释</strong></div>
                                <p>启明星学校首要目标是让每位学生通过学习取得学业上的成功，发展人际交往能力。评估报告的目的是根据年级评定标准，反映 学生进步的信息。在语言艺术、社会学、科学等学科上，使用专业标准来评估每位学生。 </p>
                                <div class='relative pl10 mt20'>
                                    <strong>
                                    <span class='piont'></span> 学业
                                    </strong>
                                </div>
                                <p>小学评估报告上学业部分的成绩有四个级别反映学生的水平：起步、发展中、达标、优秀。下面的表格提供了各个水平的详细描述：</p>
                                <table  class='table  table-bordered Annotation' >
                                    <tr>
                                        <th width='70'>水平</th>
                                        <th>说明</th>
                                    </tr>
                                    <tr>
                                        <th>E</th>
                                        <td>
                                            <p class='flex'>
                                                <strong class='flexWidth'>优秀：</strong>
                                                <span class='flex1'>学生始终轻松掌握年级标准，而且经常超过标准所要求的认知水平。学生应用并拓展年级所学的重要概念、流程和技能。</span>
                                            </p>
                                            <p class='flex'>
                                                <strong class='flexWidth'>专家：</strong>
                                                <span class='flex1'>学生可以向他人展示如何运用技能并且准确地评估技能运用的有效程度（自我调整）</span>
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>P</th>
                                        <td>
                                            <p class='flex'>
                                                <strong class='flexWidth'>达标：</strong>
                                                <span class='flex1'>学生掌握了年级标准，而且处于标准所要求的认知水平。学生始终掌握重要概念、流程和技能，且应用时错误很少。</span>
                                            </p>
                                            <p class='flex'>
                                                <strong class='flexWidth'>实践者：</strong>
                                                <span class='flex1'>学生自信有效地运用技能（展示）</span>
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>L</th>
                                        <td>
                                            <p class='flex'>
                                                <strong class='flexWidth'>发展中：</strong>
                                                <span class='flex1'>学生掌握了一些年级标准，但没有一贯稳定地掌握和应用重要概念、流程和技能，有明显的错误。</span>
                                            </p>
                                            <p class='flex'>
                                                <strong class='flexWidth'>学习者：</strong>
                                                <span class='flex1'>学生模仿他人使用技能，但需要在辅助和指导下使用技能（模仿）</span>
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>N</th>
                                        <td>
                                            <p class='flex'>
                                                <strong class='flexWidth'>起步：</strong>
                                                <span class='flex1'>学生对标准理解较有限，未能达到年级标准。即使在指导和辅助下，学生的表现也不稳定。</span>
                                            </p>
                                            <p class='flex'>
                                                <strong class='flexWidth'>新手：</strong>
                                                <span class='flex1'>学生初次了解技能，可以观看他人操作技能（观察）</span>
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>N/A</th>
                                        <td>
                                            <p class='flex'>
                                                <strong class='flexWidth'>不评估：</strong>
                                                <span class='flex1'>此次未评估这个标准或学习领域。</span>
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>N/E</th>
                                        <td>
                                            <p class='flex'>
                                                <strong class='flexWidth'>没有证据：</strong>
                                                <span class='flex1'>没有足够的学习证据，无法按照要求对学生的表现情况做出公正的评估。</span>
                                            </p>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class='pt20 page-break-after pb20'>
                                <div class='relative pl10 mt10'>
                                    <strong>
                                    <span class='piont'></span> 非学业
                                    </strong>
                                </div>
                                <p>
                                小学评估报告上非学业部分的成绩有四个级别水平，反映了学生表现出非学业行为或技能的频率，它们分别是：很少、有时、经常、总是。下面的表格提供了各个水平的详细描述：
                                </p>
                                <table  class='table  table-bordered Annotation' >
                                    <tr>
                                        <th width='70'>水平</th>
                                        <th>说明</th>
                                    </tr>
                                    <tr>
                                        <th>C</th>
                                        <td>
                                            <p class='flex'>
                                                <strong class='flexWidth'>总是：</strong>
                                                <span class='flex1'>表明学生总是（76%-100%的情况下）表现是希望的行为。</span>
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>O</th>
                                        <td>
                                            <p class='flex'>
                                                <strong class='flexWidth'>经常：</strong>
                                                <span class='flex1'>表明学生经常（51%-75%的情况下）表现出希望的行为。</span>
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>S</th>
                                        <td>
                                            <p class='flex'>
                                                <strong class='flexWidth'>有时：</strong>
                                                <span class='flex1'>表明学生有时（26%-50%的情况下）表现出希望的行为。</span>
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>R</th>
                                        <td>
                                            <p class='flex'>
                                                <strong class='flexWidth'>很少：</strong>
                                                <span class='flex1'>表明学生很少（0%-25%的情况下）表现出希望的行为。</span>
                                            </p>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    <?php else:?>
                        <div class="en color3 justify">
                            <div class='pt20 page-break-after'>
                                <div class='font14 mt10'><strong>Explanation of the Progress Report</strong></div>
                                <p>The primary goal of Daystar Academy is to enable each student to learn and achieve academic success and develop effective interpersonal skills. The purpose of the Progress Report is to give more information about each child’s progress in relation to the grade-level standards. Each grade-level assesses students on the most significant standards in the areas of language arts, social studies, and science.</p>
                                <div class='relative pl10 mt20'>
                                    <strong>
                                    <span class='piont'></span> Academic
                                    </strong>
                                </div>
                                <p>Academic scores on elementary school reports are recorded on a four-level scale that reflects students’ proficiency: novice, learner, proficient, and exemplary. The following chart provides a more detailed description of each level:</p>
                                <table  class='table  table-bordered Annotation' >
                                    <tr>
                                        <th width='55'>Level</th>
                                        <th>Description</th>
                                    </tr>
                                    <tr>
                                        <th>E</th>
                                        <td>
                                            <p class='flex'>
                                                <strong class='flexWidth'>Exemplary ：</strong>
                                                <span class='flex1'>The student demonstrates mastery of the grade-level standards with ease and consistency, and often exceeds the cognitive level of the standard. The student applies and extends the key concepts, processes and skills of the grade level. </span>
                                            </p>
                                            <p class='flex'>
                                                <strong class='flexWidth'>Expert ：</strong>
                                                <span class='flex1'>Student can show others how to use the skill and accurately assess how effectively the skill is used (self-regulation)</span>
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>P</th>
                                        <td>
                                            <p class='flex'>
                                                <strong class='flexWidth'>Proficient ：</strong>
                                                <span class='flex1'>The student demonstrates mastery of grade-level standards at the cognitive level the standard is written. The student consistently grasps and applies the key concepts, processes and skills with limited errors.</span>
                                            </p>
                                            <p class='flex'>
                                                <strong class='flexWidth'>Practitioner：</strong>
                                                <span class='flex1'>Students employ the skill confidently and effectively  (demonstration)</span>
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>L</th>
                                        <td>
                                            <p class='flex'>
                                                <strong class='flexWidth'>Learner：</strong>
                                                <span class='flex1'>The student demonstrates mastery of some grade-level standards. The student inconsistently grasps and applies the key concepts, processes and skills with significant errors.</span>
                                            </p>
                                            <p class='flex'>
                                                <strong class='flexWidth'>Learner：</strong>
                                                <span class='flex1'>Students copy others who use the skill and use the skill with scaffolding and guidance (emulation)</span>
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>N</th>
                                        <td>
                                            <p class='flex'>
                                                <strong class='flexWidth'>Novice：</strong>
                                                <span class='flex1'>The student has minimal understanding and does not meet grade-level standards. Performance is inconsistent, even with guidance and support.</span>
                                            </p>
                                            <p class='flex'>
                                                <strong class='flexWidth'>Novice：</strong>
                                                <span class='flex1'>Students are introduced to the skill, and can watch others performing it  (observation)</span>
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>N/A</th>
                                        <td>
                                            <p class='flex'>
                                                <strong class='flexWidth'>Not Assessed：</strong>
                                                <span class='flex1'>The standards or areas of learning have not been assessed at this time.</span>
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>N/E</th>
                                        <td>
                                            <p class='flex'>
                                                <strong class='flexWidth'>No Evidence：</strong>
                                                <span class='flex1'>Insufficient evidence of learning exists to make a fair evaluation of student performance against expectations.</span>
                                            </p>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class='pt20 page-break-after pb20'>
                                <div class='relative pl10 mt10'>
                                    <strong>
                                    <span class='piont'></span> Non-Academic
                                    </strong>
                                </div>
                                <p>
                                Non-academic scores on elementary school reports are recorded on a four-level scale that reflects the frequency of which students demonstrate non-academic behaviors or skills: rarely, sometimes, often, and consistently. The following chart provides a more detailed description of each level:
                                </p>
                                <table  class='table  table-bordered Annotation' >
                                    <tr>
                                        <th width='70'>Level</th>
                                        <th>Description</th>
                                    </tr>
                                    <tr>
                                        <th>C</th>
                                        <td>
                                            <p class='flex'>
                                                <strong class='flexWidth'>Consistently: </strong>
                                                <span class='flex1'>Indicates that the student consistently (76%-100% of the times) displays the desired behaviors.</span>
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>O</th>
                                        <td>
                                            <p class='flex'>
                                                <strong class='flexWidth'>Often: </strong>
                                                <span class='flex1'>Indicates that the student often (51%-75% of the times) displays the desired behaviors.</span>
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>S</th>
                                        <td>
                                            <p class='flex'>
                                                <strong class='flexWidth'>Sometimes: </strong>
                                                <span class='flex1'>Indicates that the student occasionally (26%-50% of the time) displays the desired behaviors.</span>
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>R</th>
                                        <td>
                                             <p class='flex'>
                                                <strong class='flexWidth'>Rarely: </strong>
                                                <span class='flex1'>Indicates that the student seldom (0%-25% of the time) displays the desired behaviors.</span>
                                            </p>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    <?php endif;?>
                <?php endif;?>
                <!-- <div style="height: 20px;"></div> -->
            </div>
            <!-- page content -->
            <!-- 结尾 -->
        </div>
    </div>


    <style>
        .pb20{
            padding-bottom:20px
        }
        .mt10{
            margin-top:10px
        }
        .mt30{
            margin-top:30px
        }
        .mt20{
            margin-top:20px
        }
        .pt20{
            padding-top:20px
        }
        .font14{
            font-size:14px
        }
        .flex{
            display: flex;
            /* line-height: 22px; */
            height: 100%;
            position: relative;
        }
        .flex1{
            padding-left: 60px;
            flex:1;
            height:100%;
            display:block
        }
        .flexWidth{
            display: block;
            height: 100%;
            position: absolute;
        }
        .en .flex1{
            padding-left: 90px;
        }
        .Annotation tr th{
           text-align:center !important;
           vertical-align: middle !important;
           background-color:#eeeeee !important;
           text-align: justify;
        }
        .justify{
            text-align: justify;
        }
        .color3{
            color:#333
        }
        .relative{
            position: relative;
        }
        .piont{
            width: 5px;
            height: 5px;
            background: #666666;
            display: inline-block;
            border-radius: 50%;
            position: absolute;
            top: 6px;
            left: 0;
        }
        .pl10{
            padding-left:10px
        }
        .Annotation{
            margin-top:20px
        }
        .en{font-family: Tahoma,'Trebuchet','Utopia';}
        .cn{font-family:'Microsoft Yahei'}
        .report-page{
            min-height:877px;
            position: relative;
            //background: url('http://m1.files.ivykids.cn/images/sreport/header.gif') no-repeat;
            background-size: 100%;
            background-color: #ffffff;
        }
        .clear{
            clear:both;
        }
        .report-wrapper{width:620px; margin: 0px auto; padding: 0;}
        .logo-top-center{
            width: 160px;
            height: 158px;
            background: url('https://m2.files.ivykids.cn/cloud01-file-8035133FueFp-X_Mzk0IJPxInAZ_vikTFWW.png') no-repeat;
            background-size: 100%;
            position: absolute;
            left: 225px;
            top: 0;
        }
        .top-title{
            width: 620px;
            height: 60px;
            position: absolute;
            top: 160px;
            color: #000;
            font-size: 16px;
            text-align: center;
        }
        .top-small-title{
            width: 260px;
            height: 60px;
            position: absolute;
            top: 38px;
            left: 180px;
            color: #000;
            font-size: 14px;
            text-align: center;
            font-weight: bolder;
        }
        .top-school{
            width: 220px;
            height: 60px;
            position: absolute;
            top: 160px;
            left: 200px;
            font-size: 16px;
            text-align: center;
        }
        .logo-contact-left{
            width: 110px;
            height: 104px;
            background: url('https://m2.files.ivykids.cn/cloud01-file-8035133FueFp-X_Mzk0IJPxInAZ_vikTFWW.png') no-repeat;
            background-size: 100%;
            position: absolute;
            left: 14px;
            top: 5px;
        }
        .page-content{
            margin: 0 auto;position: absolute;top: 125px; width: 640px;
            position: relative;
        }

        .pull-content{
            margin: 0 auto;
            width: 580px;
        }

        .page-first-content{
            margin: 0 60px;position: absolute;top: 90px; width: 500px;
            position: relative;
        }
        .page-first-content .profile-photo{
            position: absolute;
            right: 0;
            top: 170px;
            width: 150px;
        }

        .page-first-content .profile-box{
            padding-top: 150px;
        }

        .page-first-content .profile-box dl{line-height: 40px;}
        .page-first-content .profile-box dt{float: left; width: 90px; clear: left;color: #999;text-align: right}
        .page-first-content .profile-box dt:after{content: ': '}
        .page-first-content .profile-box dd{margin-left: 100px;border-bottom: 1px dotted #efefef;}
        .page-first-content .profile-box dd:before{display: table}
        .page-first-content .profile-box dd:after{clear: both;}
        .teacher-list span.item{display: block; text-align: left;color: #666666;line-height:30px}
        .teacher-list-a span.item{width: 310px;display: block; }
        .teacher-list-b span.item{width: 110px;float: left; height: 25px;}

        .page-content .cover-bottom{
            position: absolute;
            width: 100%;
            top: 395px;
        }

        span.ld{
            color: #000;
            font-size: 110%;
            padding: 1px 3px;
            margin-right: 4px;
            background: #efefef;
        }

        span.ld:after{
        }

        hr.sep{
            margin: 5em 0 3em;
            border: 0;
            height: 1px;
            background: #333;
            background-image: -webkit-linear-gradient(left, #efefef, #ccc, #efefef);
            background-image:    -moz-linear-gradient(left, #efefef, #999, #ccc);
            background-image:     -ms-linear-gradient(left, #efefef, #999, #ccc);
            background-image:      -o-linear-gradient(left, #efefef, #999, #ccc);
        }

        ul.mission li{line-height: 1.5em;}
        /*.web-view .report-page{margin: 1em 0;}*/

        .tac{text-align: center}
        .page-break-after{page-break-after: always;}
        img.middle-size{
            max-width: 360px;
            max-height: 272px;
            margin: 10px auto;
            display: block;
            border: 4px solid #efefef;
        }
        body {
            font-family: Utopia, "Trebuchet MS", Arial, "Microsoft Yahei", "微软雅黑", STXihei, "华文细黑", sans-serif;
            font-size: 10px;
            line-height: 1.42857143;
            color: #333333;
            margin: 0;
            padding: 0;
            background-color: #efefef;
        }

        @media print {
            /**.web-view .report-page {background: none;}**/
            /*.web-view .report-page{margin: 0;}*/
        }
        .navbar-inverse {
            background-color: #333333;
            border-color: #efefef;
        }
        .navbar-inverse form {
            line-height: 50px;
            height: 50px;
            text-align: center;
        }
        .navbar-inverse form input {
            margin-top: 13px;
        }
        .navbar-fixed-top {
            top: 0;
            position: fixed;
            right: 0;
            left: 0;
            z-index: 1030;
            -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
        }
        .navbar-inverse a{
            color: #f2f2f2;
        }

        .table td,
        .table th {
            background-color: #fff !important;
        }
        .table {
            border-collapse: collapse !important;
        }
        .table-bordered th,
        .table-bordered td {
            border: 1px solid #ddd !important;
        }
        table {
            background-color: transparent;
        }
        th {
            text-align: left;
        }
        .table {
            width: 100%;
            max-width: 100%;
            margin-bottom: 17px;
        }
        .table > thead > tr > th,
        .table > tbody > tr > th,
        .table > tfoot > tr > th,
        .table > thead > tr > td,
        .table > tbody > tr > td,
        .table > tfoot > tr > td {
            padding: 8px;
            line-height: 1.42857143;
            vertical-align: top;
            border-top: 1px solid #dddddd;
        }
        .table > thead > tr > th {
            vertical-align: bottom;
            border-bottom: 2px solid #dddddd;
        }
        .table > caption + thead > tr:first-child > th,
        .table > colgroup + thead > tr:first-child > th,
        .table > thead:first-child > tr:first-child > th,
        .table > caption + thead > tr:first-child > td,
        .table > colgroup + thead > tr:first-child > td,
        .table > thead:first-child > tr:first-child > td {
            border-top: 0;
        }
        .table > tbody + tbody {
            border-top: 2px solid #dddddd;
        }
        .table-bordered {
            border: 2px solid #dddddd;
        }
        .table-bordered > thead > tr > th,
        .table-bordered > tbody > tr > th,
        .table-bordered > tfoot > tr > th,
        .table-bordered > thead > tr > td,
        .table-bordered > tbody > tr > td,
        .table-bordered > tfoot > tr > td {
            border: 1px solid #dddddd;
        }
        .table-bordered > thead > tr > th,
        .table-bordered > thead > tr > td {
            border-bottom-width: 2px !important;
            background-color: #eeeeee !important;
        }
        .table-striped > tbody > tr:nth-child(odd) > td,
        .table-striped > tbody > tr:nth-child(odd) > th {
            background-color: #f9f9f9;
        }
        .table-hover > tbody > tr:hover > td,
        .table-hover > tbody > tr:hover > th {
            background-color: #f5f5f5;
        }
        table col[class*="col-"] {
            position: static;
            float: none;
            display: table-column;
        }
        table td[class*="col-"],
        table th[class*="col-"] {
            position: static;
            float: none;
            display: table-cell;
        }
        .table > thead > tr > td.active,
        .table > tbody > tr > td.active,
        .table > tfoot > tr > td.active,
        .table > thead > tr > th.active,
        .table > tbody > tr > th.active,
        .table > tfoot > tr > th.active,
        .table > thead > tr.active > td,
        .table > tbody > tr.active > td,
        .table > tfoot > tr.active > td,
        .table > thead > tr.active > th,
        .table > tbody > tr.active > th,
        .table > tfoot > tr.active > th {
            background-color: #f5f5f5;
        }
    </style>
</body>
</html>
