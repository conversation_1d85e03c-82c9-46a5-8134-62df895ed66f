<?php

class GuideItem{
    //欢迎页
    public static function welcomeGuide($next = null, $prev = null){
        $guideItem = array(
            'selector'=>'#guide-icon',
            'caption'=>'welcome',
            'style'=>array(
                'arrow'=>array(
                    'direction'=>'upright',
                    'top'=>0,
                    'left'=>-70,
                ),
                'mainbox'=>array(
                    'leftOffset'=>217, //guide窗口与主窗口(.page) 左侧的距离
                    'top'=>50,
                ),				
            ),
            'next'=>$next,
            'prev'=>$prev,
        );
        
        if (is_null($next))
            unset($guideItem['next']);
        if (is_null($prev))
            unset($guideItem['prev']);
        
        return $guideItem;
    
    }
    
    public static function childSelectorGuide($next = null, $prev = null){
        $guideItem = array(
            'selector'=>'#child-selector',
            'caption'=>'profileSelector',
            'style'=>array(
                'arrow'=>array(
                    'direction'=>'upleft',
                    'top'=>0,
                    'left'=>270,
                ),
                'mainbox'=>array(
                    'leftOffset'=>100, 
                    'top'=>50,
                ),				
            ),				
            'jsrun' => 'showProfileHover();',
            'next'=>$next,
            'prev'=>$prev,
        );
        
        if (is_null($next))
            unset($guideItem['next']);
        if (is_null($prev))
            unset($guideItem['prev']);
        
        return $guideItem;
    
    }
    
    public static function navGuide($next = null, $prev = null, $active=false, $tpl='navigations', $leftOffset=100)
    {
        if($active==null){
            $selector = '#navigation';
            $left = 70;
        }else{
            $selector = '#navigation li.active';
            $left = 10;
        }
        $guideItem = array(
            'selector'=>$selector,
            'caption'=>$tpl,
            'style'=>array(
                'arrow'=>array(
                    'direction'=>'up',
                    'top'=>0,
                    'left'=>$left,
                ),
                'mainbox'=>array(
                    'leftOffset'=>$leftOffset, 
                    'top'=>50,
                ),				
            ),
            'next'=>$next,
            'prev'=>$prev,
        );
        
        if (is_null($next))
            unset($guideItem['next']);
        if (is_null($prev))
            unset($guideItem['prev']);
        
        return $guideItem;
    }
    
    public static function subNavGuide($next = null, $prev = null, $tpl=null)
    {
        $guideItem = array(
            'selector'=>'#current-sub-nav',
            'caption'=>$tpl,
            'style'=>array(
                'arrow'=>array(
                    'direction'=>'upleft',
                    'top'=>0,
                    'left'=>200,
                ),
                'mainbox'=>array(
                    'leftOffset'=>100, 
                    'top'=>50,
                ),				
            ),
            'next'=>$next,
            'prev'=>$prev,
        );
        
        if (is_null($next))
            unset($guideItem['next']);
        if (is_null($prev))
            unset($guideItem['prev']);
        
        return $guideItem;
    }
    
    
}