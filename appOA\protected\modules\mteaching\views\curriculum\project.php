<div class="container-fluid">
    <!-- 面包屑导航 -->
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Teaching Tasks'), array('//mteaching/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Curriculum'), array('//mteaching/curriculum/project', 'official'=>'1'))?></li>
        <li class="active"><?php echo Yii::t('curriculum','Theme List');?></li>
    </ol>
    <div class="row">
        <!-- 左侧菜单栏 -->
        <div class="col-md-2">
            <div class="list-group" id="classroom-status-list">
                <?php foreach ($this->leftMenu as $key => $value) { ?>
                <a href="<?php echo ($value[0] == 'project') ? $this->createUrl($value[0], array('official' =>1 ))  : $this->createUrl($value[0]);?>" class="list-group-item status-filter <?php echo $this->getAction()->getId()==$value[0]?'active':''; ?>"><?php echo $value[1]; ?></a></li>
                <?php } ?>
            </div>
        </div>
        <div class="col-md-10">
            <?php if (Yii::app()->user->checkAccess('o_E_Edu_Common')) { ?>
            <div class="mb10">
                <a class="btn btn-primary" href="<?php echo $this->createUrl('editproject'); ?>"><span class="glyphicon glyphicon-plus"></span> <?php echo Yii::t('curriculum', 'Add a new theme');?></a>
            </div>
            <?php } ?>
            <div class="btn-group mb10">
                <!-- 搜索框 -->
                <form action="<?php $this->createUrl('editproject'); ?>" method="get" class="row">
                <!-- 年龄 -->
                <div class="col-sm-2">
                <select name="age" class="form-control">
                    <option value=""><?php echo Yii::t('curriculum','Any Age'); ?></option>
                    <?php foreach ($diglossia['age'] as $key => $value) {
                        echo "<option value='$key'";
                        echo Yii::app()->request->getParam('age','')==$key?'selected':'';
                        echo ">$value</option>";
                    } ?>
                </select> 
                </div>
                <!-- 季节 -->
                <div class="col-sm-2">
                <select name="season" class="form-control">
                    <option value=""><?php echo Yii::t('curriculum','All Season'); ?></option>
                    <?php foreach ($diglossia['season'] as $key => $value) {
                        echo "<option value='$key'";
                        echo Yii::app()->request->getParam('season','')==$key?'selected':'';
                        echo ">$value</option>";
                    } ?>
                </select>
                </div>
                <!-- 是否为教育部门 -->
                <div class="col-sm-2">
                <select name="official" class="form-control">
                    <?php foreach ($diglossia['official'] as $key => $value) {
                        echo "<option value='$key'";
                        echo Yii::app()->request->getParam('official',3)==$key?'selected':'';
                        echo ">$value</option>";
                    } ?>
                </select>
                </div>
                <!-- 内容匹配 -->
                <div class="col-sm-3">
                    <div class="input-group">
                        <input type="text" class="form-control" name="search" value="<?php echo Yii::app()->request->getParam('search','')?Yii::app()->request->getParam('search',''):''; ?>">
                        <span class="input-group-btn">
                        <button class="btn btn-default" type="submit"><span class="glyphicon glyphicon-search"> </span> </button>
                        </span>
                    </div>
                </div>
                </form>
            </div>
            <div class="panel panel-default">
                <div class="panel-body">
                    <?php $this->widget('ext.ivyCGridView.BsCGridView', array(
                        'id'=>'points-product-grid',
                        'dataProvider'=>$projects,
                        'colgroups'=>array(
                            array(
                                "colwidth"=>array(50,50,50,80),
                            )
                        ),
                        'columns'=>array(
                            array(
                            'name'=>'cn_title',
                            'value'=>'$data->cn_title',
                            ),
                            'en_title',
                            array(
                            'name'=>Yii::t('curriculum','Public'),
                            'value'=>array($this,'getUserName'),
                            ),
                            array(
                            'name'=>Yii::t('curriculum','Operation'),
                            'value'=>array($this,'getProButton'),
                            ),
                        ),
                        )); 
                    ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    //删除项目
    function deletePro (deletebtn,pid) {
        if (confirm('确定要删除吗？')) {
            $.ajax( {    
                url:'<?php echo $this->createUrl('deleteProject'); ?>',    
                data:{pid : pid},    
                type:'post',    
                dataType:'json',    
                success:function(data) {    
                    if(data.msg == 'success'){    
                        $(deletebtn).parent().parent().remove();
                        // window.location.reload();    
                    }else{    
                        alert(data.msg);
                        console.log(data);    
                    }    
                 },    
                error : function() {    
                    console.log("异常！");    
                }    
            });  
        }
    }
    //添加收藏
    function collect (collectBtn,pid) {
        if($(collectBtn).text() != '<?php echo Yii::t('curriculum', 'Collected')?>'){
            $.ajax({
                url:'<?php echo $this->createUrl('collect'); ?>',
                data:{pid : pid},
                type:'post',
                dataType:'json',
                success : function (data) {
                    if (data.msg == 'success') {
                        $(collectBtn).text('<?php echo Yii::t('curriculum', 'Collected')?>');
                    }
                    console.log(data.info);
                },
                error : function (data) {
                    
                }
            });
        }else{
            $.ajax({
                url:'<?php echo $this->createUrl('removeCollect'); ?>',
                data:{pid : pid},
                type:'post',
                dataType:'json',
                success : function (data) {
                    if (data.msg == 'success') {
                        $(collectBtn).html('<?php echo Yii::t('curriculum', 'Collect')?>');
                    }
                },
                error : function (data) {
                    
                }
            });
        }
    }
</script>
