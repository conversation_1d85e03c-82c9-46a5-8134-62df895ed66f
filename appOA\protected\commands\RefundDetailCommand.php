<?php
class RefundDetailCommand extends CConsoleCommand{
    public function init()
    {
        Yii::import('common.models.child.*');
        Yii::import('common.models.*');
    }

    public function actionRefundDetailOne($school)
    {
        $file = fopen('./teshuxin.csv', 'r+');
//        $bankinfo = BranchVar::model()->bankInfo($school);
        $csv = $this->_t('孩子ID').",";
        $csv .= $this->_t('孩子姓名').",";
        $csv .= $this->_t('年级').",";
        $csv .= $this->_t('失败原因')."\r\n";
        $i = 1;
        Yii::app()->language = 'zh_cn';
        while ($data = fgetcsv($file)) {
            $parent = array();
            $criteria = new CDbCriteria();
            $criteria->compare('t.childid', $data[0]);
            $childModel = ChildProfileBasic::model()->find($criteria);
            if($childModel) {
                //$parentData = $childModel->getParents();
                $parentEmail = array();
//                foreach ($parentData as $val){
//                    /*$pattern = '/^[a-z0-9]+([._-][a-z0-9]+)*@([0-9a-z]+\.[a-z]{2,14}(\.[a-z]{2})?)$/i';
//                    if(preg_match($pattern,$val->email)){
//                        $parentEmail[] = $val->email;
//                    }*/
//                    if($val->email && strpos($val->email,'@')!==false){
//                        $parentEmail[] = $val->email;
//                    }
//                }


                $parentEmail = array($data[12],$data[13]);
                if($parentEmail){
                    Yii::app()->language = 'zh_cn';
                    $childData = array(
                        'childNameCn' => $childModel->getChildName(),
                        'childNameEn' => $childModel->getChildName(),
                        'childClass' => $data[2],
                        'schoolName' => $school == 'BJ_DS' ? 'DS (启明星)' : 'SLT (三里屯)',
                        'schoolid' => $school,
                        'a' => (intval($data[3])) ? number_format($data[3],2) : '0.00',
                        'a1' => (intval($data[4])) ? number_format($data[4],2) : '0.00',
                        'a2' => (intval($data[5])) ? number_format($data[5],2) : '0.00',
                        'a3' => (intval($data[6])) ? number_format($data[6],2) : '0.00',
                        'b' => (intval($data[7])) ? number_format($data[7],2) : '0.00',
                        'c' => (intval($data[8])) ? number_format($data[8],2) : '0.00',
                        'c1' => (intval($data[9])) ? number_format($data[9],2) : '0.00',
                        'c2' => (intval($data[10])) ? number_format($data[10],2) : '0.00',
                        'd' => (intval($data[11])) ? number_format($data[11],2) : '0.00',
                    );

                    $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
                        // 发件人邮箱
                    foreach($parentEmail as $k=>$val){
                        if(!empty($val)) {
                            $mailer->AddAddress($val);
                        }
                    }

                    $bcc = array('<EMAIL>','<EMAIL>');
                     foreach ($bcc as $key => $Bccemail) {
                         $mailer->AddBcc($Bccemail);
                     }
                    $mailer->Subject = '财务信息 Financial Information';
                    @$mailer->getCommandView('detailsMailOne', array(
                        'childData' => $childData,
                        'bankinfo' => array(),
                    ), $school == 'BJ_DS' ? 'todsparent' : 'main');
                    $isProduction = defined("IS_PRODUCTION") ? IS_PRODUCTION : false;
                    //$isProduction = true;
                    $mailer->iniMail($isProduction);
                    if ($mailer->Send()) {
                        echo $i . ' - ' . $data[0] . ' - Success' . "\n";
                        sleep(1);
                    } else {
                        $csv .= $this->_t($data[0]).",";
                        $csv .= $this->_t($data[1]).",";
                        $csv .= $this->_t($data[3])."\r\n";
                        echo  $i . ' - ' . $data[0] . ' - Fail - ' . $mailer->ErrorInfo ."\n";
                    }
                }else{
                    $csv .= $this->_t($data[0]).",";
                    $csv .= $this->_t($data[1]).",";
                    $csv .= $this->_t($data[3]).",";
                    $csv .= $this->_t('孩子父母邮件不对')."\r\n";
                    echo $i . ' - ' . $data[0] . ' - Fail - No ParentEmail' . "\n";
                }
            }else{
                $csv .= $this->_t($data[0]).",";
                $csv .= $this->_t($data[1]).",";
                $csv .= $this->_t($data[3]).",";
                $csv .= $this->_t('未找到孩子')."\r\n";
                echo $i . ' - ' . $data[0] . ' - Fail - No Child' . "\n";
            }
            $i++;
        }
        file_put_contents('fail'.'.csv', $csv);
    }

    public function actionRefundDetailTwo($school)
    {
        $file = fopen('./billTwo-two.csv', 'r+');
//        $bankinfo = BranchVar::model()->bankInfo($school);
        $csv = $this->_t('孩子ID').",";
        $csv .= $this->_t('孩子姓名').",";
        $csv .= $this->_t('年级').",";
        $csv .= $this->_t('失败原因')."\r\n";
        $i = 1;
        while ($data = fgetcsv($file)) {
            $parent = array();
            $childModel = ChildProfileBasic::model()->findByPk($data[0]);
            if($childModel) {
                $parentData = $childModel->getParents();
                $parentEmail = array();
                foreach ($parentData as $val){
                    //$pattern = '/^[a-z0-9]+([._-][a-z0-9]+)*@([0-9a-z]+\.[a-z]{2,14}(\.[a-z]{2})?)$/i';
                    //if(preg_match($pattern,$val->email)){
                    if(strpos($val->email,'@')!==false){
                        $parentEmail[] = $val->email;
                    }
                }
                if($parentEmail){
                    $childData = array(
                        'childNameCn' => iconv("GBK", "UTF-8", $data[1]),
                        'childNameEn' => $data[2],
                        'childClass' => $data[3],
                        'discountCn' => iconv("GBK", "UTF-8", $data[4]),
                        'discountEn' => $data[5],
                        'schoolName' => $school == 'BJ_DS' ? 'DS (启明星)' : 'SLT (三里屯)',
                        'schoolid' => $school,
                        'a' => (intval($data[6])) ? number_format($data[6],2) : '0.00',
                        'b' => (intval($data[7])) ? number_format($data[7],2) : '0.00',
                        'c' => (intval($data[8])) ? number_format($data[8],2) : '0.00',
                        'd' => (intval($data[9])) ? number_format($data[9],2) : '0.00',
                    );

                    $mailer = Yii::createComponent('common.extensions.mailer.EMailer');

                    foreach($parentEmail as $k=>$val){
                        $mailer->AddAddress($val);
                    }
                    $bcc = array('<EMAIL>','<EMAIL>','<EMAIL>');
                    foreach ($bcc as $key => $Bccemail) {
                        $mailer->AddBcc($Bccemail);
                    }
                    $mailer->Subject = '财务信息 Financial Information';
                    @$mailer->getCommandView('detailsMail', array(
                        'childData' => $childData,
                        'bankinfo' => array(),
                    ), $school == 'BJ_DS' ? 'todsparent' : 'main');
                    //$isProduction = defined("IS_PRODUCTION") ? IS_PRODUCTION : false;
                    $isProduction = true;
                    $mailer->iniMail($isProduction);
                    if ($mailer->Send()) {
                        echo $i . ' - ' . $data[0] . ' - Success' . "\n";
                        sleep(1);
                    } else {
                        $csv .= $this->_t($data[0]).",";
                        $csv .= $this->_t($data[1]).",";
                        $csv .= $this->_t($data[3])."\r\n";
                        echo  $i . ' - ' . $data[0] . ' - Fail - ' . $mailer->ErrorInfo ."\n";
                    }
                }else{
                    $csv .= $this->_t($data[0]).",";
                    $csv .= $this->_t($data[1]).",";
                    $csv .= $this->_t($data[3]).",";
                    $csv .= $this->_t('孩子父母邮件不对')."\r\n";
                    echo  $i . ' - ' . $data[0] . ' - Fail - No ParentEmail' . "\n";
                }
            }else{
                $csv .= $this->_t($data[0]).",";
                $csv .= $this->_t($data[1]).",";
                $csv .= $this->_t($data[3]).",";
                $csv .= $this->_t('未找到孩子')."\r\n";
                echo  $i . ' - ' . $data[0] . ' - Fail - No Child' . "\n";
            }
            $i++;
        }
        file_put_contents('fail-two'.'.csv', $csv);
    }

    public function _t($str='')
    {
        $str = str_replace(',', '，', $str);
        return iconv("UTF-8", "GBK", $str);
    }

    // payment_type = 'bus'
    // title = '疫情退费'
    // fee_other_id = 0
    // schoolid = 'BJ_IASLT'
    // status = 20
    // inout=out
    public function actionSaveFeeOtherId($schoolid)
    {
        Yii::import('common.models.invoice.*');
        $criteria = new CDbCriteria();
        $criteria->compare('t.payment_type', 'bus');
        $criteria->compare('t.title', '疫情退费');
        $criteria->compare('t.fee_other_id', 0);
        $criteria->compare('t.schoolid', $schoolid);
        $criteria->compare('t.status', 20);
        $criteria->compare('t.inout', 'out');
        $invoiceModel = Invoice::model()->findAll($criteria);

        $i = 1;
        if($invoiceModel) {
            foreach ($invoiceModel as $val) {
                $criteria = new CDbCriteria();
                $criteria->compare('t.invoice_id', $val->invoice_id);
                $invoiceChildRefundModel = InvoiceChildRefund::model()->find($criteria);
                if ($invoiceChildRefundModel && $invoiceChildRefundModel->Invoice) {
                    $val->fee_other_id = $invoiceChildRefundModel->Invoice->fee_other_id;
                    $val->fee_program = $invoiceChildRefundModel->Invoice->fee_program;
                    if ($val->save()) {
                        echo $i . ' - ' . $val->invoice_id . ' - success' . "\n";
                        $i++;
                    }
                } else {
                    echo $i . ' - ' . $val->invoice_id . ' - fail' . "\n";
                    $i++;
                }
            }
        }else{
            echo 'Null' . "\n";
        }
    }


    // $classTitle  文件名称
    // classid  班级ID
    // 根据Excel 增加2020.08 月份的餐费账单
    public function actionSaveInvoice()
    {
        $file = fopen('./HH_202008_lunce_in.csv', 'r+');
        $i = 1;
        $message = 'success';
        $csv = $this->_t('孩子ID').",";
        $csv .= $this->_t('孩子姓名').",";
        $csv .= $this->_t('金额').",";
        $csv .= $this->_t('失败原因')."\r\n";
        Yii::import('common.models.invoice.*');
        while ($data = fgetcsv($file)) {
            $criteria = new CDbCriteria();
            $criteria->compare('t.calendar_id', 125);
            $criteria->compare('t.payment_type', 'lunch');
            $criteria->compare('t.schoolid', 'NB_HH');
            $criteria->compare('t.childid', $data[0]);
            $criteria->compare('t.status', 20);
            $criteria->compare('t.inout', 'in');
            $criteria->order = 'invoice_id DESC';
            $criteria->limit = 1;
            $invoiceModel = Invoice::model()->find($criteria);

            $invoice = new Invoice();
            $invoice->calendar_id  = $invoiceModel->calendar_id;
            $invoice->amount  = $data[2];
            $invoice->original_amount  = $data[2];
            $invoice->nodiscount_amount  = 0;
            $invoice->schoolid  = 'NB_HH';
            $invoice->classid  = $invoiceModel->classid;
            $invoice->childid  = $data[0];
            $invoice->payment_type  = 'lunch';
            $invoice->afterschool_id  = '';
            $invoice->inout  = 'in';
            $invoice->fee_type  = 10;
            $invoice->startdate  = strtotime('2020-08-01');
            $invoice->enddate  = strtotime('2020-08-31');
            $invoice->installment  = '2020-08';
            $invoice->duetime  = strtotime('2020-08-01');
            $invoice->gen_latefees_date  = '';
            $invoice->title  = '2020/08 餐费';
            $invoice->memo  = '8月份餐费账单';
            $invoice->child_service_info  = $invoiceModel->child_service_info;
            $invoice->discount_id  = 0;
            $invoice->agreeddate  = 0;
            $invoice->fee_program  = 'LUNCH_PERDAY';
            $invoice->userid  = 1;
            $invoice->timestamp  = time();
            $invoice->send_timestamp  = 0;
            $invoice->last_paid_timestamp  = 0;
            $invoice->sender  = 0;
            $invoice->status  = 10;
            $invoice->latepay_id  = 0;
            $invoice->fee_other_id  = 0;
            $invoice->receivable_status  = 0;
            $invoice->flag  = 0;
            $invoice->apportion_status  = 0;
            $invoice->admission_id  = '';
            if(!$invoice->save()){
                $error = current($invoice->getErrors());
                $message = $error[0];
                $csv .= $this->_t($data[0]).",";
                $csv .= $this->_t($data[1]).",";
                $csv .= $this->_t($data[2]).",";
                $csv .= $this->_t($message)."\r\n";
            }
            echo $i . ' - '.$invoice->childid . ' - ' . $message . "\n";
            $i++;
        }
        file_put_contents('invoiceError'.'.csv', $csv);
    }

    public function actionCopyingBus()
    {
        Yii::import('common.models.schoolbus.*');
        $criteria = new CDbCriteria();
        $criteria->compare('t.branchid', 'BJ_DS');
        $criteria->compare('t.state', 10);
        $schoolBusModel = SchoolBus::model()->findAll($criteria);

        foreach ($schoolBusModel as $bus){
            $model = new SchoolBus();
            $model->attributes = $bus->attributes;
            $model->branchid = 'BJ_QF';
            if(!$model->save()){
                echo 'fail' . "\n";
            }else{
                echo 'success' . "\n";
            }
        }
    }

    public function actionUpdateChildBus()
    {
        Yii::import('common.models.schoolbus.*');

        $criteria = new CDbCriteria();
        $criteria->compare('t.branchid', 'BJ_DS');
        $criteria->compare('t.state', 10);
        $schoolBusDSModel = SchoolBus::model()->findAll($criteria);

        $criteria = new CDbCriteria();
        $criteria->compare('t.branchid', 'BJ_QF');
        $criteria->compare('t.state', 10);
        $criteria->index = 'bus_title';
        $schoolBusQFModel = SchoolBus::model()->findAll($criteria);
        $busList = array();
        foreach ($schoolBusDSModel as $schoolBus){
            if(isset($schoolBusQFModel[$schoolBus->bus_title])){
                $busList[$schoolBus->bid] = $schoolBusQFModel[$schoolBus->bus_title]->bid;
            }
        }

        $criteria = new CDbCriteria();
        $criteria->compare('t.schoolid', 'BJ_QF');
        $criteria->compare('t.status', "<100");
        $criteria->index = 'childid';
        $childInfo = ChildProfileBasic::model()->findAll($criteria);

        if($childInfo && $busList) {
            $criteria = new CDbCriteria();
            $criteria->compare('t.schoolid', 'BJ_DS');
            $criteria->compare('t.startyear', 2020);
            $criteria->compare('t.childid', array_keys($childInfo));
            $schoolBusChildModel = SchoolBusChild::model()->findAll($criteria);
            if ($schoolBusChildModel) {
                $i = 1;
                foreach ($schoolBusChildModel as $childBus) {
                    $childBus->busid = $busList[$childBus->busid];
                    $childBus->schoolid = 'BJ_QF';
                    if (!$childBus->save()) {
                        echo $i . ' - fail' . "\n";
                    } else {
                        echo $i . ' - success' . "\n";
                    }
                    $i++;
                }
            }
        }
    }


    /*
     * 替换邮件
     *  根据旧的邮件查询，替换成新的邮件
     */
    public function actionSaveEmail()
    {
        $file = fopen('./email_address_list.csv', 'r+');
        $i = 1;
        $csv = $this->_t('老师ID').",";
        $csv .= $this->_t('新工作邮箱').",";
        $csv .= $this->_t('失败原因')."\r\n";
        Yii::import('common.models.invoice.*');
        while ($data = fgetcsv($file)) {
            $userModel = User::model()->findByPk($data[0]);
            if($userModel && in_array($userModel->profile->branch, array("BJ_DS", "BJ_SLT", "BJ_QF", "BJ_QFF"))) {
                $pattern = '/^[a-z0-9]+([._-][a-z0-9]+)*@([0-9a-z]+\.[a-z]{2,14}(\.[a-z]{2})?)$/i';
                if(preg_match($pattern, $data[1])){
                    $userModel->email = strtolower($data[1]);
                    if (!$userModel->save()) {
                        $error = current($userModel->getErrors());
                        $csv .= $this->_t($data[0]) . ",";
                        $csv .= $this->_t($data[1]) . ",";
                        $csv .= $this->_t($error[0]) . "\r\n";
                    }else{
                        echo $i . ' - ' . $userModel->uid . ' - success' . "\n";
                        $i++;
                    }
                }else{
                    $csv .= $this->_t($data[0]) . ",";
                    $csv .= $this->_t($data[1]) . ",";
                    $csv .= $this->_t("新邮箱不是正确邮箱") . "\r\n";
                }
            }else{
                $csv .= $this->_t($data[0]) . ",";
                $csv .= $this->_t($data[1]) . ",";
                $csv .= $this->_t('未找到用户') . "\r\n";
            }
        }
        file_put_contents('email_address_list_error'.'.csv', $csv);
    }

    // 修改午餐退费金额
    public function actionSaveLunceAmount($school,$status = 1)
    {
        Yii::import('common.models.invoice.*');

        $criteria = new CDbCriteria();
        $criteria->compare('t.schoolid', $school);
        $criteria->compare('t.amount', 30);
        $criteria->compare('t.operator_uid', "<>0");
        $criteria->addBetweenCondition('t.target_timestamp', 1598889600, 1601481600);
        $lunchModel = RefundLunch::model()->findAll($criteria);
        if(!$lunchModel){
            echo '未找到要修改的数据'."\n";
            return false;
        }
        
        if($status == 1) {
            $childCredit = array();
            foreach ($lunchModel as $value) {
                $childCredit[$value->childid][] = $value->child_credit_id;
            }

            $num = 1;
            foreach ($childCredit as $childid => $val) {
                $affairs = Yii::app()->db->beginTransaction();
                try {
                    $criteria = new CDbCriteria();
                    $criteria->compare('t.cid', $val);
                    $criteria->order = 'cid asc';
                    $childCreditModel = ChildCredit::model()->findAll($criteria);
                    if ($childCreditModel) {
                        $i = 1;
                        $transaction = array();
                        foreach ($childCreditModel as $item) {
                            $str = 5 * $i;
                            $item->amount = 25;
                            $item->balance = $item->balance - $str;
                            if (!$item->save()) {
                                $affairs->rollback();
                                $errs = current($item->getErrors());
                                echo $num . '-fail-' . $childid . '-' . $errs[0] . "\n";
                                return false;
                            }
                            $transaction[$item->transaction_id][] = $item->transaction_id;
                            $i++;
                        }

                        $invoiceTransactionModel = InvoiceTransaction::model()->findAllByPk(array_keys($transaction));
                        if (!$invoiceTransactionModel) {
                            $affairs->rollback();
                            echo $num . '-fail-' . $childid . ' - 未找到退费账单' . "\n";
                            return false;
                        }
                        foreach ($invoiceTransactionModel as $invocieItem){
                            $amount = count($transaction[$invocieItem->id]) * 5;
                            $invocieItem->amount = $invocieItem->amount - $amount;
                            if (!$invocieItem->save()) {
                                $affairs->rollback();
                                $errs = current($invocieItem->getErrors());
                                echo $num . '-fail-' . $childid . '-' . $errs[0] . "\n";
                                return false;
                            }
                            if (!$invocieItem->invoiceInfo) {
                                $affairs->rollback();
                                echo $num . '-fail-' . $childid . ' - 未找到退费账单' . "\n";
                                return false;
                            }

                            $invocieItem->invoiceInfo->amount = $invocieItem->amount;
                            $invocieItem->invoiceInfo->original_amount = $invocieItem->amount;
                            if (!$invocieItem->invoiceInfo->save()) {
                                $affairs->rollback();
                                $errs = current($invocieItem->invoiceInfo->getErrors());
                                echo $num . '-fail-' . $childid . '-' . $errs[0] . "\n";
                                return false;
                            }
                        }


                        $balanceAmount = ChildCredit::model()->getChildCredit($childid);
                        $childModel = ChildProfileBasic::model()->findByPk($childid);
                        $childModel->credit = $balanceAmount;
                        if (!$childModel->save()) {
                            $affairs->rollback();
                            $errs = current($childModel->getErrors());
                            echo $num . '-fail-' . $childid . '-' . $errs[0] . "\n";
                            return false;
                        }
                    }

                    $affairs->commit();
                    echo $num . '-success-' . $childid . '-' . '成功' . "\n";
                } catch (Exception $e) {
                    $affairs->rollback();
                    echo $num . 'fail - 失败' . "\n";
                }
                $num++;
            }
        }else{
            foreach ($lunchModel as $key=>$value) {
                $value->amount = 25;
                if(!$value->save()){
                    $errs = current($value->getErrors());
                    echo $key . '-fail-' . $value->id . '-' . $errs[0] . "\n";
                }else{
                    echo $key . '-success-' . $value->id. "\n";
                }
            }
        }
    }

    public function actionCade()
    {
        $json = array('title' => '邮件模板测试', 'type' => 'visit','data' => '邮件模板测试');
        var_dump(json_encode($json));exit;
        //CREATE TABLE `email_template` (
        //  `id` int(11) NOT NULL AUTO_INCREMENT,
        //  `title` varchar(255) NOT NULL DEFAULT '',
        //  `type` varchar(255) NOT NULL DEFAULT '',
        //  `data` text NOT NULL,
        //  `status` int(10) NOT NULL DEFAULT '0',
        //  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
        //  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
        //  `tenant_id` int(11) NOT NULL DEFAULT '0' COMMENT '租户ID',
        //  PRIMARY KEY (`id`)
        //) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邮件模板';
    }
}
