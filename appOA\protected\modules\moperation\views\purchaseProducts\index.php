<?php
$purchaseProducts = Yii::app()->request->getParam('PurchaseProducts', '');
?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'HQ Operations'), array('default/index')); ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Support'), array('default/index')); ?></li>
        <li class="active"><?php echo Yii::t('site', '采购商品管理'); ?></li>
    </ol>
    <div class="row">
        <div class="col-md-2">
            <div class="list-group" id="classroom-status-list">
                <a href="<?php echo $this->createUrl('purchaseProducts/index'); ?>"
                   class="list-group-item status-filter active">商品管理</a>
                <a href="<?php echo $this->createUrl('purchaseStandard/index'); ?>"
                   class="list-group-item status-filter">标配管理</a>
                <a href="<?php echo $this->createUrl('purchaseOrder/index'); ?>" class="list-group-item status-filter">生成订单</a>
                <a href="<?php echo $this->createUrl('purchaseOrder/orders'); ?>" class="list-group-item status-filter">订单管理</a>
            </div>
        </div>
        <div class="col-md-10">
            <div class="mb10">
                <button class="btn btn-primary" onclick="update(0)"><span class="glyphicon glyphicon-plus"></span> 新建商品
                </button>
            </div>
            <!-- 筛选条件 -->
            <div class="form-inline mb10">
                <form action="<?php echo $this->createUrl('index'); ?>" method="get" class="">
                        <select name="PurchaseProducts[cid]" class="form-control form-group mb5">
                            <option value="">商品分类</option>
                            <?php foreach ($diglossiaArray['cate'] as $key => $value) {
                                echo "<option value='$key'";
                                echo $purchaseProducts['cid'] == $key ? 'selected' : '';
                                echo ">$value</option>";
                            } ?>
                        </select>
                        <select name="PurchaseProducts[aid]" class="form-control form-group mb5">
                            <option value="">摆放区域</option>
                            <?php foreach ($diglossiaArray['area'] as $key => $value) {
                                echo "<option value='$key'";
                                echo $purchaseProducts['aid'] == $key ? 'selected' : '';
                                echo ">$value</option>";
                            } ?>
                        </select>
                    <input class="form-control form-group mb5" placeholder="类型筛选" type="text" name="PurchaseProducts[tid]"
                           value="<?php echo $purchaseProducts['tid'] ?>">
                    <input class="form-control form-group mb5" placeholder="价格筛选" type="text" name="PurchaseProducts[price]"
                           value="<?php echo $purchaseProducts['price'] ?>">
                    <input class="form-control form-group mb5" placeholder="标题筛选" type="text" name="PurchaseProducts[cn_name]"
                           value="<?php echo $purchaseProducts['cn_name'] ?>">
                    <button class="btn btn-default mb5" type="submit"><span class="glyphicon glyphicon-search"> </span>
                    </button>
                </form>
            </div>
            <div class="panel panel-default">
                <div class="panel-body">
                    <?php
                    $this->widget('ext.ivyCGridView.BsCGridView', array(
                        'id' => 'points-product-grid',
                        'afterAjaxUpdate' => 'js:function(){head.Util.ajaxDel()}',
                        'dataProvider' => $model->search(),
                        'colgroups' => array(
                            array(
                                "colwidth" => array(100, 100, 100, 100, 50, 100, 100),
                            )
                        ),
                        'columns' => array(
                            'cn_name' => array(
                                'name' => 'cn_name',
                                'type' => 'raw',
                                'value' => array($this, 'showPic'),
                            ),
                            'cid' => array(
                                'name' => 'cid',
                                'value' => array($this, 'showCate'),
                            ),
                            'aid' => array(
                                'name' => 'aid',
                                'value' => array($this, 'showArea'),
                            ),
                            'tid',
                            'price',
                            // 'model',
                            // 'tip',
                            array(
                                'name' => 'update_timestamp',
                                'value' => 'CommonUtils::formatDateTime($data->update_timestamp, "medium", "short")',
                            ),
                            array(
                                'name' => '操作',
                                'value' => array($this, 'showBtn'),
                            ),
                        ),
                    )); ?>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- 模态框 -->
<div class="modal fade bs-example-modal-lg" id="modal" tabindex="-1" role="dialog" aria-labelledby="modal">
    <div class="modal-dialog  modal-lg"  role="document">
        <div class="modal-content">

        </div>
    </div>
</div>
<!-- script代码 -->
<script>
    //显示编辑界面
    function update(id) {
        $('#modal .modal-content').load('<?php echo $this->createUrl('update') ?>' + '?id=' + id, function () {
            $('#modal').modal({
                show: true,
                backdrop: 'static'
            });
            head.Util.ajaxForm($('#modal'));
        });
    }
    //添加到标配
    function addStandard(pid) {
        $('#modal .modal-content').load('<?php echo $this->createUrl('addStandard') ?>' + '?id=' + pid, function () {
            $('#modal').modal({
                show: true,
                backdrop: 'static'
            });
            head.Util.ajaxForm($('#modal'));
        });
    }

    // 回调：商品添加成功
    function cbProduct() {
        $('#modal').modal('hide');
        $.fn.yiiGridView.update('points-product-grid');
    }
</script>
