<?php

/* *
 * 类名：AlipaySubmit
 * 功能：支付宝各接口请求提交类
 * 详细：构造支付宝各接口表单HTML文本，获取远程HTTP数据
 * 版本：3.2
 * 日期：2011-03-25
 * 说明：
 * 以下代码只是为了方便商户测试而提供的样例代码，商户可以根据自己网站的需要，按照技术文档编写,并非一定要使用该代码。
 * 该代码仅供学习和研究支付宝接口使用，只是提供一个参考。
 */
//require_once("alipayCore.php");

class AlipaySubmit {

    /**
     * 生成要请求给支付宝的参数数组
     * @param $para_temp 请求前的参数数组
     * @param $aliapy_config 基本配置信息数组
     * @return 要请求的参数数组
     */
    function buildRequestPara($para_temp, $aliapy_config) {
        //除去待签名参数数组中的空值和签名参数
        $para_filter = paraFilter($para_temp);

        //对待签名参数数组排序
        $para_sort = argSort($para_filter);

        //生成签名结果
        $mysign = buildMysign($para_sort, trim($aliapy_config['key']), strtoupper(trim($aliapy_config['sign_type'])));

        //签名结果与签名方式加入请求提交参数组中
        $para_sort['sign'] = $mysign;
        $para_sort['sign_type'] = strtoupper(trim($aliapy_config['sign_type']));

        return $para_sort;
    }

    /**
     * 生成要请求给支付宝的参数数组
     * @param $para_temp 请求前的参数数组
     * @param $aliapy_config 基本配置信息数组
     * @return 要请求的参数数组字符串
     */
    function buildRequestParaToString($para_temp, $aliapy_config) {
        //待请求参数数组
        $para = $this->buildRequestPara($para_temp, $aliapy_config);

        //把参数组中所有元素，按照“参数=参数值”的模式用“&”字符拼接成字符串，并对参数值做urlencode编码
        $request_data = createLinkstringUrlencode($para);

        return $request_data;
    }

    /**
     * 构造提交表单HTML数据
     * @param $para_temp 请求参数数组
     * @param $gateway 网关地址
     * @param $method 提交方式。两个值可选：post、get
     * @param $button_name 确认按钮显示文字
     * @return 提交表单HTML文本
     */
    function buildForm($para_temp, $gateway, $method, $button_name, $aliapy_config) {
        //待请求参数数组
        $para = $this->buildRequestPara($para_temp, $aliapy_config);

        $sHtml = "<form id='alipaysubmit' name='alipaysubmit' action='" . $gateway . "_input_charset=" . trim(strtolower($aliapy_config['_input_charset'])) . "' method='" . $method . "'>";
        while (list ($key, $val) = each($para)) {
            $sHtml.= "<input type='hidden' name='" . $key . "' value='" . $val . "'/>";
        }

        //submit按钮控件请不要含有name属性
        $sHtml = $sHtml . "<input type='submit' value='" . $button_name . "'></form>";

        //$sHtml = $sHtml . "<script>document.forms['alipaysubmit'].submit();</script>";

        return $sHtml;
    }

    function buildForm4Yii($para_temp, $gateway, $method, $aliapy_config) {
        //待请求参数数组
        $para = $this->buildRequestPara($para_temp, $aliapy_config);
        $action = $gateway . "_input_charset=" . trim(strtolower($aliapy_config['_input_charset']));
        $sHtml = CHtml::beginForm($action, $method, array(
                    'id' => 'frmOnlinePaySubmit',
                    'name' => 'frmOnlinePaySubmit',
                ));
        while (list ($key, $val) = each($para)) {
            $sHtml.= CHtml::hiddenField($key, $val);
        }
        $sHtml.= CHtml::endForm();
        return $sHtml;
    }

    /**
     * 构造模拟远程HTTP的POST请求，获取支付宝的返回XML处理结果
     * 注意：该功能PHP5环境及以上支持，因此必须服务器、本地电脑中装有支持DOMDocument、SSL的PHP配置环境。建议本地调试时使用PHP开发软件
     * @param $para_temp 请求参数数组
     * @param $gateway 网关地址
     * @param $aliapy_config 基本配置信息数组
     * @return 支付宝返回XML处理结果
     */
    function sendPostInfo($para_temp, $gateway, $aliapy_config) {
        $xml_str = '';

        //待请求参数数组字符串
        $request_data = $this->buildRequestParaToString($para_temp, $aliapy_config);
        //请求的url完整链接
        $url = $gateway . $request_data;
        //远程获取数据
        $xml_data = getHttpResponse($url, trim(strtolower($aliapy_config['input_charset'])));
        //解析XML
        $doc = new DOMDocument();
        $doc->loadXML($xml_data);

        return $doc;
    }

}

?>