<?php $pageTitle =  ($data['id'] == 0) ? Yii::t('newDS', 'Create a notice') : Yii::t('newDS', 'Edit a notice'); ?>
<style>
    [v-cloak] {
        display: none;
    }
    .borderRight {
        border-right: 1px solid #ccc;
        padding-right: 15px
    }
    .inputStyle {
        border: none;
        border-bottom: 1px solid #ccc;
        padding: 0;
        height: 25px;
        line-height: 25px;
        width: 100%;
    }
    .hoursMinutes{
        width:70px;
        float:left
    }
    .absolute{
        position: absolute;
        color: #FF3838;
        top: 12px;
    }
    .flexWidth{
        width:40px
    }
    .lineHeight{
        line-height:31px
    }
    .bgGrey{
        background: #FAFAFA;
        border-radius: 4px;
        padding:10px;
        border: 1px solid #FAFAFA;
    }
    .bgGrey:hover{
        background:rgba(77, 136, 210, 0.1000);
        border: 1px solid #4D88D2;
    }
    .contentAvatar {
        width: 44px;
        height: 44px;
        border-radius: 50%;
        object-fit: cover;
    }
    .pl0{
        padding-left:0
    }
    .selectTeacher{
        width: 100%;
        max-height: 330px;
        position: absolute;
        background: #fff;
        border: 1px solid #E8EAED;
        border-radius: 3px;
        top:35px;
        overflow-y: auto;
        z-index:1;
        left:0px;
        padding:6px 0;
        box-shadow: 0px 0px 6px 0px rgba(0,0,0,0.04), 0px 2px 4px 0px rgba(0,0,0,0.12);
    }
    .searchItem{
        margin-bottom:8px;
        padding:2px 16px;

    }
    .searchItem:hover{
        background-color: #F5F7FA;
        cursor: pointer;
    }
    .imageSearch {
        width: 44px;
        height: 44px;
        object-fit: cover;
    }
    .example{
        width:16px;
        height:16px;
        cursor: pointer;
    }
    .newIcon{
        background: red !important;
        position: absolute !important;
        right: -16px !important;
        top: -10px !important;
        height: 15px !important;
        line-height: 12px !important;
        border-radius: 40px !important;
        color: #fff !important;
        font-size: 12px !important;
        padding: 0 2px !important;
    }
    .tox .tox-tbtn{
        overflow: inherit !important;
    }
    .borderRadio {
        border: 1px solid #dddddd;
        border-radius: 8px;
        display: flex;
        align-items: center;
        padding:12px 16px;
        font-weight: normal;
        transition: border-color 0.3s ease;
    }
    .borderRadio.selected {
        border-color: #428bca; /* 蓝色 */
    }
    .borderRadio img {
        width: 44px;
        height: 44px;
        border-radius: 50%;
        object-fit: cover;
    }
    .width0 {
        width: 0;
    }
    .borderRadio .nowrap {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Teaching Tasks'), array('//mteaching/default/index')) ?></li>
        <li><?php echo Yii::t('newDS', 'Notice') ?></li>
        <li><?php echo $pageTitle; ?></li>
    </ol>
    <div class="row">
        <div class="col-md-2 col-sm-12">
            <div class="list-group" id="classroom-status-list">
                <a href="<?php echo $this->createUrl('index'); ?>" class="list-group-item status-filter"><?php echo Yii::t('newDS', 'Notice List'); ?></a>
                
                <a href="<?php echo $this->createUrl('feedback'); ?>" class="list-group-item status-filter"><?php echo Yii::t("newDS", "Parent Feedback");?></a>
            </div>
        </div>
        <div id='container' v-cloak>
            <div class='col-md-6 col-sm-6 mb20'>
                <div class="panel panel-default">
                    <div class="panel-heading"><?php echo Yii::t("newDS", "Edit a notice");?></div>
                    <div class="panel-body">
                        <form class="form-horizontal">
                            <div class="form-group">
                                <label for="exampleInputEmail1" class="col-sm-2 control-label"><?php echo Yii::t("newDS", "School Year");?></label>
                                <div class="col-sm-10 pt7">
                                    {{startYear}}
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="exampleInputEmail1" class="col-sm-2 control-label"><?php echo Yii::t('newDS', 'Title (Cn)'); ?></label>
                                <div class="col-sm-10">
                                    <input type="input" class="form-control" v-model='editNotice.q_cn' placeholder="<?php echo Yii::t('newDS', 'Title (Cn)'); ?>">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="exampleInputEmail1" class="col-sm-2 control-label"><?php echo Yii::t('newDS', 'Title (En)'); ?></label>
                                <div class="col-sm-10">
                                    <input type="input" class="form-control" v-model='editNotice.q_en' placeholder="<?php echo Yii::t('newDS', 'Title (En)'); ?>">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t('newDS', 'Applicable to'); ?></label>
                                <div class="col-sm-10">
                                    <div  v-if='!showNext'>
                                        <div v-for='(list,id,index) in gradeGroupList'>
                                            <!-- <div><strong> <input type="checkbox" :value="list.key"> {{list.value}}</strong></div> -->
                                            <div class="checkbox"  >
                                                <label >
                                                    <input type="checkbox" :disabled="grades_status"  @change="checkAll(list)" v-model="list.checked" :value="list.key"> {{list.value}}
                                                </label>
                                            </div>
                                            <div class="ml20">
                                                <div class="checkbox-inline" v-for='(item,idx) in list.item' >
                                                    <label >
                                                        <input type="checkbox" :disabled="grades_status"  @change="checkGrades(list)"  v-model='list.checkedList' :value="item.key"> {{item.value}}
                                                    </label>
                                                </div>
                                            </div>  
                                        </div>
                                    </div>
                                    <div v-else>
                                        <select class="form-control length_4 mb10" v-model='applicable' :disabled='grades_status' @change='getNextList()'>
                                            <option value='' disabled><?php echo Yii::t('newDS', 'Please select'); ?></option>
                                            <option value='1'><?php echo Yii::t('newDS', 'Grades of current school year'); ?></option>
                                            <option value='2'><?php echo Yii::t('newDS', 'Student tag of next school year'); ?></option>
                                        </select>   
                                        <div v-if='applicable==2'>
                                            <div v-if='Object.values(markList).length==4' class='flex mt12'>
                                                <div class='flex1 mr8' style='background:#F2F9F2;padding:12px 12px 4px;border-radius: 4px;'>
                                                    <div style='color:#5CB85C' class='font14 fontBold'><?php echo Yii::t('newDS', 'New Students'); ?>：</div>
                                                    <div class="checkbox-inline mr16" v-for='(val, id, index) in markList1'>
                                                        <label >
                                                            <input type="checkbox" :disabled="grades_status" :value="id" v-model='editNotice.child_mark'> {{val}} <span class="badge" style='padding:2px 6px;margin-left:4px'>{{nextData[id]}}</span>
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class='flex1 ml8' style='background:#F1F9FC;padding:12px 12px 4px;border-radius: 4px;'>
                                                    <div style='color:#5BC0DE;' class='font14 fontBold'><?php echo Yii::t('newDS', 'Returning Students'); ?>：</div>
                                                    <div class="checkbox-inline mr16" v-for='(val, id, index) in markList2'>
                                                        <label >
                                                            <input type="checkbox" :disabled="grades_status" :value="id" v-model='editNotice.child_mark'> {{val}} <span class="badge" style='padding:2px 6px;margin-left:4px'>{{nextData[id]}}</span>
                                                        </label>
                                                    </div>
                                                </div>
                                                
                                            </div>
                                            <div v-else>
                                                <div class="checkbox-inline mr10" v-for='(val, id, index) in markList'>
                                                    <label >
                                                        <input type="checkbox" :disabled="grades_status" :value="id" v-model='editNotice.child_mark'> {{val}} <span class="badge">{{nextData[id]}}</span>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div  v-if='applicable==1' >
                                        <div v-for='(list,id,index) in gradeGroupList'>
                                            <!-- <div><strong> <input type="checkbox" :value="list.key"> {{list.value}}</strong></div> -->
                                            <div class="checkbox"  >
                                                <label >
                                                    <input type="checkbox" :disabled="grades_status"  @change="checkAll(list)" v-model="list.checked" :value="list.key"> {{list.value}}
                                                </label>
                                            </div>
                                            <div class="ml20">
                                                <div class="checkbox-inline" v-for='(item,idx) in list.item' >
                                                    <label >
                                                        <input type="checkbox" :disabled="grades_status"  @change="checkGrades(list)"  v-model='list.checkedList' :value="item.key"> {{item.value}}
                                                    </label>
                                                </div>
                                            </div>  
                                        </div>
                                    </div>
                                    </div>
                                </div>
                            </div>
                            <!-- <div class="form-group" >
                                <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t('newDS', '下学年'); ?></label>
                                <div class="col-sm-10">
                                    <div class="checkbox-inline" v-for='(val, id, index) in markList'>
                                        <label >
                                            <input type="checkbox" :disabled="grades_status" :value="id" v-model='editNotice.child_mark'> {{val}}
                                        </label>
                                    </div>
                                </div>
                            </div> -->
                            <div class="form-group" >
                                <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t('newDS', 'Abstract (Cn)'); ?></label>
                                <div class="col-sm-10">
                                    <textarea class="form-control" rows="3" v-model='editNotice.summary_cn'  placeholder="<?php echo Yii::t('newDS', 'Abstract (Cn)'); ?>"></textarea>
                                </div>
                            </div>
                            <div class="form-group" >
                                <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t('newDS', 'Abstract (En)'); ?></label>
                                <div class="col-sm-10">
                                    <textarea class="form-control" rows="3"  v-model='editNotice.summary_en'  placeholder="<?php echo Yii::t('newDS', 'Abstract (En)'); ?>"></textarea>
                                </div>
                            </div>
                            <div class="form-group" >
                                <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t('newDS', 'Level'); ?></label>
                                <div class="col-sm-10">
                                    <div class="radio-inline" v-for='(list,id,index) in levelList'>
                                        <label>
                                            <input type="radio" v-model='editNotice.level' :value="id"> {{list}}
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group" >
                                <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t('newDS', 'Category'); ?></label>
                                <div class="col-sm-10">
                                    <div class="radio-inline" v-for='(list,id,index) in categoryList'>
                                        <label>
                                            <input type="radio" v-model='editNotice.category' :value="list.key"> {{list.value}}
                                        </label>
                                    </div>
                                    <div class="alert alert-warning mt10" role="alert">
                                        <p><?php echo Yii::t("newDS", "Notices can be viewed by category at parent side.");?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="exampleInputEmail1" class="col-sm-2 control-label"><?php echo Yii::t("newDS", "Content (Cn)");?></label>
                                <div class="col-sm-10">
                                    <input id="tinymce" type="textarea" v-model='editNotice.content'>
                                    <div class="alert alert-warning mt20" role="alert">
                                        <p><?php echo Yii::t("newDS", "How to insert a photo or video?");?></p> 
                                        <p>
                                            <?php echo sprintf(Yii::t("newDS", "From Media Repository: Click <a target='_blank'  href='%s'>here</a> to upload photos or videos, then insert files by clicking the gallery icon."), $this->createUrl('/mteaching/weekly/index'));?>
                                            <br>
                                            <?php echo Yii::t("newDS", "Direct Upload: Just drag images to editor area (Images Only).");?>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="exampleInputEmail1" class="col-sm-2 control-label"><?php echo Yii::t("newDS", "Content (En)");?></label>
                                <div class="col-sm-10">
                                    <input id="tinymceEn" type="textarea" v-model='editNotice.content_en'>
                                    <div class="alert alert-warning mt20" role="alert">
                                        <p><?php echo Yii::t("newDS", "How to insert a photo or video?");?></p> 
                                        <p>
                                            <?php echo sprintf(Yii::t("newDS", "From Media Repository: Click <a target='_blank'  href='%s'>here</a> to upload photos or videos, then insert files by clicking the gallery icon."), $this->createUrl('/mteaching/weekly/index'));?>
                                            <br>
                                            <?php echo Yii::t("newDS", "Direct Upload: Just drag images to editor area (Images Only).");?>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group mb20">
                                <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t("newDS", "Comment");?></label>
                                <div class="col-sm-10">
                                    <label class="checkbox-inline">
                                        <input type="checkbox" id="inlineCheckbox1" value="1" v-model='comment_enabled'><?php echo Yii::t("newDS", "Enable");?>
                                    </label>
                                    <div class="alert alert-warning mt10" role="alert"><?php echo Yii::t("newDS", "Parents can make comment regarding this notice.");?></div>
                                    <!-- <p class='pt10'><?php echo Yii::t("newDS", "Parents can make comment regarding this notice.");?></p> -->
                                </div>
                            </div>
                            <!-- <div class="form-group" >
                                <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t("newDS", "Status");?></label>
                                <div class="col-sm-10">
                                    <div class="radio-inline" v-for='(list,id,index) in publishTypeList'>
                                        <label>
                                            <input type="radio" v-model='editNotice.publish_type' :value="id"> {{list}}
                                        </label>
                                    </div>
                                    <div class='mt20' v-show='editNotice.publish_type==10'>
                                        <div class="col-sm-3">
                                            <input type="text" class="form-control form-group"  id="publish_at" v-model='publish_at'  placeholder="<?php echo Yii::t("newDS", "Select a date");?>"  :value='publish_at'>
                                        </div>
                                        <select class="form-control hoursMinutes"   v-model='publish_atHours'>
                                            <option v-for='(list,index) in 25'>{{index < 10?'0'+index:index}}</option>
                                        </select>
                                        <select class="form-control ml5 hoursMinutes"   v-model='publish_atMinutes'>
                                            <option v-for='(list,index) in 61' >{{index < 10?'0'+index:index}}</option>
                                        </select>
                                    </div>
                                </div>
                            </div> -->
                            <!-- <div class="form-group" >
                                <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t("newDS", "Expire at");?></label>
                                <div class="col-sm-10">
                                    <div class="radio-inline">
                                        <label>
                                            <input type="radio" v-model='endStatus' value='0'> <?php echo Yii::t("newDS", "No expiration (Always display)");?>
                                        </label>
                                    </div>
                                    <div class="radio-inline">
                                        <label>
                                            <input type="radio" v-model='endStatus' value='1'>  <?php echo Yii::t("newDS", "Specify a time");?>
                                        </label>
                                    </div>
                                    <div class='mt20'  v-show='endStatus==1'>
                                        <div class="col-sm-3">
                                            <input type="text" class="form-control form-group" id="expired_at" v-model='expired_at' placeholder="<?php echo Yii::t("newDS", "Select a date");?>"   :value='expired_at'>
                                        </div>
                                        <select class="form-control hoursMinutes" v-model='expired_atHours'>
                                            <option v-for='(list,index) in 25'  >{{index < 10?'0'+index:index}}</option>
                                        </select>
                                        <select class="form-control ml5 hoursMinutes"   v-model='expired_atMinutes'>
                                            <option v-for='(list,index) in 61' >{{index < 10?'0'+index:index}}</option>
                                        </select>
                                    </div>
                                </div>
                            </div> -->
                            
                            <div class="form-group">
                                <label for="inputEmail3" class="col-sm-2 control-label"> <?php echo Yii::t("newDS", "Status");?></label>
                                <div class="col-sm-10">
                                    <p class='mt10'>{{notice!=null && notice.publish_type==10?"<?php echo Yii::t("newDS", "online");?>":"<?php echo Yii::t("newDS", "offline");?>"}}</p>
                                    <div class="alert alert-warning" role="alert"><?php echo Yii::t("newDS", "Please return to list page to publish online/offline");?></div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t("newDS", "Parent Response");?></label>
                                <div class="col-sm-10">
                                    <div class="radio" v-for='(list,id,index) in requireResponseList'>
                                        <label>
                                            <input type="radio" v-model='editNotice.require_response' :value="id"> {{list}}
                                        </label>
                                    </div>
                                    <div class="alert alert-warning mt10" role="alert"><?php echo Yii::t("newDS", "Save content first before make settings at right side");?></div>

                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t("newDS", "In the name of");?></label>
                                <div class="col-sm-10 ">
                                    <div class='font12 color6 mt8'>按拼音和字母排序</div>
                                    <div class='row'>
                                        <div class='col-md-6 col-sm-6 mt10 '  v-for='(list,index) in addresser'>
                                            <label :class="['borderRadio', { 'selected': editNotice.sign_as_uid === list.uid }]">
                                                <input type="radio" name="sign_as_uid" :value="list.uid" v-model='editNotice.sign_as_uid'>
                                                <img :src="list.photoUrl" data-holder-rendered="true" class='ml10'>
                                                <div class='ml10 flex1 width0'>
                                                    <div class='color3 font14 nowrap'>{{list.name}}</div>
                                                    <div class='color6 font12 nowrap'>{{list.title}}</div>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                    <div class='clearfix'></div>
                                    <div class="alert alert-warning mt20" role="alert"><?php echo Yii::t("newDS", "This photo is for parent view and mangaged by HR. If the photo is not uploaded or mis-uploaded, please contact HR.");?></div>
                                </div>
                            </div> 
                            <div class="form-group mb20">
                                <label for="inputEmail3" class="col-sm-2 control-label"></label>
                                <div class="col-sm-10">
                                    <label class="checkbox-inline">
                                        <input type="checkbox" id="inlineCheckbox1" value="1" v-model='editNotice.on_top'><?php echo Yii::t("newDS", "Fix on top");?>
                                        <?php echo Yii::t('newDS', '(') . Yii::t('newDS', 'Fixed On Top till to: ') ?>{{editNotice.on_top_expired_at}}<?php echo Yii::t('newDS', ')'); ?>
                                    </label>
                                </div>
                            </div> 
                        </form>
                        <div class='clearfix'>
                            <hr>
                            <p> <button  class="btn  btn-primary pull-right" :disabled='disabled' @click='saveData'><?php echo Yii::t("global", "Save");?></button></p>
                        </div> 
                    </div>
                </div> 
            </div>
            <div class='col-md-4 col-sm-4' >
                <div class="panel panel-default">
                    <div class="panel-heading"><?php echo Yii::t("newDS", "Attachments");?></div>
                    <div class="panel-body">
                        <div v-if='editNotice && editNotice._id'>
                            <button type="button" id="pickfilesPhoto" class="btn  btn-primary " :disabled='disabledUpload'>
                            <span class='glyphicon glyphicon-paperclip'></span> {{disabledUpload?'<?php echo Yii::t("newDS", "Uploading...");?>':'<?php echo Yii::t("newDS", "Add");?>'}}
                            </button>
                            <div class='mt20'>
                                <p class='flex' v-for='(list,index) in attachments'>
                                    <span style='width:18px;line-height:25px' class='glyphicon glyphicon-list-alt inline-block'> </span>
                                    <a target="_blank" class='flex1'  style='line-height:26px' :href='list.file_key' v-if='!list.isEdit'>{{list.title}}</a>
                                    <span class='flex1' v-else><input type="input" class='inputStyle' :value='list.title'  @keyup.enter="saveFile(list)"  v-model='attachmentName' ></span>
                                    <span style='width:85px' class='text-right inline-block' v-if='!list.isEdit'>

                                        <a href="javascript:;" class="btn btn-xs btn-primary" 
                                            draggable="true"
                                            @dragstart="handleDragStart($event, list)" 
                                            @dragover.prevent="handleDragOver($event, list)" 
                                            @dragenter="handleDragEnter($event, list)" 
                                            @dragend="handleDragEnd($event, list)" 
                                            onMouseOver="$(this).tooltip('show')" 
                                            data-toggle="tooltip" 
                                            data-placement="top"  
                                            title="<?php echo Yii::t("newDS", "可上下拖动");?>">
                                            <span class="glyphicon glyphicon-move"></span>
                                        </a>

                                        <button type="button" class="btn btn-primary btn-xs" aria-label="Left Align" @click='list.isEdit=true,attachmentName=list.title'>
                                            <span class="glyphicon glyphicon-pencil" aria-hidden="true"></span>
                                        </button>
                                        <button type="button" class="btn btn-danger btn-xs" aria-label="Left Align" @click='delFile(list)'>
                                            <span class="glyphicon glyphicon-trash" aria-hidden="true"></span>
                                        </button>
                                    </span>
                                    <span style='width:80px'  class='text-right inline-block' v-else>
                                        <button type="button" class="btn btn-primary btn-xs" @click='saveFile(list)'><?php echo Yii::t("global", "Save");?></button>
                                        <button type="button" class="btn btn-default btn-xs" @click='list.isEdit=false'><?php echo Yii::t("global", "Cancel");?></button>
                                    </span>
                                </p>
                            </div>
                        </div>
                        <div  v-else class="alert alert-info" role="alert"><?php echo Yii::t("newDS", "Save content first before upload attachment.");?></div>
                    </div>
                </div> 
                <!--   -->
                <div class="panel panel-default" v-if='editNotice && editNotice._id && require_response==2'>
                    <div class="panel-heading"><?php echo Yii::t("newDS", "Options Settings");?></div>
                    <div class="panel-body">
                        <p><label><?php echo Yii::t("newDS", "Title");?>：</label></p>
                        <div class='ml10 mb10 flex'>
                            <span  class='flex1' v-if='!questionT_cn'>{{confirm_configs.t_cn?confirm_configs.t_cn:questionData.t_cn}}</span>
                            <span class='flex1' v-else><input type="input" class='inputStyle' :value='confirm_configs.t_cn'  @keyup.enter="saveQuestion('cn')"  v-model='questioncn' ></span>
                            <span style='width:85px' class='text-right inline-block' v-if='!questionT_cn'>
                                <button type="button" class="btn btn-primary btn-xs" aria-label="Left Align" @click='questionT_cn=true;questioncn=confirm_configs.t_cn'>
                                    <span class="glyphicon glyphicon-edit" aria-hidden="true"></span>
                                </button>
                            </span>
                            <span style='width:85px'  class='text-right inline-block' v-else>
                                <button type="button" class="btn btn-primary btn-xs" @click='saveQuestion("cn")'><?php echo Yii::t("global", "Save");?></button>
                                <button type="button" class="btn btn-default btn-xs" @click='questionT_cn=false'><?php echo Yii::t("global", "Cancel");?></button>
                            </span>
                        </div>
                        <div class='ml10 mb10 flex'>
                            <span  class='flex1' v-if='!questionT_en'>{{confirm_configs.t_en?confirm_configs.t_en:questionData.t_en}}</span>
                            <span class='flex1' v-else><input type="input" class='inputStyle' :value='confirm_configs.t_en'  @keyup.enter="saveQuestion('en')"  v-model='questionen' ></span>
                            <span style='width:85px' class='text-right inline-block' v-if='!questionT_en'>
                                <button type="button" class="btn btn-primary btn-xs" aria-label="Left Align" @click='questionT_en=true;questionen=confirm_configs.t_en'>
                                    <span class="glyphicon glyphicon-edit" aria-hidden="true"></span>
                                </button>
                            </span>
                            <span style='width:85px'  class='text-right inline-block' v-else>
                                <button type="button" class="btn btn-primary btn-xs" @click='saveQuestion("en")'><?php echo Yii::t("global", "Save");?></button>
                                <button type="button" class="btn btn-default btn-xs" @click='questionT_en=false'><?php echo Yii::t("global", "Cancel");?></button>
                            </span>
                        </div>
                        <p class='mt20'><label><?php echo Yii::t("report", "Options");?>：</label>
                        <button type="button" class="btn btn-primary btn-xs pull-right" @click='optionEdit("")'>
                                <span class='glyphicon glyphicon-plus'></span> <?php echo Yii::t("report", "Add Option");?> 
                            </button></p>
                        <div class="panel panel-default" v-for='(list,index) in confirm_configs.sort' >
                            <div class="panel-heading" v-if='optionList[list]'>
                                <strong><?php echo Yii::t("report", "Options");?> {{index+1}}</strong>
                                <span  class='pull-right inline-block'>
                                    <a href="javascript:;" class="mr10"  @click='optionEdit(list)'>
                                        <span class="glyphicon glyphicon-edit" aria-hidden="true"></span>
                                    </a>
                                    <a href="javascript:;" class="mr10" @click='delOption(optionList[list])'>
                                        <span class="glyphicon glyphicon-trash" aria-hidden="true"></span>
                                    </a>
                                    <a href="javascript:;" class="mr10" 
                                        draggable="true"
                                        @dragstart="optionStart($event, list)" 
                                        @dragover.prevent="optionOver($event, list)" 
                                        @dragenter="optionEnter($event, list)" 
                                        @dragend="optionEnd($event, list)" 
                                        onMouseOver="$(this).tooltip('show')" 
                                        data-toggle="tooltip" 
                                        data-placement="top"  
                                        title="<?php echo Yii::t("newDS", "可上下拖动");?>">
                                        <span class="glyphicon glyphicon-move"></span>
                                    </a>
                                </span>
                                <div class='clearfix'></div>
                            </div>
                            <div class="panel-body" v-if='optionList[list]'>
                                <div class='flex'>
                                    <div class='flex1'  style='line-height:26px'>
                                        <div>{{optionList[list].cn}}</div>
                                        <div>{{optionList[list].en}}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="panel panel-default" v-if='editNotice && editNotice._id && require_response==3'>
                    <div class="panel-heading"><?php echo Yii::t("newDS", "Mini survey");?></div>
                    <div class="panel-body">
                        <p class='flex'>
                            <label class='flex1'>
                                <?php echo Yii::t("newDS", "Choose a survey");?>
                            </label> 
                            <a v-if='editNotice.survey_id && editNotice.survey_id!="" && editNotice.survey_id!=0' target="_blank" :href="survey_url"><?php echo Yii::t("newDS", "Edit survey");?></a>
                        </p>
                        <p class='flex' style='align-items: center;'>
                            <select class="form-control flex1" v-model='survey_id' style='display:inline-block'>
                                <option disabled value=''><?php echo Yii::t("global", "Please Select");?></option>
                                <option v-for='(list,index) in survey_list' :value='list.id'>{{list.title}}</option>
                            </select>
                            <a href='javascript:;' @click='getSurveyList()'><span  class='glyphicon glyphicon-refresh ml20 cur-p'></span> <?php echo Yii::t("global", "Refresh");?></a>
                            <button type="button" class="btn btn-primary ml20" @click='getSurvey()' :disabled='saveQuestionBtn'><?php echo Yii::t("global", "Save");?></button> 
                        </p>                           
                    </div>
                </div>
                <div class="panel panel-default">
                    <div class="panel-heading"><?php echo Yii::t("directMessage", "Parent Feedback Collaborators"); ?></div>
                    <div class="panel-body">
                        <div class='flex font14 color9'>
                            <span class='glyphicon glyphicon-exclamation-sign mt2'></span>
                            <span class='flex1 ml8'><?php echo Yii::t("newDS", "All collaborators can reply parent feedback, if no collaborators is set, ONLY the user of signature can reply.");?></span>
                        </div>
                        <div v-if='editNotice && editNotice._id'>
                            <div class='mt24 flex mb24'>
                                <div class='flex1' >
                                    <button type="button" class="btn  btn-primary " @click='addUser'><?php echo Yii::t("directMessage", "Add a member");?>
                                </button>
                                </div>
                                <button type="button" @click='clearJoint("clear")'  class='btn btn-link'  v-if='editNotice.joint_admins && editNotice.joint_admins.length!=0'><?php echo Yii::t("directMessage", "Clear All"); ?>（{{editNotice.joint_admins.length}}）</button>
                            </div>
                            <div class='col-md-6 col-sm-6 mb24 pl0' v-for='(list,index) in editNotice.joint_admins'>
                                <div class="media bgGrey">
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                            <img :src="jointAdmins[list].photoUrl" data-holder-rendered="true" class="contentAvatar">
                                        </a>
                                    </div>
                                    <div class="media-right pull-right pt12 text-right">
                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/del.png' ?>" class='example'  @click='delJoint(list)' alt="">
                                    </div>
                                    <div class="media-body pt4 media-middle">
                                        <div class="lineHeight20 text-primary"><span class="font14 color3 nowrap">{{jointAdmins[list].name}}</span></div>
                                        <div class="font12 color6 nowrap">{{jointAdmins[list].hrPosition}}</div>
                                    </div>
                                </div>
                            </div>
                            <div class='clearfix'></div>
                        </div>
                        <div  v-else class="alert alert-info mt15" role="alert"><?php echo Yii::t("newDS", "Save content first before Add a member.");?></div>
                    </div>
                </div> 
            </div>
            <div class="modal fade" tabindex="-1" role="dialog" id="editModal">
                <div class="modal-dialog modal-sm" role="document">
                    <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title"><?php echo Yii::t("newDS", "Data Saved!");?></h4>
                    </div>
                    <div class="modal-footer ">
                        <a type="button" class="btn btn-default" href="<?php echo $this->createUrl('index'); ?>" ><?php echo Yii::t("newDS", "Go to notice list");?></a>
                        <button type="button" v-if='isEdit' class="btn btn-primary" data-dismiss="modal"><?php echo Yii::t("newDS", "Stay in this page");?></button>
                        <a type="button" v-else class="btn btn-primary" :href="'<?php echo $this->createUrl('edit'); ?>&id='+editNotice._id"  ><?php echo Yii::t("newDS", "Stay in this page");?></a>
                    </div>
                    </div>
                </div>
            </div>
            <div class="modal fade" tabindex="-1" role="dialog" id="optionModal">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title">
                            <span v-if='optionId==""'><?php echo Yii::t("report", "Add Option");?> </span>
                            <span v-else><?php echo Yii::t("global", "Edit");?></span>
                        </h4>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label  class="col-sm-2 control-label"><?php echo Yii::t('newDS', 'Title (Cn)'); ?></label>
                            <div class="col-sm-10">
                                <input type="text" v-model='optionCn' :value='optionCn' class="form-control" placeholder="<?php echo Yii::t('newDS', 'Title (Cn)'); ?>">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label mt10"><?php echo Yii::t('newDS', 'Title (En)'); ?></label>
                            <div class="col-sm-10 mt10">
                                <input type="text" v-model='optionEn' :value='optionEn' class="form-control" placeholder="<?php echo Yii::t('newDS', 'Title (En)'); ?>">
                            </div>
                        </div>
                        <div class='clearfix'></div>
                    </div>
                    <div class="modal-footer ">
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                        <button type="button" class="btn btn-primary" @click='saveOption()'><?php echo Yii::t("global", "OK");?></button>

                    </div>
                    </div>
                </div>
            </div>
            <!-- 协同管理 -->
            <div class="modal fade" id="authorizedModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header"  @click='showSelect=false'>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("directMessage", "Add Reply collaborators"); ?></h4>
                        </div>
                        <div class="modal-body"  >
                            <div class='color6 font14' @click='showSelect=false' ><?php echo Yii::t("global", "Search"); ?></div>
                            <div  class='flex pt16'>
                                <div class='flex1 relative'>
                                    <input type="text" ref='inputSearch' class="form-control" id="exampleInputAmount" placeholder="<?php echo  Yii::t('attends', 'Filter by name');  ?>" v-model="searchStu"   @focus="focusShow()">
                                    <div class='selectTeacher' v-if='showSelect  && !loading'>
                                        <div v-if='options.length==0'>
                                            <p class='font14 color3 text-center mt20 color9'><?php echo Yii::t('ptc', 'No Data'); ?></p> 
                                        </div>
                                        <div v-else>
                                            <div class="searchItem" v-for='(item,index) in options'>
                                                <div class="media" @click.stop='confirmTeacher(item)'>
                                                    <div class="media-left pull-left media-middle">
                                                        <a href="javascript:void(0)">
                                                            <img :src="item.photoUrl" data-holder-rendered="true" class="media-object img-circle imageSearch">
                                                        </a>
                                                    </div>
                                                    <div class="media-body mt5 media-middle">
                                                        <h4 class="media-heading font14 color3 text_overflow">{{item.name}}</h4>
                                                        <div class="text-muted text_overflow font12">{{ item.hrPosition }}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div  @click='showSelect=false'>
                                    <button type="button" class="btn btn-primary ml16"  :disabled='teacherUid==""?true:false' @click.stop='confirmAdmin'><?php echo Yii::t("directMessage", "Proceed to add"); ?></button>
                                </div>
                            </div>
                            <div  @click='showSelect=false'>                            
                                <div class='color6 font14 mt24 mb16' v-if='editNotice.joint_admins'><span><?php echo Yii::t("directMessage", "Reply collaborators"); ?></span>  <span class="badge">{{editNotice.joint_admins.length}}</span></div>
                                <div class='col-md-4 col-sm-4 mb24' v-for='(list,index) in editNotice.joint_admins'>
                                    <div class="media bgGrey" >
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)"><img :src="jointAdmins[list].photoUrl" data-holder-rendered="true" class="contentAvatar"></a>
                                        </div>
                                        <div class="media-right pull-right pt12 text-right">
                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/del.png' ?>" class='example' alt="" @click.stop='delJoint(list)'>
                                        </div>
                                        <div class="media-body pt4 media-middle">
                                            <div class="lineHeight20  text-primary">
                                                <span class='font14 color3 nowrap'>{{jointAdmins[list].name}}</span>
                                            </div>
                                            <div class="font12 color6 nowrap">{{jointAdmins[list].hrPosition}}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class='clearfix'></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal fade" id="delModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
                <div class="modal-dialog modal-sm" role="document">
                    <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "Delete");?></h4>
                    </div>
                    <div class="modal-body">
                        <div><?php echo Yii::t("directMessage", "Empty all collaborators?");?></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                        <button type="button" class="btn btn-primary" @click='clearJoint()'><?php echo Yii::t("message", "OK");?></button>
                    </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>

<script>
    $(function() {
		$("#publish_at").datepicker({
			dateFormat: "yy-mm-dd ",
		});
		$("#expired_at").datepicker({
			dateFormat: "yy-mm-dd ",
		});
        $( "#publish_at").on('change', function () {
            container.publish_at = $('#publish_at').val();
        });
        $( "#expired_at").on('change', function () {
            container.expired_at = $('#expired_at').val();
        });
	});
    tinymce.init({
        selector: '#tinymce',
        content_style: "img{max-width:100%}",
        language: '<?php echo CommonUtils::autoLang("zh_CN" , "en"); ?>',
        height: 600,
        plugins: 'fullscreen image link media imagetools preview table code wordcount paste my-example-plugin lists, advlist',
        imagetools_cors_hosts: ['picsum.photos'],
        menubar: 'file edit view insert format help',
        contextmenu: 'link image imagetools table spellchecker lists editimage',
        toolbar_sticky: true,
        image_uploadtab: false,
        image_dimensions: false,
        automatic_uploads: true,
        paste_data_images: true,
        menu: {
            insert: { title: 'Insert', items: 'image link media template codesample inserttable | charmap emoticons hr | pagebreak nonbreaking anchor toc | insertdatetime | editimage' },
        },
        // 上传文件，成功回调，失败回调，进度
        images_upload_handler: function (blobInfo, success, failure, progress) {
            handleUploadImage(blobInfo, success, failure, progress);
        },
        toolbar: 'mediaDialog shareDialog bullist numlist',
        setup: function(editor) {
            // 实时同步编辑器内容到 selector
            editor.on('change', function() {
                tinymce.triggerSave();
            });
            editor.ui.registry.addButton('mediaDialog', {
                text:'<span title="<?php echo Yii::t("newDS", "Media Gallery");?>"><svg width="24" height="24"><path fill-rule="nonzero" d="M5 15.7l2.3-2.2c.3-.3.7-.3 1 0L11 16l5.1-5c.3-.4.8-.4 1 0l2 1.9V8H5v7.7zM5 18V19h3l1.8-1.9-2-2L5 17.9zm14-3l-2.5-2.4-6.4 6.5H19v-4zM4 6h16c.6 0 1 .4 1 1v13c0 .6-.4 1-1 1H4a1 1 0 0 1-1-1V7c0-.6.4-1 1-1zm6 7a2 2 0 1 1 0-4 2 2 0 0 1 0 4zM4.5 4h15a.5.5 0 1 1 0 1h-15a.5.5 0 0 1 0-1zm2-2h11a.5.5 0 1 1 0 1h-11a.5.5 0 0 1 0-1z"></path></svg></span>',
                onAction: function () {
                    container.tinymceList=[]
                    var instanceApi = editor.windowManager.openUrl({
                        onMessage: function (dialogApi, details) {
                            switch (details.mceAction) {
                                case 'loading':
                                    dialogApi.unblock()
                                    break;
                                case 'addItem':
                                    container.tinymceList.push(details.content)
                                    break;
                                case 'removeItem':
                                    for(var i=0;i<container.tinymceList.length;i++){
                                        if(container.tinymceList[i].id==details.content.id){
                                            container.tinymceList.splice(i,1)
                                        }
                                    }
                                break;
                            }
                        },
                        onAction: function (dialogApi, details) {
                            dialogApi.close()
                            for(var i=0;i<container.tinymceList.length;i++){
                                if(container.tinymceList[i].type=='photo'){
                                    editor.insertContent('<div><img  style="max-width:100%"  src='+container.tinymceList[i]._url+'></div>')
                                }else{
                                    let url=container.tinymceList[i].url.split('!vh120')[0]
                                    editor.insertContent(
                                    '<p><video controls  style="max-width:100%;object-fit:cover"  width="600" height="auto" poster='+url+' >'+
                                        '<source src='+container.tinymceList[i]._url+'  type="video/mp4">'+
                                    '</video></p>')
                                }
                            }
                        },
                        title: '<?php echo Yii::t("newDS", "Media Gallery");?>',
                        url: '<?php echo $this->createUrl("journals/media") ?>',
                        height: 500,
                        width:730,
                        buttons: [{
                            type:'custom',
                            text:'Insert',
                            name:'btn-insert',
                            primary: true,
                            align: 'end'
                        },
                            {
                            type:'cancel',
                            text:'Close',
                            name:'btn-close',
                            primary: false,
                            align: 'end'
                        }],

                    });
                    instanceApi.block('loading')
                }
            })
            editor.ui.registry.addButton('shareDialog', {
                text: '<span title="<?php echo Yii::t("directMessage", "Insert Department Contact QrCode"); ?>" style="position:relative"><span class="newIcon">new</span><svg xmlns="http://www.w3.org/2000/svg" version="1.0" width="24" height="24" viewBox="0 0 36.000000 36.000000" preserveAspectRatio="xMidYMid meet"><g transform="translate(0.000000,36.000000) scale(0.100000,-0.100000)" fill="#000000" stroke="none"><path d="M67 324 c-10 -11 -8 -74 2 -74 5 0 11 12 13 28 l3 27 103 3 102 3 0 -126 0 -125 -105 0 -105 0 0 25 c0 14 -5 25 -11 25 -6 0 -9 -16 -7 -37 l3 -38 120 0 120 0 0 145 0 145 -116 3 c-63 1 -118 -1 -122 -4z"/><path d="M161 242 c-12 -23 4 -52 29 -52 26 0 45 36 29 56 -16 19 -46 18 -58 -4z m44 -11 c7 -12 -12 -24 -25 -16 -11 7 -4 25 10 25 5 0 11 -4 15 -9z"/><path d="M40 220 c0 -5 14 -10 30 -10 17 0 30 5 30 10 0 6 -13 10 -30 10 -16 0 -30 -4 -30 -10z"/><path d="M40 180 c0 -5 14 -10 30 -10 17 0 30 5 30 10 0 6 -13 10 -30 10 -16 0 -30 -4 -30 -10z"/><path d="M152 154 c-12 -8 -22 -24 -22 -35 0 -26 13 -24 25 4 7 14 19 22 35 22 16 0 28 -8 35 -22 12 -28 25 -30 25 -4 0 22 -35 51 -60 51 -9 0 -26 -7 -38 -16z"/><path d="M40 140 c0 -5 14 -10 31 -10 17 0 28 4 24 10 -3 6 -17 10 -31 10 -13 0 -24 -4 -24 -10z"/></g></svg></span>',
                onAction: function () {
                    container.tinymceList=[]
                    var instanceApi = editor.windowManager.openUrl({
                        onMessage: function (dialogApi, details) {
                            switch (details.mceAction) {
                                case 'loading':
                                    dialogApi.unblock()
                                    break;
                                case 'insertImg':
                                    editor.insertContent('<div style="text-align:center;margin:0 auto"><img src='+details.content+'></div><div></div>')
                                    dialogApi.close()
                                break;
                            }
                        },                    
                        title: '<?php echo Yii::t("directMessage", "Insert Department Contact QrCode"); ?>',
                        url: '<?php echo $this->createUrl('directMessage/qrcode'); ?>',
                        height: 350,
                        width:400,
                    });
                    instanceApi.block('loading')
                }
            })
        },
    });
    tinymce.init({
        selector: '#tinymceEn',
        content_style: "img{max-width:100%}",
        language: '<?php echo CommonUtils::autoLang("zh_CN" , "en"); ?>',
        height: 600,
        plugins: 'fullscreen image link media imagetools preview table code wordcount paste my-example-plugin lists, advlist',
        imagetools_cors_hosts: ['picsum.photos'],
        menubar: 'file edit view insert format help',
        contextmenu: 'link image imagetools table spellchecker lists editimage',
        toolbar: 'undo redo bold italic underline strikethrough fontselect fontsizeselect formatselect | image fullscreen preview | editimage',
        toolbar_sticky: true,
        image_uploadtab: false,
        image_dimensions: false,
        automatic_uploads: true,
        paste_data_images: true,
        menu: {
            insert: { title: 'Insert', items: 'image link media template codesample inserttable | charmap emoticons hr | pagebreak nonbreaking anchor toc | insertdatetime | editimage' },
        },
        // 上传文件，成功回调，失败回调，进度
        images_upload_handler: function (blobInfo, success, failure, progress) {
            handleUploadImage(blobInfo, success, failure, progress);
        },
        toolbar: 'mediaDialog shareDialog  bullist numlist',
        setup: function(editor) {
            // 实时同步编辑器内容到 selector
            editor.on('change', function() {
                tinymce.triggerSave();
            });
            editor.ui.registry.addButton('mediaDialog', {
                text:'<span title="<?php echo Yii::t("newDS", "Media Gallery");?>"><svg width="24" height="24"><path fill-rule="nonzero" d="M5 15.7l2.3-2.2c.3-.3.7-.3 1 0L11 16l5.1-5c.3-.4.8-.4 1 0l2 1.9V8H5v7.7zM5 18V19h3l1.8-1.9-2-2L5 17.9zm14-3l-2.5-2.4-6.4 6.5H19v-4zM4 6h16c.6 0 1 .4 1 1v13c0 .6-.4 1-1 1H4a1 1 0 0 1-1-1V7c0-.6.4-1 1-1zm6 7a2 2 0 1 1 0-4 2 2 0 0 1 0 4zM4.5 4h15a.5.5 0 1 1 0 1h-15a.5.5 0 0 1 0-1zm2-2h11a.5.5 0 1 1 0 1h-11a.5.5 0 0 1 0-1z"></path></svg></span>',
                onAction: function () {
                    container.tinymceList=[]
                    var instanceApi = editor.windowManager.openUrl({
                        onMessage: function (dialogApi, details) {
                            switch (details.mceAction) {
                                case 'loading':
                                    dialogApi.unblock()
                                    break;
                                case 'addItem':
                                    container.tinymceList.push(details.content)
                                    break;
                                case 'removeItem':
                                    for(var i=0;i<container.tinymceList.length;i++){
                                        if(container.tinymceList[i].id==details.content.id){
                                            container.tinymceList.splice(i,1)
                                        }
                                    }
                                break;
                            }
                        },
                        onAction: function (dialogApi, details) {
                            dialogApi.close()
                            for(var i=0;i<container.tinymceList.length;i++){
                                if(container.tinymceList[i].type=='photo'){
                                    editor.insertContent('<div><img  style="max-width:100%"  src='+container.tinymceList[i]._url+'></div>')
                                }else{
                                    let url=container.tinymceList[i].url.split('!vh120')[0]
                                    editor.insertContent(
                                    '<p><video controls  style="max-width:100%;object-fit:cover"  width="600" height="auto" poster='+url+' >'+
                                        '<source src='+container.tinymceList[i]._url+'  type="video/mp4">'+
                                    '</video></p>')
                                }
                            }
                        },
                        title: '<?php echo Yii::t("newDS", "Media Gallery");?>',
                        url: '<?php echo $this->createUrl("journals/media") ?>',
                        height: 500,
                        width:730,
                        buttons: [{
                            type:'custom',
                            text:'Insert',
                            name:'btn-insert',
                            primary: true,
                            align: 'end'
                        },
                            {
                            type:'cancel',
                            text:'Close',
                            name:'btn-close',
                            primary: false,
                            align: 'end'
                        }],

                    });
                    instanceApi.block('loading')
                }
            });
            editor.ui.registry.addButton('shareDialog', {
                text: '<span title="<?php echo Yii::t("directMessage", "Insert Department Contact QrCode"); ?>" style="position:relative"><span class="newIcon">new</span><svg xmlns="http://www.w3.org/2000/svg" version="1.0" width="24" height="24" viewBox="0 0 36.000000 36.000000" preserveAspectRatio="xMidYMid meet"><g transform="translate(0.000000,36.000000) scale(0.100000,-0.100000)" fill="#000000" stroke="none"><path d="M67 324 c-10 -11 -8 -74 2 -74 5 0 11 12 13 28 l3 27 103 3 102 3 0 -126 0 -125 -105 0 -105 0 0 25 c0 14 -5 25 -11 25 -6 0 -9 -16 -7 -37 l3 -38 120 0 120 0 0 145 0 145 -116 3 c-63 1 -118 -1 -122 -4z"/><path d="M161 242 c-12 -23 4 -52 29 -52 26 0 45 36 29 56 -16 19 -46 18 -58 -4z m44 -11 c7 -12 -12 -24 -25 -16 -11 7 -4 25 10 25 5 0 11 -4 15 -9z"/><path d="M40 220 c0 -5 14 -10 30 -10 17 0 30 5 30 10 0 6 -13 10 -30 10 -16 0 -30 -4 -30 -10z"/><path d="M40 180 c0 -5 14 -10 30 -10 17 0 30 5 30 10 0 6 -13 10 -30 10 -16 0 -30 -4 -30 -10z"/><path d="M152 154 c-12 -8 -22 -24 -22 -35 0 -26 13 -24 25 4 7 14 19 22 35 22 16 0 28 -8 35 -22 12 -28 25 -30 25 -4 0 22 -35 51 -60 51 -9 0 -26 -7 -38 -16z"/><path d="M40 140 c0 -5 14 -10 31 -10 17 0 28 4 24 10 -3 6 -17 10 -31 10 -13 0 -24 -4 -24 -10z"/></g></svg></span>',
                onAction: function () {
                    container.tinymceList=[]
                    var instanceApi = editor.windowManager.openUrl({
                        onMessage: function (dialogApi, details) {
                            switch (details.mceAction) {
                                case 'loading':
                                    dialogApi.unblock()
                                    break;
                                case 'insertImg':
                                    editor.insertContent('<div style="text-align:center;margin:0 auto"><img src='+details.content+'></div><div></div>')
                                    dialogApi.close()
                                break;
                            }
                        },                    
                        title: '<?php echo Yii::t("directMessage", "Insert Department Contact QrCode"); ?>',
                        url: '<?php echo $this->createUrl('directMessage/qrcode'); ?>',
                        height: 350,
                        width:400,
                    });
                    instanceApi.block('loading')
                }
            })
        },
    });
    tinymce.PluginManager.add('my-example-plugin', function (editor) {
        editor.ui.registry.addMenuItem('editimage', {
            icon: 'image',
            text: '<?php echo Yii::t("newDS", "Add/Remove Watermark");?>',
            onAction: function () {
                if(container.Watermark.indexOf(container.WatermarkImg) == -1){
                    container.Watermark=container.Watermark+container.WatermarkImg
                }else{
                    container.Watermark=container.Watermark.replace(container.WatermarkImg,"")
                }
                editor.insertContent('<div><img  style="max-width:100%"  src='+container.Watermark+'></div>')
            }
        });
        editor.ui.registry.addContextMenu('image', {
            update: function (element) {
                container.Watermark=element.src
                return !container.Watermark ? '' : 'image';
            }
        });
    });
    function handleUploadImage (blobInfo, success, failure, progress) {
        $.ajax({
            url: '<?php echo $this->createUrl("journals/getQiniuTokenSimple") ?>',
            type: "post",
            dataType: 'json',
            data: {
                isPrivate: 0,
                prefix: 'notice',
            },
            success: function(data) {
                if (data.state == 'success') {
                    var token = data.data.token;
                    var domain = data.data.domain;
                    // 上传文件

                    var xhr = new XMLHttpRequest();
                    xhr.withCredentials = false;
                    xhr.open("post", "https://up-z0.qiniup.com");
                    xhr.upload.onprogress = function (e) {
                        progress(Math.round(e.loaded / e.total * 100) | 0);
                    };
                    xhr.onerror = function () {
                        failure('Image upload failed due to a XHR Transport error. Code: ' + xhr.status);
                    };
                    xhr.onload = function() {
                        var json;

                        if (xhr.status === 403) {
                            failure('HTTP Error: ' + xhr.status, { remove: true });
                            return;
                        }

                        if (xhr.status < 200 || xhr.status >= 300) {
                            failure('HTTP Error: ' + xhr.status);
                            return;
                        }

                        json = JSON.parse(xhr.responseText);

                        if (!json || typeof json.name != 'string') {
                            failure('Invalid JSON: ' + xhr.responseText);
                            return;
                        }
                        if(json.h<=500){
                            success(domain + "/" + json.name);
                        }else{
                            success(domain + "/" + json.name+container.WatermarkImg);
                        }
                    };
                    var formData = new FormData();
                    var file = blobInfo.blob();
                    formData.append('file', file, file.name);
                    formData.append('token', token);
                    xhr.send(formData);
                } else {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                }
            },
            error: function(data) {
                resultTip({
                    error: 'warning',
                    msg: "上传失败"
                });
            },
        })
    }
    var data = <?php echo json_encode($data); ?>;
    console.log(data)
    var container = new Vue({
        el: "#container",
        data: {
            Watermark:'',
            WatermarkImg:'<?php echo (isset($this->schoolType) && $this->schoolType == 'ds') ? '!v1000' : '!i1000'; ?>',
            dataConfig:data,
            markList: data.configList.markList,
            markList1: {},
            markList2: {},
            gradeGroupList: data.configList.gradeList,
            levelList: data.configList.levelList,
            categoryList: data.categoryList,
            requireResponseList:data.configList.requireResponseList,
            publishTypeList: data.configList.publishTypeList,
            requireResponseList:data.configList.requireResponseList,
            showNext:data.configList.showNext,
            notice:data.notice,
            require_response:data.notice!=null?data.notice.require_response:'',
            token: '',
            editNotice:{},
            endStatus:'',
            attachments:[],
            expired_atHours:'00',
            expired_atMinutes:'00',
            expired_at:'',
            publish_at:'',
            publish_atHours:'00',
            publish_atMinutes:'00',
            attachmentName:'',
            comment_enabled:true,
            disabled:false,
            disabledUpload:false,
            tinymceList:[],
            isEdit:false,
            startYear:data.configList.startYear,
            addresser:data.addresser,
            confirm_configs:{},
            questionT_cn:false,
            questionT_en:false,
            optionList:{},
            questionData:{
                t_cn: "<?php echo Yii::t('newDS', 'Chinese Title (please modify)'); ?>",
                t_en: "<?php echo Yii::t('newDS', 'English Title (please modify)'); ?>",
                options:{},
                sort:[]
            },
            optionEn:'',
            optionCn:'',
            optionId:{},
            questioncn:'',
            questionen:'',
            grades_status: data.gradesStatus == 1 ? true : false,
            survey_url: '',
            saveQuestionBtn:false,
            survey_id:'',
            survey_list:[],
            applicable:'1',
            nextData:{},
            options: [],
            loading: false,
            teacherUid:'',
            searchStu:'',
            showSelect:false,
            jointAdmins:data.jointAdmins
        },
        watch: {
            'editNotice._id': {
                handler(newVal, oldVal) {
                    if(newVal!=undefined){
                       this.uploadFile()
                    }
                },
            deep: true
            },
            searchStu:function(newVal,oldVal){
                this.remoteMethod(newVal)
            },
        },
        created: function() {
            const dataArray = Object.values(data.addresser);
            this.addresser=this.sortTea(dataArray)
            if(Object.values(this.markList,).length==4){
                this.markList1={3:'ES',4:'SS'}
                this.markList2={5:'ES',6:'SS'}
            }
            for(var i=0;i<this.gradeGroupList.length;i++){
                this.gradeGroupList[i].checked=false
                this.gradeGroupList[i].checkedList=[]
                if (data.notice != null && data.notice != 'null' && Object.keys(data.notice).length != 0) {
                    this.gradeGroupList[i].item.forEach(items => {
                        if(data.notice.grades.indexOf(items.key)!=-1){
                            this.gradeGroupList[i].checkedList.push(items.key)
                        }
                    })
                }
                if(this.gradeGroupList[i].checkedList.length==this.gradeGroupList[i].item.length){
                    this.gradeGroupList[i].checked=true
                }else{
                    this.gradeGroupList[i].checked=false
                }
            }
            this.startYear=data.configList.startYear+'-'+ (parseInt(data.configList.startYear)+1)
            if (data.notice == null || data.notice == 'null' || Object.keys(data.notice).length == 0) {
                this.editNotice = {
                    'child_mark': [],
                    'grades': [],
                    'expired_at': '',
                    'content': '',
                    'content_en':'',
                    'summary_cn': "",
                    'summary_en': '',
                    'category':'',
                    'q_cn':'',
                    'q_en':'',
                    // 'publish_at': "",
                    'publish_type': '',
                    'level':'',
                    'on_top':'',
                    'require_response':'',
                    'start_year':data.configList.startYear,
                    'sign_as_uid':'',
                    'on_top_expired_at': '<?php echo date('Y-m-d', time() + 864000); ?>',
                }
                this.endStatus=''
            } else {
                this.editNotice = data.notice
                this.endStatus=this.editNotice.expired_at==0?0:1
                this.comment_enabled=this.editNotice.comment_enabled==0?false:true
                if(this.editNotice.expired_at!=0){
                    this.expired_atHours=this.newDate(this.editNotice.expired_at,'hours')
                    this.expired_atMinutes=this.newDate(this.editNotice.expired_at,'minutes')
                    this.expired_at=this.newDate(this.editNotice.expired_at,'date')
                }
                if(this.editNotice.publish_type=='10'){
                    this.publish_atHours=this.newDate(this.editNotice.format_publish_at,'hours')
                    this.publish_atMinutes=this.newDate(this.editNotice.format_publish_at,'minutes')
                    this.publish_at=this.newDate(this.editNotice.format_publish_at,'date')
                }
                if(data.notice.confirm_configs && data.notice.confirm_configs!=null && data.notice.confirm_configs.length!=0){
                    this.confirm_configs=data.notice.confirm_configs
                    this.optionList=this.confirm_configs.options
                }else{
                    this.confirm_configs=this.questionData
                }
                this.survey_list=this.editNotice.survey_list
                if (data.notice.survey_id) {
                    this.survey_id=data.notice.survey_id
                    this.survey_url = "<?php echo Yii::app()->createUrl('//moperation/surveyReturn/questionsList'); ?>?survey_id=" + data.notice.survey_id
                }
                if(data.notice.isNext==1){
                    this.applicable=2
                    this.getNextList()
                }else{
                    this.applicable=1
                }
            }
            
        },
        methods: {
            sortTea(list){
                list.sort((x,y)=>{
                    return x['name'].localeCompare(y['name'])
                })
                return list
            },
            getNextList(){
                let that=this
                if(Object.keys(that.nextData).length!=0){
                    return
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("getChildMarkNum") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.nextData=data.data
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.disabled=false
                        }
                    },
                    error: function(data) {
                        that.disabled=false
                    },
                })
            },
            checkAll(list){
                if(list.checked){
                    list.item.forEach(items => {
                        list.checkedList.push(items.key)
                    })
                }else{
                    list.checkedList=[]
                }
                this.$forceUpdate()
            },
            checkGrades(list){
                if(list.checkedList.length==list.item.length){
                    list.checked=true
                }else{
                    list.checked=false
                }
                this.$forceUpdate()
            },
            stringdate(date){
                var d=new Date(date);
                return d.toUTCString()
            },
            newDate(dateTime,type){
                const date = new Date(dateTime.replace(/\-/g, '/'));
                const Y = date.getFullYear() + '-';
                const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
                const D = (date.getDate() < 10 ? '0'+date.getDate() : date.getDate()) + ' ';
                const h = (date.getHours() < 10 ? '0'+date.getHours() : date.getHours()) ;
                const m = (date.getMinutes() <10 ? '0'+date.getMinutes() : date.getMinutes());
                const s = date.getSeconds(); // 秒
                const dateString = Y + M + D + h+':' + m+':' + s;
                if(type=='hours'){
                    return h
                } if(type=='minutes'){
                    return m
                } if(type=='date'){
                    return Y + M + D 
                }
                if(!type){
                    return dateString;
                }
            },
            saveData() {      
                if(this.editNotice.q_cn==''){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("newDS", "Please input");?><?php echo Yii::t("newDS", "Title (Cn)");?>'
                    });
                    return
                }
                if(this.editNotice.q_en==''){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("newDS", "Please input");?><?php echo Yii::t("newDS", "Title (En)");?>'
                    });
                    return
                }
                this.editNotice.grades=[]
                this.gradeGroupList.forEach(items => {
                    this.editNotice.grades.push.apply(this.editNotice.grades,items.checkedList)
                })
                
                // if(this.editNotice.grades.length==0){
                //     resultTip({
                //         error: 'warning',
                //         msg:'<?php echo Yii::t("newDS", "Please select");?><?php echo Yii::t("newDS", "Applicable to");?>'
                //     });
                //     return
                // }
                if(this.editNotice.summary_cn==''){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("newDS", "Please input");?><?php echo Yii::t("newDS", "Abstract (Cn)");?>'
                    });
                    return
                }
                if(this.editNotice.summary_en==''){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("newDS", "Please input");?><?php echo Yii::t("newDS", "Abstract (En)");?>'
                    });
                    return
                }
                if(this.editNotice.level==''){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("newDS", "Please select");?><?php echo Yii::t("newDS", "Level");?>'
                    });
                    return
                }
                if(this.editNotice.category==''){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("newDS", "Please select");?><?php echo Yii::t('newDS', 'Category'); ?>'
                    });
                    return
                }
                // if(this.endStatus!='0' && $('#expired_at').val()==''){     
                //     resultTip({
                //         error: 'warning',
                //         msg:'<?php echo Yii::t("newDS", "Please select");?><?php echo Yii::t("newDS", "Expire");?>'
                //     });
                //     return
                // }
                if(!this.editNotice.sign_as_uid || this.editNotice.sign_as_uid==''){
                    resultTip({
                        error: 'warning',
                        msg:'请选择署名人'
                    });
                    return
                }
                if(this.showNext && this.applicable==''){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("newDS", "Please select");?><?php echo Yii::t("newDS", "Applicable to");?>'
                    });
                    return
                }
                if(this.applicable==1){
                    this.editNotice.child_mark=[]
                   
                }else{
                    this.editNotice.grades=[]
                }
                this.disabled=true
                this.editNotice.content = tinymce.get('tinymce').getContent() 
                this.editNotice.content_en = tinymce.get('tinymceEn').getContent() 
                this.editNotice.on_top = this.editNotice.on_top ? 1 : 0
                // this.editNotice.expired_at=this.endStatus==0?0:$('#expired_at').val().trim()+' '+this.expired_atHours+':'+this.expired_atMinutes
                // this.editNotice.publish_at=this.editNotice.publish_type=='10'? $('#publish_at').val().trim()+' '+this.publish_atHours+':'+this.publish_atMinutes:''
                this.editNotice.comment_enabled=this.comment_enabled ? 1 : 0
                let that = this
                if(this.editNotice.content==''){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("newDS", "Please input");?><?php echo Yii::t("newDS", "Content (Cn)");?>'
                    });
                    that.disabled=false
                    return
                }
                if(this.editNotice.content_en==''){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("newDS", "Please input");?><?php echo Yii::t("newDS", "Content (En)");?>'
                    });
                    that.disabled=false
                    return
                }
                that.editNotice.confirm_configs=that.confirm_configs
                if (this.editNotice._id) {
                    $.ajax({
                        url: '<?php echo $this->createUrl("update") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            data: that.editNotice
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.disabled=false
                                that.isEdit=true
                                $('#editModal').modal('show') 
                                that.require_response=that.editNotice.require_response
                            } else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                                that.disabled=false
                            }
                        },
                        error: function(data) {
                            that.disabled=false
                        },
                    })
                } else { 
                    $.ajax({
                        url: '<?php echo $this->createUrl("add") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            data: that.editNotice
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.editNotice = data.data
                                // resultTip({
                                //     "msg": data.state
                                // })
                                that.disabled=false
                                that.isEdit=false
                                $('#editModal').modal('show')
                            } else {
                                resultTip({
                                    error: 'warning',
                                    msg:data.message
                                });
                                that.disabled=false
                            }
                        },
                        error: function(data) {
                            that.disabled=false
                        },
                    })
                }
            },
            uploadFile(){
                var that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/getAttachments") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        linkId: this.editNotice._id,
                        linkType:'notice'
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            data.data.data.forEach(item => {
                                item.isEdit=false
                            })
                            that.attachments=data.data.data
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/getQiniuToken") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        linkId: this.editNotice._id,
                        linkType:'notice'
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.token=data.data.data
                            config['token'] = data.data.data;
                            var uploader = new plupload.Uploader(config);
                            uploader.init();
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            delFile(list) {
                var that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/deleteAttachment") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        attachmentId:list._id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.attachments.forEach((item,index) => {
                                if (item._id==list._id) {
                                    that.attachments.splice(index, 1)
                                } 
                            })
                        } else {
                            resultTip({  
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })

            },
            saveFile(list) {
                var that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/updateAttachmentName") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        attachmentId:list._id,
                        attachmentName:this.attachmentName

                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.attachments.forEach((item,index) => {
                                if (item._id==list._id) {
                                    Vue.set(that.attachments[index], 'title', that.attachmentName);
                                    Vue.set(that.attachments[index], 'isEdit', false);
                                } 
                            })
                            resultTip({
                                msg:'<?php echo Yii::t("newDS", "Data Saved!");?>'
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            handleDragStart(e, item) {
                this.dragging = item;
            },
            handleDragEnd(e, item) {
                this.dragging = null
                var fileList=[];
                this.attachments.forEach((item,index) => {
                    fileList.push(item._id)
                })
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/sortAttachment") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        fileSortList:fileList,

                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg:'排序成功'
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })

            },
            handleDragOver(e) {
                e.dataTransfer.dropEffect = 'move' // e.dataTransfer.dropEffect="move";//在dragenter中针对放置目标来设置!
            },
            handleDragEnter(e, item) {
                e.dataTransfer.effectAllowed = "move" //为需要移动的元素设置dragstart事件
                if(item === this.dragging) {
                    return
                }
                const newItems = [...this.attachments]
                const src = newItems.indexOf(this.dragging)
                const dst = newItems.indexOf(item)
                newItems.splice(dst, 0, ...newItems.splice(src, 1))
                this.attachments = newItems
            },
            saveQuestion(type){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("updateConfirmConfig") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        [type]:this['question'+type],
                        id:this.editNotice._id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           resultTip({
                                msg:data.state
                            });
                           that.confirm_configs=data.data.data
                           that['questionT_'+type]=false
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            optionEdit(index){
                if(index!=''){
                    this.optionCn=this.optionList[index].cn
                    this.optionEn=this.optionList[index].en
                    this.optionId=this.optionList[index].id
                }else{
                    this.optionCn=''
                    this.optionEn=''
                    this.optionId=''
                }
                $('#optionModal').modal('show')
            },
            saveOption(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("updateConfirmConfigOption") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        cn:this.optionCn,
                        en:this.optionEn,
                        optionId:this.optionId,
                        id:this.editNotice._id

                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg:data.state
                            });
                            $('#optionModal').modal('hide')
                            that.optionList=data.data.data.options
                            that.confirm_configs =data.data.data
                      
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                    },
                })
            },
            delOption(data){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("deleteConfirmConfigOption") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        optionId:data.id,
                        id:this.editNotice._id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg:data.state
                            });
                            that.confirm_configs=data.data.data
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            optionStart(e, item) {
                this.dragging = item;
            },
            optionEnd(e, item) {
                this.dragging = null
                $.ajax({
                    url: '<?php echo $this->createUrl("sortConfirmConfigOption") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        sort:this.confirm_configs.sort,
                        id:this.editNotice._id

                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg:data.state
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })

            },
            optionOver(e) {
                e.dataTransfer.dropEffect = 'move' // e.dataTransfer.dropEffect="move";//在dragenter中针对放置目标来设置!
            },
            optionEnter(e, item) {
                e.dataTransfer.effectAllowed = "move" //为需要移动的元素设置dragstart事件
                if(item === this.dragging) {
                    return
                }
                const newItems = [...this.confirm_configs.sort]
                const src = newItems.indexOf(this.dragging)
                const dst = newItems.indexOf(item)
                newItems.splice(dst, 0, ...newItems.splice(src, 1))
                this.confirm_configs.sort = newItems
            },
            getSurveyList(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("surveyList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg: data.message
                            });
                            that.survey_list=data.data
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            getSurvey(){
                let that=this
                this.saveQuestionBtn=true
                $.ajax({
                    url: '<?php echo $this->createUrl("saveSurvey") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:this.editNotice._id,
                        survey_id:this.survey_id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.editNotice.survey_id=that.survey_id
                           resultTip({
                                msg: data.message
                            });
                            that.survey_url = "<?php echo Yii::app()->createUrl('//moperation/surveyReturn/questionsList'); ?>?survey_id=" + that.editNotice.survey_id
                            that.saveQuestionBtn=false

                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.saveQuestionBtn=false
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            addUser(){
                $('#authorizedModal').modal('show')
                this.searchStu=''
                this.showSelect=false
                this.options=[]
                this.$refs.inputSearch.placeholder='<?php echo Yii::t("directMessage", "Key words"); ?>'
            },
            remoteMethod(query) {
                let that=this
                if (query !== '') {       
                    that.loading = true;
                    $.ajax({
                        url: '<?php echo $this->createUrl("directMessage/teacherSearch") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            searchString:query
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.options = Object.values(data.data) ;
                                that.showSelect=true
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                            that.loading = false;
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.loading = false;
                        },
                    })
                }
            },
            confirmTeacher(item){
                this.searchStu=''
                this.teacherUid=item.uid
                this.$refs.inputSearch.placeholder=item.name
                this.showSelect=false
               
            },
            focusShow(){
                if(this.options.length!=0){
                    this.showSelect=true
                }
            },
            confirmAdmin(){
                let that=this
                if(this.editNotice.joint_admins && this.editNotice.joint_admins.indexOf(this.teacherUid)!=-1){
                    resultTip({
                        error: 'warning',
                        msg: '<?php echo Yii::t("directMessage", "Already added"); ?>'
                    });
                    return
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("jointAdminsAdd") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        noitice_id:this.editNotice._id,
                        staff_id:this.teacherUid
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.editNotice.joint_admins.push(that.teacherUid)
                           that.jointAdmins[that.teacherUid]=data.data
                           that.teacherUid=''
                           that.options=[]
                           resultTip({
                                msg:data.state
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            clearJoint(data){
                if(data){
                    $('#delModal').modal('show')
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("jointAdminsDelAll") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        noitice_id:this.editNotice._id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.editNotice.joint_admins=[]
                            resultTip({
                                msg:data.state
                            });
                            $('#delModal').modal('hide')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            delJoint(id){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("jointAdminsDel") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        noitice_id:this.editNotice._id,
                        staff_id:id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            var index = that.editNotice.joint_admins.indexOf(id);
                            if (index > -1) {
                                that.editNotice.joint_admins.splice(index, 1);
                            }
                            resultTip({
                                msg:data.state
                            });

                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
        }
    })
    var config = {
        runtimes: 'html5,flash,html4', //上传模式,依次退化
        container: 'container', //上传区域DOM ID，默认是browser_button的父元素，
        browse_button: 'pickfilesPhoto', //上传选择的点选按钮，**必需**
        token: '',
        flash_swf_url: '/js/plupload/Moxie.swf', // flash的相对地址
        auto_start: true, //选择文件后自动上传，若关闭需要自己绑定事件触发上传
        multi_selection: false, //不允许多选
        url:'https://upload-z1.qiniup.com',
        
        init: {
            'FilesAdded': function(up, files) {
                container.disabledUpload=true
                up.start();
            },
            BeforeUpload: function(up, file) {
                //设置参数
                up.setOption({
                    multipart_params:{token:container.token}
                })
            },
            'FileUploaded': function(up, file, info) {
                var response = eval('('+info.response+')');
                if(info.status == 200){   
                    response.data.isEdit=false   
                    container.attachments.push(response.data)
                    container.disabledUpload=false
                }
            },
            'Error': function(up, err, errTip) {
                if(err.response){
                    var response = eval('('+err.response+')');
                }
                else{
                    var response = {message: err.message};
                }
                if(response.error == 'token not specified'){
                    up.stop();
                    $.ajax({
                        url: '<?php echo $this->createUrl("getQiniuToken") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            editNoticeId: container.editNotice._id
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                container.uptoken=data.data.data
                                config['uptoken'] = data.data.data;
                                up.setOption("multipart_params", {"token":data.data.token,})
                                up.start();
                            } else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.msg
                                });
                            }
                        },
                        error: function(data) {

                        },
                    })
                }
            },
            'UploadComplete': function(up, file) {
                //队列文件处理完毕后,处理相关的事情
            }
        }
    };
</script>