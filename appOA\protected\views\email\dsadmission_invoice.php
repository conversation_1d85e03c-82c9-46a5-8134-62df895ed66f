<div style="color:#274257;">
    <div style="font-family: 'Microsoft Yahei'">
    	<p>Dear parents,</p>
    	<p>We have received your application. Thank you for considering Daystar Academy for your child's education.</p>
    	<p>The next step in our application process is for you to pay a application fee. You are welcome to pay via bank transfer, WeChat, or directly at our school via cash and credit card. You can find the bank information below.</p>
    	<p>Once we received your application fee, we will send you a confirmation letter and set up an interview for your child to meet one of our English and Chinese teachers. Interview will start January 2019. Once the schedule is confirmed we will inform you about the date and time via email. </p>
    	<p>Bank information:</p>
    	<table class="table" border="1" style="border-collapse: collapse;">
    		<tr>
    			<td>
    				<p>Beneficiary:</p>
    				<p>收款人：</p>
    			</td>
    			<td>
    				<p>DAYSTAR ACADEMY</p>
    				<p>北京市朝阳区启明星学校</p>
    			</td>
    		</tr>
    		<tr>
    			<td>
    				<p>Bank/Branch：</p>
    				<p>银行/分行</p>
    			</td>
    			<td>
    				<p>YANSHA BRANCH OF INDUSTRIAL AND COMMERCIAL BANK OF CHINA, BEIJING, PRC</p>
    				<p>中国工商银行股份有限公司北京燕莎支行</p>
    			</td>
    		</tr>
    		<tr>
    			<td>
    				<p>Account Number:</p>
    				<p>帐户号码：</p>
    			</td>
    			<td>
    				<p>0200012719201171490</p>
    			</td>
    		</tr>
    		<tr>
    			<td>
    				<p>Swift Code：</p>
    				<p>银行国际代码：</p>
    			</td>
    			<td>
    				<p>ICBKCNBJBJM</p>
    			</td>
    		</tr>
    	</table>

		<p>Wechat Pay(Valid for 2 hours)</p>
    	<p>
    		<?php
    			$QRcodeUrl = $data['QRcodeUrl'];
    			if (!empty($QRcodeUrl)) {
    			    $filename = $data['childid'] .'_'. uniqid();
    			    $this->widget('common.extensions.qrcode.QRCodeGenerator',array(
    			        'data' => $QRcodeUrl,
    			        'subfolderVar' => true,
    			        'filename' => $filename,
    			        'filePath' => Yii::app()->params['OAUploadBasePath'],
    			        'fileUrl' => Yii::app()->params['OAUploadBaseUrl'],
    			    ));
    			}else{
    		          echo Yii::t('wechat', 'Error in payment process. Please try again.');
    		    }
    		 ?>
    	</p>

		<p>Should you have any questions, please do not hesitate to contact us.</p>
		<p>Daystar Academy,</p>
		<p>Admissions Team</p>
		<p>(010) 5603-9446</p>
		<p><EMAIL></p>


		<p>亲爱的家长：</p>
		<p>感谢您申请启明星学校！您的申请资料我们已经收到。</p>
		<p>申请流程的下一步是提交申请费，您可通过银行转账、微信付款、到学校支付现金或者刷卡支付申请费。银行信息如下。</p>
		<p>我们在收到您的申请费后安排学生进行中英文面试。面试将于2019年1月开始，我们将会以邮件的形式通知您具体面试时间。</p>
		<p>银行转账：</p>
    	<table class="table" border="1" style="border-collapse: collapse;">
    		<tr>
    			<td>
    				<p>Beneficiary:</p>
    				<p>收款人：</p>
    			</td>
    			<td>
    				<p>DAYSTAR ACADEMY</p>
    				<p>北京市朝阳区启明星学校</p>
    			</td>
    		</tr>
    		<tr>
    			<td>
    				<p>Bank/Branch：</p>
    				<p>银行/分行</p>
    			</td>
    			<td>
    				<p>YANSHA BRANCH OF INDUSTRIAL AND COMMERCIAL BANK OF CHINA, BEIJING, PRC</p>
    				<p>中国工商银行股份有限公司北京燕莎支行</p>
    			</td>
    		</tr>
    		<tr>
    			<td>
    				<p>Account Number:</p>
    				<p>帐户号码：</p>
    			</td>
    			<td>
    				<p>0200012719201171490</p>
    			</td>
    		</tr>
    		<tr>
    			<td>
    				<p>Swift Code：</p>
    				<p>银行国际代码：</p>
    			</td>
    			<td>
    				<p>ICBKCNBJBJM</p>
    			</td>
    		</tr>
    	</table>

		<p>微信付款（有效期2小时）：</p>
		<p>
			<?php
				$QRcodeUrl = $data['QRcodeUrl'];
				if (!empty($QRcodeUrl)) {
				    $this->widget('common.extensions.qrcode.QRCodeGenerator',array(
				        'data' => $QRcodeUrl,
				        'subfolderVar' => true,
				        'filename' => $filename,
				        'filePath' => Yii::app()->params['OAUploadBasePath'],
				        'fileUrl' => Yii::app()->params['OAUploadBaseUrl'],
				    ));
				}else{
			          echo Yii::t('wechat', 'Error in payment process. Please try again.');
			    }
			 ?>
		</p>
		<p>如果您有任何疑问请联系我们！</p>
		<p>启明星学校招生部</p>
		<p>tel: (010)5603-9446</p>
		<p>email: ）<EMAIL></p>

	</div>
</div>
