<style>
 [v-cloak] {
            display: none;
}
.reportTbody > tr > td, .reportTbody > tr > th {
    vertical-align: middle !important;
}
</style>
<div class="container-fluid">
<ol class="breadcrumb">
    <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
    <li><?php echo CHtml::link(Yii::t('site', 'Routines'), array('//mcampus/default/index')) ?></li>
    <li class="active"><?php echo Yii::t('site', 'ASA Management') ?></li>
</ol>

<div class="row">
    <div class="col-md-2 mb10">
        <?php
        $this->widget('zii.widgets.CMenu', array(
            'items' => $this->module->getMenu(),
            'id' => 'pageCategory',
            'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
            'activeCssClass' => 'active',
        ));
        ?>
    </div>
    <div class="col-md-2">
        <?php
        $this->widget('zii.widgets.CMenu', array(
            'items' => $this->module->getAttendSubMenu(),
            'id' => 'pageCategory',
            'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
            'activeCssClass' => 'active',
        ));
        ?>
    </div>
    <div class="col-md-8" id='datalist' v-cloak >
        <div class=" tablelists">
        <div class="panel panel-default panel-body" v-if="Object.keys(list.personal).length==0 && Object.keys(list.company).length==0">
        暂无数据
        </div>
        <div v-else>
            <div class="panel panel-default"   v-if="Object.keys(list.personal).length!=0 ">
            <div class="panel-heading">为个人申请报销</div>
            <table class="table">
                <thead>
                    <colgroup>
                        <col style="width:40%">
                        <col style="width:20%">
                        <col style="width:40%">
                    </colgroup>
                </thead>
                <tbody class="reportTbody">
                <tr  v-for="(tData,cid,index) in list.personal">
                    <td width="50">{{tData.name}}</td>
                    <td width="50">
                        <div  v-for='(list,index) in tData.months'>
                            <div class="checkbox">
                                <label>
                                    <input v-if="list.status == 30" type="checkbox" :data-name="'item_id[' + tData.vendor_id + '][]'"  :value="list.amount" :data-id='list.month' :data-vendorid='tData.vendor_id' class="xz"  @click='expensecheck(tData.vendor_id)'>{{list.month}}
                                </label>
                                <div class="text-right pull-right"> {{list.amount}} </div>
                            </div>
                        </div>
                        <div class="checkbox">
                            <label>
                                总额
                            </label><div class="text-right pull-right zonge">0</div>
                        </div>
                    </td>
                     <td width="50"></td>
                </tr>
                <tr class="mb15">
                    <td></td>
                    <td></td>
                    <td class="mb15 text-right form-group " >
                    <div v-if="Object.keys(list.personal).length!=0" class="mb15 mt15">
                        <div class="btn-group">
                            <button type="button" class="btn btn-primary btn btn-sm">结算为某月工资</button>
                            <button type="button" class="btn btn-primary btn btn-sm dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <span class="caret"></span>
                                <span class="sr-only">Toggle Dropdown</span>
                            </button>
                            <ul id="mon" class="dropdown-menu">
                               <li v-for='(mon,index) in list.settle_month'><a href="javascript:;" @click='settle(mon)'>{{mon}}</a></li>
                            </ul>
                         <div class="col-md-1"></div>
                         <button @click='expense()' role="button" class="btn btn-sm  btn-primary modifybtn" data-toggle="modal"> 申请报销 </button>
                        </div>
                    </div>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>

        <div class="clearfix"></div>
        <p  class="form-group "></p>

        <div class="panel panel-default"  v-if="Object.keys(list.personalArr).length!=0"  >
            <div class="panel-heading">结算到某月切未提交</div>
            <table class="table">
                <thead>
                <colgroup>
                    <col style="width:40%">
                    <col style="width:20%">
                    <col style="width:40%">
                </colgroup>
                </thead>
                <tbody class="reportTbody">
                <tr  v-for="(tData,cid,index) in list.personalArr">
                    <td width="50">{{tData.name}}</td>
                    <td width="50">
                        <div class="checkbox">
                            <label>
                                所属月份
                            </label>
                            <label>
                                结算月份
                            </label>
                            <div class="text-right pull-right">钱数</div>
                        </div>
                        <div  v-for='(list,index) in tData.months'>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" :name='tData.vendor_id' :data-name="'item_id[' + tData.vendor_id + '][]'" :data-month="list.month" :data-vendorId="tData.vendor_id"  :value='list.id' class="monTh">{{list.month}}
                                </label>
                                <label>
                                    {{list.money}}
                                </label>
                                <div class="text-right pull-right"> {{list.amount}} </div>
                            </div>
                        </div>
                    </td>
                    <td width="50">
                        <button role="button" @click="qxpayment(tData.vendor_id)" class="btn btn-sm  btn-primary modifybtn pull-right" data-toggle="modal">取消结算</button>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>

        <div class="clearfix"></div>
        <p  class="form-group "></p>

        <div class="panel panel-default"  v-if="Object.keys(list.company).length!=0"  >
            <div class="panel-heading">为公司申请付款</div>
            <table class="table">
                <thead>
                    <colgroup>
                        <col style="width:40%">
                        <col style="width:20%">
                        <col style="width:40%">
                    </colgroup>
                </thead>
                <tbody class="reportTbody">
                    <tr  v-for="(tData,index) in list.company">
                        <td width="50">{{tData.name}}</td>
                    <td width="50">
                        <div  v-for='(list,index) in tData.months' >
                            <div class="checkbox">
                                <label>
                                    <input v-if="list.status == 30" type="checkbox" :name='tData.vendor_id' :data-name="'item_id[' + tData.vendor_id + '][]'" :data-month="list.month" :data-vendorId="tData.vendor_id" @click='paycheck(tData.vendor_id)' :value='list.amount'>
                                    <span  v-if="list.status != 30" style="color: red">{{list.month}}</span>
                                    <span  v-else>{{list.month}}</span>
                                </label>
                                <div class="text-right pull-right"> {{list.amount}} </div>
                            </div>
                        </div>
                        <div class="checkbox">
                            <label>
                            总额
                            </label><div class="text-right pull-right payzong">0</div>
                        </div>
                    </td>
                    <td width="50">
                        <button role="button" @click="payment(tData.vendor_id)" class="btn btn-sm  btn-primary modifybtn pull-right" data-toggle="modal">申请付款</button>
                    </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" data-keyboard="false" data-backdrop="static">
     <div class="modal-dialog">
      <?php $form = $this->beginWidget('CActiveForm', array(
        'id' => 'expense-forms',
        'enableAjaxValidation' => false,
        'action' => ($model->id) ? $this->createUrl('expenesAdd', array('expenesId' => $model->id)) : $this->createUrl('saveApplication'),
        'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form', 'enctype' => "multipart/form-data"),
    )); ?>
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×
                </button>
                <h4 class="modal-title" id="myModalLabel">
                    {{expenses.title}}
                </h4>
            </div>
            <div class="modal-body">
                <div class="form-horizontal">
                  <div class="form-group">
                    <label class="col-xs-2 control-label"><label for="AsaRefund_course_id">选择收款人</label></label>
                    <div class="col-xs-10 control-label">
                       <select id="group-name" class="form-control ol-xs-5" v-model="couponSelected" >
                                <option  :value='list' v-for='list in expenses.staff'>{{list.name}}</option>
                        </select>
                    </div>
                 </div>
                 <input type="text" :value='couponSelected.vendor_id' name="vendor_id" class="hidden">
                 <div v-for='(tds,i) in baoxiaobtn'  class="hidden">
                  <input type="text" :value="tds.val" :name="tds.name">
                   </div>
                 <input type="text" :value="expenses.acmount" name="amount"  class="hidden">
                 <input type="text" value="1" name="type"  class="hidden">
      

                 <div class="form-group">
                    <label class="col-xs-2 control-label"><label for="AsaRefund_course_id">收款方户名</label></label>
                    <div class="col-xs-10 control-label">
                        <label class="pull-left">{{couponSelected.account_name}}</label>
                    </div>
                 </div>
                 <div class="form-group">
                    <label class="col-xs-2 control-label"><label for="AsaRefund_course_id">收款方账户</label></label>
                    <div class="col-xs-10 control-label">
                        <label class="pull-left">{{couponSelected.account_number}}</label>
                    </div>
                 </div>
                 <div class="form-group">
                    <label class="col-xs-2 control-label"><label for="AsaRefund_course_id">开户行</label></label>
                    <div class="col-xs-10 control-label">
                        <label class="pull-left">{{couponSelected.bank_name}}</label>
                    </div>
                 </div>
                 <div class="form-group">
                    <label class="col-xs-2 control-label"><label for="AsaRefund_course_id">金额</label></label>
                    <div class="col-xs-10 control-label">
                        <label class="pull-left">{{expenses.acmount}}</label>
                    </div>
                 </div>
                 <div class="form-group">
                    <label class="col-xs-2 control-label"><label for="AsaRefund_course_id">标题</label></label>
                    <div class="col-xs-10 control-label">
                        <input type="text" class="form-control" id="extitle" :value="expenses.title" name="title">
                    </div>
                 </div>
                   <div class="form-group">
                        <label class="col-md-2 control-label">报销文件</label>

                        <div class="col-md-9">
                            <input class="inputfile" onchange="uploadata(this,'myModal')" type="file">

                            <p class="help-block">报销说明，行政付款申请等附加材料</p>
                            <p class="loadmyModal" class="mb5"></p>
                            <div class="attachment myModal" class="mb5">
                            </div>
                            <span class="fileinfo" class="text-warning"></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
               
                </button>
              
                <button type="submit" class="btn btn-primary J_ajax_submit_btn">提交</button>
  <button type="button" class="btn btn-default" data-dismiss="modal">
                    取消
                </button>
    <input type="reset" class="_reset" style="display: none"/>
                
            </div>
        </div>
         <?php $this->endWidget(); ?>
     </div>
    </div>
    <div class="modal fade" id="pay" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog">
         <?php $form = $this->beginWidget('CActiveForm', array(
        'id' => 'expense-forms',
        'enableAjaxValidation' => false,
        'action' => ($model->id) ? $this->createUrl('expenesAdd', array('expenesId' => $model->id)) : $this->createUrl('saveApplication'),
        'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form', 'enctype' => "multipart/form-data"),
    )); ?>
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×
                    </button>
                    <h4 class="modal-title" id="myModalLabel">
                      {{pay.title}}
                    </h4>
                </div>
                <div class="modal-body">
                    <div class="form-horizontal">
                     <div v-for='(tds,i) in paybtn'>
                  <input type="text" :value="tds.val" :name="tds.name"  class="hidden">
                   </div>
                   <input type="text" :value='pay.vendor_id' name="vendor_id"  class="hidden">
                    <input type="text" :value="pay.acmount" name="amount"  class="hidden">
                 <input type="text" value="2" name="type"  class="hidden">
                    <div class="form-group">
                        <label class="col-xs-2 control-label"><label for="AsaRefund_course_id">收款方户名</label></label>
                        <div class="col-xs-10 control-label">
                            <label class="pull-left">{{pay.account_name}}</label>
                        </div>
                     </div>
                     <div class="form-group">
                        <label class="col-xs-2 control-label"><label for="AsaRefund_course_id">收款方账户</label></label>
                        <div class="col-xs-10 control-label">
                            <label class="pull-left">{{pay.account_number}}</label>
                        </div>
                     </div>
                     <div class="form-group">
                        <label class="col-xs-2 control-label"><label for="AsaRefund_course_id">开户行</label></label>
                        <div class="col-xs-10 control-label">
                            <label class="pull-left">{{pay.bank_name}}</label>
                        </div>
                     </div>
                     <div class="form-group">
                        <label class="col-xs-2 control-label"><label for="AsaRefund_course_id">金额</label></label>
                        <div class="col-xs-10 control-label">
                            <label class="pull-left">{{pay.acmount}}</label>
                        </div>
                     </div>
                     <div class="form-group">
                        <label class="col-xs-2 control-label"><label for="AsaRefund_course_id">标题</label></label>
                        <div class="col-xs-10 control-label">
                            <input type="text" class="form-control" id="paytitle" :value="pay.title" name="title">
                        </div>
                     </div>

                       <div class="form-group">
                            <label class="col-md-2 control-label">付款文件</label>

                            <div class="col-md-9">
                                <input class="inputfile" onchange="uploadata(this,'pay')" type="file">

                                <p class="help-block">付款说明，行政付款申请等附加材料</p>
                                <p class="loadpay" class="mb5"></p>
                                <div class="attachment pay" class="mb5">
                                </div>
                                <span class="fileinfo" class="text-warning"></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary paybtnsub J_ajax_submit_btn">
                        提交
                    </button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">
                        取消
                    </button>
                     <input type="reset" class="_reset" style="display: none"/>
                
            </div>
                </div>
                  <?php $this->endWidget(); ?>
            </div>
        </div>
    </div>
        </div>

    </div>

    <script>
     var data=<?php echo json_encode($data)?>;
     var datalist = new Vue({
        el: "#datalist",
        data: {  
            expenses:'',  //报销      
            list:data,//列表
            pay:'',//付款
            couponSelected:{},//报销收款人
            baoxiaobtn:[],
            paybtn:[],
        },
        methods: {
            parsefloat(num) {
                return parseFloat(num);
            },
            and(add) {
               var sum = 0;
              for(var i= 0 ;i< add.length; i++) {
                    sum += parseInt(add[i]);
              }
              return sum;
            },
            expensecheck(e){
                var target=event.target;
                var dataid=e;
                var vals=[]
                 if ($(target).is(':checked')) {                   
                     $(target).addClass("bgRed"+dataid)
                     $(target).addClass("bgRed")
                     $(".bgRed"+dataid).each(function(){                   
                          vals.push(parseInt($(this).val()))
                     });
                   var sum = 0;
                  for(var i= 0 ;i< vals.length; i++) {
                        sum += parseInt(vals[i]);
                  }
                   $(target).parent().parent().parent().parent().find('.zonge').html(sum)

                } else {
                   $(target).removeClass("bgRed"+dataid)
                   $(target).removeClass("bgRed")
                    $(".bgRed"+dataid).each(function(){                   
                          vals.push(parseInt($(this).val()))
                    });
                     var sum = 0;
                  for(var i= 0 ;i< vals.length; i++) {
                        sum += parseInt(vals[i]);
                  }
                   $(target).parent().parent().parent().parent().find('.zonge').html(sum)
                  }
            },
            paycheck(e){
                var target=event.target;
                var dataid=e;
                var vals=[]
                if ($(target).is(':checked')) {
                   
                    $(target).addClass("pasment"+dataid)
                    $(".pasment"+dataid).each(function(){                   
                          vals.push(parseInt($(this).val()))
                    });
                   var sum = 0;
                  for(var i= 0 ;i< vals.length; i++) {
                        sum += parseInt(vals[i]);
                  }

                   $(target).parent().parent().parent().parent().find('.payzong').html(sum)

                } else {
                   $(target).removeClass("pasment"+dataid)
                   $(target).removeClass("pasment")
                    $(".pasment"+dataid).each(function(){                   
                          vals.push(parseInt($(this).val()))
                    });
                     var sum = 0;
                  for(var i= 0 ;i< vals.length; i++) {
                        sum += parseInt(vals[i]);
                  }
                   $(target).parent().parent().parent().parent().find('.payzong').html(sum)
                  }
            },
            settle(mon){
                var item_id=[]
                var vendor_id=[]
                $(".bgRed").each(function(){                     
                   vendor_id.push($(this).attr('data-vendorid'))
                    item_id.push($(this).attr('data-id'))
                });
                 if(item_id.length==0){
                  resultTip({error: 'warning', msg:'请选择月份'})
                  return
               }
                var settle_data=[]
               settle_data = vendor_id.map((key,value)=>[key,item_id[value]]);
                $.ajax({
                        url:'<?php echo $this->createUrl('SaveSettle'); ?>',
                        type: 'post',
                        dataType:'json',
                        data:{
                            item_id:settle_data,
                            setMonth:mon
                        },
                        success:function(data){
                       
                       
                         if(data.state=='success'){
                            resultTip({"msg": data.message})
                             setTimeout(function(){
                               reloadPage(window);
                              },2000);
                         }else{
                            resultTip({error: 'warning', msg: data.message});
                         }
                           
                        },error:function(data){
                            resultTip({error: 'warning', msg: '请求错误'});
                        }
                    })
            },
            expense(){
                var item_id= []
                var vendor_id=[]
                var baoxiaonames=[]
                $(".bgRed").each(function(){                     
                    vendor_id.push($(this).attr('data-vendorid'))
                    item_id.push($(this).attr('data-id'))
                    baoxiaonames.push({"name":$(this).attr('data-name'),"val":$(this).attr('data-id')});
                });
               if(item_id.length==0){
                   resultTip({error: 'warning', msg:'请选择月份'})
                  return
               }
               var expense_data=[]
               expense_data = vendor_id.map((key,value)=>[key,item_id[value]]);
               this.baoxiaobtn=baoxiaonames
                $.ajax({
                        url:'<?php echo $this->createUrl('Application'); ?>',
                        type: 'post',
                        dataType:'json',
                        data:{
                           
                            item_id:expense_data,
                        },
                        success:function(data){
                       
                       
                         if(data.state=='success'){
                             $('#myModal').modal('show')
                             datalist.expenses=data.date
                             datalist.expenses.staff.unshift({ name:"请选择"})
                              datalist.couponSelected =datalist.expenses.staff[0]
                         }else{
                            resultTip({error: 'warning', msg: data.message});
                         }
                           
                        },error:function(data){
                            resultTip({error: 'warning', msg: '请求错误'});
                        }
                    })
            },
            qxpayment(vendor_id){
              var datamon=[]
                $("input[name="+vendor_id+"]:checkbox:checked").each(function(){
                   datamon.push($(this).val()) 
               });
                $.ajax({
                    url:'<?php echo $this->createUrl('deleteVariation'); ?>',
                    type: 'post',
                    dataType:'json',
                    data:{
                        variation_id:datamon,
                    },
                    success:function(data){
                        if(data.state=='success'){
                            resultTip({"msg": data.message})
                             setTimeout(function(){
                               reloadPage(window);
                              },2000);
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }

                    },error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            payment(vendor_id){
                var item=[]
                var pay_vendor=[]
                var names=[]
                $("input[name="+vendor_id+"]:checkbox:checked").each(function(){
                   item.push($(this).attr('data-month')) 
                    pay_vendor.push($(this).attr('data-vendorid'))
                    names.push({"name":$(this).attr('data-name'),"val":$(this).attr('data-month')});
               });
                if(item.length==0){
                  resultTip({error: 'warning', msg:'请选择月份'})
                  return
               }

                var pay_data=[]
               pay_data = pay_vendor.map((key,value)=>[key,item[value]]);
             
               this.paybtn=names

               $.ajax({
                    url:'<?php echo $this->createUrl('applicationFirm'); ?>',
                    type: 'post',
                    dataType:'json',
                    data:{
                        item_id:pay_data,
                    },
                    success:function(data){
                  
                    
                     if(data.state=='success'){
                        $('#pay').modal('show')
                        $('.text-warning').html('')
                        $(".attachment").html('');
                       datalist.pay=data.date
                     }else{
                        resultTip({error: 'warning', msg: data.message});
                     }
                       
                    },error:function(data){
                       resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
        }
      });
//报销1 付款2
   function uploadata(obj,id) {
        $(".fileinfo").text('');
       
        var data = new FormData();
        $.each($(obj)[0].files, function (i, file) {   
       
             data.append('file', file); 
        });
        $.ajax({
            url: '<?php echo $this->createUrl('ExpenseFiles'); ?>',
            type: 'post',
            dataType: 'json',
            data:data,
            contentType: false,    //不可缺
            processData: false,    //不可缺
            beforeSend: function() {
               $('.load'+id).append('上传中...');
            },
            success: function (data) {
                if (data.msg == 'success') {
                    //清空文件域
                   $(obj).after($(obj).clone().val(""));
                    $(obj).remove();
                    //显示附件
                    var atta = '<p class="visible-lg-inline-block mr5'+id+'"><a class="btn btn-info btn-xs" target="_blank" href="' + data.url + '">' + data.saveName + '</a><input type="hidden" name="attachment[]" class="eaFile" value="' + data.saveName + '" > <span class="btn btn-danger btn-xs '+id+'delfile" _value="' + data.saveName + '"><i class="glyphicon glyphicon-remove"></i></span></p> ';
                    $('.'+id).append(atta);
                    if(id=='pay'){
                        var paypic=[]
                        $(".mr5pay").each(function(){                     
                            paypic.push($(this).find('a').html())
                        });
                      
                        datalist.paypic=paypic
                    }else{
                        var mr5myModal=[]
                        $(".mr5myModal").each(function(){                                         
                            mr5myModal.push($(this).find('a').html())
                        });
                      
                        datalist.baoxiaopic=mr5myModal
                     }
                   
                } else {
                    $(".fileinfo").text(data.msg);
                }
            },
            complete: function () {
                 $('.load'+id).remove();
            },
            error: function (data) {
                $(".fileinfo").text(data.msg);
            }
        });
      // $(".inputfile").after($(obj).clone().val(""));
      //       $(obj).remove();
    }
    $(document).on('click', '.paydelfile', function () {
        var _this = $(this);
        var filename = $(this).attr('_value');
        $.ajax({
            url: '<?php echo $this->createUrl('DelFiles'); ?>',
            data: {fileName: filename},
            type: 'post',
            dataType: 'json',
            success: function (data) {
                if (data.state == 'success') {
                    _this.parent().remove()
                     var paypic=[]
                        $(".mr5pay").each(function(){                     
                            paypic.push($(this).find('a').html())
                        });
                       
                        datalist.paypic=paypic
                } else {
                    $(".fileinfo").text(data.message);
                }
            },
            error: function (data) {
                $(".fileinfo").text(data.message);
            }
        });
    })
     $(document).on('click', '.myModaldelfile', function () {
        var _this = $(this);
        var filename = $(this).attr('_value');
        $.ajax({
            url: '<?php echo $this->createUrl('DelFiles'); ?>',
            data: {fileName: filename},
            type: 'post',
            dataType: 'json',
            success: function (data) {
                if (data.state == 'success') {
                    _this.parent().remove()
                     var mr5myModal=[]
                    $(".mr5myModal").each(function(){                                         
                        mr5myModal.push($(this).find('a').html())
                    });
                   
                     datalist.baoxiaopic=mr5myModal
                } else {
                    $(".fileinfo").text(data.message);
                }
            },
            error: function (data) {
                $(".fileinfo").text(data.message);
            }
        });
    })
      function cbVisit() {
        setTimeout(function(){
          window.location.reload(true);
          },1000);
        }
    </script>                
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>