<?php

/**
 * This is the model class for table "ivy_reg_config".
 *
 * The followings are the available columns in table 'ivy_reg_config':
 * @property integer $id
 * @property integer $reg_id
 * @property integer $step
 * @property string $schoolid
 * @property string $content_cn
 * @property string $content_en
 * @property integer $updated_at
 * @property integer $updated_by
 * @property integer $created_at
 * @property integer $created_by
 */
class RegConfig extends CActiveRecord
{
    public $welcome_cn;
    public $welcome_en;
    public $bus_cn;
    public $bus_en;
    public $card_cn;
    public $card_en;
    public $fee_cn;
    public $fee_en;
    public $uniform_primary_man_cn;
    public $uniform_primary_woman_cn;
    public $uniform_primary_man_en;
    public $uniform_primary_woman_en;
    public $lang_cn;
    public $lang_en;
    public $lunch_cn;
    public $lunch_en;

    public $medical_cn;
    public $medical_en;
    public $medical_kindergarten_cn;
    public $medical_kindergarten_en;
    public $medical_content_cn;
    public $medical_content_en;

    public $uniform_man_cn;
    public $uniform_woman_cn;
    public $uniform_man_en;
    public $uniform_woman_en;
    public $safe_cn;
    public $safe_en;

    public $student_cn;
    public $student_en;

    public $parents_cn;
    public $parents_en;

    public $material_cn;
    public $material_en;
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_reg_config';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('reg_id, step, updated_at, updated_by, created_at, created_by', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>255),
			array('content_cn, content_en', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, reg_id, step, schoolid, content_cn, content_en, updated_at, updated_by, created_at, created_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'reg_id' => 'Reg',
			'step' => 'Step',
			'schoolid' => 'Schoolid',
			'content_cn' => 'Content Cn',
			'content_en' => 'Content En',
			'updated_at' => 'Updated At',
			'updated_by' => 'Updated By',
			'created_at' => 'Created At',
			'created_by' => 'Created By',

            'welcome_cn' => Yii::t('reg','中文内容'),
            'welcome_en' => Yii::t('reg','英文内容'),
            'bus_cn' => Yii::t('reg','中文内容'),
            'bus_en' => Yii::t('reg','英文内容'),
            'card_cn' => Yii::t('reg','中文内容'),
            'card_en' => Yii::t('reg','英文内容'),
            'fee_cn' => Yii::t('reg','中文内容'),
            'fee_en' => Yii::t('reg','英文内容'),
            'lang_cn' => Yii::t('reg','中文内容'),
            'lang_en' => Yii::t('reg','英文内容'),
            'lunch_cn' => Yii::t('reg','中文内容'),
            'lunch_en' => Yii::t('reg','英文内容'),

            'medical_cn' => Yii::t('reg','中小学中文内容'),
            'medical_en' => Yii::t('reg','中小学英文内容'),
            'medical_kindergarten_cn' => Yii::t('reg','幼儿园中文内容'),
            'medical_kindergarten_en' => Yii::t('reg','幼儿园英文内容'),
            'medical_content_cn' => Yii::t('reg','家长协议中文'),
            'medical_content_en' => Yii::t('reg','家长协议英文'),

            'uniform_primary_man_cn' => Yii::t('reg','中学男中文内容'),
            'uniform_primary_man_en' => Yii::t('reg','中学男英文内容'),
            'uniform_primary_woman_cn' => Yii::t('reg','中学女中文内容'),
            'uniform_primary_woman_en' => Yii::t('reg','中学女英文内容'),
            'uniform_man_cn' => Yii::t('reg','小学男中文内容'),
            'uniform_man_en' => Yii::t('reg','小学男英文内容'),
            'uniform_woman_cn' => Yii::t('reg','小学女中文内容'),
            'uniform_woman_en' => Yii::t('reg','小学女英文内容'),


            'safe_cn' => Yii::t('reg','中文内容'),
            'safe_en' => Yii::t('reg','英文内容'),

            'student_cn' => Yii::t('reg','中文内容'),
            'student_en' => Yii::t('reg','英文内容'),

            'parents_cn' => Yii::t('reg','中文内容'),
            'parents_en' => Yii::t('reg','英文内容'),

            'material_cn' => Yii::t('reg','中文内容'),
            'material_en' => Yii::t('reg','英文内容'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('reg_id',$this->reg_id);
		$criteria->compare('step',$this->step);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('content_cn',$this->content_cn,true);
		$criteria->compare('content_en',$this->content_en,true);
		$criteria->compare('updated_at',$this->updated_at);
		$criteria->compare('updated_by',$this->updated_by);
		$criteria->compare('created_at',$this->created_at);
		$criteria->compare('created_by',$this->created_by);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return RegConfig the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	public static function getContent($reg_id, $step)
	{
		$criteria = new CDbCriteria();
		$criteria->compare('reg_id', $reg_id);
		$criteria->compare('step', $step);
		$model = RegConfig::model()->find($criteria);
		$data = array();
		if ($model) {
			$data['cn'] = json_decode($model->content_cn, true);
			$data['en'] = json_decode($model->content_en, true);
		}
		return $data;
	}
}
