<?php

class PayController extends BranchBasedController
{
    public $printFW = array();
    public $report;

    // 访问action的初级权限
    public $actionAccessAuths = array(
        'directorCheck' => 'ivystaff_cd',
        'overrule' => 'ivystaff_cd',
    );

    public function createUrl($route, $params = array(), $ampersand = '&', $parentOnly = false)
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        if (empty($params['month'])) {
            $params['month'] = $this->report->report_month;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        $this->branchSelectParams['urlArray'] = array('//masa/pay/index');

        // jquery ui
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl.'/modern/css/wizard/bootstrap-nav-wizard.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue2.js');

        // 初始化
        Yii::import('common.models.asainvoice.*');
        $month = Yii::app()->request->getParam('month', date('Ym', time()));
        $schoolid = Yii::app()->request->getParam('branchId');
        $monthTimestamp = strtotime($month . '01');
        if (!$monthTimestamp) {
            return false;
        }
        $month = date('Ym', $monthTimestamp);
        $this->report = AsaMonthReport::model()->findByAttributes(array('report_month'=>$month, 'schoolid' => $schoolid));
        if (!$this->report) {
            $this->report = new AsaMonthReport();
            $this->report->schoolid = $schoolid;
            $this->report->report_month = $month;
            $this->report->total_amount = 0;
            $this->report->unsettle_amount = 0;
            $this->report->status = AsaMonthReport::PREPAR;
            $this->report->create_time = time();
            $this->report->update_time = time();
            $this->report->uid = $this->staff->uid;
            if (!$this->report->save()) {
                return false;
            }
        }
    }

	public function actionIndex()
    {
        $this->render('index');
    }

    public function actionSummaryTable()
    {
        $this->render('table');
    }

    public function actionGetIndexData()
    {
        $data = array(1=>array(), 2=>array(), 3=>array());
        $by_month = $this->report->getFreelancerFee();
        $by_count = $this->report->getFreelancerDividedFee();

        foreach ($by_month as $vendorType => $items) {
            foreach ($items as $vendorId => $item) {
                $data[$vendorType][$vendorId] = $item;
                $data[$vendorType][$vendorId]['amount'] += $item['variation_amout'];
                if ($vendorType == 1) {
                    $data[$vendorType][$vendorId]['settle'] = 1;
                }
                if (isset($by_count[$vendorType][$vendorId])) {
                    if (!isset($data[$vendorType][$vendorId]['courses'])) {
                        $data[$vendorType][$vendorId]['courses'] = array();
                    }
                    $data[$vendorType][$vendorId]['amount'] += $by_count[$vendorType][$vendorId]['amount'];
                    $data[$vendorType][$vendorId]['courses'] += $by_count[$vendorType][$vendorId]['courses'];
                    unset($by_count[$vendorType][$vendorId]);
                }
                foreach ($data[$vendorType][$vendorId]['courses'] as $course) {
                    $data[$vendorType][$vendorId]['amount'] -= $course['assistant_pay'];
                }
            }
        }
        foreach ($by_count as $vendorType => $items) {
            foreach ($items as $vendorId => $item) {
               $data[$vendorType][$vendorId] = $item;
                if ($vendorType == 1) {
                    $data[$vendorType][$vendorId]['settle'] = 1;
                }
               $data[$vendorType][$vendorId]['amount'] += $item['variation_amout'];
               foreach ($by_count[$vendorType][$vendorId]['courses'] as $course) {
                   $data[$vendorType][$vendorId]['amount'] -= $course['assistant_pay'];
               }
            }
        }
        // 1内部个人，2外部公司，3外部个人
        sort($data[1]);
        sort($data[2]);
        sort($data[3]);
        $this->addMessage('state', 'success');
        $this->addMessage('message', 'success');
        $this->addMessage('data', $data);
        $this->showMessage();
    }

    public function actionSaveIndividual()
    {
        if (Yii::app()->request->isPostRequest) {
            $this->addMessage('state', 'fail');
            if ($this->report->status != AsaMonthReport::PREPAR) {
                $this->addMessage('message', '报告已提交');
                $this->showMessage();
            }
            $id = Yii::app()->request->getParam('id');
            $settle = Yii::app()->request->getParam('settle');
            $amount = Yii::app()->request->getParam('amount');
            $memo = Yii::app()->request->getParam('memo');
            $amount_item = Yii::app()->request->getParam('amount_item');
            $data = array();
            foreach ($id as $i => $v) {
                $data[$v]['settle'] = $settle[$i];
                $data[$v]['amount'] = $amount[$i] ? $amount[$i] : 0;
                $data[$v]['memo'] = $memo[$i];
                $data[$v]['amount_item'] = array();
                if (isset($amount_item[$v])) {
                    $data[$v]['amount_item'] = $amount_item[$v];
                }
            }
            // 保存调整数据
            $this->report->saveVariation($data);
            // 生成报告数据
            // 生成内部个人固定费用
            // $this->report->generalData();
            // 生成选择结算的外部个人固定费用
            $this->report->generaFreelancerData();
            
            $this->addMessage('message', 'success');
            $this->addMessage('state', 'success');
            $this->showMessage();
        }
    }

    // 所有工资统计
    public function actionGetTableData()
    {
        // 获取固定费用数据
        $by_month = $this->report->showData();
        // 获取分成统计数据
        $by_count = $this->report->showDividedData();
        // 其它月结算到当前月数据
        $others = $this->report->otherMonthData();
        $schoolStatus = 0;
        if (in_array($this->branchId, array('BJ_DS', 'BJ_SLT'))) {
            $schoolStatus = 1;
        }
        $data = array(
            'total'=>0, 
            'schoolStatus'=> $schoolStatus,
            'month'=>$this->report->report_month,
            'branchId'=>$this->branchId,
            'update_time'=>date('Y-m-d', $this->report->update_time),
            'vendors'=>array()
        );
        foreach ($by_month['vendors'] as $vendor_id => $item) {
            $data['total'] += $item['amount'];
            if (!isset($data['vendors'][$vendor_id])) {
                $data['vendors'][$vendor_id] = $item;
                unset($data['vendors'][$vendor_id]['courses']);
            } else {
                $data['vendors'][$vendor_id]['amount'] += $item['amount'];
                $data['vendors'][$vendor_id]['variation_amout'] += $item['variation_amout'];
                $data['vendors'][$vendor_id]['variation_memo'] += $item['variation_memo'];
            }

            foreach ($item['courses'] as $month => $courses) {
                foreach ($courses as $course) {
                    $data['vendors'][$vendor_id]['amount'] -= $course['assistant_pay'];
                    $data['total'] -= $course['assistant_pay'];
                    $data['vendors'][$vendor_id]['courses'][$month][] = $course;
                }
            }
        }

        foreach ($by_count['vendors'] as $vendor_id => $item) {
            $data['total'] += $item['amount'];
            if (!isset($data['vendors'][$vendor_id])) {
                $data['vendors'][$vendor_id] = $item;
                unset($data['vendors'][$vendor_id]['courses']);
            } else {
                $data['vendors'][$vendor_id]['amount'] += $item['amount'];
                $data['vendors'][$vendor_id]['variation_amout'] += $item['variation_amout'];
                $data['vendors'][$vendor_id]['variation_memo'] += $item['variation_memo'];
            }
            foreach ($item['courses'] as $month => $courses) {
                foreach ($courses as $course) {
                    $data['vendors'][$vendor_id]['amount'] -= $course['assistant_pay'];
                    $data['total'] -= $course['assistant_pay'];
                    $data['vendors'][$vendor_id]['courses'][$month][] = $course;
                }
            }
        }
        foreach ($others as $vendor_id => $item) {
            $data['total'] += $item['amount'];
            if (!isset($data['vendors'][$vendor_id])) {
                $data['vendors'][$vendor_id] = $item;
                unset($data['vendors'][$vendor_id]['courses']);
            } else {
                $data['vendors'][$vendor_id]['amount'] += $item['amount'];
                $data['vendors'][$vendor_id]['variation_amout'] += $item['variation_amout'];
                $data['vendors'][$vendor_id]['variation_memo'] += $item['variation_memo'];
            }
            foreach ($item['courses'] as $month => $courses) {
                foreach ($courses as $course) {
                    $data['vendors'][$vendor_id]['amount'] -= $course['assistant_pay'];
                    $data['total'] -= $course['assistant_pay'];
                    $data['vendors'][$vendor_id]['courses'][$month][] = $course;
                }
            }
        }
        sort($data['vendors']);
        foreach ($data['vendors'] as $i=>$items) {
            foreach ($items['variation_amout'] as $variation_amout) {
                $data['total'] += $variation_amout;
                $data['vendors'][$i]['amount'] += $variation_amout;
            }
        }
        if ($this->report->status == AsaMonthReport::PREPAR) {
            $this->report->total_amount = $data['total'];
            $this->report->save();
        }

        $this->addMessage('data', $data);
        $this->addMessage('message', 'success');
        $this->addMessage('state', 'success');
        $this->showMessage();
    }

    // 保存调整金额
    public function actionSaveAdjustment()
    {
        $month = Yii::app()->request->getParam('month', '');
        $vendorId = Yii::app()->request->getParam('id', '');
        $amount = Yii::app()->request->getParam('amount', '');
        $memo = Yii::app()->request->getParam('memo', '');
        $settle = Yii::app()->request->getParam('settle', '1');

        if (!$month || !$vendorId || !is_numeric($amount)) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }
        $report_month = date("Ym", strtotime($month . '01'));
        $criteria = new CDbCriteria();
        $criteria->compare('report_month', $report_month);
        $criteria->compare('schoolid', $this->branchId);
        $report = AsaMonthReport::model()->find($criteria);

        if (!$report) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '报表不存在');
            $this->showMessage();
        }
        if ($this->report->status != AsaMonthReport::PREPAR){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '报表已提交，不可再更改');
            $this->showMessage();
        }

        $criteria = new CDbCriteria();
        $criteria->compare('report_id', $report->id);
        $criteria->compare('vendor_id', $vendorId);
        $model = AsaMonthReportVariation::model()->find($criteria);
        if(!$model){
            $model = new AsaMonthReportVariation();
            $oAmount = $report->total_amount + $amount;
        }else{
            $oAmount = $report->total_amount - $model->variation_amout + $amount;
        }
        $model->report_id = $report->id;
        $model->vendor_id = $vendorId;
        $model->variation_amout = $amount;
        $model->memo = $memo;
        $model->uid = $this->staff->uid;
        $model->update_time = time();
        $model->settlement = $settle;
        if($model->save()){
            $report->total_amount = $oAmount;
            $report->save();
            $this->addMessage('message', 'success');
            $this->addMessage('state', 'success');
            $this->addMessage('data', array('total_amount'=>$report->total_amount));
            $this->addMessage('message', '调整成功');
            $this->showMessage();
        }else {
            $error = current($model->getErrors());
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $error[0]);
            $this->showMessage();
        }
    }

    // 提交工资表
    public function actionSaveSummaryTable()
    {
        if (Yii::app()->request->isPostRequest) {
            $months = Yii::app()->request->getParam('month', '');
            $month = ($months) ? date("Ym", strtotime($months . '01')) : '';
            $report = AsaMonthReport::model()->findByAttributes(array('schoolid'=>$this->branchId, 'report_month'=>$month));

            if($report){
                if ($this->report->status != AsaMonthReport::PREPAR){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '报表已提交，不可再更改');
                    $this->showMessage();
                }
                $isDs = in_array($this->branchId, array('BJ_DS', 'BJ_SLT'));
                $report->status = $isDs ? AsaMonthReport::SUBMIT : AsaMonthReport::UNCHECK ;
                if (!$report->save()) {
                    $error = $report->getErrors();
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', $error[0]);
                    $this->showMessage();
                }

                $criteria = new CDbCriteria();
                $criteria->with = array('course', 'vendor');
                // 教师类型为外部个人或内部个人
                $criteria->compare('vendor.type', array(1, 3));
                $criteria->compare('t.settle_month', strtotime($month . '01'));
                $criteria->compare('t.report_month', $month);
                $criteria->compare('t.school_id', $this->branchId);

                $thirdParts = AsaThirdpartReport::model()->findAll($criteria);

                if($thirdParts){
                    foreach ($thirdParts as $val){
                        $val->status = 1;
                        $val->save();
                    }
                }
                $this->addMessage('message', 'success');
                $this->addMessage('state', 'success');
                $this->showMessage();
            }else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '请求错误');
                $this->showMessage();
            }
        }else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '请求错误');
            $this->showMessage();
        }
    }

    // 园长审核提交
    public function actionDirectorCheck()
    {
        if (Yii::app()->request->isPostRequest) {
            $months = Yii::app()->request->getParam('month', '');
            $month = ($months) ? date("Ym", strtotime($months . '01')) : '';
            $report = AsaMonthReport::model()->findByAttributes(array('schoolid'=>$this->branchId, 'report_month'=>$month));

            if($report){
                if ($this->report->status != AsaMonthReport::UNCHECK){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '报表已提交，不可再更改');
                    $this->showMessage();
                }

                $report->status = AsaMonthReport::SUBMIT;
                if (!$report->save()) {
                    $error = $report->getErrors();
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', $error[0]);
                    $this->showMessage();
                }
                $this->addMessage('message', 'success');
                $this->addMessage('state', 'success');
                $this->showMessage();
            }else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '请求错误');
                $this->showMessage();
            }
        }else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '请求错误');
            $this->showMessage();
        }
    }

    // 园长审核驳回
    public function actionOverrule()
    {
        if (Yii::app()->request->isPostRequest) {
            $months = Yii::app()->request->getParam('month', '');
            $month = ($months) ? date("Ym", strtotime($months . '01')) : '';

            $report = AsaMonthReport::model()->findByAttributes(array('schoolid'=>$this->branchId, 'report_month'=>$month));

            if($report){
                $report->status = AsaMonthReport::PREPAR;
                if (!$report->save()) {
                    $error = $report->getErrors();
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', $error[0]);
                    $this->showMessage();
                }
                $this->addMessage('message', 'success');
                $this->addMessage('state', 'success');
                $this->showMessage();
            }else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '请求错误');
                $this->showMessage();
            }
        }else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '请求错误');
            $this->showMessage();
        }
    }

    // 费用申请列表
    public function actionFee()
    {
        $this->branchSelectParams['urlArray'] = array('//masa/pay/fee');
        $date =date("Ym", time());
        $criteria = new CDbCriteria();
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('status',  AsaMonthReport::PREPAR);
        $model = AsaMonthReport::model()->findAll($criteria);
        $data['settle_month'] = array();
        $data['personal'] = array();
        $data['company'] = array();
        $data['personalArr'] = array();

        // 可结算的月份
        if($model){
            foreach ($model as $val){
                $data['settle_month'][] = $val->report_month;
            }
        }

        $criteria = new CDbCriteria();
        $criteria->with = array('report', 'vendor');
        $criteria->compare('vendor.type', array(2,3));
        //$criteria->compare('vendor.site_id', $this->branchId);
        $criteria->compare('report.status', ">15");
        $criteria->compare('t.expense_id', 0);
        $criteria->compare('t.settlement', 0);
        $criteria->compare('t.settle_report_id', 0);
        $modelVariation = AsaMonthReportVariation::model()->findAll($criteria);

        $criteria = new CDbCriteria();
        $criteria->with = array('vendor', 'settleReport','report');
        $criteria->compare('vendor.type', 3);
        $criteria->compare('settleReport.status', 10);
        $criteria->compare('t.expense_id', 0);
        $criteria->compare('t.settlement', 0);
        $criteria->compare('t.settle_report_id', ">0");
        $modelVariationT = AsaMonthReportVariation::model()->findAll($criteria);

        $personalArr = array();
        $modelStatusArr = array();
        $variation_amouts = array();
        if($modelVariationT){
            foreach ($modelVariationT as $val) {
                $modelStatusArr[$val->vendor_id]['report_month'][$val->report->report_month] = $val->settleReport->report_month;
                $modelStatusArr[$val->vendor_id]['variationid'][$val->report->report_month] = $val->id;
                $variation_amouts[$val->report->report_month][$val->vendor_id] = $val->variation_amout;
            }
        }

        if($modelStatusArr){
            // 外部个人和公司固定费用查询
            foreach ($modelStatusArr as $vendor_id => $monthData) {

                $criteria = new CDbCriteria();
                $criteria->compare('t.vendor_id', $vendor_id);
                $criteria->compare('report.report_month', array_keys($monthData['report_month']));
                $criteria->compare('report.schoolid', $this->branchId);
                $criteria->with = 'report';
                $reportModelItemArr = AsaMonthReportItem::model()->findAll($criteria);

                if ($reportModelItemArr) {
                    foreach ($reportModelItemArr as $val) {
                        $personalArr[$val->vendor_id]['vendor_id'] = $val->vendor_id;
                        $personalArr[$val->vendor_id]['name'] = $val->vendor->getName();
                        $personalArr[$val->vendor_id]['months'][$val->report->report_month]['item_id'] = $val->id;
                        $personalArr[$val->vendor_id]['months'][$val->report->report_month]['month'] = $val->report->report_month;
                        $personalArr[$val->vendor_id]['months'][$val->report->report_month]['amount'] += $val->unit_price * $val->course_count;
                        $personalArr[$val->vendor_id]['months'][$val->report->report_month]['money'] = $monthData['report_month'][$val->report->report_month];
                        $personalArr[$val->vendor_id]['months'][$val->report->report_month]['id'] = $monthData['variationid'][$val->report->report_month];
                    }
                }
            }

            // 外部个人和公司分成费用查询

            foreach ($modelStatusArr as $vendor_id => $monthData) {
                $criteria = new CDbCriteria();
                $criteria->compare('course_staff_id', $vendor_id);
                $criteria->compare('report_month', array_keys($monthData['report_month']));
                $criteria->compare('school_id', $this->branchId);
                $reportModel = AsaThirdpartReport::model()->findAll($criteria);

                if($reportModel){
                    foreach ($reportModel as $val){
                        //if($val->report_month > '201901') {
                            $assistant_pay = 0;
                            if ($val->assistant_pay) {
                                $assistant_pay = $val->assistant_pay;
                            }
                            $personalArr[$val->course_staff_id]['vendor_id'] = $val->course_staff_id;
                            $personalArr[$val->course_staff_id]['name'] = $val->vendor->getName();
                            $personalArr[$val->course_staff_id]['months'][$val->report_month]['item_id'] = $val->id;
                            $personalArr[$val->course_staff_id]['months'][$val->report_month]['month'] = $val->report_month;
                            $personalArr[$val->course_staff_id]['months'][$val->report_month]['amount'] += $val->total_amount - $assistant_pay;
                            $personalArr[$val->course_staff_id]['months'][$val->report_month]['money'] = $monthData['report_month'][$val->report_month];
                            $personalArr[$val->course_staff_id]['months'][$val->report_month]['id'] = $monthData['variationid'][$val->report_month];
                        //}
                    }
                }
            }

            // 外部个人
            if($personalArr){
                foreach ($personalArr as $val) {
                    $monthsArr = array();
                    foreach ($val['months'] as $key=>$item) {
                        $variatioAmount = 0;
                        if($variation_amouts && $variation_amouts[$key][$val['vendor_id']]){
                            $variatioAmount = $variation_amouts[$key][$val['vendor_id']];
                        }
                        $personalAmount = $item['amount'] + $variatioAmount;
                        $monthsArr[] = array(
                            'item_id' => $item['item_id'],
                            'month' => $item['month'],
                            'amount' => $personalAmount,
                            'money' => $item['money'],
                            'id' => $item['id'],
                        );
                    }
                    $data['personalArr'][] = array(
                        'vendor_id' => $val['vendor_id'],
                        'name' => $val['name'],
                        'months' => $monthsArr,
                    );
                }
            }
        }

        $month = array();
        $report = array();
        $vendor = array();
        $variation_amout = array();

        if($modelVariation){
            foreach ($modelVariation as $val) {
                $month[$val->vendor_id]['report_month'][$val->report->report_month] = $val->report->report_month;
                $month[$val->vendor_id]['status'][$val->report->report_month] = $val->report->status;
                $report[$val->report->id] = $val->report->id;
                $vendor[$val->vendor_id] = $val->vendor_id;
                $variation_amout[$val->report->report_month][$val->vendor_id] = $val->variation_amout;
            }
        }

        $personal = array();
        $company = array();


        if($month){
            // 外部个人和公司固定费用查询
            foreach ($month as $vendor_id => $monthData) {
                $criteria = new CDbCriteria();
                $criteria->compare('t.vendor_id', $vendor_id);
                $criteria->compare('report.report_month', $monthData['report_month']);
                $criteria->compare('report.schoolid', $this->branchId);
                $criteria->with = 'report';
                $reportModelItem = AsaMonthReportItem::model()->findAll($criteria);

                if ($reportModelItem) {
                    foreach ($reportModelItem as $val) {
                        //if($reportModelItem->report->report_month > "201901") {
                            if ($val->vendor->type == 3) {
                                $personal[$val->vendor_id]['vendor_id'] = $val->vendor_id;
                                $personal[$val->vendor_id]['name'] = $val->vendor->getName();
                                $personal[$val->vendor_id]['months'][$val->report->report_month]['item_id'] = $val->id;
                                $personal[$val->vendor_id]['months'][$val->report->report_month]['month'] = $val->report->report_month;
                                $personal[$val->vendor_id]['months'][$val->report->report_month]['amount'] += $val->unit_price * $val->course_count;
                                $personal[$val->vendor_id]['months'][$val->report->report_month]['status'] = $monthData['status'][$val->report->report_month];
                            }
                            if ($val->vendor->type == 2) {
                                $company[$val->vendor_id]['vendor_id'] = $val->vendor_id;
                                $company[$val->vendor_id]['name'] = $val->vendor->getName();
                                $company[$val->vendor_id]['months'][$val->report->report_month]['item_id'] = $val->id;
                                $company[$val->vendor_id]['months'][$val->report->report_month]['month'] = $val->report->report_month;
                                $company[$val->vendor_id]['months'][$val->report->report_month]['amount'] += $val->unit_price * $val->course_count;
                                $company[$val->vendor_id]['months'][$val->report->report_month]['status'] = $monthData['status'][$val->report->report_month];
                            }
                        //}
                    }
                }
            }

            // 外部个人和公司分成费用查询

            foreach ($month as $vendor_id => $monthData) {
                $criteria = new CDbCriteria();
                $criteria->compare('course_staff_id', $vendor_id);
                $criteria->compare('report_month', $monthData['report_month']);
                $criteria->compare('school_id', $this->branchId);
                $reportModel = AsaThirdpartReport::model()->findAll($criteria);

                if($reportModel){
                    foreach ($reportModel as $val){
                        //f($val->report_month > '201901') {
                            if ($val->vendor->type == 3) {
                                $assistant_pay = 0;
                                if ($val->assistant_pay) {
                                    $assistant_pay = $val->assistant_pay;
                                }
                                $personal[$val->course_staff_id]['vendor_id'] = $val->course_staff_id;
                                $personal[$val->course_staff_id]['name'] = $val->vendor->getName();
                                $personal[$val->course_staff_id]['months'][$val->report_month]['item_id'] = $val->id;
                                $personal[$val->course_staff_id]['months'][$val->report_month]['month'] = $val->report_month;
                                $personal[$val->course_staff_id]['months'][$val->report_month]['amount'] += $val->total_amount - $assistant_pay;
                                $personal[$val->course_staff_id]['months'][$val->report_month]['status'] = $monthData['status'][$val->report_month];
                            }
                            if ($val->vendor->type == 2) {
                                $assistant_pay = 0;
                                if ($val->assistant_pay) {
                                    $assistant_pay = $val->assistant_pay;
                                }
                                $company[$val->course_staff_id]['vendor_id'] = $val->course_staff_id;
                                $company[$val->course_staff_id]['name'] = $val->vendor->getName();
                                $company[$val->course_staff_id]['months'][$val->report_month]['item_id'] = $val->id;
                                $company[$val->course_staff_id]['months'][$val->report_month]['month'] = $val->report_month;
                                $company[$val->course_staff_id]['months'][$val->report_month]['amount'] += $val->total_amount - $assistant_pay;
                                $company[$val->course_staff_id]['months'][$val->report_month]['status'] = $monthData['status'][$val->report_month];
                            }
                        //}
                    }
                }
            }

            // 外部个人
            if($personal){
                foreach ($personal as $val) {
                    $months = array();
                    foreach ($val['months'] as $key=>$item) {
                        $variatioAmount = 0;
                        if($variation_amout && $variation_amout[$key][$val['vendor_id']]){
                            $variatioAmount = $variation_amout[$key][$val['vendor_id']];
                        }
                        $personalAmount = $item['amount'] + $variatioAmount;
                        $months[] = array(
                            'item_id' => $item['item_id'],
                            'month' => $item['month'],
                            'amount' => $personalAmount,
                            'status' => $item['status'],
                        );
                    }
                    $data['personal'][] = array(
                        'vendor_id' => $val['vendor_id'],
                        'name' => $val['name'],
                        'months' => $months,
                    );
                }
            }
            // 外部公司
            if($company){
                foreach ($company as $val) {
                    $months = array();
                    foreach ($val['months'] as $key=>$item) {
                        $variatioAmount = 0;
                        if($variation_amout && $variation_amout[$key][$val['vendor_id']]){
                            $variatioAmount = $variation_amout[$key][$val['vendor_id']];
                        }
                        $companyAmount = $item['amount'] + $variatioAmount;
                        $months[] = array(
                            'item_id' => $item['item_id'],
                            'month' => $item['month'],
                            'amount' => $companyAmount,
                            'status' => $item['status'],
                        );
                    }
                    $data['company'][] = array(
                        'vendor_id' => $val['vendor_id'],
                        'name' => $val['name'],
                        'months' => $months,
                    );
                }
            }
        }

        $this->render('fee', array(
            'data' => $data,
        ));
    }

    // 删除结算未提交的数据
    public function actionDeleteVariation()
    {
        $variation_id = Yii::app()->request->getParam('variation_id', array());
        $state = 'fail';
        $message = '请求错误';
        if(Yii::app()->request->isPostRequest){
            $model = AsaMonthReportVariation::model()->findAllByPk($variation_id);
            if($model){
                foreach ($model as $val){
                    $val->settle_report_id = 0;
                    $val->save();
                }
                $state = 'success';
                $message = '成功';
            }
        }
        $this->addMessage('state', $state);
        $this->addMessage('message', $message);
        $this->showMessage();
    }

    //第三方个人暂不结算的结算为某月工资
    public function actionSaveSettle()
    {
        $item_id = Yii::app()->request->getParam('item_id', array());
        $setMonth = Yii::app()->request->getParam('setMonth', '');
        $state = 'fail';
        $message = '请求错误';

        if(Yii::app()->request->isPostRequest){
            if($item_id){
                $data = $this->organizeData($item_id);
                foreach ($data as $vendor_id => $val) {
                    $criteria = new CDbCriteria();
                    $criteria->with = array('report', 'vendor');
                    $criteria->compare('t.vendor_id', $vendor_id);
                    $criteria->compare('report.report_month', $val);
                    $criteria->compare('report.status', "<>10");
                    $criteria->compare('t.settlement', 0);
                    $modelVariation = AsaMonthReportVariation::model()->findAll($criteria);

                    if($modelVariation){
                        $criteria = new CDbCriteria();
                        $criteria->compare('t.report_month', $setMonth);
                        $criteria->compare('t.schoolid', $this->branchId);
                        $reportModel = AsaMonthReport::model()->find($criteria);

                        if($reportModel){
                            foreach ($modelVariation as $item){
                                $item->settle_report_id = $reportModel->id;
                                $item->save();
                            }
                            /*$criteria = new CDbCriteria();
                            $criteria->compare('course_staff_id', $vendor_id);
                            $criteria->compare('report_month', $item_id);
                            $criteria->compare('status', 0);
                            $criteria->compare('school_id', $this->branchId);
                            $reportModel = AsaThirdpartReport::model()->findAll($criteria);
                            if($reportModel){
                                foreach ($reportModel as $item){
                                    $item->status = 1;
                                    $item->save();
                                }
                            }*/
                        }
                    }
                }

                $state = 'success';
                $message = '成功';
            }
        }

        $this->addMessage('state', $state);
        $this->addMessage('message', $message);
        $this->showMessage();
    }

    //申请报销窗口
    public function actionApplication()
    {
        $item_id = Yii::app()->request->getParam('item_id', array());
        $data = array();
        $data['staff'] = array();
        $state = 'fail';
        $message = '请求错误';

        $typeList = CommonUtils::LoadConfig('CfgASA');
        unset($typeList['job_type'][1]);
        unset($typeList['job_type'][2]);

        $criteria = new CDbCriteria();
        $criteria->compare('job_type', array_keys($typeList['job_type']));
        $criteria->compare('site_id', $this->branchId);
        $criteria->compare('status', 1);
        $criteria->group  = 'vendor_id';
        $staffModel = AsaCourseStaff::model()->findAll($criteria);

        $title = array();
        $staff = array();
        if($staffModel){
            foreach ($staffModel as $val){
                $staff[] = array(
                    'vendor_id' => isset($val->vendor) ?$val->vendor->vendor_id : $val->id,
                    'name' => isset($val->vendor) ? $val->vendor->getName() : $val->id,
                    'account_name' => isset($val->vendor->vendorInfo) ? $val->vendor->vendorInfo->bank_name : "",
                    'account_number' => isset($val->vendor->vendorInfo) ? $val->vendor->vendorInfo->bank_account : "",
                    'bank_name' => isset($val->vendor->vendorInfo) ? $val->vendor->vendorInfo->bank_address : "",
                );
            }
        }
        if($item_id){
            $item = $this->organizeData($item_id);
            $data = $this->showApplication($item);
            $model = AsaVendor::model()->findAllByPk(array_keys($item));
            if($model){
                foreach ($model as $val){
                    $title[$val->vendor_id] = $val->getName();
                }
                $title = implode(',',$title);
            }
            $data['title'] =  $title . ' 个人劳务费报销 ';
            $data['staff'] = $staff;
            $state = 'success';
            $message = '成功';
        }

        echo CJSON::encode(array(
            'state' => $state,
            'date' => $data,
            'message' => $message,
        ));
    }

    //申请付款窗口
    public function actionApplicationFirm()
    {
        $item_id = Yii::app()->request->getParam('item_id', array());

        $data = array();
        $state = 'fail';
        $message = '请求错误';
        if($item_id){
            $item = $this->organizeData($item_id);
            $model = AsaVendor::model()->findByPk(array_keys($item));
            $data = $this->showApplication($item);
            $data['vendor_id'] = $model->vendor_id;
            $data['title'] = $model->getName() . '付款费用';
            $data['vendor_id'] = $model->vendor_id;
            $data['name'] = $model->getName();
            $data['account_name'] = $model->vendorInfo->bank_name;
            $data['account_number'] = $model->vendorInfo->bank_account;
            $data['bank_name'] = $model->vendorInfo->bank_address;
            $state = 'success';
            $message = '成功';
        }

        echo CJSON::encode(array(
            'state' => $state,
            'date' => $data,
            'message' => $message,
        ));
    }

    // 根据vendor_id 和月份来获取全部的金额
    public function showApplication($item_id)
    {
        $amount = 0;
        foreach ($item_id as $vendor_id => $value) {
            $criteria = new CDbCriteria();
            $criteria->compare('report_month', $value);
            $criteria->compare('course_staff_id', $vendor_id);
            $criteria->compare('school_id', $this->branchId);
            $reportModel = AsaThirdpartReport::model()->findAll($criteria);
            if($reportModel){
                foreach ($reportModel as $val){
                    $assistant_pay = 0;
                    if($val->assistant_pay){
                        $assistant_pay = $val->assistant_pay;
                    }

                    $amount += $val->total_amount - $assistant_pay;
                }
            }

            $criteria = new CDbCriteria();
            $criteria->with = array('report', 'vendor');
            $criteria->compare('t.vendor_id', $vendor_id);
            $criteria->compare('report.report_month', $value);
            $criteria->compare('report.schoolid', $this->branchId);
            $criteria->compare('t.settlement', 0);
            $modelVariation = AsaMonthReportVariation::model()->findAll($criteria);

            if($modelVariation){
                foreach ($modelVariation as $val) {
                    $amount += $val->variation_amout;
                }
            }

            $criteria = new CDbCriteria();
            $criteria->compare('report.report_month', $value);
            $criteria->compare('t.vendor_id', $vendor_id);
            $criteria->compare('report.schoolid', $this->branchId);
            $criteria->with = array('report');
            $reportModelItem = AsaMonthReportItem::model()->findAll($criteria);

            if($reportModelItem){
                foreach ($reportModelItem as $val) {
                    $amount += $val->unit_price * $val->course_count;
                }
            }

        }

        $date['acmount'] = $amount;
        return $date;
    }

    // 申请窗口提交
    public function actionSaveApplication()
    {
        $vendor_id = Yii::app()->request->getParam('vendor_id', '');
        $amount = Yii::app()->request->getParam('amount', '');
        $title = Yii::app()->request->getParam('title', '');
        $attachment = Yii::app()->request->getParam('attachment', '');
        $item = Yii::app()->request->getParam('item_id', array());
        $type = Yii::app()->request->getParam('type', '');

        $state = 'fail';
        $message = '非法操作';

        $monthArr = array();
        if($item){
            foreach ($item as $val) {
                foreach ($val as $month_item) {
                    $monthArr[$month_item] = $month_item;
                }
            }
        }
       
        if(empty($attachment)){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '文件不能为空');
            $this->showMessage();
        }

        $modelVendor = AsaVendor::model()->findByPk($vendor_id);
        if (isset($modelVendor) && isset($modelVendor->vendorInfo)) {
            if(!$modelVendor->vendorInfo->bank_name){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '收款人不能为空');
                $this->showMessage();
            }
            if(!$modelVendor->vendorInfo->bank){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '开户行不能为空');
                $this->showMessage();
            }
            if(!$modelVendor->vendorInfo->bank_address){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '银行具体信息不能为空');
                $this->showMessage();
            }
            if(!$modelVendor->vendorInfo->bank_account){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '收款人账号不能为空');
                $this->showMessage();
            }
            $model = new AsaExpense();
            $model->title = $title;
            $model->branch = $this->branchId;
            $model->uid = $vendor_id;
            $model->amount = $amount;
            $model->payee_user = $modelVendor->vendorInfo->bank_name;
            $model->payee_bank = $modelVendor->vendorInfo->bank;
            $model->payee_branch = $modelVendor->vendorInfo->bank_address;
            $model->payee_account = $modelVendor->vendorInfo->bank_account;
            $model->type = $type;
            $model->status = 10;
            $model->created = time();
            $model->updated = time();
            $model->expense_uid = Yii::app()->user->id;

            if ($model->save()) {
                if ($item) {
                    foreach ($item as $vendor_id => $month) {
                        $criteria = new CDbCriteria();
                        $criteria->compare('t.school_id', $this->branchId);
                        $criteria->compare('t.report_month', $month);
                        $criteria->compare('t.course_staff_id', $vendor_id);
                        $modelReport = AsaThirdpartReport::model()->findAll($criteria);
                        if ($modelReport) {
                            foreach ($modelReport as $items) {
                                $modelItem = new AsaExpenseItem();
                                $modelItem->eid = $model->id;
                                $modelItem->content = $items->vendor->getName() . '课时费';
                                $modelItem->files = json_encode($attachment);
                                $modelItem->groupid = $items->program_id;
                                $modelItem->courseid = $items->course_id;
                                $modelItem->amount = $items->total_amount;
                                $modelItem->uid = Yii::app()->user->id;
                                $modelItem->updated = time();
                                $modelItem->save();
                            }
                        }

                        $criteria = new CDbCriteria();
                        $criteria->with = array('report');
                        $criteria->compare('report.report_month', $month);
                        $criteria->compare('t.vendor_id', $vendor_id);
                        $criteria->compare('report.schoolid', $this->branchId);
                        $modelReportItem = AsaMonthReportItem::model()->findAll($criteria);

                        if ($modelReportItem) {
                            foreach ($modelReportItem as $items) {
                                $modelItem = new AsaExpenseItem();
                                $modelItem->eid = $model->id;
                                $modelItem->content = $items->vendor->getName() . '课时费';
                                $modelItem->files = json_encode($attachment);
                                $modelItem->groupid = $items->group_id;
                                $modelItem->courseid = $items->course_id;
                                $modelItem->amount = $items->unit_price * $items->course_count;
                                $modelItem->uid = Yii::app()->user->id;
                                $modelItem->updated = time();
                                $modelItem->save();
                            }
                        }

                        $criteria = new CDbCriteria();
                        $criteria->with = array('report', 'vendor');
                        $criteria->compare('report.report_month', $month);
                        $criteria->compare('t.vendor_id', $vendor_id);
                        $criteria->compare('report.schoolid', $this->branchId);
                        $criteria->compare('t.expense_id', 0);
                        $criteria->compare('t.settlement', 0);
                        $criteria->compare('t.settle_report_id', 0);
                        $modelVariation = AsaMonthReportVariation::model()->findAll($criteria);

                        if ($modelVariation) {
                            foreach ($modelVariation as $reportItem) {
                                if($reportItem->items){
                                    foreach ($reportItem->items as $value){
                                        $modelItem = new AsaExpenseItem();
                                        $modelItem->eid = $model->id;
                                        $modelItem->content = '调整金额';
                                        $modelItem->groupid = $value->program_id;
                                        $modelItem->courseid = $value->course_id;
                                        $modelItem->amount = $value->variation_amout;
                                        $modelItem->uid = Yii::app()->user->id;
                                        $modelItem->updated = time();
                                        $modelItem->save();
                                    }
                                }

                                $reportItem->expense_id = $model->id;
                                $reportItem->save();
                            }
                        }
                    }
                    $state = 'success';
                    $message = '申请提交成功！请到到·报销及付款·页面查看进度。';
                    $this->addMessage('callback', 'cbVisit');
                }
            }
        } else {
            $state = 'fail';
            $message = '该用户没有银行账户，清先添加银行账户';
        }

        $this->addMessage('state', $state);
        $this->addMessage('message', $message);
        $this->showMessage();

    }


    // 申请窗口上传附件
    public function actionExpenseFiles()
    {
        $file = CUploadedFile::getInstanceByName('file');
        $msg = Yii::t('asa','没有附件选中');
        $saveName = "";
        if ($file) {
            if ($file->size > 10*1024*1024) {
                $msg = Yii::t('asa','文件过大');
            } else{
                $needType = array('jpg','jpeg','png','pdf');
                if (!in_array($file->getExtensionName(), $needType)) {
                    $msg = Yii::t('asa','此文件类型不允许上传');
                }else{
                    $filePath = Yii::app()->params['xoopsVarPath'] . '/asa/expense/';
                    if(!is_dir($filePath)){
                        mkdir($filePath,0777);
                    }
                    $ext = $file->getExtensionName();
                    $saveName = date("Ydm") . '_' . uniqid() . '.' . $ext;
                    if ($file->saveAs($filePath . $saveName)){
                        $msg = 'success';
                        $baseUrl = $this->createUrl('downloadsExpense', array('fileName'=>$saveName));
                    }else{
                        $msg = Yii::t('asa','文件上传失败');
                    }
                }
            }
        }
        echo CJSON::encode(array(
            'url' => $baseUrl,
            'saveName' => $saveName,
            'msg' => $msg,
        ));
    }

    //删除单个图片
    public function actionDelFiles()
    {
        if (Yii::app()->request->isPostRequest){
            $fileName = Yii::app()->request->getParam('fileName', '');
            $filePath = Yii::app()->params['xoopsVarPath'] . '/asa/expense/';
            unlink($filePath . $fileName);
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message','success'));
            $this->showMessage();
        }
    }

    // 查看图片
    public function actionDownloadsExpense()
    {
        ob_end_clean();
        $fileName = Yii::app()->request->getParam('fileName', "");
        $extension = substr(strrchr($fileName, '.'), 1);
        $file_dir = Yii::app()->params['xoopsVarPath'] . '/asa/expense/';

        $fileres = file_get_contents($file_dir . $fileName);
        if($extension == "pdf"){
            header('Content-type: application/pdf');
        }else{
            header('Content-type: image/jpeg');
        }
        echo $fileres;
    }

    //整理数据
    /*
     * array(
     *      array(vendor_id, month)
     *      array(vendor_id, month)
     *      array(vendor_id, month)
     * )
     */
    public function organizeData($item)
    {
        $data = array();

        foreach ($item as $val) {
            $data[$val[0]][] = $val[1];
        }

        return $data;
    }

}