<?php

/**
 * This is the model class for table "ivy_child_credit_now".
 *
 * The followings are the available columns in table 'ivy_child_credit_now':
 * @property integer $cid
 * @property integer $childid
 * @property string $childname
 * @property integer $classid
 * @property string $classname
 * @property string $schoolid
 * @property string $schoolname
 * @property integer $updated_timestamp
 * @property double $surplus
 */
class ChildCreditNow extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_child_credit_now';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('childid, updated_timestamp', 'required'),
			array('childid, classid, updated_timestamp', 'numerical', 'integerOnly'=>true),
			array('surplus', 'numerical'),
			array('childname, classname, schoolname', 'length', 'max'=>255),
			array('schoolid', 'length', 'max'=>11),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('cid, childid, childname, classid, classname, schoolid, schoolname, updated_timestamp, surplus', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'child' => array(self::BELONGS_TO, 'ChildProfileBasic', 'childid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'cid' => 'Cid',
			'childid' => 'Childid',
			'childname' => 'Childname',
			'classid' => 'Classid',
			'classname' => 'Classname',
			'schoolid' => 'Schoolid',
			'schoolname' => 'Schoolname',
			'updated_timestamp' => 'Updated Timestamp',
			'surplus' => 'Surplus',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('cid',$this->cid);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('childname',$this->childname,true);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('classname',$this->classname,true);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('schoolname',$this->schoolname,true);
		$criteria->compare('updated_timestamp',$this->updated_timestamp);
		$criteria->compare('surplus',$this->surplus);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return ChildCreditNow the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
