<div class="form">
<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'user-form',
	'enableAjaxValidation'=>false,
        'htmlOptions'=>array('class'=>'J_ajaxForm'),
)); 
?>
    <div class="table_full">
        <table width="100%">
            <tbody>
                <colgroup>
                    <col width="200">
                </colgroup>
                <tr>
                    <th><?php echo Yii::t("hr", "预计加班日期");?></th>
                    <?php
                        for ($i=0;$i<21;$i++){
                            $day[$i] = $i.Yii::t('hr', '天');
                        }
                        for ($i=0;$i<8;$i++){
                            $hour[$i] = $i.Yii::t('hr', '小时');
                        }
                    ?>
                    <td>
                        <?php echo $form->dropDownList($model,'ot_num',$day);?>
                        <?php echo $form->dropDownList($model,'ot_hour',$hour);?>
                        <?php echo $form->error($model,'hours'); ?>
                    </td>
                </tr>
                 <tr>
                    <th><?php echo $form->labelEx($model, 'startdate'); ?></th>
                    <td>
                        <?php
                            $this->widget('common.extensions.datePicker.JMy97DatePicker', array(
                                    'name'=>CHtml::activeName($model,'startdate'),
                                    'value'=>$model->startdate,
                                    'options'=>array('dateFmt'=>'yyyy-MM-dd','opposite'=>true),
                                    'htmlOptions'=>array('readonly'=>'readonly', 'class'=>'input'),
                            ));
                        ?>
                        <?php echo $form->error($model,'startdate'); ?>
                    </td>
                </tr>
                <tr>
                    <th><?php echo $form->labelEx($model, 'enddate'); ?></th>
                        <td>
                            <?php
                                $this->widget('common.extensions.datePicker.JMy97DatePicker', array(
                                      'name'=>CHtml::activeName($model,'enddate'),
                                      'value'=>$model->enddate,
                                      'options'=>array('dateFmt'=>'yyyy-MM-dd','opposite'=>true),
                                      'htmlOptions'=>array('readonly'=>'readonly', 'class'=>'input'),
                                ));
                            ?>
                            <?php echo $form->error($model,'enddate'); ?>
                        </td>
                </tr>
                <tr>
                    <th><?php echo $form->labelEx($model, 'type'); ?></th>
                    <td>
                            <?php
                                echo $form->radioButtonList($model, 'type', OTItem::getOType(), array('template' => '<li style="float:left;padding-right:10px;">{input}{label}</li>', 'separator' => ''));
                            ?>
                            <?php echo $form->error($model,'type'); ?>
                    </td>
               </tr>
               <tr>
                    <th><?php echo $form->labelEx($model, 'memo'); ?></th>
                    <td>
                            <?php
                                echo $form->textArea($model, 'memo',array('cols'=>30));
                            ?>
                            <?php echo $form->error($model,'memo'); ?>
                    </td>
              </tr>
            </tbody>
        </table>
    </div>
    <div id="J_submit_tips"></div>
    <div class="pop_bottom tar">
        <button type="submit" class="btn btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
        <button id="J_dialog_close" type="button" class="btn btn_cancel"><?php echo Yii::t('global','Cancel');?></button>
    </div>
<?php $this->endWidget(); ?>
</div><!-- form -->
