<?php
$this->renderPartial('header');

	$this->widget('zii.widgets.CMenu',array(
		'items'=> $pageSubMenu,
		'activeCssClass'=>'current',
		'id'=>'page-subMenu',
		'htmlOptions'=>array('class'=>'pageSubMenu')
	));
	echo CHtml::openTag("div", array("class"=>"c"));
	echo CHtml::closeTag("div");
?>


<?php if ($calendar):?>
	<div class="h_a"><?php echo Yii::t("campus", "School Year");?></div>
	<div class="prompt_text">
		<ul>
			<li><?php echo $calendar->startyear . ' ~ ' . intval($calendar->startyear + 1);?></li>
		</ul>
	</div>
	
	<?php foreach ($classIds as $classid=>$classTitle):?>
		<div class="table_full h_a"><span class="mr10"><?php echo $classTitle;?></span><?php echo CHtml::link(Yii::t("campus","Invoice & Payment Status"),'javascript:void(0);', array('class'=>'displayInvoice','cid'=>$classid,'caid'=>$calendar->yid,'rel'=>'invoice-overview-'.$classid));?></div>
		<div class="table_full" id="invoice-overview-<?php echo $classid;?>" style="margin-left:2em;"></div>
	<?php endforeach; ?>

<?php endif;?>


<?php
$this->renderPartial('_class_js', array('flag'=>$flag));
?>