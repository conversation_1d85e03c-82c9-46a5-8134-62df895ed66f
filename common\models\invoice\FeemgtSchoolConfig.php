<?php

/**
 * This is the model class for table "ivy_feemgt_school_config".
 *
 * The followings are the available columns in table 'ivy_feemgt_school_config':
 * @property integer $id
 * @property integer $yid
 * @property string $schoolid
 * @property string $feemgt_type
 * @property string $data
 * @property double $deposit_refund_rates
 * @property integer $is_tuition_factor
 * @property string $tuition_factor_key
 * @property double $tuition_refund_rates
 * @property integer $tuition_refund_way
 * @property integer $tuition_leave_school_way
 * @property double $month_amount
 * @property double $day_amount
 * @property double $day_refund_amount
 * @property integer $lunch_payment_type
 * @property integer $is_latepay
 * @property double $latepay_rate
 * @property integer $userid
 * @property integer $updated_timestamp
 * @property double $schoolbus
 * @property integer $differ
 * @property integer $latepay
 * @property integer $agreeddate
 * @property integer $is_y_s
 * @property double $library_card_cost
 */
class FeemgtSchoolConfig extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return FeemgtSchoolConfig the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_feemgt_school_config';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('schoolid, feemgt_type, data, userid, updated_timestamp', 'required'),
			array('yid, is_tuition_factor, tuition_refund_way, tuition_leave_school_way, lunch_payment_type, is_latepay, userid, updated_timestamp, differ, latepay, agreeddate, is_y_s', 'numerical', 'integerOnly'=>true),
			array('deposit_refund_rates, tuition_refund_rates, month_amount, day_amount, day_refund_amount, latepay_rate, schoolbus, library_card_cost', 'numerical'),
			array('schoolid', 'length', 'max'=>15),
			array('feemgt_type, tuition_factor_key', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, yid, schoolid, feemgt_type, data, deposit_refund_rates, is_tuition_factor, tuition_factor_key, tuition_refund_rates, tuition_refund_way, tuition_leave_school_way, month_amount, day_amount, day_refund_amount, lunch_payment_type, is_latepay, latepay_rate, userid, updated_timestamp, schoolbus, differ, latepay, agreeddate, is_y_s, library_card_cost', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'yid' => 'Yid',
			'schoolid' => 'Schoolid',
			'feemgt_type' => 'Feemgt Type',
			'data' => 'Data',
			'deposit_refund_rates' => 'Deposit Refund Rates',
			'is_tuition_factor' => 'Is Tuition Factor',
			'tuition_factor_key' => 'Tuition Factor Key',
			'tuition_refund_rates' => 'Tuition Refund Rates',
			'tuition_refund_way' => 'Tuition Refund Way',
			'tuition_leave_school_way' => 'Tuition Leave School Way',
			'month_amount' => 'Month Amount',
			'day_amount' => 'Day Amount',
			'day_refund_amount' => 'Day Refund Amount',
			'lunch_payment_type' => 'Lunch Payment Type',
			'is_latepay' => 'Is Latepay',
			'latepay_rate' => 'Latepay Rate',
			'userid' => 'Userid',
			'updated_timestamp' => 'Updated Timestamp',
			'schoolbus' => 'Schoolbus',
			'differ' => 'Differ',
			'latepay' => 'Latepay',
			'agreeddate' => 'Agreeddate',
			'is_y_s' => 'Is Y S',
			'library_card_cost' => 'Library Card Cost',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('feemgt_type',$this->feemgt_type,true);
		$criteria->compare('data',$this->data,true);
		$criteria->compare('deposit_refund_rates',$this->deposit_refund_rates);
		$criteria->compare('is_tuition_factor',$this->is_tuition_factor);
		$criteria->compare('tuition_factor_key',$this->tuition_factor_key,true);
		$criteria->compare('tuition_refund_rates',$this->tuition_refund_rates);
		$criteria->compare('tuition_refund_way',$this->tuition_refund_way);
		$criteria->compare('tuition_leave_school_way',$this->tuition_leave_school_way);
		$criteria->compare('month_amount',$this->month_amount);
		$criteria->compare('day_amount',$this->day_amount);
		$criteria->compare('day_refund_amount',$this->day_refund_amount);
		$criteria->compare('lunch_payment_type',$this->lunch_payment_type);
		$criteria->compare('is_latepay',$this->is_latepay);
		$criteria->compare('latepay_rate',$this->latepay_rate);
		$criteria->compare('userid',$this->userid);
		$criteria->compare('updated_timestamp',$this->updated_timestamp);
		$criteria->compare('schoolbus',$this->schoolbus);
		$criteria->compare('differ',$this->differ);
		$criteria->compare('latepay',$this->latepay);
		$criteria->compare('agreeddate',$this->agreeddate);
		$criteria->compare('is_y_s',$this->is_y_s);
		$criteria->compare('library_card_cost',$this->library_card_cost);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
	
	/**
	 * 取得学校费用的配置
	 * @param string $schoolId
	 * @param int $calendarId
	 * @param string $feeType
	 * @return object
	 * <AUTHOR>
	 */
	public function getSchoolFeeConfig($schoolId,$calendarId,$feeType)
	{
		$criteria = new CDbCriteria();
		$criteria->compare("feemgt_type", $feeType);
		$criteria->compare("schoolid", $schoolId);
		$criteria->compare("yid", $calendarId);
		return $this->find($criteria);
	}
}