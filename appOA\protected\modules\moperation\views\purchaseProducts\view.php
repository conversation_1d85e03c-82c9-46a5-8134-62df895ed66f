<ol class="breadcrumb">
    <li><?php echo CHtml::link(Yii::t('site','HQ Operations'), array('default/index'));?></li>
    <li><?php echo CHtml::link(Yii::t('site','Support'), array('default/index'));?></li>
    <li class="active"><?php echo CHtml::link(Yii::t('site','采购商品管理'), array('purchaseProducts/index'));?></li>
</ol>
<div class="modal-header">
	<h4 class="modal-title" id="exampleModalLabel"><?php echo $model->cn_name; ?></h4>
</div>
<?php $form=$this->beginWidget('CActiveForm', array(
		'id'=>'purchase-products-form',
		'enableAjaxValidation'=>false,
		'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
)); ?>
<div class="modal-body">
	<div class="form-group">
		<label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'cn_name'); ?></label>
		<div class="col-xs-8">
			<?php echo $form->textField($model,'cn_name',array('maxlength'=>255,'class'=>'form-control','readonly'=>'readonly')); ?>
		</div>
	</div>
	<div class="form-group">
		<label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'cid'); ?></label>
		<div class="col-xs-8">
			<?php echo $form->dropDownList($model,'cid', $diglossiaArray['cate'] , array('class'=>'select_2 form-control', 'empty'=>Yii::t("global", 'Please Select'))); ?>
		</div>
	</div>
	<div class="form-group">
		<label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'aid'); ?></label>
		<div class="col-xs-8">
			<?php echo $form->dropDownList($model,'aid', $diglossiaArray['area'] , array('class'=>'select_2 form-control', 'empty'=>Yii::t("global", 'Please Select'))); ?>
		</div>
	</div>
	<div class="form-group">
		<label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'tid'); ?></label>
		<div class="col-xs-8">
			<?php echo $form->textField($model,'tid',array('maxlength'=>255,'class'=>'form-control','readonly'=>'readonly')); ?>
		</div>
	</div>
	<div class="form-group">
		<label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'price'); ?></label>
		<div class="col-xs-8">
			<?php echo $form->textField($model,'price',array('maxlength'=>255,'class'=>'form-control','readonly'=>'readonly')); ?>
		</div>
	</div>
	<div class="form-group">
		<label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'model'); ?></label>
		<div class="col-xs-8">
			<?php echo $form->textField($model,'model',array('maxlength'=>255,'class'=>'form-control','readonly'=>'readonly')); ?>
		</div>
	</div>
	<div class="form-group">
		<label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'pic'); ?></label>
		<div class="col-xs-8">
			<?php if ($model->pic) {?>
				<div>
					<a target="_blank" href="<?php echo Yii::app()->params['OAUploadBaseUrl'] . '/purchase/' . $model->pic;?>">
						<img width="400px" src="<?php echo Yii::app()->params['OAUploadBaseUrl'] . '/purchase/' . $model->pic;?>"></a><br><br>
				</div>
			<?php } ?>
		</div>
	</div>
	<div class="form-group">
		<label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'tip'); ?></label>
		<div class="col-xs-8">
			<?php echo $form->textArea($model,'tip',array('maxlength'=>255,'class'=>'form-control','readonly'=>'readonly')); ?>
		</div>
	</div>
</div>
<div class="modal-footer">
	<a href="<?php echo $this->createUrl('index'); ?>" class="btn btn-default">返回</a>
</div>
<?php $this->endWidget(); ?>