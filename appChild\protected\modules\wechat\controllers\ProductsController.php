<?php

class ProductsController extends WeChatVisualController
{

    public $signPackage;
    public $childid;
    public $config;
    public $schoolid;
    public $childObj;
    public $userid;
    public $langPrefix;
    public $nextYear;
	public function beforeAction($action)
	{
		$url = 'https://wx.daystaracademy.cn/uniform';
		$appid = $this->appId;
		$state = $this->state;
		$redirect_uri = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=$appid&redirect_uri=$url&response_type=code&scope=snsapi_base&state=$state#wechat_redirect";

		$this->redirect($redirect_uri);
		Yii::import('common.models.products.*');
		$this->userid = $this->wechatUser->userid;
		$this->childid = isset($_COOKIE['childid']) ? $_COOKIE['childid'] : '' ;
		if ($this->childid && isset($this->myChildObjs[$this->childid])) {
			$this->childObj = $this->myChildObjs[$this->childid];
		} else {			
			$this->childObj = end($this->myChildObjs);
			$this->childid = $this->childObj->childid;
		}

		$this->schoolid = $this->childObj->schoolid;
		// 如果下学年分班，则采用下学年的学校ID
        $today = date('m-d');
        Yii::import('common.models.invoice.ChildReserve');
        $this->nextYear = ChildReserve::model()->findByAttributes(array('childid'=>$this->childid, 'stat'=>20));
        if ($this->nextYear && $today > '05-26') {
            $this->schoolid = $this->nextYear->schoolid;
        }
		// 语言前缀
		$this->langPrefix = 'cn';
		if (Yii::app()->language == 'en_us') {
			$this->langPrefix = 'en';
		}
		// 获取配置
		$this->config = Products::config();
		return true;
	}

	// 首页
	public function actionIndex()
	{
        $cats = $this->getCats();
        $this->render('index', array('cats'=>$cats));
	}

	// 显示分类下的商品
	public function actionProducts($cid)
	{
		$cid = Yii::app()->request->getParam('cid', '');
		if ($cid) {
			$titleLang = 'title_' . $this->langPrefix;
			$cat = ProductsCat::model()->findByPk($cid);
			$catTitle = $cat->$titleLang;
			$products = $this->getProducts($cid);
		}
        $this->render('products', array('products'=>$products, 'catTitle'=>$catTitle));
	}

	// 商品下单
	public function actionOrder()
	{
		$products = Yii::app()->request->getParam('data', '');
		// 开启事物
		$transaction = Yii::app()->subdb->beginTransaction();
		try{
			// 验证订单合法性并计算订单总金额
			$totalAmount = 0;
			$stockFlag = false;
			$paids = array();
			foreach ($products as $paid => $product) {
				$pid = $product['pid'];
				$num = $product['num'];
				// 查找库存
				$criteria = new CDbCriteria();
				$criteria->compare('t.school_id', $this->schoolid);
				$criteria->compare('t.pid', $pid);
				$criteria->compare('t.paid', $paid);
				$criteria->compare('product.status', Products::STATUS_NORMAL);
				$criteria->with = array('product', 'attr');
				$stock = ProductsStock::model()->find($criteria);
				if (!$stock) {
					throw new Exception('商品错误');
				}
				if ($stock->num < $num) {
					$stockFlag = true;
					$paids[] = $paid;
				} elseif (!$stockFlag) {
					// 减少库存
					$memo = '家长下单减少库存';
					$changeNum = 0 - $num;
					$res = $stock->changeStock($changeNum, $memo, $this->userid);
					if (!$res) {
						throw new Exception('商品库存变动失败。');
					}
				}
				$products[$paid]['cid'] = $stock->cid;
				$products[$paid]['unit_price'] = $stock->attr->unit_price;
				$totalAmount += $stock->attr->unit_price * $num;
			}

			// 判断库存
			if ($stockFlag) {
				throw new Exception(Yii::t('products', 'out of stock'));
			}

			if (!is_numeric($totalAmount) || $totalAmount < 0.01) {
				throw new Exception(Yii::t('products', 'Incorrect order amount'));
			}

			if (!isset($this->childObj->ivyclass)) {
				if ($this->nextYear) {
					$yid = $this->nextYear->calendar;
					$classid = $this->nextYear->classid;
				} else {
					throw new Exception(Yii::t('products', 'Children are not assigned'));
				}
			} else {
				$classid = $this->childObj->classid;
				$yid = $this->childObj->ivyclass->yid;
			}


			Yii::import('common.models.calendar.*');
			$calendarInfo = Calendar::model()->getCalendarInfo($yid);
			$calendarDay = explode(',', $calendarInfo['timepoints']);

			// 保存账单表
			Yii::import('common.models.invoice.Invoice');
			$invoice = new Invoice();
			$invoice->calendar_id = $yid;
			$invoice->amount = $totalAmount;
			$invoice->original_amount = $totalAmount;
			$invoice->nodiscount_amount = $totalAmount;
			$invoice->schoolid = $this->schoolid;
			$invoice->classid = $classid;
			$invoice->childid = $this->childid;
			$invoice->payment_type = 'uniform';
			$invoice->fee_type = 0;
			$invoice->startdate = current($calendarDay);
			$invoice->enddate = end($calendarDay);
			$invoice->title = '校服费 Uniform Fee';
			$invoice->userid = $this->userid;
			$invoice->duetime = time() + 86400;
			$invoice->timestamp = time();
			if (!$invoice->save()) {
				$error = current($invoice->getErrors());
				throw new Exception($error[0]);
			}

			// 保存商品订单表
			$pInvoice = new ProductsInvoice();
			$pInvoice->invoice_id = $invoice->invoice_id;
			$pInvoice->childid = $invoice->childid;
			$pInvoice->classid = $invoice->classid;
			$pInvoice->school_id = $invoice->schoolid;
			$pInvoice->amount = $invoice->amount;
			$pInvoice->product_count = count($products);
			$pInvoice->status = ProductsInvoice::STATS_UNPAID;
			$pInvoice->open_id = $this->openid;
			$pInvoice->updated_userid = $this->userid;
			$pInvoice->updated_time = $invoice->timestamp;
			if (!$pInvoice->save()) {
				$error = current($pInvoice->getErrors());
				throw new Exception($error[0]);
			}
			// 保存商品订单状态表
			$pInvoiceStatus = new ProductsInvoiceStatus();
			$pInvoiceStatus->iid = $pInvoice->id;
			$pInvoiceStatus->status = $pInvoice->status;
			$pInvoiceStatus->uid = $pInvoice->updated_userid;
			$pInvoiceStatus->timestamp = $pInvoice->updated_time;
			if (!$pInvoiceStatus->save()) {
				$error = current($pInvoiceStatus->getErrors());
				throw new Exception($error[0]);
			}
			// 保存商品订单关联表
			$totalAmount = 0;
			foreach ($products as $paid => $product) {
				// 保存商品订单关联表
				$pInvoiceItem = new ProductsInvoiceItem();
				$pInvoiceItem->cid = $product['cid'];
				$pInvoiceItem->pid = $product['pid'];
				$pInvoiceItem->paid = $paid;
				$pInvoiceItem->childid = $invoice->childid;
				$pInvoiceItem->classid = $invoice->classid;
				$pInvoiceItem->school_id = $invoice->schoolid;
				$pInvoiceItem->unit_price = $product['unit_price'];
				$pInvoiceItem->num = $product['num'];
				$pInvoiceItem->iid = $pInvoice->id;
				$pInvoiceItem->invoice_id = $invoice->invoice_id;
				$pInvoiceItem->updated_userid = $this->userid;
				$pInvoiceItem->updated_time = $invoice->timestamp;
				if (!$pInvoiceItem->save()) {
					$error = current($pInvoiceItem->getErrors());
					throw new Exception($error[0]);
				}
			}
			$transaction->commit();
			// 发送定时作废账单MQ
			CommonUtils::addProducer('product', "Products.invalidProductInvoice", $pInvoice->id, 60);
			$this->addMessage('state', 'success');
			$this->addMessage('message', 'success');
		} catch (Exception $e) {
			$transaction->rollBack();
			$this->addMessage('state', 'fail');
			$this->addMessage('data', $paids);
			$this->addMessage('message', $e->getMessage());
		}
		$this->showMessage();
	}

	// 购买记录
	public function actionHistory()
	{
		$status = array(ProductsInvoice::STATS_PAID, ProductsInvoice::STATS_CONFIRM, ProductsInvoice::STATS_SEND);
		$history = $this->getHistory($this->childid, $status);


		$titleLang = 'title_' . $this->langPrefix;
		$statusConfig = ProductsInvoice::statusConfig();
		$products = array();
		foreach ($history as $item) {
			$products[$item->id]['title'] = $item->product->$titleLang;
			$products[$item->id]['status'] = $statusConfig[$item->status];
			$i = 1;
			foreach ($this->config[$item->product->type]['item'] as $ck => $cv) {
				$attrIndex = 'attr' . $i;
				$products[$item->id]['attr'][] = Yii::t('products', $cv['option'][$item->attr->$attrIndex]);
				$i++;
			}
			// 商品图片
			$products[$item->id]['img']['all'] = array();
			$products[$item->id]['imgthumb']['all'] = array();
			foreach ($item->product->img as $img) {
				$imgUrl = Yii::app()->params['uploadBaseUrl'] . 'products/' . $img->img;
				$imgThumbUrl = Yii::app()->params['uploadBaseUrl'] . 'products/thumbs/' . $img->img;
				$products[$item->id]['img']['all'][] = $imgUrl;
				$products[$item->id]['imgthumb']['all'][] = $imgThumbUrl;
				if ($img->tag) {
					$tagArray = explode(';', $img->tag);
					foreach ($tagArray as $tag) {
						if ($tag) {
							$tag = Yii::t('products', $tag);
							if (!in_array($imgUrl, $products[$item->pid]['img'][$tag])) {
								$products[$item->id]['img'][$tag][] = $imgUrl;
								$products[$item->id]['imgthumb'][$tag][] = $imgThumbUrl;
							}
						}
					}
				}
			}
		}
		$this->render('history', array('products'=>$products));
	}

	// 缺货登记
	public function actionRegister()
	{
		$aid = Yii::app()->request->getPost('aid', 0);

		$this->addMessage('state', 'fail');
		if ($aid <= 0) {
			$this->addMessage('message', '商品详情ID不能为空');
			$this->showMessage();
		}
		$model = ProductsAttr::model()->findByPk($aid);
		if (!$model) {
			$this->addMessage('message', '未找到相关商品');
			$this->showMessage();
		}
		// 检查用户是否已经登记过
		$crit = new CDbCriteria();
		$crit->compare("register_by", $this->userid);
		$crit->compare("aid", $aid);
		$crit->compare("status", 0);
		$exist = ProductsOutofStock::model()->exists($crit);
		if ($exist) {
			$this->addMessage('message', '已经登记过该商品');
			$this->showMessage();
		}
		// 登记保存
		$outofStockModel = new ProductsOutofStock();
		$outofStockModel->setAttributes($model->getAttributes());
		$outofStockModel->school_id = $this->schoolid;
		$outofStockModel->child_id = $this->childid;
		$outofStockModel->aid = $aid;
		$outofStockModel->cid = $model->products->cid;
		$outofStockModel->register_by = $this->userid;
		$outofStockModel->register_at = time();
		$outofStockModel->register_openid = $this->openid;
		$outofStockModel->status = 0;
		if (!$outofStockModel->save()) {
			$error = current($outofStockModel->getErrors());
			$this->addMessage('message', '登记失败，' . $error[0]);
			$this->showMessage();
		}
		$this->addMessage('state', 'success');
		$this->addMessage('message', '登记成功');
		$this->showMessage();

	}

	// *****************************************************************************


	// 获取首页显示的所有分类
	public function getCats()
	{
		$cats = array();
		$catsModel = Products::getProductsCat($this->schoolid, 1);
		$i = 1;
		foreach ($catsModel as $item) {
			$titleLang = 'title_' . $this->langPrefix;
			$cats[$i]['title'] = $item->$titleLang;
			$cats[$i]['url'] = $this->createUrl('products', array('cid'=>$item->id));
			$cats[$i]['img'] = Yii::app()->params['uploadBaseUrl'] . 'productsCat/' . $item->img;
            $i++;
		}
		return $cats;
	}

	// 获取某个分类下的所有商品
	public function getProducts($cid)
	{
		$products = array();
		$criteria = new CDbCriteria();
		$criteria->compare('t.school_id', $this->schoolid);
		$criteria->compare('product.cid', $cid);
		$criteria->compare('product.status', Products::STATUS_NORMAL);
		$criteria->compare('attr.status', Products::STATUS_NORMAL);
		$criteria->with = array('product', 'attr');
		$criteria->order = 'product.sort DESC';
		$stocksModel = ProductsStock::model()->findAll($criteria);

		foreach ($stocksModel as $item) {
			// 过滤库存为0的商品
			// if ($item->num < 1) {
			// 	continue;
			// }

			$titleLang = 'title_' . $this->langPrefix;
			$introLang = 'intro_' . $this->langPrefix;

			$pid = $item->pid;
			$item->pid = "_$item->pid";
			// 商品简介
			$products[$item->pid]['title'] = $item->product->$titleLang;
			$products[$item->pid]['intro'] = $item->product->$introLang;
			$products[$item->pid]['unit_price'] = $item->product->unit_price;
			$products[$item->pid]['pid'] = $pid;

			// 商品图片
			$products[$item->pid]['img']['all'] = array();
			$products[$item->pid]['imgthumb']['all'] = array();
			foreach ($item->product->img as $img) {
				$imgUrl = Yii::app()->params['uploadBaseUrl'] . 'products/' . $img->img;
				$imgThumbUrl = Yii::app()->params['uploadBaseUrl'] . 'products/thumbs/' . $img->img;
				$products[$item->pid]['img']['all'][] = $imgUrl;
				$products[$item->pid]['imgthumb']['all'][] = $imgThumbUrl;
				if ($img->tag) {
					$tagArray = explode(';', $img->tag);
					foreach ($tagArray as $tag) {
						if ($tag) {
							$tag = Yii::t('products', $tag);
							if (!in_array($imgUrl, $products[$item->pid]['img'][$tag])) {
								$products[$item->pid]['img'][$tag][] = $imgUrl;
								$products[$item->pid]['imgthumb'][$tag][] = $imgThumbUrl;
							}
						}
					}
				}
			}
			$products[$item->pid]['img']['all'] = array_unique($products[$item->pid]['img']['all']);
			$products[$item->pid]['imgthumb']['all'] = array_unique($products[$item->pid]['imgthumb']['all']);

			// 商品属性
			$i = 1;
			$infoIndex = '';
			foreach ($this->config[$item->product->type]['item'] as $ck => $cv) {
				$attrIndex = 'attr' . $i;
				$infoIndex .= $item->attr->$attrIndex . '_';
				$products[$item->pid]['attr'][$ck]['title'] = Yii::t('products', $cv['title']);
				$products[$item->pid]['attr'][$ck]['item'][$item->attr->$attrIndex] = Yii::t('products', $cv['option'][$item->attr->$attrIndex]);
				$i++;
			}
			$infoIndex = trim($infoIndex, '_');
			// 商品库存
			$products[$item->pid]['info'][$infoIndex]['stock'] = $item->num;
			$products[$item->pid]['info'][$infoIndex]['price'] = $item->attr->unit_price;
			$products[$item->pid]['info'][$infoIndex]['paid'] = $item->paid;
		}
		return $products;
	}

	// 获取已购买的商品
	public function getHistory($childid, $status, $offset = 0, $limit = 100)
	{
		$criteria = new CDbCriteria();
		$criteria->compare('t.childid', $childid);
		$criteria->compare('invoice.status', $status);
		$criteria->with = array('invoice', 'product', 'attr');
		$criteria->offset = $offset;
		$criteria->limit = $limit;
		return ProductsInvoiceItem::model()->findAll($criteria);
	}
	
}
