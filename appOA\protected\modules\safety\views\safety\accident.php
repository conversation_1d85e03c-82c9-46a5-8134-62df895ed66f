<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Routines'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', '安全事故管理') ?></li>
    </ol>

    <div class="row">
        <div class="col-md-12 mb10">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->module->getMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-tabs'),
                'activeCssClass' => 'active',
            ));
            ?>
        </div>
        <div class="col-md-12">
            <a class="btn btn-primary pull-right" href="<?php echo $this->createUrl('update') ?>">

                <span class="glyphicon glyphicon-plus" aria-hidden="true"></span> <?php echo Yii::t('safety', '上传事故报告') ?></a>
            <?php
            $this->widget('ext.ivyCGridView.BsCGridView', array(
                'id' => 'safetyIncidentReport',
                'afterAjaxUpdate' => 'js:head.Util.modal',
                'dataProvider' => $dataProvider,
                'template' => "{items}{pager}",
                //状态为无效时标红
                //'rowCssClassExpression' => '( $data->active == 0 ? "active" : "" )',
                'colgroups' => array(
                    array(
                        "colwidth" => array(null, 200, 200, 160),
                    )
                ),
                'columns' => array(
                    'incident_name',
                    array(
                        'name'=>'incident_date',
                        'value'=> 'date("Y-m-d H:i:s", $data->incident_date)',
                    ),
                    'incident_place',
                    array(
                        'name' => Yii::t('laseregg', 'Edit'),
                        'value' => array($this, 'getButton'),
                    ),
                ),

            ));
            ?>
        </div>
    </div>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');

?>

<script>
function cbDelReport(data) {
    $.fn.yiiGridView.update('safetyIncidentReport');
}
function addNewRepoet(id) {
    $.ajax({
        url:'<?php echo $this->createUrl('newReport');?>',
        data:{id: id},
        type:'post',
        dataType:'json',
        success:function (data) {
            if(data.state == 'success'){
                cbDelReport();
            }else{
                resultTip({error: 'warning', msg: data.message});
            }
        }
    });
}
</script>
