<div>
    <div class='flex align-items  mb24 mt10'>
        <span class='font14 color3'>处理人：</span>
        <div class='flex1 flex align-items lib_class_handle'>
        </div>
    </div>
    <div class='reject redColor'>
        <div class='font14'><span class='el-icon-warning mr5 font16'></span>已驳回</div>
        <div class='mt8'>
        驳回理由：<span class='rejectMemo'></span>
        </div>
    </div>
    <form class="J_ajaxForm formStu" action="<?php echo $this->createUrl("saveNode");?>" method="post" onsubmit="return check(this)">
        <p class='itemReturn'>
            <label class="radio-inline font14 color6 lableHeight">
                <input type="radio"  name="balance" value="1"> <?php echo Yii::t('withdrawal','Fully Reimbursed');?>
            </label>
            <label class="radio-inline font14 color6 lableHeight">
                <input type="radio"  name="balance" value="0"> <?php echo Yii::t('withdrawal','Compensation Required');?>
            </label>
        </p>
        <textarea class="form-control mb24" rows="3" placeholder="<?php echo Yii::t('withdrawal','Comment');?>" name="remark" id='remark'  class='font14'></textarea>
        <div id="balance_items" class="hide">    
            <table  class="table tableCss">
                <thead>
                    <tr>
                        <td>物品名称</td>
                        <td>赔偿金额</td>
                        <td><?php echo Yii::t('withdrawal','Comment');?></td>
                        <td><?php echo Yii::t('withdrawal','Action');?></td>
                    </tr>
                </thead>
                <tbody class='tbody'>
                </tbody>
            </table>
            <div><span class='bluebg font14 cur-p' onclick='addTable()'><span class='el-icon-plus'></span>新增</span></div>
        </div>
        <input type="hidden" name="type" id="type"  value='lib_class'>
        <input type="hidden" name="applicationId" id="applicationId">
        <input type="hidden" name="node" id="node">
        <div class='modal-footer borderTopNone'>
            <label class="checkbox-inline">
                <input type="checkbox" name="status" value="1"> <?php echo Yii::t('withdrawal', 'Mark this step as completed'); ?>
            </label>
            <button type="submit" class="btn btn-primary ml24 submitSend"><?php echo Yii::t('global','Submit');?></button>
            <button type="button" class="btn btn-primary J_ajax_submit_btn ml24"><?php echo Yii::t('global','Submit');?></button>
        </div>
    </form>
</div>

<script>
     lib()
     $(document).ready(function() {
        $('input[name="amount[]"]').on('input', function() {
            var value = $(this).val();
            if (!/^[1-9]\d*|0$/.test(value)) {
                $(this).val('');
            }
        });
    });
     function lib(){
        let forms=childDetails.forms.find(item2 =>item2.key === 'lib_class')
        for(var key in forms.owner_info){
            if (key !== '2' && key !== '5') {
            $('.lib_class_handle').append('<div class="flex align-items mr16"><img src='+forms.owner_info[key].photoUrl+' alt="" class="img28"/><div class="font14 color3 ml8">'+forms.owner_info[key].name+'</div></div>')
            }
        }
        $('#type').val(forms.key)
        if(forms.confirm_status==2){
            $('.reject').show()
            $('.rejectMemo').html(forms.reject_memo)
        }else{
            $('.reject').hide()
            $('.rejectMemo').html(forms.reject_memo)
        }
        if(forms.status=='1'){
            $('input[type="checkbox"][name="status"][value='+forms.status+']').prop('checked', true);
        }
        if(forms.data.balance){
            $('input[type="radio"][name="balance"][value='+forms.data.balance+']').prop('checked', true);
            if(forms.data.balance=='0'){
                $('#balance_items').removeClass('hide')
            }else{
                $('#balance_items').addClass('hide')
            }
        }
        if(forms.data.remark!=''){
            $('#remark').val(forms.data.remark);
        }
        if(forms.data.goods && forms.data.goods.length){
            for(let i=0;i<forms.data.goods.length;i++){
                $('.tbody').append('<tr> <td><input type="text" class="form-control" name="name[]"  placeholder="<?php echo Yii::t('leave','Input');?>" value="'+forms.data.goods[i]["name"]+'"></td><td><input type="number" min="0" step="0.01" class="form-control" name="amount[]"  placeholder="<?php echo Yii::t('leave','Input');?>" value='+forms.data.goods[i].amount+'></td><td><input type="text" class="form-control" name="memo[]"  placeholder="<?php echo Yii::t('leave','Input');?>" value="'+forms.data.goods[i].memo+'"></td><td><span class="glyphicon glyphicon-trash cur-p mt8" onclick="delTable(this)"></span></td></tr>');
            }
        }else{
            for(let i=0;i<1;i++){
                $('.tbody').append('<tr> <td><input type="text" class="form-control" name="name[]"  placeholder="<?php echo Yii::t('leave','Input');?>"></td><td><input type="number" min="0" step="0.01" class="form-control" name="amount[]"  placeholder="<?php echo Yii::t('leave','Input');?>"></td><td><input type="text" class="form-control" name="memo[]"  placeholder="<?php echo Yii::t('leave','Input');?>"></td><td></td></tr>');
            }
        }
     }
     $('input[name="balance"]').click(function () {
        if ($(this).val() == '0') {
            $('#balance_items').removeClass('hide')
        }
        else {
            $('#balance_items').addClass('hide')
        }
    })
    function delTable(_this){
         $(_this).parent().parent().remove();
     }
    function addTable(){
        $('.tbody').append('<tr> <td><input type="text" class="form-control" name="name[]" placeholder="<?php echo Yii::t('leave','Input');?>"></td><td><input type="number" min="0" step="0.01" class="form-control" name="amount[]" placeholder="<?php echo Yii::t('leave','Input');?>"></td><td><input type="text" class="form-control" name="memo[]" placeholder="<?php echo Yii::t('leave','Input');?>"></td><td><span class="glyphicon glyphicon-trash cur-p mt8" onclick="delTable(this)"></span></td></tr>');
    }
    $('.submitSend').show()
    $('.J_ajax_submit_btn').hide()
    function check(_this){
        let balanceId=$('input[name="balance"]:checked').val()
        $('#J_fail_info').remove();
        var button = $(_this).find('button[type="submit"]');
        var flag = true, msg=[];
        if(!balanceId){
            flag = false;
            msg.push('<?php echo Yii::t("teaching","请选择归还状态");?>');
        }
        if(balanceId=='0'){
            $("input[name='name[]']").each(function(){
                if($(this).val()==""){
                    flag = false;
                    msg.push('<?php echo Yii::t("teaching","请输入物品名称");?>');
                }
            });
            $("input[name='amount[]']").each(function() {
                var val=$.trim($(this).val());
                if (val==''){
                    flag = false;
                    msg.push('<?php echo Yii::t("teaching","请输入金额");?>');
                }
            })
        }else{
            $("input[name='name[]']").each(function(){
                $(this).val('')
            });
            $("input[name='amount[]']").each(function() {
                $(this).val('')
            })
            $("input[name='memo[]']").each(function() {
                $(this).val('')
            })
        }
        if(flag){
            $('.submitSend').hide()
            $('.J_ajax_submit_btn').show()
            $('.formStu .J_ajax_submit_btn').click();
            return false;
        }
        else{
            $( '<span id="J_fail_info" class="text-warning"><i class="glyphicon glyphicon-remove text-warning"></i> ' + msg.join('、') + '！</span>' ).appendTo(button.parent()).fadeIn( 'fast' );
            return false;
        }
    }
</script>
