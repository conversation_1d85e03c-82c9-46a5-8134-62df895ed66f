<?php

/**
 * Controller is the customized base controller class.
 * All controller classes for this application should extend from this base class.
 */
class Controller extends CController {

    /**
     * @var string the default layout for the controller view. Defaults to '//layouts/column1',
     * meaning using a single column layout. See 'protected/views/layouts/column1.php'.
     */
    public $layout = '//layouts/column1';
	
	protected $securityKey = 'I3v4y7Onl8ine6Mi433ms';

    /**
     * @var array context menu items. This property will be assigned to {@link CMenu::items}.
     */
    public $menu = array();

    /**
     * @var array the breadcrumbs of the current page. The value of this property will
     * be assigned to {@link CBreadcrumbs::links}. Please refer to {@link CBreadcrumbs::links}
     * for more details on how to specify this property.
     */
    public $breadcrumbs = array();
    
    public $msg = array();

    public function init() {
        if( Yii::app()->params['maintain'] === true ){
            $this->redirect(Yii::app()->params['ivyschoolsUrl']);
        }

        if(!isset(Yii::app()->params['siteFlag']) || empty(Yii::app()->params['siteFlag'])){
            $this->redirect($this->createUrl('//Denied/Error', array('flag'=>'NO_SITE_FLAG')));
        }
        Yii::app()->language = (isset($_COOKIE['lang']) && $_COOKIE['lang'] == 'english') ? "en_us" : "zh_cn";
        Yii::app()->clientScript->registerScript('ym', 'function m(message){console.log(message);}', CClientScript::POS_END);

    }
    
     public function addMessage($key = '', $value='') {
        $accept = array('referer', 'refresh', 'state', 'message', 'callback', 'data', 'error');
        if ( in_array($key, $accept) ){
            $this->msg[$key]=$value;
        }
	}

	public function showMessage() {
		echo CJSON::encode($this->msg);
        Yii::app()->end();
	}
}