<?php

/**
 * This is the model class for table "ivy_as_ot_item".
 *
 * The followings are the available columns in table 'ivy_as_ot_item':
 * @property integer $id
 * @property integer $staff_uid
 * @property string $branchid
 * @property integer $classid
 * @property integer $ot_date
 * @property integer $hours
 * @property integer $type
 * @property string $memo
 * @property integer $status
 * @property integer $approvedate
 * @property integer $approver
 * @property integer $created
 * @property integer $creator
 */

class OTItem extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return OTItem the static model class
	 */
        const OT_WEEK_DAY = 1;
        const OT_WEEKEN = 2;
        const OT_HOLIDAY = 3;
        const OT_CONVER_HOLIDAY = 4; //调休
        public $ot_num;
        public $ot_hour;
        const OT_DURATION ='%s-8-31'; //调休有效期
    
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_as_ot_item';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('branchid, startdate, enddate,type, created', 'required'),
			array('staff_uid, classid, startdate, enddate, hours, type, status, approvedate, approver, created, creator', 'numerical', 'integerOnly'=>true),
			array('branchid', 'length', 'max'=>10),
			array('memo', 'safe'),
            array('hours','checkHours'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, staff_uid, branchid, classid, startdate, enddate, hours, type, memo, status, approvedate, approver, created, creator', 'safe', 'on'=>'search'),
		);
	}
    
    //验证小时与分钟的值必须选择并且剩余假期不能为空
    public function checkHours(){
        if (!$this->hasErrors()) {
            if ($this->hours==0){
                $this->addError('hours',  Yii::t('user', '请选择预计加班日期！'));
                return true;
            }
        }
    }

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
                    'appUser' => array(self::BELONGS_TO, 'User', 'staff_uid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'staff_uid' => 'Staff Uid',
			'branchid' => 'Branchid',
			'classid' => 'Classid',
			'ot_date' => 'Ot Date',
			'hours' => 'Hours',
			'type' => 'Type',
			'memo' => 'Memo',
			'status' => 'Status',
			'approvedate' => 'Approvedate',
			'approver' => 'Approver',
			'created' => 'Created',
			'creator' => 'Creator',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('staff_uid',$this->staff_uid);
		$criteria->compare('branchid',$this->branchid,true);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('ot_date',$this->ot_date);
		$criteria->compare('hours',$this->hours);
		$criteria->compare('type',$this->type);
		$criteria->compare('memo',$this->memo,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('approvedate',$this->approvedate);
		$criteria->compare('approver',$this->approver);
		$criteria->compare('created',$this->created);
		$criteria->compare('creator',$this->creator);

		return new CActiveDataProvider(get_class($this), array(
			'criteria'=>$criteria,
		));
	}
        
    static public function getOType($id=0){
        $data =  array(
            self::OT_WEEK_DAY => Yii::t('hr','周中延时加班'),
            self::OT_WEEKEN => Yii::t('hr','周末加班'),
            self::OT_HOLIDAY => Yii::t('hr','法定假日加班'),
            self::OT_CONVER_HOLIDAY => Yii::t('hr','调休'),
        );
        return ($id) ? $data[$id] : $data;
    }
    
     /*
     * 获取某员工已审核的或等待审核加班信息
     * @retun Array
     */
    static public function getAllOTItem($staffUid){
        $result = array();
        $leaveData = Yii::app()->db->createCommand()
                    ->select()
                    ->from('ivy_as_ot_item')
                    ->where('staff_uid=:staff_uid',array(':staff_uid'=>$staffUid))
                    ->order('startdate ASC')
                    ->queryAll();
        if (is_array($leaveData) && count($leaveData)){
            foreach ($leaveData as $val){
                if ($val['status'] == LeaveItem::STATUS_AGREE){
                    $result['sum']['agree']['hours'] = isset($result['sum']['agree']['hours']) ? $result['sum']['agree']['hours'] + $val['hours'] : $val['hours'];
                    $result['sub'][$val['type']]['agree']['hours'] = isset($result['sub'][$val['type']]['agree']['hours']) ? $result['sub'][$val['type']]['agree']['hours'] + $val['hours'] : $val['hours'];
                }elseif($val['status'] == LeaveItem::STATUS_WAITING){
                    $result['sum']['waiting']['hours'] = isset($result['sum']['waiting']['hours']) ? $result['sum']['waiting']['hours'] + $val['hours'] : $val['hours'];
                    $result['sub'][$val['type']]['waiting']['hours'] = isset($result['sub'][$val['type']]['waiting']['hours']) ? $result['sub'][$val['type']]['waiting']['hours'] + $val['hours'] : $val['hours'];
                }
            }
        }
        unset($leaveData);
        return $result;
    }
}