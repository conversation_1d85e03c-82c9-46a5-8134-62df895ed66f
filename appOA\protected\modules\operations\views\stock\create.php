<?php echo $this->renderPartial('_form', array('model'=>$model)); ?>

<div style="height:200px;overflow-y:auto;">
<?php $this->widget('ext.ivyCGridView.IvyCGridView', array(
	'id'=>'points-stock-grid',
	'dataProvider'=>$model->search(),
//	'filter'=>$model,
    'colgroups'=>array(
        array(
            "colwidth"=>array(null,80,null,100,50),
        )
    ),
	'columns'=>array(
		array(
            'name'=>'product_id',
            'value'=>'$data->Product->getContent()',
        ),
        array(
        	'name'=>'num',
            'value'=>'$data->num',
        ),
        array(
        	'name'=>'memo',
            'value'=>'$data->memo',
        ),
 		array(
            'name'=>'update_timestamp',
            'value'=>'OA::formatDateTime($data->update_timestamp)',
        ),
		array(
			'class'=>'CButtonColumn',
		 	'template' => '{delete}',
            'afterDelete'=>'function(link,success,data){ if(success && data) alert(data);}',
		),
	),
)); 
?>
</div>
<script type="text/javascript">
function callback(data)
{
    $.fn.yiiGridView.update('points-stock-grid');
}
</script>