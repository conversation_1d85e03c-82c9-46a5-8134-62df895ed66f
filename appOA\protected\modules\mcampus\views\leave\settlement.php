
<div  id='container' v-cloak>
    <div v-if='showTableList'>
        <div class='font16'>
        <a href="<?php echo Yii::app()->createUrl('/mcampus/leave/index', array('branchId' => $this->branchId,"category"=>"settle")); ?>"><span class='el-icon-arrow-left mr5'></span><span>考勤月列表</span></a>
            <!-- <span class='text-primary'>
                <span class='el-icon-arrow-left mr5'></span><span>考勤月列表</span>
            </span> -->
        <span class='color6'> <el-divider direction="vertical"></el-divider><span>考勤结算</span> </span>
        </div>
        <div class='color3 font14 mt20'>当前月：{{currentMonth}}</div>
        <div class='flex mt16'>
            <div class='flex1'>
                <el-dropdown v-if='!tableList.settle'> 
                    <el-button size="small"  type="primary" class='btn btn-primary'>
                    添加记录<i class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item @click.native='newLeave()'>请假</el-dropdown-item>
                        <el-dropdown-item  @click.native='newOvertime()'>加班</el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
            </div>
            <div v-if='Object.keys(tableList.list).length>0'>
                <button type="button" class="btn btn-default mr16" @click='cancelSettlement'  v-if='tableList.settle'>撤销本月结算</button>
                <button type="button" class="btn btn-default" @click='completeSettlement' v-if='!tableList.settle'>结算完成</button>
                <button type="button" class="btn btn-default completeBtn"  v-else><span class='el-icon-check'></span> 结算完成</button>
                <button type="button" class="btn btn-primary ml16" @click='exportData'>导出归档数据</button>
            </div>
            <div>
                <button type="button" class="btn btn-primary ml16" @click='exportOutOfOfficeData'>导出外出数据</button>
            </div>
        </div>
        <div class='mt20'>
            <table class="table" v-if='Object.keys(tableList.list).length>0'>
                <thead >
                    <tr>
                        <th width='250'>申请人</th>
                        <th>时间</th>
                        <th>类型</th>
                        <th>时长</th>
                        <th>提交时间</th>
                        <th>归档状态</th>
                        <th width='200' v-if='!tableList.settle'>操作</th>
                    </tr>
                </thead>
                <tbody v-for='(list,key,index) in tableList.list'>
                    
                    <tr v-for='(item,i) in list' class='tableTd'>
                        <!-- <td class='borderNone'></td> -->
                        <td class='' :rowspan='list.length+1'  v-if='i==0'>
                            <div class='flex align-items' v-if='i==0' >
                                <img class="img42" :src="tableList.user_info[key].photoUrl" />
                                <div class='flex1 word-break ml8'>
                                    <div class='color3 font14'>{{ tableList.user_info[key].name }}</div>
                                    <div class='color6 font12'>{{ tableList.user_info[key].hrPosition }}</div>
                                </div>
                            </div>
                        </td>
                        <!-- <td v-else-if='i==1' :rowspan='list.length'></td> -->
                        <td>
                            <div v-for='(_item,id) in item.children' class='font14' :class='id+1<item.children.length?"mb8":""'>
                                {{formatDate(_item.date)}}<span v-if='_item.date_end!=null'> - {{formatDate(_item.date_end)}}</span> 
                            </div>
                        </td>
                        <td>
                            <div v-for='(_item,id) in item.children'  class=' font14' :class='id+1<item.children.length?"mb8":""'>
                                <span v-if='configPage.leaveConfig && configPage.leaveConfig[item.type]'>{{configPage.leaveConfig[item.type].title}}</span>
                                <span v-if='item.type=="jiaban"'>
                                    <span class="label label-default fontWeight lablrBg">{{overtimeValue(_item.overtime_type,'overtimeType')}}</span>
                                    <span class="label label-default fontWeight lablrBg">{{overtimeValue(_item.overtime_settlement,'overtimeSettlement')}}</span>
                                </span>
                            </div>
                        </td>
                        <td>
                            <div v-for='(_item,id) in item.children'  class='font14' :class='id+1<item.children.length?"mb8":""'>
                                {{_item.duration_format}}<span v-if='_item.duration_start'> (<?php echo Yii::t('leave', 'From ') ?>{{_item.duration_start}})</span>
                            </div>
                        </td>
                        <td>
                        <span class='font14'>{{item.created_at}}</span>  <span v-if='item.is_hr' class="label label-default fontWeight lablrBg">人事添加</span>
                        </td>
                        <td>
                            <span v-if='item.def_state==1'><span class="label label-success fontWeight">归档</span></span>
                            <span v-if='item.def_state==0'><span class="label label-warning fontWeight">不归档</span></span>
                        </td>
                        <td v-if='!tableList.settle'>
                            <span class='text-primary font14 cur-p' v-if='item.def_state==0' @click='editStatus(item)'> 改为归档</span>
                            <span class='colorRed font14 cur-p' v-if='item.def_state==1'  @click='editStatus(item,"0")'>无需归档</span>
                            <span class='text-primary font14 cur-p ml20'  @click='cancelList(item.id)'>作废</span>
                            <span class='text-primary font14 cur-p ml20' v-if='item.type!="jiaban"'  @click='approval(item)'>修改</span>
                            <!-- <el-dropdown>
                                <span class="el-dropdown-link text-primary ml20">
                                    更多<i class="el-icon-arrow-down el-icon--right"></i>
                                </span>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item @click.native='approval(item)'>修改</el-dropdown-item>
                                    <el-dropdown-item @click.native='cancelList(item.id)'>作废</el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown> -->
                        </td>
                    </tr>
                </tbody>
            </table>
            <div v-else>
                <el-empty description="暂无数据"></el-empty>
            </div>
        </div>
    </div>
    <!-- 请假/修改 -->
    <div class="modal fade" id="addLeaveModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("directMessage", "添加请假记录");?></h4>
            </div>
            <div class="modal-body p24 overflow-y scroll-box font14" :style="'max-height:'+(modalHeight)+'px;overflow-x: hidden;'" v-if='balanceData.approver'>
                <div class='row'>
                    <div class='col-md-6'>
                        <div class='titleLeave mt4'>
                            申请人 <span class='text-primary cur-p' v-if='!detailData.applyData' @click='cutover'>更换</span>
                        </div>
                        <div class="flex mt8">
                            <div class="">
                                <img :src="currentStaff.photoUrl" class="img-circle img42">
                            </div>
                            <div class="flex1 ml15">
                                <h4 class="list-group-item-heading mt5 color3 font14">{{currentStaff.name}}</h4>
                                <p class="color6 font12">{{currentStaff.hrPosition}}</p>
                            </div>
                        </div>
                        <div class='titleLeave'><?php echo Yii::t('leave', 'Leave Type') ?></div>
                        <div class='mt8' v-if='configPage.leaveType'>
                            <el-select v-model="typeLeave" placeholder="<?php echo Yii::t('global', 'Please Select') ?>" size='small' style='width:100%'>
                                <el-option
                                v-for='(list,index) in configPage.leaveTypeShort'
                                :key="index"
                                :label="configPage.leaveConfig[list].title"
                                :value="list"
                                v-if="!(balanceData.balance[list]==0 && list=='tiaoxiu')"
                                :disabled="!balanceData.balance.start_date || (balanceData.balance[list]==0 && list!='bingjia')?true:false">
                                <span v-if='balanceData.balance.start_date'>
                                    <span>
                                        <span >{{configPage.leaveConfig[list].title}} 
                                            <span v-if='balanceData.balance[list] && balanceData.balance[list]!=0'>（{{translate("<?php echo Yii::t('leave', '%s Left'); ?>", countHour(balanceData.balance[list]))}}）</span>  
                                            <span v-if='balanceData.balance[list]==0'>（{{translate("<?php echo Yii::t('leave', '%s Left'); ?>","0 <?php echo Yii::t('site', 'Days') ?>")}}）</span>
                                        </span>
                                    </span>
                                </span>
                                <span class='color9' v-else>{{configPage.leaveConfig[list].title}} 
                                        <span>（<?php echo Yii::t('leave', 'No quota configured, please contact HR.') ?>）</span>  
                                    </span> 
                                </el-option>
                            </el-select>
                        </div>
                        <div class='titleLeave'><span v-if='typeLeave=="waichu"'><?php echo Yii::t('leave', 'Reason For Out of Office') ?></span><span v-else><?php echo Yii::t('leave', 'Reason For Leave Application') ?></span> </div>
                        <div class='mt8'>
                            <el-input
                                type="textarea"
                                :rows="2"
                                placeholder="<?php echo Yii::t('leave', 'Input') ?>"
                                v-model="memoLeave">
                            </el-input>
                        </div>
                        <div class='titleLeave'><?php echo Yii::t('leave', 'Upload Attachment') ?></div>
                        <div class='font12 color6 mt5'><?php echo Yii::t('leave', 'For sick leave of 2 consecutive days or more, you need to upload an official doctor note.') ?></div>
                        <div class='mt12 mb12' id="leaveBox">
                            <div id='leaveFile' class='leaveFile'>
                                <span class='el-icon-upload2'></span> <?php echo Yii::t('referral', 'Upload') ?>
                            </div>
                        </div>
                        <div>
                            <div class='' v-if='attachments.img && attachments.img.length>0'>
                                <div class='imgData mr8'  v-for='(list,i) in attachments.img'>
                                    <div v-if="list.types=='1'">
                                        <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Uploading");?></div>
                                    </div>
                                    <div v-else>
                                        <img class='imgList'  :src="list.url" alt="">
                                        <span aria-hidden="true" class='closeImg' @click.stop='delImg("img",list,i)'>×</span>
                                    </div>
                                </div>
                                <template v-if='loadingType==1'>
                                    <div class='imgData mr8'  v-for='(list,i) in loadingList'>
                                        <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Upload queue");?></div>
                                    </div>
                                </template>
                            </div>
                            <div>
                                <div class='mt16' v-if='attachments.other && attachments.other.length>0'>
                                    <div class='flex uploadFile' v-for='(list,index) in attachments.other'>
                                        <span class='glyphicon glyphicon-paperclip mr8'></span>
                                        <span v-if="list.types=='1'"><?php echo Yii::t("directMessage", "Uploading");?></span>
                                        <template v-else>
                                            <a target="_blank" class='flex1'  style='line-height:26px' :href='list.url' v-if='!list.isEdit'>{{list.name}}</a>
                                            <span class='flex1' v-else><input type="input" class='inputStyle'  @keyup.enter="saveFile(list,index)"  v-model='attachmentName' ></span>
                                            <template v-if='!list.isEdit'>
                                                <span class='glyphicon glyphicon-edit icon mr16' v-if='list.url!=""' @click.stop='list.isEdit=true,attachmentName=list.name'></span>
                                                <span class='glyphicon glyphicon-trash icon' v-if='list.url!=""' @click.stop='delImg("link",list,index)'></span>
                                            </template>
                                            <span style='width:90px'  class='text-right inline-block' v-else>
                                                <button type="button" class="btn btn-primary btn-xs" @click='saveFile(list,index)'><?php echo Yii::t("global", "Save");?></button>
                                                <button type="button" class="btn btn-default btn-xs" @click='list.isEdit=false'><?php echo Yii::t("global", "Cancel");?></button>
                                            </span>
                                        </template>
                                    </div>
                                </div>
                                <template v-if='loadingType==2'>
                                    <div class='flex uploadFile'  v-for='(list,i) in loadingList'>
                                        <span class='glyphicon glyphicon-paperclip mr8'></span>
                                        <a  class='flex1'  style='line-height:26px' > <?php echo Yii::t("directMessage", "Upload queue");?></a>
                                    </div>
                                </template>
                            </div>
                        </div>  
                        <div class="panel panel-default mt24" v-if='balanceData.substitute'>
                            <div class="panel-heading"><?php echo Yii::t('leave', 'Substitute Info') ?></div>
                            <div class="panel-body">
                                <div class='titleLeave mt0'><?php echo Yii::t('leave', 'Course Substitute Required?') ?></div>
                                <div class='mt8 font14'>
                                    <label class="radio-inline">
                                        <input type="radio" v-model='substitute' value="1"> <?php echo Yii::t('leave', 'Yes') ?>
                                    </label>
                                    <label class="radio-inline">
                                        <input type="radio" v-model='substitute' value="2"> <?php echo Yii::t('leave', 'No') ?>
                                    </label>
                                </div>
                                <div class='font14' v-if='substitute=="1"'>
                                    <div class='' v-for='(list,index) in configPage.substitute_type'>
                                        <label class="checkbox-inline mt12">
                                            <input type="checkbox" v-model="selectedItems" :value="list.value">{{list.title}}
                                        </label>
                                        <div v-if="selectedItems.includes(list.value)" class='mb12'>
                                            <div class='color6 font12 mt12'>{{list.desc}}</div>
                                            <div>
                                                <textarea class="form-control" placeholder= "<?php echo Yii::t('leave', 'Input') ?>" rows="2" v-model="list.text"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>                      
                    </div>
                    <div class='col-md-6'>
                        <div class='titleLeave mt0'><?php echo Yii::t('leave', 'Please select the date & time of leave application on the calendar and the dates respectively if it is more than one day.') ?></div>
                        <div class=' mt8'  v-if='calendarDate'>
                            <div class=''>
                                <v-date-picker  :locale="localeType" :attributes='addAttrsLeave' style='width:100%' :min-date='minData' :max-date='maxData'  :available-dates='availableDate' @dayclick="pickDateLeave"  v-model='selectedDate'/>
                            </div>
                            <div class='mt8'>
                                <div class='dateList font14 color3 flex mb8' v-for='(item,index) in addTimeListLeave'>
                                    <span class='flex1 pt8'>{{item.day}} {{item.text}} </span>
                                    <div>
                                        <el-select v-model="item.value" size='small' placeholder="<?php echo Yii::t('teaching', 'Please select') ?>" style='width:150px' @change='countDuration("leave")'>
                                            <el-option
                                            v-for="(item,index) in configPage.dateType"
                                            :key="item.key"
                                            :label="item.value"
                                            :value="item.key">
                                            </el-option>
                                        </el-select>
                                        <div class='mt8' v-if='choose_time.indexOf(item.value)!=-1'>
                                            <el-time-picker
                                                style='width:150px' 
                                                size='small' 
                                                
                                                v-model="item.time"
                                                value-format="HH:mm"
                                                format="HH:mm"
                                                placement="bottom-start"
                                                placeholder="<?php echo Yii::t('leave', 'Start Time');?>">
                                            </el-time-picker>
                                        </div>
                                    </div>
                                    <span class='el-icon-error font16 ml12 color9 pt8' @click='delLeaveTime(index,item,"leave")'></span>
                                </div>
                            </div>
                        </div>
                        <div v-if='totalDurationLeave!=""'>
                            <div class='titleLeave'><?php echo Yii::t('leave', 'Total Time') ?> <span class='color6 ml12 font12 fontNormal'><?php echo Yii::t('leave', 'One working day = 8 hours') ?></span></div>
                            <div  class='mt20'><span class='dateList font14 color3'>{{totalDurationLeave}}</span></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                <button type="button" class="btn btn-primary" @click="saveLeave('leave')"><?php echo Yii::t("newDS", "提交");?></button>
            </div>
            </div>
        </div>
    </div>
    <!-- 加班 -->
    <div class="modal fade" id="overtimeModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("directMessage", "添加加班记录");?></h4>
            </div>
            <div class="modal-body p24 overflow-y scroll-box font14" :style="'max-height:'+(modalHeight)+'px;overflow-x: hidden;'" v-if='balanceData.approver'>
                <div class='titleLeave mt4'>
                    申请人 <span class='text-primary cur-p' v-if='!detailData.applyData' @click='cutover'>更换</span>
                </div>
                <div class="flex mt8">
                    <div class="">
                        <img :src="currentStaff.photoUrl" class="img-circle img42">
                    </div>
                    <div class="flex1 ml15">
                        <h4 class="list-group-item-heading mt5 color3 font14">{{currentStaff.name}}</h4>
                        <p class="color6 font12">{{currentStaff.hrPosition}}</p>
                    </div>
                </div>
                <div class='titleLeave'>加班时间</div>
                <div class='mt8'>
                    <div class='text-center mb16'>
                        <v-date-picker :attributes='addAttrsOvertime' :available-dates='availableDate' @dayclick="pickDateOvertime"  v-model='selectedDate' :from-page="fromData"/>
                    </div>
                    <div >
                        <div class='dateList font14 color3 flex mb8' v-for='(item,index) in addTimeListOvertime'>
                            <span class='flex1'>{{item.day}} {{item.text}}</span>
                            <el-select v-model="item.value" size='small' placeholder="请选择" style='width:100px'  @change='countDuration("overtime")'>
                                <el-option
                                v-for="(item,i) in configPage.dateType"
                                :key="item.key"
                                :label="item.value"
                                :value="item.key">
                                </el-option>
                            </el-select>
                            <el-select v-model="item.overtime_type" size='small' class='ml12' placeholder="请选择" style='width:100px'>
                                <el-option
                                v-for="(item,index) in configPage.overtimeType"
                                :key="item.key"
                                :label="item.value"
                                :value="item.key">
                                </el-option>
                            </el-select>
                            <el-select v-model="item.overtime_settlement" size='small' class='ml12' placeholder="请选择" style='width:100px'>
                                <el-option
                                v-for="(item,index) in configPage.overtimeSettlement"
                                :key="item.key"
                                :label="item.value"
                                :value="item.key">
                                </el-option>
                            </el-select>
                            <span class='el-icon-error font16 ml12 color9'  @click='delLeaveTime(index,"overtime")'></span>
                        </div>
                    </div>
                </div>
                <div v-if='totalDurationOvertime!=""'>
                    <div class='titleLeave'>合计时长 <span class='color6 ml12 font12'>注：1个工作日按8小时计算</span></div>
                    <div  class='mt20'><span class='dateList font14 color3'> {{totalDurationOvertime}}</span></div>
                </div>
                <div class='titleLeave'>上传附件（选填）</div>
                    <div class='mt8'><el-button type="text" icon="el-icon-circle-plus-outline" id='overtimeFile' class='font14 bluebg'>上传</el-button></div>
                    <div>
                        <div class='' v-if='attachments.img && attachments.img.length>0'>
                            <div class='imgData mr8'  v-for='(list,i) in attachments.img'>
                                <div v-if="list.types=='1'">
                                    <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Uploading");?></div>
                                </div>
                                <div v-else>
                                    <img class='imgList' @click='uploadShowImg(list.url)'  :src="list.url" alt="">
                                    <span aria-hidden="true" class='closeImg' @click.stop='delImg("img",list,i)'>×</span>
                                </div>
                            </div>
                            <template v-if='loadingType==1'>
                                <div class='imgData mr8'  v-for='(list,i) in loadingList'>
                                    <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Upload queue");?></div>
                                </div>
                            </template>
                        </div>
                        <div>
                            <div class='mt16' v-if='attachments.other && attachments.other.length>0'>
                                <div class='flex uploadFile' v-for='(list,index) in attachments.other'>
                                    <span class='glyphicon glyphicon-paperclip mr8'></span>
                                    <span v-if="list.types=='1'"><?php echo Yii::t("directMessage", "Uploading");?></span>
                                    <template v-else>
                                        <a target="_blank" class='flex1'  style='line-height:26px' :href='list.url' v-if='!list.isEdit'>{{list.name}}</a>
                                        <span class='flex1' v-else><input type="input" class='inputStyle'  @keyup.enter="saveFile(list,index)"  v-model='attachmentName' ></span>
                                        <template v-if='!list.isEdit'>
                                            <span class='glyphicon glyphicon-edit icon mr16' v-if='list.url!=""' @click.stop='list.isEdit=true,attachmentName=list.name'></span>
                                            <span class='glyphicon glyphicon-trash icon' v-if='list.url!=""' @click.stop='delImg("link",list,index)'></span>
                                        </template>
                                        <span style='width:90px'  class='text-right inline-block' v-else>
                                            <button type="button" class="btn btn-primary btn-xs" @click='saveFile(list,index)'><?php echo Yii::t("global", "Save");?></button>
                                            <button type="button" class="btn btn-default btn-xs" @click='list.isEdit=false'><?php echo Yii::t("global", "Cancel");?></button>
                                        </span>
                                    </template>
                                </div>
                            </div>
                            <template v-if='loadingType==2'>
                                <div class='flex uploadFile'  v-for='(list,i) in loadingList'>
                                    <span class='glyphicon glyphicon-paperclip mr8'></span>
                                    <a  class='flex1'  style='line-height:26px' > <?php echo Yii::t("directMessage", "Upload queue");?></a>
                                </div>
                            </template>
                        </div>
                    </div>
                <div class='titleLeave'>加班理由</div>
                <div class='mt8'>
                    <el-input
                        type="textarea"
                        :rows="2"
                        placeholder="请输入加班理由"
                        v-model="memoOvertime">
                    </el-input>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                <button type="button" class="btn btn-primary" @click="saveLeave('overtime')"><?php echo Yii::t("newDS", "提交");?></button>
            </div>
            </div>
        </div>
    </div>
    <!-- 选择成员 -->
    <div class="modal fade" id="addStaffModal" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("newDS", "选择成员");?></h4>
                </div>
                <div class="modal-body relative" style='padding:0'>
                    <span class='borderLeftpos'></span>
                    <div style='max-height:600px;' class='p24 row'>
                        <div class='col-md-6 col-sm-6'>
                            <div>
                                <el-select v-model="schoolId" class='width100' placeholder="请选择" @change='getSchoolData'>
                                    <el-option
                                        v-for="(item,key,index) in schoolList"
                                        :key="key"
                                        :label="item.title"
                                        :value="key">
                                    </el-option>
                                </el-select>
                            </div>
                            <div class='mt10'>
                                <el-input
                                placeholder="搜索"
                                v-model='searchText' 
                                clearable>
                                </el-input>
                            </div>
                            <div  class="tab-pane active mt15 scroll-box" id="class" v-if='searchText==""'  style='max-height:460px;overflow-y:auto'>
                                <div v-for='(list,index) in currentDept.list' class='relative mb16'>
                                    <p  @click='dep_name=list.dep_name'>
                                        <span  class='font14 color606 cur-p'>{{list.dep_name}} </span>
                                        <span class='el-icon-arrow-down ml5' v-if='dep_name!=list.dep_name'></span>
                                        <span class='el-icon-arrow-up ml5' v-else></span>
                                    </p>
                                    <!-- <p  class='allCheck' v-if='dep_name==list.dep_name'><button class="btn btn-default pull-right btn-xs" type="button" @click='selectAll(list,index)' ><?php echo Yii::t("global", "Select All");?></button></p> -->
                                    <div  class='border scroll-box mr10 childList' v-if='dep_name==list.dep_name'>
                                        <div class="flex align-items listMedia" v-for='(item,key,idx) in list.user'>
                                            <div class='flex flex1' v-if='currentDept.user_info[item.uid]'>
                                                <img :src="currentDept.user_info[item.uid].photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                                <div class="flex1 ml10 flex1Text">
                                                    <div class=" font14 mt4 color3 text_overflow">{{currentDept.user_info[item.uid].name}}</div>
                                                    <div class="font12 color6 text_overflow">{{currentDept.user_info[item.uid].hrPosition}}</div>
                                                </div>
                                            </div>
                                            <div >
                                                <span class='cur-p font16 text-primary  el-icon-circle-plus-outline' v-if='!currentDept.user_info[item.uid].disabled' @click='assignStaff(item,index,idx)'></span>
                                                <span v-else>已选择</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else>
                                <div v-if='searchStaffList.length!=0' class='mt24 scroll-box'  style='max-height:460px;overflow-y:auto'>  
                                    <div class="flex align-items listMedia" v-for='(item,idx) in searchStaffList'>
                                        <div class='flex flex1' >
                                            <img :src="item.photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                            <div class="flex1 ml10 flex1Text">
                                                <div class=" font14 mt4 color3 text_overflow">{{item.name}}</div>
                                                <div class="font12 color6 text_overflow">{{item.hrPosition}}</div>
                                            </div>
                                        </div>
                                        <div >
                                            <span class='cur-p font16 text-primary  el-icon-circle-plus-outline' v-if='!currentDept.user_info[item.uid].disabled' @click='assignStaff(item,idx,"search")'></span>
                                            <span v-else>已选择</span>
                                        </div>
                                    </div> 
                                </div>
                                <div v-else-if='searchText!=""'>
                                        <div class='font14 color6 text-center mt20'>暂无数据</div>    
                                </div>
                            </div>
                        </div>
                        <div class='col-md-6 col-sm-6 borderLeft'>
                            <p class='mt10 font14 color606'>
                            <?php echo Yii::t("newDS", " ");?>{{staffSelected.length}}<?php echo Yii::t("newDS", "名成员");?>
                                <button class="btn btn-link pull-right btn-xs font14" v-if='staffSelected.length!=0' type="button" @click='batchDel("modal")'><?php echo Yii::t("newDS", "清空");?></button>
                            </p>
                            <div class='scroll-box p10 overflow-y' style='height:500px'>
                                <div class="flex align-items listMedia" v-for='(item,idx) in staffSelected'>
                                    <div class='flex flex1' v-if='allDept[item]'>
                                        <img :src="allDept[item].photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                        <div class="flex1 ml10 flex1Text">
                                            <div class=" font14 mt4 color3 text_overflow">{{allDept[item].name}}</div>
                                            <div class="font12 color6 text_overflow">{{allDept[item].hrPosition}}</div>
                                        </div>
                                    </div>
                                    <div @click='Unassign(item,idx)'>
                                        <span class='closeChild cur-p mt10 font16 el-icon-circle-close'></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                    </div>
                </div> 
                <div class="modal-footer">
                    <span class='text-left colorRed'><span class='glyphicon glyphicon-exclamation-sign'></span> 最多选择一名申请人</span>
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" :disabled="staffSelected.length==1?false:true" @click='confirmSatff()'>确定</button>
                </div>
            </div>
        </div>
    </div>
    <!-- 撤销 -->
    <div class="modal fade" id="delModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("global", "撤销"); ?></h4>
                </div>
                <div class="modal-body p24" >
                    确定作废吗？
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" @click='cancelList("del")'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 修改 -->
    <div class="modal fade" id="editLeaveModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("leave", "Modify Approved Leave");?></h4>
            </div>
            <div class="modal-body p24 overflow-y scroll-box font14" :style="'max-height:'+(modalHeight)+'px;overflow-x: hidden;'" v-if='balanceData.approver && editDetailData.applyData'>
                <div class='history'>
                    <span class='historyTitle'><?php echo Yii::t("leave", "Original Leave Information");?></span>
                    <div>
                        <span class='color6 font12'><?php echo Yii::t('leave', 'Type') ?>：</span><span class='color3 font12'>{{configPage.leaveConfig[editRow.type].title}}</span>
                    </div>
                    <div class='mt16'><span class='color6 font12'><?php echo Yii::t('labels', 'Time') ?>：</span><span><span v-for='(list,index) in editRow.children' class='historyDate'>{{formatDate(list.date)}} <span class='historyBorder'>{{list.duration_format}} <span v-if='list.duration_start'>（<?php echo Yii::t('leave', 'From ') ?>{{list.duration_start}}）</span></span></span></span></div>
                    <div class='color6 font12 mt16'><?php echo Yii::t('leave', 'Modification Reason (By HR)') ?> <span class='redColor'>*</span></div>
                    <div class='mt8'>
                        <el-input
                            type="textarea"
                            :rows="2"
                            placeholder="<?php echo Yii::t('leave', 'Input') ?>"
                            v-model="leaveModifyMemo">
                        </el-input>
                    </div>
                </div>
                <div class='row'>
                    <div class='col-md-6'>
                        <div class='titleLeave mt4'>
                            <?php echo Yii::t('leave', 'Applicant') ?>
                        </div>
                        <div class="flex mt8">
                            <div class="">
                                <img :src="editCurrentStaff.photoUrl" class="img-circle img42">
                            </div>
                            <div class="flex1 ml15">
                                <h4 class="list-group-item-heading mt5 color3 font14">{{editCurrentStaff.name}}</h4>
                                <p class="color6 font12">{{editCurrentStaff.hrPosition}}</p>
                            </div>
                        </div>
                        <div class='titleLeave'><?php echo Yii::t('leave', 'Leave Type') ?></div>
                        <div class='mt8' v-if='configPage.leaveType'>
                            <el-select v-model="typeLeave" placeholder="<?php echo Yii::t('global', 'Please Select') ?>" size='small' style='width:100%'>
                                <el-option
                                v-for='(list,index) in configPage.leaveTypeShort'
                                :key="index"
                                :label="configPage.leaveConfig[list].title"
                                :value="list"
                                v-if="!(balanceData.balance[list]==0 && list=='tiaoxiu')"
                                :disabled="!balanceData.balance.start_date || (balanceData.balance[list]==0 && list!='bingjia')?true:false">
                                <span v-if='balanceData.balance.start_date'>
                                    <span>
                                        <span >{{configPage.leaveConfig[list].title}} 
                                            <span v-if='balanceData.balance[list] && balanceData.balance[list]!=0'>（{{translate("<?php echo Yii::t('leave', '%s Left'); ?>", countHour(balanceData.balance[list]))}}）</span>  
                                            <span v-if='balanceData.balance[list]==0'>（{{translate("<?php echo Yii::t('leave', '%s Left'); ?>","0 <?php echo Yii::t('site', 'Days') ?>")}}）</span>
                                        </span>
                                    </span>
                                </span>
                                <span class='color9' v-else>{{configPage.leaveConfig[list].title}} 
                                        <span>（<?php echo Yii::t('leave', 'No quota configured, please contact HR.') ?>）</span>  
                                    </span> 
                                </el-option>
                            </el-select>
                        </div>
                        <div class='titleLeave'><span v-if='typeLeave=="waichu"'><?php echo Yii::t('leave', 'Reason For Out of Office') ?></span><span v-else><?php echo Yii::t('leave', 'Reason For Leave Application') ?></span> </div>
                        <div class='mt8'>
                            <el-input
                                type="textarea"
                                :rows="2"
                                disabled
                                placeholder="<?php echo Yii::t('leave', 'Input') ?>"
                                v-model="memoLeave">
                            </el-input>
                        </div>
                        <div class='titleLeave'><?php echo Yii::t('leave', 'Upload Attachment') ?></div>
                        <div class='font12 color6 mt5'><?php echo Yii::t('leave', 'For sick leave of 2 consecutive days or more, you need to upload an official doctor note.') ?></div>
                        <div class='mt12 mb12' id="leaveBox">
                            <div id='editLeaveFile' class='leaveFile'>
                                <span class='el-icon-upload2'></span> <?php echo Yii::t('referral', 'Upload') ?>
                            </div>
                        </div>
                        <div>
                            <div class='' v-if='attachments.img && attachments.img.length>0'>
                                <div class='imgData mr8'  v-for='(list,i) in attachments.img'>
                                    <div v-if="list.types=='1'">
                                        <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Uploading");?></div>
                                    </div>
                                    <div v-else>
                                        <img class='imgList'  :src="list.url" alt="">
                                        <span aria-hidden="true" class='closeImg' @click.stop='delImg("img",list,i)'>×</span>
                                    </div>
                                </div>
                                <template v-if='loadingType==1'>
                                    <div class='imgData mr8'  v-for='(list,i) in loadingList'>
                                        <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Upload queue");?></div>
                                    </div>
                                </template>
                            </div>
                            <div>
                                <div class='mt16' v-if='attachments.other && attachments.other.length>0'>
                                    <div class='flex uploadFile' v-for='(list,index) in attachments.other'>
                                        <span class='glyphicon glyphicon-paperclip mr8'></span>
                                        <span v-if="list.types=='1'"><?php echo Yii::t("directMessage", "Uploading");?></span>
                                        <template v-else>
                                            <a target="_blank" class='flex1'  style='line-height:26px' :href='list.url' v-if='!list.isEdit'>{{list.name}}</a>
                                            <span class='flex1' v-else><input type="input" class='inputStyle'  @keyup.enter="saveFile(list,index)"  v-model='attachmentName' ></span>
                                            <template v-if='!list.isEdit'>
                                                <span class='glyphicon glyphicon-edit icon mr16' v-if='list.url!=""' @click.stop='list.isEdit=true,attachmentName=list.name'></span>
                                                <span class='glyphicon glyphicon-trash icon' v-if='list.url!=""' @click.stop='delImg("link",list,index)'></span>
                                            </template>
                                            <span style='width:90px'  class='text-right inline-block' v-else>
                                                <button type="button" class="btn btn-primary btn-xs" @click='saveFile(list,index)'><?php echo Yii::t("global", "Save");?></button>
                                                <button type="button" class="btn btn-default btn-xs" @click='list.isEdit=false'><?php echo Yii::t("global", "Cancel");?></button>
                                            </span>
                                        </template>
                                    </div>
                                </div>
                                <template v-if='loadingType==2'>
                                    <div class='flex uploadFile'  v-for='(list,i) in loadingList'>
                                        <span class='glyphicon glyphicon-paperclip mr8'></span>
                                        <a  class='flex1'  style='line-height:26px' > <?php echo Yii::t("directMessage", "Upload queue");?></a>
                                    </div>
                                </template>
                            </div>
                        </div>  
                        <!-- <div class="panel panel-default mt24" v-if='balanceData.substitute'>
                            <div class="panel-heading"><?php echo Yii::t('leave', 'Substitute Info') ?></div>
                            <div class="panel-body">
                                <div class='titleLeave mt0'><?php echo Yii::t('leave', 'Course Substitute Required?') ?></div>
                                <div class='mt8 font14'>
                                    <label class="radio-inline">
                                        <input type="radio" v-model='substitute' value="1"> <?php echo Yii::t('leave', 'Yes') ?>
                                    </label>
                                    <label class="radio-inline">
                                        <input type="radio" v-model='substitute' value="2"> <?php echo Yii::t('leave', 'No') ?>
                                    </label>
                                </div>
                                <div class='font14' v-if='substitute=="1"'>
                                    <div class='' v-for='(list,index) in configPage.substitute_type'>
                                        <label class="checkbox-inline mt12">
                                            <input type="checkbox" v-model="selectedItems" :value="list.value">{{list.title}}
                                        </label>
                                        <div v-if="selectedItems.includes(list.value)" class='mb12'>
                                            <div class='color6 font12 mt12'>{{list.desc}}</div>
                                            <div>
                                                <textarea class="form-control" placeholder= "<?php echo Yii::t('leave', 'Input') ?>" rows="2" v-model="list.text"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>                       -->
                    </div>
                    <div class='col-md-6'>
                        <div class='titleLeave mt4'>
                        <?php echo Yii::t('leave', 'Please select the date & time of leave application on the calendar and the dates respectively if it is more than one day.') ?>
                        </div>
                        <!-- <div class='font12 color3 mt0'><?php echo Yii::t('leave', 'Please select the date & time of leave application on the calendar and the dates respectively if it is more than one day.') ?></div> -->
                        <div class=' mt8'  v-if='calendarDate'>
                            <div class=''>
                                <v-date-picker  :locale="localeType" :attributes='addAttrsLeave' style='width:100%' :min-date='minData' :max-date='maxData'  :available-dates='availableDate' @dayclick="pickDateLeave"  v-model='selectedDate'/>
                            </div>
                            <div class='mt8'>
                                <div class='dateList font14 color3 flex mb8' v-for='(item,index) in addTimeListLeave'>
                                    <span class='flex1 pt8'>{{item.day}} {{item.text}} </span>
                                    <div>
                                        <el-select v-model="item.value" size='small' placeholder="<?php echo Yii::t('teaching', 'Please select') ?>" style='width:150px' @change='countDuration("leave")'>
                                            <el-option
                                            v-for="(item,index) in configPage.dateType"
                                            :key="item.key"
                                            :label="item.value"
                                            :value="item.key">
                                            </el-option>
                                        </el-select>
                                        <div class='mt8' v-if='choose_time.indexOf(item.value)!=-1'>
                                            <el-time-picker
                                                style='width:150px' 
                                                size='small' 
                                                v-model="item.time"
                                                value-format="HH:mm"
                                                format="HH:mm"
                                                placement="bottom-start"
                                                placeholder="<?php echo Yii::t('leave', 'Start Time');?>">
                                            </el-time-picker>
                                        </div>
                                    </div>
                                    <span class='el-icon-error font16 ml12 color9 pt8' @click='delLeaveTime(index,item,"leave")'></span>
                                </div>
                            </div>
                        </div>
                        <div v-if='totalDurationLeave!=""'>
                            <div class='titleLeave'><?php echo Yii::t('leave', 'Total Time') ?> <span class='color6 ml12 font12 fontNormal'><?php echo Yii::t('leave', 'One working day = 8 hours') ?></span></div>
                            <div  class='mt20'><span class='dateList font14 color3'>{{totalDurationLeave}}</span></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                <button type="button" class="btn btn-primary" @click="saveEditLeave('leave')"><?php echo Yii::t("newDS", "提交");?></button>
            </div>
            </div>
        </div>
    </div>
</div>

<script>
const year = new Date().getFullYear();
const month = new Date().getMonth()+1;
const day = new Date().getDate();
var yearData = <?php echo CJSON::encode($yearList) ?>;
var getMonth = '<?php echo $_GET['month_data']?>';
var branchId = '<?php echo $_GET['branchId']?>';

var yearList = yearData.list.map((val,index,arr)=>{
	let json = {}
	json.title= val.value
	json.key = val.key+''
	return json
})
var height=document.documentElement.clientHeight;

var container=new Vue({
    el: "#container",
    data: {
        branchId:branchId,
        currentMonth:getMonth,
        modalHeight:height-220,
        configPage:{},
        tableList:{},
        balanceData:{},
        token:'',
        schoolId:'',
        schoolList:[],
        searchText:'',
        currentDept:{},
        allDept:{},
        staffSelected:[],
        year:2023,
        uploader:[],
        typeLeave:'',
        typeLeaveOther:'',
        addAttrsLeave:[],
        availableDate:{},
        selectedDate: {},
        fromData:{},
        value:'',
        textarea:'',
        currentStaff:{},
        addTimeListLeave:[],
        totalDurationLeave:'',
        addAttrsOvertime:[],
        addTimeListOvertime:[],
        memoLeave:'',
        loadingType:false,
        totalDurationOvertime:'',
        memoOvertime:'',
        inputHour:'',
        inputDay:'',
        date_end:'',
        dep_name:'',
        detailData:{},
        attachments:{
            img:[],
            other:[]
        },
        loadingList:[],
        loadingType:0,
        attachmentName:'',
        delId:'',
        showTableList:false,
        minData:'',
        maxData:'',
        calendarDate:false,
        localeType:'',
        substitute:null,
        selectedItems:[],
        choose_time:[],
        editDetailData:{},
        editCurrentStaff:{},
        editRow:{},
        leaveModifyMemo:''
    },
    created: function() {
        this.localeType='<?php echo Yii::app()->language;?>'=='zh_cn'?"zh-cn":"en-us"
        this.getConfig()
        this.getPage()
    },
    computed: {
        searchStaffList: function() {
            var search = this.searchText;
            var searchVal = ''; //搜索后的数据
            if(search) {
                searchVal =Object.values(this.currentDept.user_info).filter(function(product) {
                    return Object.keys(product).some(function(key) {
                        return String(product['name'].toLowerCase()).indexOf(search.toLowerCase()) !== -1;
                    })
                })
                return searchVal;
            }
            return this.searchStaffList;
        },
    },
    methods: {
        translate(text, ...args) {
            return text.replace(/%s/g, () => args.shift());
        },
        getConfig(){
            let that=this
            $.ajax({
                url: '<?php echo $this->createUrl("api") ?>',
                type: "post",
                dataType: 'json',
                data: {
                    url:'config'
                },
                success: function(data) {
                    if (data.state == 'success') {
                        that.choose_time=[]
                        data.data.substitute_type.forEach(item => {
                            item.show=false
                            item.text=''
                        });
                        data.data.dateType.forEach(item => {
                            if(item.choose_time){
                                that.choose_time.push(item.key)
                            }
                        });
                        that.year = data.data.startyear
                        that.configPage=data.data
                    }else{
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                },
            })
        },
        getPage(){
            let that=this
            $.ajax({
                url: '<?php echo $this->createUrl("archiveDetail") ?>',
                type: "post",
                dataType: 'json',
                data: {
                    archive_month: this.currentMonth,
                },
                success: function(data) {
                    if (data.state == 'success') {
                        that.showTableList=true
                        that.tableList=data.data
                    }else{
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                },
            })
        },
        editStatus(item,type){
            let that=this
            $.ajax({
                url: '<?php echo $this->createUrl("saveArchiveStatus") ?>',
                type: "post",
                dataType: 'json',
                data: {
                    id:item.id,
                    archive_month:type?0:this.currentMonth
                },
                success: function(data) {
                    if (data.state == 'success') {
                        resultTip({
                            msg: data.state
                        });
                        that.getPage()
                    }else{
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                },
            })
        },
        exportData(){
            let that=this
            $.ajax({
                url: '<?php echo $this->createUrl("archiveExport") ?>',
                type: "post",
                dataType: 'json',
                data: {
                    archive_month:this.currentMonth
                },
                success: function(data) {
                    if (data.state == 'success') {
                        var title=that.currentMonth+'月'+branchId+'归档数据.xlsx'   
                        const ws_name = "SheetJS";
                        var exportDatas = [];
                        for(var i=0;i<data.data.length;i++){
                            exportDatas.push(
                                {
                                '序号':i+1,
                                '<?php echo Yii::t('global', 'Name') ?>':data.data[i].name,
                                "<?php echo Yii::t('labels', '身份证号') ?>":data.data[i].card_id,
                                "<?php echo Yii::t('labels', '单位') ?>":data.data[i].school_id,
                                "<?php echo Yii::t('labels', '上级汇报对象') ?>":data.data[i].top_user_name,
                                "<?php echo Yii::t('labels', '职位') ?>":data.data[i].position,
                                "<?php echo Yii::t('labels', '报到日期') ?>":data.data[i].startdate,
                                "<?php echo Yii::t('labels', '离职日期') ?>":data.data[i].leavedate,
                                "<?php echo Yii::t('labels', '当月工作天数') ?>":'',
                                "<?php echo Yii::t('labels', '当月调整天数') ?>":'',
                                "<?php echo Yii::t('labels', '调休天数') ?>":data.data[i].tiaoxiu_use,
                                "<?php echo Yii::t('labels', '当月年假') ?>":data.data[i].nianjia_use,
                                "<?php echo Yii::t('labels', '当年度可休年假') ?>":data.data[i].nianjia_current,
                                "<?php echo Yii::t('labels', '上年度顺延年假') ?>":data.data[i].nianjia_last,
                                "<?php echo Yii::t('labels', '累计至上月已休年假') ?>":data.data[i].last_nianjia_use,
                                "<?php echo Yii::t('labels', '当年累计已休年假') ?>":data.data[i].total_nianjia,
                                "<?php echo Yii::t('labels', '当年度剩余年假') ?>":data.data[i].nianjia,
                                "<?php echo Yii::t('labels', '当月病假') ?>":data.data[i].bingjia_use,
                                "<?php echo Yii::t('labels', '当年度可休病假') ?>":data.data[i].bingjia_current,
                                "<?php echo Yii::t('labels', '累计至上月已休病假') ?>":data.data[i].last_bingjia_use,
                                "<?php echo Yii::t('labels', '累计已休病假') ?>":data.data[i].total_bingjia,
                                "<?php echo Yii::t('labels', '剩余病假') ?>":data.data[i].bingjia,
                                "<?php echo Yii::t('labels', '当月事假') ?>":data.data[i].shijia_use,
                                "<?php echo Yii::t('labels', '累计至上月已休事假') ?>":data.data[i].last_shijia_use,
                                "<?php echo Yii::t('labels', '累计已休事假') ?>":data.data[i].total_shijia,

                                "<?php echo Yii::t('labels', '当年可休调休小时') ?>":data.data[i].tiaoxiu_current,
                                "<?php echo Yii::t('labels', '当年已休调休小时') ?>":data.data[i].tiaoxiu_total_hour,

                                "<?php echo Yii::t('labels', 'OT Hr (Week)') ?>":data.data[i].jiaban_week,
                                "<?php echo Yii::t('labels', 'OT Hr (weekend)') ?>":data.data[i].jiaban_weekend,
                                "<?php echo Yii::t('labels', 'OT Hr (Holiday)') ?>":data.data[i].jiaban_holiday,
                                "<?php echo Yii::t('labels', '当月已休寒暑假') ?>":data.data[i].hanshujia_use,
                                "<?php echo Yii::t('labels', '累计至上月已休寒暑假') ?>":data.data[i].last_hanshujia_use,
                                "<?php echo Yii::t('labels', '当年可休寒暑假') ?>":data.data[i].hanshujia_current,
                                "<?php echo Yii::t('labels', '上年顺延寒暑假') ?>":data.data[i].hanshujia_last,
                                "<?php echo Yii::t('labels', '剩余寒暑假') ?>":data.data[i].hanshujia,
                                "<?php echo Yii::t('labels', '年假当月日期') ?>":data.data[i].nianjia_current_month_date,
                                "<?php echo Yii::t('labels', '病假当月日期') ?>":data.data[i].bingjia_current_month_date,
                                "<?php echo Yii::t('labels', '事假当月日期') ?>":data.data[i].shijia_current_month_date,
                                "<?php echo Yii::t('labels', '年假累计日期') ?>":data.data[i].nianjia_current_date,
                                "<?php echo Yii::t('labels', '病假累计日期') ?>":data.data[i].bingjia_total_date,
                                "<?php echo Yii::t('labels', '事假累计日期') ?>":data.data[i].shijia_total_date,
                                "<?php echo Yii::t('labels', '备注') ?>":data.data[i].remark,
                                });
                        }
                        var wb=XLSX.utils.json_to_sheet(exportDatas,{
                            origin:'A1',// 从A1开始增加内容
                            header: ['序号',
                                '<?php echo Yii::t('global', 'Name') ?>',
                                '<?php echo Yii::t('labels', '身份证号') ?>',
                                '<?php echo Yii::t('labels', '单位') ?>',
                                '<?php echo Yii::t('labels', '上级汇报对象') ?>',
                                '<?php echo Yii::t('labels', '职位') ?>',
                                '报到日期','离职日期','当月工作天数','当月调整天数','调休天数','当月年假','当年度可休年假','上年度顺延年假','累计至上月已休年假','当年累计已休年假','当年度剩余年假','当月病假','当年度可休病假','累计至上月已休病假','累计已休病假','剩余病假','当月事假','累计至上月已休事假','累计已休事假','当年可休调休小时','当年已休调休小时','OT Hr (Week)','OT Hr (weekend)','OT Hr (Holiday)','当月已休寒暑假','累计至上月已休寒暑假','当年可休寒暑假','上年顺延寒暑假','剩余寒暑假','年假当月日期','病假当月日期','事假当月日期','年假累计日期','病假累计日期','事假累计日期','备注'],
                        });
                        const workbook = XLSX.utils.book_new();
                        XLSX.utils.book_append_sheet(workbook, wb, ws_name);
                        const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                        const blob = new Blob([wbout], {type: 'application/octet-stream'});
                        let link = document.createElement('a');
                        link.href = URL.createObjectURL(blob);
                        link.download = title;
                        link.click();
                        setTimeout(function() {
                            // 延时释放掉obj
                            URL.revokeObjectURL(link.href);
                            link.remove();
                        }, 500);
                    }else{
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                },
            })
        },
        exportOutOfOfficeData(){
          /**导出外出的数据**/
          let that=this
            $.ajax({
                url:'<?php echo $this->createUrl("exportOutOfOfficeData") ?>',
                type:'post',
                dataType: 'json',
                data:{
                    archive_month:this.currentMonth
                },
                success: function(data) {
                    if (data.state == 'success') {
                        var title=that.currentMonth+'月'+branchId+'外出数据.xlsx'
                        const ws_name = "SheetJS";
                        var exportDatas = [];
                        for(var i=0;i<data.data.length;i++){
                            console.log(data.data[i]['waichu_total_days'])
                            for (var j=0; j<data.data[i]['waichu_total_days'].length;j++){
                               var waichu_day = data.data[i]['waichu_total_days'][j].date
                               var waichu_duration = data.data[i]['waichu_total_days'][j].date_duration
                               var waichu_leave_memo = data.data[i]['waichu_total_days'][j].leave_memo
                               var created_time = data.data[i]['waichu_total_days'][j].created_time
                               exportDatas.push(
                                    {
                                        '序号':j==0 ? i+1 : '',
                                        '<?php echo Yii::t('global', 'Name') ?>':j==0 ? data.data[i].name : '',
                                        "<?php echo Yii::t('labels', '身份证号') ?>":j==0 ? data.data[i].card_id : '',
                                        "<?php echo Yii::t('labels', '单位') ?>":j==0 ? data.data[i].school_id : '',
                                        "<?php echo Yii::t('labels', '上级汇报对象') ?>":j==0 ? data.data[i].top_user_name : '',
                                        "<?php echo Yii::t('labels', '职位') ?>":j==0 ? data.data[i].position : '',
                                        "<?php echo Yii::t('labels', '报到日期') ?>":j==0 ? data.data[i].startdate : '',
                                        "<?php echo Yii::t('labels', '离职日期') ?>":j==0 ? data.data[i].leavedate : '',
                                        "<?php echo Yii::t('labels', '当月工作天数') ?>":'',
                                        "<?php echo Yii::t('labels', '当月调整天数') ?>":'',
                                        "<?php echo Yii::t('labels', '外出日期') ?>":waichu_day,
                                        "<?php echo Yii::t('labels', '外出时长') ?>":waichu_duration,
                                        "<?php echo Yii::t('labels', '外出事由') ?>":waichu_leave_memo,
                                        "<?php echo Yii::t('labels', '申请时间') ?>":created_time,
                                        "<?php echo Yii::t('labels', '备注') ?>":j==0 ? data.data[i].remark  : '',
                                   });
                            }

                        }
                        var wb=XLSX.utils.json_to_sheet(exportDatas,{
                            origin:'A1',// 从A1开始增加内容
                            header: [
                                '序号',
                                '<?php echo Yii::t('global', 'Name') ?>',
                                '<?php echo Yii::t('labels', '身份证号') ?>',
                                '<?php echo Yii::t('labels', '单位') ?>',
                                '<?php echo Yii::t('labels', '上级汇报对象') ?>',
                                '<?php echo Yii::t('labels', '职位') ?>',
                                '报到日期',
                                '离职日期',
                                '当月工作天数',
                                '当月调整天数',
                                '外出日期',
                                '外出时长',
                                '外出事由',
                                '申请时间',
                                '备注'
                            ],
                        });
                        const workbook = XLSX.utils.book_new();
                        XLSX.utils.book_append_sheet(workbook, wb, ws_name);
                        const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                        const blob = new Blob([wbout], {type: 'application/octet-stream'});
                        let link = document.createElement('a');
                        link.href = URL.createObjectURL(blob);
                        link.download = title;
                        link.click();
                        setTimeout(function() {
                            // 延时释放掉obj
                            URL.revokeObjectURL(link.href);
                            link.remove();
                        }, 500);
                    }else{
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                }
            })
        },
        completeSettlement(){
            let that=this
            $.ajax({
                url: '<?php echo $this->createUrl("archiveFinish") ?>',
                type: "post",
                dataType: 'json',
                data: {
                    archive_month:this.currentMonth
                },
                success: function(data) {
                    if (data.state == 'success') {
                        that.getPage()
                        resultTip({
                            msg: data.state
                        });
                    }else{
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                },
            })
        },
        cancelSettlement(){
            let that=this
            $.ajax({
                url: '<?php echo $this->createUrl("archiveCancel") ?>',
                type: "post",
                dataType: 'json',
                data: {
                    archive_month:this.currentMonth
                },
                success: function(data) {
                    if (data.state == 'success') {
                        that.getPage()
                        resultTip({
                            msg: data.state
                        });
                    }else{
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                },
            })
        },
        countHour(num){
            if(!num){
                return
            }
            let b = num / 8
            let c = num % 8
            var text=''
            if(parseInt(b)==0){
                text=c + '小时'
            }else if(c==0){
                text= parseInt(b) + '天'
            }else{
                text=parseInt(b) + '天' + c + '小时'
            }
            return text
        },
        formatDate(dateString,type){
            const date=dateString+''
            const dates={
                year: date.slice(0, 4),
                month: date.slice(4, 6),
                day: date.slice(6, 8),
            }
            const year = date.slice(0, 4);
            const month = date.slice(4, 6);
            const day = date.slice(6, 8);
            return type?dates[type]:year+'.'+month+'.'+ day
        },
        getweekday(date){
            var weekArray = new Array("日", "一", "二", "三", "四", "五", "六");
            var week = weekArray[new Date(date).getDay()];//注意此处必须是先new一个Date
            return '星期'+week;
        },
        overtimeValue(id,list){
            if(this.configPage[list]){
                var list=this.configPage[list].find((item,index,arr)=>{
                    return item.key == id
                })
                return list?list.value:''
            } 
        },
        newLeave(){
            this.addTimeListLeave=[]
            this.addTimeListOvertime=[]
            this.addAttrsLeave=[]
            this.addAttrsOvertime=[]
            this.memoOvertime=''
            this.memoLeave=''
            this.substitute=null
            this.selectedDate=null
            this.totalDurationLeave=''
            this.totalDurationOvertime=''
            this.attachments={
                img:[],
                other:[]
            }
            this.selectedItems=[]
            this.configPage.substitute_type.forEach(item => {
                item.show=false
                item.text=''
            });
            this.currentStaff=this.configPage.userInfo
            this.getBalance(this.configPage.userInfo.uid,'leave')
        },
        newOvertime(){
            this.addTimeListLeave=[]
            this.addTimeListOvertime=[]
            this.addAttrsLeave=[]
            this.addAttrsOvertime=[]
            this.memoOvertime=''
            this.memoLeave=''
            this.substitute=null
            this.selectedDate=null
            this.totalDurationLeave=''
            this.totalDurationOvertime=''
            this.attachments={
                img:[],
                other:[]
            }
            this.selectedItems=[]
            this.configPage.substitute_type.forEach(item => {
                item.show=false
                item.text=''
            });
            this.currentStaff=this.configPage.userInfo
            this.detailData={}
            this.getBalance(this.configPage.userInfo.uid,'overtime')
        },
        getBalance(id,init,types){
            this.addBalanceType=init
            this.calendarDate=false
            let that=this
            $.ajax({
                url: '<?php echo $this->createUrl("api") ?>',
                type: "post",
                dataType: 'json',
                data: {
                    url:'balance',
                    startyear:this.year,
                    staff_id:id
                },
                success: function(data) {
                    if (data.state == 'success') {
                        that.balanceData=data.data
                        that.token=data.data.uploadToken
                        if(that.uploader.length!=0) {
                            for(var i=0;i<that.uploader.length;i++){
                            that.uploader[i].destroy();
                            }
                        }
                        that.calendarDate=true
                        if(init=='leave'){
                            if(types){
                                $("#editLeaveModal").modal('show')  
                                that.$nextTick(()=>{
                                    config['token'] = that.token;
                                    config['browse_button'] ='editLeaveFile';
                                    var uploader=new plupload.Uploader(config);
                                    that.uploader.push(uploader);
                                    uploader.init();
                                })
                            }else{
                                that.addAttrsLeave=[]
                                that.addTimeListLeave=[]
                                that.addTimeListOvertime=[]
                                that.addAttrsOvertime=[]
                                that.memoLeave=''
                                that.memoOvertime=''
                                that.typeLeave=''
                                that.attachments={
                                    img:[],
                                    other:[]
                                }
                                $("#addLeaveModal").modal('show') 
                                that.$nextTick(()=>{
                                    config['token'] = that.token;
                                    config['browse_button'] ='leaveFile';
                                    var uploader=new plupload.Uploader(config);
                                    that.uploader.push(uploader);
                                    uploader.init();
                                })
                            }
                        }
                        if(init=='overtime'){
                            $("#overtimeModal").modal('show')  
                            that.$nextTick(()=>{
                                config['token'] = that.token;
                                config['browse_button'] ='overtimeFile';
                                var uploader=new plupload.Uploader(config);
                                that.uploader.push(uploader);
                                uploader.init();
                            })
                        }
                        
                        
                    }else{
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                },
            })
        },
        pickDateLeave(day) {
            // if (day.date < this.minData || day.date > this.maxData) {
            //     return
            // }
            const idx = this.addAttrsLeave.findIndex(d => d.id === day.id);
            if (idx >= 0) {
                this.addAttrsLeave.splice(idx, 1);
                this.addTimeListLeave.splice(idx, 1);
            } else {
                let startDate=new Date(this.selectedDate)
                var yearStart = startDate.getFullYear();
                var monthStart =startDate.getMonth();
                var dayStart = startDate.getDate();
                this.addTimeListLeave.push({text:this.getweekday(day.id),value:'',day:day.id})
                this.addAttrsLeave.push({
                    highlight: {
                        fillMode: 'solid',
                    },
                    id:day.id,
                    dates: new Date(yearStart, monthStart, dayStart),
                })
            }
        },
        
        countDuration(type){
            if(type=='leave'){
                const arr = this.addTimeListLeave.map(item1 => this.configPage.dateType.find((item2) => {return item1.value!='' && item1.value === item2.key})).filter(item2 => item2 !== undefined);;
                const result = arr.reduce(function(prev, cur) {
                    return cur.duration + prev;
                }, 0);
                if(result==0){
                    this.totalDurationLeave=''
                }else{
                    this.totalDurationLeave=this.countHour(result)
                }
            }else{
                const arr = this.addTimeListOvertime.map(item1 => this.configPage.dateType.find((item2) => {return item1.value!='' && item1.value === item2.key})).filter(item2 => item2 !== undefined);;
                const result = arr.reduce(function(prev, cur) {
                    return cur.duration + prev;
                }, 0);
                if(result==0){
                    this.totalDurationOvertime=''
                }else{
                    this.totalDurationOvertime=this.countHour(result)
                }
            }
        },
        pickDateOvertime(day){
            // if (day.date < this.minData || day.date > this.maxData) {
            //     return
            // }
            const idx = this.addAttrsOvertime.findIndex(d => d.id === day.id);
            if (idx >= 0) {
                this.addTimeListOvertime.splice(idx, 1);
                this.addAttrsOvertime.splice(idx, 1);
            }else{
                let startDate=new Date(this.selectedDate)
                var yearStart = startDate.getFullYear();
                var monthStart =startDate.getMonth();
                var dayStart = startDate.getDate();
                this.addTimeListOvertime.push({text:this.getweekday(day.id),value:'',day:day.id,overtime_type:''})
                this.addAttrsOvertime.push({
                    highlight: {
                        fillMode: 'solid',
                    },
                    id:day.id,
                    dates: new Date(yearStart, monthStart, dayStart),
                })
            }
        },
        // delLeaveTime(index,type){
        //     if(type=='leave'){
        //         this.addTimeListLeave.splice(index, 1); 
        //         this.addAttrsLeave.splice(index, 1); 
        //         this.countDuration('leave')
        //     }else{
        //         this.addTimeListOvertime.splice(index, 1); 
        //         this.addAttrsOvertime.splice(index, 1); 
        //         this.countDuration('overtime')
        //     }
        //     this.selectedDate=null
        // },
        delLeaveTime(index,item,type){
            if(type=='leave'){
                const idx = this.addAttrsLeave.findIndex(d => d.id === item.day);
                this.addTimeListLeave.splice(index, 1); 
                this.addAttrsLeave.splice(idx, 1); 
                this.countDuration('leave')
            }else{
                const idx = this.addAttrsOvertime.findIndex(d => d.id === item.day);
                this.addTimeListOvertime.splice(index, 1); 
                this.addAttrsOvertime.splice(idx, 1); 
                this.countDuration('overtime')
            }
            this.selectedDate=null
        },
        saveLeave(type){
            let that=this
            var dateList=[]
            var fileList=[]
            this.attachments.img.forEach(list => {
                fileList.push({file:list.key,type:'img',name:list.name})
            });
            this.attachments.other.forEach(list => {
                fileList.push({file:list.key,type:'link',name:list.name})
            });
            var data={}
            if(type=='leave'){
                if(this.typeLeave=='other'){
                    if(this.date_end==''){
                        return
                    }
                    dateList=[{
                        date:this.date_end[0],
                        date_end:this.date_end[1],
                        day:this.inputDay,
                        hours:this.inputHour
                    }]
                }else{
                    this.addTimeListLeave.forEach(list => {
                        dateList.push({type:list.value,date:list.day,duration_start:list.time})
                    });
                }
                if(this.balanceData.substitute){
                        substitute_info={
                            need:this.substitute,
                            list:[]
                        }
                        if(this.substitute==null){
                            that.subBtn=false
                            resultTip({
                                error: 'warning',
                                msg: '<?php echo Yii::t('leave', 'Course Substitute Required?') ?>'
                            });
                            return
                        }
                        for(var i=0;i<this.configPage.substitute_type.length;i++){
                            let list=this.configPage.substitute_type[i]
                            if(this.selectedItems.includes(list.value)){
                                if(list.text.trim()==''){
                                    that.subBtn=false
                                    resultTip({
                                        error: 'warning',
                                        msg: '<?php echo Yii::t('leave', 'Input') ?> '+list.title
                                    });
                                    return
                                }
                                substitute_info.list.push({
                                    value:list.value,
                                    desc:list.text
                                })
                            }
                        }
                        data={
                            url:'apply',
                            startyear:this.year,
                            staff_id:this.currentStaff.uid,
                            type:this.typeLeave,
                            memo:this.memoLeave,
                            dateList:dateList,
                            fileList:fileList,
                            substitute_info:substitute_info,
                            isHr:true
                        }
                    }else{
                        data={
                            url:'apply',
                            startyear:this.year,
                            staff_id:this.currentStaff.uid,
                            type:this.typeLeave,
                            memo:this.memoLeave,
                            dateList:dateList,
                            fileList:fileList,
                            isHr:true
                        }
                    }
                if(this.memoLeave==''){
                    resultTip({
                        error: 'warning',
                        msg: '请假理由不能为空'
                    });
                    return
                }
            }else{
                this.addTimeListOvertime.forEach(list => {
                    dateList.push({overtime_type:list.overtime_type,date:list.day,type:list.value,overtime_settlement:list.overtime_settlement})
                });
                data={
                    substitute_info:{
                        need:false,
                        list:[]
                    },
                    url:'apply',
                    startyear:this.year,
                    staff_id:this.currentStaff.uid,
                    type:'jiaban',
                    memo:this.memoOvertime,
                    dateList:dateList,
                    fileList:fileList,
                    isHr:true
                }
                if(this.memoOvertime==''){
                    resultTip({
                        error: 'warning',
                        msg: '加班理由不能为空'
                    });
                    return
                }
            }
            // return
            $.ajax({
                url: '<?php echo $this->createUrl("api") ?>',
                type: "post",
                dataType: 'json',
                data: data,
                success: function(data) {
                    if (data.state == 'success') {
                        resultTip({
                            msg: data.state
                        });
                        that.substitute=null
                        that.selectedDate=null
                        that.totalDurationLeave=''
                        that.totalDurationOvertime=''
                        that.addTimeListLeave=[]
                        that.addTimeListOvertime=[]
                        that.addAttrsLeave=[]
                        that.addAttrsOvertime=[]
                        that.memoOvertime=''
                        that.memoLeave=''
                        that.selectedDate=null
                        that.attachments={
                            img:[],
                            other:[]
                        }
                        that.configPage.substitute_type.forEach(item => {
                            item.show=false
                            item.text=''
                        });
                        that.typeLeave=''
                        if(type=='leave'){
                            $("#addLeaveModal").modal('hide')
                        }else if(type=='overtime'){
                            $("#overtimeModal").modal('hide')
                        }
                        that.getPage()
                    }else{
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                },
            })
        },
        getSchoolData(){
            let that=this
            $.ajax({
                url: '<?php echo $this->createUrl("getAllDepartment") ?>',
                type: "post",
                dataType: 'json',
                data: {
                    school_id:this.schoolId
                },
                success: function(data) {
                    if (data.state == 'success') {
                        that.currentDept=data.data
                        that.allDept=Object.assign(that.allDept, data.data.user_info)
                        
                    }else{
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                },
            })
        },
        cutover(){
            let that=this
            if(Object.keys(that.schoolList).length!=0){
                $("#addStaffModal").modal('show')  
                return
            }
            
            $.ajax({
                url: '<?php echo $this->createUrl("schoolList") ?>',
                type: "post",
                dataType: 'json',
                data: {
                },
                success: function(data) {
                    if (data.state == 'success') {
                        that.schoolList=data.data.list  
                        that.schoolId=data.data.current_school 
                        $("#addStaffModal").modal('show')  
                        that.getSchoolData()
                    }else{
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                },
            })
        },
        assignStaff(list,index,idx){
            if(idx=='search'){
                this.searchStaffList[index].disabled=true
            }else{
                Vue.set(this.currentDept.user_info[list.uid], 'disabled', true);
            }
            this.staffSelected.push(list.uid)
        },
        Unassign(list,index){
            if(this.currentDept.user_info[list]){
                Vue.set(this.currentDept.user_info[list], 'disabled', false);
            }
            this.staffSelected.splice(index,1)
        },
        confirmSatff(){
            this.currentStaff=this.allDept[this.staffSelected[0]]
            this.getBalance(this.staffSelected[0],this.addBalanceType)
            $("#addStaffModal").modal('hide')  
        },
        approval(row){
            this.editRow=row
            console.log(this.editRow)
            let that=this
            $.ajax({
                url: '<?php echo $this->createUrl("api") ?>',
                type: "post",
                dataType: 'json',
                data: {
                    url:'detail',
                    id:row.id
                },
                success: function(data) {
                    if (data.state == 'success') {
                        that.editDetailData=data.data
                        that.memoLeave=that.editDetailData.applyData.leave_memo
                        that.typeLeave=that.editDetailData.applyData.type
                        that.editCurrentStaff=data.data.staffInfo[that.editDetailData.applyData.leave_staff]
                        that.attachments={
                            img:[],
                            other:[]
                        }
                        data.data.fileList.forEach(item => {
                            if(item.type=='img'){
                                that.attachments.img.push({
                                    url:item.fileUrl,
                                    key:item.file,
                                    name:item.name
                                })
                            }else{
                                that.attachments.other.push({
                                    url:item.fileUrl,
                                    key:item.file,
                                    name:item.name
                                })
                            }
                        });
                        that.addTimeListLeave=[]
                        that.addAttrsLeave=[]
                        data.data.itemData.forEach(item => {
                            let startDate= item.date+''
                            var yearStart = startDate.slice(0, 4);
                            var monthStart =startDate.slice(4, 6);
                            var dayStart = startDate.slice(6, 8);
                            let day=yearStart+'-'+monthStart+'-'+dayStart
                            that.addTimeListLeave.push({id:item.id,text:that.getweekday(day),value:item.date_type,day:day,time:item.duration_start})
                            that.addAttrsLeave.push({
                                highlight: {
                                    fillMode: 'solid',
                                },
                                id:day,
                                dates: new Date(yearStart, monthStart-1, dayStart),
                            })
                        });
                        that.selectedDate=null
                        that.countDuration('leave')
                        if(row.type=='jiaban'){
                            that.getBalance(data.data.applyData.leave_staff,'overtime')
                        //     $("#overtimeModal").modal('show')  
                        }else{
                            that.getBalance(data.data.applyData.leave_staff,'leave','edit')
                        }
                    }else{
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                },
            })
        },
        getSummaries(param){
            this.$nextTick(() => {
                const tds = document.querySelectorAll('.summaryTable .el-table__footer tr>td');
                const summaryTables = document.querySelectorAll('.summaryTables .el-table__footer tr>td');
                if(tds.length>0){
                    tds[1].colSpan=3;
                    tds[2].style.display='none'
                    tds[3].style.display='none'
                }
                if(summaryTables.length>0){
                    summaryTables[1].colSpan=3;
                    summaryTables[2].style.display='none'
                    summaryTables[3].style.display='none'
                }
            }, 1000)
            return ['合计时长',this.detailData.applyData.duration_format]
        },
        cancelList(id){
            if(id!='del'){
                this.delId=id
                $("#delModal").modal('show')  
                return
            }
            let that=this
            $.ajax({
                url: '<?php echo $this->createUrl("api") ?>',
                type: "post",
                dataType: 'json',
                data:{
                    url:'cancel',
                    id:this.delId
                },
                success: function(data) {
                    if (data.state == 'success') {
                        resultTip({
                            msg: data.state
                        });
                        that.getPage()
                        $("#delModal").modal('hide')  
                    }else{
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                },
            })
        },
        delImg(type,list,index){
            if(type=='img'){
                this.attachments.img.forEach((item,index) => {
                    if (item._id==list._id) {
                        this.attachments.img.splice(index, 1)
                    } 
                })
            }else{
                this.attachments.other.forEach((item,index) => {
                    if (item._id==list._id) {
                        this.attachments.other.splice(index, 1)
                    } 
                })
            }
        },
        saveFile(list,index) {
            Vue.set(this.attachments.other[index], 'name',this.attachmentName);
            Vue.set(this.attachments.other[index], 'isEdit', false);
        },
        saveEditLeave(type){
            let that=this
            var dateList=[]
            var fileList=[]
            this.attachments.img.forEach(list => {
                fileList.push({file:list.key,type:'img',name:list.name})
            });
            this.attachments.other.forEach(list => {
                fileList.push({file:list.key,type:'link',name:list.name})
            });
            var data={}
            if(this.typeLeave=='other'){
                if(this.date_end==''){
                    return
                }
                dateList=[{
                    date:this.date_end[0],
                    date_end:this.date_end[1],
                    day:this.inputDay,
                    hours:this.inputHour
                }]
            }else{
                this.addTimeListLeave.forEach(list => {
                    dateList.push({id:list.id,type:list.value,date:list.day,duration_start:list.time})
                });
            }
            // if(this.balanceData.substitute){
            //     substitute_info={
            //         need:this.substitute,
            //         list:[]
            //     }
            //     if(this.substitute==null){
            //         that.subBtn=false
            //         resultTip({
            //             error: 'warning',
            //             msg: '<?php echo Yii::t('leave', 'Course Substitute Required?') ?>'
            //         });
            //         return
            //     }
            //     for(var i=0;i<this.configPage.substitute_type.length;i++){
            //         let list=this.configPage.substitute_type[i]
            //         if(this.selectedItems.includes(list.value)){
            //             if(list.text.trim()==''){
            //                 that.subBtn=false
            //                 resultTip({
            //                     error: 'warning',
            //                     msg: '<?php echo Yii::t('leave', 'Input') ?> '+list.title
            //                 });
            //                 return
            //             }
            //             substitute_info.list.push({
            //                 value:list.value,
            //                 desc:list.text
            //             })
            //         }
            //     }
            //     data={
            //         url:'hrChange',
            //         id:this.editRow.id,
            //         leaveType:this.leaveType,
            //         dateList:dateList,
            //         fileList:fileList,
            //         substitute_info:substitute_info,
            //         leaveModifyMemo:this.leaveModifyMemo

            //     }
            // }else{
            data={
                url:'hrChange',
                id:this.editRow.id,
                leaveType:this.typeLeave,
                dateList:dateList,
                fileList:fileList,
                leaveModifyMemo:this.leaveModifyMemo
            }
            // }
            if(this.memoLeave==''){
                resultTip({
                    error: 'warning',
                    msg: '请假理由不能为空'
                });
                return
            }
            if(this.leaveModifyMemo==''){
                resultTip({
                    error: 'warning',
                    msg: '修改原因（HR填写）不能为空'
                });
                return
            }
            $.ajax({
                url: '<?php echo $this->createUrl("api") ?>',
                type: "post",
                dataType: 'json',
                data: data,
                success: function(data) {
                    if (data.state == 'success') {
                        resultTip({
                            msg: data.state
                        });
                        that.leaveModifyMemo==''
                        that.substitute=null
                        that.selectedDate=null
                        that.totalDurationLeave=''
                        that.totalDurationOvertime=''
                        that.addTimeListLeave=[]
                        that.addTimeListOvertime=[]
                        that.addAttrsLeave=[]
                        that.addAttrsOvertime=[]
                        that.memoOvertime=''
                        that.memoLeave=''
                        that.selectedDate=null
                        that.attachments={
                            img:[],
                            other:[]
                        }
                        that.configPage.substitute_type.forEach(item => {
                            item.show=false
                            item.text=''
                        });
                        that.typeLeave=''
                        $("#editLeaveModal").modal('hide')
                        that.getPage()
                    }else{
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                },
            })
        },
    }
})
var config = {
        runtimes: 'html5,flash,html4', //上传模式,依次退化
        container: 'container', //上传区域DOM ID，默认是browser_button的父元素，
        browse_button: '', //上传选择的点选按钮，**必需**
        token: '',
        flash_swf_url: '/js/plupload/Moxie.swf', // flash的相对地址
        auto_start: true, //选择文件后自动上传，若关闭需要自己绑定事件触发上传
        // multi_selection: false, //不允许多选
        url:'https://upload-z1.qiniup.com',
        init: {
            'FilesAdded': function(up, files) {
                container.disabledUpload=true
                let fileType=files[0].type.split('/')
                if(fileType[0]=="image"){
                  container.loadingType=1
                }else{
                  container.loadingType=2
                }
                container.loadingList=files
                up.start();
            },
            BeforeUpload: function(up, file) {
                //设置参数
                up.setOption({
                    multipart_params:{token:container.token}
                })
                let fileType=file.type.split('/')
                if(fileType[0]=="image"){
                    container.attachments.img.push({types:'1'})
                }else{
                    container.attachments.other.push({types:'1',title:' <?php echo Yii::t("directMessage", "Uploading");?>'})  
                }
                container.loadingList.splice(0,1)
            },
            UploadProgress: function(up, file) {
                $('#progress').css('width', file.percent+'%').html(file.percent+'%');
            },
            'FileUploaded': function(up, file, info) {
                var response = eval('('+info.response+')');
                if(info.status == 200){
                    let fileType=response.data.mimeType.split('/')
                    if(fileType[0]=="image"){
                        container.attachments.img.splice(container.attachments.img.length-1,1)
                        container.attachments.img.push(response.data)
                    }else{
                        response.data.isEdit=false
                        container.attachments.other.splice(container.attachments.other.length-1,1)
                        container.attachments.other.push(response.data)
                    }
                }
            },
            'Error': function(up, err, errTip) {
                if(err.response){
                    var response = eval('('+err.response+')');
                }
                else{
                    var response = {message: err.message};
                }
                if(response.error == 'token not specified'){
                    up.stop();
                    // $.ajax({
                    //     url: '<?php echo $this->createUrl("getQiniuToken") ?>',
                    //     type: "post",
                    //     dataType: 'json',
                    //     data: {
                    //         linkId: container.handId
                    //     },
                    //     success: function(data) {
                    //         if (data.state == 'success') {
                    //             container.qiniuToken=data.data
                    //             config['uptoken'] = data.data;
                    //             up.setOption("multipart_params", {"token":data.data,})
                    //             up.start();
                    //         } else {
                    //             resultTip({
                    //                 error: 'warning',
                    //                 msg: data.msg
                    //             });
                    //         }
                    //     },
                    //     error: function(data) {

                    //     },
                    // })
                }
            },
            'UploadComplete': function(up, file) {
                //队列文件处理完毕后,处理相关的事情
            }
        }
    };
</script>

<style>
    .titleLeave{
        font-size:14px;
        color:#333333;
        margin-top:24px;
        font-weight:bold
    }
    .dateList{
        background: #F7F7F8;
        padding: 8px;
        border-radius: 4px;
        align-items:center
    }
    .tableTd td{
        padding:16px !important; 
        vertical-align: middle !important;
    }
    .approvalTitle .line{
        width: 6px;
        height: 14px;
        background: #4D88D2;
        border-radius: 1px;
    }
    .approvalTitle .flex1{
        font-size:14px;
        color:#333333;
        font-weight:bold
    }
    .approvalData{
        padding:16px;
        background: #FAFAFA;
        border-radius: 2px;
    }
    .detailWidth{
        color:#666666;
        font-size:14px;
        margin-right:12px;
        width:60px
    }
    .labelWarning{
        padding:2px 5px;
        margin-left:10px
    }
    .borderLeftpos {
        display: inline-block;
        border-left: 1px solid #E4E7ED;
        height: 100%;
        width: 1px;
        position: absolute;
        left: 50%;
    }
    .allCheck{
        position: absolute;
        right: 10px;
        top: 0;
    }
    .border{
        border: 1px solid #DCDFE6;
        padding: 16px;
        border-radius: 4px;
    }
    .listMedia{
        padding:8px;
        border: 1px solid #fff;
    }
    .listMedia:hover{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        border: 1px solid #4D88D2;
        cursor: pointer;
    }
    .colorRed{
        color:rgb(217, 83, 79)
    }
    thead{
        background:#fafafa;
        color:#333
    }
    .table > thead > tr > th{
        vertical-align: middle;
        border-bottom:none;
    }
    thead th{
        padding:12px 16px !important;
        font-size:14px
    }
    /* .table > tbody + tbody{
        border-bottom: 1px solid #dddddd;
        border-top: 1px solid #dddddd;
    } */
    .borderNone{
        border-top:none !important;
    }
    .completeBtn{
        border:1px solid #4cae4c;
        color:#4cae4c
    }
    .fontWeight{
        font-weight:400
    }
    .lablrBg{
        color:#333;
        background:#EBEDF0;
    }
    .label{
        font-size:100%
    }
    .imgList{
        width: 72px;
        height: 72px;
        border-radius: 4px;
        object-fit: cover;
        margin-bottom:8px
    }
    .uploadLoading{
        width: 72px;
        height: 72px;
        background: #D9D9D9;
        border-radius: 4px;
        line-height: 72px;
        text-align: center;
    }
    .imgData{
        position: relative;
        display: inline-block;
    }
    .imgData span{    
        position: absolute;
        right: 3px;
        top: 3px;
        background: #333333;
        opacity: 1;
        color: #fff;
        width: 17px;
        height: 17px;
        border-radius: 50%;
        font-weight: 200;
        text-align: center;
        line-height: 15px;
        font-size: 19px;
    }
    .uploadFile {
        font-size:14px;
        align-items: center;
        padding:5px
    }
    .uploadFile .icon{
        display:none
    }
    .uploadFile:hover{
        background: #F7F7F8;

    }
    .uploadFile:hover .icon{
        display:block
    }
    .uploadFile a{
        color:#4D88D2
    }
    .leaveFile{
        padding:10px;
        border-radius: 4px;
        border: 1px dashed #4D88D2;
        color:#4D88D2;
        text-align:center;
        font-size:14px;
        cursor: pointer;
    }
    .mt0{
        margin-top:0
    }
    .history{
        padding:40px 24px 16px;
        background: #F7F7F8;
        border-radius: 4px;
        margin-bottom:30px;
        background: rgba(240,173,78,0.1);
        border: 1px solid #F0AD4E;
        position: relative;
    }
    .historyTitle{
        position: absolute;
        left: 0;
        top: 0;
        padding: 3px 12px;
        background: #F5A429;
        color: #fff;
        border-radius: 2px 0px 4px 0px;
    }
    .historyDate{
        padding:4px 6px;
        font-size: 12px;
        color: #333333;
        line-height: 12px;
        margin-right:12px;
        background: #FFFFFF;
        border-radius: 2px;
    }
    .historyBorder{
        border-left: 1px solid #999999;
        margin-left:5px;
        padding-left:5px
    }
    .redColor{
        color:#D9534F
    }
</style>