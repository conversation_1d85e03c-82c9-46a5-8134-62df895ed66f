<div class="form">
<?php
	foreach (Yii::app()->user->getFlashes() as $key => $message) {
		echo '<div class="flash-' . $key . '">' . $message . "</div>\n";
	}
?>
<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'ivy-pcategory-form',
	'enableAjaxValidation'=>false,
)); 
?>

	<p class="note">Fields with <span class="required">*</span> are required.</p>
	
	<div class="row">
		<?php echo $form->labelEx($model,'fullId'); ?>
		<?php echo $form->textField($model,'fullId',array('size'=>60,'maxlength'=>10,'disabled'=>'disabled')); ?>
		<?php echo $form->error($model,'fullId'); ?>
	</div>
	
	<div class="row">
		<?php echo $form->labelEx($model,'abbId'); ?>
		<?php echo $form->textField($model,'abbId',array('size'=>60,'maxlength'=>10)); ?>
		<?php echo $form->error($model,'abbId'); ?>
	</div>
	
	<div class="row">
		<?php echo $form->labelEx($model,'title'); ?>
		<?php echo $form->textField($model,'title',array('size'=>60,'maxlength'=>255)); ?>
		<?php echo $form->error($model,'title'); ?>
	</div>

	<div class="row">
		<?php echo $form->labelEx($model,'parentCategory'); ?>
		<?php echo $form->dropDownList($model, 'parentCategory', $categoryList, array('empty'=>Yii::t("global", 'Please Select'))); ?>
		<?php echo $form->error($model,'parentCategory'); ?>
	</div>

	<div class="row buttons">
		<?php echo CHtml::submitButton($model->isNewRecord ? 'Create' : 'Save'); ?>
	</div>

<?php $this->endWidget(); ?>

</div><!-- form -->