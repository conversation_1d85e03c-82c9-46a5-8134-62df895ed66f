<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'t-form',
	'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm'),
)); ?>
<div class="table_list">
    <table width="100%">
        <?php foreach ($items as $item):?>
        <tr>
            <td><?php echo CHtml::link($item->title, array('//child/invoice/viewInvoice', 'invoiceid'=>$item->invoice_id), array('target'=>'_blank'))?></td>
            <td><?php echo $item->amount;?></td>
            <td><?php echo $item->status == Invoice::STATS_UNPAID ? '将会作废' : '不处理';?></td>
        </tr>
        <?php endforeach;?>
    </table>
</div>
<?php echo CHtml::hiddenField('id', $id);?>
<div class="pop_bottom">
    <button type="button" class="btn fr" id="J_dialog_close"><?php echo Yii::t('global','Cancel');?></button>
    <button class="btn fr btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
</div>
<?php $this->endWidget(); ?>
<script type="text/javascript">
function cb()
{
    parent.ginv('<?php echo $this->createUrl('/child/invoice/task');?>');
}
</script>