<!-- 加载底部导航栏 -->
<?php
//$this->branchSelectParams['extraUrlArray'] = array('//mgrades/attendance/index');
//$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<div class="container-fluid">
    <!-- 面包屑导航 -->
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', 'G6-12 Schedule Management'); ?></li>
    </ol>
    <div class="row">
        <div class="col-md-2">
            <?php
            $this->widget('zii.widgets.CMenu',array(
                'items'=> $this->siderMenu,
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-right background-gray mb10'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>
        <div class="col-md-2">
            <div class="list-group">
                <a href="<?php echo $this->createUrl('report', array('t'=>'r01'))?>" class="list-group-item<?php if($t=='r01'){echo ' active';}?>"><?php echo Yii::t('attends', 'Attendance Submission Report'); ?></a>
                <a href="<?php echo $this->createUrl('report', array('t'=>'r02'))?>" class="list-group-item<?php if($t=='r02'){echo ' active';}?>"><?php echo Yii::t('attends', 'Daily Attendance by Period'); ?></a>
                <a href="<?php echo $this->createUrl('report', array('t'=>'r03'))?>" class="list-group-item<?php if($t=='r03'){echo ' active';}?>"><?php echo Yii::t('attends', 'Daily Grades Attendance Report'); ?></a>
                <a href="<?php echo $this->createUrl('report', array('t'=>'r04'))?>" class="list-group-item<?php if($t=='r04'){echo ' active';}?>"><?php echo Yii::t('attends', 'Grade Attendance'); ?></a>
                <a href="<?php echo $this->createUrl('report', array('t'=>'r05'))?>" class="list-group-item<?php if($t=='r05'){echo ' active';}?>"><?php echo Yii::t('attends', 'Student Attendance Profile'); ?></a>
                <a href="<?php echo $this->createUrl('report', array('t'=>'r06'))?>" class="list-group-item<?php if($t=='r06'){echo ' active';}?>"><?php echo Yii::t('attends', 'Abnormal Attendance & Violation'); ?></a>
                <a href="<?php echo $this->createUrl('report', array('t'=>'r07'))?>" class="list-group-item<?php if($t=='r07'){echo ' active';}?>"><?php echo Yii::t('attends', 'Attendance Report by Subjects'); ?></a>
            </div>
        </div>
        <div class="col-md-8">
            <?php echo $this->renderPartial('report_'.$t, array('data'=>$data));?>
        </div>
    </div>
</div>
