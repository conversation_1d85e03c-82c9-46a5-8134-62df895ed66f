var getScrollTopGetters_ = {
	ieQuirks_ : function (a) {
		return a.document.body.scrollTop
	},
	ieStandards_ : function (a) {
		return a.document.documentElement.scrollTop
	},
	dom_ : function (a) {
		return a.pageYOffset
	}
},
getScrollLeftGetters_ = {
	ieQuirks_ : function (a) {
		return a.document.body.scrollLeft
	},
	ieStandards_ : function (a) {
		return a.document.documentElement.scrollLeft
	},
	dom_ : function (a) {
		return a.pageXOffset
	}
};
function Rect(a, b, c, d) {
	this.x = a;
	this.y = b;
	this.w = c;
	this.h = d
}
Rect.prototype.contains = function (a) {
	return this.x <= a.x && a.x < this.x + this.w && this.y <= a.y && a.y < this.y + this.h
};
Rect.prototype.toString = function () {
	return "[R " + this.w + "x" + this.h + "+" + this.x + "+" + this.y + "]"
};
function DIT_floatMetadata(bid, cid) {
	var a = navigator.userAgent.toLowerCase();
//	if (!(-1 != a.indexOf("firefox/1") || -1 != a.indexOf("firefox/2") || -1 != a.indexOf("seamonkey/1.1"))) {
    var b = document.getElementById(bid),
    c = document.getElementById(cid);
    
    var em = document.createElement("em");
    em.id = 'dit-anchor';
    em.style.width = '0';
    em.style.height = '0';
    em.style.display = 'block';
    em.style.float = 'right';
    c.appendChild(em);
    
    window.attachEvent ? window.attachEvent("onscroll", function () {
        DIT_floatVertically(b, c)
    }) : window.addEventListener && window.addEventListener("scroll", function () {
        DIT_floatVertically(b, c)
    }, !1)
//	}
}
function DIT_floatVertically(a, b) {
    var em = document.getElementById('dit-anchor');
    if ( em.getBoundingClientRect().left >= b.offsetWidth ) {
        var c = nodeBounds(a),
        d = nodeBounds(b),
        e = GetWindowPropertyByBrowser_(window, getScrollTopGetters_);
        a.style.width || (a.style.width = c.w + "px");
        e > d.y && e - d.y + c.h <= b.style.top + d.h ? "fixed" != a.style.position && (a.style.position = "fixed", a.style.top = "0", BR_IsIE() ? a.parentNode.style.paddingRight = 0  + "px" : a.parentNode.style.minWidth = c.w + "px") : "relative" != a.style.position && (a.style.position = "relative", BR_IsIE() && (a.parentNode.style.paddingRight = ""))
    }
    else {
        a.style.position = 'static';
    }
}
function nodeBounds(a) {
	function b(b) {
		for (var c = a.offsetParent; c && c.offsetParent; c = c.offsetParent)
			if (c.scrollLeft && (b.x -= c.scrollLeft), c.scrollTop)
				b.y -= c.scrollTop
	}
	if (!a)
		return null;
	var c;
	c = a.ownerDocument && a.ownerDocument.parentWindow ? a.ownerDocument.parentWindow : a.ownerDocument && a.ownerDocument.defaultView ? a.ownerDocument.defaultView : window;
	if (a.getBoundingClientRect) {
		var d = a.getBoundingClientRect();
		return new Rect(d.left + GetWindowPropertyByBrowser_(c, getScrollLeftGetters_), d.top + GetWindowPropertyByBrowser_(c,
				getScrollTopGetters_), d.right - d.left, d.bottom - d.top, c)
	}
	if (a.ownerDocument && a.ownerDocument.getBoxObjectFor)
		return d = a.ownerDocument.getBoxObjectFor(a), c = new Rect(d.x, d.y, d.width, d.height, c), b(c), c;
	for (var e = d = 0, f = a; f.offsetParent; f = f.offsetParent)
		d += f.offsetLeft, e += f.offsetTop;
	c = new Rect(d, e, a.offsetWidth, a.offsetHeight, c);
	b(c);
	return c
};
function BR_AgentContains_(a) {
	return a in BR_AgentContains_cache_ ? BR_AgentContains_cache_[a] : BR_AgentContains_cache_[a] = -1 != navigator.userAgent.toLowerCase().indexOf(a)
}
var BR_AgentContains_cache_ = {};
function GetWindowPropertyByBrowser_(a, b) {
try {
	if (BR_IsSafari())
		return b.dom_(a);
	if (!window.opera && "compatMode" in a.document && "CSS1Compat" == a.document.compatMode)
		return b.ieStandards_(a);
	if (BR_IsIE())
		return b.ieQuirks_(a)
} catch (c) {}

return b.dom_(a)
}
function BR_IsIE() {
	return BR_AgentContains_("msie") && !window.opera
}
function BR_IsSafari() {
	return BR_AgentContains_("safari") || BR_AgentContains_("konqueror")
}
function BR_IsNav() {
	return !BR_IsIE() && !BR_IsSafari() && BR_AgentContains_("mozilla")
}