<?php

/**
 * This is the model class for table "ivy_schoolbus_businfo".
 *
 * The followings are the available columns in table 'ivy_schoolbus_businfo':
 * @property integer $bid
 * @property string $bus_title
 * @property string $driver_name
 * @property string $bus_code
 * @property string $bus_type
 * @property string $driver_mobile
 * @property string $aunt_name
 * @property string $aunt_mobile
 * @property string $branchid
 * @property integer $state
 * @property integer $city_id
 * @property integer $vendor_id
 * @property integer $userid
 * @property integer $update_timestamp
 */
class SchoolBus extends CActiveRecord
{
    /**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return SchoolBus the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_schoolbus_businfo';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('bus_code, branchid, state, city_id, vendor_id, userid, acctual_children', 'required'),
			array('state, city_id, vendor_id, userid, update_timestamp', 'numerical', 'integerOnly'=>true),
			array('bus_title', 'length', 'max'=>255),
			array('driver_name, aunt_name', 'length', 'max'=>50),
			array('bus_code', 'length', 'max'=>100),
			array('driver_mobile, aunt_mobile', 'length', 'max'=>60),
			array('branchid', 'length', 'max'=>10),
			array('bus_type', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('bid, bus_title, driver_name, bus_code, bus_type, driver_mobile, aunt_name, aunt_mobile, branchid, state, city_id, vendor_id, userid, update_timestamp', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'child'=>array(
				self::HAS_MANY, 'SchoolBusChild', array('busid'=>'bid'), 'with'=>array('childinfo')
			),
            'vendor'=>array(self::BELONGS_TO, 'IvyVendor', 'vendor_id'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'bid' => 'Bid',
			'bus_title' => Yii::t('labels', 'Bus NO.'),
			'driver_name' => Yii::t('labels','Driver Name'),
			'bus_code' => Yii::t('labels', 'License Plate NO.'),
			'bus_type' => Yii::t('labels', 'Bus Type'),
			'driver_mobile' => Yii::t('labels', 'Driver Mobile'),
			'aunt_name' => Yii::t('labels', 'Ayi Name'),
			'aunt_mobile' => Yii::t('labels', 'Ayi Mobile'),
			'branchid' => Yii::t("labels", 'Campus'),
			'state' => Yii::t("labels", 'Status'),
			'city_id' => Yii::t("labels", 'City'),
			'vendor_id' => Yii::t("labels", 'Vendor'),
			'userid' => 'Userid',
			'update_timestamp' => Yii::t("labels", '更新时间'),
			'acctual_children' => Yii::t("labels", 'Student Capacity'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('bid',$this->bid);
		$criteria->compare('bus_title',$this->bus_title,true);
		$criteria->compare('driver_name',$this->driver_name,true);
		$criteria->compare('bus_code',$this->bus_code,true);
		$criteria->compare('bus_type',$this->bus_type,true);
		$criteria->compare('driver_mobile',$this->driver_mobile,true);
		$criteria->compare('aunt_name',$this->aunt_name,true);
		$criteria->compare('aunt_mobile',$this->aunt_mobile,true);
		$criteria->compare('branchid',$this->branchid,true);
		$criteria->compare('state',$this->state);
		$criteria->compare('city_id',$this->city_id);
		$criteria->compare('vendor_id',$this->vendor_id);
		$criteria->compare('userid',$this->userid);
		$criteria->compare('update_timestamp',$this->update_timestamp);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function getSchoolBuses($schoolId)
    {
        $criteria = new CDbCriteria();
        $criteria->compare('state', 10);
        $criteria->compare('branchid', $schoolId);
        return $this->findAll($criteria);
    }
    
    public function getState($all=0)
    {
        $ret = array(
            10 => '有效',
            20 => '暂停',
        );
        
        return !$all?$ret[$this->state]:$ret;
    }
}