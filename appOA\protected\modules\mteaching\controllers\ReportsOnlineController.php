<?php

//报告上下线
class ReportsOnlineController extends TeachBasedController
{
    public $actionAccessAuths = array();

    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Campus Workspace');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['hideKG'] = true;
        $schoolList = array('BJ_DS', 'BJ_SLT');
        foreach ($this->accessBranch as $key => $item) {
            if (!in_array($item, $schoolList)) {
                unset($this->accessBranch[$key]);
            }
        }
        $this->branchSelectParams['urlArray'] = array('//mteaching/reportsOnline/index');
        $cs = Yii::app()->clientScript;
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        //标签字体需要的文件
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl.'/label/iconfont.css?v=20231124');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/element/vue.global.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl.'/base/js/element/index.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/element/index.js');
        Yii::import('common.models.portfolio.*');
        Yii::import('common.models.secondary.*');
    }

    public function actionIndex()
    {
        //权限验证
        if (!$this->checkAccessAuth()) {
            $this->render('//denied/index');
            Yii::app()->end();
        }
        $this->render('index', array());
    }

    //小学学期报告-班级列表
    public function actionESReportClassList()
    {
        $year = Yii::app()->request->getParam('year', '');
        $semester = Yii::app()->request->getParam('semester', '');
        $requestUrl = 'achievementReport/ESReportClassList';
        $this->remote($requestUrl, array(
            'year'     => $year,
            'semester' => $semester,
        ), 'get');
    }

    //班级维度下 小学报告评语
    public function actionESReportTeacherComment()
    {
        $year = Yii::app()->request->getParam('year', '');
        $class_id = Yii::app()->request->getParam('class_id');
        $semester = Yii::app()->request->getParam('semester');
        $requestUrl = 'achievementReport/ESReportTeacherComment';
        $this->remote($requestUrl, array(
            'year'     => $year,
            'class_id' => $class_id,
            'semester' => $semester,
        ), 'get');
    }

    //小学评语上下线
    public function actionSetESReportsOnline()
    {
        $status = Yii::app()->request->getParam('status');
        $data_ids = Yii::app()->request->getParam('data_ids');
        $requestUrl = 'achievementReport/setESReportsOnline';
        $this->remote($requestUrl, array(
            'status'   => $status,
            'data_ids' => $data_ids,
        ), 'post');
    }

    //it组不限制，默认为true
    public function checkAccessAuth()
    {
        if(!Yii::app()->user->checkAccess('ivystaff_it')){
            $requestUrl = 'achievementReport/getESReportsViewAuth';
            $res = CommonUtils::requestDsOnline2($requestUrl, array('school_id' => $this->branchId), 'get');
            if ($res['code'] == 0) {
                return true;
            } else {
                return false;
            }
        }else{
            return true;
        }
    }

    public function remote($requestUrl, $requestData = array(), $method)
    {
        if(!$this->checkAccessAuth()){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'YOU DO NOT HAVE THE PERMISSION');
            $this->addMessage('data', array());
            $this->showMessage();
        }
        if (empty($requestData['school_id'])) {
            $requestData['school_id'] = $this->branchId;
        }
        $res = CommonUtils::requestDsOnline2($requestUrl, $requestData, $method);
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        }
        $this->showMessage();
    }
}