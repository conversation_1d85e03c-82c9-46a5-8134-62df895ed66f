<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo Yii::t('site','Campus Workspace');?></li>
        <li><?php echo Yii::t('site','Support');?></li>
        <li><?php echo CHtml::link(Yii::t('site','Catering Management'), array('index'));?></li>
        <li class="active"><?php echo $model->isNewRecord ? Yii::t('lunch', 'Add New Menu') : $model->title_cn?></li>
    </ol>
    <div class="row">
        <div class="col-md-6 col-sm-12">
            <?php
            $form=$this->beginWidget('CActiveForm', array(
                'id'=>'catering-form',
                'htmlOptions'=>array('class'=>'form-horizontal J_ajaxForm'),
            ));
            ?>
            <div class="form-group">
                <?php echo $form->labelEx($model, 'title_cn', array('class'=>'col-sm-2 control-label')); ?>
                <div class="col-sm-10">
                    <?php echo $form->textField($model, 'title_cn', array('class'=>'form-control')); ?>
                </div>
            </div>
            <div class="form-group">
                <?php echo $form->labelEx($model, 'city_id', array('class'=>'col-sm-2 control-label')); ?>
                <div class="col-sm-10">
                    <?php echo $form->dropDownList($model, 'city_id', $citys, array('class'=>'form-control select_4', 'empty'=>Yii::t('global', 'Please Select'), 'onchange'=>'cCity(this)')); ?>
                </div>
            </div>
            <div class="form-group">
                <?php echo $form->labelEx($model, 'vendor_id', array('class'=>'col-sm-2 control-label')); ?>
                <div class="col-sm-10">
                    <?php echo $form->dropDownList($model, 'vendor_id', isset($vendorWhthCity[$model->city_id])?$vendorWhthCity[$model->city_id] : array(), array('class'=>'form-control select_4', 'empty'=>Yii::t('global', 'Please Select'))); ?>
                </div>
            </div>
            <div class="form-group">
                <?php echo $form->labelEx($model, 'school_mgt', array('class'=>'col-sm-2 control-label')); ?>
                <div class="col-sm-10">
                    <?php echo $form->checkBox($model, 'school_mgt'); ?>
                </div>
            </div>
            <div class="form-group">
                <?php echo $form->labelEx($model, 'is_allergy', array('class'=>'col-sm-2 control-label')); ?>
                <div class="col-sm-10">
                    <?php echo $form->checkBox($model, 'is_allergy'); ?>
                </div>
            </div>
            <div class="form-group">
                <?php echo $form->labelEx($model, 'status', array('class'=>'col-sm-2 control-label')); ?>
                <div class="col-sm-10">
                    <?php echo $form->checkBox($model, 'status'); ?>
                </div>
            </div>
            <div class="form-group">
                <?php echo $form->labelEx($model, 'weight', array('class'=>'col-sm-2 control-label')); ?>
                <div class="col-sm-10">
                    <?php echo $form->textField($model, 'weight', array('class'=>'form-control length_1')); ?>
                </div>
            </div>
            <div class="form-group">
                <?php echo $form->labelEx($model, 'menu_cate', array('class'=>'col-sm-2 control-label')); ?>
                <div class="col-sm-10">
                    <?php echo $form->checkBoxList($model, 'menu_cate', $lunchs); ?>
                </div>
            </div>
            <div class="form-group">
                <?php echo $form->labelEx($model, 'nutrition', array('class'=>'col-sm-2 control-label')); ?>
                <div class="col-sm-10">
                    <?php echo $form->textArea($model, 'nutrition', array('class'=>'form-control', 'rows'=>8)); ?>
                </div>
            </div>
            <div class="form-group">
                <?php echo $form->labelEx($model, 'memo', array('class'=>'col-sm-2 control-label')); ?>
                <div class="col-sm-10">
                    <?php echo $form->textArea($model, 'memo', array('class'=>'form-control', 'rows'=>8)); ?>
                </div>
            </div>
            <div class="form-group">
                <div class="col-sm-offset-2 col-sm-10">
                    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('message', 'Next')?></button>
                </div>
            </div>
            <?php $this->endWidget(); ?>
        </div>
    </div>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>

<script>
    function cCity(_this)
    {
        var vendors = <?php echo CJSON::encode($vendorWhthCity)?>;
        var city = $(_this).val();
        var vendorObj = $('#CateringMenu_vendor_id');
        vendorObj.html('');
        for(var cid in vendors[city]){
            $('<option></option>').text(vendors[city][cid]).val(cid).appendTo(vendorObj);
        }
    }
</script>