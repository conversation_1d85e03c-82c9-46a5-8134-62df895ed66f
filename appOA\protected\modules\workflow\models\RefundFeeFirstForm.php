<?php

/* 
 * 退费表单
 */
class RefundFeeFirstForm extends CFormModel
{
    public $childid;
    public $refund_type;
    public $refund_startdate;
    public $refund_enddate;
    public $leave_date;
    public $memo;
    public function rules(){
        return array(
			array('childid, refund_type, refund_startdate, refund_enddate, memo', 'required','on'=>'fristRefund'),
			array('childid, refund_type, leave_date', 'required','on'=>'fristLeave'),
			//array('childid, refund_type, refund_startdate', 'required','on'=>'twoRefund'),
            array('childid, refund_type, refund_startdate, refund_enddate', 'numerical', 'integerOnly'=>true),
		);
    }
    
    public function attributeLabels(){
        return array(
			'childid'=>Yii::t("workflow", "孩子"),
			'refund_type'=>Yii::t("workflow", "方式"),
			'refund_startdate'=>Yii::t("workflow", "退费开始日期"),
			'refund_enddate'=>Yii::t("workflow", "退费结束日期"),
			'leave_data'=>Yii::t("workflow", "退学日期"),
			'memo'=>Yii::t("workflow", "备注"),
		);
    }
    
    
}
