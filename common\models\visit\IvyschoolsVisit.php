<?php

/**
 * This is the model class for table "ivy_ivyschools_visit".
 *
 * The followings are the available columns in table 'ivy_ivyschools_visit':
 * @property integer $id
 * @property integer $visit_date
 * @property string $visit_time
 * @property string $parent_name
 * @property integer $phone
 * @property string $child_name
 * @property string $schoolid
 * @property integer $birthdate
 * @property string $email
 * @property string $address
 * @property integer $knowus
 * @property integer $receive_language
 * @property string $memo
 * @property integer $create_date
 * @property integer $status
 * @property string $application_grade
 * @property string $expected_attendance
 * @property string $current_school
 * @property string $current_grade
 */
class IvyschoolsVisit extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return IvyschoolsVisit the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_ivyschools_visit';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('visit_time, phone, parent_name, child_name, schoolid', 'required'),
			array('visit_date, birthdate, knowus, expected_attendance, receive_language, create_date, status', 'numerical', 'integerOnly'=>true),
			array('visit_time, email', 'length', 'max'=>100),
			array('parent_name, child_name, address, concerns, application_grade, current_school, current_grade', 'length', 'max'=>255),
			array('schoolid', 'length', 'max'=>10),
			array('memo, from', 'safe'),
//			array('phone', 'length','min'=>11,'max'=>11),
			array('email', 'email','allowEmpty'=>true),
			array('status', 'required', 'on'=>'aview'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, visit_date, visit_time, parent_name, phone, child_name, schoolid, birthdate, email, address, knowus, receive_language, memo, create_date, status, from, school_year, application_grade, current_school, current_grade, expected_attendance', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'visit_date' => Yii::t("admissions", 'Scheduled date'),
			'visit_time' => Yii::t("admissions", 'Scheduled time'),
			'parent_name' => Yii::t("admissions", 'Parent name'),
			'phone' => Yii::t("admissions", 'Phone'),
			'child_name' => Yii::t("admissions", 'Student name'),
			'schoolid' => Yii::t("admissions", 'Campus'),
			'birthdate' => Yii::t("admissions", 'Child birthday'),
			'email' => Yii::t("admissions", 'Email'),
			'address' => Yii::t("admissions", 'Adress'),
			'knowus' => Yii::t("admissions", 'How do you hear about us'),
			'receive_language' => Yii::t("admissions", 'Preferred Language'),
			'memo' => Yii::t("admissions", 'Special Notes'),
			'create_date' => Yii::t("admissions", 'Create Date'),
			'status' => Yii::t("admissions", '审核'),
			'concerns' => Yii::t("admissions", 'Concerns'),
			'from' => Yii::t("admissions", 'from'),
			'school_year' => Yii::t("admissions", 'school_year'),
            'application_grade' => Yii::t('admissions', 'Requested Starting Grade'),
            'expected_attendance' => Yii::t('admissions', 'Expected Attendance'),
            'current_school' => Yii::t('admissions', 'Current School'),
            'current_grade' => Yii::t('admissions', 'Current Grade'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('visit_date',$this->visit_date);
		$criteria->compare('visit_time',$this->visit_time,true);
		$criteria->compare('parent_name',$this->parent_name,true);
		$criteria->compare('phone',$this->phone);
		$criteria->compare('child_name',$this->child_name,true);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('birthdate',$this->birthdate);
		$criteria->compare('email',$this->email,true);
		$criteria->compare('address',$this->address,true);
		$criteria->compare('knowus',$this->knowus);
		$criteria->compare('receive_language',$this->receive_language);
		$criteria->compare('memo',$this->memo,true);
		$criteria->compare('concerns',$this->concerns,true);
		$criteria->compare('create_date',$this->create_date);
		$criteria->compare('status',$this->status);
		$criteria->compare('status',$this->application_grade);
		$criteria->compare('expected_attendance',$this->expected_attendance);
		$criteria->compare('current_school',$this->current_school);
		$criteria->compare('current_grade',$this->current_grade);
		$criteria->compare('current_grade',$this->current_grade);
		$criteria->compare('school_year',$this->school_year);


		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function searchDate()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('visit_date',strtotime($this->visit_date));
		$criteria->compare('visit_time',$this->visit_time,true);
		$criteria->compare('parent_name',$this->parent_name,true);
		$criteria->compare('phone',$this->phone);
		$criteria->compare('child_name',$this->child_name,true);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('birthdate',$this->birthdate);
		$criteria->compare('email',$this->email,true);
		$criteria->compare('address',$this->address,true);
		$criteria->compare('knowus',$this->knowus);
		$criteria->compare('receive_language',$this->receive_language);
		$criteria->compare('memo',$this->memo,true);
		$criteria->compare('concerns',$this->concerns,true);
		$criteria->compare('create_date',$this->create_date);
		$criteria->compare('status',0);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
            'sort' => array(
                'defaultOrder' => 'visit_date desc, visit_time',
            ),
            'pagination'=>array(
                'pageSize'=>200,
            ),
		));
	}
    
	/**
	 * 取某段日期内需要确认的家长信息
	 * @param int $startDatestamp(开始日期时间戳)
	 * @param int $endDatestamp(开始日期时间戳)
	 * @param string $schoolId
	 * @return ArrayObject
	 */
	public function getvisitDate($startDatestamp,$endDatestamp,$schoolId = null)
	{
		$criteria = new CDbCriteria;
		$criteria->addBetweenCondition('visit_date', $startDatestamp, $endDatestamp);
		if (!empty($schoolId))
		{
			$criteria->compare('schoolid', $schoolId);
		}
		$criteria->order = 'visit_date ASC';
		return $this->findAll($criteria);
	}
}