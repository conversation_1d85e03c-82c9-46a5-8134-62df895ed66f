
<div class="container-fluid" id='container' v-cloak >
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo Yii::t('directMessage', '销售统计') ?></li>
    </ol>
    <div class="" v-if='initData.school_title'>
        <div class='unconfirm' v-if='Object.keys(initData.wait_confirm).length>0'>
            <div class='uniformIcon'><img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/uniform.png' ?>" alt=""> <span class='font14 ml5'>待确认</span></div>
            <div class='schoolList'>
                <div class='row'>
                    <div class='col-lg-2 col-md-3' v-for='(list,key,index) in initData.wait_confirm'>
                        <div class='schoolNum color3' @click='link(key)'> 
                            <div class='flex1 font14 text_over'>{{initData.school_title[key]}}</div>  
                            <span class='font18 uniformNum'>{{initData.wait_confirm[key]?initData.wait_confirm[key]:0}}</span>
                            <span class='el-icon-arrow-right color6 font14 ml5'></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div style='margin-top:32px' v-if='initData.school_title'>
            <div class='fontBold font14 color3'>销售统计</div>
            <div class='flex align-items'>            
                <ul class="nav nav-pills flex1">
                    <li role="presentation" class='activePad' :class='monthType==1?"active":"color3"'><a href="javascript:;" @click='monthYear(1)'>近六个月</a></li>
                    <li role="presentation" class='activePad'  :class='monthType==2?"active":"color3"'><a href="javascript:;"  @click='monthYear(2)'>近一年</a></li>
                </ul>
                <ul class="pagination">
                    <li class='' :class='tabType==1?"active":"color3"'><a href="javascript:;" aria-label="Previous" @click='tabView(1)'>数据统计</a></li>
                    <li class="" :class='tabType==2?"active":"color3"'><a href="javascript:;" @click='tabView(2)'>图表</a></li>
                </ul>
            </div>
            <div v-if='tabType==1'>
                <table class="table table-bordered table-hover" style='table-layout: fixed;'>
                    <tbody>
                        <tr>
                            <th  class='tdBg'>时间</th>
                            <th  class='tdBg'>总计</th> 
                            <th  class='tdBg' v-for='(item,idx) in monthData.school'>{{initData.school_title[item.name]}}</th>
                            
                        </tr>
                        <tr v-for='(list,index) in monthData.months'>
                            <td  class='tdBg'>{{list}}</td>
                            <td  class='tdBg'>
                                {{monthData.month_total.data[index]}}
                            </td>
                            <td v-for='(item,idx) in monthData.school'>
                                <!-- {{list}}
                                {{list}} -->
                                {{item.data[index]}}
                            </td>
                        </tr>
                        <tr>
                            <td  class='tdBg'>总计</td>
                            <td  class='tdBg' :colspan='monthData.school.length+1'>{{monthData.all_total}}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div v-if='tabType==2'>
                <div id='echart' :style='monthType==2?"height:700px;width:100%":"height:400px;width:100%"'></div>
            </div>
        </div>
    </div>
</div>
<script>
    var container = new Vue({
        el: "#container",
        data: {
            initData:{},
            monthType:1,
            monthData:{},
            tabType:1,
            seriesData:[]
        },
        watch:{
           
        },
        created: function() {
            let that=this
            $.ajax({
                url: '<?php echo $this->createUrl("orderSales")?>',
                type: "post",
                async: true,
                dataType: 'json',
                data:{
                    type:'<?php echo $_GET["type"]?>'
                },
                success: function(data) {
                    console.log(data)
                    if(data.state=='success'){
                        that.initData=data.data
                        that.monthData=data.data.last_six_months_data
                    }else{
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    resultTip({
                            error: 'warning',
                            msg:'请求错误'
                        });
                }
            });
        },
        computed: {},
        methods: {
            link(id){
                let url='/mcampus/product/index?branchId='+id
                window.open(url, '_blank');
            },
            monthYear(type){
                this.monthType=type
                if(type==1){
                    this.monthData=this.initData.last_six_months_data
                }else{
                    this.monthData=this.initData.last_year_data
                }
                this.tabView(this.tabType)
            },
            tabView(type){
                this.tabType=type
                if(type==1){

                }else{
                    this.seriesData=[]
                    var colorlist = ["rgba(91, 143, 249, 0.85)", "rgba(90, 216, 166, 0.85)", "rgba(93, 112, 146, 0.85)","rgba(246, 189, 22, 0.85)","rgba(232, 104, 74, 0.85)","rgba(109, 200, 236, 0.85)","rgba(146, 112, 202, 0.85)","rgba(255, 157, 77, 0.85)","rgba(38, 154, 153, 0.85)","rgba(255, 153, 195, 0.85)","rgba(91, 143, 249, 0.85)"];
                    this.monthData.school.forEach((item,index) => {
                        this.seriesData.push({
                            itemStyle: {//自定义颜色
                                normal: { color:colorlist[index] },
                            },
                            name: this.initData.school_title[item.name],
                            type: 'bar',
                            stack: 'total',
                            label: {
                                show: true
                            },
                            emphasis: {
                                focus: 'series'
                            },
                            data:item.data
                            
                        })
                    });
                    this.$nextTick(() => {
                        var myCharts = echarts.init(document.getElementById('echart'))
                        var option = this.optionData(myCharts)
                        myCharts.setOption(option,true);
                        myCharts.resize()
                    })
                }
            },
            optionData(myCharts){
                console.log(this.seriesData)
                var option = {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                        // Use axis to trigger tooltip
                        type: 'shadow' // 'shadow' as default; can also be 'line' or 'shadow'
                        }
                    },
                    legend: {},
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'value'
                    },
                    yAxis: {
                        type: 'category',
                        data:this.monthData.months
                    },
                    series: this.seriesData
                };
                return option
            },
        }
    })

</script>
<style>
    .unconfirm{
        background:rgba(217, 83, 79, 0.08);
        border-radius: 8px;
        position: relative;
    }
    .uniformIcon{
        padding:8px 24px;
        background: #D9534F;
        border-radius: 0px 18px 18px 0px;
        position:absolute;
        left:0;
        top:0;
        color:#fff;
        display: flex;
        align-items: center;
    }
    .uniformIcon img{
        width:16px;
        height:16px;
    }
    .schoolList{
        padding:48px 24px 24px;
    }
    .schoolNum{
        background: #FFFFFF;
        border-radius: 6px;
        padding:14px 16px;
        margin-top:16px;
        display:flex;
        align-items:center
    }
    .schoolNum:hover{
        cursor: pointer;
        color:#4D88D2 !important
    }
    .uniformNum{
        font-weight:500;
        line-height:18px
    }
    td,th{
        padding: 14px 6px !important;
        text-align: center;
        color:#333;
        font-size:14px;
        white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .tdBg{
        background:#FAFAFA
    }
    .activePad a{
        padding:8px 12px !important
    }
    .text_over{
        width: 0;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
    }
</style>