<?php

/**
 * This is the model class for table "ea_parent".
 *
 * The followings are the available columns in table 'ea_parent':
 * @property integer $id
 * @property integer $family_id
 * @property string $name
 * @property string $phone
 * @property string $email
 * @property integer $gender
 * @property string $avatar
 * @property integer $country
 * @property integer $language
 * @property integer $use_lang
 * @property string $edu_background
 * @property string $employer
 * @property string $position
 * @property string $residence
 * @property integer $status
 * @property integer $created_by
 * @property integer $created_at
 * @property integer $updated_by
 * @property integer $updated_at
 * @property integer $is_creator
 */
class Parents extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ea_parent';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('family_id, status, created_by, created_at, updated_by, updated_at', 'required'),
			array('family_id, gender, country, language, use_lang, status, created_by, created_at, updated_by, updated_at, is_creator', 'numerical', 'integerOnly'=>true),
			array('name, phone, email, avatar, edu_background, employer, position, residence', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, family_id, name, phone, email, gender, avatar, country, language, use_lang, edu_background, employer, position, residence, status, created_by, created_at, updated_by, updated_at, is_creator', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'family_id' => 'Family',
			'name' => 'Name',
			'phone' => 'Phone',
			'email' => 'Email',
			'gender' => 'Gender',
			'avatar' => 'Avatar',
			'country' => 'Country',
			'language' => 'Language',
			'use_lang' => 'Use Lang',
			'edu_background' => 'Edu Background',
			'employer' => 'Employer',
			'position' => 'Position',
			'residence' => 'Residence',
			'status' => 'Status',
			'created_by' => 'Created By',
			'created_at' => 'Created At',
			'updated_by' => 'Updated By',
			'updated_at' => 'Updated At',
			'is_creator' => 'Is Creator',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('family_id',$this->family_id);
		$criteria->compare('name',$this->name,true);
		$criteria->compare('phone',$this->phone,true);
		$criteria->compare('email',$this->email,true);
		$criteria->compare('gender',$this->gender);
		$criteria->compare('avatar',$this->avatar,true);
		$criteria->compare('country',$this->country);
		$criteria->compare('language',$this->language);
		$criteria->compare('use_lang',$this->use_lang);
		$criteria->compare('edu_background',$this->edu_background,true);
		$criteria->compare('employer',$this->employer,true);
		$criteria->compare('position',$this->position,true);
		$criteria->compare('residence',$this->residence,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('created_by',$this->created_by);
		$criteria->compare('created_at',$this->created_at);
		$criteria->compare('updated_by',$this->updated_by);
		$criteria->compare('updated_at',$this->updated_at);
		$criteria->compare('is_creator',$this->is_creator);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->mmxxcxdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return Parents the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
