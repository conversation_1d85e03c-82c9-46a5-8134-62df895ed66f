<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','HQ Operations'), array('default/index'));?></li>
        <li><?php echo CHtml::link(Yii::t('site','Support'), array('default/index'));?></li>
        <li><?php echo CHtml::link(Yii::t('site','标配管理'), array('purchaseStandard/index'));?></li>
        <li class="active"><?php echo Yii::t('site',$psModel->cn_title) ;?></li>
    </ol>
    <div class="row">
        <div class="col-md-2">
            <div class="list-group" id="classroom-status-list">
                <a href="<?php echo $this->createUrl('purchaseProducts/index'); ?>" class="list-group-item status-filter ">商品管理</a>
                <a href="<?php echo $this->createUrl('purchaseStandard/index'); ?>" class="list-group-item status-filter active">标配管理</a>
            </div>
        </div>
        <div class="col-md-10">
            <!-- 筛选条件 -->
            <div class="mb10">
                <form action="<?php $id=$psModel->id; echo $this->createUrl('standardlist'); ?>" method="get" class="row">
                    <div class="col-sm-2">
                       <input class="form-control" placeholder="搜索商品" type="text" name="name"
                       value="<?php //echo $purchaseProducts['cn_name'] ?>">
                    </div>
                    <input type="hidden" value="<?php echo $id; ?>" name="id">
                    <button class="btn btn-default" type="submit"><span class="glyphicon glyphicon-search"> </span> </button>
                </form>
            </div>
            <div class="md10">
                <?php
                    $this->widget('ext.ivyCGridView.BsCGridView', array(
                        'id'=>'purchases-standlist-grid',
                        'afterAjaxUpdate'=>'js:function(){head.Util.ajaxDel()}',
                        'dataProvider'=>$ppiModel->search(),
                        'colgroups'=>array(
                            array(
                                "colwidth"=>array(100,100,100,100,100),
                            )
                        ),
                        'columns'=>array(
                            array(
                               'header' => '商品名称',
                               'type' => 'raw',
                               'value' => 'CHtml::link($data->product->cn_name,Yii::app()->createUrl("moperation/purchaseProducts/view",array("id"=>$data->product->id)),array("target"=>"_blank"))'
                            ),
                            array(
                                'name'=>'商品单价',
                                'value'=>'$data->product->price'
                                ),
                            array(
                                'name'=>'商品数量',
                                'type'=>'raw',
                                'value'=>'CHtml::textField("",$data->num,array("size"=>2,"readonly"=>"readonly","ondblclick"=>"changeState(this)","onblur"=>"changeNum(this,$data->id,$data->num)"))'
                                ),
                            array(
                                'name'=>'商品总价',
                                'value'=>'number_format($data->num*$data->product->price, 2, ".", "")'
                                ),
                            array(
                                'name'=>'操作',
                                'value'=>array($this,'showStandardBtn'),
                                ),
                        ),
                    ));
                    echo '标配总价：<span id="totalmoney">'.$psModel->getTotalMoney().'</span>';
                ?>
            </div>
        </div>
    </div>
</div>


<script>
    //改变标配中商品数量input为可输入
    function changeState (inp) {
        $(inp).removeAttr('readonly');
    }
    //改变标配中商品数量
    function changeNum (inp,id,oldnum) {
        var num = $(inp).val();
        if (oldnum == num) {
            return;
        }
        $.post(
            '<?php echo $this->createUrl('changeNum'); ?>', 
            {id: id,num: num}, 
            function(data, textStatus, xhr) {
                $(inp).attr({
                    readonly: 'readonly',
                });
        });
    }
    //回调：标配添加成功
    function cbStandard() {
        $.fn.yiiGridView.update('purchases-standlist-grid');
    }
</script>
