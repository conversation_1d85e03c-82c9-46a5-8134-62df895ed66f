<?php

class AddressCommand extends CConsoleCommand {

    public function init()
    {
    	Yii::import('common.models.child.*');
    }

    // 保存孩子证件号、教育ID、家庭地址
    public function actionSaveAddress()
    {
        $error =  "保存孩子证件号、教育ID、家庭地址";
        echo $this->_t($error) . "\r\n";
        $file = fopen('./addressNew.csv', 'r+');
        $i = 0;
        while ($arr = fgetcsv($file)) {
            $criteria = new CDbCriteria;
            $criteria->compare('educational_id', $arr[1]);
            $count = ChildProfileBasic::model()->count($criteria);
            if($count == 1) {
                $childModel = ChildProfileBasic::model()->findByAttributes(array('educational_id' => $arr[1]));
                if ($childModel) {
                    if($arr[0]){
                        $childModel->identity = $arr[0];
                    }
                    if($arr[2]){
                        $childModel->barcode = $arr[2];
                    }
                    if($childModel->save()){
                        $i++;
                        if ($arr[3] && $childModel->homeaddr) {
                            $en_address = iconv('GB2312', 'UTF-8', $arr[3]);
                            $childModel->homeaddr->en_address = $en_address;
                            if (!$childModel->homeaddr->save(false)) {
                                $err = current($childModel->homeaddr->getErrors());
                                $error = "孩子教育ID $arr[1]：" . ' 保存失败' . $err[0];
                                echo $this->_t($error) . "\r\n";
                            }else{
                                $error = "孩子教育ID $arr[1]：" . ' 成功';
                                echo $this->_t($error) . "\r\n";
                            }
                        }
                    }else{
                        $childErr = current($childModel->getErrors());
                        $error = "孩子教育ID $arr[1]：" . ' 保存失败' . $childErr[0];
                        echo $this->_t($error) . "\r\n";
                    }
                }
            }else{
                $error = "孩子教育ID $arr[1]：" . ' 保存失败, 未找到或者不是唯一教育ID';
                echo $this->_t($error) . "\r\n";
            }
        }
        fclose($file);
        $error = "共 $i 个孩子";
        echo $this->_t($error) . "\r\n";
    }


    // 根据ID 去保存学籍号
    public function actionAddressId()
    {
        echo "保存学籍号" . "\r\n";
        $file = fopen('./dtAddress.csv', 'r+');
        $i = 0;
        $f = 0;
        $fail = array();
        while ($arr = fgetcsv($file)) {
            $childModel = ChildProfileBasic::model()->findByPk($arr[0]);
            if ($childModel) {
                if($arr[1]){
                    $childModel->barcode = $arr[1];
                    if($childModel->save()){
                        $i++;
                        echo "孩子ID $arr[0]：" . ' 成功' . "\r\n";
                    }else{
                        $childErr = current($childModel->getErrors());
                        echo "孩子ID $arr[0]：" . ' 保存失败' . $childErr[0] . "\r\n";
                    }
                }else{
                    $f++;
                    $fail[$arr[0]] = $arr[0];
                    echo "孩子ID $arr[0]：" . ' 文件中未有学籍号' . "\r\n";
                }
            }
        }
        $str =  implode(',', $fail);
        fclose($file);
        echo "共 $i 个孩子成功， $f 个孩子失败 $str" . "\r\n";
    }

    public function _t($str='')
    {
        $str = "\"".$str."\"";
        $str = str_replace(',', '，', $str);
        return iconv("UTF-8", "GBK", $str);
    }
}
