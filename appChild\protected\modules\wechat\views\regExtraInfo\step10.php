<style>
 #signature-div {
        width: 100%;
        height: 100%;
        display: none;
        position: fixed;
        left: 0;
        top: 0;
        z-index: 999;
        background-color: white;
    }

    #signature-div p {
        position: absolute;
        bottom: 0;
        left: 50%;
        margin-left: -91px
    }

    .signature-pad {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: white;
    }

    .signCan {
        position: relative;
        -webkit-box-flex: 1;
        -ms-flex: 1;
        flex: 1;
        border: 1px solid #f4f4f4;
    }

    .signBody {
        position: relative;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        font-size: 10px;
        width: 100%;
        height: 100%;
        max-height: 300px;
        background-color: #fff;
        border-radius: 4px;
        padding: 16px;
    }
    #signature-data img{
        width:100px
    }
    .signText{
        padding:16px;
    }
</style>
<?php echo $this->renderPartial('steps_header', array('currentStep'=>10)); ?>
<div class="container-fluid">
    <h5 class="htitle"><span class="fas fa-file-signature"></span> <?php echo $this->getStepTitle($this->step); ?></h5>
    <?php echo $this->renderPartial('_nouser_header'); ?>
    <div class="edit-content">
    <?php
        $content = RegConfig::getContent($this->regStudent->reg_id, $this->step);
        if ($content) {
            echo CommonUtils::autoLang(base64_decode($content['cn']['parents']), base64_decode($content['en']['parents']));
        }
     ?>
    </div>
    <div>
        <!-- 英文名 -->
        <?php $form = $this->beginWidget('CActiveForm', array(
            'id' => 'sep8-form',
            'enableAjaxValidation' => false,
            'htmlOptions' => array(
                // 'class'=>'J_ajaxForm form-horizontal',
                'role' => 'form',
            ),
        )); ?>
        <?php if(Yii::app()->language == 'zh_cn'){ ?>
        <div class="form-group">
            <?php echo Yii::t('reg','儿童身份证件类型（有中国户⼝报中国户⼝）'); ?>
            <?php echo $form->textField($formModel10, 'idCardType', array('class' => 'form-control mb10')); ?>
        <?php  } ?>
        <div class="form-group">
            <?php echo Yii::t('reg','Children Passport Number'); ?>
            <?php echo $form->textField($formModel10, 'idCard', array('class' => 'form-control mb10')); ?>
        </div>
        <?php if(Yii::app()->language == 'zh_cn'){ ?>
            <div class="form-group">
                <?php echo Yii::t('reg','民族'); ?>
                <?php echo $form->textField($formModel10, 'minzu', array('class' => 'form-control mb10')); ?>
            </div>
            <div class="form-group">
                <?php echo Yii::t('reg','籍贯'); ?>
                <?php echo $form->textField($formModel10, 'nativePlace', array('class' => 'form-control mb10')); ?>
            </div>
            <div class="form-group">
                <?php echo Yii::t('reg','户口性质'); ?>
                <?php echo $form->textField($formModel10, 'residentType', array('class' => 'form-control mb10')); ?>
            </div>
            <div class="form-group">
                <?php echo Yii::t('reg','户口所在地'); ?>
                <?php echo $form->textField($formModel10, 'residentAddress', array('class' => 'form-control mb10')); ?>
            </div>
        <?php  } ?>
        <div class="form-group">
            <?php echo Yii::t('reg','Is your child the only kid in your family?'); ?>
            <?php echo $form->textField($formModel10, 'isOnlyChild', array('class' => 'form-control mb10')); ?>
        </div>
        <div class="form-group">
            <?php echo Yii::t('reg','Current Address'); ?>
            <?php echo $form->textField($formModel10, 'address', array('class' => 'form-control mb10')); ?>
        </div>
        <div class="form-group">
            <?php echo Yii::t('reg','Blood Type'); ?>
            <?php echo $form->textField($formModel10, 'bloodType', array('class' => 'form-control mb10')); ?>
        </div>
        <div class="form-group">
            <?php echo Yii::t('reg','Birth Place'); ?>
            <?php echo $form->textField($formModel10, 'placeOfBirth', array('class' => 'form-control mb10')); ?>
        </div>
        <div class="form-group">
            <?php echo Yii::t('reg','Guardian Name'); ?>
            <?php echo $form->textField($formModel10, 'guardianName', array('class' => 'form-control mb10')); ?>
        </div>
        <div class="form-group">
            <?php echo Yii::t('reg','Guardian Passport Number or Chinese ID Number'); ?>
            <?php echo $form->textField($formModel10, 'guardianIdCard', array('class' => 'form-control mb10')); ?>
        </div>
        <div class="form-group">
            <?php echo Yii::t('reg','Guardian’s relationship with child'); ?>
            <?php echo $form->textField($formModel10, 'guardianEelationship', array('class' => 'form-control mb10')); ?>
        </div>

        <div class="form-group">
            <?php echo Yii::t('reg','Father Name'); ?>
            <?php echo $form->textField($formModel10, 'fatherName', array('class' => 'form-control mb10')); ?>
        </div>
        <div class="form-group">
            <?php echo Yii::t('reg','Father’s contact number'); ?>
            <?php echo $form->textField($formModel10, 'fatherTel', array('class' => 'form-control mb10')); ?>
        </div>
        <div class="form-group">
            <?php echo Yii::t('reg','Father’s Email'); ?>
            <?php echo $form->textField($formModel10, 'fatherEmail', array('class' => 'form-control mb10')); ?>
        </div>
        <div class="form-group">
            <?php echo Yii::t('reg','Mother Name'); ?>
            <?php echo $form->textField($formModel10, 'motherName', array('class' => 'form-control mb10')); ?>
        </div>
        <div class="form-group">
            <?php echo Yii::t('reg','Mother’s contact number'); ?>
            <?php echo $form->textField($formModel10, 'motherTel', array('class' => 'form-control mb10')); ?>
        </div>
        <div class="form-group">
            <?php echo Yii::t('reg','Mother’s Email'); ?>
            <?php echo $form->textField($formModel10, 'motherEmail', array('class' => 'form-control mb10')); ?>
        </div>

        <div class="form-group">
            <?php echo Yii::t('reg','Allergies History (please give details of severity and treatment)'); ?>
            <?php echo $form->textField($formModel10, 'allergy', array('class' => 'form-control mb10')); ?>
        </div>
        <div class="form-group">
            <?php echo Yii::t('reg','Please specify any previous illness historiy （ex. dislocate, catagma, hyperpyretic convulsion，asthma ...)'); ?>
            <?php echo $form->textField($formModel10, 'medicalHistory', array('class' => 'form-control mb10')); ?>
        </div>
        <?php echo Yii::t('reg','Emergency Contact (Beside Parents)'); ?>
        <div class="form-group">
            <?php echo Yii::t('reg','Name'); ?>
            <?php echo $form->textField($formModel10, 'personName1', array('class' => 'form-control mb10')); ?>
        </div>
        <div class="form-group">
            <?php echo Yii::t('reg','Relationship'); ?>
            <?php echo $form->textField($formModel10, 'relationship1', array('class' => 'form-control mb10')); ?>
        </div>
        <div class="form-group">
            <?php echo Yii::t('reg','Contact Number'); ?>

            <?php echo $form->textField($formModel10, 'contactNumber1', array('class' => 'form-control mb10')); ?>
        </div>
        <div class="form-group">
            <?php echo Yii::t('reg','Name'); ?>
            <?php echo $form->textField($formModel10, 'personName2', array('class' => 'form-control mb10')); ?>
        </div>
        <div class="form-group">
            <?php echo Yii::t('reg','Relationship'); ?>
            <?php echo $form->textField($formModel10, 'relationship2', array('class' => 'form-control mb10')); ?>
        </div>
        <div class="form-group">
            <?php echo Yii::t('reg','Contact Number'); ?>
            <?php echo $form->textField($formModel10, 'contactNumber2', array('class' => 'form-control mb10')); ?>
        </div>
        <?php echo Yii::t('reg','Should a situation arise, when emergency medical attention is required and we are unable to contact any of the above, please indicate, on the space provided below, the hospital or clinic you want your child taken to for treatment. If no hospital is designed by you, the school will take your child to an established public hospital. If the hospital or clinic parent provided is not a public one, parents will take responsibility of the payment. '); ?>
        <div class="form-group">
            <?php echo Yii::t('reg','Hospital'); ?>
            <?php echo $form->textField($formModel10, 'hospital', array('class' => 'form-control mb10')); ?>
        </div>
        <div class="form-group">
            <?php echo Yii::t('reg','Do you give permission to the school to use pictures taken of your child for marketing purpose?'); ?>
            <div>
                <?php echo $form->radioButtonList($formModel10, 'is_promote', array(
                    1 => Yii::t('reg', 'Yes'),
                    2 => Yii::t('reg', 'No'),
                ), array(
                    'template' => '<div class="form-check form-check-inline">{input} {label}</div>',
                    'separator' => '',
                    'class'=>'form-check-input',
                    'labelOptions'=> array('class' => 'form-check-label' )
                ))?>
            </div>
        </div>
        <!-- <button class="btn btn-info btn-sm" type="button" id="signature-save">
                <span class="fa fa-pen"></span> <?php echo Yii::t('reg', "Agree & sign") ?>
        </button>
        <div class="wrapper" id="signature-div">
            <div class="signBody">
                <div class="signCan">
                    <canvas id="signature-pad" class="signature-pad"></canvas>
                </div>
            </div>
           <div class="signText"><?php echo Yii::t('reg', "Rotate your phone horizontal for better view to sign") ?></div>
            <canvas id = "watermark" width = "100%"  height = "150px" style="display:none;"></canvas>
            <p>
                <button type="button" class="btn btn-primary exit"><?php echo Yii::t('bind', "Confirm") ?></button>
                <button type="button" class="btn btn-outline-primary clear"><?php echo Yii::t('reg', "Rewrite") ?></button>
                <button type="button" class="btn btn-outline-dark dropOut"><?php echo Yii::t('bind', "Cancel") ?></button>
            </p>
        </div>
        <p id="signature-data">
            <?php if($formModel10->filedata): ?>
                <img src="<?php echo Mims::OssUploadUrl($formModel10->filedata) ?>" >
            <?php endif; ?>
        </p>
        <input type="text" id="filedata" name="RegStep10Form[filedata]" value="<?php echo $formModel10->filedata ?$formModel10->filedata : ''?>" hidden="hidden"> -->
        <div class="mt-3">
            <button type="submit" class="btn btn-primary btn-lg btn-block examine"><?php echo Yii::t('reg', 'Submit') ?></button>
            <p class="text-black-50 mt-3 text-center  examine_status">

            </p>
        </div>
        <div class="mt-3" style="height: 10px"></div>
        <?php $this->endWidget(); ?>
    </div>
    <script>
    $('.exit').click(function() {
        $('html').css({
            'overflow': 'initial',
            'position': 'initial'
        });
        $('html,body').scrollTop(scrollTops);
        var canvas = document.getElementById('signature-pad');
        var data = canvas.toDataURL("image/png");
        // $("#signature-data").attr('src', data);
        $("#signature-data").html('<img src=' + data + ' >')
        $("#filedata").val(data);
        $("#signature-data").show();
        $('#signature-div').hide();
        var width = canvas.width / 8
        var height = canvas.height / 8
        $('#signature-data img').height(height)
        $('#signature-data img').width(width)
    })
    // 重写
    $('.clear').click(function() {
        signatureInit();
    })
    var canvas = document.getElementById('signature-pad');
    $('.dropOut').click(function(){
        $('html').css({
            'overflow': 'initial',
            'position': 'initial'
        });
        $('html,body').scrollTop(scrollTops);
        $('#signature-div').hide();
        clea = canvas.getContext("2d");
        clea.clearRect(0,0,500,500);
    })
    // 点击全名书写
    $('#signature-save').click(function() {
        //canvas全屏
        $('#signature-div').show();
        signatureInit();
    });
    // 初始化画板
    var scrollTops;
    function signatureInit() {
        scrollTops = $(window).scrollTop();
        $('html').css({
            'overflow': 'hidden',
            'position': 'fixed'
        })
        ratio = Math.max(window.devicePixelRatio || 1, 1);
        canvas.width = canvas.offsetWidth * ratio;
        canvas.height = canvas.offsetHeight * ratio;
        canvas.getContext("2d").scale(ratio, ratio);
        var signaturePad = new SignaturePad(canvas);
        watermark(canvas);
    }

    function hengshuping() {
        if(window.orientation == 180 || window.orientation == 0) {
            if($('.wrapper').is(':visible')){
                clea = canvas.getContext("2d");
                clea.clearRect(0,0,500,500);
                setTimeout("signatureInit()",200);
            }
        }
        if(window.orientation == 90 || window.orientation == -90) {
            if($('.wrapper').is(':visible')){
                clea = canvas.getContext("2d");
                clea.clearRect(0,0,500,500);
                setTimeout("signatureInit()",200);
            }
        }
    }
    window.addEventListener("onorientationchange" in window ? "orientationchange" : "resize", hengshuping, false);
    // 绘制水印
    function watermark(canvas) {
        var date = new Date();
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        var strDate = date.getDate();
        if(month >= 1 && month <= 9) {
            month = "0" + month;
        }
        if(strDate >= 0 && strDate <= 9) {
            strDate = "0" + strDate;
        }
        var currentdate = year + month + strDate
        var cw = $('#watermark')[0];
        cw.width = $(document).width();
        var ctx = cw.getContext("2d");   //返回一个用于在画布上绘图的环境


        ctx.fillStyle = "rgba(100,100,100,0.1)";
        if(window.orientation == 90 || window.orientation == -90) {
            ctx.font="30px 黑体";
            ctx.rotate(-10*Math.PI/180);
            $('.signText').hide()
            ctx.fillText("<?php echo Yii::t('reg', "School safety agreement") ?>" + currentdate + "",100, 130);
        } else if(window.orientation == 0 || window.orientation == 180) {
            ctx.font="20px 黑体";
            ctx.rotate(-18*Math.PI/180);
             $('.signText').show()
           ctx.fillText("<?php echo Yii::t('reg', "School safety agreement") ?>" + currentdate + "", 0, 130);
        }

        ctx.rotate('20*Math.PI/180');  //坐标系还原
        var crw =canvas;
        ctxr = canvas.getContext("2d");
        ctxr.width = $(window).height();
        ctxr.clearRect(0,0,500,500);  //清除整个画布
        var pat = ctxr.createPattern(cw, "repeat");    //在指定的方向上重复指定的元素
        ctxr.fillStyle = pat;
        ctxr.fillRect(0, 0, crw.width, crw.height);
    }
    </script>
