<style>
    .bg{
        width:20px;
        height: 20px;
        border-radius:50%;
        display: inline-block;
        line-height:20px;
        text-align: center;
        padding: 0;
    }
    [v-cloak] {
        display: none;
    }
    .nav-wizard > li>a:hover{
        background:#d5d5d5
    }
    .nav-wizard > li{
        text-align: center;
        margin-bottom: 4px;
    }
    .nav-wizard a{
        color:#ADADAD;
        font-size:14px
    }
    .number{
        display: inline-block;
        width:17px;
        height:17px;
        border:1px solid #ADADAD;
        border-radius:50%;
        text-align: center;
        line-height:17px;
        margin-right:14px
    }
    .nav-wizard > li:not(:first-child) > a:before{
        border-top: 23px inset transparent;
        border-bottom: 18px inset transparent;
    }
    .nav-wizard > li:not(:last-child) > a:after{
        border-top: 23px inset transparent;
        border-bottom: 17px inset transparent;
    }
    .active .number{
        border:1px solid #fff;
    }
    .sign{
	    width:50px;
    }
    .card{
        width:40px
    }
    .badge-error {
      background-color: #b94a48;
    }
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', '新生注册'), array('//mcampus/registration/index')) ?></li>
        <li class="active"><?php echo Yii::t('site','完成情况') ?></li>
    </ol>
    <div class="row">
        <div class="col-md-2 col-sm-2">
            <ul class="nav nav-pills nav-stacked text-left background-gray" id="pageCategory">
                <?php foreach ($this->getSubMenu() as $val){ ?>
                    <li class="<?php echo ($this->getAction()->getId() == $val['url']) ? "active " : ""; ?>" ><a href="<?php echo $this->createUrlReg($val['url']) ?>"><?php echo $val['label'] ?></a></li>
                <?php } ?>
            </ul>
        </div>
        <div class="col-md-10 col-sm-10" >
            <div id='registration' v-cloak>
                <div class="row">
                    <div class="col-md-2">
                        <div class="btn-group mb15 wid"><button type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" class="btn btn-default dropdown-toggle  wid">{{startYearList[yid]}} <span class="caret"></span></button>
                            <ul class="dropdown-menu wid">
                                <li v-for='(data,index) in YearList'>
                                    <a :href="'<?php echo $this->createUrl('/mcampus/registration/process', array('branchId' => $this->branchId));?>&yid='+data.id+''">{{data.title}} </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="listul">
                    <ul class="nav nav-wizard mb15">
                        <template  v-for='(list,index) in steps'>
                            <li :class="{active:list==current}" >
                                <a :href="'<?php echo $this->createUrlReg('/mcampus/registration/process', array('branchId' => $this->branchId)); ?>&stepId='+list+''" ><span class="number">{{index+1}}</span>{{items[list]}}</a>
                            </li>
                        </template>
                    </ul>
                </div>
            	<p class="pull-right">
                    <?php if ($stepId == 3) : ?>
                    <a class="btn btn-primary" target="_blank" href='<?php echo $this->createUrl('/mcampus/product/index', array('branchId' => $this->branchId)); ?>'><?php echo Yii::t('user','订单详情'); ?></a>
                    <?php else: ?>
					<a class="btn btn-primary" href='javascript:;' @click='exportTab()'><?php echo Yii::t('user','Excel Export'); ?></a>
                    <?php endif; ?>
				</p>
				<div class="clearfix"></div>
                <!--整体情况-->
                <div class="modal fade" tabindex="-1" role="dialog" id='overallModel'>
                    <div class="modal-dialog" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                                <h4 class="modal-title">整体情况</h4>
                            </div>
                            <div class="modal-body">
                            <p>孩子信息</p>
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>名字</th>
                                        <th>班级</th>
                                    </tr>
                                </thead>
                            <tbody>
                            <tr>
                                <td>{{name}}</td>
                                <td>{{class_title}}</td>
                            </tr>
                            </tbody>
                            </table>
                            <p>完成情况</p>
                            <table width="100%" >
                            <thead>
                                <tr style="height:30px">
                                <!-- -1驳回 0未操作 1通过 2重新提交 -->
                                    <td align="center" valign="middle" v-for='(data,key,index) in getOverAll.steps' width='100'>
                                    <span v-if='getOverAll.childSteps[key]'>
                                        <span v-if='getOverAll.childSteps[key].complete==-1' class="label label-danger bg">{{index+1}}</span>
                                        <span v-if='getOverAll.childSteps[key].complete==0' class="label label-warning bg">{{index+1}}</span>
                                        <span v-if='getOverAll.childSteps[key].complete==1' class="label label-success bg">{{index+1}}</span>
                                        <span v-if='getOverAll.childSteps[key].complete==2' class="label label-info bg">{{index+1}}</span>
                                    </span>
                                    <span v-else class="label label-default bg">{{index+1}}</span>
                                    </td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr style="height:30px">
                                    <td align="center" valign="middle" v-for='(all,num,index) in getOverAll.steps'>
                                        <small v-if='getOverAll.childSteps[num]'>
                                            {{completeConfig[getOverAll.childSteps[num].complete]}}
                                        </small>
                                        <small v-else>
                                            未填写
                                        </small>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <p>家长信息</p>
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th></th>
                                    <th>父亲</th>
                                    <th>母亲</th>
                                </tr>
                            </thead>
                        <tbody>
                        <tr>
                            <td>姓名</td>
                            <td v-for='(father,num,index) in getOverAll.parents'>{{father.name}}</td>
                        </tr>
                        <tr>
                            <td>电话</td>
                            <td v-for='(father,num,index) in getOverAll.parents'>{{father.mphone}}</td>
                        </tr>
                            <tr>
                            <td>邮件</td>
                            <td v-for='(father,num,index) in getOverAll.parents'>{{father.email}}</td>
                        </tr>
                        </tbody>
                        </table>
                            </div>
                            <div class="modal-footer">
                            <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php
            if ($stepId == 1){
                $this->renderPartial("processBus", array('res' => $res, 'stepId' => $stepId, 'completeConfig' => $completeConfig));
            }else if ($stepId == 2){
                $this->renderPartial("processCard", array('res' => $res, 'stepId' => $stepId, 'completeConfig' => $completeConfig));
            }else if ($stepId == 3){
                $this->renderPartial("processUniform", array('res' => $res, 'stepId' => $stepId, 'completeConfig' => $completeConfig));
            }else if ($stepId == 4){
                $this->renderPartial("processMedical", array('res' => $res, 'stepId' => $stepId, 'completeConfig' => $completeConfig));
            }else if ($stepId == 5){
                $this->renderPartial("processLunch", array('res' => $res, 'stepId' => $stepId, 'completeConfig' => $completeConfig));
            }else if ($stepId == 6){
                $this->renderPartial("processLang", array('res' => $res, 'stepId' => $stepId, 'completeConfig' => $completeConfig));
            }else if ($stepId == 7){
                $this->renderPartial("processFee", array('res' => $res, 'stepId' => $stepId, 'completeConfig' => $completeConfig));
            }else if ($stepId == 8){
                $this->renderPartial("processSafe", array('res' => $res, 'stepId' => $stepId, 'completeConfig' => $completeConfig));
            }else if ($stepId == 9){
                $this->renderPartial("processStudent", array('res' => $res, 'stepId' => $stepId, 'completeConfig' => $completeConfig));
            }else if ($stepId == 10){
                $this->renderPartial("processParents", array('res' => $res, 'stepId' => $stepId, 'completeConfig' => $completeConfig));
            }else if ($stepId == 11){
                $this->renderPartial("processMaterial", array('res' => $res, 'stepId' => $stepId, 'completeConfig' => $completeConfig));
            }
            ?>
            <p>
        </div>
    </div>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    var config = <?php echo json_encode($config); ?>;
    var steps = <?php echo json_encode($steps); ?>;
    var startYearList = <?php echo json_encode($startYearList) ?>;//年月
    var completeConfig = <?php echo json_encode($completeConfig) ?>;//状态
    var yid = '<?php echo $yid ?>';
    var stepId = '<?php echo $stepId ?>';
    function sortKey(array, key) {
        return array.sort(function(a, b) {
            var x = parseInt(a[key])
            var y = parseInt(b[key]);
            return((x > y) ? -1 : (x < y) ? 1 : 0)
        })
    }
    var  registrations = new Vue({
        el: "#registration",
        data: {
            items: config,
            steps:steps,
            startYearList:startYearList,
            yid:yid,
            YearList:'',
            current:stepId,
            completeConfig:completeConfig,
            overAll:'',
            getOverAll:'',
            name:'',
            class_title:'',
        },
        created: function() {
            var newcalendarArr = []
            for(key in startYearList) {
                var calendarArrkey = {}
                calendarArrkey.id = parseInt(key)
                calendarArrkey.title = startYearList[key]
                newcalendarArr.push(calendarArrkey)
            }
            this.YearList = sortKey(newcalendarArr, 'id')
        },
        computed: {},
        methods: {
            exportTab(){
			    $.ajax({
			        url: '<?php echo $this->createUrlReg('export') ?>',
			        type: 'POST',
			        data: {yid:yid,step:stepId},
			        dataType: 'json',
			        success: function (res) {
			            if (res.state == 'success') {
			                var data = res.data.items;
			                const filename = res.data.title;
			                const ws_name = "SheetJS";

			                const worksheet = XLSX.utils.aoa_to_sheet(data);
			                const workbook = XLSX.utils.book_new();
			                XLSX.utils.book_append_sheet(workbook, worksheet, ws_name);
			                // generate Blob
			                const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
			                const blob = new Blob([wbout], {type: 'application/octet-stream'});
			                // save file
			                let link = document.createElement('a');
			                link.href = URL.createObjectURL(blob);
			                link.download = filename;
			                link.click();
			                setTimeout(function() {
			                    // 延时释放掉obj
			                    URL.revokeObjectURL(link.href);
			                    link.remove();
			                }, 500);
			            }
			        },
			    });
			},
        },
    })
    function overalls(obj){
        var id=$(obj).attr('data-id')
        var name=$(obj).attr('data-name')
        var class_title=$(obj).attr('data-class')
        registrations.name=name
        registrations.class_title=class_title
        $.ajax({
            url: "<?php echo $this->createUrlReg('overall')?>",
            type: 'get',
            dataType: 'json',
            data: {
                childid:id
            },
            success: function(data) {
                if(data.state == "success") {
                    registrations.getOverAll=data.data
                    $('#overallModel').modal('show')
                } else {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                }
            },
            error: function(data) {
                alert('请求错误')
            }
        })
    }
</script>
