<?php

class SecondaryController extends BranchBasedController
{
    // 访问action的初级权限
    public $actionAccessAuths = array(
        'Addreport'             => array('access'=>'o_MS_Rrport', 'admin'=>'o_MS_RrportMgt'),
        'saveCategory'          => array('access'=>'o_MS_Rrport', 'admin'=>'o_MS_RrportMgt'),
        'addcategory'           => 'o_MS_RrportMgt',
        'createreport'          => 'o_MS_RrportMgt',
        'saveItembat'           => 'o_MS_RrportMgt',
        'Delereport'            => 'o_MS_RrportMgt',
        'delItem'               => 'o_MS_RrportMgt',
        'updateSort'            => 'o_MS_RrportMgt',
    );

    public function createUrl($route, $params = array(), $ampersand = '&', $parentOnly = false)
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }
    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mgrades/secondary/index');

        Yii::import('common.models.calendar.*');
        Yii::import('common.models.secondary.*');
    }

    public function actionIndex()
    {
        $criteria = new CDbCriteria();
        $criteria->order = 'stat DESC';
        $dataProvider=new CActiveDataProvider('SecondaryReport', array(
            'criteria'=>$criteria,
        ));

        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue.js');
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');


        $this->render('/secondary/index',array(
            'dataProvider' => $dataProvider,
        ));
    }

    public  function  actionAddreport()
    {
        Yii::import('common.models.visit.*');
        Yii::import('common.models.attendance.*');

        $id = Yii::app()->request->getParam('id', 0);
        $class = Yii::app()->request->getParam('class', array());

        $model = SecondaryReport::model()->findByPk($id);
        $classType = array();
        if(!$model){
            $model = New SecondaryReport();
        }else{
            $criteria = new CDbCriteria();
            $criteria->compare('template_id', $model->id);
            $reportGradeObj = SecondaryReportGrade::model()->findAll($criteria);

            if($reportGradeObj){
                foreach($reportGradeObj as $itemObj){
                    $classType[] = $itemObj->grade;
                }
            }
        }


        if($_POST['SecondaryReport']){
            if(!$model->id){
                $model->time = time();
            }

            // if(empty($class)){
            //     $this->addMessage('state', 'fail');
            //     $this->addMessage('report', 'Select at least 1 grade as required');
            //     $this->showMessage();
            // }
            $model->attributes = $_POST['SecondaryReport'];
            if($model->stat){

                $criteria = new CDbCriteria();
                $criteria->compare('school', $this->branchId);
                $criteria->compare('stat', 1);
                if($id){
                    $criteria->compare('id', "<>{$id}");
                }
                $criteria->index = "id";
                $secondaryObj = SecondaryReport::model()->findAll($criteria);

                // if($secondaryObj && $class){
                //     $criteria = new CDbCriteria();
                //     $criteria->compare('grade', $class);
                //     $criteria->compare('template_id', array_keys($secondaryObj));
                //     $secondaryReportGradObj = SecondaryReportGrade::model()->count($criteria);

                //     if($secondaryReportGradObj){
                //         $this->addMessage('state', 'fail');
                //         $this->addMessage('message', 'Only 1 valid report template for each grade level at a time');
                //         $this->showMessage();
                //     }
                // }

            }



            $model->change_time = time();
            $model->school = $this->branchId;
            $model->uid = Yii::app()->user->id;

            if($model->save()){
                $criteria = new CDbCriteria();
                $criteria->compare('template_id', $model->id);
                $criteria->compare('period', 0);
                $secondaryObj = SecondaryReportGrade::model()->findAll($criteria);
                if($secondaryObj){
                    foreach ($secondaryObj as $item) {
                        $item->delete();
                    }
                }
                foreach($class as $classItem){
                    $reportGrade = new SecondaryReportGrade();
                    $reportGrade->template_id = $model->id;
                    $reportGrade->school_id = $this->branchId;
                    $reportGrade->grade = $classItem;
                    $reportGrade->save();
                }

                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('callback', 'cbVisit');
            } else {
                var_dump($model->getErrors());
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err ? $err[0] : '失败');
            }
            $this->showMessage();
        }

        $classTypes = IvyClass::getClassTypes(true, $this->branchObj->type);


        $this->renderpartial('_update', array(
            'model'=>$model,
            'modalTitle'=>'新建记录',
            'classTypes'=> $classTypes,
            'classType'=> $classType,
        ));
    }

    public function actionEditItem($id=0, $cid=0)
    {
        if($id){
            //$cfgCampusProgram = CommonUtils::LoadConfig('CfgCampusProgram');
            $model = SecondaryReport::model()->findByPk($id);
            $achievement = New AchievementReportCourseStandard;

            $criteria = new CDbCriteria();
            $criteria->compare('td', $id);
            $criteria->order = 'weight';
            $items = AchievementReportCourse::model()->findAll($criteria);
            $subs = "";
            if($cid){
                $criteria = new CDbCriteria();
                $criteria->compare('t.courseid', $cid);
                $criteria->order= 't.id, t.title_cn, items.weight';
                $subs = AchievementReportCourseStandard::model()->with('items')->findAll($criteria);
            }
            $cs = Yii::app()->clientScript;
            $cs->registerCoreScript('jquery.ui');
            $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');

            $this->render('edititem', array(
                'model' => $model,
                'achievement' => $achievement,
                'items' => $items,
                'subs' => $subs,
                'cid' => $cid,
            ));
        }
    }

    //增加类别 A B C D  和介绍
    public function actionAddcategory()
    {
        $cid = Yii::app()->request->getParam('cid', "");
        if($cid){
            $courseStandard = Yii::app()->request->getParam('AchievementReportCourseStandard', "");
            $achievement = New AchievementReportCourseStandard;
            $achievement->title_cn = trim($courseStandard['title_cn']);
            $achievement->title_en = trim($courseStandard['title_en']);
            $achievement->introduction_cn = trim($courseStandard['introduction_cn']);
            $achievement->introduction_en = trim($courseStandard['introduction_en']);
            $achievement->courseid = $cid;
            $achievement->status = 0;
            $achievement->uid = Yii::app()->user->id;
            $achievement->create_time = time();
            $achievement->update_time = time();
            if($achievement->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('callback', 'cbSuccess');
            } else {
                $this->addMessage('state', 'fail');
                $err = current($achievement->getErrors());
                $this->addMessage('message', $err ? $err[0] : '失败');
            }
            $this->showMessage();
        }
    }

    //根据IDs删除类别
    public function actionDelItem()
    {
        if(Yii::app()->request->isPostRequest){
            $id = Yii::app()->request->getParam('id', 0);
            if($id){
                //检查是否已经使用此标准打过分
                $model = AchievementCourseScores::model()->findByPk($id);
                $kid = $model->kid;
                //是否已经有学生打过分
                $criteria = new CDbCriteria();
                $criteria->compare('optionid',$kid);
                $fractionCount = AchievementReportChildFraction::model()->count($criteria);
                if($fractionCount){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '已有学生使用了该标准请联系管理员处理');
                    $this->showMessage();
                }
                if($model->delete()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('global', 'Data deleted'));
                    $this->addMessage('refresh', true);
                }
                else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('global', 'Deletion failed'));
                }
            }
        }
        $this->showMessage();
    }

    //根据ID 修改单个分数和介绍
    public function actionGetItem($id=0)
    {
        if($id){
            $model = AchievementCourseScores::model()->findByPk($id);
            $achievem = Yii::app()->request->getParam('AchievementCourseScores', "");
            if($_POST['AchievementCourseScores']){
                $model->fraction = $achievem['fraction'];
                $model->introduction_cn = $achievem['introduction_cn'];
                $model->introduction_en = $achievem['introduction_en'];
                $model->uid = Yii::app()->user->id;
                $model->update_time = time();
                if($model->save()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message','success'));
                    $this->addMessage('refresh', true);
                } else {
                    $this->addMessage('state', 'fail');
                    $err = current($model->getErrors());
                    $this->addMessage('message', $err ? $err[0] : '失败');
                }
                $this->showMessage();

            }
            $this->renderpartial('getItem', array(
                'model' => $model,
                'modalTitle'=>'修改',
            ));
        }
    }

    //根据ID修改类别
    public function actionCreatereport()
    {
        $id = Yii::app()->request->getParam('id', "");
        $model = AchievementReportCourseStandard::model()->findByPk($id);
        $achievem = Yii::app()->request->getParam('AchievementReportCourseStandard', "");
        if($achievem){
            $model->title_cn = trim($achievem['title_cn']);
            $model->title_en = trim($achievem['title_en']);
            $model->introduction_cn = $achievem['introduction_cn'];
            $model->introduction_en = $achievem['introduction_en'];
            $model->uid = Yii::app()->user->id;
            $model->update_time = time();
            if($model->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('callback', 'cbSuccess');
            } else {
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err ? $err[0] : '失败');
            }
            $this->showMessage();
        }

        $this->renderpartial('_report', array(
            'model' => $model,
            'modalTitle'=>'修改分类',
        ));
    }

    //增加分数和介绍
    public  function  actionSaveItembat()
    {
        if(Yii::app()->request->isPostRequest){
            $kid = Yii::app()->request->getParam('category_id', "");
            $id = Yii::app()->request->getParam('id', array());
            $title_en = Yii::app()->request->getParam('title_en', array());
            $title_cn = Yii::app()->request->getParam('title_cn', array());
            if($kid){
                $criteria = new CDbCriteria();
                $criteria->compare('t.kid', $kid);
                $count = AchievementCourseScores::model()->count($criteria);
                foreach($id as $k=>$_id){
                    if($_id !== ""){
                        $count++;
                        $model = new AchievementCourseScores();
                        $model->kid = $kid;
                        $model->weight = $count;
                        $model->fraction = $_id;
                        $model->introduction_en = trim($title_en[$k]);
                        $model->introduction_cn = trim($title_cn[$k]);
                        $model->uid = Yii::app()->user->id;
                        $model->create_time = time();
                        $model->update_time = time();
                        $model->save();

                    }
                }
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('callback', 'cbSuccess');
                $this->showMessage();
            }
        }
    }

    //分数和介绍排序
    public function actionUpdateSort()
    {
        if(Yii::app()->request->isPostRequest){
            $sort = Yii::app()->request->getPost('sort', array());

            foreach($sort as $key=>$val){
                AchievementCourseScores::model()->updateByPk($key, array('weight'=>$val));
            }
        }
    }

    //删除分类
    public function actionDelereport()
    {
        $id = Yii::app()->request->getPost('id', "");
        if($id){
            $criteria = new CDbCriteria();
            $criteria->compare('kid', $id);
            $count = AchievementCourseScores::model()->count($criteria);
            if($count){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '请将分类下的元素删除,在删除本项');
                $this->showMessage();
            }
            //是否已经有学生打过分
            $criteria = new CDbCriteria();
            $criteria->compare('optionid',$id);
            $fractionCount = AchievementReportChildFraction::model()->count($criteria);
            if($fractionCount){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '已有学生使用了该标准请联系管理员处理');
                $this->showMessage();
            }
            $model = AchievementReportCourseStandard::model()->findByPk($id);
            if($model->delete()) {
                $this->addMessage('state', 'success');
                $this->addMessage('message', '删除成功');
                $this->addMessage('refresh', true);
            }else{
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err ? $err[0] : '删除失败');
            }
            $this->showMessage();

        }
    }

    //查询增加过的课程在前台显示
    public function actionGetCateGory($id)
    {
        if($id){
            $ret = array(
                'items' => array(),
                'courselist' => array(),
            );
            $criteria = new CDbCriteria();
            $criteria->compare('td', $id);
            $criteria->order='weight';
            $items = AchievementReportCourse::model()->findAll($criteria);
            foreach($items as $item){
                $ret['items'][] = array(
                    'id' => $item->id,
                    'course_program' => ($item->course_program) ? $item->course_program : 0,
                    'ctitle' => $item->title_cn,
                    'etitle' => $item->title_en,
                    'alias' => $item->title_alias,
                    'weight' => intval($item->weight),
                );
            }

            Yii::import('common.models.timetable.*');
            $criteria = new CDbCriteria();
            $criteria->index = 'program';
            $model = TimetableCourses::model()->findAll($criteria);
            $ret['courselist'][] = array(
                'id' => 0,
                'program' => 0,
                'title_cn' => '请选择',
                'title_en' => '请选择',
            );
            if($model){
                foreach ($model as $val){
                    $ret['courselist'][] = array(
                        'id' => $val->id,
                        'program' => $val->program,
                        'title_cn' => $val->title_cn,
                        'title_en' => $val->title_en,
                    );
                }
            }

            echo CJSON::encode($ret);
        }
    }

    //增加课程
    public function actionSaveCategory()
    {
        $tid = Yii::app()->request->getParam('tid', 0);
        $new = Yii::app()->request->getParam('new', array());
        $old = Yii::app()->request->getParam('old', array());
        $program = Yii::app()->request->getPost('program', array());
        $weight = Yii::app()->request->getPost('sub_weight', array());
        Yii::import('common.models.timetable.*');

        if($tid) {
            $newroot = isset($new['root']) ? $new['root'] : array();
            if(!empty($newroot)){
                foreach ($newroot as $_newroot) {
                    if($program[$_newroot]['cn'] || $program[$_newroot]['en']){
                        $model = New AchievementReportCourse;
                        $model->td = $tid;
                        $model->weight = $weight[$_newroot];
                        $model->weight = $weight[$_newroot];
                        $model->title_cn = trim($program[$_newroot]['cn']);
                        $model->title_en = trim($program[$_newroot]['en']);
                        $model->title_alias = trim($program[$_newroot]['alias']);
                        $model->status = 0;
                        $model->create_time = time();
                        $model->update_time = time();
                        $model->uid = Yii::app()->user->id;
                        $model->save();
                    }
                }
            }
            $oldroot = isset($old['root']) ? $old['root'] : array();
            if(!empty($oldroot)){
                foreach ($oldroot as $_oldroot) {
                    $model = AchievementReportCourse::model()->findByPk($_oldroot);
                    $model->td = $tid;
                    $model->weight = $weight[$_oldroot];
                    $model->title_cn = trim($program[$_oldroot]['cn']);
                    $model->title_en = trim($program[$_oldroot]['en']);
                    $model->title_alias = trim($program[$_oldroot]['alias']);
                    $model->status = 0;
                    $model->update_time = time();
                    $model->uid = Yii::app()->user->id;
                    $model->save();
                }
            }
            $this->addMessage('state', 'success');
            $this->addMessage('message', '成功');
            $this->addMessage('refresh', true);
        }else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '失败');
        }

        $this->showMessage();
    }


    //删除单个课程
    public function actionDelCateGory()
    {
        $id = Yii::app()->request->getParam('id');
        if($id){
            $model = AchievementReportCourse::model()->findByPk($id);
            $program_id = $model->course_program;
            if($model->delete()){
                Yii::import('common.models.timetable.*');
                $criteria = new CDbCriteria();
                $criteria->compare('program', $program_id);
                $courseModel = TimetableCourses::model()->findAll($criteria);
                if($courseModel){
                    foreach ($courseModel as $val){
                        $val->report_course_id = 0;
                        $val->save();
                    }
                }
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('global', 'Data deleted'));
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('global', 'Deletion failed'));
            }
            $this->showMessage();
        }
    }

    public function getButton($data, $row)
    {
        echo '<a href='. $this->createUrl('addreport',array("id"=>$data->id)) . ' type="button" class="J_modal btn btn-primary btn-sm"><span class="glyphicon glyphicon-wrench"></span></a> ';
        echo sprintf('<button type="button" class="btn btn-primary btn-sm" onclick="editCategory('.$data->id.')"
        title="%s"><span class="glyphicon glyphicon-th-list"></span></button> ', Yii::t('report', 'Categories Edit'));
        echo sprintf('<a role="button" class="btn btn-primary btn-sm" title="%s" target=_blank href="%s"><span
        class="glyphicon glyphicon-pencil"></span></a> ',
            Yii::t('report', 'Items Edit'),
            $this->createUrl('editItem',array('id'=>$data->id)));
    }

    public function getActive($data, $row)
    {

        echo $data->stat == 1 ? Yii::t('report', 'Active') : Yii::t('report', 'Disabled');
    }
}