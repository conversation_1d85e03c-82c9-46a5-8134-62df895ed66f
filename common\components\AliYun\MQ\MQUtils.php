<?php

/**
 * Created by PhpStorm.
 * User: R.X.
 * Date: 2017/5/18 0018
 * Time: 16:23
 */
class MQUtils
{

    private $configs = array();

    const TXT_SIG = "Signature";
    const TXT_PID = "ProducerID";
    const TXT_CID = "ConsumerID";
    const TXT_ACCESSKEY = "AccessKey";
    const TXT_NEWLINE = "\n";
    const TXT_PREFIX_PROD = 'prod.';     //生成环境前缀
    const TXT_PREFIX_TEST = 'test.';    //测试前缀

    function __construct(){

        //读取配置信息; 正式环境
        if (file_exists(dirname(__FILE__) . "/config.server.ini")) {
            $this->configs = parse_ini_file(dirname(__FILE__) . "/config.server.ini");
        } else {
            $this->configs = parse_ini_file(dirname(__FILE__) . "/config.local.ini");
        }

        $this->configs['is_server'] = ( strtoupper(substr(PHP_OS,0,5)) === 'LINUX' ) ? true: false;

    }

    public function getConfigs()
    {
        return $this->configs;
    }

    //计算签名
    public static function calSignature($str,$key)
    {
        $sign = "";
        if(function_exists("hash_hmac"))
        {
            $sign = base64_encode(hash_hmac("sha1",$str,$key,true));
        }
        else
        {
            $blockSize = 64;
            $hashfunc = "sha1";
            if(strlen($key) > $blockSize)
            {
                $key = pack('H*',$hashfunc($key));
            }
            $key = str_pad($key,$blockSize,chr(0x00));
            $ipad = str_repeat(chr(0x36),$blockSize);
            $opad = str_repeat(chr(0x5c),$blockSize);
            $hmac = pack(
                'H*',$hashfunc(
                    ($key^$opad).pack(
                        'H*',$hashfunc($key^$ipad).$str
                    )
                )
            );
            $sign = base64_encode($hmac);
        }
        return $sign;
    }
    //计算时间戳
    public static function microtime_float()
    {
        list($usec,$sec) = explode(" ",microtime());
        return ((float)$usec+(float)$sec);
    }
}