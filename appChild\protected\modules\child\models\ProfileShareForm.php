<?php

/**
 * 设置分享报告
 */
class ProfileShareForm extends CFormModel
{
	public $accessCode;
	public $enabled;
	public $previewText;
	
	public function rules()
	{
		return array(
			array('accessCode', 'required', 'on'=>'create'),
			array('accessCode', 'length', 'max'=>20, 'min' => 4,'message' => Yii::t("message", "Access code length 4-20 characters."), 'on'=>'create'),
		);
	}
	
	public function attributeLabels()
	{
		return array(
			'accessCode'=>Yii::t("labels", "Access Code"),
		);
	}
	
	public function genAccessLink($childid){
		return md5(md5($childid.Yii::app()->basePath));
	}
	
	public function genAccessUrl($childid){
        if ( Mims::useIS() ){
            $url = 'http://www.ivyonline.cn/v/' . $this->genAccessLink($childid);
        }
        else {
            if(isset(Yii::app()->params['shareOptions']) && Yii::app()->params['shareOptions']['useProduction']===true){
                $url = Yii::app()->params['shareOptions']['productionUrl'] . '/v/' . $this->genAccessLink($childid);
            }else{
                $url = Yii::app()->getController()->createUrl("//access/index", array("id"=>$this->genAccessLink($childid)),'&', true);
            }
        }
		return $url;
	}
	
	public function getPreviewText($child){
		if($this->enabled){
			$url = $this->genAccessUrl($child->childid);
            $school_en = Mims::unIvy() ? Yii::app()->name : 'Ivy Schools';
            $school_cn = Mims::unIvy() ? Yii::app()->name : '艾毅';
			if(Yii::app()->language == 'en_us')
			$text = sprintf("Stay in touch with %s's growth and development at %s with me! \r\n\r\nAccess URL: \r\n\r\n%s \r\n\r\nAccess Code: %s \r\n", $child->getChildName(), $school_en, $url, $child->access_pass);
			else
			$text = sprintf("和我一起分享 %s 在%s的成长和发展! \r\n\r\n访客地址： \r\n\r\n%s \r\n\r\n访客口令： %s \r\n", $child->getChildName(), $school_cn, $url, $child->access_pass);
		}
		else{
			$text = "";
		}
		$this->previewText = $text;
	}
	
}