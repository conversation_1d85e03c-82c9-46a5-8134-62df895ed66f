<?php $typeList = CommonUtils::LoadConfig('CfgASA');
$jobType = array();
$staffJobType = array();
foreach ($typeList['job_type'] as $k => $job_type) {
    if($k < 10){
        $jobType[$k] = (Yii::app()->language == 'zh_cn') ? $job_type['cn'] : $job_type['en'];
    }else{
        $staffJobType[$k] = (Yii::app()->language == 'zh_cn') ? $job_type['cn'] : $job_type['en'];
    }
}

$type = array();
foreach ($typeList['type'] as $k => $job_type) {
    $type[$k] = (Yii::app()->language == 'zh_cn') ? $job_type['cn'] : $job_type['en'];
}
?>
    <div class="container-fluid">
        <ol class="breadcrumb">
            <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
            <li><?php echo CHtml::link(Yii::t('site', 'Basic'), array('//mcampus/default/index')) ?></li>
            <li><?php echo CHtml::link(Yii::t('site', '课后课管理'), array('//mcampus/asainvoice/index')) ?></li>
            <li class="active">课后课管理</li>
        </ol>

        <div class="row">
            <!-- 左侧菜单 -->
            <div class="col-md-2 col-sm-2 mb10">
                <?php
                $this->widget('zii.widgets.CMenu', array(
                    'items' => $this->getMenu(),
                    'id' => 'pageCategory',
                    'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                    'activeCssClass' => 'active',
                    'itemCssClass' => ''
                ));
                ?>
            </div>

            <div class="col-md-10 col-sm-12">
                <div class="col-md-2">
                    <a class="btn btn-default J_modal" href="<?php echo $this->createUrl('updateGroup'); ?>">新建课程组</a>
                    <?php if ($groupId): ?>
                        <a class="btn btn-default J_modal"
                           href="<?php echo $this->createUrl('updateGroup', array('groupId' => $groupId)); ?>">编辑课程组</a>
                    <?php endif; ?>
                    <br>
                    <br>
                    <ul class="nav nav-pills nav-stacked background-gray">

                        <?php
                        foreach ($courseGroup as $v) {
                            $href = $this->createUrl('index', array('groupId' => $v->id));
                            $active = ($v->id == $groupId) ? 'active' : '';
                            echo "<li class='{$active}'><a href='{$href}'>";
                            echo $v->title_cn;
                            echo '</a></li>';
                        }
                        ?>
                    </ul>
                </div>

                <div class="col-md-10">
                    <?php if ($groupId): ?>
                        <a class="btn btn-default J_modal"
                           href="<?php echo $this->createUrl('updateCourse', array('groupId' => $groupId)); ?>">增加工作人员
                        </a>
                        <?php
                        $this->widget('ext.ivyCGridView.BsCGridView', array(
                            'id' => 'course-staff',
                            'afterAjaxUpdate' => 'js:head.Util.modal',
                            'dataProvider' => $staffCourseModel,
                            'template' => "{items}{pager}",
                            //状态为无效时标红
                            'rowCssClassExpression' => '( $data->status == 0 ? "danger" : "" )',
                            'colgroups' => array(
                                array(
                                    "colwidth" => array(100, 100, 100, 100, 100, 100, 100, 100, 200),
                                )
                            ),
                            'columns' => array(
                                array(
                                    'name' => 'name',
                                    'value' => '$data->vendor->getName()',
                                ),
                                array(
                                    'name' => 'job_type',
                                    'value' => array($this, "getJobType"),
                                ),
                                'unit_salary',
                                array(
                                    'name' => 'unit_type',
                                    'value' => array($this, "getUnitType"),
                                ),
                                array(
                                    'name'=>'updated',
                                    'value'=>'date("Y-m-d", $data->updated_by)',
                                ),
                                array(
                                    'class' => 'CButtonColumn',
                                    'template' => ' {update} {delete}',
                                    'updateButtonLabel' => Yii::t('global', 'Edit'),
                                    'updateButtonUrl' => 'Yii::app()->controller->createUrl("updateCourse",array("id"=>$data->id, "branchId"=>Yii::app()->controller->branchId))',
                                    'deleteButtonUrl' => 'Yii::app()->controller->createUrl("deleteCourse",array("id"=>$data->id, "branchId"=>Yii::app()->controller->branchId))',
                                    //删除时触发的事件 afterdelete   json转换（JSON.parse）
                                    'afterDelete' => 'function(link,success,data){ data = JSON.parse(data); if(data.state=="fail" && data) alert(data.message);}',
                                    'updateButtonImageUrl' => false,
                                    'deleteButtonImageUrl' => false,
                                    'updateButtonOptions' => array('class' => 'J_modal btn btn-info btn-xs mb5'),
                                    'deleteButtonOptions' => array('class' => 'btn btn-danger btn-xs mb5'),
                                ),
                            ),
                        ));
                        ?>
                    <?php endif; ?>
                    <?php if ($groupId): ?>
                        <a class="btn btn-default J_modal"
                           href="<?php echo $this->createUrl('updateCourse', array('groupId' => $groupId)); ?>">新建课程</a>
                        <?php
                        $this->widget('ext.ivyCGridView.BsCGridView', array(
                            'id' => 'course-grid',
                            'afterAjaxUpdate' => 'js:head.Util.modal',
                            'dataProvider' => $courseModel,
                            'template' => "{items}{pager}",
                            //状态为无效时标红
                            'rowCssClassExpression' => '( $data->status == 0 ? "danger" : "" )',
                            'colgroups' => array(
                                array(
                                    "colwidth" => array(100, 100, 100, 100, 100, 100, 100, 100, 200),
                                )
                            ),
                            'columns' => array(
                                'title_cn',
                                'title_en',
                                'vendor',
                                'unit_price',
                                'default_count',
                                array(
                                    'class' => 'CButtonColumn',
                                    'template' => '{teacher} {show} {update} {delete}',
                                    'updateButtonLabel' => Yii::t('global', 'Edit'),
                                    'updateButtonUrl' => 'Yii::app()->controller->createUrl("updateCourse",array("id"=>$data->id, "branchId"=>Yii::app()->controller->branchId))',
                                    'deleteButtonUrl' => 'Yii::app()->controller->createUrl("deleteCourse",array("id"=>$data->id, "branchId"=>Yii::app()->controller->branchId))',
                                    //删除时触发的事件 afterdelete   json转换（JSON.parse）
                                    'afterDelete' => 'function(link,success,data){ data = JSON.parse(data); if(data.state=="fail" && data) alert(data.message);}',
                                    'updateButtonImageUrl' => false,
                                    'deleteButtonImageUrl' => false,
                                    'updateButtonOptions' => array('class' => 'J_modal btn btn-info btn-xs mb5'),
                                    'deleteButtonOptions' => array('class' => 'btn btn-danger btn-xs mb5'),
                                    'buttons' => array(
                                        'show' => array(
                                            'label' => '名单',
                                            'options' => array('class' => 'J_modal btn btn-xs btn-info mb5'),
                                            'url' => 'Yii::app()->controller->createUrl("showCourseInvoice", array("id"=>$data->id))',
                                        ),
                                        'teacher' => array(
                                            'label' => '老师',
                                            'options' => array('class' => 'btn btn-xs btn-info mb5 teachermodal_open'),
                                            'url' => '"javascript:teacherModal($data->id)"',
                                        ),
                                    ),

                                ),
                            ),
                        ));
                        ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="teachermodal" data-backdrop="static">
        <div class="modal-dialog" role="document">
            <form action="<?php echo $this->createUrl('addteacher') ?>" method="post" class="J_ajaxForm">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                        <h4 class="modal-title" id="exampleModalLabel">老师列表</h4>
                    </div>
                    <div class="modal-body" id="teacherVueModal">
                        <table class="table table-hover" v-show="!teacherdata.length <= 0">
                            <thead>
                            <tr>
                                <th>老师姓名</th>
                                <th>老师来源</th>
                                <th>联系方式</th>
                                <th>金额</th>
                                <th>职位</th>
                                <th></th>
                            </tr>
                            </thead>
                            <tbody>
                            <p class="p10" v-show="teacherdata.length <= 0">暂无数据</p>
                            <tr v-for="data in teacherdata">
                                <td>{{ data.tacherName }}</td>
                                <td>{{ data.type }}</td>
                                <td>{{ data.phone }}</td>
                                <td>{{ data.teacherPrice }}</td>
                                <td>{{ data.job_type }}</td>
                                <td><a class="J_ajax_del btn btn-danger btn-xs"
                                       :href="'<?php echo $this->createUrl('Delcourse') ?>&id='+data.staffId"><span
                                            class="glyphicon glyphicon-remove"></span></a></td>
                            </tr>
                            </tbody>
                        </table>
                        <hr/>
                        <div>
                            <!-- Nav tabs -->
                            <ul class="nav nav-tabs" role="tablist">
                                <li :class="{'active':index == 1}" v-for="(type,index) in types">
                                    <a :href="'#tab'+index" aria-controls="home" role="tab" :checktab="'tab'+index"
                                       :checknum="index"
                                       data-toggle="tab">{{type}}</a>
                                </li>
                            </ul>
                            <!-- Tab panes -->
                            <div class="row">
                                    <input type="hidden" name="courseId" id="courseId"/>
                                    <div class="tab-content" style="padding: 14px">
                                        <div v-for="(type,index) in types" :id="'tab' + index" :class="{'active':index == 1}" class="tab-pane"> <!--内部个人-->
                                            <div role="tabpanel" class="col-md-12">
                                                <div class="form-group">
                                                    <input type="hidden" :id="'teacherId' + index" name="teacher" v-bind="{ disabled: index != 1}">
                                                    <input type="text" :id="'project' + index" class="form-control" v-bind="{ disabled: index != 1}"
                                                           placeholder="请搜索老师">
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="form-group">
                                                    <input type="text" class="form-control" name="teacherPhone" v-bind="{ disabled: index != 1}"
                                                           :id="'teacherPhone' + index"
                                                           placeholder="联系方式">
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="form-group">
                                                    <input type="text" class="form-control" name="teacherPrice" v-bind="{ disabled: index != 1}"
                                                           placeholder="课时费">
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <select class="form-control" name="teacher_job" v-bind="{ disabled: index != 1}">
                                                    <option v-for="(jobtype,index) in jobtypes" :value="index">
                                                        {{jobtype}}
                                                    </option>
                                                </select>
                                            </div>
                                            <input type="hidden" name="teacherType" :value="index" v-bind="{ disabled: index != 1}"/>
                                        </div>
                                    </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <label id="J_fail_info" class="text-warning" style="display: none;"><i
                                class="glyphicon glyphicon-remove text-warning"></i><span></span></label>
                        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-primary J_ajax_submit_btn">添加老师</button>
                        <input type="reset" class="_reset" style="display: none"/>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script>
        var modal = '<div class="modal fade" id="modal" class="1123" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog modal-lg" role="document"><div class="modal-content"></div></div></div>';
        $('body').append(modal);
        function cbCourse() {
            $('#modal').modal('hide');
            $.fn.yiiGridView.update('course-grid');
        }
        var teacherflag = false;
        var types = <?php echo json_encode($type)?>;
        var jobtypes = <?php echo json_encode($jobType)?>;
        var teacherdata = [];
        var teachersearch = [];
        function teacherModal(id) {
            $('#J_fail_info').hide();
            $('._reset').click();
            teacherdata.splice(0, teacherdata.length);
            $('#courseId').val(id);
            $('.tab-content').find('input').val('');
            $.getJSON('<?php echo $this->createUrl('teacherlist') ?>', {courseid: id}, function (data) {
                for (var val of data) {
                    teacherdata.push(val);
                }
            });
            $('#teachermodal').modal();
        }

        var teacherVue = new Vue({
            el: "#teacherVueModal",
            data: {
                teachersearch,
                teacherdata,
                types,
                jobtypes
            },
            created: function () {
                Vue.set({teachersearch});
                Vue.set({teacherdata});
                Vue.set({types});
                Vue.set({jobtypes})
            },
            updated: function () {
                head.Util.ajaxDel();
            },
            methods: {
                removeTodo: function (id) {
                    for (var index in this.teacherdata) {
                        if (this.teacherdata[index].staffId == id) {
                            this.teacherdata.splice(index, 1)
                        }
                    }
                }
            }
        });

        function _pushteacher(data) {    //添加老师成功回调
            teacherVue.teacherdata.push(data);
            $('#J_fail_info').hide();
            $('._reset').click();
            $('.tab-content').find('input').val('');
            resultTip({msg: '添加成功'})
        }     //自动完成
        var projects = [];
        $('.teachermodal_open').on('click', function () {
            if (!teacherflag) {
                $.ajax({
                    type: "POST",
                    url: "<?php echo $this->createUrl('schoolteacherlist') ?>",
                    dataType: "json",
                    success: function (data) {
                        $.each( data, function(i, n){
                            $("#project" + i).autocomplete({
                                minLength: 0,
                                source: n,
                                focus: function (event, ui) {
                                    $("#project" + i).val(ui.item.value);
                                    if (ui.item.teacherPhone !== '') {
                                        $("#teacherPhone" + i).val(ui.item.teacherPhone).attr('readonly', 'readonly');
                                    } else {
                                        $("#teacherPhone" + i).val('').removeAttr('readonly');
                                    }
                                    return false;
                                },
                                select: function (event, ui) {
                                    $("#project" + i).val(ui.item.value);
                                    $("#teacherId" + i).val(ui.item.teacherId);
                                    return false;
                                }
                            });
                        });

                    }
                });
                teacherflag = true;
            }

        });
        var removeTeacher = function (id) {
            teacherVue.removeTodo(id);
        };
        $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
            $('#J_fail_info').hide();
            var checktabname = $(e.target).attr('checktab');// 激活的标签页
            $('#' + checktabname + ' :input').removeAttr('disabled');

            var checkdtabname = $(e.relatedTarget).attr('checktab');// 前一个激活的标签页
            $('#' + checkdtabname + ' :input').attr('disabled', 'disabled');
        })
    </script>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>