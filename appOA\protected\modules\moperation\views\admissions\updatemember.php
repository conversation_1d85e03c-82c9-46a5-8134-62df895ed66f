<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'event-member-form',
	'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
));
$knowus = array();
foreach($this->cfg['knowus'] as $key=>$val){
    $knowus[$key]=  CommonUtils::autoLang($val['cn'], $val['en']);
}
?>
<div class="pop_cont" style="height:auto;">
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'parent_name'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'parent_name',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'child_name'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'child_name',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'email'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'email',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'tel'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'tel',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'child_birthday'); ?></label>
        <div class="col-xs-9">
            <?php
            $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                "model"=>$model,
                "attribute"=>"child_birthday",
                "options"=>array(
                    'changeMonth'=>true,
                    'changeYear'=>true,
                    'dateFormat'=>'yy-mm-dd',
                ),
                'htmlOptions'=>array(
                    'class'=>'form-control'
                ),
            ));
            ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'adult_number'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->numberField($model,'adult_number',array('class'=>'form-control length_1')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'kid_number'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->numberField($model,'kid_number',array('class'=>'form-control length_1')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'address'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'address',array('class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'knowus'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->dropDownList($model,'knowus',$knowus,array('empty'=>Yii::t('global','Please Select'), 'class'=>'form-control')); ?>
        </div>
    </div>
</div>
<div class="pop_bottom">
    <button id="J_dialog_close" type="button" class="btn btn-default pull-right"><?php echo Yii::t('global','Cancel');?></button>
    <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
</div>
<?php $this->endWidget(); ?>