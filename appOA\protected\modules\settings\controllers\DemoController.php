<?php

class DemoController extends BranchBasedController
{
	public function actionIndex()
	{
		$this->mainMenu = array(
			array(
				'label' => '菜单一',
				'url' => '#'
			),
			array(
				'label' => '菜单二',
				'url' => '#'
			),
			array(
				'label' => '菜单三',
				'url' => '#'
			)
		);
		$this->subMenu = array(
			array(
				'label' => '子菜单一',
				'url' => '#'
			),
			array(
				'label' => '子菜单二',
				'url' => '#'
			),
			array(
				'label' => '子菜单三',
				'url' => '#'
			)
		);
		$this->branchSelectParams["urlArray"] = array("//settings/demo/index");
		$this->render('index');
	}
	
	
	public function actionSelect()
	{
		$this->branchSelectParams["showList"] = true;
		$this->branchSelectParams["urlArray"] = array("//settings/demo/index");
		$this->render('select');
	}
	

	// Uncomment the following methods and override them if needed
	/*
	public function filters()
	{
		// return the filter configuration for this controller, e.g.:
		return array(
			'inlineFilterName',
			array(
				'class'=>'path.to.FilterClass',
				'propertyName'=>'propertyValue',
			),
		);
	}

	public function actions()
	{
		// return external action classes, e.g.:
		return array(
			'action1'=>'path.to.ActionClass',
			'action2'=>array(
				'class'=>'path.to.AnotherActionClass',
				'propertyName'=>'propertyValue',
			),
		);
	}
	*/
}