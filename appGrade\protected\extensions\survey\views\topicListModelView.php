<?php 
/*
 * <PERSON><PERSON>
* 2012-7-9
* 题目列表的 模型方式的 视图文件
*
*/

?>


<div class="survey_box">
	<?php if($template_count):?>
	<?php $form = $this->beginWidget('CActiveForm', array(
			'id'=>'topic-list-form',
			'enableAjaxValidation'=>false,

	));

	//$form = new CActiveForm();

	Yii::import('ext.BinPower');
	$binPower = new BinPower();
	Mims::LoadHelper('HtoolKits');
	$tk = new HtoolKits();
    $f = new CFormatter();
	$config = Mims::LoadConfig('CfgParentSurvey');
	$survey_binary_flag = $config['survey_binary_flag'];
	// fm form model
	$fb_fm = new WSurveyFeedbackForm;
	$fb_fm->survey_id = $this->surveyId;
	$fb_fm->child_id = $this->childId;

	$fbo_fm = new WSurveyFeedbackOptionForm;
	$fbt_fm = new WSurveyFeedbackTextForm;

	$template_index = 1;
	foreach ($topic_list as $template_id => $topics):

	?>

	<div class="template" id="template_<?php echo $template_id;?>">

		<?php if(!empty($template_list[$template_id])):?>
		<h3>
			<?php echo $template_list[$template_id];?>
		</h3>
		<?php 	else:?>
		<?php 	if($template_index > 1):?>
		<h3>
			<?php echo sprintf(Yii::t("survey", 'Part %s'),$template_index);?>
		</h3>
		<?php 	endif;?>
		<?php 	endif;?>
		<?php 	$template_index++;?>
	</div>

	<?php  

	foreach ($topics as $tid => $topic):

	$binPower->setPower( $topic->binary_flag);
	$has_input = $binPower->isPower($survey_binary_flag['allow_feedback']);
	$is_required = $binPower->isPower($survey_binary_flag['option_required']);
	$numconfing = $topic->getNumberconfig($this->surveyId);
	?>

	<div class="topic clearfix" id="topic_<?php echo $topic->id;?>"
		topic_id="<?php echo $topic->id;?>"
		<?php if(!empty($topic->option_group) && $is_required){echo 'is_required="true"';}else{echo 'is_required="false"';} ?>>

		<h3>
			<?php echo isset($numconfing[$tid]) ? $numconfing[$tid] : ++$tid;?>
            <?php if(!empty($topic->option_group) && $is_required):?>
            <span class="h3required">*</span>
            <?php 	endif;?>
		</h3>

		<div class="item <?php echo $langKey;?>">
			<span class="warn"> <?php 
			//请完成本题目后再提交，谢谢！
			echo Yii::t("survey", 'Please finish this topic before submit.');
			?>
			</span>
			<?php if(true === $this->topicDiglossia):?>
			<p class="title cn">
				<?php 	echo $f->formatNtext($topic->title_cn);?>
			</p>
			<p class="title en">
				<?php 	echo $f->formatNtext($topic->title_en);?>
			</p>
			<?php 	else:?>
			<p class="title <?php echo $langKey;?>">
				<?php 	echo $f->formatNtext($tk->getContentByLang($topic->title_cn,$topic->title_en));?>
			</p>
			<?php 	endif;?>
			<div class="userinput">
				<ul>
					<?php 	

					if(!empty($topic->option_group) || !empty($has_fb)){
						echo "<hr>";
					};

					if(!empty($topic->option_group)):
					echo CHtml::tag('li',array('class'=>'option'));
					// 多选
					if('2' == $topic->optionGroup->option_cat){
						foreach ($topic->optionGroup->options as $ok => $option){
							echo CHtml::tag('div',array('class'=>'qOption og_'.$topic->id));
							echo $form->checkBox($fbo_fm,'['.$topic->id.']['.$option->id.']option_answer',array('class'=>'cb topic_option','topic_id'=>$topic->id,'uncheckValue'=>null,'value'=>$option->option_value));
							echo CHtml::tag('label',array('id'=>'lWSurveyFeedbackOptionForm_'.$topic->id.'_'.$option->id.'_option_answer','for'=>'WSurveyFeedbackOptionForm_'.$topic->id.'_'.$option->id.'_option_answer','class'=>'cb_off'));
							echo CHtml::image($this->assetsHomeUrl.'/images/t.gif');
							echo CHtml::tag('span',array('class'=>'qLabel'));
							echo $tk->getContentByLang($option->title_cn,$option->title_en);
							echo CHtml::closeTag('span');
							echo CHtml::closeTag('label');
							echo CHtml::closeTag('div');
						}
						// 单选
					}elseif('1' == $topic->optionGroup->option_cat){
						foreach ($topic->optionGroup->options as $ok => $option){
							echo CHtml::tag('div',array('class'=>'qOption og_'.$topic->id));
							echo $form->radioButton($fbo_fm,'['.$topic->id.']option_answer',array('class'=>'rb topic_option','topic_id'=>$topic->id,'uncheckValue'=>null,'value'=>$option->option_value,'id'=>'WSurveyFeedbackOptionForm_'.$topic->id.'_'.$option->id.'_option_answer'));
							echo CHtml::tag('label',array('id'=>'lWSurveyFeedbackOptionForm_'.$topic->id.'_'.$option->id.'_option_answer','for'=>'WSurveyFeedbackOptionForm_'.$topic->id.'_'.$option->id.'_option_answer','class'=>'rb_off'));
							echo CHtml::image($this->assetsHomeUrl.'/images/t.gif');
							echo CHtml::tag('span',array('class'=>'qLabel'));
							echo $tk->getContentByLang($option->title_cn,$option->title_en);
							echo CHtml::closeTag('span');
							echo CHtml::closeTag('label');
							echo CHtml::closeTag('div');
						}
					}

					echo CHtml::Tag('div',array('class'=>'clearfix'));
					echo CHtml::closeTag('div');
					echo CHtml::closeTag('li');

					endif;
					if($has_input){
						echo CHtml::Tag('p',array('class'=>'feedback'));
						echo CHtml::Tag('span');
						//附加注释：
						echo Yii::t("survey", 'Comment');
						echo CHtml::closeTag('span');
						echo '<br>';
						echo $form->textArea($fbt_fm,'['.$topic->id.']fb_text',array('rows'=>2,'cols'=>6));
						echo CHtml::closeTag('p');
					}?>
				</ul>
			</div>

		</div>
	</div>

	<?php  endforeach; ?>
	<?php  endforeach; ?>
	<?php  if(true === $this->allowFeedback): ?>

	<?php if(Yii::app()->user->checkAccess('adminChild',array("childid"=>$this->getController()->getChildId()))): ?>
		<div class="actions">
			<ul>
				<li>
				<span class="btn001">
				<?php echo $form->hiddenField($fb_fm,'survey_id'); ?> <?php echo $form->hiddenField($fb_fm,'child_id'); ?>
                                <input type="hidden" value="0"
			name="is_feedback" id="is_feedback" />
					<?php echo  $this->ajaxSubmitButton; ?>
				</span>
				</li>
				<?php $this->beginWidget('zii.widgets.jui.CJuiDialog', array(
						'id'=>'mydialog',
						// additional javascript options for the dialog plugin
						'options'=>array(
								// 提示
								'title'=>Yii::t("survey", 'Hint'),
								'autoOpen'=>false,
						),
				)); ?>
				<?php $this->endWidget('zii.widgets.jui.CJuiDialog'); ?>
			</ul>
		</div>
	<?php else:?>
		<p class="tar">
			<?php echo Yii::t("portfolio","Submit not available to visitors.");?>
		</p>	
	<?php endif;?>
	<div class="clearfix"></div>
	<?php  endif; ?>

	<?php $this->endWidget(); ?>
	<?php else:?>

	<div class="no-data">
		<ul>
			<li><span> <?php 
			//暂无数据
			echo Yii::t("survey", 'No Data');
			?>
			</span>
			</li>
		</ul>
	</div>
	<?php endif;?>

</div>

<script type="text/javascript">
   
    $(document).ready(function() {
        var o_id = null;
        $(".userinput li.option .rb:checked").each(function(i, item) {
            $("#label_" + item.id).removeClass('rb_off').addClass('rb_on');
        });
        $(".userinput li.option .cb:checked").each(function(i, item) {
            $("#label_" + item.id).removeClass('cb_off').addClass('cb_on');
        });
        $("#is_feedback").val(0);
        
        setup();
    });
/*
 * 提交表单之前调用的方法，这里主要做 验证用
 */
    function beforeSendFunc(){
    	var checked_num = 0;
    	var has_error = false;
    	$("div.topic[is_required=true]").each(function(i, item) {
    		checked_num = $("#"+item.id+ " input:checked").length;
			if(parseInt(checked_num) < 1) {
				$(this).removeClass('required').addClass('required');
				has_error = true;
			}else{
                $(this).removeClass('required');
            }
		});

    	if(true == has_error) {
			var first_required = $(".survey_box .required").get(0);
			var prev_node = $(first_required).prev('.topic');
			var prev_node_id = "";
			if(prev_node.length == 0) {
				prev_node_id = $(first_required).attr("id");
			} else {
				prev_node_id = $(prev_node).attr("id");
			}
			window.location.hash = prev_node_id;
			return false;
		}
		var is_feedback = $("#is_feedback").val();
		if(is_feedback == "1") {
			return false;
		}
		$("#is_feedback").val(1);
		return true;
    }

    /*
    * 成功之后的处理方法
    */
    function successFunc(rt){

		var notice_info = "";
		var redirect_url = "<?php echo Yii::app()->homeUrl; ?>";
    	if('success' == rt.flag){
    		notice_info = "<?php echo Yii::t("survey", 'We have successfully received your survey submission, thank you!'); ?>";
    	}else if('not_login' == rt.flag){
    		notice_info = "<?php echo Yii::t("user", 'Please Login'); ?>";
    		redirect_url = "<?php echo Yii::app()->createUrl('user/login'); ?>";
    	}else if('already_fb' == rt.flag){
    		notice_info = "<?php echo Yii::t("survey", 'Voted'); ?>";
    	}else{
    		notice_info = "<?php echo Yii::t("survey", 'Failed,Please Try Again'); ?>";
    	}
    	$("#mydialog").html("<div style='text-align:center'><p>"+notice_info+"</p><p><img src='<?php echo Yii::app()->theme->baseUrl; ?>/images/loading.gif'></p></div>");
    	$("#mydialog").dialog("open");
    	setTimeout('location.href = "'+redirect_url+'";',1200);
    	
    }

    
</script>
