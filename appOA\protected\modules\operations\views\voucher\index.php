<div class="h_a"><?php echo Yii::t('voucher', '搜索（按使用现金抵用卷区间）');?></div>
<div class="search_type cc mb10">
    <form action="" method="post">
        <input type="hidden" name="action" value="search">
        <label>
         <?php echo Yii::t('voucher', '开始日期：');?>
         <?php
            $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                "attribute"=>"startdate",
                "name"=>"startdate",
                "options"=>array(
                    "changeMonth"=>true,
                        "changeYear"=>true,
                        "dateFormat"=>"yy-mm-dd",
                    ),
                    "value"=>$startdate,
                    "htmlOptions"=>array(
                        "class"=>"input length_3"
                    )
             ));
          ?>
        </label>
        <label>
            <?php echo Yii::t('voucher', '结束日期：');?>
            <?php
                $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                    "attribute"=>"enddate",
                    "name"=>"enddate",
                    "options"=>array(
                        "changeMonth"=>true,
                            "changeYear"=>true,
                            "dateFormat"=>"yy-mm-dd",
                        ),
                        "value"=>$enddate,
                        "htmlOptions"=>array(
                            "class"=>"input length_3"
                        )
                ));
           ?>
        </label>
        <button type="submit" class="btn">搜索</button>
    </form>
</div>
<?php foreach ($tranList as $val):?>
<div class="h_a"><?php echo $val->title;?></div>
<div class="table_full">
    <div class="user_group" style="border:none;">
            <table width="100%">
                <colgroup>
                    <col width="200">
                    <col width="200">
                    <col width="200">
                    <col width="150">
                    <col width="100">
                </colgroup>
                <thead>
                    <tr>
                        <th><?php echo Yii::t('voucher', '孩子名称')?></th>
                        <th>班级</th>
                        <th>账单标题</th>
                        <th>现金抵用卷金额</th>
                        <th>付款日期</th>
                        <th>收款人</th>
                    </tr>
                </thead>
                <tbody>
                    <?php 
                    $amount = 0;
                    foreach ($val->voucherInvoice as $v): 
                    ?>
                        <tr>
                            <td><?php echo CHtml::link($v->childInfo->getChildName(), $this->createUrl('//child/index/index', array('childid' => $v->childid))); ?></td>
                            <td><?php echo $v->classInfo->title; ?></td>
                            <td><?php echo CHtml::link($v->title, array("//child/invoice/viewInvoice", 'invoiceid' => $v->invoice_id, 'childid' => $v->childid), array('class' => 'mr5 J_dialog', 'non-resize' => 1)); ?></td>
                            <td><?php echo $v->amount; ?></td>
                            <td><?php echo OA::formatDateTime($v->timestampe); ?></td>
                            <td><?php echo $v->userInfo->getName(); ?></td>
                        </tr>
                    <?php 
                    $amount += $v->amount;
                    endforeach; 
                    ?>
                        <tr>
                            <td colspan="3" style="text-align: right;"><?php echo Yii::t('voucher', '代现金抵用卷总金额：');?></td>
                            <td colspan="3" style="font-weight: bold;"><?php echo OA::formatMoney($amount);?></td>
                        </tr>
                </tbody>
            </table>
        </div>
</div>
<?php endforeach;?>

