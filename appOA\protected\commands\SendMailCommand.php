<?php
class SendMailCommand extends CConsoleCommand{

    public $batchNum = 200;
    private $ivyAccessToken = '';
    private $dsAccessToken = '';

    public function actionIndex()
    {
        echo $this->getHelp();
	}

    public function actionWeeklyJournal()
    {
        Yii::import('common.models.calendar.CalendarWeek');
        Yii::import('common.models.portfolio.NotesSchCla');
        Yii::import('common.models.portfolio.NotesChild');
        Yii::import('common.models.wechat.WechatUser');
        Yii::import('common.models.ChildProfileBasic');
        Yii::import('common.models.child.HomeAddress');

        // 获取微信的access_token
         $this->getAccessToken();

        $monday = mktime(0,0,0, date("n"), date("j") - date("N") + 1, date("Y"));

        $criteria = new CDbCriteria();
        $criteria->compare('monday_timestamp', $monday);
        $items = CalendarWeek::model()->findAll($criteria);
        $yw = array();
        foreach($items as $item){
            $yw[$item->yid] = $item->weeknumber;
        }

        $criteria = new CDbCriteria();
        $criteria->compare('stat', IvyClass::STATS_OPEN);
        $criteria->order='schoolid';
        $items = IvyClass::model()->findAll($criteria);
        if($items){
            foreach($items as $item){
                $weeknumber = isset($yw[$item->yid]) ? $yw[$item->yid] : 0;

                $criteria = new CDbCriteria();
                $criteria->compare('type', NotesSchCla::NOTESSCHCLA_TYPE_CLASSNEW);
                $criteria->compare('classid', $item->classid);
                $criteria->compare('stat', 20);
                $criteria->compare('weeknumber', $weeknumber);
                $count = NotesSchCla::model()->count($criteria);
                if($count){
                    $criteria = new CDbCriteria();
                    $criteria->compare('t.classid', $item->classid);
                    $criteria->compare('t.weeknumber', $weeknumber);
                    $criteria->compare('t.stat', 20);
                    $model = NotesChild::model()->with('weekInfo')->findAll($criteria);

                    if($model){
                        foreach($model as $cnote){
                            $child = ChildProfileBasic::model()->findByPk($cnote->childid);

                            $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
                            if($child->fid){

//                                 if($item->schoolid == 'BJ_DS'){
//                                     查询是否绑定了微信帐号
                                     $crite = new CDbCriteria();
                                     $crite->compare('userid', $child->fid);
                                     $crite->compare('valid', 1);
                                     if(in_array($child->schoolid, array('BJ_DS', 'BJ_SLT'))) {
                                         $crite->compare('account', 'ds');
                                     }
                                     else {
                                         $crite->compare('account', 'ivy');
                                     }
//                                     $crite->compare('openid', 'oHBKPw', true);
                                     $wmodel = WechatUser::model()->findAll($crite);
                                     if ($wmodel) {
                                         foreach ($wmodel as $w) {
                                             $this->sendMessage($child->schoolid, $w->openid, $cnote);
                                         }
                                     }
//                                 }
                                $fmodel = User::model()->findByPk($child->fid);
                                if(strpos($fmodel->email, '@') != false){
                                    $mailer->AddAddress($fmodel->email);
                                }
                            }

                            if($child->mid){

//                                 if($item->schoolid == 'BJ_DS') {
//                                     查询是否绑定了微信帐号
                                     $crite = new CDbCriteria();
                                     $crite->compare('userid', $child->mid);
                                     $crite->compare('valid', 1);
                                    if(in_array($child->schoolid, array('BJ_DS', 'BJ_SLT'))) {
                                        $crite->compare('account', 'ds');
                                    }
                                    else {
                                        $crite->compare('account', 'ivy');
                                    }
//                                     $crite->compare('openid', 'oHBKPw', true);
                                     $wmodel = WechatUser::model()->findAll($crite);
                                     if ($wmodel) {
                                         foreach ($wmodel as $w) {
                                             $this->sendMessage($child->schoolid, $w->openid, $cnote);
                                         }
                                     }
//                                 }
                                $mmodel = User::model()->findByPk($child->mid);
                                if(strpos($mmodel->email, '@') != false){
                                    $mailer->AddAddress($mmodel->email);
                                }
                            }

                            $mailer->Subject = in_array($child->schoolid, array('BJ_DS', 'BJ_SLT')) ? '[DaystarOnline] Daystar Weekly Journal  启明星在线每周报告' : '[IvyOnline] Ivy Weekly Journal  艾毅在线每周报告';
                            $mailer->getCommandView('weeklyjournal', array(
                                'schoolid' => $child->schoolid,
                                'loginurl' => CommonUtils::isGradeSchool($child->classid) ? 'http://dse.ivyonline.cn' : 'http://dsk.ivyonline.cn',
                            ), in_array($child->schoolid, array('BJ_DS', 'BJ_SLT')) ? 'todsparent' : 'main');
                            $isProduction = defined("IS_PRODUCTION")? IS_PRODUCTION : false;
                            $mailer->iniMail($isProduction);
//                            $mailer->IsSMTP();
//                            $mailer->SMTPAuth=true;
//                            $mailer->Host = 'mail.ivygroup.org';
//                            $mailer->Username = '<EMAIL>';
//                            $mailer->Password = 'barney2006';
//                            $mailer->Sender = '<EMAIL>';
//                            $mailer->From = '<EMAIL>';
//                            $mailer->FromName = '<EMAIL>';
                            $mailer->ClearBCCs();
                            if ($mailer->Send()) {
                                $tos = $mailer->getToAddresses();
                                $log = $tos[0][0];
                                if(isset($tos[1]))
                                    $log .= ','.$tos[1][0];
                                Yii::log($log, CLogger::LEVEL_INFO, 'sendmail.weeklyjournal');
                            }
                            echo $child->childid."\n";
                        }
                    }
                }
            }
        }
    }

    public function actionDasboard()
    {
        Yii::import('common.models.calendar.*');
        $criteria = new CDbCriteria();
        $criteria->compare('status', 10);
        $criteria->compare('type', ">10");
        $branch = Branch::model()->findAll($criteria);

        foreach($branch as $item){

            // 获取周一的时间戳
            $nowtime = time();
            $dateN = date('N', $nowtime);

            $mondayTimestamps = $nowtime - ($dateN-1)*3600*24;
            $monday = date("Ymd" , $mondayTimestamps); // 周一
            $friday = date('Ymd', $mondayTimestamps + 345600); // 周五
            $month = array(substr($monday, 0, 6), substr($friday, 0, 6));
            // 根据学校ID 获取当前校历

            $criteria = new CDbCriteria();
            $criteria->compare('branchid', $item->branchid);
            $criteria->compare('is_selected', 1);
            $calenderModel = CalendarSchool::model()->find($criteria);

            $criteria = new CDbCriteria();
            $criteria->compare('yid', $calenderModel->yid);
            $criteria->compare('month', $month);
            $calendarSchoolDays = CalendarSchoolDays::model()->findAll($criteria);
            $days = array();

            foreach ($calendarSchoolDays as $schoolDay) {
                foreach (explode(',', $schoolDay->schoolday_array) as $schoolArray) {
                    $days[$schoolDay->month . $schoolArray] = $schoolDay->month . $schoolArray;
                }
            }
            $num = 0;
            foreach ($days as $val) {
                if($val >= $monday && $val <= $friday){
                    $num++;
                }
            }

            if($num >= 3){
                $lunch = $this->GenerateCampusLunch($item->branchid,$monday);
                $weeklyReport = $this->GenerateCampusReport($item->branchid,$monday);

                $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
                $mailer->AddAddress($item->info->support_email);
                /*$mailer->AddCC('<EMAIL>');*/
                $mailer->Subject = $item->branchid . ' Weekly Journal  ' . $item->branchid . '在线每周报告';
                $mailer->getCommandView('dasboard', array(
                    'lunch' => $lunch,
                    'weeklyReport' => $weeklyReport,
                    ), $item->type == Branch::PROGRAM_DAYSTAR ? 'todsparent' : 'main');
                $isProduction = defined("IS_PRODUCTION")? IS_PRODUCTION : false;
                $mailer->iniMail($isProduction);
                $mailer->ClearBCCs();
                if ($mailer->Send()) {
                    echo  $item->branchid . ' - Success' . "\n";
                }else{
                    echo $item->branchid . ' - Fail'  . "\n";
                }
            }else{
                echo $item->branchid . ' Less than 3 days in class' . "\n";
            }
        }
    }

    public function GenerateCampusLunch($schoolid = "", $specifiedDate = "")
    {
        $sTime = strtotime($specifiedDate);
        $monTime = $sTime - ((date('N', $sTime)-1)*86400);

        $item = Yii::app()->db->createCommand()
            ->select('*')
            ->from('ivy_catering_menu_weeklink')
            ->where("monday_timestamp = :monday and schoolid = :schoolid", array(':monday'=>$monTime, ':schoolid'=>$schoolid))
            ->queryRow();

        $result = array();
        $result['type'] = 'lunch';
        $result['general_lunch'] = $item['menu_id'] ? 1 : 0;
        $result['special_lunch'] = $item['allergy_id'] ? 1 : 0;
        return $result;
    }


    public function GenerateCampusReport($schoolId = "", $specifiedDate = "")
    {
        Yii::import('common.models.portfolio.*');
        Yii::import('common.models.calendar.*');
        Yii::import('common.models.schedule.*');
        Yii::import('common.models.child.StatsChildCount');

        $sTime = strtotime($specifiedDate);
        $monTime = $sTime - ((date('N', $sTime)-1)*86400);

        $criteria = new CDbCriteria();
        $criteria->compare('branchid', $schoolId);
        $items = CalendarSchool::model()->findAll($criteria);
        $yids = array();
        foreach($items as $item){
            $yids[$item->yid] = $item->yid;
        }

        $citems = IvyClass::model()->findAllByAttributes(array('schoolid'=>$schoolId, 'stat'=>10));
        foreach($citems as $citem){
            $result['classtitle'][$citem->classid]=$citem->title;
        }

        $criteria = new CDbCriteria();
        $criteria->compare('monday_timestamp', $monTime);
        $criteria->compare('yid', $yids);
        $model = CalendarWeek::model()->find($criteria);

        $criteria = new CDbCriteria();
        $criteria->compare('type', 10);
        $criteria->compare('stat', 20);
        $criteria->compare('yid', $model->yid);
        $criteria->compare('weeknumber', $model->weeknumber);
        $criteria->compare('classid', 0);
        $criteria->compare('schoolid', $schoolId);
        $count = NotesSchCla::model()->count($criteria);
        if($count)
            $result['schoolreport'] = 1;
        else
            $result['schoolreport'] = 0;

        $criteria = new CDbCriteria();
        $criteria->compare('t.type', 20);
        $criteria->compare('t.stat', 20);
        $criteria->compare('t.yid', $model->yid);
        $criteria->compare('t.weeknumber', $model->weeknumber);
        $criteria->compare('classTitle.schoolid', $schoolId);
        $criteria->order = 'classTitle.child_age asc, classTitle.classid asc';
        $items = NotesSchCla::model()->with('classTitle')->findAll($criteria);
        foreach($items as $item){
            $result['classreport'][$item->classid]=1;
//                $result['classtitle'][$item->classid]=$item->classTitle->title;
        }

        $criteria = new CDbCriteria();
        $criteria->compare('t.yid', $model->yid);
        $criteria->compare('t.weeknumber', $model->weeknumber);
        $criteria->compare('classTitle.schoolid', $schoolId);
        $items = ClassScheduleV2::model()->with('classTitle', 'data')->findAll($criteria);
        foreach($items as $item){
            if(strlen($item->data->data) > 3000){
                $result['schedule'][$item->classid]=1;
            }
            else{
                $result['schedule'][$item->classid]=0;
            }
        }

        $criteria = new CDbCriteria();
        $criteria->compare('t.yid', $model->yid);
        $criteria->compare('t.weeknumber', $model->weeknumber);
        $criteria->compare('t.stat', 20);
        $criteria->compare('cInfo.schoolid', $schoolId);
        $items = NotesChild::model()->with('cInfo')->findAll($criteria);
        foreach($items as $item){
            $result['childreport'][$item->classid][$item->childid] = $item->childid;
            if($item->invalid)
                $result['childreport_invalid'][$item->classid][$item->childid] = $item->childid;
        }

        $criteria = new CDbCriteria();
        $criteria->compare('schoolid', $schoolId);
        $criteria->compare('classid', "<>0");
        $criteria->compare('period_timestamp', strtotime($specifiedDate));
        $items = StatsChildCount::model()->findAll($criteria);
        foreach($items as $item){
            $result['countchild'][$item->classid] = $item->num1+$item->num2;
        }
        $result['type'] = 'teaching';
        $result['oaUrl'] = Yii::app()->urlManager->baseUrl;
        $result['yid'] = $model->yid;
        $result['weeknum'] = $model->weeknumber;

        return $result;
    }

    public function actionTest()
    {
        $this->getAccessToken();
        Yii::import('common.models.calendar.CalendarWeek');
        Yii::import('common.models.portfolio.NotesChild');
        Yii::import('common.models.child.HomeAddress');

        //$note = NotesChild::model()->with('weekInfo')->findByPk(704141);
        $this->sendMessageData('BJ_DS', 'oHBKPwQUsreFHcZkzsq0CdW7VqEQ', 1);
    }

    // 获取access_token
    private function getAccessToken()
    {
        $wechatKey = 'ivy';
        $token = md5(CommonUtils::SECURITY_KEY);
        $data = array(
            "token" => $token,
            "wechatKey" => $wechatKey,
            "authKey" => md5(sprintf("%s%s", $wechatKey, CommonUtils::SECURITY_KEY))
        );
        $url = "http://apps.ivyonline.cn/webapi/wechat/getAccessToken";
        $res = CJSON::decode($this->httpGet($url, $data));
        $this->ivyAccessToken = $res['accessToken'];

        $wechatKey = 'ds';
        $token = md5(CommonUtils::SECURITY_KEY);
        $data = array(
            "token" => $token,
            "wechatKey" => $wechatKey,
            "authKey" => md5(sprintf("%s%s", $wechatKey, CommonUtils::SECURITY_KEY))
        );
        $url = "http://apps.ivyonline.cn/webapi/wechat/getAccessToken";
        $res = CJSON::decode($this->httpGet($url, $data));
        $this->dsAccessToken = $res['accessToken'];
    }

    // 发送模板消息
    private function sendMessage($schoolid, $openid, $note)
    {

        // 微信模板消息内容
        if (in_array($schoolid, array('BJ_DS', 'BJ_SLT'))) {
            $url = 'https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=' . $this->dsAccessToken;
            $appid = 'wxb1a42b81111e29f3';
            $state = 'ds';
            $template_id = 'iwYT6iZbSeSoE1Z8IK-ZxOir0hh2iGKOySsvEZeLHnw';
        } else {
            $url = 'https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=' . $this->ivyAccessToken;
            $appid = 'wx903fba9d4709cf10';
            $state = 'ivy';
            $template_id = 'x_a4DB2q4j6CM5oNb0hLc0eozEGrgtR2jMjTpLtK640';
        }
        $redirectUrl = "http://www.ivyonline.cn/wechat/user/article/childid/$note->childid/classid/$note->classid/yid/$note->yid/weeknum/$note->weeknumber/startyear/$note->startyear";
        $date = date('Y/m/d', $note->weekInfo->monday_timestamp) .' - '. date('Y/m/d', ($note->weekInfo->monday_timestamp+345600));
        $data = array(
            'touser' => $openid,
            'template_id' => $template_id,
            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid='.$appid.'&redirect_uri='.$redirectUrl.'&response_type=code&scope=snsapi_base&state='.$state.'#wechat_redirect',
            'data' => array(
                'first' => array('value' => '尊敬的家长，您孩子的学习报告已经上线'),
                'keyword1' => array('value' => $note->childInfo->getChildName(false, true)),
                'keyword2' => array('value' => $date),
                'remark' => array('value'=>'请点击“详情”查看报告完整信息'),
            ),
        );
        $jsonData = json_encode($data);
        $res = $this->httpGet($url, $jsonData);
        $jsonRes = CJSON::decode($res);
        Yii::log($openid.'=>'.$jsonRes['errcode'], CLogger::LEVEL_INFO, 'sendmail.weeklyjournal');
        if( in_array($jsonRes['errcode'], array(42001,40001))){
//            echo 'eeeeeee';
            $this->getAccessToken();
//            $this->sendMessage($schoolid, $openid, $note);
        }
    }

    // 给预约过得家长发通知 ptc
    public function actionReserve(){
        Yii::import('common.models.ptc.*');
        Yii::import('common.models.wechat.*');
        $tomorrow = date("Ymd",strtotime("+1 day"));

        $criteria = new CDbCriteria();
        $criteria->compare('t.target_date', $tomorrow);
        $criteria->compare('t.childid', ">0");
        $criteria->compare('plan.semester', array(1,2));
        $criteria->with = 'plan';
        $model = ParentMeetingItem::model()->findAll($criteria);

        if($model) {
            $this->getAccessToken();
            foreach ($model as $val) {
                $childModel = ChildProfileBasic::model()->findByPk($val->childid);
                $branchInfo = BranchInfo::model()->findByPk($childModel->schoolid);
                $parent = array();
                if($childModel->fid){
                    $parent['fid'] = $childModel->fid;
                }
                if($childModel->mid){
                    $parent['mid'] = $childModel->mid;
                }

                $criteria = new CDbCriteria();
                $criteria->compare('t.userid', $parent);
                $criteria->compare('t.valid', 1);
                if(in_array($childModel->schoolid, array('BJ_DS','BJ_SLT'))){
                    $criteria->compare('account', 'ds');
                    $school = $branchInfo->title_cn . $branchInfo->title_en;
                }else{
                    $criteria->compare('account', 'ivy');
                    $school = $childModel->school->title;
                }
                $wechatModel = WechatUser::model()->findAll($criteria);

                if($wechatModel){
                    $times = explode(',' , $val->timeslot);
                    $day = date("Y-m-d", strtotime($val->target_date));

                    $data = array(
                        'childName' => $childModel->getChildName(true,true,true),
                        'time' => $day . ' ' . $times[0],
                        'school' => $school,
                        'class' => $childModel->ivyclass->title,
                    );

                    foreach ($wechatModel as $item){
                        $this->sendMessageData($childModel->childid,$childModel->schoolid, $item->openid,$data);
                        //$this->SendMessageData($childModel->schoolid, $item->openid,$data);
                    }
                }
            }
        }
    }

    // 发送模板消息 新
    private function sendMessageData($childid,$schoolid, $openid, $reserveData)
    {
        // 微信模板消息内容
        if (in_array($schoolid, array('BJ_DS','BJ_SLT'))) {
            $url = 'https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=' . $this->dsAccessToken;
            $appid = 'wxb1a42b81111e29f3';
            $state = 'ds';
            $template_id = 'l_A1EyhJpDFaHiTn8NpTzGaguyqTTdtiBqAZmfOdc4I';
            $ceshiOpenid = 'oHBKPwQUsreFHcZkzsq0CdW7VqEQ';
            //$first = '您的家长会议将于明天举行，请准时参加。 Your conference is tomorrow. Please arrive on time.';
            $first = '您的家长会将于明天举行，请准时登陆Zoom会议。Your conference is tomorrow. Please login to zoom on time.';
            $remark = '期待明天与您见面！We look forward to seeing you tomorrow';
            $keyword3 = 'Daystar (Beigao)';
        } else {
            $url = 'https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=' . $this->ivyAccessToken;
            $appid = 'wx903fba9d4709cf10';
            $state = 'ivy';
            $template_id = 'FxXYyxYBxoQXqXhK51M4golMEg7h-Hvl5QhMAiSiU5Y';
            $ceshiOpenid = 'ouwmTjiT6aIh_TLd6Kldr2Cx4tCs';
            $first = '家长您好，您预约的家长会将于明天开始，预计进行30分钟。';
            $remark = '请您准时参加，如有变化请联系班级老师';
            $keyword3 = $reserveData['school'];
        }

        $redirectUrl = "http://www.ivyonline.cn/wechat/reserve/index";

        $data = array(
            'touser' => $openid,
            'template_id' => $template_id,
            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid='.$appid.'&redirect_uri='.$redirectUrl.'&response_type=code&scope=snsapi_base&state='.$state.'#wechat_redirect',
            'data' => array(
                'first' => array('value' => $first, 'color' => '#ff6726'),
                'keyword1' => array('value' => $reserveData['childName']), // 学生姓名
                'keyword2' => array('value' => $reserveData['time']), // 预约时间
                'keyword3' => array('value' => $keyword3),//校区
                'keyword4' => array('value' => $reserveData['class']),//班级
                'remark' => array('value'=> $remark),
            ),
        );
        $jsonData = json_encode($data);
        $res = $this->httpGet($url, $jsonData);
        $jsonRes = CJSON::decode($res);
        Yii::log($state.'=>'.$childid.'=>'.$openid.'=>'.$jsonRes['errcode'], CLogger::LEVEL_INFO, 'wechatptc.server');
        if($jsonRes['errcode'] == 42001){
            $this->getAccessToken();
            //$this->sendMessageData($schoolid, $openid, $reserveData);
        }
    }

    // 发起 curl 请求
    private function httpGet($url, $data = '') {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_TIMEOUT, 500);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        if ($data) {
            curl_setopt($curl, CURLOPT_POST, 1);
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        }
        curl_setopt($curl, CURLOPT_URL, $url);

        $res = curl_exec($curl);
        curl_close($curl);

        return $res;
    }

    public function actionNotSignedMail()
    {
        Yii::app()->language = 'zh_cn';
        $model = new Branch();
        $schoolList = $model->getBranchList(null,true);

        Yii::import('common.models.calendar.*');
        Yii::import('common.models.child.*');
        Yii::import('common.models.attendance.*');

        foreach ($schoolList as $school => $val){
            echo $school . '：';
            // 暂时跳过启明星
            if($val['type'] == 50 || !in_array($val['abb'], array('EC', 'ES', 'EB', 'HH', 'SQ', 'LT', 'GJ', 'YJ', 'OE', 'OG-PH', 'CP', 'IASLT'))){
                echo '跳过' . "\n";
                continue;
            }
            $childData = array();
            // 所有开放的班级
            $crit = new CDbCriteria();
            $crit->compare('branchid', $school);
            $crit->compare('is_selected', 1);
            $calendars = CalendarSchool::model()->find($crit);
            $is_class = $this->getClassList($school, $calendars->yid);

            $time = time();
            $dayTime = strtotime(date('Y-m-d', $time));

            // 如果是非上学日，不运行
            $month = date('Ym', $time);
            $day = date('d', $time);
            $crit = new CDbCriteria();
            $crit->compare('yid', $calendars->yid);
            $crit->compare('month', $month);
            $schoolDays = CalendarSchoolDays::model()->find($crit);
            if (!$schoolDays) {
                echo '未找到教学日' . "\n";
                continue;
            }

            $dayArr = explode(',', $schoolDays->schoolday_array);
            if (!in_array($day, $dayArr)) {
                echo '不在教学日内' . "\n";
                continue;
            }

            if($is_class) {
                foreach ($is_class as $classid) {
                    // 根据学校和班级，孩子状态来查询出所有的孩子
                    $criteria = new CDbCriteria();
                    $criteria->compare('t.status', array(ChildProfileBasic::STATS_ACTIVE_WAITING, ChildProfileBasic::STATS_ACTIVE));
                    $criteria->compare('t.schoolid', $school);
                    $criteria->compare('t.classid', $classid);
                    $criteria->index = 'childid';
                    $childModel = ChildProfileBasic::model()->findAll($criteria);

                    if ($childModel) {
                        $childDatas = array();
                        foreach($childModel as $child){
                            if ($child->enter_date != 0 ) {
                                if ($child->enter_date > time()) {
                                    continue;
                                }
                            } else {
                                if ($child->est_enter_date > time()) {
                                    continue;
                                }
                            }
                            $childDatas[$child->childid] = array(
                                'id'=>$child->childid,
                                'name'=>$child->getChildName(false, true),
                                'class'=> $child->ivyclass->title,
                            );
                        }
                        // 查找签到数据
                        $childids = array_keys($childDatas);
                        $criteriaSign = new CDbCriteria();
                        $criteriaSign->compare('t.sign_timestamp', $dayTime);
                        $criteriaSign->compare('t.childid', $childids);
                        $criteriaSign->index = 'childid';
                        $signs = ChildDailySign::model()->findAll($criteriaSign);
                        // 查找请假数据
                        $criteriaVacation = new CDbCriteria();
                        $criteriaVacation->compare('t.child_id', $childids);
                        $criteriaVacation->compare('t.class_id', $classid);
                        $criteriaVacation->compare('t.vacation_time_start', "<=$dayTime");
                        $criteriaVacation->compare('t.vacation_time_end', ">=$dayTime");
                        $criteriaVacation->compare('t.type', array(10, 20));
                        $criteriaVacation->compare('t.stat', ChildVacation::STATUS_CHECKED);
                        $criteriaVacation->index = 'child_id';
                        $vacations = ChildVacation::model()->findAll($criteriaVacation);

                        $criteriaVacation = new CDbCriteria();
                        $criteriaVacation->compare('t.child_id', $childids);
                        $criteriaVacation->compare('t.class_id', $classid);
                        $criteriaVacation->compare('t.vacation_time_start', $dayTime);
                        $criteriaVacation->compare('t.vacation_time_end', $dayTime);
                        $criteriaVacation->compare('t.type', 40);
                        $criteriaVacation->compare('t.stat', ChildVacation::STATUS_CHECKED);
                        $criteriaVacation->index = 'child_id';
                        $vacationCount = ChildVacation::model()->findAll($criteriaVacation);

                        foreach ($childDatas as $childid=>$child) {
                            if(!isset($signs[$childid]) && !isset($vacations[$childid])){
                                $childData['key_' . $classid]['title'] = $child['class'];
                                if(!isset($vacationCount[$childid])){
                                    $childData['key_' . $classid]['nocd'][] = $child['name'];
                                }else{
                                    $childData['key_' . $classid]['cd'][] = $child['name'];
                                }
                            }
                        }
                    }
                }
            }

            if (count($childData) == 0) {
                echo '全部签到' . "\n";
                continue;
            }

            $branchModel = BranchInfo::model()->findByPk($school);
            $abb = $schoolList[$school]['abb'];
            $sub = '[' .$abb. '] ';
            $date = date('Y-m-d H:i', $time);
            $subject = ' 截止 ' .$date. ' 未签到名单';
            $main = 'main';

            if($val['type'] == 50){
                $main = 'todsparent';
                $sub = '[' .$abb. '] ';
                $subject = ' 截止 ' .$date. ' 未签到名单';
            }
            $title = $sub . $subject ;
            $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
            $mailer->Subject = $title;

            $mailer->AddAddress($branchModel->support_email);
            $mailer->AddCC('<EMAIL>');
            $mailer->AddCC('<EMAIL>');
            $mailer->AddBCC('<EMAIL>');
            $mailer->getCommandView('notSignedMail', array('branchId' => $school, 'title' => $title, 'childData'=> $childData), $main);
            /* if($this->branchId != 'BJ_TYG'){
                $mailer->AddCC($this->branchObj->info->support_email);
            }*/
            $isProduction = defined("IS_PRODUCTION")? IS_PRODUCTION : false;
            $mailer->iniMail($isProduction); // 此行代码要放到AddAddress, AddCC，getView法下面
            $status = 'fail';
            try {
                if ($mailer->Send()) {
                    $status = 'success';
                }
            } catch (phpmailerException $e) {
                $error = $e->getMessage();
                echo $error . ', ';
            }
            Yii::log($school .', '. $status, CLogger::LEVEL_INFO, 'sendmail.NotSignedMail');
            echo $status . "\n";
        }
    }

    // 获取当前学校所有开放的班级
    public function getClassList($branchId, $yid){
        $crit = new CDbCriteria;
        $crit->compare('t.schoolid', $branchId);
        $crit->compare('t.stat', IvyClass::STATS_OPEN);
        $crit->compare('t.yid', $yid);
        $crit->order = 't.child_age ASC, t.title ASC';
        $_classes = IvyClass::model()->findAll($crit);
        $classList = array();
        if($_classes){
            foreach ($_classes as $val){
                $classList[] = $val->classid;
            }
        }

        return $classList;
    }


    // 给预约过得家长发通知 wida
    public function actionReserveWida(){
        Yii::import('common.models.ptc.*');
        Yii::import('common.models.wechat.*');
        Yii::import('common.models.invoice.*');
        $tomorrow = date("Ymd",strtotime("+1 day"));

        $criteria = new CDbCriteria();
        $criteria->compare('t.target_date', $tomorrow);
        $criteria->compare('t.childid', ">0");
        $criteria->compare('plan.semester', 100);
        $criteria->with = 'plan';
        $model = ParentMeetingItem::model()->findAll($criteria);

        if($model) {
            $this->getAccessToken();
            $classtype = IvyClass::getClassTypes(true,50);

            foreach ($model as $val) {
                $childModel = ChildProfileBasic::model()->findByPk($val->childid);
                $branchInfo = Branch::model()->findByPk($val->plan->school_id);

                $childClassModel = IvyClass::model()->findByPk($childModel->nextYear->classid);

                $parent = array();
                if($childModel->fid){
                    $parent['fid'] = $childModel->fid;
                }
                if($childModel->mid){
                    $parent['mid'] = $childModel->mid;
                }

                $criteria = new CDbCriteria();
                $criteria->compare('t.userid', $parent);
                $criteria->compare('t.valid', 1);
                $criteria->compare('account', 'ds');


                $wechatModel = WechatUser::model()->findAll($criteria);

                if($wechatModel){
                    $times = explode(',' , $val->timeslot);
                    $day = date("Y-m-d", strtotime($val->target_date));
                    $TitleInfo = json_decode($val->plan->extra,true);
                    $data = array(
                        'childName' => $childModel->getChildName(true,true,true),
                        'time' => $day . ' ' . $times[0],
                        'school' => $branchInfo->title,
                        'class' => $classtype[$childClassModel->classtype],
                        'memo' => $TitleInfo['title_cn'],
                    );

                    foreach ($wechatModel as $item){
                        $this->sendMessageDataWida($val->childid,$val->plan->school_id, $item->openid,$data);
                        //$this->SendMessageData($childModel->schoolid, $item->openid,$data);
                    }
                }
            }
        }
    }

    // 发送模板消息 新wida
    private function sendMessageDataWida($childid,$schoolid, $openid, $reserveData)
    {
        // 微信模板消息内容
        $url = 'https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=' . $this->dsAccessToken;
        $appid = 'wxb1a42b81111e29f3';
        $state = 'ds';
        $template_id = 'l_A1EyhJpDFaHiTn8NpTzGaguyqTTdtiBqAZmfOdc4I';
        $ceshiOpenid = 'oHBKPwQUsreFHcZkzsq0CdW7VqEQ';
        /*$first = '您预约的WIDA测试将于明天进行，请准时参加。Your scheduled WIDA test is tomorrow. Please be on time.';
        $remark = '期待明天与您见面！We look forward to seeing you tomorrow!';*/
        //$first = '您的预约信息如下。Your appointment is stated below:';
        $first = '您的家长会将于明天举行，请准时登陆Zoom会议。Your conference is tomorrow. Please login to zoom on time.';
        $remark = '期待与您见面！We look forward to seeing you!';
        $keyword3 = 'Daystar (Beigao)';


        $redirectUrl = "http://www.ivyonline.cn/wechat/wida/index";
        //$touser  = (OA::isProduction()) ? $openid  : $ceshiOpenid;

        $data = array(
            'touser' => $openid,
            'template_id' => $template_id,
            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid='.$appid.'&redirect_uri='.$redirectUrl.'&response_type=code&scope=snsapi_base&state='.$state.'#wechat_redirect',
            'data' => array(
                'first' => array('value' => $first, 'color' => '#ff6726'),
                'keyword1' => array('value' => $reserveData['childName']), // 学生姓名
                'keyword2' => array('value' => $reserveData['time']), // 预约时间
                'keyword3' => array('value' => $reserveData['school']),//校区
                //'keyword4' => array('value' => '待定 To Be Determined'),//班级
                'keyword4' => array('value' => $reserveData['class']),//班级
                'remark' => array('value' => trim($reserveData['memo'])),
                //'remark' => array('value'=> $remark),
            ),
        );
        $jsonData = json_encode($data);
        $res = $this->httpGet($url, $jsonData);
        $jsonRes = CJSON::decode($res);
        Yii::log($state.'=>'.$childid.'=>'.$openid.'=>'.$jsonRes['errcode'], CLogger::LEVEL_INFO, 'wechatptc.server');
        if($jsonRes['errcode'] == 42001){
            $this->getAccessToken();
            //$this->sendMessageData($schoolid, $openid, $reserveData);
        }
    }

    /*
     *
     * 查上一周的转校学生名单发送邮件
     *
     */
    public function actionTransferEmail()
    {
        Yii::import('common.models.child.TransferSchoolHistory');
        /*$beginLastweek = mktime(0,0,0,date('m'),date('d')-date('w')+1-7,date('Y'));
        $endLastweek = mktime(23,59,59,date('m'),date('d')-date('w')+7-7,date('Y'));*/
        $beginLastweek = strtotime('-1 week last monday');
        $endLastweek = strtotime('last sunday')+24*60*60-1;

        $criteria = new CDbCriteria();
        $criteria->compare('t.transfer_time', ">=$beginLastweek");
        $criteria->compare('t.transfer_time', "<=$endLastweek");
        $criteria->compare('t.status', array(1,2));
        $wechatModel = TransferSchoolHistory::model()->findAll($criteria);
        Yii::app()->language = 'zh_cn';

        if($wechatModel){
            foreach ($wechatModel as $value){
                $schoolList[$value->to_schoolid] = $value->to_schoolid;
                $schoolList[$value->from_schoolid] = $value->from_schoolid;
                $transferInfo[$value->to_schoolid][] =  array(
                    'childid' => $value->childid,
                    'from_schoolid' => $value->from_schoolid,
                    'to_schoolid' => $value->to_schoolid,
                    'transfer_time' => date("Y-m-d", $value->transfer_time),
                    'balance' => $value->balance,
                    'childName' => $value->child->getChildName(),
                );
            }

            $criter = new CDbCriteria();
            $criter->compare('branchid', $schoolList);
            $criter->index = 'branchid';
            $sModel = Branch::model()->findAll($criter);

            foreach($sModel as $k =>$school){
                if($transferInfo[$school->branchid]) {
                    $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
                    $mailer->Subject = $school->title . ": 孩子转到本校名单";
                    $mailer->AddAddress($school->info->email);
                    $mailer->AddCC('<EMAIL>'); //抄送
                    $mailer->getCommandView('transferChild', array(
                        'data' => $transferInfo[$school->branchid],
                        'sModel' => $sModel,
                        'schoolTitle' => $school->title,
                        'beginLastweek' => $beginLastweek,
                        'endLastweek' => $endLastweek,
                    ), $school->type == 50 ? 'todsparent' : 'main');
                    $isProduction = defined("IS_PRODUCTION") ? IS_PRODUCTION : false;
                    $mailer->iniMail($isProduction);
                    if($mailer->Send()){
                        echo $k . ' - success' ."\n";
                    }else{
                        echo $k . 'fail' ."\n";
                    }
                }
            }
        }
    }
}
