<?php

class UserController extends Controller
{
	protected $securityKey = 'A23sW343eL8934ov2332E';
	protected $expireTime = 7200; //2 hours

	public $wechatUser = null;
	public $isBound = 0;
	public $myChildObjs = array();
	public $activeChildIds = array();

	public function init(){
		Yii::app()->theme = 'mobile';
		$this->layout = '//layouts/main';
		$this->siteTitle = Mims::unIvy() ? Yii::app()->params['ownerConfig']['name_cn'] : Yii::t("global","IvyOnline");
	}
	public function filters()
    {
        return array(
            'accessControl'
        );
    }
	public function accessRules(){
		return array(
			array('allow',
				'actions'=>array('test'),
				'ips'=>array('***********')
			)
		);
	}
		
	public function actionIndex()
	{
		$this->subPageTitle = "绑定系统帐号";
		Yii::import('application.modules.user.components.*');
		$status = 0;
		$checkCode=Yii::app()->request->getParam("mk", null);
		$wechatName= Yii::app()->request->getParam("md", null);
		
		$weChatUserObj = WechatUser::model()->findByPk($wechatName);
		if(!is_null($weChatUserObj) && $weChatUserObj->valid){
			$status = 2;
			$this->render('index', array('status'=>$status));
			Yii::app()->end();
		}
		
		$model = new UserBindForm;
		$model->wechatUname = $wechatName;
		
		if(!empty($checkCode) && !empty($wechatName)){
			$toCheckCode = md5(substr($wechatName, 3, 3) . $this->securityKey);
			if($checkCode == $toCheckCode){
				$status = 1;
				if(isset($_POST['UserBindForm'])) {
					$model->attributes = $_POST['UserBindForm'];
					if($model->validate()){
						//$weChatUserObj = WechatUser::model()->findByPk($model->wechatUname);
						if(is_null($weChatUserObj)){
							$weChatUserObj = new WechatUser;
						}
						$weChatUserObj->openid = $model->wechatUname;
						$weChatUserObj->userid = $model->userid;
						$weChatUserObj->valid = 1;
						$weChatUserObj->updated = time();
						if(!$weChatUserObj->save()){
							print_r($weChatUserObj->getErrors());
						}else{
							$status = 2;
						}
						
					}
				}				
			}
		}
		
		//测试专用
		//$status = 1;
		$this->render('index', array('status'=>$status, 'model'=>$model));
	}
	
	public function actionJournal($data,$t,$o,$code){
		$codetoCheck = md5($this->securityKey . $t . $data . $o );
		if($codetoCheck == $code){
			if( 1 || ( (time() - $t) <= $this->expireTime ) ){
				$noteid = $data;
				if(intval($noteid)){
					Yii::import('common.models.portfolio.*');
					Yii::import('common.models.calendar.*');
					Yii::import('common.models.classTeacher.*');
					$noteObj = NotesChild::model()->with(array('cInfo','weekInfo'))->findByPk($noteid);
					$wechatUser = WechatUser::model()->findByPk($o);
					$parent = IvyParent::model()->findByPk($wechatUser->userid);
					$cids = @unserialize($parent->childs);
					if(in_array($noteObj->childid, $cids)){
						$this->subPageTitle = sprintf("周报告 第%s周", $noteObj->weeknumber);
						$child = ChildProfileBasic::model()->findByPk($noteObj->childid);
						
						$crit = new CDbCriteria;
						//$crit->compare('schoolid', $noteObj->cInfo->schoolid);
						//$crit->compare('classid', array(0, $noteObj->classid));
						//
						//$crit->compare('yid', $noteObj->yid);
						//$crit->compare('weeknumber', $noteObj->weeknumber);
						//$crit->compare('stat', 20);
						
						$crit->condition = '( yid=:yid AND weeknumber=:weeknumber AND `stat`=:stat) AND (
						(classid=:classid) OR (schoolid=:schoolid AND classid = 0 ) )';
						$crit->params =	array(
								':yid' => $noteObj->yid,
								':weeknumber' => $noteObj->weeknumber,
								':stat' => 20,
								':classid' => $noteObj->classid,
								':schoolid' => $noteObj->cInfo->schoolid
						);
						
						$crit->index = 'type';
						$campusClassRpts = NotesSchCla::model()->findAll($crit);

						$photos = array();
						
						$showSpecialReport = false;
						$childReportPhotos = array();

						if($noteObj->startyear){
							ChildMedia::setStartYear($noteObj->startyear);
							ChildMediaLinks::setStartYear($noteObj->startyear);
							
							$criteria = new CDbCriteria();
							$criteria->compare('t.childid', array($noteObj->childid,0));
							$criteria->compare('t.classid', $noteObj->classid);
							//$criteria->compare('t.category', 'week'); 实际上有week和snotes两种类型；后者用于IA的个人报告
							$criteria->compare('t.yid', $noteObj->yid);
							$criteria->compare('t.weeknum', $noteObj->weeknumber);
							$criteria->order = 't.weight ASC, t.id asc';
							$photos = ChildMediaLinks::model()->with('photoInfo')->findAll($criteria);
							
							foreach($photos as $index=>$photo){
								if($photo->category == 'snotes'){
									$childReportPhotos[$index] = $photo;
									unset($photos[$index]);
								}
							}
							
							if(count($childReportPhotos)){
								$noteSpecialObj = NotesSpecial::model()->findByAttributes(
									array(
										'classid' => $noteObj->classid,
										'childid' => $noteObj->childid,
										'weeknum' => $noteObj->weeknumber,
										'stat' => 20
									)
								);
								if($noteSpecialObj){
									$showSpecialReport = true;
								}
							}
						}
						
						$crit = new CDbCriteria;
						$crit->compare('classid', $noteObj->classid);
						$crit->order ='weight ASC';
						$classTeachers = ClassTeacher::model()->with(array('userWithProfile','staffInfo'))->findAll($crit);
						
						$this->render('journal', array(
							'child'=>$child,
							'noteObj'=>$noteObj,
							'campusClassRpts'=>$campusClassRpts,
							'photos'=>$photos,
							'teachers'=>$classTeachers,
							'showSpecialReport' => $showSpecialReport,
							'childReportPhotos' => $childReportPhotos
						));
						Yii::app()->end();
					}
				}
			}
		}
		$this->render('linkexpired');
	}
	
	
	
	public function actionLunchMenu($data, $t, $o, $code){
		$codetoCheck = md5($this->securityKey . $t . $data . $o );
		if($codetoCheck == $code){
				$menuLinkId = $data;
				if(intval($menuLinkId)){
					Yii::import('common.models.lunchmenu.*');
					Yii::import('common.models.calendar.Calendar');
					Yii::import('common.models.child.ChildMisc');
					
					$menuLink = CateringMenuWeeklink::model()->with(array('branch','calendar'))->findByPk($menuLinkId);
					
					if($menuLink->menu_id) $menuIds[$menuLink->menu_id] = $menuLink->menu_id;
					if($menuLink->allergy_id) $menuIds[$menuLink->allergy_id] = $menuLink->allergy_id;
					
					$crit = new CDbCriteria;
					$crit->compare('t.id', $menuIds);
					$crit->index = 'id';
					
					$menus = CateringMenu::model()->with('dailyMenus')->findAll($crit);
					
					foreach($menus as $menu){
					
						//$weekDays = array('mon', 'tue', 'wed', 'thu', 'fri');
						//$menuCat = unserialize($menu->menu_cate);
					
						foreach($menu->dailyMenus as $menuItem){
							$result[$menu->id][$menuItem->weekday][$menuItem->category] = array(
								'title' => $menuItem->termInfo->cntitle,
								'content' => $menuItem->food_list,
                                'photo' => $menuItem->photo
							);
						}
					}
					
					$weekDays = array(
						'mon' => '星期一',
						'tue' => '星期二',
						'wed' => '星期三',
						'thu' => '星期四',
						'fri' => '星期五'
					);
					
					$this->render('lunchmenu', array(
						'result'=>$result,
						'menus'=>$menus,
						'menuLink'=>$menuLink,
						'weekDays'=>$weekDays,
					));
					Yii::app()->end();
				}
		}
		$this->render('linkexpired');	
	}

    public function actionTest2() {
        echo "Test";
//        echo urlencode('http://www.ivyonline.cn/weixin/user/test2');
        echo "<hr>";
        echo $_REQUEST['code'];
        echo "<hr>";
        echo $_REQUEST['state'];
    }

    public function actionTest3(){
        echo 'a';
    }

	public function actionTest($date=20121001){
	
		//$data = array('touser'=>'ouwmTjrjwe6XzIk3ppPQ_KzmM4nk', 'content'=>'scan789');
		
		//print_r(MimsAPI::getChildInfo(7001437));
		//print_r(MimsAPI::getChildInfo(8005531));
		//$ch = curl_init();
		//$time = time();
		//$command = 'doCustomSendText';
		//$postData = array(
		//	"postTime" => $time,
		//	"command" => $command,
		//	"data" => base64_encode(serialize($data)),
		//	"postKey" => md5(sprintf("%s&%s&%s", $time, $command,  $this->securityKey))
		//);
		//curl_setopt($ch, CURLOPT_URL,  Yii::app()->createUrl("/weixin/remoteCall/doCustomSendText")); 
		//curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
		////curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
		//curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
		//curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		//$return = curl_exec($ch);
		//print_r($return);
		//curl_close($ch);		
		
		
//		die;
//		if(Mims::isProduction()){
//			echo 'Yes';
//		}else{
//			echo "No";
//		}
//		die;
//        $ch = curl_init();
//		$time = time();
//		$command = 'doGroup';
//        $data = array(
//            "grant_type" => "client_credential",
//            "postTime" => $time,
//			"command" => $command,
//			"params" => base64_encode(serialize(array(
//				'openid' => 'ouwmTjrjwe6XzIk3ppPQ_KzmM4nk',
//				'groupKey' => 'OE',
//			))),
//            "postKey" => md5(sprintf("%s&%s&%s", $time, $command, $this->securityKey))
//            );
//        curl_setopt($ch, CURLOPT_URL, "http://www.ivynew.cn/weixin/remoteCall/doGroup"); 
//        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
//        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
//        if(!curl_exec($ch)){
//            print_r(curl_error($ch));
//        }
//        curl_close($ch);
	
	
        //$ch = curl_init();
        //$data = array(
        //    "grant_type" => "client_credential",
        //    "appid" => $this->AppId,
        //    "secret" => $this->AppSecret
        //    );
        //curl_setopt($ch, CURLOPT_URL,"https://api.weixin.qq.com/cgi-bin/token"); 
        //curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        //curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        //if(!curl_exec($ch)){
        //    print_r(curl_error($ch));
        //}
        //curl_close($ch);
		
		//print_r(MimsAPI::getBalance(array(5151,3739,4043,5342)));
		//print_r(MimsAPI::getInvoices(array(5151,3739,4043,5342), 'SPT_UNPAID'));
		//echo '<hr>';
		//print_r(MimsAPI::getInvoices(array(5151,3739,4043,5342), 'SPT_RECENT_PAID'));
		
		//$text = 'qxyc#1#20130902';
		////$text = 'QXYC#2#大幅度释放的方式的电风扇电风扇的';
		//$text = strtolower($text);
		//$pattern = '/^([a-z]{4})#(.*)/';
		//preg_match($pattern,$text,$matches);
		//print_r($matches);
		//
		//$pattern = '/^([0-9]{1})#(.*)/';
		//preg_match($pattern, $matches[2], $o);
		//print_r($o);
		
//		$this->myChildObjs = ChildProfileBasic::model()->findAllByPk(array(3954));
//		
//				$child = array_shift($this->myChildObjs);
//				$campus = Branch::model()->with('info')->findByPk($child->schoolid);
//				
//                $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
//                $mailer->Subject = sprintf('[微信助手 - %s] 孩子信息：%s' , $campus->abb, $child->getChildName());
//				
//                $mailer->AddAddress('<EMAIL>');
//                $mailer->iniMail(); // 此行代码要放到AddAddress, AddCC方法下面
//                $mailer->getView('wechatContact', array('userMsg'=>$content), 'wechat');
//				print_r($mailer->Body);
				//$mailer->Send();
				//$this->text($mailer->Subject);
		//echo $date;
		//echo '<hr>';		
		//print_r(MimsAPI::lunchCancel(2815, $date));
		//echo '<hr>';
		//print_r(MimsAPI::lunchRecover(2815, $date, 2));
		//
		//$child = ChildProfileBasic::model()->findByPk(3954);
		//$targetDate = '20130902';
		//
		//$this->sendLunchEmail($child, $targetDate, 'cancel');
		
		
		
		//$notes = MimsAPI::getRecentJournals(2815, 20);
//		$classid = 313;
//		$time = time();
//		$openid = 'ouwmTjrjwe6XzIk3ppPQ_KzmM4nk';
//		echo CHtml::link($classid, MimsAPI::genClassMatesLink($classid, $time, $openid, $this->securityKey, true));

		//$picsByYear[2013] = array(1);		
		//$picsByYear[2012] = array(375048,358375,402819);		
		//
		//
		//Yii::import('common.models.portfolio.*');
		//
		//$photos = MimsAPI::getPhotos($picsByYear);
		//
		//foreach($photos as $year=>$ps){
		//	foreach($ps as $p){
		//		echo MimsAPI::getMediaUrl(true, $p) . '<br >';
		//	}
		//	
		//}

		
				
//		$notes = MimsAPI::getRecentJournals(array(3645,1624), 20);
//		$notes = MimsAPI::getRecentJournals(array(6021), 20);
		$notes = MimsAPI::getRecentJournals(3954, 20);
		$time = time();
		$openid = 'ouwmTjrjwe6XzIk3ppPQ_KzmM4nk';
//		$openid = 'ouwmTjlpDGbwn6l5TC8V2VW878Xk';
		foreach($notes as $note){
			echo CHtml::link($note->classid . '-'.$note->weeknumber, MimsAPI::createJournalUrl($note, $time, $openid, $this->securityKey));
			echo '<br>';
		}
		
//		Yii::import('common.models.lunchmenu.*');
////		$menuLinks = CateringMenuWeeklink::model()->findAllByPk(array(1671,1670,1669,1668));
//		$menuLinks = CateringMenuWeeklink::model()->findAllByPk(array(2023,2021,2195));
//		$time = time();
//		$openid = 'ouwmTjrjwe6XzIk3ppPQ_KzmM4nk';
//		foreach($menuLinks as $menuLink){
//			echo CHtml::link($menuLink->id, MimsAPI::createLunchMenuUrl($menuLink, $time, $openid, $this->securityKey));
//			echo '<br>';
//		}
		
		
		
		
		
		
				//$child = array_shift($this->myChildObjs);
//				$child = ChildProfileBasic::model()->findByPk(3954);
//				$campus = Branch::model()->with('info')->findByPk($child->schoolid);
//				
//                $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
//                $mailer->Subject = sprintf('【艾毅在线微信助手 - %s】孩子信息：%s（联系校园）' , $campus->abb, $child->getChildName());
//				
//				$pEmails = $this->getParentsEmail($child);
//				
//				//$content .= implode(',',$pEmails);
//				
//                $mailer->AddAddress('<EMAIL>');
//				$mailer->AddReplyTo($campus->info->support_email);
//				foreach($pEmails as $email){
//					$mailer->AddCC($email);
//				}
//				
//                $mailer->iniMail(true); // 此行代码要放到AddAddress, AddCC方法下面
//                $mailer->getView('wechatContact', array('userMsg'=>$content), 'wechat');
//				//$this->text($campus->info->support_email);
//                if ($mailer->Send()) {
//					//$this->text("您的信息已经发送至校园支持团队，内容如下：\r\n" . $content );
//				}else{
//					//$this->text("系统错误，请稍后再试\r\n");
//				}		
//		
//		
//		
		
	}
	private function getParentsEmail($child){
		$emails = array();
		$pids = array();
		if($child->fid) $pids[] = $child->fid;
		if($child->mid) $pids[] = $child->mid;
		$parents = User::model()->findAllByPk($pids, '`level`>:level', array(':level'=>0));
		if(!empty($parents)){
			foreach($parents as $parent){
				$emails[] = $parent->email;
			}
		}
		return $emails;		
	}
	
	private function iniChildFromWeChat($openid, $with=null){
		$this->wechatUser = WechatUser::model()->findByPk($openid);
		if(is_null($this->wechatUser)){
			$this->isBound = WeChatBasedController::STAT_BOUND_NONE;
		}else{
			if($this->wechatUser->valid && $this->wechatUser->userid)
				$this->isBound = WeChatBasedController::STAT_BOUND;
			else
				$this->isBound = WeChatBasedController::STAT_BOUND_EXPIRED;
		}
		
		if( $this->isBound == WeChatBasedController::STAT_BOUND ){
			$parent = IvyParent::model()->findByPk($this->wechatUser->userid);
			if (!empty($parent) && count(@unserialize($parent->childs))) {
				$cids = @unserialize($parent->childs);
				
				if(is_null($with))
					$this->myChildObjs = ChildProfileBasic::model()->findAllByPk($cids, array("index" => "childid","order"=>"childid DESC"));
				else
					$this->myChildObjs = ChildProfileBasic::model()->with($with)->findAllByPk($cids, array("index" => "childid","order"=>"t.childid DESC"));
				
				foreach($this->myChildObjs as $child){
					if($child->status < 100){
						$this->activeChildIds[] = $child->childid;
					}
				}
				//孩子id从大到小排列
				rsort($this->activeChildIds);
			}
		}
	}
	
	public function actionUploadChildAvatar(){
		set_time_limit(0);
		extract($_REQUEST);
		$keytoCheck = md5($index . $id . $openid . $this->securityKey);
		if($key == $keytoCheck){
			$wechatUpload = WechatUploads::model()->findByPk($id);
			if(!empty($wechatUpload)){
			
				$this->iniChildFromWeChat($openid);
				$index = $index - 1;
				
				if(isset($this->activeChildIds[$index])){

                    $rootUploadPath = rtrim(Yii::app()->params['uploadPath'], '/') . '/';

					$partName = uniqid('childw_');
					$newPhoto = MimsAPI::copyImage(
						$wechatUpload->imgurl,
                        $rootUploadPath . 'childmgt',
						$partName
					);
					
					if($newPhoto){
						$old = $this->myChildObjs[$this->activeChildIds[$index]]->photo;
						$this->myChildObjs[$this->activeChildIds[$index]]->photo  = $newPhoto;
						if( $this->myChildObjs[$this->activeChildIds[$index]]->save() ){
							$wechatUpload->status = 1;
							$wechatUpload->save();
                            $aliYunOss['new'][] = 'childmgt/' . $newPhoto;
                            $aliYunOss['new'][] = 'childmgt/thumbs/' . $newPhoto;
							if(strpos($old, 'blank')===false){
								@unlink($rootUploadPath . 'childmgt/'.$old);
								@unlink($rootUploadPath . 'childmgt/thumbs/'.$old);
                                $aliYunOss['del'][] = 'childmgt/'.$old;
                                $aliYunOss['del'][] = 'childmgt/thumbs/'.$old;
							}

                            CommonUtils::processAliYunOSS($aliYunOss, $rootUploadPath);
						}
					}
					
				
				}
			}
		}
	}
	
	public function actionClassMates($data, $t, $o, $code){
		$codetoCheck = md5($this->securityKey . $t . $data . $o );
		if($codetoCheck == $code){
			if( (time() - $t) <= $this->expireTime ){
				$class = IvyClass::model()->with('schoolInfo')->findByPk($data);
				$crit = new CDbCriteria;
				$crit->compare('classid', $data);
				$crit->compare('status', '<'. ChildProfileBasic::STATS_GRADUATED);
				$crit->order = 'childid ASC';
				$crit->index = 'childid';
				$classmates = ChildProfileBasic::model()->findAll($crit);
				$this->render('classmates', array('class'=>$class, 'classmates'=>$classmates));
			}
		}
	}
	
	public function actionClassMates2($data, $t, $o, $code){
		Yii::app()->language = 'zh_cn';
		$codetoCheck = md5($this->securityKey . $t . $data . $o );
		if($codetoCheck == $code){
			if( 1 || (time() - $t) <= $this->expireTime ){
				$class = IvyClass::model()->with('schoolInfo')->findByPk($data);
				$crit = new CDbCriteria;
				$crit->compare('classid', $data);
				$crit->compare('status', '<'. ChildProfileBasic::STATS_GRADUATED);
				$crit->order = 'childid ASC';
				$crit->index = 'childid';
				$classmates = ChildProfileBasic::model()->findAll($crit);
				
				$cfgs = Mims::LoadConfig('CfgProfile');
				foreach($classmates as $child){
					$childs[] = array(
						'childid' => $child->childid,
						'smallface' => CommonUtils::childPhotoUrl($child->photo, 'small'),
						'face' => CommonUtils::childPhotoUrl($child->photo),
						'name' => $child->getChildName(true, true),
						'gender' => $cfgs['gender'][$child->gender],
						'age' => CommonUtils::getAge($child->birthday)
					);
				}
				Yii::app()->getClientScript()->registerCoreScript('jquery');
				Yii::app()->clientScript->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/hammer.js');
				Yii::app()->clientScript->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/modernizr.js');
				//Yii::app()->clientScript->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
				//Yii::app()->clientScript->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
				
				$this->render('classmates2', array('class'=>$class, 'childs'=>$childs, 'classmates'=>CJSON::encode($childs)));
			}
		}
	}
	
	// Uncomment the following methods and override them if needed
	/*
	public function filters()
	{
		// return the filter configuration for this controller, e.g.:
		return array(
			'inlineFilterName',
			array(
				'class'=>'path.to.FilterClass',
				'propertyName'=>'propertyValue',
			),
		);
	}

	public function actions()
	{
		// return external action classes, e.g.:
		return array(
			'action1'=>'path.to.ActionClass',
			'action2'=>array(
				'class'=>'path.to.AnotherActionClass',
				'propertyName'=>'propertyValue',
			),
		);
	}
	*/

}
