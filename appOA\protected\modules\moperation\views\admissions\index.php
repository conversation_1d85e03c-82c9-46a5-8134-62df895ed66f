<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','HQ Operations'), array('/moperation'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Marketing'), array('/moperation'))?></li>
        <li class="active"><?php echo Yii::t('site','Marketing Activity') ?></li>
    </ol>

    <div class="row">
        <div class="col-md-1 col-sm-2">
            <?php
            $mainMenu = array(
                array('label'=>Yii::t('user','活动管理'), 'url'=>array("/moperation/admissions/index")),
            );

            $this->widget('zii.widgets.CMenu',array(
                'items'=> $mainMenu,
                'id'=>'pageCategory',
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-right background-gray'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>
        <div class="col-md-11 col-sm-10">
            <div class="background-gray p8">
                <?php echo CHtml::link('<span class="glyphicon glyphicon-plus"></span> 添加活动', array('update'), array('class'=>'J_dialog btn btn-primary', 'title'=>'添加活动'));?>
            </div>
            <?php
            $this->widget('ext.ivyCGridView.BsCGridView', array(
                'id'=>'event-grid',
                'dataProvider'=>$model->search(),
            //	'filter'=>$model,
                'colgroups'=>array(
                    array(
                        "colwidth"=>array(null,150,150,150),
            //            "htmlOptions"=>array("align"=>"left", "span"=>2)
                    )
                ),
                'columns'=>array(
                    array(
                        'name'=>'title',
                        'value'=>'CommonUtils::autoLang($data->cn_title, $data->en_title)',
                    ),
                    array(
                        'name'=>'event_date',
                        'value'=>'OA::formatDateTime($data->event_date)',
                    ),
                    array(
                        'name'=>'学校',
                        'value'=>'Yii::app()->controller->branchArr[$data->school_id]',
                    ),
                    array(
                        'class'=>'BsCButtonColumn',
                        'template' => '{member} {update} {delete}',
                        'buttons'=>array(
                            'member'=>array(
                                'label'=>'<i class="glyphicon glyphicon-user"></i>',
                                'url'=>'Yii::app()->createUrl("/moperation/admissions/viewMember", array("id" => $data->id))',
                                'options'=>array('class'=>'btn btn-info btn-xs', 'title'=>'查看报名名单'),
                            ),
                        ),
                    ),
                ),
            )); ?>
        </div>
</div>