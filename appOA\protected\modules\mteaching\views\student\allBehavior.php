<style>
    .listCom{
        border-radius: 8px;
        border: 1px solid #E8EAED;
        padding:24px 0 24px 24px
    }
    .labelbg{
        padding:2px;
        background: #F7F7F8;
        border-radius: 2px;
        border: 1px solid #D9D9D9;
    }
    .supportPlan{
        background: #FFFFFF;
        border: 1px solid #E8EAED;
        border-radius:4px;
    }
    
    .handlingResults{
        background: rgba(77,136,210,0.06);
        border-radius: 4px;
        border: 1px solid #E8EAED;
    }
    .handlingTitle{
        background: linear-gradient(90deg, #84BDEB 0%, #4D88D2 100%);
        border-radius: 4px 4px 0px 0px;
        border: 1px solid #E8EAED;
        padding:16px 0;
        color: #FFFFFF;
        text-align:center
    }
    .labelTag{
        width: 18px;
        height: 18px;
        background: #666666;
        border-radius: 9px;
        border: 1px solid #FFFFFF;
        display:inline-block;
        text-align: center;
        line-height: 17px;
        color: #fff;
        font-size: 12px;
    }
    .labelTagRed{
        background:#D9534F
    }
    .plr0{
        padding-left:0;
        padding-right:0
    }    
    .font24{
        font-size:24px
    }
    .lineHeight{
        line-height:1
    }
    .blueBlock{
        width: 6px;
        height: 16px;
        background: #4D88D2;
        border-radius: 1px;
        display:block
    }
    .warningBg{
        background: #FAFAFA;
        border-radius: 4px;
        padding: 16px 24px;
        font-size:14px;
        height:140px
    }
    .line-clamp{
        overflow: hidden;
        word-break: break-all;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
    }
    .badgeNum{
        min-width: 18px;
        height: 18px;
        background: #666666;
        border-radius: 9px;
        border: 1px solid #FFFFFF;
        color:#fff;
        font-size:12px;
        text-align:center;
        line-height:18px;
        display:inline-block;
        padding:0 4px
    }
    .warningStu{
        padding:8px 10px;
        display:inline-flex; 
        align-items:center
    }
    .warningStu:hover{
        border-radius: 4px;
        background: #EBEDF0;
        cursor: pointer;
    }
    .allApply{
        width: 216px;
        height: 100px;
        background: #EEF4FB;
        border-radius: 4px;
        border: 1px solid transparent;
        margin-right:16px;
        padding: 12px ;
        display:inline-block;
        cursor: pointer;
    }
    .pb0{
        padding-bottom:0px !important
    }
    .mt_3{
        margin-top:-3px
    }
    .allApply:last-child{
        margin-right:0px;
    }
    .bgPink{
      background: #FCF2F1;
    }
    .bgYellow{
        background: #FDF6ED;
    }
    .bgGreen{
        background: #F2FAF2;
    }
    .bgPurple{
        background:#EDEFFD
    }
    .bgPurpleBorder{
        border:1px solid #192FE5 
    }
    .colorPurple{
        color:#192FE5
    }
    .colorGreen{
        color: #5CB85C;
    }
    .yearList{
        display: inline-block;
        position: absolute;
        right: 0;
        top: 3px;
    }
    .yearList .dropdown-menu{
        right:0;
        left:auto
    }
    .moreChild{
        height: 130px;
        background: #FFFFFF;
        border-radius: 4px;
        border: 1px solid #E8EAED;
        padding:16px;
        margin-bottom:16px
    }
    .moreChildFilter{
        height: 75px;
        background: #FFFFFF;
        border-radius: 4px;
        border: 1px solid #E8EAED;
        padding:16px;
        margin-bottom:16px;
        align-items:center
    }
    .moreChild:hover,.warningClassList:hover,.moreChildFilter:hover{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        border: 1px solid #4D88D2;
        cursor: pointer;
    }
    .warningClass{
        width:250px;
        background: #FAFAFA;  
    }
    .warningClassList{
        padding:10px;
        font-size:14px;
        border: 1px solid #FAFAFA;
        color:#333;
    }
    .warningClassListCheck{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        border: 1px solid #4D88D2;
        cursor: pointer;
    }
    .bgPinkBorder{
        border:1px solid #D9534F
    }
    .bgYellowBorder{
        border:1px solid #F0AD4E 
    }
    .bgGreenBorder{
        border:1px solid #5CB85C 

    }
    .el-loading-mask{
        z-index:1000
    }
    .height32{
        height:32px
    }
    .p12{
        padding:12px !important
    }
    .problemList{
        padding:8px;
        border: 1px solid #E5E6EB;
        border-radius:4px
    }
    .maxWidth{
        max-width:200px
    }
    .problemTitle{
        padding:8px
    }
    .circle{
        width: 6px;
        height: 6px;
        background: #969799;
        display:inline-block;
        border-radius:50%;
        margin-right: 8px;
        margin-top: 5px;
    }
    .circleFlex{
        padding:4px 8px ;
        /* align-items:center; */
        color:#333333;
    }
    .circleFlex:hover{
        background: #F2F3F5;
        border-radius: 4px;
        /* cursor: pointer; */
    }
    .dp-text-ellipsis-wrapper {
        display: flex;
        overflow: hidden;
        font-size: 12px;
        line-height: 25px;
    }
    .text {
        position: relative;
        overflow: hidden;
        line-height:20px;
        text-align: justify;
        text-overflow: ellipsis;
        word-break: break-all;
    }
    .text::before {
        float: right;
        height: calc(100% - 20px);
        content: '';
    }
    .expand {
        position: relative;
        float: right;
        clear: both;
        margin-left: 20px;
        font-size: 14px;
        padding: 0 0 0 8px;
        color: #4D88D2;
        line-height: 20px;
        border-radius: 4px;
        cursor: pointer;
        z-index: 10;
    }
    
    .expand::before {
        position: absolute;
        left: 1px;
        color: #333;
        transform: translateX(-100%);
        content: '...';
    }
    .classMore{
        height: 50px;
        background: #FFFFFF;
        border-radius: 4px;
        border: 1px solid #E8EAED;
        padding:16px;
        margin-bottom:16px;
        align-items:center
    }
    .classMore:hover{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        border: 1px solid #4D88D2;
        cursor: pointer;
    }
    .warning_outline{
        padding:6px 8px;
        background:#F7F7F8;
        margin-left:15px;
        border-radius:2px;
        color:#666666;
        font-size:12px
    }
    .praise{
        width: 19px;
        height: 19px;
        margin-right: 6px;
    }
    .hoverImg:hover{
        cursor: pointer;
    }
    .ml6{
        margin-left:6px
    }
    .word_break{
        word-break: break-word;
    }
    .identifiedTab{
        display: inline-flex;
        padding: 16px 20px;
        align-items: center;
        border: 1px solid transparent;
        border-radius: 8px;
        line-height:1;
        cursor: pointer;
    }
    .identifiedTab img{
        width:32px;
        height:32px
    }
    .badBehavior{
        background: #FDF8F1;
    }
    .badBehaviorBorder{
        border: 1px solid #F0AD4E;
    }
    .goodBehavior{
        background: #F0F5FB;
    }
    .goodBehaviorBorder{
        border: 1px solid #4D88D2;
    }
    .labelWidth{
        width:75px;
    }
    .widthBad{
        width:120px;
    }
    .behavior-title {
        color: #333;
        padding:4px 8px 4px 20px;
        text-align: center;
        font-size: 12px;
        position: relative;
        line-height: 1;
        margin-right: -20px;
    }

    .behavior-title::before {
        content: "";
        position: absolute;
        top: 3px;
        left: -7px;
        width: 14px;
        height: 14px;
        background-color: #F7F7F8;
        transform: rotate(45deg);
        border-radius: 1px;
    }
    .labelRecord{
        background: #E5E6EB;
    }
    .labelRed{
        color:#D9534F;
        background:rgba(217, 83, 79, 0.15)
    }
    .labelGreen{
        background:rgba(92, 184, 92, 0.15);
        color:#5CB85C
    }
    .labelBlue{
        color:#4D88D2;
        background:rgba(77, 136, 210, 0.15)
    }
    .blueHover:hover{
        color:#4D88D2;
    }
</style>
<div class="" id='container' v-cloak >
    <div class='relative'  v-if='initList.warning'>
        <ul class="nav nav-tabs" role="tablist">
            <li role="presentation" :class="currentGrade_group==key?'active':''" v-for='(list,key,index) in initList.showTab'><a href="javascript:void(0)" @click='currentGrade_group=key;initData(currentYear)'   role="tab" data-toggle="tab">{{list}}</a></li>
        </ul>
        <div class='yearList'>
            <div class="btn-group">
                <button type="button" class="btn btn-link dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    {{initSchoolYear[currentYear]}} <span class="caret"></span>
                </button>
                <ul class="dropdown-menu">
                    <li v-for='(list,key,index) in initSchoolYear' @click='initData(key)'><a href="javascript:void(0)">{{list}}</a></li>
                </ul>
            </div>
        </div>
    </div>
    <div  v-loading="initLoading"  v-if='initList.warning'>
        <div class='mt24' v-if='initList.warning.childWarning.length!=0 || initList.warning.classWarning1.length!=0'>
            <div class='flex align-items mb16'>
                <span class='blueBlock'></span>
                <div class='flex1 color3 font16 ml8 fontBold lineHeight'><?php echo Yii::t("referral", "Frequency Attention"); ?></div>
            </div>
            <div class='row'>
                <div class='col-md-6'>
                    <div class='warningBg'>                
                        <div class='flex'>
                            <span class='flex1 fontBold'><?php echo Yii::t("newDS", "Student"); ?></span>
                            <span class='btn-link' @click='studentMore()'><?php echo Yii::t("referral", "More"); ?> <span class='el-icon-arrow-right'></span></span>
                        </div>
                        <div class='mt12 line-clamp'>
                            <span class='warningStu' v-for='(list,index) in initList.warning.childWarning' @click='identifiedList(list)'>
                                <span class='color6 mr4'>{{initList.warning.child_data[list.child_id].bilingual_name}}</span> 
                                <span class='badgeNum'>{{list.num}}</span>
                            </span>
                        </div>
                    </div>
                </div>
                <div class='col-md-6'>
                    <div class='warningBg'>                
                        <div class='flex'>
                            <span class='flex1 fontBold'><?php echo Yii::t("labels", "Class"); ?></span>
                            <span class='btn-link'  @click='classMore(initList.warning.classWarning1[0].class_id)'><?php echo Yii::t("referral", "More"); ?> <span class='el-icon-arrow-right'></span></span>
                        </div>
                        <div class='mt12 line-clamp'>
                            <span class='warningStu' v-for='(list,index) in initList.warning.classWarning1' @click='classMore(list.class_id)'>
                                <span class='color6 mr4'>{{initList.warning.class_data[list.class_id].title}}</span>  <span class='badgeNum'>{{list.num}}</span>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class='mt24 mb24 pt8' >
            <div class='flex align-items mb16'>
                <span class='blueBlock'></span>
                <div class='flex1 color3 font16 ml8 fontBold lineHeight'><?php echo Yii::t("referral", "All Entries"); ?></div>
            </div>
            <div class='mt16 flex'>
                <div class='allApply' style='padding:12px 0' :class='currentProcessing==null?"warningClassListCheck":""' @click='getData("init")'>
                    <div class='font14 ml12'><span class='glyphicon glyphicon-info-sign bluebg'></span><span class='color3 ml6'><?php echo Yii::t("newDS", "New"); ?></span></div>
                    <div class='font24 text-center'>{{currentStat.new}}</div>
                    <div class='text-center'>
                        <div class="btn-group btn-group-xs" role="group" aria-label="..." v-if='!maxWidth'>
                            <button type="button" class="btn" :class='currentTime=="1"?"btn-primary":"btn-default"' @click.stop='currentTime=1;getData("init")'><?php echo Yii::t("withdrawal", "Today"); ?></button>
                            <button type="button" class="btn" :class='currentTime==3?"btn-primary":"btn-default"'  @click.stop='currentTime=3;getData("init")'><?php echo Yii::t("withdrawal", "Month"); ?></button>
                            <button type="button" class="btn" :class='currentTime==5?"btn-primary":"btn-default"'  @click.stop='currentTime=5;getData("init")'><?php echo Yii::t("withdrawal", "Semester"); ?></button>
                            <button type="button" class="btn" :class='currentTime==4?"btn-primary":"btn-default"'  @click.stop='currentTime=4;getData("init")'><?php echo Yii::t("withdrawal", "Year"); ?></button>
                        </div>
                        <div class="btn-group btn-group-xs" role="group" aria-label="..." v-else>
                            <el-tooltip class="item" effect="dark" content="<?php echo Yii::t("withdrawal", "Today"); ?>" placement="top-start">
                                <button type="button" class="btn" :style='lang=="en_us"?"padding:1px 10px":""' :class='currentTime=="1"?"btn-primary":"btn-default"' @click.stop='currentTime=1;getData("init")'><?php echo Yii::t("withdrawal", "D"); ?></button>
                            </el-tooltip>
                            <el-tooltip class="item" effect="dark" content="<?php echo Yii::t("withdrawal", "Month"); ?>" placement="top-start">
                                <button type="button" class="btn"  :style='lang=="en_us"?"padding:1px 10px":""' :class='currentTime==3?"btn-primary":"btn-default"'  @click.stop='currentTime=3;getData("init")'><?php echo Yii::t("withdrawal", "M"); ?></button>
                            </el-tooltip>
                            <el-tooltip class="item" effect="dark" content="<?php echo Yii::t("withdrawal", "Semester"); ?>" placement="top-start">
                                <button type="button" class="btn"  :style='lang=="en_us"?"padding:1px 10px":""' :class='currentTime==5?"btn-primary":"btn-default"'  @click.stop='currentTime=5;getData("init")'><?php echo Yii::t("withdrawal", "S"); ?></button>
                            </el-tooltip>
                            <el-tooltip class="item" effect="dark" content="<?php echo Yii::t("withdrawal", "Year"); ?>" placement="top-start">
                                <button type="button" class="btn"  :style='lang=="en_us"?"padding:1px 10px":""' :class='currentTime==4?"btn-primary":"btn-default"'  @click.stop='currentTime=4;getData("init")'><?php echo Yii::t("withdrawal", "Y"); ?></button>
                            </el-tooltip>

                        </div>
                    </div>
                </div>
                <div class='allApply bgPink' @click='getProcessing(1,"init")' :class='currentProcessing==1?"bgPinkBorder":""'>
                    <div class='font14'><span class='glyphicon glyphicon-exclamation-sign colorRed'></span><span class='color3 ml6 '><?php echo Yii::t("referral", "My Tasks"); ?></span></div>
                    <div class='font24 p10 text-center'>{{currentStat.my}}</div>
                </div>
                <div class='allApply bgYellow' @click='getProcessing(2,"init")' :class='currentProcessing==2?"bgYellowBorder":""'>
                    <div class='font14'><span class='glyphicon glyphicon-question-sign waitingColor'></span><span class='color3 ml6 '><?php echo Yii::t("referral", "In progress"); ?></span></div>
                    <div class='font24 p10 text-center'>{{currentStat.processing}}</div>
                </div>
                <div class='allApply bgPurple' @click='getProcessing(4,"init")' :class='currentProcessing==4?"bgPurpleBorder":""'>
                    <div class='font14'><span class='glyphicon glyphicon-question-sign colorPurple'></span><span class='color3 ml6 '><?php echo Yii::t("referral", "To Complete"); ?></span></div>
                    <div class='font24 p10 pb0 text-center'>{{currentStat.waitEnd}}</div>
                    <div class='text-center mt_3 color6'><?php echo Yii::t("referral", "Result already issued"); ?></div>
                </div>
                <div class='allApply bgGreen mr0' @click='getProcessing(3,"init")' :class='currentProcessing==3?"bgGreenBorder":""'>
                    <div class='font14'><span class='glyphicon glyphicon-ok-sign colorGreen'></span><span class='color3 ml6 '><?php echo Yii::t("referral", "Completed"); ?></span></div>
                    <div class='font24 p10 text-center'>{{currentStat.end}}</div>
                </div>
            </div>
            <div class='flex  mt20 mb16'>
                <span class='color6 font14 mt5'><?php echo Yii::t("global", "Filter"); ?>：</span>
                <div class='flex1'>
                    <div>
                        <span v-if='searchName==""'>
                            <el-button  size="mini" @click='filterStu'><span class='glyphicon glyphicon-filter'> </span> <?php echo Yii::t("labels", "Student"); ?></el-button>
                            <el-button  size="mini" @click='filterClass'><span class='glyphicon glyphicon-filter'> </span> <?php echo Yii::t("labels", "Class"); ?></el-button>
                        </span>
                        <span v-else>
                            <el-tag
                                closable
                                :disable-transitions="false"
                                @close="handleClose()">
                                {{searchName}}
                            </el-tag>
                        </span>
                        <span class='warning_outline'><span class='el-icon-info mr5 font14'></span><?php echo Yii::t("referral", "Click on a student's photo or class to add a filter"); ?></span>
                    </div>
                    <div class='mt12'>
                        <el-select v-model="filterBehaviorValue" size='small' style='width:240px' clearable placeholder="<?php echo Yii::t("referral", "All Problem Behavior"); ?>" @change='filterBehavior()'>
                            <el-option
                                label="<?php echo Yii::t("referral", "All Problem Behavior"); ?>"
                                value="">
                            </el-option>
                            <el-option
                                v-for='(list,index) in initList.searchCan'
                                :key="list.value"
                                :label="list.title"
                                :value="list.value">
                                <span class='flex align-items' v-if='list.value=="POSITIVEBEHAVIOR"'>
                                     <img  class='praise'  src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/praise.png' ?>" alt="">
                                    <span class='flex1'>{{ list.title }}</span>
                                    <span class="badge ml20"  v-if='dataList.countProblemBehavior && dataList.countProblemBehavior.POSITIVEBEHAVIOR'>{{dataList.countProblemBehavior.POSITIVEBEHAVIOR}}</span>
                                </span>
                                <span class='flex align-items' v-else-if='list.value=="WINTERLANGUAGE"'>
                                     <img  class='praise'  src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/special1.png' ?>" alt="">
                                    <span class='flex1'>{{ list.title }}</span>
                                    <span class="badge ml20"  v-if='dataList.countProblemBehavior && dataList.countProblemBehavior[list.value]'>{{dataList.countProblemBehavior[list.value]}}</span>
                                </span>
                                <span class='flex align-items' v-else>
                                    <span class='flex1'>{{ list.title }}</span>
                                    <span class="badge ml20"  v-if='dataList.countProblemBehavior && dataList.countProblemBehavior[list.value]'>{{dataList.countProblemBehavior[list.value]}}</span>
                                </span>
                            </el-option>
                        </el-select>
                    </div>
                </div>
            </div>
        </div>
        <div v-if='dataList.list'>
            <el-table
                :header-cell-style="{background:'#fafafa',color:'#333'}"
                :data="dataList.list"
                style="width: 100%;font-size:12px"
                v-loading="indexLoading"
                element-loading-background="rgba(255, 255, 255, 1)"
                :default-sort = "{prop: 'created_at', order: 'descending'}"
                @sort-change="sort_change" 
                >
                <template slot="empty">
                    <?php echo Yii::t("ptc", "No Data"); ?>
                </template>
                <el-table-column
                prop="child"
                label="<?php echo Yii::t("newDS", "Student"); ?>"
                min-width="200">
                    <template slot-scope="scope">
                        <div v-if='scope.row.child.length==1'>
                            <div class="media flex  align-items" v-for='(list,index) in scope.row.child'>
                                <div class="media-left pull-left media-middle relative">
                                <!-- <el-tooltip class="item" effect="dark" content="<?php echo Yii::t("referral", "Filter by this student"); ?>" placement="top"> -->
                                    <img :src="dataList.child_list[list.id].avatar" data-holder-rendered="true" class="avatar hoverImg" @click='filterList(list.id,dataList.child_list[list.id].name,"child")'>
                                <!-- </el-tooltip> -->
                                </div>
                                <div class="pull-left media-middle">
                                    <div class="mt5 color3 lineHeight" >
                                        <!-- <el-tooltip class="item" effect="dark" content="<?php echo Yii::t("referral", "Filter by this student"); ?>" placement="top"> -->
                                            <span>
                                                <span @click='filterList(list.id,dataList.child_list[list.id].name,"child")' class='cur-p'>{{dataList.child_list[list.id].name}}</span> 
                                                <span v-if='list.tag==2 || list.tag==3' class='glyphicon glyphicon-bookmark font12' :class='list.tag==2?"waitingColor":list.tag==3?"colorRed":""'></span>
                                            </span>
                                            <!-- <span  :class='list.tag==2?"tagLabelYellow":list.tag==3?"tagLabel":""'>{{showTitle(list.tag,'ISP_type')}}</span> -->
                                        <!-- </el-tooltip> -->
                                    </div>
                                    
                                    <div class="color6 font12 mt8 lineHeight">
                                        <!-- <el-tooltip class="item" effect="dark" content="<?php echo Yii::t("referral", "Filter by this class"); ?>" placement="top"> -->
                                            <span class='cur-p' @click='filterList(list.class_id,dataList.child_list[list.id].className,"class")'>{{dataList.child_list[list.id].className}}</span>
                                        <!-- </el-tooltip> -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-else>
                            <span v-for='(list,index) in scope.row.child' >
                                <!-- <el-tooltip class="item" effect="dark" content="<?php echo Yii::t("referral", "Filter by this student"); ?>" placement="top"> -->
                                    <img :src="dataList.child_list[list.id].avatar" data-holder-rendered="true" class="avatar32 mb8 mr4 cur-p" @click='filterList(list.id,dataList.child_list[list.id].name,"child")'>
                                <!-- </el-tooltip> -->
                            </span>
                            <el-popover
                                placement="bottom"
                                :ref="`popover-${scope.row._id}`"
                                trigger="click">
                                <div class="media flex  align-items" v-for='(list,index) in scope.row.child'>
                                    <div class="media-left pull-left media-middle relative">
                                        <!-- <el-tooltip class="item" effect="dark" content="<?php echo Yii::t("referral", "Filter by this student"); ?>" placement="top"> -->
                                            <img :src="dataList.child_list[list.id].avatar" @click='filterList(list.id,dataList.child_list[list.id].name,"child",scope.row._id)' data-holder-rendered="true" class="avatar hoverImg">
                                        <!-- </el-tooltip> -->
                                    </div>
                                    <div class="pull-left media-middle">
                                        <div class="font12 color6 lineHeight"> 
                                            <!-- <el-tooltip class="item" effect="dark" content="<?php echo Yii::t("referral", "Filter by this student"); ?>" placement="top"> -->
                                                <span>
                                                    <span @click='filterList(list.id,dataList.child_list[list.id].name,"child",scope.row._id)' class='cur-p'>{{dataList.child_list[list.id].name}}</span> 
                                                    <span v-if='list.tag==2 || list.tag==3' class='glyphicon glyphicon-bookmark font12' :class='list.tag==2?"waitingColor":list.tag==3?"colorRed":""'></span>
                                                </span>
                                            <!-- </el-tooltip> -->
                                        </div>
                                        <div class="color6 font12  mt8 lineHeight">
                                            <!-- <el-tooltip class="item" effect="dark" content="<?php echo Yii::t("referral", "Filter by this class"); ?>" placement="top"> -->
                                                <span class='cur-p' @click='filterList(list.class_id,dataList.child_list[list.id].className,"class",scope.row._id)'>{{dataList.child_list[list.id].className}}</span>
                                            <!-- </el-tooltip> -->
                                        </div>
                                    </div>
                                </div>
                                <span class='bluebg  cur-p' slot="reference"><?php echo Yii::t("referral", "Total "); ?>{{scope.row.child.length}}<?php echo Yii::t("referral", " "); ?></span>
                            </el-popover>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                prop="id"
                label="<?php echo Yii::t("labels", "ID"); ?>"
                min-width="150">
                    <template slot-scope="scope">
                       <div  class='color6'>No.{{scope.row.id}}</div>
                       <div class='color3'>{{dataList.staff_info[scope.row.created_by].name}}</div>
                       <el-tooltip class="item" effect="dark" :content="scope.row.created_date" placement="top">
                            <span class='color6 cur-p'>{{scope.row.diff_date}}</span>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column
                prop="id"
                label="<?php echo Yii::t("referral", "Event Description"); ?>"
                min-width="300">
                    <template slot-scope="scope">
                        <div class="dp-text-ellipsis-wrapper" :class='scope.row._id' v-if='showHtml'>
                            <div class="text cur-p" :style="scope.row.more?textStyleObject:''" ref="text" @click='scope.row.more=!scope.row.more' v-if='shouldShowExpandButton[scope.row._id]' >
                                <span class="expand" v-if='scope.row.more' @click='scope.row.more=false'></span>
                                <el-tooltip class="item" effect="dark" content="<?php echo Yii::t("behavior", "Positive Behavior"); ?>" placement="top">
                                 <img class='praise' v-if='scope.row.detail.subtype=="POSITIVEBEHAVIOR"' src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/praise.png' ?>" alt="">
                                 </el-tooltip><span :class="'span_'+scope.row._id" class='word_break'>{{scope.row.detail.desc}}</span>
                            </div>
                            <div v-else-if='!shouldShowExpandButton[scope.row._id]' style='line-height:20px'>
                                <el-tooltip class="item" effect="dark" content="<?php echo Yii::t("behavior", "Positive Behavior"); ?>" placement="top">
                                    <img  class='praise'  v-if='scope.row.detail.subtype=="POSITIVEBEHAVIOR"' src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/praise.png' ?>" alt="">
                                </el-tooltip>
                                <span :class="'span_'+scope.row._id" class='word_break'>{{scope.row.detail.desc}}</span>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                prop="office_user"
                label="<?php echo Yii::t("referral", "Status"); ?>"
                :min-width="showMinWidth">
                    <template slot-scope="scope">
                        <div class='flex'>
                            <div class='flex' v-if='scope.row.just_record==0'>
                                <div class='text-center minWidth mr50' v-for='(list,index) in scope.row.nodes_order'>
                                    <div v-if='scope.row.nodes_by_node[list].status==1'>
                                        <div class='relative'>
                                            <div class='firstBorder' v-if='scope.row.nodes_order.length>1 && index+1<scope.row.nodes_order.length'></div>
                                            <div class='relative inline-block'>
                                                <img :src="dataList.staff_info[scope.row.nodes_by_node[list].updated_by].photoUrl" alt="" class='avatar32'>
                                                <span class='el-icon-success iconAbsolute'></span>
                                            </div>
                                        </div>
                                        <div class='color3 '>{{scope.row.nodes_order_title[index]}}</div>
                                        <div class='color6 font12'>
                                        <el-tooltip class="item" effect="dark"  placement="top">
                                        <div slot="content">{{dataList.staff_info[scope.row.nodes_by_node[list].updated_by].name}}<br/><?php echo Yii::t("leave", "Completed"); ?> {{scope.row.nodes_by_node[list].updated_at}}</div>
                                        <div class='color6'>{{scope.row.nodes_by_node[list].diff_date}}</div>
                                        </el-tooltip>
                                        </div>
                                    </div>
                                    <div v-else>
                                        <div>
                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/pending.png' ?>" alt="" class='avatar32'>
                                        </div>
                                        <div class='color3 '>{{scope.row.nodes_order_title[index]}}</div>
                                        <div class='font12 waitingColor'><?php echo Yii::t("referral", "In Process"); ?></div>
                                    </div>
                                </div>
                            </div>
                            <div v-if='scope.row.just_record==1' class='text-center minWidth'>
                                <?php echo Yii::t("referral", "Record only"); ?>
                            </div>
                        </div>
                    </template>     
                </el-table-column>
                <el-table-column
                prop="office_user"
                fixed="right"
                label="<?php echo Yii::t("newDS", "Actions"); ?>"
                width="150">
                    <template slot-scope="scope">
                        <!-- <el-dropdown v-if='tableOffice'>
                            <span class="el-dropdown-link">
                                更多<i class="el-icon-arrow-down el-icon--right"></i>
                            </span>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item v-if='scope.row.can_processing==1' @click.native='showDetails(scope.row._id,"init")'><?php echo Yii::t("referral", "Process"); ?></el-dropdown-item>
                                <el-dropdown-item v-if='scope.row.can_processing==0' @click.native='showDetails(scope.row._id,"init")'><?php echo Yii::t("global", "View Detail"); ?></el-dropdown-item>
                                <el-dropdown-item>删除</el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                        <div v-else>
                            <el-button type="text" @click='showDetails(scope.row._id,"init")'class='font14 bluebg' v-if='scope.row.can_processing==1'><?php echo Yii::t("referral", "Process"); ?></el-button>
                            <el-button type="text" @click='showDetails(scope.row._id,"init")'class='font14 bluebg' v-if='scope.row.can_processing==0'><?php echo Yii::t("global", "View Detail"); ?></el-button>
                            <el-button type="text" @click='showDetails(scope.row._id,"init")'class='font14 bluebg' v-if='scope.row.can_processing==0'><?php echo Yii::t("global", "View Detail"); ?></el-button>
                        </div> -->
                        <el-button type="text" @click='showDetails(scope.row._id,"init")'class='font12 bluebg' v-if='scope.row.can_processing==1'><?php echo Yii::t("referral", "Process"); ?></el-button>
                        <el-button type="text" @click='showDetails(scope.row._id,"init")'class='font12 bluebg' v-if='scope.row.can_processing==0'><?php echo Yii::t("global", "View Detail"); ?></el-button>
                        <el-button type="text" @click='delDetails(scope.row._id)'class='font12 bluebg' v-if='tableOffice'><?php echo Yii::t("global", "Delete"); ?></el-button>
                    </template>
                </el-table-column>
            </el-table>
            <nav aria-label="Page navigation" v-if='CopyPages.count_page>1'  class="text-left ml10">
                <ul class="pagination">
                    <li v-if='pageNum >1'>
                        <a href="javascript:void(0)" @click="plus(1)" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    <li v-else class="disabled">
                        <a aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    <li class="previous" v-if='pageNum >1'>
                        <a href="javascript:void(0)" @click="prev(pageNum)">‹</a>
                    </li>
                    <li class="disabled" v-else>
                        <a href="javascript:void(0)">‹</a>
                    </li>
                    <li v-for='(data,index) in pages.count' :class="{ active:data==pageNum }">
                        <a href="javascript:void(0)" @click="plus(data)">{{data}}</a>
                    </li>
                    <li class="previous" v-if='pageNum <CopyPages.count_page'>
                        <a href="javascript:void(0)" @click="next(pageNum)">›</a>
                    </li>
                    <li class="previous disabled" v-else>
                        <a href="javascript:void(0)">›</a>
                    </li>
                    <li v-if='pageNum <CopyPages.count_page'>
                        <a href="javascript:void(0)" @click="plus(CopyPages.count_page)" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li v-else class="disabled">
                        <a aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                </ul>
                <div class='summary mb10'>第 <span v-if='pageNum*CopyPages.per_page-CopyPages.per_page==0'>1</span><span v-else>{{pageNum*CopyPages.per_page-CopyPages.per_page}}</span>-{{pageNum*CopyPages.per_page}} 条, 共 {{CopyPages.total}} 条.</div>
            </nav>
        </div>
    </div>
    <!-- 认定记录 -->
    <div class="modal fade" id="identifiedModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("referral", "Behavior Submission Entries"); ?></h4>
                </div>
                <div class="modal-body p24 overflow-y scroll-box font14" :style="'max-height:'+(height)+'px;overflow-x: hidden;'" v-if='affirmRecordList.child_info'>
                    <div class="media pb10" >
                        <div class="media-left pull-left media-middle relative">
                            <img :src="affirmRecordList.child_info[affirmData.child_id].avatar" data-holder-rendered="true" class="avatar">
                        </div> 
                        <div class="pull-left media-middle">
                            <div class="mt4 color3">
                                <span class='font14'>{{affirmRecordList.child_info[affirmData.child_id].name}}</span>  
                                <span v-for='(item,ind) in affirmRecordList.child_info[affirmData.child_id].label' >
                                <span v-if='item.flag==2 || item.flag==3' class='glyphicon glyphicon-bookmark font12' :class='item.flag==2?"waitingColor":item.flag==3?"colorRed":""'></span>
                                    <!-- <span v-if='item.flag==2 || item.flag==3' :class='item.flag==2?"tagLabelYellow":item.flag==3?"tagLabel":""'>{{item.name}}</span> -->
                                </span>
                            </div> 
                            <div class="color6 font12">{{affirmRecordList.child_info[affirmData.child_id].className}}</div>
                        </div>
                    </div>
                    <div class='mb20 pt10' v-if='affirmRecordList.child_info[affirmData.child_id].grade=="HS" || affirmRecordList.child_info[affirmData.child_id].grade=="MS"'>
                        <div class='identifiedTab badBehavior'  @click='behaviorTabDetail="bad"' :class='behaviorTabDetail=="bad"?"badBehaviorBorder":""'>
                            <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/praise3.png' ?>" alt="">
                            <div class='ml12'>
                                <div class='font12 color3 mb4'><?php echo Yii::t("referral", "Negative Behavior"); ?></div>
                                <div class='font16 color3'>{{affirmRecordList.list.length}}</div>
                            </div>
                        </div>
                        <div class='identifiedTab ml20 goodBehavior' @click='behaviorTabDetail="good"' :class='behaviorTabDetail=="good"?"goodBehaviorBorder":""'>
                            <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/praise2.png' ?>" alt="">
                            <div class='ml12'>
                                <div class='font12 color3 mb4'><?php echo Yii::t("behavior", "Positive Behavior"); ?></div>
                                <div class='font16 color3'>{{affirmRecordList.positive.length}}</div>
                            </div>
                        </div>
                    </div>
                    <div v-if='behaviorTabDetail=="bad"'>
                        <div class='row p16 presenter pt15 pb15 m0'>
                            <div class='col-md-7 '>
                                <div class='font12 color6 mb12'><?php echo Yii::t("referral", "A behavior issue"); ?>：{{affirmRecordList.stat.behaviorNum}}</div>
                                <div class='flex mt12'>
                                    <div class='flex1 text-center'>
                                        <div class='font14 color3 fontBold'>{{affirmRecordList.stat.inSchoolSuspensionNum}}</div>
                                        <div class='mt4 font12 color3'><?php echo Yii::t("referral", "In school suspension"); ?></div>
                                    </div>
                                    <div class='flex1 text-center'>
                                        <div class='font14 color3 fontBold'>{{affirmRecordList.stat.outSchoolSuspensionNum}}</div>
                                        <div class='mt4 font12 color3'><?php echo Yii::t("referral", "Out of school suspension"); ?></div>
                                    </div>
                                    <div class='flex1 text-center'>
                                        <div class='font14 color3 fontBold'>{{affirmRecordList.stat.serviceNum}}</div>
                                        <div class='mt4 font12 color3'><?php echo Yii::t("referral", "Community Service"); ?></div>
                                    </div>
                                </div>
                            </div>
                            <div class='col-md-5 borderLeft'>
                                <div class='font12 color6 mb12 pl16'><?php echo Yii::t("report", "Other"); ?>：{{affirmRecordList.stat.otherNum}}</div>
                                <div class='flex mt12'>
                                    <div class='flex1 text-center'>
                                        <div class='font14 color3 fontBold'>{{affirmRecordList.stat.undeterminedNum}}</div>
                                        <div class='mt4 font12 color3'><?php echo Yii::t("referral", "NOT a behavior issue"); ?></div>
                                    </div>
                                    <div class='flex1 text-center'>
                                        <div class='font14 color3 fontBold'>{{affirmRecordList.stat.recordOnlyNum}}</div>
                                        <div class='mt4 font12 color3'><?php echo Yii::t("referral", "Record only"); ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-if='affirmRecordList.stat.problemBehavior.length!=0' class='problemList mt16 mb16'>
                            <div class='problemTitle color6'><?php echo Yii::t("referral", "Problem Behavior"); ?></div>
                            <div>
                                <div v-for='(list,key,index) in affirmRecordList.stat.problemBehavior'>
                                    <div class='flex circleFlex' >
                                        <span class="circle"></span>
                                        <div class='flex flex1 align-items'>
                                            <div class='flex1  mr20'>{{showTitle(key,'problem_behavior')}} </div>
                                            <span class="badgeNum">{{list}}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-for='(item,idx) in affirmRecordList.list' class='bgGrey p20 mt12 relative font14'>
                            <div>
                                <div class='flex font14'>
                                    <div class='flex1 color3 '><span class='cur-p blueHover' @click='showDetails(item.behavior_id,"show")'>No.{{item.id}} <span class='el-icon-arrow-right'></span></span> </div>  
                                    <span class='behavior-title color6 labelRecord' v-if='item.result.identified==3'><?php echo Yii::t("referral", "Record only, no followup needed"); ?></span>
                                    <span class='behavior-title'v-else :class='item.result.identified==1?"labelRed":"labelGreen"'>{{showTitle(item.result.identified,'fact')}}</span>
                                </div>
                                <div class='color6 font12 mt16'>{{item.desc}}</div>
                                <div class='flex align-items mt12'>
                                    <img :src="affirmRecordList.staff_info[item.result.created_by].photoUrl" data-holder-rendered="true" class="avatar32">
                                    <span class='ml10 color3 font12'>{{affirmRecordList.staff_info[item.result.created_by].name}}</span>
                                    <span class='color9 font12'>｜</span>
                                    <div class='flex1 color3 font12'>{{item.result.created_at}}</div>
                                </div>
                                <div class='flex mt12 font12'  v-if='item.result.identified!=3'>
                                    <span class='color9  '><?php echo Yii::t("referral", "Final Result"); ?>：</span>
                                    <div class='flex1 color3'>
                                        <span class='mr10' v-for='(list,index) in item.result.actions'>{{showTitle(list,'actions')}}</span>
                                        <span v-if='item.followup.date'>{{item.followup.date[0]}} ~ {{item.followup.date[1]}}</span>
                                    </div>
                                </div>
                                <div class='flex mt12 font12' v-if='item.result.identified!=3'>
                                    <span class='color9  '><?php echo Yii::t("referral", "Comment"); ?>：</span>
                                    <div class='flex1 color3' v-html='htmlBr(item.result.remark)'></div>
                                </div>
                                <div class='flex mt12 font12' v-if='item.result.attachments && (item.result.attachments.img || item.result.attachments.other)'>
                                    <span class='color9  '><?php echo Yii::t("curriculum", "Attachments"); ?>：</span>
                                    <div class='flex1 color3'>
                                        <div class=''>
                                            <ul class='mb12 imgLi'  id='record'  v-if='item.result.attachments.img'>
                                                <li v-for='(list,i) in item.result.attachments.img'>
                                                    <img :src="affirmRecordList.attachmentList[list].file_key" class='imgList mb8 mr8' @click='showImg("record",item.result.attachments.img)' alt=""  >
                                                </li>
                                            </ul>
                                        </div>
                                        <div class='mt12 color3' v-if='item.result.attachments.other'>
                                            <div v-for='(list,j) in item.result.attachments.other'>
                                                <div class='flex fileLink' >
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='affirmRecordList.attachmentList[list].mime_type=="application/pdf"' />
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='affirmRecordList.attachmentList[list].mime_type=="application/vnd.ms-excel" || affirmRecordList.attachmentList[list].mime_type=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='affirmRecordList.attachmentList[list].mime_type=="application/msword" || affirmRecordList.attachmentList[list].mime_type=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='affirmRecordList.attachmentList[list].mime_type=="application/x-zip-compressed"' />
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                                    <a class='flex1 ml5' target= "_blank" :href="list.url">{{affirmRecordList.attachmentList[list].title}}
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-if='behaviorTabDetail=="good"'>
                        <div v-if='affirmRecordList.positive.length!=0'>
                            <div v-for='(item,idx) in affirmRecordList.positive' class='bgGrey p20 mt12 relative font14'>
                                <div class='flex font14'>
                                    <div class='flex1 color3 '><span class='cur-p blueHover' @click='showDetails(item.behavior_id,"show")'>No.{{item.id}} <span class='el-icon-arrow-right'></span></span> </div> 
                                    <span class='behavior-title font12 labelBlue'><?php echo Yii::t("behavior", "Positive Behavior"); ?></span>
                                </div>
                                <div class='color6 font12 mt16'>{{item.desc}}</div>
                                <div class='flex align-items mt12'>
                                    <img :src="affirmRecordList.staff_info[item.created_by].photoUrl" data-holder-rendered="true" class="avatar32">
                                    <span class='ml10 color3 font12'>{{affirmRecordList.staff_info[item.created_by].name}}</span>
                                    <span class='color9 font12'>｜</span>
                                    <div class='flex1 color3 font12'>{{item.created_at}}</div>
                                </div>
                            </div>
                        </div>
                        <div v-else><el-empty description="<?php echo Yii::t("ptc", "No Data"); ?>"></el-empty></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
     <!-- 详情 -->
     <div class="modal fade" id="contentModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static" style="overflow-y: auto;">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <div class='flex'>
                        <div class='flex1'>
                            <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "Detail"); ?>
                            <!-- <span class='text-primary cur-p font14 fontWeightNormal ml10' @click='allSendMessage()'>
                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/share.png' ?>" alt="" width='16' height='16'>
                                <span class='ml4 '><?php echo Yii::t("referral", "Share To"); ?></span>
                            </span> -->
                            </h4>
                        </div>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close" ><span aria-hidden="true">&times;</span></button>
                    </div>
                </div>
                <div class="modal-body p0 relative" v-if='contentList.behavior'>
                    <div><?php $this->renderPartial("detail", array('type' => 'processing'));?></div>
                    <div class='clearfix'></div>
                </div>
            </div>
        </div>
    </div>
      <!-- 认定非行为问题和最终处理 -->
      <div class="modal fade"  id="finalResultModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel"  aria-labelledby="wantSayModalLabel"  data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">
                    <span v-if='processType=="" && (resultType=="isp" || resultType=="grade"  || resultType=="subject")'><?php echo Yii::t("referral", "It is not a behavior issue"); ?></span>
                    <span v-if='resultType=="office"|| processType!=""'><?php echo Yii::t("referral", "Final Decision"); ?></span></h4>
            </div>
            <div class="modal-body p24">
                <div class='font14'>
                    <div v-if='resultType=="office"|| processType!=""'>       
                        <div  v-if='contentList.is_office'>
                            <div class='flex align-items'>
                                <span class='fontBold color3 flex1'><?php echo Yii::t("ptc", "Student"); ?></span> 
                                <template>
                                    <el-select
                                            v-model="stuVal"
                                            filterable
                                            remote
                                            size='small'
                                            clearable
                                            class='inline-input flex1 formControl'
                                            reserve-keyword
                                            placeholder="<?php echo Yii::t("ptc", "Add student"); ?>"
                                            :remote-method="stuRemoteMethod"
                                            prefix-icon="el-icon-search"
                                            @change='addStu'
                                            :loading="loading">
                                        <el-option
                                                v-for="item in stuOption"
                                                :key="item.id"
                                                :label="item.name"
                                                class='optionSearch mb8'
                                                :value="item.id">
                                            <div class="media">
                                                <div class="media-left pull-left media-middle">
                                                    <a href="javascript:void(0)">
                                                        <img :src="item.avatar" data-holder-rendered="true" class="media-object img-circle avatar">
                                                    </a>
                                                </div>
                                                <div class="media-body mt5 media-middle">
                                                    <h4 class="media-heading font14 color3 text_overflow">{{item.name}}</h4>
                                                    <div class="text-muted text_overflow font12">{{ item.class_name }}</div>
                                                </div>
                                            </div>
                                        </el-option>
                                    </el-select>
                                </template>
                            </div>
                            <div class='flex align-items waitingColor'><span class='glyphicon glyphicon-info-sign'></span> <span class='ml5'><?php echo Yii::t("referral", "Different result should be processed one by one."); ?></span> </div>
                            <div  class='mt8'>
                                <div class='row' v-if='contentList.behavior'>
                                    <div class='col-md-6 mt10' v-for='(list,index) in contentList.behavior.child'>
                                        <div class='bgGrey p8 height65'>
                                            <label class="checkbox-inline flex">
                                                <input type="checkbox"  :value="list.id" class='mt16' v-model='resultData.student_ids'> 
                                                <div class="media flex1 m0 authorizedMedia" >
                                                    <div class="media-left pull-left media-middle">
                                                        <a href="javascript:void(0)">
                                                            <img :src="contentList.student_info_list[list.id].avatar" data-holder-rendered="true" class="avatar">
                                                        </a>
                                                    </div>
                                                    <div class="media-body media-middle">
                                                        <div class="lineHeight20  text-primary">
                                                            <span class='font14 color3 nowrap'>{{contentList.student_info_list[list.id].name}} </span>
                                                            <span v-if='list.tag==2 || list.tag==3' class='glyphicon glyphicon-bookmark font12' :class='list.tag==2?"waitingColor":list.tag==3?"colorRed":""'></span>
                                                            <!-- <span :class='list.tag==2?"tagLabelYellow":list.tag==3?"tagLabel":""'>{{showTitle(list.tag,'ISP_type')}}</span> -->
                                                        </div>
                                                        <div class="font12 color6 nowrap">{{contentList.student_info_list[list.id].className}}</div>
                                                    </div>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                    <div class='col-md-6 mt10' v-for='(list,index) in addChildList' :key='list.id'>
                                        <div class='bgGrey p8 height65'>
                                            <label class="checkbox-inline flex">
                                                <input type="checkbox"  :value="list.id" class='mt16' v-model='resultData.student_ids'> 
                                                <div class="media flex1 m0 authorizedMedia" >
                                                    <div class="media-left pull-left media-middle">
                                                        <a href="javascript:void(0)">
                                                            <img :src="list.avatar" data-holder-rendered="true" class="avatar">
                                                        </a>
                                                    </div>
                                                    <div class="media-right pull-right mt15 text-right">
                                                        <span class='el-icon-circle-close font16' @click='delChild(index,list.id)'></span>
                                                    </div>
                                                    <div class="media-body media-middle">
                                                        <div class="lineHeight20  text-primary">
                                                            <span class='font14 color3 nowrap'>{{list.name}} </span>
                                                            <span v-for='(item,ind) in list.label' >
                                                                <span v-if='item.flag==2 || item.flag==3' class='glyphicon glyphicon-bookmark font12' :class='item.flag==2?"waitingColor":item.flag==3?"colorRed":""'></span>
                                                                <!-- <span v-if='item.flag==2 || item.flag==3' :class='item.flag==2?"tagLabelYellow":item.flag==3?"tagLabel":""'>{{item.name}}</span> -->
                                                            </span>
                                                        </div>
                                                        <div class="font12 color6 nowrap">{{list.className}}</div>
                                                    </div>
                                                    
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                    
                                </div>
                            </div>
                        </div>    
                        <div v-else>
                            <div class='flex align-items'>
                                <span class='fontBold color3 flex1'><?php echo Yii::t("ptc", "Student"); ?></span> 
                            </div>
                            <div class='row' v-if='contentList.behavior'>
                                <div class='col-md-6 mt10' v-for='(list,index) in contentList.behavior.child'>
                                    <div class='bgGrey p8 height65'>
                                        <label class="checkbox-inline flex">
                                            <div class="media flex1 m0 authorizedMedia" >
                                                <div class="media-left pull-left media-middle">
                                                    <a href="javascript:void(0)">
                                                        <img :src="contentList.student_info_list[list.id].avatar" data-holder-rendered="true" class="avatar">
                                                    </a>
                                                </div>
                                                <div class="media-body media-middle">
                                                    <div class="lineHeight20  text-primary">
                                                        <span class='font14 color3 nowrap'>{{contentList.student_info_list[list.id].name}} </span>
                                                        <span v-if='list.tag==2 || list.tag==3' class='glyphicon glyphicon-bookmark font12' :class='list.tag==2?"waitingColor":list.tag==3?"colorRed":""'></span>
                                                        <!-- <span :class='list.tag==2?"tagLabelYellow":list.tag==3?"tagLabel":""'>{{showTitle(list.tag,'ISP_type')}}</span> -->
                                                    </div>
                                                    <div class="font12 color6 nowrap">{{contentList.student_info_list[list.id].className}}</div>
                                                </div>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>   
                        <div class='fontBold color3 mt24'><?php echo Yii::t("referral", "Decision"); ?></div>
                        <div  class='mt8'>
                            <label class="radio-inline" v-for='(list,index) in config.fact'>
                                <input type="radio" name="inlineRadioOptions" v-model='resultData.identified' :value="list.value"> {{list.title}}
                            </label>
                        </div>
                    </div>
                    <div  v-else-if='processType=="" && (resultType=="isp" || resultType=="grade"  || resultType=="subject") '>
                        <div class='flex align-items'>
                            <span class='fontBold color3 flex1'><?php echo Yii::t("ptc", "Student"); ?></span> 
                        </div>
                        <div class='row'>
                        <div class='col-md-6 mt10' v-for='(list,index) in contentList.behavior.child'>
                            <div class='height65'>
                                <div class="media flex1 m0 authorizedMedia" >
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                            <img :src="contentList.student_info_list[list.id].avatar" data-holder-rendered="true" class="avatar">
                                        </a>
                                    </div>
                                    <div class="media-body media-middle">
                                        <div class="lineHeight20  text-primary">
                                            <span class='font14 color3 nowrap'>{{contentList.student_info_list[list.id].name}} </span>
                                            <span v-if='list.tag==2 || list.tag==3' class='glyphicon glyphicon-bookmark font12' :class='list.tag==2?"waitingColor":list.tag==3?"colorRed":""'></span>
                                            <!-- <span :class='list.tag==2?"tagLabelYellow":list.tag==3?"tagLabel":""'>{{showTitle(list.tag,'ISP_type')}}</span> -->
                                        </div>
                                        <div class="font12 color6 nowrap">{{contentList.student_info_list[list.id].className}}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        </div>
                        <div class='fontBold color3'><?php echo Yii::t("referral", "Decision"); ?></div>
                        <div  class='mt8'>
                            <div class='flex1 color3'><span class="label label-success" ><?php echo Yii::t("referral", "NOT a behavior issue"); ?></span></div>
                        </div>
                    </div>
                    <div>   
                        <div class='fontBold color3 mt24'><?php echo Yii::t("referral", "Final Result"); ?></div>
                        <div class='row'>
                            <div class='col-md-3 col-sm-12'  v-for='(list,index) in actionsList'>
                                <div class="checkbox m0 mt5" >
                                    <label>
                                        <input type="checkbox" v-model='resultData.actions'  :value="list.value" >{{list.title}}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class='fontBold color3 mt24'><?php echo Yii::t("referral", "Contact parents"); ?></div>
                    <div class='row'>
                        <div class='col-md-3 col-sm-12'  v-for='(list,index) in config.contact_parents'>
                            <div class="checkbox m0 mt5" >
                                <label>
                                    <input type="checkbox" v-model='resultData.contact_parents'  :value="list.value" >{{list.title}}
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class='otherBg mt10' v-if='showIndexOf("actions")'>
                        <input type="text" class="form-control" placeholder="<?php echo Yii::t("report", "Other"); ?>"  v-model='resultData.actions_other' >
                    </div>
                    <div class='fontBold color3 mt24'><?php echo Yii::t("referral", "Time in Office"); ?></div>
                    <div class='mt8'>
                        <el-time-picker
                            v-model="resultData.durationTime"
                            value-format="H:m"
                            :format="pickerFormatText"
                            placement="bottom-start"
                            placeholder="<?php echo Yii::t("referral", "Time in Office"); ?>">
                        </el-time-picker>
                    </div>
                    <div class='fontBold color3 mt24'><?php echo Yii::t("referral", "Comment"); ?></div>
                    <div  class='mt8'><textarea class="form-control" placeholder="<?php echo Yii::t("teaching", "Input"); ?>" v-model='resultData.remark' rows="3"></textarea></div>
                    <div class='flex mb10 align-items mt24 '>
                        <div class='fontBold color3 font14'><?php echo Yii::t("curriculum", "Attachments"); ?></div>
                        <div class='flex1 ml10'>
                            <el-button type="text" icon="el-icon-circle-plus-outline" id='finalResultAddFile' class='font14 bluebg'><?php echo Yii::t("referral", "Upload"); ?></el-button>
                        </div>
                    </div>
                    <div>
                        <div class='' v-if='attachments.img.length>0'>
                            <div class='imgData mr8'  v-for='(list,i) in attachments.img'>
                                <div v-if="list.types=='1'">
                                    <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Uploading");?></div>
                                </div>
                                <div v-else>
                                    <img class='imgList' @click='uploadShowImg(list.file_key)'  :src="list.file_key" alt="">
                                    <span aria-hidden="true" class='closeImg'  @click.stop='delImg("img",list,i)'>×</span>
                                </div>
                            </div>
                            <template v-if='loadingType==1'>
                                <div class='imgData mr8'  v-for='(list,i) in loadingList'>
                                    <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Upload queue");?></div>
                                </div>
                            </template>
                        </div>
                        <div>
                            <div class='mt16' v-if='attachments.other.length>0'>
                                <div class='flex uploadFile' v-for='(list,index) in attachments.other'>
                                    <span class='glyphicon glyphicon-paperclip mr8'></span>
                                    <a target="_blank" class='flex1'  style='line-height:26px' :href='list.file_key' v-if='!list.isEdit'>{{list.title}}</a>
                                    <span class='flex1' v-else><input type="input" class='inputStyle'  @keyup.enter="saveFile(list,index)"  v-model='attachmentName' ></span>
                                    <span v-if="list.types=='1'"></span>
                                    <template v-else>
                                        <template v-if='!list.isEdit'>
                                            <span class='glyphicon glyphicon-edit icon mr16' v-if='list.file_key!=""' @click.stop='list.isEdit=true,attachmentName=list.title'></span>
                                            <span class='glyphicon glyphicon-trash icon' v-if='list.file_key!=""' @click.stop='delImg("link",list,index)'></span>
                                        </template>
                                        <span style='width:90px'  class='text-right inline-block' v-else>
                                            <button type="button" class="btn btn-primary btn-xs" @click='saveFile(list,index)'><?php echo Yii::t("global", "Save");?></button>
                                            <button type="button" class="btn btn-default btn-xs" @click='list.isEdit=false'><?php echo Yii::t("global", "Cancel");?></button>
                                        </span>
                                    </template>
                                </div>
                            </div>
                            <template v-if='loadingType==2'>
                                <div class='flex uploadFile'  v-for='(list,i) in loadingList'>
                                    <span class='glyphicon glyphicon-paperclip mr8'></span>
                                    <a  class='flex1'  style='line-height:26px' > <?php echo Yii::t("directMessage", "Upload queue");?></a>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                <button type="button" class="btn btn-primary"  v-if='resultType=="office"' @click='saveOffice("result")'><?php echo Yii::t("global", "OK"); ?></button>
                <button type="button" class="btn btn-primary" v-if='resultType=="isp"' @click='saveTeacher("result")'><?php echo Yii::t("global", "OK"); ?></button>
                <button type="button" class="btn btn-primary" v-if='resultType=="grade"' @click='saveGrade("result")'><?php echo Yii::t("global", "OK"); ?></button>
                <button type="button" class="btn btn-primary" v-if='resultType=="subject"' @click='saveSubject("result")'><?php echo Yii::t("global", "OK"); ?></button>
            </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
    <!-- 重新认定 和 认定其他学生-->
    <div class="modal fade"  id="afreshResultModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel"  aria-labelledby="wantSayModalLabel"  data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">{{editStatus==2?"<?php echo Yii::t("referral", "Add other student"); ?>":"<?php echo Yii::t("referral", "Reset"); ?>"}}</h4>
            </div>
            <div class="modal-body p24 font14">
                <div v-if='editStatus!=0'>
                    <div v-if='editStatus==2'>
                        <div class='flex align-items'>
                            <span class='fontBold color3 flex1'><?php echo Yii::t("ptc", "Student"); ?></span> 
                            <template>
                                <el-select
                                        v-model="stuVal"
                                        filterable
                                        size='small'
                                        remote
                                        clearable
                                        class='inline-input flex1 formControl'
                                        reserve-keyword
                                        placeholder="<?php echo Yii::t("ptc", "Add student"); ?>"
                                        :remote-method="stuRemoteMethod"
                                        prefix-icon="el-icon-search"
                                        @change='addStu'
                                        :loading="loading">
                                    <el-option
                                            v-for="item in stuOption"
                                            :key="item.id"
                                            :label="item.name"
                                            class='optionSearch mb8'
                                            :value="item.id">
                                        <div class="media">
                                            <div class="media-left pull-left media-middle">
                                                <a href="javascript:void(0)">
                                                    <img :src="item.avatar" data-holder-rendered="true" class="media-object img-circle avatar">
                                                </a>
                                            </div>
                                            <div class="media-body mt5 media-middle">
                                                <h4 class="media-heading font14 color3 text_overflow">{{item.name}}</h4>
                                                <div class="text-muted text_overflow font12">{{ item.class_name }}</div>
                                            </div>
                                        </div>
                                    </el-option>
                                </el-select>
                            </template>
                        </div>
                        <div class='flex align-items waitingColor'><span class='glyphicon glyphicon-info-sign'></span> <span class='ml5'><?php echo Yii::t("referral", "Different result should be processed one by one."); ?></span> </div>
                        <div  class='mt8'>
                            <div class='row' v-if='contentList.behavior'>
                                <div class='col-md-6 mt10' v-for='(list,index) in contentList.behavior.child'>
                                    <div class='bgGrey p8 height65'>
                                        <label class="checkbox-inline flex">
                                            <input type="checkbox"   class='mt16' v-if='showDisabled(list.id)' disabled='true' > 
                                            <input type="checkbox"  :value="list.id" class='mt16' v-if='!showDisabled(list.id)' v-model='officeData.student_ids'> 
                                            <div class="media flex1 m0 authorizedMedia" >
                                                <div class="media-left pull-left media-middle">
                                                    <a href="javascript:void(0)">
                                                        <img :src="contentList.student_info_list[list.id].avatar" data-holder-rendered="true" class="avatar">
                                                    </a>
                                                </div>
                                                <div class="media-right pull-right mt15 text-right">
                                                    <span class='color6' v-if='showDisabled(list.id)'><?php echo Yii::t("referral", "Processed"); ?></span>
                                                </div>
                                                <div class="media-body media-middle">
                                                    <div class="lineHeight20  text-primary">
                                                        <span class='font14 color3 nowrap'>{{contentList.student_info_list[list.id].name}} </span>
                                                        <span v-if='list.tag==2 || list.tag==3' class='glyphicon glyphicon-bookmark font12' :class='list.tag==2?"waitingColor":list.tag==3?"colorRed":""'></span>
                                                    </div>
                                                    <div class="font12 color6 nowrap">{{contentList.student_info_list[list.id].className}}</div>
                                                </div>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                                <div class='col-md-6 mt10' v-for='(list,index) in addChildList' :key='list.id'>
                                    <div class='bgGrey p8 height65'>
                                        <label class="checkbox-inline flex">
                                            <input type="checkbox"  :value="list.id" class='mt16' v-model='officeData.student_ids'> 
                                            <div class="media flex1 m0 authorizedMedia" >
                                                <div class="media-left pull-left media-middle">
                                                    <a href="javascript:void(0)">
                                                        <img :src="list.avatar" data-holder-rendered="true" class="avatar">
                                                    </a>
                                                </div>
                                                <div class="media-right pull-right mt15 text-right">
                                                    <span class='el-icon-circle-close font16' @click='delChild(index,list.id)'></span>
                                                </div>
                                                <div class="media-body media-middle">
                                                    <div class="lineHeight20  text-primary">
                                                        <span class='font14 color3 nowrap'>{{list.name}} </span>
                                                        <span v-for='(item,ind) in list.label' >
                                                            <span v-if='item.flag==2 || item.flag==3' class='glyphicon glyphicon-bookmark font12' :class='item.flag==2?"waitingColor":item.flag==3?"colorRed":""'></span>
                                                        </span>
                                                    </div>
                                                    <div class="font12 color6 nowrap">{{list.className}}</div>
                                                </div>
                                                
                                            </div>
                                        </label>
                                    </div>
                                </div>
                                
                            </div>
                        </div>
                    </div>
                    <div  v-if='editStatus==1'>
                        <div class='row' v-if='reaffData.child'>
                            <div class='col-md-6 mt10'>
                                <div class="media flex1 m0" >
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                            <img :src="contentList.student_info_list[reaffData.child.id].avatar" data-holder-rendered="true" class="avatar">
                                        </a>
                                    </div>
                                    <div class="media-body pt4 media-middle">
                                        <div class="lineHeight20  text-primary">
                                            <span class='font14 color3 nowrap'>{{contentList.student_info_list[reaffData.child.id].name}} </span>
                                        </div>
                                        <div class="font12 color6 nowrap">{{contentList.student_info_list[reaffData.child.id].className}}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class='fontBold color3 mt24'><?php echo Yii::t("referral", "Decision"); ?></div>
                    <div  class='mt8'>
                        <div  >
                        <label class="radio-inline" v-for='(list,index) in config.fact'>
                            <input type="radio" name="inlineRadioOptions" v-model='officeData.identified' :value="list.value"> {{list.title}}
                        </label>
                        </div>
                    </div>
                    <div>                    
                        <div class='fontBold color3 mt24'><?php echo Yii::t("referral", "Final Result"); ?></div>
                        <div class='row'>
                            <div class='col-md-3 col-sm-12'  v-for='(list,index) in actionsList'>
                                <div class="checkbox m0 mt5" >
                                    <label>
                                        <input type="checkbox" v-model='officeData.actions'  :value="list.value" >{{list.title}}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class='fontBold color3 mt24'><?php echo Yii::t("referral", "Contact parents"); ?></div>
                    <div class='row'>
                        <div class='col-md-3 col-sm-12'  v-for='(list,index) in config.contact_parents'>
                            <div class="checkbox m0 mt5" >
                                <label>
                                    <input type="checkbox" v-model='officeData.contact_parents'  :value="list.value" >{{list.title}}
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class='otherBg mt10' v-if='showIndexOf("actions")'>
                        <input type="text" class="form-control" placeholder="<?php echo Yii::t("report", "Other"); ?>"  v-model='officeData.actions_other' >
                    </div>
                    <div class='fontBold color3 mt24'><?php echo Yii::t("referral", "Time in Office"); ?></div>
                    <div class='mt8'>
                        <el-time-picker
                            v-model="officeData.durationTime"
                            value-format="H:m"
                            @input="$forceUpdate()"
                            :format="pickerFormatText"
                            placement="bottom-start"
                            placeholder="<?php echo Yii::t("referral", "Time in Office"); ?>">
                        </el-time-picker>
                    </div>
                    <div class='fontBold color3 mt24'><?php echo Yii::t("referral", "Comment"); ?></div>
                    <div  class='mt8'><textarea class="form-control" placeholder="<?php echo Yii::t("teaching", "Input"); ?>" v-model='officeData.remark' rows="3"></textarea></div>
                    <div class='flex mb10 align-items mt24'>
                        <div class='fontBold color3 font14'><?php echo Yii::t("curriculum", "Attachments"); ?></div>
                        <div class='flex1 ml10'>
                            <el-button type="text" icon="el-icon-circle-plus-outline" id='afreshAddFile' class='font14 bluebg'><?php echo Yii::t("referral", "Upload"); ?></el-button>

                        </div>
                    </div>
                    <div>
                        <div class='' v-if='attachments.img && attachments.img.length>0'>
                            <div class='imgData mr8'  v-for='(list,i) in attachments.img'>
                                <div v-if="list.types=='1'">
                                    <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Uploading");?></div>
                                </div>
                                <div v-else>
                                    <img class='imgList' @click='uploadShowImg(list.file_key)'  :src="list.file_key" alt="">
                                    <span aria-hidden="true" class='closeImg' @click.stop='delImg("img",list,i)'>×</span>
                                </div>
                            </div>
                            <template v-if='loadingType==1'>
                                <div class='imgData mr8'  v-for='(list,i) in loadingList'>
                                    <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Upload queue");?></div>
                                </div>
                            </template>
                        </div>
                        <div>
                            <div class='mt16' v-if='attachments.other && attachments.other.length>0'>
                                <div class='flex uploadFile' v-for='(list,index) in attachments.other'>
                                    <span class='glyphicon glyphicon-paperclip mr8'></span>
                                    <a target="_blank" class='flex1'  style='line-height:26px' :href='list.file_key' v-if='!list.isEdit'>{{list.title}}</a>
                                    <span class='flex1' v-else><input type="input" class='inputStyle'  @keyup.enter="saveFile(list,index)"  v-model='attachmentName' ></span>
                                    <span v-if="list.types=='1'"></span>
                                    <template v-else>
                                        <template v-if='!list.isEdit'>
                                            <span class='glyphicon glyphicon-edit icon mr16' v-if='list.file_key!=""' @click.stop='list.isEdit=true,attachmentName=list.title'></span>
                                            <span class='glyphicon glyphicon-trash icon' v-if='list.file_key!=""' @click.stop='delImg("link",list,index)'></span>
                                        </template>
                                        <span style='width:90px'  class='text-right inline-block' v-else>
                                            <button type="button" class="btn btn-primary btn-xs" @click='saveFile(list,index)'><?php echo Yii::t("global", "Save");?></button>
                                            <button type="button" class="btn btn-default btn-xs" @click='list.isEdit=false'><?php echo Yii::t("global", "Cancel");?></button>
                                        </span>
                                    </template>
                                </div>
                            </div>
                            <template v-if='loadingType==2'>
                                <div class='flex uploadFile'  v-for='(list,i) in loadingList'>
                                    <span class='glyphicon glyphicon-paperclip mr8'></span>
                                    <a  class='flex1'  style='line-height:26px' > <?php echo Yii::t("directMessage", "Upload queue");?></a>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                <button type="button" class="btn btn-primary" @click='afreshOffice()' v-if='editStatus==2'><?php echo Yii::t("global", "OK"); ?></button>
                <button type="button" class="btn btn-primary" @click='afreshReaff()' v-if='editStatus==1'><?php echo Yii::t("global", "OK"); ?></button>
            </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
     <!-- 更多学生 -->
     <div class="modal fade" id="moreChildListModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("referral", "More"); ?></h4>
                </div>
                <div class="modal-body p24  font14" v-if='moreChildList.child_data'>
                    <div class='mb24 text-center'>
                        <el-input
                            class='length_5'
                            size='small'
                            placeholder="<?php echo Yii::t("global", "Search"); ?>"
                            prefix-icon="el-icon-search"
                            v-model="searchChildVal">
                        </el-input>
                    </div>
                    <div class='row overflow-y scroll-box' v-if='childSearchData.length!=0'  :style="'max-height:'+(height)+'px;overflow-x: hidden;'">
                        <div class='col-md-6' v-for='(list,index) in childSearchData'>
                            <div class='moreChild' @click='identifiedList(list,"child")'>
                                <div class="media" >
                                    <div class="media-left pull-left media-middle relative">
                                        <img :src="moreChildList.child_data[list.child_id].avatar" data-holder-rendered="true" class="avatar">
                                    </div> 
                                    <div class="pull-left media-middle">
                                        <div class="font14 color3"> {{moreChildList.child_data[list.child_id].name}}
                                            <span v-for='(item,ind) in moreChildList.child_data[list.child_id].label' >
                                                <span v-if='item.flag==2 || item.flag==3' class='glyphicon glyphicon-bookmark font12' :class='item.flag==2?"waitingColor":item.flag==3?"colorRed":""'></span>
                                                <!-- <span v-if='item.flag==2 || item.flag==3' :class='item.flag==2?"tagLabelYellow":item.flag==3?"tagLabel":""'>{{item.name}}</span> -->
                                            </span>
                                        </div> 
                                        <div class="color6 font12">{{moreChildList.child_data[list.child_id].className}}</div>
                                    </div>
                                </div>
                                <div class='flex mt12'>
                                    <div class='flex1 text-center'>
                                        <div class='font14 color3 fontBold'>{{list.behaviorNum}}</div>
                                        <div class='mt4 font12 color6'><?php echo Yii::t("referral", "Behavior Submission Entries"); ?></div>
                                    </div>
                                    <div class='flex1 text-center'>
                                        <div class='font14 color3 fontBold'>{{list.notBehaviorNum}}</div>
                                        <div class='mt4 font12 color6'><?php echo Yii::t("referral", "NOT a behavior issue"); ?>/ <?php echo Yii::t("referral", "Record only"); ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-else class='p24 text-center font14 color3'><?php echo Yii::t("ptc", "No Data"); ?></div>
                </div>
            </div>
        </div>
    </div>
     <!-- 更多班级 -->
     <div class="modal fade" id="moreClassListModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("referral", "More"); ?></h4>
                </div>
                <div class="modal-body p0 overflow-y scroll-box font14" :style="'max-height:'+(height)+'px;overflow-x: hidden;'" >
                    <div class='flex' v-if='initList.warning'>
                        <div class='p16 warningClass'>
                            <el-switch
                                class='ml10'
                                style="display: block"
                                v-model="classWarning"
                                active-color="#4D88D2"
                                inactive-color="#4D88D2"
                                active-text="<?php echo Yii::t("reg", "student"); ?>"
                                inactive-text="<?php echo Yii::t("reg", "student-time"); ?>"
                                active-value="2"
                                inactive-value="1">
                            </el-switch>
                            <div class=' mt20' v-if='classWarning=="1"'>
                                <div v-for='(list,index) in initList.warning.classWarning1' :class='moreClassid==list.class_id?"warningClassListCheck":""' class='flex warningClassList' @click='classMore(list.class_id)'>
                                    <span class='flex1'>{{initList.warning.class_data[list.class_id].title}} </span> 
                                    <span class='badgeNum'>{{list.num}}</span>
                                </div>
                            </div>
                            <div class=' mt20'  v-if='classWarning=="2"'>
                                <div v-for='(list,index) in initList.warning.classWarning2' :class='moreClassid==list.class_id?"warningClassListCheck":""' class='flex warningClassList' @click='classMore(list.class_id)'>
                                    <span class='flex1'>{{initList.warning.class_data[list.class_id].title}} </span> 
                                    <span class='badgeNum'>{{list.num}}</span>
                                </div>
                            </div>
                        </div>
                        <div class='flex1 p24' >
                            <div class='moreChild' v-for='(list,index) in moreClassList.list' @click='identifiedList(list,"class")'>
                                <div class="media" >
                                    <div class="media-left pull-left media-middle relative">
                                        <img :src="moreClassList.child_data[list.child_id].avatar" data-holder-rendered="true" class="avatar">
                                    </div> 
                                    <div class="pull-left media-middle">
                                        <div class="font14 color3"> {{moreClassList.child_data[list.child_id].name}}
                                            <span v-for='(item,ind) in moreClassList.child_data[list.child_id].label' >
                                                <span v-if='item.flag==2 || item.flag==3' class='glyphicon glyphicon-bookmark font12' :class='item.flag==2?"waitingColor":item.flag==3?"colorRed":""'></span>
                                                <!-- <span v-if='item.flag==2 || item.flag==3' :class='item.flag==2?"tagLabelYellow":item.flag==3?"tagLabel":""'>{{item.name}}</span> -->
                                            </span>
                                        </div> 
                                        <div class="color6 font12">{{moreClassList.child_data[list.child_id].className}}</div>
                                    </div>
                                </div>
                                <div class='flex mt12'>
                                    <div class='flex1 text-center'>
                                        <div class='font14 color3 fontBold'>{{list.behaviorNum}}</div>
                                        <div class='mt4 font12 color6'><?php echo Yii::t("referral", "Behavior Submission Entries"); ?></div>
                                    </div>
                                    <div class='flex1 text-center'>
                                        <div class='font14 color3 fontBold'>{{list.notBehaviorNum}}</div>
                                        <div class='mt4 font12 color6'><?php echo Yii::t("referral", "NOT a behavior issue"); ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
     <!-- 删除确认框 -->
     <div class="modal fade" id="delListModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel">
                       <span><?php echo Yii::t("global", "Delete"); ?></span>
                    </h4>
                </div>
                <div class="modal-body" >
                    <div ><?php echo Yii::t("directMessage", "Proceed to remove?"); ?></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" class="btn btn-primary" @click='delDetails("del")'><?php echo Yii::t("message", "OK");?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 筛选学生 -->
    <div class="modal fade" id="filterChildListModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("referral", "Select Student"); ?></h4>
                </div>
                <div class="modal-body p24  font14" >
                    <div class='mb24 text-center' v-if='filterMoreChildList.list'>
                        <el-input
                            class='length_5'
                            clearable
                            size='small'
                            placeholder="<?php echo Yii::t("global", "Search"); ?>"
                            prefix-icon="el-icon-search"
                            v-model="filterChildListVal">
                        </el-input>
                    </div>
                    <div class='row overflow-y scroll-box' v-if='filterMoreChildList.list && filterChildData.length!=0'  :style="'max-height:'+(height)+'px;overflow-x: hidden;'">
                        <div class='col-md-6' v-for='(list,index) in filterChildData'>
                            <div class='moreChildFilter flex' @click='filterList(list.child_id,filterMoreChildList.info[list.child_id].name,"child")'>
                                <div class="media flex1" >
                                    <div class="media-left pull-left media-middle relative">
                                        <img :src="filterMoreChildList.info[list.child_id].avatar" data-holder-rendered="true" class="avatar">
                                    </div>
                                    <div class="pull-left media-middle">
                                        <div class="font14 color3 mt4"> {{filterMoreChildList.info[list.child_id].name}}
                                            <span v-for='(item,ind) in filterMoreChildList.info[list.child_id].label' >
                                                <span v-if='item.flag==2 || item.flag==3' class='glyphicon glyphicon-bookmark font12' :class='item.flag==2?"waitingColor":item.flag==3?"colorRed":""'></span>
                                                <!-- <span v-if='item.flag==2 || item.flag==3' :class='item.flag==2?"tagLabelYellow":item.flag==3?"tagLabel":""'>{{item.name}}</span> -->
                                            </span>
                                        </div>
                                        <div class="color6 font12">{{filterMoreChildList.info[list.child_id].className}}</div>
                                    </div>
                                </div>
                                <div>
                                    <div class='flex1 text-center'>
                                        <div class='font14 color3 fontBold'>{{list.num}}</div>
                                        <div class='mt4 font12 color6'><?php echo Yii::t("referral", "Record Only"); ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-else class='p24 text-center font14 color3'><?php echo Yii::t("ptc", "No Data"); ?></div>
                </div>
            </div>
        </div>
    </div>
    <!-- 筛选班级 -->
    <div class="modal fade" id="filterClassListModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("referral", "Select Class"); ?></h4>
                </div>
                <div class="modal-body overflow-y scroll-box font14" :style="'max-height:'+(height)+'px;overflow-x: hidden;'">
                    <div class='row' v-if='filterMClassList.list && filterMClassList.list.length!=0'>
                        <div class='col-md-6' v-for='(list,index) in filterMClassList.list'>
                            <div class='classMore flex' @click='filterList(list.class_id,filterMClassList.info[list.class_id],"class")'>
                                <div class="flex1 font14 color6" >
                                        {{filterMClassList.info[list.class_id]}}
                                </div>
                                <div>
                                    <span class="badge">{{list.num}}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-else class='p24 text-center font14 color3'><?php echo Yii::t("ptc", "No Data"); ?></div>
                </div>
            </div>
        </div>
    </div>
    <div><?php $this->renderPartial("sendModal");?></div>
    <div><?php $this->renderPartial("transferModal");?></div>
</div>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    var height=document.documentElement.clientHeight;
    var configList = <?php echo CJSON::encode($config)?>;
    var id_num = <?php echo CJSON::encode($id_num)?>;
    var getGrade = "<?php echo $_GET['grade']?>"
    var need_me = "<?php echo $_GET['needMe']?>"
    var lang='<?php echo Yii::app()->language;?>'
    console.log(configList)
    var container = new Vue({
        el: "#container",
        data: {
            lang:lang,
            search:'',
            height:height-220,
            config:configList,
            teacherUid:'',
            studyTeacherUid:'',
            options:[],
            studyOptions:[],
            loading:false,
            dataList:{},
            contentList:{},
            time:'',
            principal_processing:'',
            remark:'',
            duration:'00:00',
            help:false,
            officeData:{},
            gradeData:{},
            resultData:{},
            ispData:{},
            planData:{},
            viewType:'',
            sendType:'',
            appendComment:'',
            appendText:false,
            refill:false,
            typeData:{},
            detail:{},
            order: "descending",
            orderProp:"created_at",
            pageNum:1,
            itemList:{},
            qiniuToken:'',
            loadingType:0,
            loadingList:[],
            handId:'',
            uploader:[],
            stuVal:'',
            stuOption:[],
            showBehavior:true,
            showStudy:true,
            attachments:{
                img:[],
                other:[]
            },
            addChildList:[],
            resultType:"",
            editStatus:0,
            reaffData:{},
            followItem:{},
            followData:{
                remark:'',
                date:'',
                time:''
            },
            followType:'',
            sendItem:{},
            classTeacher:{},
            teacher_ids:[],
            sendRemark:'',
            checkall:false,
            sendLogList:[],
            affirmRecordList:{},
            affirmData:{},
            forwardType:'',
            selectOptionWidth: null,
            adminForwardType:'',
            classId:'',
            currentYear:'',
            currentGrade_group:'',
            initSchoolYear:{},
            initList:{},
            currentStat:{},
            currentTime: 1,// #1-今日 2-本周 3-本月 4-本学年
            currentProcessing:null, ////1-需要我处理 2-处理中
            moreChildList:{},
            searchChildVal:'',
            moreClassList:{},
            classWarning:'1',
            moreClassid:'',
            indexLoading:false,
            actionsList:[],
            initLoading:false,
            ispForwardType:'',
            GradeForwardType:'',
            behaviorValue:'',
            learningValue:'',
            behaviorTeacher:[],
            learningTeacher:[],
            officeList:{},
            sendTecherOptions:[],
            sendTeacherUid:[],
            needMe:need_me,
            pickerFormatText:"H <?php echo Yii::t("referral", "'Hour'"); ?> m <?php echo Yii::t("referral", "'Minutes'"); ?>",
            subForwardType:'',
            subjectList:[],
            subjectData:{},
            processType:'',
            tableOffice:null,
            delId:'',
            nextNode:{},
            CopyPages:{},
            filterBehaviorValue:'',
            searchName:'',
            lineClamp: 3,
            searchChild:[],
            searchClass:[],
            filterChildListVal:'',
            filterMoreChildList:{},
            filterMClassList:{},
            shouldShowExpandButton: {},
            showHtml:true,
            showMinWidth:'200',
            showPositive:false,
            behaviorTabDetail:'bad',
            maxWidth:false,
            lang:'<?php echo Yii::app()->language ?>'
        },
        watch:{
            // "officeData.student_ids":{
            //     deep:true,
            //     handler(newVal){
            //         if(newVal){
            //             if(newVal.length>0){
            //                 let childAll=this.addChildList.concat(this.contentList.behavior.child)
            //                 let newArr2  = childAll.filter((i) => newVal.indexOf(i.id) !== -1)
            //                 let gradeList=[]
            //                 newArr2.forEach(item => {
            //                     gradeList.push(item.grade)
            //                 });
            //                 this.showActions(Array.from(new Set(gradeList)))
            //             }else{
            //                 this.showActions([])
            //             }
            //             this.officeData.actions=[]
            //         }
                    
            //     }
            // },
            // "resultData.student_ids":{
            //     deep:true,
            //     handler(newVal){
            //         if(newVal){
            //             // if(newVal.length>0){
            //                 // let childAll=this.addChildList.concat(this.contentList.behavior.child)
            //                 // let newArr2  = childAll.filter((i) => newVal.indexOf(i.id) !== -1)
            //                 let gradeList=[this.contentList.behavior.child[0].grade]
            //                 // newArr2.forEach(item => {
            //                 //     gradeList.push(item.grade)
            //                 // });
                            
            //                 this.showActions(Array.from(new Set(gradeList)))
            //             // }else{
            //             //     this.showActions([])
            //             // }
            //             this.resultData.actions=[]
            //         }
                    
            //     }
            // },
            'dataList': {
                handler(newVal) {
                if (newVal && newVal.list) {
                    this.$nextTick(() => {
                        newVal.list.forEach(key => {
                            this.getHeight(key._id);
                        });
                    });
                }
                },
                deep: true
            }
        },
        created: function() {
            let start_year = sessionStorage.getItem('start_year') || '';
            this.initData(start_year)
        },
        computed: {
            childSearchData: function() {
                var search = this.searchChildVal;
                let searchVal = ''; //搜索后的数据
                if(search) {
                    searchVal =Object.values(this.moreChildList.list).filter(function(product) {
                        return Object.keys(product).some(function(key) {
                            return String(product['child_name'].toLowerCase()).indexOf(search.toLowerCase()) !== -1;
                        })
                    })
                    return searchVal;
                }
                return this.moreChildList.list;
            },
            filterChildData: function() {
                var search = this.filterChildListVal;
                let searchVal = ''; //搜索后的数据
                if(search) {
                    searchVal =Object.values(this.filterMoreChildList.list).filter(function(product) {
                        return Object.keys(product).some(function(key) {
                            return String(product['child_name'].toLowerCase()).indexOf(search.toLowerCase()) !== -1;
                        })
                    })
                    return searchVal;
                }
                return this.filterMoreChildList.list;
            },
            textStyleObject () {
                return {
                    'max-height': '6.7em'
                }
            }
        },
        mounted() {
            this.$nextTick(() => {
                this.updateTextBasedOnWidth();
                window.addEventListener('resize', this.updateTextBasedOnWidth);
            });
        },
        beforeDestroy() {
            window.removeEventListener('resize', this.updateTextBasedOnWidth);
        },
        methods: {
            updateTextBasedOnWidth(){
                const width = window.innerWidth;
                if (width < 1250) {
                    this.maxWidth = true;
                } else {
                    this.maxWidth = false;
                }
            },
            getHeight(id) {
                return new Promise((resolve) => {
                    this.$nextTick(() => {
                        const element = $(`.${id}`);
                        const span = $(`.span_${id}`);
                        const flag = element.height() >= 80 && span.height()>80;
                        resolve(flag);
                    });
                    }).then(flag => {
                    this.showHtml=false
                    this.$nextTick(() => { 
                        this.showHtml=true
                        
                        this.shouldShowExpandButton[id] = flag;
                        
                    });
                });
            },
            showActions(list){
                this.actionsList=[]
                let actionsData= JSON.parse(JSON.stringify(this.config.actions));
                if(list.length==0){
                    this.actionsList=actionsData
                    return
                }
                actionsData.forEach(item => {
                    list.forEach(list => {
                        if(item.apply.indexOf(list)!=-1) this.actionsList.push(item)
                    });
                });
                this.actionsList=Array.from(new Set(this.actionsList))
            },
            htmlBr(content){
                if(content!=null){
                    var reg=new RegExp("\n","g");
                    content= content.replace(reg,"<br/>");
                }
                return content
            },
            setOptionWidth(event){
                this.$nextTick(() => {
                    this.selectOptionWidth = event.srcElement.offsetWidth;
                    this.studyOptions=[]
                    this.options=[]
                }); 
            },
            showTitle(data,type){
                for(var i=0;i<this.config[type].length;i++){
                    if(this.config[type][i].value==data){
                        return this.config[type][i].title
                    }
                }
            },
            sort_change(column){
                this.order=column.order
                this.orderProp=column.prop
                if(this.currentProcessing==null){
                    this.getData()
                }else{
                    this.getProcessing(this.currentProcessing)
                }
            },
            initPage(){
                var _this = this;
                _this.CopyPages=JSON.parse(JSON.stringify(_this.pages))
                if(_this.CopyPages.count_page>=10){
                    _this.pages.count=[1,2,3,4,5,6,7,8,9,10]
               }else{
                    var numPage=[]
                    for(var i=1;i<=_this.CopyPages.count_page;i++){
                        numPage.push(i)
                    }
                    _this.pages.count=numPage
               }
            },
            
            pagesSize(){
                var _this = this;
                if(_this.pageNum<10){
                    if(_this.CopyPages.count_page>=10){
                        _this.pages.count=[1,2,3,4,5,6,7,8,9,10]
                    }else{
                        var numPage=[]
                        for(var i=1;i<=_this.CopyPages.count_page;i++){
                            numPage.push(i)
                        }
                        _this.pages.count=numPage
                    }
                }else if(_this.pageNum<=_this.CopyPages.count_page){
                    if(_this.CopyPages.count_page-_this.pageNum>=4){
                        var minPage=_this.pageNum-5
                        var maxPage=_this.pageNum+4
                    }else{
                        var minPage=_this.pageNum-(9-((_this.CopyPages.count_page-_this.pageNum)))
                        var maxPage=_this.pageNum+(_this.CopyPages.count_page-_this.pageNum)
                    }
                    var numPage=[]
                    for(var i=minPage;i<=maxPage;i++){
                        numPage.push(i)
                    }
                    _this.pages.count=numPage
                }
            },
            prev(index) {
                this.pageNum = Number(index) - 1
                this.pagesSize()
                if(this.currentProcessing==null){
                    this.getData()
                }else{
                    this.getProcessing(this.currentProcessing)
                }
            },
            plus(index) {
                this.pageNum = Number(index)
                this.pagesSize()
                if(this.currentProcessing==null){
                    this.getData()
                }else{
                    this.getProcessing(this.currentProcessing)
                }
            },
            next(index){
                this.pageNum = Number(index) + 1
                this.pagesSize()
                if(this.currentProcessing==null){
                    this.getData()
                }else{
                    this.getProcessing(this.currentProcessing)
                }
            },
            initData(year,result){
                let that=this
                this.initLoading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("allBehaviorListIndex") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        start_year:year,
                        grade_group:that.currentGrade_group,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.showPositive=data.data.show_positive
                            that.initList=data.data
                            sessionStorage.setItem('start_year',year);
                            that.currentYear=data.data.opt
                            that.initSchoolYear=data.data.schoolYear
                            that.currentStat=data.data.stat
                            if(that.currentGrade_group==''){
                                if(data.data.showTab[getGrade]){
                                    that.currentGrade_group=getGrade
                                }else{
                                    that.currentGrade_group=Object.keys(data.data.showTab)[0]
                                }
                            }
                            if(!result){
                                if(id_num!='' || need_me!=''){
                                    that.currentProcessing=1
                                    that.getProcessing(1,"init")
                                }else{
                                    that.getData('init')
                                }
                            }
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.initLoading=false
                    },
                    error: function(data) {
                        that.initLoading=false
                    },
                })
            },
            getData(init){
                let that=this
                this.currentProcessing=null
                this.indexLoading=true
                if(init){
                    this.pageNum=1
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("allNewBehaviorList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        page:this.pageNum,
                        order: this.order,
                        orderProp:this.orderProp,
                        time: this.currentTime, 
                        grade_group:that.currentGrade_group,
                        start_year: that.currentYear,
                        recordOnly:0,
                        problem_behavior:this.filterBehaviorValue,
                        child_id:this.searchChild,
                        class_id:this.searchClass,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            let maxNodesOrderLength = 0;
                            data.data.list.forEach(item => {
                                item.more=true
                                if (item.nodes_order.length > maxNodesOrderLength) {
                                    maxNodesOrderLength = item.nodes_order.length;
                                }
                            });
                            if(maxNodesOrderLength==0){
                                that.showMinWidth=200
                            }else{
                                that.showMinWidth=maxNodesOrderLength*110
                            }
                            that.dataList=data.data
                            that.tableOffice=data.data.is_office
                            that.currentStat.new=data.data.meta.total2
                            if(init){
                                that.pages=data.data.meta
                                that.CopyPages=data.data.meta
                                that.initPage()
                            }
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.indexLoading=false
                    },
                    error: function(data) {
                        that.indexLoading=false
                    },
                })
            },
            filterStu(){
                if(this.dataList.child_count_data.list){
                    this.dataList.child_count_data.list.forEach(item => {
                        item.child_name=this.dataList.child_count_data.info[item.child_id].bilingual_name
                    });
                    this.filterMoreChildList=this.dataList.child_count_data
                    this.filterChildListVal=''
                }else{
                    this.filterMoreChildList={}
                    this.filterChildListVal=''
                }
                $('#filterChildListModal').modal('show')
            },
            filterClass(){
                this.filterMClassList=this.dataList.class_count_data
                $('#filterClassListModal').modal('show')
            },
            filterList(id,name,type,_id){
                if(_id){
                    this.$refs['popover-'+_id].doClose()
                }
                if(type=='child'){
                    this.searchChild=[id]
                    this.searchClass=[]
                }else{
                    this.searchClass=[id]
                    this.searchChild=[]
                }
                this.searchName=name
                if(this.currentProcessing==null){
                    this.getData('init')
                }else{
                    this.getProcessing(this.currentProcessing,'init')
                }
                $('#filterChildListModal').modal('hide')
                $('#filterClassListModal').modal('hide')

               
            },
            handleClose() {
                this.searchName=''
                this.searchChild=[]
                this.searchClass=[]
                if(this.currentProcessing==null){
                    this.getData('init')
                }else{
                    this.getProcessing(this.currentProcessing,'init')
                }
            },
            filterBehavior(type){
                if(type){
                    this.filterBehaviorValue=''
                }
                if(this.currentProcessing==null){
                    this.getData('init')
                }else{
                    this.getProcessing(this.currentProcessing,'init')
                }
            },
            getProcessing(type,init){
                let that=this
                this.currentProcessing=type
                // this.currentTime=null
                if(init){
                    this.pageNum=1
                }
                this.indexLoading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("allBehaviorList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        page:this.pageNum,
                        order: this.order,
                        orderProp:this.orderProp,
                        type: type, 
                        grade_group:that.currentGrade_group,
                        start_year: that.currentYear,
                        problem_behavior:this.filterBehaviorValue,
                        child_id:this.searchChild,
                        class_id:this.searchClass,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            let maxNodesOrderLength = 0;
                            data.data.list.forEach(item => {
                                item.more=true
                                if (item.nodes_order.length > maxNodesOrderLength) {
                                    maxNodesOrderLength = item.nodes_order.length;
                                }
                            });
                            if(maxNodesOrderLength==0){
                                that.showMinWidth=200
                            }else{
                                that.showMinWidth=maxNodesOrderLength*110
                            }
                            that.dataList=data.data
                            that.tableOffice=data.data.is_office
                            if(init){
                                that.CopyPages=data.data.meta
                                that.pages=data.data.meta
                                that.initPage()
                            }
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.indexLoading=false
                    },
                    error: function(data) {
                        that.indexLoading=false
                    },
                })
            },
            studentMore(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("warningStudentMore") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        grade_group:that.currentGrade_group,
                        start_year: that.currentYear
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.moreChildList=data.data
                            $('#moreChildListModal').modal('show')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            classMore(classid){
                this.moreClassid=classid
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("warningClassMore") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        grade_group:that.currentGrade_group,
                        start_year: that.currentYear,
                        class_id:classid
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.moreClassList=data.data
                            $('#moreClassListModal').modal('show')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            getQiniu(id){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getQiniuToken") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        linkId:id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.qiniuToken=data.data
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            showDetails(id,type){
                let that=this
                this.sendLogList=[]
                if(type=='show'){
                    // this.affirmData={}
                    // this.affirmRecordList={}
                }
                if(type=='init'){
                    that.getQiniu(id)
                }
                if(that.uploader.length!=0) {
                    for(var i=0;i<that.uploader.length;i++){
                    that.uploader[i].destroy();
                    }
                }
                this.handId=id
                this.appendText=false
                this.refill=false
                this.help=''
                this.reaffData={}
                this.officeData={
                    to_admin: "", //是否需要校长处理 1是 0否
                    remark: "",
                    duration: "00:00",
                    durationTime:"0:0",
                    attachments:{
                        img:[],
                        other:[]
                    },
                    notify_ids:''
                }
                this.resultData={
                    identified:'',
                    actions_other:'',
                    duration: "00:00",
                    durationTime:"0:0",
                    remark:'',
                    actions:[],
                    attachments:{
                        img:[],
                        other:[]
                    },
                    student_ids:[],
                    contact_parents:[]
                }
                this.ispData={
                    help_type:'',
                    support:'',
                    remark:'',
                    attachments:{
                        img:[],
                        other:[]
                    },
                    notify_ids:''
                }
                this.gradeData={
                    remark:'',
                    attachments:{
                        img:[],
                        other:[]
                    },
                    notify_ids:''
                }
                this.subjectData={
                    remark:'',
                    attachments:{
                        img:[],
                        other:[]
                    },
                    subject:''
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("getBehaviorDetail") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.contentList=data.data
                            that.detail=data.data.behavior.detail
                            $('#contentModal').modal('show')
                            // if(type=='show'){
                            //     $('#identifiedModal').modal('hide')
                            // }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.loading = false;
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.loading = false;
                    },
                })
            },
            getSupport(group){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getSupport") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        group:group
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            if(data.data.behavior.teacher && data.data.behavior.teacher.length!=0){
                                that.behaviorTeacher=[{
                                    label:'<?php echo Yii::t("newDS", "Please select"); ?>',
                                    value:''
                                }]
                                data.data.behavior.teacher.forEach(item => {
                                    that.behaviorTeacher.push({
                                        label:data.data.teacher_list[item].name,
                                        value:item,
                                        hrPosition:data.data.teacher_list[item].hrPosition,
                                        photoUrl:data.data.teacher_list[item].photoUrl
                                    })
                                });
                            }
                            if(data.data.study.teacher && data.data.study.teacher.length!=0){
                                that.learningTeacher=[{
                                    label:'<?php echo Yii::t("newDS", "Please select"); ?>',
                                    value:''
                                }]
                                data.data.study.teacher.forEach(item => {
                                    that.learningTeacher.push({
                                        label:data.data.teacher_list[item].name,
                                        value:item,
                                        hrPosition:data.data.teacher_list[item].hrPosition,
                                        photoUrl:data.data.teacher_list[item].photoUrl
                                    })
                                });
                            }
                            that.planData=data.data
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            confirmTeacher(type){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("setSupportTeacher") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        teacher_id:type=='3'?this.teacherUid:this.studyTeacherUid,
                        type:type,//2-学习计划支持 3-行为计划支持
                        status: "1",//1-设置 2取消
                        group:that.contentList.behavior.child[0].grade
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.options=[]
                            that.studyOptions=[]
                            that.teacherUid=''
                            that.studyTeacherUid=''
                            resultTip({
                                msg: data.message
                            });
                            that.getSupport(that.contentList.behavior.child[0].grade)
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            delTeacher(show,list,index,type){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("setSupportTeacher") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        teacher_id:list,
                        type:type,//2-学习计划支持 3-行为计划支持
                        status: "2",//1-设置 2取消
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.planData[show].teacher.splice(index,1)
                            resultTip({
                                msg: data.message
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            studyRemoteMethod(query){
                let that=this
                if (query !== '') {
                    this.loading = true;
                    $.ajax({
                        url: '<?php echo $this->createUrl("teacherSearch") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            teacher_name:query,
                            group:that.contentList.behavior.child[0].grade
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.studyOptions = Object.values(data.data) ;
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                            that.loading = false;
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.loading = false;
                        },
                    })
                } else {
                    this.options = [];
                }
            },
            remoteMethod(query) {
                let that=this
                if (query !== '') {
                    this.loading = true;
                    $.ajax({
                        url: '<?php echo $this->createUrl("teacherSearch") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            teacher_name:query,
                            group:that.contentList.behavior.child[0].grade
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.options = Object.values(data.data) ;
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                            that.loading = false;
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.loading = false;
                        },
                    })
                } else {
                    this.options = [];
                }
            },
            showIndexOf(type){
                if(this.officeData[type] && this.officeData[type].indexOf("OTHER")!=-1){
                    return true
                }else{
                    return false
                }
            },
            saveGrade(type){
                let that=this
                var data={id:this.contentList.behavior._id}
                if(type=='isp'){
                    for(var i=0;i<this.attachments.img.length;i++){
                        this.ispData.attachments.img.push(this.attachments.img[i]._id)
                    }
                    for(var i=0;i<this.attachments.other.length;i++){
                        this.ispData.attachments.other.push(this.attachments.other[i]._id)
                    }
                    this.ispData.to_isp=1
                    data=Object.assign(data,{data:this.ispData})
                }
                if(type=='office'){
                    for(var i=0;i<this.attachments.img.length;i++){
                        this.officeData.attachments.img.push(this.attachments.img[i]._id)
                    }
                    for(var i=0;i<this.attachments.other.length;i++){
                        this.officeData.attachments.other.push(this.attachments.other[i]._id)
                    }
                    data=Object.assign(data,{data:{remark:this.officeData.remark,attachments:this.officeData.attachments,to_admin:1}})
                }
                if(type=='result'){
                    this.resultData.attachments.img=[]
                    this.resultData.attachments.other=[]
                    for(var i=0;i<this.attachments.img.length;i++){
                        this.resultData.attachments.img.push(this.attachments.img[i]._id)
                    }
                    for(var i=0;i<this.attachments.other.length;i++){
                        this.resultData.attachments.other.push(this.attachments.other[i]._id)
                    }
                    for(var i=0;i<this.contentList.behavior.child.length;i++){
                        this.resultData.student_ids.push(this.contentList.behavior.child[i].id)
                    }
                    let hour=this.resultData.durationTime.split(':')
                    if(parseInt(hour[0])==0){
                        this.resultData.duration=hour[1]+' <?php echo Yii::t("teaching", "Minutes"); ?>'
                    }else{
                        this.resultData.duration=hour[0]+' <?php echo Yii::t("referral", "Hour"); ?> '+hour[1]+' <?php echo Yii::t("teaching", "Minutes"); ?>'
                    }
                    if(this.processType==''){
                        this.resultData.identified=2
                    }
                    data=Object.assign(data,{data:this.resultData})
                }
                if(type=="subject"){
                    for(var i=0;i<this.attachments.img.length;i++){
                        this.subjectData.attachments.img.push(this.attachments.img[i]._id)
                    }
                    for(var i=0;i<this.attachments.other.length;i++){
                        this.subjectData.attachments.other.push(this.attachments.other[i]._id)
                    }
                    this.subjectData.to_subjectlead=1
                    if(this.subjectData.subject==''){
                        resultTip({
                            error: 'warning',
                            msg: '<?php echo Yii::t("newDS", "Please select"); ?> <?php echo Yii::t("application", "Subject"); ?>'
                        });
                        return
                    }
                    data=Object.assign(data,{data:this.subjectData})
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("gradeleadProcess") ?>',
                    type: "post",
                    dataType: 'json',
                    data:data,
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg: data.message
                            });
                            if(that.currentProcessing==null){
                                that.getData(that.showType)
                            }else{
                                that.getProcessing(that.currentProcessing,"init")
                            }
                            that.showDetails(that.contentList.behavior._id)
                            if(type=='result'){
                                that.initData(that.currentYear,'result')
                                $('#finalResultModal').modal('hide')
                            }else if(type=="office"){
                                $('#officeModal').modal('hide')
                            }else if(type=="subject"){
                                $('#subjectModal').modal('hide')
                            }else{
                                $('#ispModal').modal('hide')
                            }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            saveTeacher(type){
                let that=this
                var data={id:this.contentList.behavior._id}
                if(type=='result'){
                    this.resultData.attachments.img=[]
                    this.resultData.attachments.other=[]
                    for(var i=0;i<this.attachments.img.length;i++){
                        this.resultData.attachments.img.push(this.attachments.img[i]._id)
                    }
                    for(var i=0;i<this.attachments.other.length;i++){
                        this.resultData.attachments.other.push(this.attachments.other[i]._id)
                    }
                    for(var i=0;i<this.contentList.behavior.child.length;i++){
                        this.resultData.student_ids.push(this.contentList.behavior.child[i].id)
                    }
                    let hour=this.resultData.durationTime.split(':')
                    if(parseInt(hour[0])==0){
                        this.resultData.duration=hour[1]+' <?php echo Yii::t("teaching", "Minutes"); ?>'
                    }else{
                        this.resultData.duration=hour[0]+' <?php echo Yii::t("referral", "Hour"); ?> '+hour[1]+' <?php echo Yii::t("teaching", "Minutes"); ?>'
                    }
                    if(this.processType==''){
                        this.resultData.identified=2
                    }
                    data=Object.assign(data,{data:this.resultData})
                }
                if(type=="office"){
                    for(var i=0;i<this.attachments.img.length;i++){
                        this.officeData.attachments.img.push(this.attachments.img[i]._id)
                    }
                    for(var i=0;i<this.attachments.other.length;i++){
                        this.officeData.attachments.other.push(this.attachments.other[i]._id)
                    }
                    data=Object.assign(data,{data:{remark:this.officeData.remark,attachments:this.officeData.attachments,to_admin:1}})
                }
                if(type=="grade"){
                    for(var i=0;i<this.attachments.img.length;i++){
                        this.gradeData.attachments.img.push(this.attachments.img[i]._id)
                    }
                    for(var i=0;i<this.attachments.other.length;i++){
                        this.gradeData.attachments.other.push(this.attachments.other[i]._id)
                    }
                    this.gradeData.to_gradelead=1
                    data=Object.assign(data,{data:this.gradeData})
                }
                if(type=="subject"){
                    for(var i=0;i<this.attachments.img.length;i++){
                        this.subjectData.attachments.img.push(this.attachments.img[i]._id)
                    }
                    for(var i=0;i<this.attachments.other.length;i++){
                        this.subjectData.attachments.other.push(this.attachments.other[i]._id)
                    }
                    this.subjectData.to_subjectlead=1
                    if(this.subjectData.subject==''){
                        resultTip({
                            error: 'warning',
                            msg: '<?php echo Yii::t("newDS", "Please select"); ?> <?php echo Yii::t("application", "Subject"); ?>'
                        });
                        return
                    }
                    data=Object.assign(data,{data:this.subjectData})
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("ISPProcess") ?>',
                    type: "post",
                    dataType: 'json',
                    data:data,
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg: data.message
                            });
                            if(that.currentProcessing==null){
                                that.getData(that.showType)
                            }else{
                                that.getProcessing(that.currentProcessing,"init")
                            }
                            that.showDetails(that.contentList.behavior._id)
                            if(type=='result'){
                                that.initData(that.currentYear,'result')
                                $('#finalResultModal').modal('hide')
                            }else if(type=="grade"){
                                $('#gradeModal').modal('hide')
                            }else if(type=="subject"){
                                $('#subjectModal').modal('hide')
                            }else{
                                $('#officeModal').modal('hide')
                            }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            saveOffice(type){
                let that=this
                var data={id:this.contentList.behavior._id}
                this.resultData.attachments.img=[]
                this.resultData.attachments.other=[]
                this.ispData.attachments.img=[]
                this.ispData.attachments.other=[]
                this.gradeData.attachments.img=[]
                this.gradeData.attachments.other=[]
                if(type=='office'){
                    this.officeData.attachments.img=[]
                    this.officeData.attachments.other=[]
                    for(var i=0;i<this.attachments.img.length;i++){
                        this.officeData.attachments.img.push(this.attachments.img[i]._id)
                    }
                    for(var i=0;i<this.attachments.other.length;i++){
                        this.officeData.attachments.other.push(this.attachments.other[i]._id)
                    }
                   
                    data=Object.assign(data,{data:{notify_ids:this.officeData.notify_ids,to_admin:1}})
                }
                if(type=='isp'){
                    for(var i=0;i<this.attachments.img.length;i++){
                        this.ispData.attachments.img.push(this.attachments.img[i]._id)
                    }
                    for(var i=0;i<this.attachments.other.length;i++){
                        this.ispData.attachments.other.push(this.attachments.other[i]._id)
                    }
                    this.ispData.notify_ids=''
                    if(this.ispData.support=='3'){
                        this.ispData.notify_ids=this.behaviorValue
                    }
                    if(this.ispData.support=='2'){
                        this.ispData.notify_ids=this.learningValue
                    }
                    this.ispData.to_isp=1
                    if(this.ispForwardType=="office" && this.ispData.notify_ids==''){
                        resultTip({
                            error: 'warning',
                            msg: '<?php echo Yii::t("newDS", "Please select"); ?> <?php echo Yii::t("referral", "Assign to"); ?>'
                        });
                        return
                    }
                    data=Object.assign(data,{data:this.ispData})
                }
                if(type=="grade"){
                    for(var i=0;i<this.attachments.img.length;i++){
                        this.gradeData.attachments.img.push(this.attachments.img[i]._id)
                    }
                    for(var i=0;i<this.attachments.other.length;i++){
                        this.gradeData.attachments.other.push(this.attachments.other[i]._id)
                    }
                    this.gradeData.to_gradelead=1
                    if(this.gradeData.notify_ids==''){
                        resultTip({
                            error: 'warning',
                            msg: '<?php echo Yii::t("newDS", "Please select"); ?> <?php echo Yii::t("referral", "Assign to"); ?>'
                        });
                        return
                    }
                    data=Object.assign(data,{data:this.gradeData})
                }
                if(type=="result"){
                    this.resultData.attachments.img=[]
                    this.resultData.attachments.other=[]
                    for(var i=0;i<this.attachments.img.length;i++){
                        this.resultData.attachments.img.push(this.attachments.img[i]._id)
                    }
                    for(var i=0;i<this.attachments.other.length;i++){
                        this.resultData.attachments.other.push(this.attachments.other[i]._id)
                    }
                    if(this.resultData.student_ids.length==0){
                        resultTip({
                            error: 'warning',
                            msg: '<?php echo Yii::t("referral", "Student"); ?>'
                        });
                        return
                    }
                    let hour=this.resultData.durationTime.split(':')
                    if(parseInt(hour[0])==0){
                        this.resultData.duration=hour[1]+' <?php echo Yii::t("teaching", "Minutes"); ?>'
                    }else{
                        this.resultData.duration=hour[0]+' <?php echo Yii::t("referral", "Hour"); ?> '+hour[1]+' <?php echo Yii::t("teaching", "Minutes"); ?>'
                    }
                    data=Object.assign(data,{data:this.resultData,help:0})
                }
                if(type=="subject"){
                    for(var i=0;i<this.attachments.img.length;i++){
                        this.subjectData.attachments.img.push(this.attachments.img[i]._id)
                    }
                    for(var i=0;i<this.attachments.other.length;i++){
                        this.subjectData.attachments.other.push(this.attachments.other[i]._id)
                    }
                    this.subjectData.to_subjectlead=1
                    if(this.subjectData.subject==''){
                        resultTip({
                            error: 'warning',
                            msg: '<?php echo Yii::t("newDS", "Please select"); ?> <?php echo Yii::t("application", "Subject"); ?>'
                        });
                        return
                    }
                    data=Object.assign(data,{data:this.subjectData})
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("officeProcess") ?>',
                    type: "post",
                    dataType: 'json',
                    data:data,
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg: data.message
                            });
                            if(type=='office'){
                                $('#officeModal').modal('hide')
                            }
                            if(type=='result'){
                                that.initData(that.currentYear,'result')
                                $('#finalResultModal').modal('hide')
                            }else if(type=="subject"){
                                $('#subjectModal').modal('hide')
                            }else{
                                $('#gradeModal').modal('hide')
                                $('#ispModal').modal('hide')
                            }
                            that.showDetails(that.contentList.behavior._id)
                            if(that.currentProcessing==null){
                                that.getData(that.showType)
                            }else{
                                that.getProcessing(that.currentProcessing,"init")
                            }
                            // $('#contentModal').modal('hide')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            saveSubject(type){
                let that=this
                var data={id:this.contentList.behavior._id}
                if(type=='isp'){
                    for(var i=0;i<this.attachments.img.length;i++){
                        this.ispData.attachments.img.push(this.attachments.img[i]._id)
                    }
                    for(var i=0;i<this.attachments.other.length;i++){
                        this.ispData.attachments.other.push(this.attachments.other[i]._id)
                    }
                    this.ispData.to_isp=1
                    data=Object.assign(data,{data:this.ispData})
                }
                if(type=='office'){
                    for(var i=0;i<this.attachments.img.length;i++){
                        this.officeData.attachments.img.push(this.attachments.img[i]._id)
                    }
                    for(var i=0;i<this.attachments.other.length;i++){
                        this.officeData.attachments.other.push(this.attachments.other[i]._id)
                    }
                    data=Object.assign(data,{data:{remark:this.officeData.remark,attachments:this.officeData.attachments,to_admin:1}})
                }
                if(type=="grade"){
                    for(var i=0;i<this.attachments.img.length;i++){
                        this.gradeData.attachments.img.push(this.attachments.img[i]._id)
                    }
                    for(var i=0;i<this.attachments.other.length;i++){
                        this.gradeData.attachments.other.push(this.attachments.other[i]._id)
                    }
                    this.gradeData.to_gradelead=1
                    data=Object.assign(data,{data:this.gradeData})
                }
                if(type=='result'){
                    this.resultData.attachments.img=[]
                    this.resultData.attachments.other=[]
                    for(var i=0;i<this.attachments.img.length;i++){
                        this.resultData.attachments.img.push(this.attachments.img[i]._id)
                    }
                    for(var i=0;i<this.attachments.other.length;i++){
                        this.resultData.attachments.other.push(this.attachments.other[i]._id)
                    }
                    for(var i=0;i<this.contentList.behavior.child.length;i++){
                        this.resultData.student_ids.push(this.contentList.behavior.child[i].id)
                    }
                    let hour=this.resultData.durationTime.split(':')
                    if(parseInt(hour[0])==0){
                        this.resultData.duration=hour[1]+' <?php echo Yii::t("teaching", "Minutes"); ?>'
                    }else{
                        this.resultData.duration=hour[0]+' <?php echo Yii::t("referral", "Hour"); ?> '+hour[1]+' <?php echo Yii::t("teaching", "Minutes"); ?>'
                    }
                    if(this.processType==''){
                        this.resultData.identified=2
                    }
                    data=Object.assign(data,{data:this.resultData})
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("subjectLeadProcess") ?>',
                    type: "post",
                    dataType: 'json',
                    data:data,
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg: data.message
                            });
                            if(that.currentProcessing==null){
                                that.getData(that.showType)
                            }else{
                                that.getProcessing(that.currentProcessing,"init")
                            }
                            that.showDetails(that.contentList.behavior._id)
                            if(type=='result'){
                                that.initData(that.currentYear,'result')
                                $('#finalResultModal').modal('hide')
                            }else if(type=="office"){
                                $('#officeModal').modal('hide')
                            }else if(type=="grade"){
                                $('#gradeModal').modal('hide')
                            }else{
                                $('#ispModal').modal('hide')
                            }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            saveComment(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("officeProcessAddComment") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        id:this.contentList.behavior._id,
                        comment:this.appendComment
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.appendText=false
                            resultTip({
                                msg: data.message
                            });
                            that.appendComment=''
                            that.showDetails(that.contentList.behavior._id)
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            showImg(id,list){
                var viewer = new Viewer(document.getElementById(id),{
                    fullscreen: false,
                    title:false,
                    scalable:false,
                    show:function(){ 
                        if(list.length==1){
                            $('.viewer-prev').hide()
                            $('.viewer-next').hide()
                        }
                    },
                    hide:function(){ 
                        viewer.destroy()
                    }
                });
                $("#"+id).click();
            },
            stuRemoteMethod(query) {
                let that=this
                if (query !== '') {
                    this.loading = true;
                    $.ajax({
                        url: '<?php echo $this->createUrl("studentSearch") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            child_name:query
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.stuOption = Object.values(data.data) ;
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                            that.loading = false;
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.loading = false;
                        },
                    })
                } else {
                    this.options = [];
                }
            },
            addStu(id){
                for(let i=0;i<this.addChildList.length;i++){
                    if(this.addChildList[i].id==this.stuVal){
                        resultTip({
                            error: 'warning',
                            msg: '不能重复添加'
                        });
                        return
                    }
                }
                for(let i=0;i<this.stuOption.length;i++){
                    if(this.stuOption[i].id==this.stuVal){
                        if( this.stuOption[i].grade!=this.contentList.behavior.child[0].grade){
                            resultTip({
                                error: 'warning',
                                msg: '不同学部的学生请分开提交'
                            });
                            return
                        }else{
                            this.addChildList.push(this.stuOption[i])
                        }
                    }
                   
                }
                this.officeData.student_ids.push(this.stuVal)
                this.stuOption=[]
                this.stuVal=''
            },
            delChild(index,id){
                this.addChildList.splice(index, 1)
                this.officeData.student_ids=this.officeData.student_ids.filter((item)=>{return item!=id});
            },
            finalResult(type,result){
                this.resultType=type
                if(result){
                    this.processType=result
                }else{
                    this.processType=''
                }
                if(this.uploader.length!=0) {
                    for(var i=0;i<this.uploader.length;i++){
                    this.uploader[i].destroy();
                    }
                }
                this.attachments={
                    img:[],
                    other:[]
                }
                if(this.processType=="" && (type=="isp" || type=="grade"  || type=="subject")){
                    let gradeList=[]
                    this.contentList.behavior.child.forEach(item => {
                        gradeList.push(item.grade)
                    });
                    this.showActions(Array.from(new Set(gradeList)))
                }
                
                if(type=="office" || this.processType!=''){
                    this.officeData={
                        identified:'',
                        actions_other:'',
                        duration:"0:0",
                        durationTime:"0:0",
                        remark:'',
                        actions:[],
                        attachments:{
                            img:[],
                            other:[]
                        },
                        student_ids:[],
                        contact_parents:[]
                    }
                    if(this.contentList.behavior.child.length==1){
                        this.contentList.behavior.child.forEach(item => {
                            this.resultData.student_ids.push(item.id)
                        });
                    }
                }
                let gradeList=[this.contentList.behavior.child[0].grade]
                this.showActions(Array.from(new Set(gradeList)))
                // that.stuOption=[]
                $('#finalResultModal').modal('show')
                this.$nextTick(()=>{
                    config['token'] = this.qiniuToken;
                    config['browse_button'] ='finalResultAddFile';
                    var uploader=new plupload.Uploader(config);
                    this.uploader.push(uploader);
                    uploader.init();
                })
            },
            forwardOffice(type){
                this.forwardType=type
                if(this.uploader.length!=0) {
                    for(var i=0;i<this.uploader.length;i++){
                    this.uploader[i].destroy();
                    }
                }
                this.attachments={
                    img:[],
                    other:[]
                }
                if(type=='office'){
                    let that=this
                    $.ajax({
                        url: '<?php echo $this->createUrl("officeUserList") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            group:that.contentList.behavior.child[0].grade
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                delete data.data.staff_info[that.config.user_data.uid]
                                that.officeList=data.data
                                $('#officeModal').modal('show')
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        },
                    })
                }else{
                    this.getSendEmail('',2)
                    
                }
                
                this.$nextTick(()=>{
                    config['token'] = this.qiniuToken;
                    config['browse_button'] ='officeAddFile';
                    var uploader=new plupload.Uploader(config);
                    this.uploader.push(uploader);
                    uploader.init();
                })
            },
            forwardGrade(type){
                this.GradeForwardType=type
                if(this.uploader.length!=0) {
                    for(var i=0;i<this.uploader.length;i++){
                    this.uploader[i].destroy();
                    }
                }
                this.attachments={
                    img:[],
                    other:[]
                }
                this.getSendEmail('',4)
                this.$nextTick(()=>{
                    config['token'] = this.qiniuToken;
                    config['browse_button'] ='gradeAddFile';
                    var uploader=new plupload.Uploader(config);
                    this.uploader.push(uploader);
                    uploader.init();
                })
            },
            forwardISP(type){
                this.getSupport(this.contentList.behavior.child[0].grade)
                this.ispForwardType=type
                if(this.uploader.length!=0) {
                    for(var i=0;i<this.uploader.length;i++){
                    this.uploader[i].destroy();
                    }
                }
                this.attachments={
                    img:[],
                    other:[]
                }
                $('#ispModal').modal('show')
                this.$nextTick(()=>{
                    config['token'] = this.qiniuToken;
                    config['browse_button'] ='ispAddFile';
                    var uploader=new plupload.Uploader(config);
                    this.uploader.push(uploader);
                    uploader.init();
                })
            },
            forwardSubject(type){
                this.subForwardType=type
                if(this.uploader.length!=0) {
                    for(var i=0;i<this.uploader.length;i++){
                    this.uploader[i].destroy();
                    }
                }
                this.attachments={
                    img:[],
                    other:[]
                }
                this.$nextTick(()=>{
                    config['token'] = this.qiniuToken;
                    config['browse_button'] ='subjectAddFile';
                    var uploader=new plupload.Uploader(config);
                    this.uploader.push(uploader);
                    uploader.init();
                })
                let chlidId=[]
                for(var i=0;i<this.contentList.behavior.child.length;i++){
                    chlidId.push(this.contentList.behavior.child[i].id)
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("followupNode") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        group:that.contentList.behavior.child[0].grade,
                        child_id:chlidId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.subjectList=data.data.subject
                            $('#subjectModal').modal('show')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            getSendEmail(subject,type){
                let child_id=[]
                this.contentList.behavior.child.forEach(item => {
                    child_id.push(item.id)
                });
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getNodeTeacher") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        "subject":subject,
                        "node":type,
                        "child_id":child_id
                    } ,
                    success: function(data) {
                        if (data.state == 'success') {
                            that.nextNode=data.data
                            if(type==4){
                                $('#gradeModal').modal('show')
                            }
                            if(type==2){
                                $('#officeModal').modal('show')
                            }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            delImg(type,list,index){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("deleteAttachment") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        attachmentId:list._id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            if(type=='img'){
                                that.attachments.img.forEach((item,index) => {
                                    if (item._id==list._id) {
                                        that.attachments.img.splice(index, 1)
                                    } 
                                })
                            }else{
                                that.attachments.other.forEach((item,index) => {
                                    if (item._id==list._id) {
                                        that.attachments.other.splice(index, 1)
                                    } 
                                })
                            }
                            resultTip({
                                msg:data.state
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            saveFile(list,index) {
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("updateAttachmentName") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        attachmentId:list._id,
                        attachmentName:this.attachmentName

                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.attachments.other.forEach((item,index) => {
                                if (item._id==list._id) {
                                    Vue.set(that.attachments.other[index], 'title',data.data.title);
                                    Vue.set(that.attachments.other[index], 'file_key', data.data.url);
                                    Vue.set(that.attachments.other[index], 'isEdit', false);
                                } 
                            })
                            resultTip({
                                msg:'<?php echo Yii::t("newDS", "Data Saved!");?>'
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            markEnd(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("setOver") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:this.contentList.behavior._id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            if(that.currentProcessing==null){
                                that.getData(that.showType)
                            }else{
                                that.getProcessing(that.currentProcessing,"init")
                            }
                            that.showDetails(that.contentList.behavior._id)
                            that.initData(that.currentYear,'result')
                            // $('#contentModal').modal('hide')
                            resultTip({
                                msg:data.state
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            addReaff(){
                this.officeData={
                    identified:'',
                    actions_other:'',
                    duration:"0:0",
                    durationTime:"0:0",
                    remark:'',
                    actions:[],
                    attachments:{
                        img:[],
                        other:[]
                    },
                    student_ids:[],
                    contact_parents:[]
                }
                this.addChildList=[]
                this.attachments={
                    img:[],
                    other:[]
                }
                this.editStatus=2
                let gradeList=[this.contentList.behavior.child[0].grade]
                this.showActions(Array.from(new Set(gradeList)))
                $('#afreshResultModal').modal('show')
            },
            showDisabled(id){
                var finallyId=[]
                this.contentList.finally_data.forEach(item => {
                    finallyId.push(item.child.id)
                });
                if(finallyId.indexOf(id)!=-1){
                    return true
                }else{
                    return false
                }
            },
            reaffirmation(data,type){
                this.officeData={
                    identified:data.result.identified,
                    actions_other:data.result.actions_other,
                    duration:data.result.duration,
                    remark:data.result.remark,
                    actions:data.result.actions,
                    attachments:{ 
                        img:[],
                        other:[]
                    },
                    contact_parents:data.result.contact_parents
                }
                var imgs=[]
                var others=[]
                if(data.result.attachments.img){
                    data.result.attachments.img.forEach(item => {
                        imgs.push(this.contentList.attachmentList[item]) 
                    });
                }
                if(data.result.attachments.other){
                    data.result.attachments.other.forEach(item => {
                        others.push(this.contentList.attachmentList[item]) 
                    });
                }
                this.attachments={
                    img:imgs,
                    other:others
                }
                var hourNum='0'
                var minuteNum='0'
                let hour=this.officeData.duration.indexOf("<?php echo Yii::t("referral", "Hour"); ?>")
                if(hour!=-1){
                    hourNum =this.officeData.duration.substring(0,hour)
                }
                let minute=this.officeData.duration.indexOf("<?php echo Yii::t("teaching", "Minutes"); ?>")
                if(minute!=-1){
                    if(hour!=-1){
                        minuteNum =this.officeData.duration.substring(hour+2,minute)
                    }else{
                        minuteNum =this.officeData.duration.substring(0,minute) 
                    }
                }
                this.officeData.durationTime=hourNum+':'+minuteNum
                this.editStatus=1
                this.showActions([data.child.level])
                this.reaffData=data
                if(this.uploader.length!=0) {
                    for(var i=0;i<this.uploader.length;i++){
                    this.uploader[i].destroy();
                    }
                }
                $('#afreshResultModal').modal('show')
                this.$nextTick(()=>{
                    config['token'] = this.qiniuToken;
                    config['browse_button'] ='afreshAddFile';
                    var uploader=new plupload.Uploader(config);
                    this.uploader.push(uploader);
                    uploader.init();
                })
            },
            afreshOffice(type){
                let that=this
                if(this.officeData.student_ids.length==0){
                    resultTip({
                        error: 'warning',
                        msg: '<?php echo Yii::t("referral", "Student"); ?>'
                    });
                    return
                }
                for(var i=0;i<this.attachments.img.length;i++){
                    this.officeData.attachments.img.push(this.attachments.img[i]._id)
                }
                for(var i=0;i<this.attachments.other.length;i++){
                    this.officeData.attachments.other.push(this.attachments.other[i]._id)
                }
                // for(var i=0;i<this.addChildList.length;i++){
                //     this.officeData.student_ids.push(this.addChildList[i].id)
                // }
                let hour=this.officeData.durationTime.split(':')
                if(parseInt(hour[0])==0){
                    this.officeData.duration=hour[1]+' <?php echo Yii::t("teaching", "Minutes"); ?>'
                }else{
                    this.officeData.duration=hour[0]+' <?php echo Yii::t("referral", "Hour"); ?> '+hour[1]+' <?php echo Yii::t("teaching", "Minutes"); ?>'
                }
                var data={id:this.contentList.behavior._id,data:this.officeData,help:0}
                $.ajax({
                    url: '<?php echo $this->createUrl("officeProcess") ?>',
                    type: "post",
                    dataType: 'json',
                    data:data,
                    success: function(data) {
                        if (data.state == 'success') {
                            $('#afreshResultModal').modal('hide')
                            resultTip({
                                msg: data.message
                            });
                            that.showDetails(that.contentList.behavior._id)
                            
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            afreshReaff(){
                let that=this
                for(var i=0;i<this.attachments.img.length;i++){
                    this.officeData.attachments.img.push(this.attachments.img[i]._id)
                }
                for(var i=0;i<this.attachments.other.length;i++){
                    this.officeData.attachments.other.push(this.attachments.other[i]._id)
                }
                let hour=this.officeData.durationTime.split(':')
                if(parseInt(hour[0])==0){
                    this.officeData.duration=hour[1]+' <?php echo Yii::t("teaching", "Minutes"); ?>'
                }else{
                    this.officeData.duration=hour[0]+' <?php echo Yii::t("referral", "Hour"); ?> '+hour[1]+' <?php echo Yii::t("teaching", "Minutes"); ?>'
                }
                var data={id:this.reaffData._id,data:this.officeData}
                $.ajax({
                    url: '<?php echo $this->createUrl("afreshAffirm") ?>',
                    type: "post",
                    dataType: 'json',
                    data:data,
                    success: function(data) {
                        if (data.state == 'success') {
                            $('#afreshResultModal').modal('hide')
                            resultTip({
                                msg: data.message
                            });
                            that.showDetails(that.contentList.behavior._id)
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            showFollow(list){
                if(list.indexOf("OUTSCHOOLSUSPENSION")!=-1 || list.indexOf("INSCHOOLSUSPENSION")!=-1 ){
                    return true
                }else{
                    return false
                }
            },
            follow(data,type){
                this.followItem=data
                this.followType=type
                this.followData={
                    remark:this.followItem.followup.remark?this.followItem.followup.remark:'',
                    date:this.followItem.followup.date?this.followItem.followup.date:'',
                    time:this.followItem.followup.time?this.followItem.followup.time:''
                }
                $('#followModal').modal('show')
            },
            saveFollow(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("saveFollowup") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:this.followItem._id,
                        data:this.followData

                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.showDetails(that.contentList.behavior._id)
                            resultTip({
                                msg:data.message
                            });
                             $('#followModal').modal('hide')

                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            sendMessage(item){
                this.sendItem=item
                let that=this
                this.teacher_ids=[]
                this.sendRemark=''
                this.checkall=false
                $.ajax({
                    url: '<?php echo $this->createUrl("getClassTeacherList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        class_id:[item.child.class_id]
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.classTeacher=data.data[item.child.class_id]
                            $('#sendTeacherModal').modal('show')
                            that.$nextTick(()=>{
                                $('.qrcodeShow').empty();
                                $('.qrcodeShow').qrcode({
                                    width: 120,
                                    height: 120,
                                    text: 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxe929eea87aaf012f&redirect_uri=https%3A%2F%2Fstaff.ivyonline.cn%2FrequestForm&response_type=code&scope=snsapi_userinfo&state=ivy#wechat_redirect',
                                    background: "#fff",//二维码的后景色
                                    // foreground: "#2f2c2c"//二维码的前景色
                                });
                            })
                            
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            allTeacher(){
                this.teacher_ids=[]
                if(this.checkall){
                    for(var keys in this.classTeacher){
                        this.teacher_ids.push(parseInt(keys))                         
                    }
                }
            },
            selectSendTeacher(){
                if(this.teacher_ids.length==Object.values(this.classTeacher).length){
                    this.checkall=true
                }else{
                    this.checkall=false
                }
            },
            saveSend(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("sendBehaviorMsgToTeacher") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        class_id:[this.sendItem.child.class_id],
                        id: this.sendItem._id,
                        remark: this.sendRemark,
                        "assist[class_id]":this.sendItem.child.class_id,
                        "assist[teacher_ids]":this.teacher_ids,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg: data.message
                            });
                            that.showDetails(that.contentList.behavior._id)
                            $('#sendTeacherModal').modal('hide')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            viewSendMessage(item){
                this.sendLogList=item
                $('#viewSendLogModal').modal('show')
                this.$nextTick(()=>{
                    $('.qrcodeShow').empty();
                    $('.qrcodeShow').qrcode({
                        width: 120,
                        height: 120,
                        text: 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxe929eea87aaf012f&redirect_uri=https%3A%2F%2Fstaff.ivyonline.cn%2FrequestForm&response_type=code&scope=snsapi_userinfo&state=ivy#wechat_redirect',
                        background: "#fff",//二维码的后景色
                        // foreground: "#2f2c2c"//二维码的前景色
                    });
                })
            },
            allSendMessage(item){
                this.sendTecherOptions=[]
                $('#allSendLogModal').modal('show')
            },
            senTeacherOption(){
                this.sendTecherOptions=[]
            },
            sendConfirmTeacher(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("sendBehaviorMsgToTeacher") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        teacher_ids:this.sendTeacherUid,
                        id: this.contentList.behavior._id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.sendTecherOptions=[]
                            that.sendTeacherUid=[]
                            resultTip({
                                msg: data.message
                            });
                            that.showDetails(that.contentList.behavior._id)
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            loseFocus(val) {
                // 下拉框隐藏时
                if (!val) {
                this.sendTecherOptions=[]
                }
            },
            sendRemoteMethod(query) {
                let that=this
                if (query !== '') {
                    this.loading = true;
                    $.ajax({
                        url: '<?php echo $this->createUrl("teacherSearch") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            teacher_name:query,
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.sendTecherOptions = Object.values(data.data) ;
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                            that.loading = false;
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.loading = false;
                        },
                    })
                } else {
                    this.options = [];
                }
            },
            viewAffirm(list){
                let data={
                    child_id:list.id,
                    num:list.affirm_num
                }
               this.identifiedList(data,'content')
            },
            identifiedList(list,type){
                let that=this
                if(list.num==0){
                    return
                }
                this.affirmRecordList={}
                this.affirmData=list
                $.ajax({
                    url: '<?php echo $this->createUrl("allBehaviorAffirmRecordList2") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        child_id:list.child_id,
                        start_year: that.currentYear,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.affirmRecordList=data.data
                            that.behaviorTabDetail="bad"
                            $('#identifiedModal').modal('show')
                            if(type=='content'){
                                $('#contentModal').modal('hide')                                
                            }
                            if(type=='child'){
                                $('#moreChildListModal').modal('hide')
                            }
                            if(type=='class'){
                                $('#moreClassListModal').modal('hide')
                            }
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            delDetails(id){
                if(id!='del'){
                    this.delId=id
                    $('#delListModal').modal('show')
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("adminDelBehavior") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id: this.delId,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg: data.state
                            });
                            $('#delListModal').modal('hide')
                            if(that.currentProcessing==null){
                                that.getData(that.showType)
                            }else{
                                that.getProcessing(that.currentProcessing,"init")
                            }
                            that.initData(that.currentYear,'result')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            }
        }
    })
   
    var config = {
        runtimes: 'html5,flash,html4', //上传模式,依次退化
        container: 'container', //上传区域DOM ID，默认是browser_button的父元素，
        browse_button: '', //上传选择的点选按钮，**必需**
        token: '',
        flash_swf_url: '/js/plupload/Moxie.swf', // flash的相对地址
        auto_start: true, //选择文件后自动上传，若关闭需要自己绑定事件触发上传
        // multi_selection: false, //不允许多选
        url:'https://upload-z1.qiniup.com',
        init: {
            'FilesAdded': function(up, files) {
                container.disabledUpload=true
                let fileType=files[0].type.split('/')
                if(fileType[0]=="image"){
                  container.loadingType=1
                }else{
                  container.loadingType=2
                }
                container.loadingList=files
                up.start();
            },
            BeforeUpload: function(up, file) {
                //设置参数
                up.setOption({
                    multipart_params:{token:container.qiniuToken}
                })
                let fileType=file.type.split('/')
                if(fileType[0]=="image"){
                    container.attachments.img.push({types:'1'})
                }else{
                    container.attachments.other.push({types:'1',title:' <?php echo Yii::t("directMessage", "Uploading");?>'})  
                }
                container.loadingList.splice(0,1)
            },
            UploadProgress: function(up, file) {
                $('#progress').css('width', file.percent+'%').html(file.percent+'%');
            },
            'FileUploaded': function(up, file, info) {
                var response = eval('('+info.response+')');
                if(info.status == 200){
                    let fileType=response.data.mimetype.split('/')
                    if(fileType[0]=="image"){
                        container.attachments.img.splice(container.attachments.img.length-1,1)
                        container.attachments.img.push(response.data)
                    }else{
                        response.data.isEdit=false
                        container.attachments.other.splice(container.attachments.other.length-1,1)
                        container.attachments.other.push(response.data)
                    }
                }
            },
            'Error': function(up, err, errTip) {
                if(err.response){
                    var response = eval('('+err.response+')');
                }
                else{
                    var response = {message: err.message};
                }
                if(response.error == 'token not specified'){
                    up.stop();
                    $.ajax({
                        url: '<?php echo $this->createUrl("getQiniuToken") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            linkId: container.handId
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                container.qiniuToken=data.data
                                config['uptoken'] = data.data;
                                up.setOption("multipart_params", {"token":data.data,})
                                up.start();
                            } else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.msg
                                });
                            }
                        },
                        error: function(data) {

                        },
                    })
                }
            },
            'UploadComplete': function(up, file) {
                //队列文件处理完毕后,处理相关的事情
            }
        }
    };
</script>
