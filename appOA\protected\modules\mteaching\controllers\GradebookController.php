<?php

class GradebookController extends TeachBasedController
{
    public $userid;

    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'teaching';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Teaching Tasks');

        //初始化选择校园页面
        $this->branchSelectParams['hideKG'] = true;
        $this->branchSelectParams['urlArray'] = array('//mteaching/gradebook/index');
        $this->userid = Yii::app()->user->getId();
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/vue2.js');

        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/css/iconfont.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/v-calendar/vue.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/v-calendar/v-calendar.umd.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/backbone-min.js');
        $cs->registerCssFile(Yii::app()->theme->baseUrl . '/css/calendar.css');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/xlsx.full.min.js');
    }

    public function createUrlReg($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function actionIndex()
    {
        $this->render('index');
    }

    public function actionIndexData()
    {
        $requestData = array(
            'teacher_id' => Yii::app()->request->getParam('teacher_id', 0),
            'startyear' => Yii::app()->request->getParam('startyear', ''),
            'semester' => Yii::app()->request->getParam('semester', ''),
        );
        $requestUrl = 'gradebook/indexData';
        $requestData['school_id'] = $this->branchId;
        $res = CommonUtils::requestDsOnline($requestUrl, $requestData);
        if ($res['code'] == 0) {
            // ivystaff_cd ivystaff_dsedu_editall  ivystaff_dsedu_editall
            //可查看其他人
            $res['data']['isSuper'] = Yii::app()->user->checkAccess('ivystaff_cd') || Yii::app()->user->checkAccess('ivystaff_dsedu_editall') || Yii::app()->user->checkAccess('ivystaff_dsedu_viewall');
            //可编辑其他人
            $res['data']['editall'] = Yii::app()->user->checkAccess('ivystaff_cd') || Yii::app()->user->checkAccess('ivystaff_dsedu_editall');
            $this->addMessage('state', 'success');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        }
        $this->showMessage();
    }

    public function actionGetCategoryList()
    {
        $requestData = array(
            'startyear' => Yii::app()->request->getParam('startyear', ''),
            'semester' => Yii::app()->request->getParam('semester', ''),
        );
        $class_id = Yii::app()->request->getParam('class_id', '');
        $requestUrl = 'standard/template/data/' . $class_id;
        $this->remote($requestUrl, $requestData);

    }

    public function actionTeacherList()
    {
        $requestData = array(
            'startyear' => Yii::app()->request->getParam('startyear', ''),
        );
        $requestUrl = 'gradebook/teacherList';
        $this->remote($requestUrl, $requestData);
    }

    public function actionSaveLockTime()
    {
        $requestData = array(
            'startyear' => Yii::app()->request->getParam('startyear', ''),
            'semester' => Yii::app()->request->getParam('semester', ''),
            'lock_from_time' => Yii::app()->request->getParam('lock_from_time', ''),
        );
        $requestUrl = 'gradebook/saveLockTime';
        $this->remote($requestUrl, $requestData);
    }

    public function actionSaveEvidence()
    {
        $teacher_id = Yii::app()->request->getParam('teacher_id', 0);
        $requestData = array(
            'data' => Yii::app()->request->getParam('data', ''),
            'teacher_id' => $teacher_id,
        );

        if (!empty($teacher_id) && Yii::app()->user->getId() != $teacher_id && !Yii::app()->user->checkAccess('ivystaff_cd') && !Yii::app()->user->checkAccess('ivystaff_dsedu_editall')) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '不可操作');
            $this->addMessage('data', '');
            $this->showMessage();
        }
        $requestUrl = 'gradebook/saveEvidence';
        $this->remote($requestUrl, $requestData);
    }

    public function actionGetEvidence()
    {
        $requestData = array(
            'evidence_id' => Yii::app()->request->getParam('evidence_id', 0),
        );
        $requestUrl = 'gradebook/getEvidence';
        $this->remote($requestUrl, $requestData);
    }

    public function actionSaveScore()
    {
        $teacher_id = Yii::app()->request->getParam('teacher_id', 0);
        $requestData = array(
            'evidence_id' => Yii::app()->request->getParam('evidence_id', 0),
            'data' => Yii::app()->request->getParam('data', 0),
            'teacher_id' => $teacher_id,
        );
        if (!empty($teacher_id) && Yii::app()->user->getId() != $teacher_id && !Yii::app()->user->checkAccess('ivystaff_cd') && !Yii::app()->user->checkAccess('ivystaff_dsedu_editall')) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '不可操作');
            $this->addMessage('data', '');
            $this->showMessage();
        }
        $requestUrl = 'gradebook/saveScore';
        $this->remote($requestUrl, $requestData);
    }

    public function actionSaveScore2()
    {
        $teacher_id = Yii::app()->request->getParam('teacher_id', 0);
        $requestData = array(
            'evidence_id' => Yii::app()->request->getParam('evidence_id', 0),
            'data' => Yii::app()->request->getParam('data', array()),
            'teacher_id' => $teacher_id,
        );
        if (!empty($teacher_id) && Yii::app()->user->getId() != $teacher_id && !Yii::app()->user->checkAccess('ivystaff_cd') && !Yii::app()->user->checkAccess('ivystaff_dsedu_editall')) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '不可操作');
            $this->addMessage('data', '');
            $this->showMessage();
        }
        $requestUrl = 'gradebook/saveScore2';
        $this->remote($requestUrl, $requestData);
    }

    public function actionStudentList()
    {
        $requestData = array(
            'class_id' => Yii::app()->request->getParam('class_id', 0),
            'startyear' => Yii::app()->request->getParam('startyear', 2022),
        );
        $requestUrl = 'gradebook/studentList';
        $this->remote($requestUrl, $requestData);
    }

    public function actionGetScore()
    {
        $requestData = array(
            'evidence_id' => Yii::app()->request->getParam('evidence_id', 0),
            'teacher_id' => Yii::app()->request->getParam('teacher_id', 0),
        );
        $requestUrl = 'gradebook/getScore';
        $this->remote($requestUrl, $requestData);
    }

    public function actionCheckScore()
    {
        $requestData = array(
            'evidence_id' => Yii::app()->request->getParam('evidence_id', 0),
        );
        $requestUrl = 'gradebook/checkScore';
        $this->remote($requestUrl, $requestData);
    }

    public function actionGetAllScore()
    {
        $requestData = array(
            'startyear' => Yii::app()->request->getParam('startyear', 0),
            'semester' => Yii::app()->request->getParam('semester', 0),
            'class_id' => Yii::app()->request->getParam('class_id', 0),
            'program' => Yii::app()->request->getParam('program', 0),
        );
        $requestUrl = 'gradebook/getAllScore';
        $this->remote($requestUrl, $requestData);
    }

    public function actionDelEvidence()
    {
        $teacher_id = Yii::app()->request->getParam('teacher_id', 0);
        $requestData = array(
            'evidence_id' => Yii::app()->request->getParam('evidence_id', array()),
            'teacher_id' => $teacher_id,
        );
        if (!empty($teacher_id) && Yii::app()->user->getId() != $teacher_id && !Yii::app()->user->checkAccess('ivystaff_cd') && !Yii::app()->user->checkAccess('ivystaff_dsedu_editall')) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '不可操作');
            $this->addMessage('data', '');
            $this->showMessage();
        }
        $requestUrl = 'gradebook/delEvidence';
        $this->remote($requestUrl, $requestData);
    }

    public function actionGetBatchClass()
    {
        $requestData = array(
            'evidence_id' => Yii::app()->request->getParam('evidence_id', 0),
        );
        $requestUrl = 'gradebook/getBatchClass';
        $this->remote($requestUrl, $requestData);
    }

    public function actionGetSyncClass()
    {
        $requestData = array(
            'teacher_id' => Yii::app()->request->getParam('teacher_id', 0),
            'startyear' => Yii::app()->request->getParam('startyear', ''),
            'semester' => Yii::app()->request->getParam('semester', ''),
            'class_id' => Yii::app()->request->getParam('class_id', ''),
            'item_id' => Yii::app()->request->getParam('item_id', ''),
            'type' => Yii::app()->request->getParam('type', ''),
        );
        $requestUrl = 'gradebook/getSyncClass';
        $this->remote($requestUrl, $requestData);
    }


    public function remote($requestUrl, $requestData = array())
    {
        $requestData['school_id'] = $this->branchId;
        $res = CommonUtils::requestDsOnline($requestUrl, $requestData);
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        }
        $this->showMessage();
    }

}