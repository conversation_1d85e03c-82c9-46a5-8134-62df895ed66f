<?php
class Schedule extends CWidget{
	public $classId;
    public $weeknum;
    public $cssFile;
    public $assetsUrl;
	
	public function init(){
		Yii::import('common.models.schedule.*');

//        if($this->assetsUrl===null)
//			$this->assetsUrl=Yii::app()->getAssetManager()->publish(dirname(__FILE__) . '/assets', false, -1, YII_DEBUG);

        $cs = Yii::app()->clientScript;
        if (false !== $this->cssFile)
        {
            if (null === $this->cssFile)
//                $this->cssFile = $this->assetsUrl . '/schedule.css';
                $this->cssFile = Yii::app()->theme->baseUrl.'/widgets/schedule/schedule.css?t='.Yii::app()->params['refreshAssets'];
            $cs->registerCssFile($this->cssFile);
//            $cs->registerCssFile(Yii::app()->theme->baseUrl.'/css/widgetbase.css?t=2');
            $cs->registerCssFile(Yii::app()->theme->baseUrl.'/css/widgetbase.css?t='.Yii::app()->params['refreshAssets']);
        }
	}
	
	public function run(){
        Yii::import('common.models.calendar.CalendarWeek');

        Mims::LoadHelper('HtoolKits');
        
        $class_model = IvyClass::model()->findByPk($this->classId);
        $yid        = $class_model->yid;

        $weeks = CalendarWeek::weeks($yid);
        $weekpk = array_keys($weeks);
        if (!$this->weeknum)
            $this->weeknum = end($weekpk);

        $criteria = new CDbCriteria();
        $criteria->compare('classid', $this->classId);
        $criteria->compare('weeknumber', $this->weeknum);
        $criteria->order='t.weight asc';
		$schedule=ClassSchedule::model()->with('activityInfo')->findAll($criteria);

        $sArr = explode('|', $class_model->periodtime);
        $startTime  = strtotime($sArr[0].':'.$sArr[1]);
        $endTime    = strtotime($sArr[2].':'.$sArr[3]);

        $i=0;
        $tArr = array();
        while ($startTime <= $endTime){
            $p1 = ($endTime-$startTime)/60;
            $p = $p1 < 5 ? $p1 : 5;
            $tArr[$startTime] = array('t'=>date('H:i', $startTime), 'p'=>$p);
            $fd = date('i', $startTime);
            if ($fd == '30' || $fd == '00' || $p < 5 || $i == 0){
                if ($p < 5){
                    $tArr[$startTime]['part'] = $i;
                }
                else {
                    $tArr[$startTime]['part'] = $i++;
                }

                if ($endTime == $startTime){
                    $tArr[$startTime]['end'] = 1;
                }
            }
            $tArr[$startTime]['cssname'] = $i%2==1?'bg1':'bg2';
            $startTime += 300;
        }

        $scheduleday = array();
        foreach ($schedule as $s){
            $scheduleday[$s->weekday][$s->id]['model'] = $s;
            $scheduleday[$s->weekday][$s->id]['h'] = $s->minutes/5*20;
        }

		$this->render('schedule',array('scheduleday'=>$scheduleday, 'class_model'=>$class_model, 'tArr'=>$tArr, 'weeks'=>$weeks, 'weeknum'=>$this->weeknum));
	}
	
}