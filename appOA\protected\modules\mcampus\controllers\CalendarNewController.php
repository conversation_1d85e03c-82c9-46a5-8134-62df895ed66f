<?php

class calendarNewController extends BranchBasedController
{
    public $actionAccessAuths = array(
        'index'            => 'o_A_Access',
        'GetCalendarDays'  => 'o_A_Access',
        'SaveCalendarDays' => 'o_A_Adm_Calendar',
        'DelDay'           => 'o_A_Adm_Calendar',
    );


    public function createUrlReg($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function beforeAction($action)
    {
        parent::beforeAction($action);
        return true;
    }

    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout     = "//layouts/column1";

        $this->modernMenuFlag          = 'campusOp';
        $this->modernMenu              = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Campus Workspace');
        $cs                            = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/v-calendar/vue.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/v-calendar/v-calendar.umd.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/backbone-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/plupload/plupload.full.min.js');
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile($cs->getCoreScriptUrl() . '/jui/js/jquery-ui-i18n.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/phpfunc.js');
        $cs->registerCssFile(Yii::app()->theme->baseUrl . '/css/calendar.css');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/xlsx.full.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/jquery.qrcode.min.js');
        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray']   = array('//mcampus/calendarNew/index');


    }


    public function actionIndex()
    {
        $this->render('index');
    }

    public function actionGetHomePageDataBySchool()
    {
//        $yid = Yii::app()->request->getParam("yid");
//        $school_id = Yii::app()->request->getParam("school_id");
        $res = CommonUtils::requestDsOnline('calendar/getHomePageDataBySchool', array(
            'school_id' => $this->branchId,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionCreateCalendarDay()
    {
        $yid        = Yii::app()->request->getParam("yid");
        $title_cn   = Yii::app()->request->getParam("title_cn");
        $title_en   = Yii::app()->request->getParam("title_en");
        $memo_cn    = Yii::app()->request->getParam("memo_cn");
        $memo_en    = Yii::app()->request->getParam("memo_en");
        $event      = Yii::app()->request->getParam("event");
        $day_type   = Yii::app()->request->getParam("day_type");
        $event_type = Yii::app()->request->getParam("event_type");
        $date_list  = Yii::app()->request->getParam("date_list");
        $privacy    = Yii::app()->request->getParam("privacy", 0);
        $dept       = Yii::app()->request->getParam("dept", 0);
        $school_id  = $this->branchId;
        $res        = CommonUtils::requestDsOnline('calendar/createCalendarDay', array(
            'yid'        => $yid,
            'title_cn'   => $title_cn,
            'title_en'   => $title_en,
            'memo_cn'    => $memo_cn,
            'memo_en'    => $memo_en,
            'event'      => $event,
            'day_type'   => $day_type,
            'date_list'  => $date_list,
            'privacy'    => $privacy,
            'school_id'  => $school_id,
            'event_type' => $event_type,
            'dept'       => $dept,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionCreateNewCalendar()
    {
        $school_year      = Yii::app()->request->getParam("school_year");
        $title            = Yii::app()->request->getParam("title");
        $first_semester1  = Yii::app()->request->getParam("first_semester1");
        $first_semester2  = Yii::app()->request->getParam("first_semester2");
        $second_semester1 = Yii::app()->request->getParam("second_semester1");
        $second_semester2 = Yii::app()->request->getParam("second_semester2");
        $remark_cn        = Yii::app()->request->getParam("remark_cn");
        $remark_en        = Yii::app()->request->getParam("remark_en");
        $stat             = Yii::app()->request->getParam("stat");
        $res              = CommonUtils::requestDsOnline('calendar/createNewCalendar', array(
            'school_year'      => $school_year,
            'title'            => $title,
            'first_semester1'  => $first_semester1,
            'first_semester2'  => $first_semester2,
            'second_semester1' => $second_semester1,
            'second_semester2' => $second_semester2,
            'remark_cn'        => $remark_cn,
            'remark_en'        => $remark_en,
            'stat'             => $stat,#家长是否能查看校历10可以 20不可以
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionSaveCalendarYearlyStat()
    {
        $yid = Yii::app()->request->getParam("yid");
        $res = CommonUtils::requestDsOnline('calendar/saveCalendarYearlyStat', array(
            'yid' => $yid,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionGetCalendarData()
    {
        $yid       = Yii::app()->request->getParam("yid");
        $school_id = $this->branchId;
        $res       = CommonUtils::requestDsOnline('calendar/getCalendarData', array(
            'yid'       => $yid,
            'school_id' => $school_id,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionSaveCalendar()
    {
        $yid              = Yii::app()->request->getParam("yid");
        $title            = Yii::app()->request->getParam("title");
        $first_semester1  = Yii::app()->request->getParam("first_semester1");
        $first_semester2  = Yii::app()->request->getParam("first_semester2");
        $second_semester1 = Yii::app()->request->getParam("second_semester1");
        $second_semester2 = Yii::app()->request->getParam("second_semester2");
        $remark_cn        = Yii::app()->request->getParam("remark_cn");
        $remark_en        = Yii::app()->request->getParam("remark_en");
        $stat             = Yii::app()->request->getParam("stat");
        $res              = CommonUtils::requestDsOnline('calendar/saveCalendar', array(
            'yid'              => $yid,
            'title'            => $title,
            'first_semester1'  => $first_semester1,
            'first_semester2'  => $first_semester2,
            'second_semester1' => $second_semester1,
            'second_semester2' => $second_semester2,
            'remark_cn'        => $remark_cn,
            'remark_en'        => $remark_en,
            'stat'             => $stat,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionDelCalendarDay()
    {
        $yid        = Yii::app()->request->getParam("yid");
        $bindend_id = Yii::app()->request->getParam("bindend_id");
        $res        = CommonUtils::requestDsOnline('calendar/delCalendarDay', array(
            'yid'        => $yid,
            'bindend_id' => $bindend_id,
            'school_id'  => $this->branchId,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionSaveCalendarDay()
    {
        $yid                     = Yii::app()->request->getParam("yid");
        $title_cn                = Yii::app()->request->getParam("title_cn");
        $title_en                = Yii::app()->request->getParam("title_en");
        $edit_calendar_start_day = Yii::app()->request->getParam("edit_calendar_start_day");
        $edit_calendar_end_day   = Yii::app()->request->getParam("edit_calendar_end_day");
        $time                    = Yii::app()->request->getParam("time");
        $bindend_id              = Yii::app()->request->getParam("bindend_id");
        $event                   = Yii::app()->request->getParam("event");
        $day_type                = Yii::app()->request->getParam("day_type");
        $memo_cn                 = Yii::app()->request->getParam("memo_cn", '');
        $memo_en                 = Yii::app()->request->getParam("memo_en", '');
        $privacy                 = Yii::app()->request->getParam("privacy", 0);
        $event_type              = Yii::app()->request->getParam("event_type", 0);
        $dept                    = Yii::app()->request->getParam("dept", 0);
        $school_id               = $this->branchId;
        $res                     = CommonUtils::requestDsOnline('calendar/saveCalendarDay', array(
            'yid'                     => $yid,
            'bindend_id'              => $bindend_id,
            'title_cn'                => $title_cn,
            'title_en'                => $title_en,
            'edit_calendar_start_day' => $edit_calendar_start_day,
            'edit_calendar_end_day'   => $edit_calendar_end_day,
            'time'                    => $time,
            'event_type'              => $event_type,
            'event'                   => $event,
            'day_type'                => $day_type,
            'memo_cn'                 => $memo_cn,
            'memo_en'                 => $memo_en,
            'privacy'                 => $privacy,
            'school_id'               => $school_id,
            'dept'                    => $dept,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    // 获取七牛上传的token
    public function actionGetQiniuToken()
    {
        $school_id = Yii::app()->request->getParam("school_id");
        $yid       = Yii::app()->request->getParam("yid");
        $linkId    = $school_id . $yid;
        $data      = array(
            'linkType'  => 'calendar',
            'linkId'    => $linkId,
            'isPrivate' => 0,
        );
        $res       = CommonUtils::requestDsOnline('getQiniuToken', $data);
        $this->addMessage("state", "success");
        $this->addMessage('data', $res);
        $this->showMessage();
    }

    public function actionSaveCalendarPDF()
    {
        $yid        = Yii::app()->request->getParam("yid");
        $pdf_url_cn = Yii::app()->request->getParam("pdf_url_cn", NULL);#上传pdf家长可以下载否则不能
        $pdf_url_en = Yii::app()->request->getParam("pdf_url_en", NULL);
        $school_id  = $this->branchId;

        $res = CommonUtils::requestDsOnline('calendar/saveCalendarPDF', array(
            'yid'        => $yid,
            'school_id'  => $school_id,
            'pdf_url_cn' => $pdf_url_cn,
            'pdf_url_en' => $pdf_url_en,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    /**
     * 导出校历的假期 事件 调休
     */
    public function actionExportEvent()
    {
        $yid       = Yii::app()->request->getParam("yid");
        $school_id = $this->branchId;
        $res       = CommonUtils::requestDsOnline('calendar/exportEvent', array(
            'yid'       => $yid,
            'school_id' => $school_id,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    /***
     * 学校 导入校历的活动事件 前端读取数据
     */
    public function actionImportEvent2(){
        $yid = Yii::app()->request->getPost("yid");
        $data = Yii::app()->request->getPost("data");
        $tempData = html_entity_decode($data);
        $data = json_decode($tempData, true);
        if (empty($this->branchId) || empty($yid)) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'parameter error');
            $this->showMessage();
        }
        foreach ($data as $item) {
            $start_day1 = trim($item['StartDateAndTime']);
            $end_day   = trim($item['EndDateAndTime']);
            $event     = trim($item['Event']);
            $day_type  = trim($item['FullOrHalfDay']);
            $title_cn  = trim($item['TitleCn']);
            $title_en  = trim($item['TitleEn']);
            $division  = trim($item['Division']);
            $Categories = trim($item['Categories']);
            //开始时间
            if (empty($start_day1)) {
                $this->addMessage('message', '开始时间不能为空');
                $this->showMessage();
            }
            //结束时间
            if (empty($end_day)) {
                $this->addMessage('message', '结束时间不能为空');
                $this->showMessage();
            }
            if (empty($event)) {
                $this->addMessage('message', '校例日类型不能为空');
                $this->showMessage();
            }
            if (empty($day_type)) {
                $this->addMessage('message', '时间类型不能为空');
                $this->showMessage();
            }
            if (!in_array(strtolower($day_type), array('full', 'half', '全天', '半天'))) {
                $this->addMessage('message', '时间类型不正确');
                $this->showMessage();
            }
            if (empty($title_cn) || empty($title_en)) {
                $this->addMessage('message', '中英文标题不能为空');
                $this->showMessage();
            }
            if($this->branchId !='BJ_DS' && in_array($division,array('ES','SS'))){
                $this->addMessage('message', 'Division没有权限选择ES或者SS');
                $this->showMessage();
            }
            if(preg_match('/[\x{4e00}-\x{9fa5}]/u', $Categories) === 1){
                $this->addMessage('message', 'Categories使用对应的英文');
                $this->showMessage();
            }
        }
        $input_data = array();
        foreach ($data as $key => $val) {
            $event     = trim($val['Event']);
            if (!in_array(strtolower($event), array('event', '事件'))) {
                continue;
            }
            if (in_array(trim($val['FullOrHalfDay']), array('full', '全天'))) {
                $day_type = 10;
            } else {
                $day_type = 20;
            }
            if($val['Division'] == 'ES'){
                $dept = 1;
            }elseif ($val['Division'] == 'SS'){
                $dept = 2;
            }else{
                $dept = 0;
            }
            $privacy          = trim($val['OnlyStaffSee']);
            $input_data[$key] = array(
                'date_list' => date('Y.m.d', strtotime(trim($val['StartDateAndTime']))) . '-' . date('Y.m.d', strtotime(trim($val['EndDateAndTime']))),
                #开始时间分时
                'start_time' => $this->hasTime($val['StartDateAndTime']) ? date('H:i', strtotime(trim($val['StartDateAndTime']))) : '',
                #结束时间分时
                'end_time'   => $this->hasTime($val['EndDateAndTime']) ? date('H:i', strtotime(trim($val['EndDateAndTime']))) : '',
                'yid'       => $yid,
                'day_type'  => $day_type,
                'dept'      => $dept,
                'event'     => 20,//学校只能上传事件不能传假期，调休等
                'title_cn'  => trim($val['TitleCn']),
                'title_en'  => trim($val['TitleEn']),
                'memo_cn'   => trim($val['DescriptionCn']),
                'memo_en'   => trim($val['DescriptionEn']),
                'privacy'   => !empty($privacy) && $privacy === 'Y' ? 1 : 0,
                'event_type'=>trim($val['Categories']),
                'locations'=>trim($val['Location']),
                'school_id' => $this->branchId,
                'user_id'   => Yii::app()->user->getId(),
            );
        }
//        var_dump($input_data);die;
        //提交导入数据
        $res = CommonUtils::requestDsOnline2('calendar/importEvent', array('data' => $input_data),'post');
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }


    /***
     * 学校 导入校历的事件 使用php读取二进制文件
     */
    public function actionImportEvent()
    {
        $yid = Yii::app()->request->getParam("yid");
        if (empty($this->branchId) || empty($yid)) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'parameter error');
            $this->showMessage();
        }
        Yii::import('common.models.visit.*');
        $model = new EventImportForm;
        if (isset($_POST['EventImportForm'])) {
            $model->attributes = $_POST['EventImportForm'];
            if ($model->validate()) {
                $model->csv = CUploadedFile::getInstance($model, 'csv');
                if ($model->csv) {
                    $fileName = uniqid('', true) . '.' . $model->csv->getExtensionName();
                    $model->csv->saveAs(Yii::app()->params['xoopsVarPath'] . '/' . $fileName);
                    if (is_file(Yii::app()->params['xoopsVarPath'] . '/' . $fileName)) {
                        $fileArray = file(Yii::app()->params['xoopsVarPath'] . '/' . $fileName);
                        @unlink(Yii::app()->params['xoopsVarPath'] . '/' . $fileName);
                        foreach ($fileArray as $key=>$val) {
                            if ($key > 0) {
                                $val = mb_convert_encoding( $val, 'UTF-8', 'UTF-8,GBK,GB2312,BIG5,ASCII' );//iconv('GB2312', 'UTF-8', $val);
                                $val          = trim(str_replace(';', ',', $val));
                                $explode_data = explode(',', $val);
                                $unique_data  = array_unique(array_values($explode_data));
                                if (!empty($unique_data[0])) {
                                    $valArray[] = explode(',', $val);
                                }
                            }
                        }
                        //数据检查
                        //0 开始时间 1结束时间 2校例日类型 3时间类型all全天 Half半天 4标题中文 5标题英文 6描述中文 7描述英文 8仅员工可见
                        //9活动事件的类型 10活动事件的类型中文 11活动地点  12部门
                        $this->addMessage('state', 'fail');
                        foreach ($valArray as $item) {
                            $start_day1 = trim($item[0]);
                            $end_day   = trim($item[1]);
                            $event     = trim($item[2]);
                            $day_type  = trim($item[3]);
                            $title_cn  = trim($item[4]);
                            $title_en  = trim($item[5]);
                            //开始时间
                            if (empty($start_day1)) {
                                $this->addMessage('message', '开始时间不能为空');
                                $this->showMessage();
                            }
                            $start_day = strtotime(date($start_day1));
                            if (!$start_day) {
                                $this->addMessage('message', $start_day1.'开始时间格式不正确');
                                $this->showMessage();
                            }
                            //结束时间
                            if (empty($end_day)) {
                                $this->addMessage('message', '结束时间不能为空');
                                $this->showMessage();
                            }
                            $end_day = strtotime(date($end_day));
                            if (!$end_day) {
                                $this->addMessage('message', '结束时间格式不正确');
                                $this->showMessage();
                            }

                            if (empty($event)) {
                                $this->addMessage('message', '校例日类型不能为空');
                                $this->showMessage();
                            }
                            if (!in_array(strtolower($event), array('event', '事件'))) {
                                $this->addMessage('message', '学校只能导入校历事件');
                                $this->showMessage();
                            }

                            if (empty($day_type)) {
                                $this->addMessage('message', '时间类型不能为空');
                                $this->showMessage();
                            }
                            if (!in_array(strtolower($day_type), array('full', 'half', '全天', '半天'))) {
                                $this->addMessage('message', '时间类型不正确');
                                $this->showMessage();
                            }

                            if (empty($title_cn) || empty($title_en)) {
                                $this->addMessage('message', '中英文标题不能为空');
                                $this->showMessage();
                            }

                        }
                        //重新赋值数据
                        //0 开始时间 1结束时间 2校例日类型 3时间类型all全天 Half半天 4标题中文 5标题英文 6描述中文 7描述英文 8仅员工可见
                        //9活动事件的类型 10活动事件的类型中文 11活动地点  12部门
                        $input_data = array();
//                        var_dump($valArray);
                        foreach ($valArray as $key => $val) {
                            if (in_array(trim($val[3]), array('full', '全天'))) {
                                $day_type = 10;
                            } else {
                                $day_type = 20;
                            }
                            $privacy          = trim($val[8]);
                            $input_data[$key] = array(
                                'date_list' => date('Y.m.d', strtotime(trim($val[0]))) . '-' . date('Y.m.d', strtotime(trim($val[1]))),
                                'start_time'=>date('H:i', strtotime(trim($val[0]))),#开始时间分时
                                'end_time'  =>date('H:i', strtotime(trim($val[1]))),#结束时间分时
                                'yid'       => $yid,
                                'day_type'  => $day_type,
                                'event'     => 20,//学校只能上传事件不能传假期，调休等
                                'title_cn'  => trim($val[4]),
                                'title_en'  => trim($val[5]),
                                'memo_cn'   => trim($val[6]),
                                'memo_en'   => trim($val[7]),
                                'privacy'   => !empty($privacy) && $privacy === 'Y' ? 1 : 0,
                                'event_type'=>trim($val[9]),
                                'locations'=>trim($val[11]),
                                'school_id' => $this->branchId,
                                'user_id'   => Yii::app()->user->getId(),
                            );
                        }
//                        var_dump($input_data);die;
                        //提交导入数据
                        $res = CommonUtils::requestDsOnline2('calendar/importEvent', array('data' => $input_data),'post');
                        if ($res['code'] == 0) {
                            $this->addMessage('state', 'success');
                            $this->addMessage('data', $res);
                            $this->showMessage();
                        } else {
                            $this->addMessage('state', 'fail');
                            $this->addMessage('message', $res['msg']);
                            $this->showMessage();
                        }
                    }else{
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', '读取文件失败');
                    }
                }else{
                    $this->addMessage('state', 'fail');
                    $errs = current($model->getErrors());
                    $this->addMessage('message', $errs ? $errs[0] : Yii::t('message', 'Failed!'));
                }
            } else {
                $this->addMessage('state', 'fail');
                $errs = current($model->getErrors());
                $this->addMessage('message', $errs ? $errs[0] : Yii::t('message', 'Failed!'));
            }
            $this->showMessage();
        }
    }

    //判断是否输入了时分
    function hasTime($dateTimeString)
    {
        return count(explode(':', $dateTimeString)) == 2;
    }

    //清空所有事件
    public function actionClearAllEvent()
    {
        Yii::import('common.models.calendar.*');
        $yid = Yii::app()->request->getParam("yid");
        $school_id = $this->branchId;
        if(empty($school_id)){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'school_id null');
            $this->showMessage();
        }
        $res = CommonUtils::requestDsOnline('calendar/clearAllEvent', array(
                'yid' => $yid,
                'schoolid' => $school_id,
            )
        );
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

}
