<?php $form=$this->beginWidget('CActiveForm', array(
    'id'=>'title-edit-form',
    'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
)); ?>
    <div class="pop_cont" style="height: 400px;overflow-y: auto;">
        <div class="form-group">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($model->link,'department_id'); ?></label>
            <div class="col-xs-9">
                <?php
                echo $form->dropDownList($model->link, 'department_id', CHtml::listData($depts, 'id', Yii::app()->language=='zh_cn'?'cn_name':'en_name'), array('empty'=>Yii::t('global','Please Select'), 'class'=>'form-control'));
                ?>
            </div>
        </div>
        <div class="form-group">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'en_name'); ?></label>
            <div class="col-xs-9">
                <?php echo $form->textField($model, 'en_name', array('class'=>'form-control'));?>
            </div>
        </div>
        <div class="form-group">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'cn_name'); ?></label>
            <div class="col-xs-9">
                <?php echo $form->textField($model, 'cn_name', array('class'=>'form-control'));?>
            </div>
        </div>
        <div class="form-group">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'weight'); ?></label>
            <div class="col-xs-9">
                <?php echo $form->textField($model, 'weight', array('class'=>'form-control'));?>
            </div>
        </div>
        <div class="form-group">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'status'); ?></label>
            <div class="col-xs-9">
                <?php echo $form->checkBox($model, 'status', array('value'=>1));?>
            </div>
        </div>
        <div class="form-group">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($model->link,'is_lead'); ?></label>
            <div class="col-xs-9">
                <?php echo $form->checkBox($model->link, 'is_lead', array('value'=>1));?>
            </div>
        </div>
        <div class="form-group">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'_role'); ?></label>
            <div class="col-xs-9">
                <div class="row">
                <?php echo $form->checkBoxList($model, '_role', CHtml::listData($roles, 'name', 'name'), array('separator'=>'', 'template'=>'<div class="col-xs-5">{input} {label}</div>'));?>
                </div>
            </div>
        </div>
    </div>
    <div class="pop_bottom">
        <button id="J_dialog_close" type="button" class="btn btn-default pull-right"><?php echo Yii::t('global','Cancel');?></button>
        <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
    </div>
<?php $this->endWidget(); ?>