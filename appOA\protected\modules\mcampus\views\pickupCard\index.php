<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', '接送卡'), array('//mcampus/pickupCard/index')) ?></li>
    </ol>

    <div class="row">
        <!-- 左侧菜单 -->
        <div class="col-md-2 col-sm-12">
            <div class="list-group" id="classroom-status-list">
                <a href="<?php echo $this->createUrl('index'); ?>" class="list-group-item status-filter active"><?php echo Yii::t("newDS", "接送卡");?></a>
                <a href="<?php echo $this->createUrl('homeAddress/index'); ?>" class="list-group-item status-filter"><?php echo Yii::t("newDS", "家庭居住地址");?></a>
                <a href="<?php echo $this->createUrl('bankAccountInfo/index'); ?>" class="list-group-item status-filter"><?php echo Yii::t("newDS", "银行账户信息");?></a>
            </div>
            <div class="text-center" v-if='qrcodeImg!=""'>
                <h5><?php echo Yii::t("newDS", "家长二维码入口");?></h5>
                <p><img class='qrcodeImg' src="http://m2.files.ivykids.cn/cloud01-file-8025768FrYTEr4kBrOMc2Y_ngpjf30eWJWH.png" alt=""></p>
            </div>
        </div>
        <div class="col-md-10 col-sm-10" id='container' v-cloak>
            <div class="panel panel-default">
                <div class="panel-heading">当前状态</div>
                <div class="panel-body" v-if='Object.keys(initData).length != 0'>
                    <div v-if='initData.start!=""'>
                        <p class='font14 mb20'>
                            <strong>{{initData.start_year}}-{{initData.start_year+1}}学年</strong>
                        </p>
                        <p>
                            <span class='mr20'>开放时间：{{initData.start}}-{{initData.end}}</span>
                            <span class='ml20'>车牌输入：{{initData.enable_plates==1?"开启":"未开启"}} </span>
                            <!-- <span class='cur-p ml10 text-primary'>
                                <span class='glyphicon glyphicon-download-alt'></span><span> 导出所有车牌号</span>
                            </span> -->
                        </p>
                    </div>
                    <div v-else   class='font14 mb20'>
                        <strong>未设置</strong>
                    </div>
                    <p>
                        <button type="button" class="btn btn-primary mt10"  @click='editInfo()'>
                            <span class="glyphicon glyphicon-pencil" aria-hidden="true"></span> 设置
                        </button>
                    </p>
                </div>
            </div>
            <div class="panel panel-default" v-if='Object.keys(initData).length != 0 && initData.tips_text_en!=""'>
                <div class="panel-heading">学生接送卡信息</div>
                    <div class="panel-body">
                    <div class='col-sm-2 scroll-box' style='max-height:500px;overflow-y:auto'>
                        <ul class="list-group " >
                            <li class="list-group-item" :class='list.class_id==activeId?"classActive":""' v-for='(list,index) in initData.classList'  @click='tabList(list.class_id)'>
                                {{list.title}} <span class="badge floatNone" v-if='list.newSubmitNum>0'>{{list.newSubmitNum}}</span>
                                <div class="progress pull-left mt10 mb5" style='height:8px;width:80%'>
                                    <div class="progress-bar progress-bar-success" role="progressbar" aria-valuenow="40" aria-valuemin="0" aria-valuemax="100" :style="progress(list)">
                                    </div>
                                </div>
                                <div class="pull-right mt5">{{list.submitNum}}/{{list.total}}</div>
                                <div class='clearfix'></div>
                            </li>
                        </ul>
                    </div>
                    <div class='col-sm-10'  v-if='tableData.childInfo && Object.keys(tableData.childInfo).length != 0'>
                        <div>
                            <div class="btn-group">
                                <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    批量操作 <span class="caret"></span>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a href="#">
                                    <form action="<?php echo $this->createUrl('print', array('branchId' => $this->branchId)); ?>" method="post">
                                        <button  class="btn btn-link btn-xs padd0 color3"  style='width:100%;text-align:left'>打印</button>
                                        <input type="hidden"  v-for='(list,id) in printIds' checked :value='list' name='ids[]'>
                                    </form>
                                    </a>
                                    </li>
                                    <li><a href="#" @click='markMade("1","checkbox")'>标记为已制作</a></li>
                                    <li><a href="#" @click='markMade("0","checkbox")'>标记为未制作</a></li>
                                </ul>
                            </div>
                            <span class='text-success ml10'>已选择{{printIds.length}}名学生</span>
                            <button  class="btn btn-default pull-right" @click='wechatModel'> 发送微信提醒</button>
                        </div>
                        <table class="table mt10">
                            <thead>
                                <tr>
                                <th><input type="checkbox" id="inlineCheckbox1" v-model="printAll" @change='printAllData()'></th>
                                <th>学生</th>
                                <th>审核状态</th>
                                <th>是否制作</th>
                                <th>接送车牌号</th>
                                <th>接送人数</th>
                                <th width='160'>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for='(list,index) in dataList'>
                                    <th scope="row"><input type="checkbox" id="inlineCheckbox1" v-model='printIds'  @change='printData()' :value="list.id"></th>
                                    <td> <a href='javascript:void(0);' @click='stuInfo(list.child_id)'>{{tableData.childInfo[list.child_id]}}</a></td>
                                    <td>{{initData.statusList[list.audit_status]}}</td>
                                    <td>{{list.is_made==1?"是":"否"}}</td>
                                    <td><span v-for='(item,id) in list.plates'>{{item}} <span v-if='id!=list.plates.length-1'>｜</span></span></td>
                                    <td>{{list.pickup_num}}</td>
                                    <td>
                                        <a href="javascript:;" @click='editCard(list)'>查看详情</a>
                                        <span v-if='list.audit_status==1'>
                                            <a href="javascript:;" v-if='list.is_made==0' class='ml10' @click='markMade("1",list.id)'>标记为已制作</a>
                                        </span>
                                    </td>
                                </tr>
                                <tr v-for='(list,index) in tableData.unSubmitChild'>
                                    <th scope="row"><input type="checkbox" id="inlineCheckbox1" disabled></th>
                                    <td><a href='javascript:void(0);' @click='stuInfo(list)'>{{tableData.childInfo[list]}}</a></td>
                                    <td>-</td>
                                    <td>-</td>
                                    <td>-</td>
                                    <td>-</td>
                                    <td>-</td>
                                </tr>
                                <tr v-for='(list,index) in tableData.regChildList'>
                                    <th scope="row"><input type="checkbox" id="inlineCheckbox1" disabled></th>
                                    <td><a href='javascript:void(0);' @click='stuInfo(list)'>{{tableData.childInfo[list]}}</a>（已在新生注册流程中处理）</td>
                                    <td>-</td>
                                    <td>-</td>
                                    <td>-</td>
                                    <td>-</td>
                                    <td>-</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal fade" tabindex="-1" role="dialog" id='infoModal'>
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title">学生接送卡管理</h4>
                        </div>
                        <div class="modal-body">
                            <p class='font14 mb20'>
                                <strong>接送卡文字说明（显示在家长端）</strong>
                            </p>
                            <div class="form-group mb5">
                                <label for="inputEmail3" class="col-sm-2 control-label">中文</label>
                                <div class="col-sm-10 mb15">
                                <textarea class="form-control" v-model='tips_text_cn' rows="10"></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputPassword3" class="col-sm-2 control-label">英文</label>
                                <div class="col-sm-10 mb15">
                                <textarea class="form-control" v-model='tips_text_en' rows="10"></textarea>
                                </div>
                            </div>
                            <p class='font14 mb20'>
                                <strong>开放填写时间段</strong>
                            </p>
                            <div class="form-group">
                                <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t("newDS", "开始时间");?></label>
                                <div class="col-sm-10 mb15">
                                    <input type="text" class="form-control  select_3 pull-left" id='startDate'  v-model='startDate'  placeholder="<?php echo Yii::t("newDS", "Select a date");?>"   :value='startDate'>
                                    <select class="form-control selectWidth ml20 pull-left" v-model='startHours'>
                                        <option v-for='(list,index) in 25'>{{index < 10?'0'+index:index}}</option>
                                    </select>
                                    <select class="form-control ml5 selectWidth pull-left"   v-model='startMinutes'>
                                        <option v-for='(list,index) in 61' >{{index < 10?'0'+index:index}}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t("newDS", "结束时间");?></label>
                                <div class="col-sm-10 mb15">
                                    <input type="text" class="form-control  select_3 pull-left"  id='endDate'  v-model='endDate' placeholder="<?php echo Yii::t("newDS", "Select a date");?>"   :value='endDate'>
                                    <select class="form-control selectWidth ml20 pull-left" v-model='endHours'>
                                        <option v-for='(list,index) in 25'>{{index < 10?'0'+index:index}}</option>
                                    </select>
                                    <select class="form-control ml5 selectWidth pull-left"   v-model='endMinutes'>
                                        <option v-for='(list,index) in 61' >{{index < 10?'0'+index:index}}</option>
                                    </select>
                                </div>
                            </div>
                            <p class='font14 mb20'>
                                <strong>车牌输入状态</strong>
                            </p>
                            <div class='ml10 mb20'>
                                <label class="radio-inline">
                                    <input type="radio" name="inlineRadioOptions" v-model='enable_plates'  value="1"> 开启
                                </label>
                                <label class="radio-inline">
                                    <input type="radio" name="inlineRadioOptions" v-model='enable_plates'  value="0"> 关闭
                                </label>
                            </div>
                            <div class="form-group mb5" v-if='enable_plates==1'>
                                <label for="inputEmail3" class="col-sm-2 control-label">车牌中文</label>
                                <div class="col-sm-10 mb15">
                                <textarea class="form-control" v-model='tips_plates_cn' rows="3"></textarea>
                                </div>
                            </div>
                            <div class="form-group mb5" v-if='enable_plates==1'>
                                <label for="inputEmail3" class="col-sm-2 control-label">车牌英文</label>
                                <div class="col-sm-10 mb15">
                                <textarea class="form-control" v-model='tips_plates_en' rows="3"></textarea>
                                </div>
                            </div>
                            <div class='clearfix'></div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                            <button type="button" class="btn btn-primary" @click='saveInfo()'>保存</button>
                        </div>
                    </div><!-- /.modal-content -->
                </div><!-- /.modal-dialog -->
            </div><!-- /.modal -->
            <div class="modal fade" tabindex="-1" role="dialog" id="editModal">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            详细信息
                        </div>
                        <div class="modal-body">
                        <p class='font14'><strong>接送车牌号：</strong><span v-for='(item,id) in childDetail.plates'>{{item}} <span v-if='id!=childDetail.plates.length-1'>｜</span></span></p>
                            <div class="borderNone col-md-12 padd0 relative mb20">
                                <img :src="childDetail.school_id=='BJ_QFF' || childDetail.school_id=='BJ_SLT'  || childDetail.school_id=='BJ_DS'?bgImg:ivyImg" style='width:100%;height:100%;position: absolute;'>
                                <div class="col-md-6 borderRight padd0 h100">
                                    <div  v-for='(list,idx) in childDetail.pickups'>
                                        <div class="datalist" v-if='list.name!=null'>
                                            <div class='list'>
                                                <p><img :src="list.head_url" alt="" class="ptintImg"></p>
                                                <div class="keepAll flex" >
                                                <span class='flexW'> <?php echo Yii::t('global','Name') ?><?php echo Yii::t('global',': ') ?></span>	
                                                <span class='flex1'>{{list.name}}</span>
                                                </div>
                                                <div class=""><?php echo Yii::t('asa','Phone') ?><?php echo Yii::t('global',': ') ?>{{trim(list.phone)}}</div>
                                                <div class=""><?php echo Yii::t('site','Relationship') ?><?php echo Yii::t('global',': ') ?>{{list.relation}}</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div style="clear: both;"></div>
                                </div>
                                <div class="col-md-6 pt10 h100 relative">
                                    <div class="h100 text-center ">
                                        <p class="pt30"><img :src="logo" alt="" :style='childDetail.school_id=="BJ_QFF" || childDetail.school_id=="BJ_SLT"  || childDetail.school_id=="BJ_DS"?"width:180px":"width:108px"'></p>
                                        <img :src="childDetail.child_avatar" class="mt10 chilidPto " alt="">
                                        <div class="text-center pt10 fontSize">{{childDetail.child_name_cn}}<br>{{childDetail.child_name_en}}</div>
                                    </div>
                                    <div class='text-left absolute'>
                                        <div>{{childDetail.start_year}}-{{childDetail.start_year+1}}</div>
                                        <div>{{schoolTitle}}</div>
                                    </div>
                                </div>
                            </div>
                            <div>
                            <!-- <form action="<?php echo $this->createUrl('print', array('branchId' => $this->branchId)); ?>" method="post">
                                <button type="button"  class="btn btn-primary pull-right" data-dismiss="modal"><?php echo Yii::t("newDS", "打印");?></button>
                                <input type="hidden"  checked :value='tabDetailData.id' name='ids[]'>
                            </form> -->
                            <div class='clearfix'></div>
                            </div>
                            <hr>
                            <div class='col-md-12 col-sm-12 font14'>
                                <div class='col-md-6 col-sm-6 borderRight scroll-box' style='max-height:250px;overflow-y:auto'>
                                    <p><strong>当前审核状态</strong></p>
                                    <div>{{childDetail.audit_status==0?'未审核':childDetail.audit_status==1?'通过':childDetail.audit_status==2?'未通过':'重置'}}
                                    <button type="button" class="btn btn-primary  btn-xs pull-right" @click='resetStatus("show")'  v-if='childDetail.audit_status==1 || childDetail.audit_status==2'>重置</button>
                            
                                    </div>
                                    <p class='mt10'><strong>审核记录</strong></p>
                                    <div>
                                        <div v-for='(list,index) in childDetail.audit_comments' class='ml10' style='border-left:1px dashed #ccc'>
                                        <p style='margin-left:-7px;background: #fff;' >
                                            <span v-if='list.status==1'>
                                            <span class='glyphicon glyphicon-ok-sign green'></span><span>已通过</span> 
                                            </span>
                                            <span v-if='list.status==2'>
                                            <span class='glyphicon glyphicon-info-sign red'></span> <span>驳回</span>
                                            </span>
                                            <span v-if='list.status==0'>
                                            <span class='glyphicon glyphicon-question-sign yellow'></span> <span>重置</span>
                                            </span>
                                            <span v-if='list.status==4'>
                                            <span class='glyphicon glyphicon-ok-sign yellow'></span> <span>取消制卡</span>
                                            </span>
                                            <span v-if='list.status==5'>
                                            <span class='glyphicon glyphicon-ok-sign green'></span> <span>已制卡</span>
                                            </span>
                                            </p> 
                                        <p class='ml10' style='background:#F7F7F8;line-height:22px' v-if='list.status==2'>原因：{{list.comment}}</p>
                                        <p class='pl10 font12'>{{list.date}} {{list.user}}</p>
                                        </div>
                                    </div>
                                </div>
                                <div class='col-md-6 col-sm-6' v-if='childDetail.audit_status==0 || childDetail.audit_status==3'>
                                    <p><strong>审核</strong></p>
                                    <p>
                                        <label class="radio-inline">
                                            <input type="radio" id="inlineradio1" v-model='auditStatus' value="1"> 通过
                                        </label>
                                        <label class="radio-inline">
                                            <input type="radio" id="inlineradio2" v-model='auditStatus' value="2"> 未通过
                                        </label>
                                    </p>
                                    <div v-if='auditStatus==2'>
                                        <textarea class="form-control" rows="3" placeholder='请输入未通过原因' v-model='comment'></textarea>
                                        <p class='mt10'>修改建议将直接显示在家长端，请准确措辞。</p>   
                                        <div class="checkbox">
                                            <label>
                                                <input type="checkbox"  v-model='send_wechat'>将审核结果发送至家长微信
                                            </label>
                                        </div>
                                    </div>
                                    <div>
                                        <button type="button"  class="btn btn-primary pull-right" @click='saveAudit()'><?php echo Yii::t("newDS", "确认");?></button>
                                    </div>
                                </div>
                            </div>
                            <div class='clearfix'></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal fade" tabindex="-1" role="dialog" id="wechatModal">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            发送微信提醒 <span class='ml20'>24小时最多提醒一次</span>
                        </div>
                        <div class="modal-body">
                            <div class='col-md-12 col-sm-12 font14'>
                                <div class='col-md-6 col-sm-6 borderRight scroll-box' style='max-height:600px;min-height:400px;overflow-y:auto'>
                                    <p><strong>选择学生</strong></p>
                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox"  v-model="wechatAll" @change='wechatAllData()' >全选
                                        </label>
                                    </div>
                                    <div class='ml20'>
                                        <div class="checkbox"  v-for='(list,index) in tableData.unSubmitChild'>
                                            <label v-if='sendData[list]' >
                                                <span  v-if='sendData[list].can_send==0'>
                                                    <input type="checkbox" disabled='true' :value="list" v-model='wechatList'  @change='wechatData()'>{{tableData.childInfo[list]}} <span class='ml20 font12 red'>上次提醒：{{sendData[list].latest_date}}</span>
                                                </span>
                                               <span v-else>
                                               <input type="checkbox"  :value="list" v-model='wechatList'  @change='wechatData()'>{{tableData.childInfo[list]}} <span class='ml20 font12 color6'>上次提醒：{{sendData[list].latest_date}}</span>
                                               </span>
                                                
                                            </label>
                                            <label v-else >
                                                <input type="checkbox" :value="list" v-model='wechatList'  @change='wechatData()'>{{tableData.childInfo[list]}}
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class='col-md-6 col-sm-6'>
                                    <p><strong>微信消息示例（截图）</strong></p>
                                    <div>
                                        <img src="http://m2.files.ivykids.cn/cloud01-file-8025768Fnaa95WCpEPPfK2opGJYOxJ8DgL0.jpg" style='width:100%' alt="">
                                    </div>
                                </div>
                            </div>
                            <div class='clearfix'></div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("newDS", "取消");?></button>
                            <button type="button" class="btn btn-primary" :disabled='wechatBtn' @click='sendWechat()'><?php echo Yii::t("newDS", "发送微信通知");?></button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal fade" tabindex="-1" role="dialog" id="resetModal">
                <div class="modal-dialog modal-sm" role="document">
                    <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title"><?php echo Yii::t("newDS", "提示");?></h4>
                    </div>
                    <div class="modal-body">
                        重置之后需家长再次提交审核，同时将标记为“未制作”
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("newDS", "取消");?></button>
                        <button type="button" class="btn btn-primary" @click='resetStatus("ok")'><?php echo Yii::t("newDS", "确定");?></button>
                    </div>
                    </div>
                </div>
            </div>
            <div class="modal fade" tabindex="-1" role="dialog" id='childInfo'>
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title"><?php echo Yii::t("site", "Student Information");?></h4>
                    </div>
                    <div class="modal-body">
                        <div class="text-center marginAuto mb20">
                            <img :src='stuData.photo' class="imgCover" alt="">
                            <p class='font14 fontBold mt10'>{{stuData.name}} | {{childId}}</p>
                        </div>
                        <div  class="col-md-12 mb20 mt20">
                            <div  class="col-md-3">
                                <p class="font12 color6"> <?php echo Yii::t("labels", "Legal First Name");?></p>
                                <p class="font14 color3">{{stuData.first_name_en}}</p>
                            </div>
                            <div  class="col-md-3">
                                <p class="font12 color6"><?php echo Yii::t("labels", "Legal Middle Name");?></p>
                                <p class="font14 color3">{{stuData.middle_name_en}}</p>
                            </div>
                            <div  class="col-md-3">
                                <p class="font12 color6"><?php echo Yii::t("labels", "Legal Last Name");?></p>
                                <p class="font14 color3">{{stuData.last_name_en}}</p>
                            </div>
                            <div  class="col-md-3">
                                <p class='font12 color6'><?php echo Yii::t("labels", "Preferred Name");?></p>
                                <p class='font14 color3'>{{stuData.nick}}</p>
                            </div>
                        </div>
                        <div  class="col-md-12 ">
                            <div  class="col-md-3">
                                <p class="font12 color6"><?php echo Yii::t("labels", "Nationality");?></p>
                                <p class="font14 color">{{stuData.country}}</p>
                            </div>
                            <div  class="col-md-3">
                                <p class="font12 color6"><?php echo Yii::t("labels", "Date of Birth");?></p> 
                                <p class="font14 color">{{stuData.birthday_search}}</p>
                            </div>
                            <div  class="col-md-3">
                                <p class="font12 color6"><?php echo Yii::t("labels", "Gender");?></p> 
                                <p class="font14 color">{{stuData.gender==1?"男":"女"}}</p>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                        <hr>
                        <div  class="col-md-12">
                        <div  class="col-md-3">
                                <p class="font12 color6"><?php echo Yii::t("labels", "Bus NO.");?></p>
                                <p class="font14 color">{{stuData.busId}}</p>
                            </div>
                            <div  class="col-md-9">
                                <p class="font12 color6"><?php echo Yii::t("labels", "Bus Notes");?></p>
                                <p class="font14 color">{{stuData.BusContent}}</p>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                        <hr>
                        <div  class="col-md-12 mb20">
                            <div  class="col-md-3">
                                <p class="font12 color6"><?php echo Yii::t("labels", "Father");?></p>                           
                                <p class="font14 color">{{stuData.fName}}</p>
                            </div>
                            <div  class="col-md-3">
                                <p class="font12 color6"><?php echo Yii::t("labels", "Mobile Phone");?></p>
                            <p class="font14 color">{{stuData.fTel}}</p>
                        </div>
                            <div  class="col-md-6">
                                <p class="font12 color6"><?php echo Yii::t("child", "Email");?></p>
                                <p class="font14 color">{{stuData.fEmail}}</p>
                            </div>
                        </div>
                        <div  class="col-md-12 ">
                            <div  class="col-md-3">
                                <p class="font12 color6"><?php echo Yii::t("labels", "Mother");?></p>
                            <p class="font14 color">{{stuData.mName}}</p>
                            </div>
                            <div  class="col-md-3">
                                <p class="font12 color6"><?php echo Yii::t("labels", "Mobile Phone");?></p>
                                <p class="font14 color">{{stuData.mTel}}</p>
                            </div>
                            <div  class="col-md-6">
                                <p class="font12 color6"><?php echo Yii::t("child", "Email");?></p>
                                <p class="font14 color">{{stuData.mEmail}}</p>
                            </div>
                        </div>
                         <hr>
                        <div class="clearfix"></div>
                    </div>
                    </div>
                </div>
            </div>            
        </div>
    </div>
</div>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    var schoolTitle = '<?php echo $schoolTitle ?>';
    var logo ='<?php echo $logo ?>';
    $(function() {
		$("#startDate").datepicker({
			dateFormat: "yy-mm-dd ",
		});
        $( "#startDate").on('change', function () {
            container.startDate = $('#startDate').val();
        });
        $("#endDate").datepicker({
			dateFormat: "yy-mm-dd ",
		});
        $( "#endDate").on('change', function () {
            container.endDate = $('#endDate').val();
        });
	});
    var container = new Vue({
        el: "#container",
        data: {
            tips_text_cn:'',
            tips_text_en:'',
            tips_plates_en:'',
            tips_plates_cn:'',
            startDate:'',
            startHours:'00',
            startMinutes:"00",
            endDate:'',
            endHours:'00',
            endMinutes:"00",
            class_ids:[],
            classAll:false,
            dataList:[],
            bgImg:'http://m2.files.ivykids.cn/cloud01-file-8025768FtLJ88p3PQvUtBNeUSKGQPXQBynO.jpg',
            ivyImg:'http://m2.files.ivykids.cn/cloud01-file-8025768FqoRUgyX5mkN_M9pWPUH9fEAKZvK.jpg',
            childDetail:{},
            logo:logo,
            schoolTitle:schoolTitle,
            auditStatus:0,
            comment:'',
            tabDetailData:{},
            printIds:[],
            printAll:false,
            initData:{},
            enable_plates:0,
            activeId:'',
            childInfo:{},
            unSubmitChild:[],
            stuData:{},
            childId:'',
            tableData:{},
            wechatAll:false,
            wechatList:[],
            send_wechat:true,
            sendData:{},
            wechatBtn:false
        },
        watch:{},
        created:function(){
            let that=this
            this.getData()
        },
        methods: {
            classTitle(id){
              for(var i=0;i<this.initData.classList.length;i++){
                if(id==this.initData.classList[i].class_id){
                    return this.initData.classList[i].title
                }
              }  
            },
            getData(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getData") ?>',
                    type: "get",
                    dataType: 'json',
                    success: function(data) {
                        if (data.state == 'success') {
                            that.initData=data.data
                            that.enable_plates=data.data.enable_plates
                            that.tips_text_en=data.data.tips_text_en
                            that.tips_text_cn=data.data.tips_text_cn
                            that.tips_plates_cn=data.data.tips_plates_cn
                            that.tips_plates_en=data.data.tips_plates_en
                            if(data.data.start!=''){
                                that.startDate=that.newDate(data.data.start,'date')
                                that.startHours=that.newDate(data.data.start,'hours')
                                that.startMinutes=that.newDate(data.data.start,'minutes')
                            }
                            if(data.data.end!=''){
                                that.endDate=that.newDate(data.data.end,'date')
                                that.endHours=that.newDate(data.data.end,'hours')
                                that.endMinutes=that.newDate(data.data.end,'minutes')
                            }
                           
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                    },
                })
            },
            newDate(dateTime,type){
                const date = new Date(dateTime.replace(/\-/g, '/'));
                const Y = date.getFullYear() + '-';
                const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
                const D = (date.getDate() < 10 ? '0'+date.getDate() : date.getDate()) + ' ';
                const h = (date.getHours() < 10 ? '0'+date.getHours() : date.getHours()) ;
                const m = (date.getMinutes() <10 ? '0'+date.getMinutes() : date.getMinutes());
                const s = date.getSeconds(); // 秒
                const dateString = Y + M + D + h+':' + m+':' + s;
                if(type=='hours'){
                    return h
                } if(type=='minutes'){
                    return m
                } if(type=='date'){
                    return Y + M + D 
                }
                if(!type){
                    return dateString;
                }
            },
            editInfo(){
                $('#infoModal').modal('show') 
            },
            saveInfo(){
                let that=this
                let start=$('#startDate').val().trim()+' '+this.startHours+':'+this.startMinutes
                let end=$('#endDate').val().trim()+' '+this.endHours+':'+this.endMinutes
                $.ajax({
                    url: '<?php echo $this->createUrl("edit") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        "start":start,
                        "end": end,
                        'enable_plates':that.enable_plates,
                        "tips_text_cn": that.tips_text_cn,
                        "tips_text_en": that.tips_text_en,
                        'tips_plates_en':that.tips_plates_en,
                        'tips_plates_cn':that.tips_plates_cn,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                          console.log(data.data)
                          resultTip({
                                msg: data.state
                            });
                           $('#infoModal').modal('hide') 
                           that.getData()

                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                    },
                })
            },
            progress(data){
                let num=data.submitNum <= 0? "0%" : Math.round((data.submitNum / data.total) * 10000) / 100.0 + "%";
                return 'width:'+num
            },
            tabList(id){
                let that=this
                this.printIds=[]
                this.activeId=id
                this.printAll=false
                $.ajax({
                    url: '<?php echo $this->createUrl("list") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        "class_id":id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.dataList=data.data.items
                            that.tableData=data.data
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                    },
                })
            },
            printAllData(){
                if(this.printAll){
                    this.printIds=[]
                    for(var i=0;i<this.dataList.length;i++){
                        this.printIds.push(this.dataList[i].id)
                    }
                }else{
                    this.printIds=[]
                }
            },
            printData(){
                if(this.printIds.length==this.dataList.length){
                    this.printAll=true
                }else{
                    this.printAll=false
                }
            },
            trim(str){
                if(str==null){
                    return
                }
				var result;
				var is_global='g'
					result = str.replace(/(^\s+)|(\s+$)/g,"");
					if(is_global.toLowerCase()=="g")
					{
					result = result.replace(/\s/g,"");
					}
					var tel=result.replace("+86",'')
				  return tel;
			},
            editCard(list){
                console.log(list)
                this.tabDetailData=list
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("detail") ?>',
                    type: "get",
                    dataType: 'json',
                    data:{
                        "id": list.id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                        console.log(data)
                            that.childDetail=data.data  
                            that.auditStatus=data.data.audit_status
                            $('#editModal').modal('show')                       
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                    },
                })
            },
            resetStatus(type){
                if(type=='show'){
                    $('#resetModal').modal('show')  
                    return
                }else{
                    let that=this
                    $.ajax({
                        url: '<?php echo $this->createUrl("reset") ?>',
                        type: "get",
                        dataType: 'json',
                        data:{
                            id:this.childDetail.id
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                console.log(data)
                                that.editCard(that.tabDetailData)    
                                that.tabList(that.activeId)  
                                $('#resetModal').modal('hide')  
                            } else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                        },
                        error: function(data) {
                        },
                    })
                }
                
            },
            saveAudit(){
                let that=this
                if(this.auditStatus==2 && this.comment.trim()==''){
                    resultTip({
                        error: 'warning',
                        msg: '请输入未通过原因'
                    });
                    return
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("audit") ?>',
                    type: "get",
                    dataType: 'json',
                    data:{
                        "audit_status": this.auditStatus,
                        "comment": this.comment,
                        id:this.childDetail.id,
                        send_wechat:that.send_wechat?1:0
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            console.log(data)
                            that.editCard(that.tabDetailData)       
                            that.tabList(that.activeId)       
                            for(var i=0;i<that.initData.classList.length;i++){
                                if(that.initData.classList[i].class_id==that.activeId){
                                    if(that.initData.classList[i].newSubmitNum!=0){
                                        that.initData.classList[i].newSubmitNum=that.initData.classList[i].newSubmitNum-1
                                    }
                                }
                            }    
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                    },
                })
            },
            markMade(type,id){
                let ids=[]
                if(id=='checkbox'){
                    ids=this.printIds
                }else{
                    ids=[id]
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("markMade") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        "ids": ids,
                        'is_made':type
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            console.log(data)
                            that.tabList(that.activeId)       
                            resultTip({
                                msg: data.state
                            });                
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                    },
                })
                
            },
            stuInfo(childId){
                this.childId=childId
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("student/childInfo") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        childid:childId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                        console.log(data)
                        that.stuData=data.data
                        $('#childInfo').modal()
                        } else {
                            resultTip({  
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            wechatModel(){
                let that=this
                if(this.tableData.unSubmitChild.length==0){
                    resultTip({
                        error: 'warning',
                        msg: '暂无可发送人员'
                    });
                    return
                }
                this.wechatList=[]
                $.ajax({
                    url: '<?php echo $this->createUrl("sendInfo") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        "child_ids":this.tableData.unSubmitChild
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            console.log(data)
                            that.sendData=data.data
                            $('#wechatModal').modal('show')               
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                    },
                })
            },
            wechatAllData(){
                if(this.wechatAll){
                    let dataList=JSON.parse(JSON.stringify(this.tableData.unSubmitChild))
                    for(var i=0;i<dataList.length;i++){
                        if(this.sendData[dataList[i]] && this.sendData[dataList[i]].can_send==0){
                            dataList.splice(i,1)
                        }
                    }
                    this.wechatList=dataList
                }else{
                    this.wechatList=[]
                }
            },
            wechatData(){
                if(this.wechatList.length==this.tableData.unSubmitChild.length){
                    this.wechatAll=true
                }else{
                    this.wechatAll=false
                }
            },
            sendWechat(){
                let that=this
                that.wechatBtn=true
                $.ajax({
                    url: '<?php echo $this->createUrl("send") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        "child_ids":this.wechatList
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            console.log(data)
                            that.wechatBtn=false
                            resultTip({
                                msg:data.state
                            });   
                            that.wechatModel()            
                        } else {
                            that.wechatBtn=false
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                    },
                })
            }
        }
    })
</script>
<style>
    [v-cloak] {
		display: none;
	}
    .imgCover{
        width:100px;
        height:100px;
        object-fit:cover;
        border-radius:50%;
        border:1px solid #428bca
    }
    .color4D88D2{
        color:#4D88D2
    }
    .border {
		border: 1px solid #ccc;
		border-radius: 5px
	}
	.ptintImg {
		height: 140px;
        border-radius: 4px;
	}
	.datalist{
		float: left;
		width: 50%;
		margin-top:10px;
	}
	.datalist div{
		font-size:12px
	}
	.list{
		width:140px;
		margin:0 auto;
	}
	.borderNone {
		border: 1px solid #ccc;
        height: 440px
	}
	.borderRight {
		border-right: 1px solid #ccc;
	}
	.padd0 {
		padding: 0 !important
	}
	.h100 {
		height: 100%
	}
	.chilidPto {
		max-height: 40%;
        border-radius: 4px;
	}
	.pt10 {
		padding: 10px 0 20px 10px;
	}
    .pl10{
        padding-left:10px;
    }
	.col-md-6 {
		float: left;
		width: 50%;
	}
	.relative{
		position: relative;
	}
	.absolute{
		position: absolute;
        bottom: 15px;
        left: 15px;
        font-size: 16px;
        color: #fff;
	}
	.pt30{
		padding-top:30px
	}

	.fontSize{
		font-size:18px
	}
	.keepAll{
		word-wrap:break-word;
		word-break:keep-all;
	}
	.flexW{
		width:39px
	}
	.imgCss{
		width:150px
	}
    .green{
        color:#5DB85B
    }
    .red{
        color:#D5514E
    }
    .yellow{
        color:#F1AD4E
    }
    .selectWidth{
        width:70px
    }
    .classActive{
        border-left:6px solid #428BCA;
        color:#4D88D2;
        padding-left:10px
    }
    .floatNone{
        /* float:none !important; */
        background:#D5514E;
        margin-left:5px
    }
</style>