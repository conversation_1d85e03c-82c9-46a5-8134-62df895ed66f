<?php
$this->breadcrumbs=array(
	'Points Stocks'=>array('index'),
	'Manage',
);

$this->menu=array(
	array('label'=>Yii::t('operations', '新增库存'), 'url'=>array('create')),
);
?>
<h1><?php echo Yii::t('operations', '库存管理');?></h1>

<?php $this->widget('zii.widgets.grid.CGridView', array(
	'id'=>'points-stock-grid',
	'dataProvider'=>$model->search(),
	'filter'=>$model,
	'columns'=>array(
		'id',
		array(
            'name'=>'product_id',
            'value'=>'$data->Product->getContent()',
        ),
		'num',
		'memo',
 		array(
            'name'=>'update_timestamp',
            'value'=>'OA::formatDateTime($data->update_timestamp)',
        ),
		array(
			'class'=>'CButtonColumn',
		 	'template' => '{delete}'
		),
	),
)); 
?>
