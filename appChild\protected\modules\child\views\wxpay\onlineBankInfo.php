<?php 
$payNotice1 = Yii::t('payment', 'Please select at least one invoice.');
$payNotice2 = Yii::t("payment", 'Please select up to 6 invoices each time to proceed the payment.');
?>
<div class="form">
	<h3><?php echo Yii::t('payment','Total'); ?>：￥<span id="paymoney2"></span></h3>
	<p><label for="submoney2"><?php echo Yii::t("payment", 'Payment Amount: '); ?></label><input id="submoney2" type="text" name="submoney2" size="10" autocomplete="off" style="text-align: right;"></p>
</div>

<?php
echo '<div align="center"><span class="btn001">';
if( Yii::app()->user->checkAccess('adminChild', array("childid" => $this->childid)) ){
	echo $currentPaymentMethodHtml = CHtml::link('<span>'.Yii::t("payment", 'Wechat Pay').'</span>', 'javascript:;', array('onclick' => "confirmInvoiceInfo('wxpay','" . $payNotice1 . "','" . $payNotice2 . "','" . $payNotice3 ."');return false",'class'=>'bigger'));
}
else{
	echo '请登录家长帐号付款！';
}
echo '</span></div>';
 ?>

<script>
	var notice4 = '<?php echo Yii::t("payment", 'For partial payments, you must select only 1 invoice that you want to make payment for!');?>';
	var notice5 = '<?php echo Yii::t("payment", 'Please enter a valid payment amount!');?>';
	var notice6 = '<?php echo Yii::t("payment", 'Your payment amount cannot exceed the total amount of the invoice(s)!');?>';
</script>
