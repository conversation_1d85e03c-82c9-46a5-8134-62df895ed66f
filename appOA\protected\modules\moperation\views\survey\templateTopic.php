<style>
    [v-cloak] {
        display: none;
    }
    .display-flex{
        display: -webkit-flex;
        display: flex;
    }
    .num input{
        text-align: center;
        width:35px;
        height: 35px;
    }
    .ml50{
        margin-left: 50px
    }
    .pt5{
        padding-top:0 !important;
        margin-top:-2px;
    }
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','HQ Operations'), array('/moperation'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','问卷管理'), array('index'))?></li>
        <li class="active"><?php echo Yii::t('site','模版管理') ?></li>
    </ol>

    <div class="row">
        <div class="col-md-2 col-sm-2">
            <?php

            $this->widget('zii.widgets.CMenu',array(
                'items'=> $this->getSubMenu(),
                'id'=>'pageCategory',
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-left background-gray'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>
        <div class="col-md-10 col-sm-10" id='template' v-cloak>
            <p class=" pull-right">
                <button type="submit" class="btn btn-primary" @click='add(0)'>添加</button>
                <div class="clearfix"></div>
            </p>
            <div class="panel panel-default">
                <div class="list-group"  v-if='data.length!=""'>
                    <form  id="sort">
                        <div href="#" class="list-group-item list-hover" v-for='(list,index) in data'>
                            <div class="list-group-item-heading display-flex">
                                <div>
                                    <h4 class="num"><input type="text" :value="index+1" :name="'topic_order['+list.id+']'"></h4>
                                </div>
                                <div class="ml15">
                                    <h4>{{list.title_cn}}</h4>
                                    <h4>{{list.title_en}}</h4>
                                </div>
                            </div>
                            <div class="list-group-item-text ml50">
                                <p>
                                    <span v-if='list.option_cat==1'>单选</span>
                                    <span v-if='list.option_cat==2'>多选</span>
                                    <a href="javascript:;" @click='option()'>{{list.option_group_title}}</a>
                                    
                                    <span v-if='list.binary_flag=="0" || list.binary_flag=="1"'>可选</span>
                                    <span v-if='list.binary_flag=="2" || list.binary_flag=="3" '>必选</span>
                                </p>
                                <p v-if='list.binary_flag==3 || list.binary_flag==1'>参与者可附加注释</p>
                                <p class="text-right">
                                    <button type="button" class="btn btn-default btn-sm" @click='Delete(list.id,index)'>删除</button>
                                    <button type="button" class="btn btn-default btn-sm" @click='add(list.id)'>编辑</button>
                                </p>
                            </div>
                        </div>
                    </form>
                </div>
                <div v-else class="panel-body">
                    暂无数据
                </div>
            </div>
            <p class="pull-right" v-if='data.length!=""'>
                <button class="btn btn-primary" >预览</button>
                <!-- <button type="submit" class="btn btn-primary" data-toggle="modal" data-target="#addTem">按编号排序</button> -->  
                <button type="submit" class="btn btn-primary" @click='sort()'>更新显示顺序</button>
            </p>
            <!--修改题目-->
            <div class="modal fade bs-example-modal-lg" id="addEdit" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" data-backdrop="static">
                <div class="modal-dialog ">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                            &times;
                        </button>
                            <h4 class="modal-title" id="myModalLabel">更改问卷题目</h4>
                        </div>
                        <?php $form = $this->beginWidget('CActiveForm', array(
                            'id' => 'expense-forms',
                            'enableAjaxValidation' => false,
                            'action' => ($model->id) ? $this->createUrl('expenesAdd', array('expenesId' => $model->id)) : $this->createUrl('updateTemplateTopic'),
                            'htmlOptions' => array('class' => 'J_ajaxForm', 'role' => 'form', 'enctype' => "multipart/form-data"),
                        )); ?>
                        <div class="modal-body">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <label for="inputEmail3" class="col-sm-2 control-label">中文描述</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" :value='editTopic.title_cn' name="WSurveyTopic[title_cn]">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputEmail3" class="col-sm-2 control-label">英文描述</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control" :value='editTopic.title_en' name="WSurveyTopic[title_en]">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputEmail3" class="col-sm-2 control-label">答案选项</label>
                                    <div class="col-sm-10">
                                        <div class="checkbox">
                                            <label>
                                               <input type="checkbox" v-if='editTopic.allow_feedback==0' name='allow_feedback' value="1">
                                               <input type="checkbox" v-if='editTopic.allow_feedback==1' checked name='allow_feedback' value="1">
                                               <input type="checkbox" v-if='editTopic==""' checked name='allow_feedback' value="1">参与者可附加注释
                                            </label>
                                        </div>
                                        <p class="pt10">
                                            <a href="javascript:;" @click='selectOpt()'>请选择一个选项 </a><span> 无选项请忽略此项</span>
                                        </p>
                                        <div class="alert alert-warning" role="alert" v-if='editTopic!=""'>
                                            <p>已设置：单选（<a href="">{{editTopic.option_group}}</a>）</p>
                                            <p v-if='editTopic.binary_flag==1' >
                                                <label class="radio-inline pt5">
                                                    <input type="radio" name="binary_flag" id="inlineRadio1" value="1" checked>必选
                                                </label>
                                                <label class="radio-inline pt5">
                                                    <input type="radio" name="binary_flag" id="inlineRadio1" value="2">可选
                                                </label>
                                            </p>
                                             <p v-if='editTopic.binary_flag==2' >
                                                <label class="radio-inline pt5">
                                                    <input type="radio" name="binary_flag" id="inlineRadio1" value="1" >必选
                                                </label>
                                                <label class="radio-inline pt5">
                                                    <input type="radio" name="binary_flag" id="inlineRadio1" value="2" checked>可选
                                                </label>
                                            </p>
                                            <p>
                                                <a href="">清除选项设置</a>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                            <button type="button" class="btn btn-primary J_ajax_submit_btn">更新</button>
                        </div>
                        <?php $this->endWidget(); ?>
                    </div>
                </div>
            </div>
            <!--题目选项-->
            <div class="modal fade bs-example-modal-lg" id="option" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" data-backdrop="static">
                <div class="modal-dialog ">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                            &times;
                        </button>
                            <h4 class="modal-title" id="myModalLabel">题目选项</h4>
                        </div>
                        <div class="modal-body">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <label for="inputEmail3" class="col-sm-2 control-label">选项组名称</label>
                                    <div class="col-sm-10">
                                        <input type="text" class="form-control">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputEmail3" class="col-sm-2 control-label">选项</label>
                                    <div class="col-sm-10">
                                        <div class="checkbox">
                                            <label>
                                              <input type="checkbox"> Remember me
                                            </label>
                                        </div> 
                                        <div class="checkbox">
                                            <label>
                                              <input type="checkbox"> Remember me
                                            </label>
                                        </div> 
                                        <div class="checkbox">
                                            <label>
                                              <input type="checkbox"> Remember me
                                            </label>
                                        </div>    
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
            <!--选项-->
            <div class="modal fade bs-example-modal-lg" id="optionGroup" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" data-backdrop="static">
                <div class="modal-dialog ">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                                &times;
                            </button>
                            <h4 class="modal-title" id="myModalLabel">题目选项</h4>
                        </div>
                        <div class="modal-body">
                            <ul id="myTab" class="nav nav-tabs">
                                <li class="active">
                                    <a href="#radio" data-toggle="tab">单选</a>
                                </li>
                                <li><a href="#check" data-toggle="tab">多选</a></li>
                            </ul>
                            <div id="myTabContent" class="tab-content">
                                <div class="tab-pane fade in active" id="radio">
                                   <div class="radio">
                                        <label>
                                            <input type="radio"> Check me out
                                        </label>
                                    </div>
                                </div>
                                <div class="tab-pane fade" id="check">
                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox"> Check me out
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-primary">确认</button>
                            <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
</div>
<script>
//不带备注 必选 2  不带备注 可选 0 
//带备注   必选 3  带备注 可选1

//updateTemplateTopic  新建/更新模板题目
//$tempid  模板id  $topicid 题目ID    $allow_feedback 备注  binary_flag选项 

    var topic = <?php echo json_encode($topic) ?>;
    var tempid = '<?php echo Yii::app()->request->getParam('tempid', ''); ?>';
    console.log(tempid)
    var template = new Vue({
            el: "#template",
            data: {
                data: topic,
                editTopic:'',
                tempid:tempid
            },
            created: function() {},
            methods: {
                plus(index){
                    return Number(index)+1
                },
                prev(index){
                    return  Number(index)-1
                },
                add(id){
                    if(id==0){
                        $('#addEdit').modal('show')
                        return
                    }
                    $.ajax({
                        url: "<?php echo $this->createUrl('updateTemplateTopic')?>",
                        type: 'post',
                        dataType: 'json',
                        data:{
                            topicid:id,
                            tempid:tempid
                        },
                        success:function(data){
                           console.log(data)
                            if(data.state=='success'){
                                template.editTopic=data.data
                                $('#addEdit').modal('show')
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg:data.message
                                });
                            }
                        },
                        error:function(data) {
                            console.log(data)
                            alert('请求错误')
                        }
                    })
                    
                },
                option(){
                    $('#option').modal('show')
                },
                sort(){
                    $.ajax({
                        url: "<?php echo $this->createUrl('sortTemplateTopic')?>",
                        type: 'post',
                        dataType: 'json',
                        data:$('#sort').serialize(),
                        success:function(data){
                           console.log(data)
                            if(data.state=='success'){
                                resultTip({"msg": data.message})
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg:data.message
                                });
                            }
                        },
                        error:function(data) {
                            console.log(data)
                            alert('请求错误')
                        }
                    })
                },
                Delete(id,index){
                    $.ajax({
                        url: "<?php echo $this->createUrl('DeleteTemplateTopic')?>",
                        type: 'post',
                        dataType: 'json',
                        data:{
                            topic_id:id
                        },
                        success:function(data){
                           console.log(data)
                            if(data.state=='success'){
                                resultTip({"msg": data.message})
                                 template.data.splice(index,1);
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg:data.message
                                });
                            }
                        },
                        error:function(data) {
                            console.log(data)
                            alert('请求错误')
                        }
                    })
                },
                selectOpt(){
                    $('#optionGroup').modal('show')
                }
            }
        })
</script>