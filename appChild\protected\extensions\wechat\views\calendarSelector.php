<style>
    .bg{
        background:#fbf9fe;
        padding:16px 15px 8px 15px !important;
    }
    .weekFlex1{
        flex:1;
        font-size: 14px;
        font-weight: 600;
        color: #323233;
    }
    .weekView{
        font-size: 14px;
        font-weight: 500;
        color: #323233;
    }
    .ft:after{
        transform: rotate(135deg) !important;
    }
</style>
<div class="flex bg">
    <span class=' weekFlex1'><?php echo Yii::t('portfolio', 'View by Weeks'); ?></span>
    <span class='weekView' id="showCalendar"><?php echo $calendarArr[$currentYid] . ' - ' . ($calendarArr[$currentYid]+1) . ' ' . Yii::t('portfolio', 'School Year'); ?> <i class="weui_cell_ft ft"></i></span>
</div>
<!--BEGIN 学年选择框-->
<div id="calendar">
    <div class="weui_mask_transition" id="calendar_mask"></div>
    <div class="weui_actionsheet" id="calendar_sheet">
        <div class="weui_actionsheet_menu">
            <?php foreach ($calendarArr as $yid=>$calendar): ?>
                <div class="weui_actionsheet_cell" onclick="location.href=$(this).data('alt')" data-alt="<?php echo $this->controller->createUrl($jumpUrl, array('yid'=>$yid)); ?>">
                    <?php echo $calendar . ' - ' . ($calendar+1) . ' ' . Yii::t('portfolio', 'School Year'); ?>
                </div>
            <?php endforeach; ?>
        </div>
        <div class="weui_actionsheet_action">
            <div class="weui_actionsheet_cell" id="calendar_cancel"><?php echo Yii::t('global', 'Cancel'); ?></div>
        </div>
    </div>
</div>
<!--END 学年选择框-->

<script>
    //选择学年
    $('#showCalendar').click(function () {
        var mask = $('#calendar_mask');
        var weuiActionsheet = $('#calendar_sheet');
        weuiActionsheet.addClass('weui_actionsheet_toggle');
        mask.show().addClass('weui_fade_toggle').one('click', function () {
            hideActionSheet(weuiActionsheet, mask);
        });
        $('#calendar_cancel').one('click', function () {
            hideActionSheet(weuiActionsheet, mask);
        });
        weuiActionsheet.unbind('transitionend').unbind('webkitTransitionEnd');

        function hideActionSheet(weuiActionsheet, mask) {
            weuiActionsheet.removeClass('weui_actionsheet_toggle');
            mask.removeClass('weui_fade_toggle');
            weuiActionsheet.on('transitionend', function () {
                mask.hide();
            }).on('webkitTransitionEnd', function () {
                mask.hide();
            })
        }
    });
</script>