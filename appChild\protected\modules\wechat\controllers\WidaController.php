<?php

class WidaController extends WeChatVisualController
{
    public $signPackage;
    public $childid;
    public $classid;
    public $schoolid;
    public $childObj;
    public $userid;
    public $langPrefix;
    public $nextYear;
    public $accessToken = '';
    public function beforeAction($action)
    {
        $this->userid = $this->wechatUser->userid;
        $this->childid = isset($_COOKIE['childid']) ? $_COOKIE['childid'] : '' ;
        if ($paramChildid = Yii::app()->request->getParam('childid')) {
            $this->childid = $paramChildid;
        }
        if ($this->childid && isset($this->myChildObjs[$this->childid])) {
            $this->childObj = $this->myChildObjs[$this->childid];
        } else {
            $this->childObj = end($this->myChildObjs);
            $this->childid = $this->childObj->childid;
        }
        $this->schoolid = $this->childObj->schoolid;
        $this->classid = $this->childObj->classid;
        // 如果下学年分班，则采用下学年的学校ID
        Yii::import('common.models.invoice.ChildReserve');
        $this->nextYear = ChildReserve::model()->findByAttributes(array('childid'=>$this->childid, 'stat'=>20));
        if ($this->nextYear) {
            $this->schoolid = $this->nextYear->schoolid;
        }
        // 语言前缀
        $this->langPrefix = 'cn';
        if (Yii::app()->language == 'en_us') {
            $this->langPrefix = 'en';
        }
        Yii::import('common.models.ptc.*');
        return true;
    }


    public function actionIndex()
    {
        $this->layout = '//layouts/mainv2.0.0';
        $this->subPageTitle = Yii::t("navigations", "Scheduling Platform");
        // 判断是否为微信消息进入
        $randtag = Yii::app()->request->getParam('randtag', '');
        if($randtag){
            $criteria = new CDbCriteria();
            $criteria->compare('t.randtag', $randtag);
            $wxModel = Wxnotif::model()->find($criteria);
            if($wxModel && $wxModel->user_responed != 1){
                $wxModel->user_responed = 1;
                $wxModel->save();
            }
        }

        // 查找孩子标记记录
        $criteria = new CDbCriteria();
        $criteria->compare('t.childid', $this->activeChildIds);
        $criteria->compare('plan.semester', 100);
        $criteria->compare('plan.status', ParentMeetingPlan::STATUS_ONLINE);
        $criteria->with = 'plan';
        $models = PmeetPlanChild::model()->findAll($criteria);

        $data = array();
        $planids = array();
        $childids = array();
        foreach ($models as $model) {
            $plan = $model->plan;
            $planids[] = $plan->id;
            $childids[] = $model->childid;
            $info = json_decode($plan->extra, true);
            $data[$model->childid][$plan->id]['childid'] = $model->childid;
            $data[$model->childid][$plan->id]['planid'] = $plan->id;
            $data[$model->childid][$plan->id]['title_cn'] = $info['title_cn'];
            $data[$model->childid][$plan->id]['title_en'] = $info['title_en'];
        }

        // 查找孩子已预约记录
        $criteria = new CDbCriteria();
        $criteria->compare('planid', $planids);
        $criteria->compare('childid', $childids);
        $planItems = ParentMeetingItem::model()->findAll($criteria);
        $itemData = array();
        foreach ($planItems as $planItem) {
            $itemData[$planItem->childid][$planItem->planid]['date'] = $planItem->target_date;
            $itemData[$planItem->childid][$planItem->planid]['timeslot'] = $planItem->timeslot;
        }

        $this->render('index', array(
            'data' => $data,
            'itemData' => $itemData,
        ));
    }

	// 首页
	public function actionDetail()
	{
        $this->subPageTitle = Yii::t("navigations", "Scheduling Platform");
        $planid = Yii::app()->request->getParam('planid');
        $childid = Yii::app()->request->getParam('childid');

        if (!$planid || !$childid) {
            return false;
        }
        $criteria = new CDbCriteria();
        $criteria->compare('t.childid', $childid);
        $criteria->compare('plan.id', $planid);
        $criteria->compare('plan.status', ParentMeetingPlan::STATUS_ONLINE);
        $criteria->with = 'plan';
        $model = PmeetPlanChild::model()->find($criteria);
        if (!$model) {
            return false;
        }
        $plan = $model->plan;
        $planStatus = $plan->status;

        $memo = '';
        $dateStr = date('Ymd');
        $dataArr = array();  // 时间表格
        $data = array();
        if($plan && $plan->items){
            $planid = 0;
            // 拼接已经开放的预约时间数组 $data[时间]
            foreach ($plan->items as $val){
                $show_time = explode(',', $val->timeslot);
                $default = 0;
                $updateStatus = 0;
                if($val->childid == $childid){
                    $op_timestamp = time();
                    $twoday = $op_timestamp + 172800;
                    $target_date = strtotime($val->target_date);
                    if ($twoday > $target_date) {
                        $updateStatus = 1;
                    }
                    $default = 1;
                }

                $data[$val->target_date][] = array(
                    'id' => $val->planid . '_' . $val->target_date  . '_' . $val->timeslot . '_' . $val->meet_index,
                    'time' => $show_time[0] . '-' . $show_time[1],
                    'name' => $val->childid,
                    'default' => $default,
                    'status' => ($dateStr < $val->target_date) ? 0 : 1,
                    'updateStatus' => $updateStatus,  //  1 不可修改 48小时判断
                );
                $planid = $val->planid;
            }
            // 获取备注信息 $memo
            $memo = '';
            // if($planid > 0){
            //     $criteria = new CDbCriteria();
            //     $criteria->compare('t.childid', $this->childid);
            //     $criteria->compare('t.planid', $planid);
            //     $memoModel = ParentMeetingItemMemo::model()->find($criteria);
            //     $memo = (isset($memoModel) && $memoModel->memo) ? $memoModel->memo : '';
            // }

            // 根据开发的开始和结束时间获取日历表格时间
            $array = array_keys($data);
            $month = $this->getDateFromRange(reset($array), end($array));
            $array = array();
            $k = 0;
            $oldM = 0;

            // 根据开始时间和结束时间 弄成以月分隔的数组$array[自增下标][时间] = 周几 0-6
            foreach ($month as $val){
                $w = date("w", strtotime($val));
                $m = date("m", strtotime($val));
                if($m != $oldM){
                    $k += 1;
                    $oldM = $m;
                }
                $array[$k][$val] = $w;
            }


            $calendar = array();
            $key = 0;
            // 把$array里面每月的数组在以每周7天来分隔数组
            foreach ($array as $val){
                $num = 0;
                foreach ($val as $d=>$month){
                    $calendar[$key][$num][] = $d;
                    if($month == 6){
                        $num += 1;
                    }
                }
                $key++;
            }

            //  以周的数组判断是否7个  前后补齐
            foreach ($calendar as $key=>$val){
                foreach ($val as $k=>$itemas){
                    if(count($itemas) < 7){
                        $star = strtotime(reset($itemas));
                        $end = strtotime(end($itemas));
                        $starmonthDate = date('w', $star);
                        $starmonthDay = date('d', $star);
                        $endmonthDate = date('w', $end);
                        $endmonthDay = date('m', $end);
                        // 补齐前面
                        if($starmonthDate > 0){
                            $before = $star - 86400;
                            for ($i = 0; $i < $starmonthDate ; $i++) {
                                if($starmonthDay < 2){
                                    array_unshift($itemas, 0);
                                }else{
                                    $beforeTime = date("Ymd" , $before);
                                    array_unshift($itemas, $beforeTime);
                                    $before -= 86400;
                                }
                            }
                        }

                        // 补齐后面
                        if($endmonthDate < 6){
                            $after = $end + 86400;
                            for ($i = $endmonthDate; $i < 6 ; $i++) {
                                $NewEndmonthDate = date('m', $after);
                                if($endmonthDay != $NewEndmonthDate){
                                    array_push($itemas, 0);
                                }else{
                                    $afterTime = date("Ymd" , $after);
                                    array_push($itemas, $afterTime);
                                    $after += 86400;
                                }
                            }
                        }
                    }
                    $dataArr[$key][$k] = $itemas;
                }
            }
        }
        $memoStatus = 0;
        if(in_array($this->schoolid, array('BJ_DS','BJ_SLT'))){
            $memoStatus = 1;
        }

        $this->render('detail', array(
            'data' => $data,   // 开放时间
            'memo' => $memo,   // 备注
            'timeTable' => $dataArr, // 开放的时间 以最小最大为标准
            'momeStatus' => $memoStatus, // 1 启明星 0 艾毅
            'planStatus' => $planStatus, // 1 开放 0 不开放
            'childid' => $childid,
        ));
	}

	/*
	 * $startdate 开始时间
	 * $enddate 结束时间
	 *
	 * 计算中间的全部日期
	 */

    function getDateFromRange($startdate, $enddate){
        $stimestamp = strtotime($startdate);
        $etimestamp = strtotime($enddate);
        // 计算日期段内有多少天
        $days = ($etimestamp-$stimestamp)/86400+1;
        // 保存每天日期
        $date = array();
        for($i=0; $i<$days; $i++){
            $date[] = date('Ymd', $stimestamp+(86400*$i));
        }
        return $date;
    }

    /*
     *  增加
     *  unikey id_日期_时间 组合数组
     *  memo 备注
     */

	public function actionUpdate()
    {
        $unikey = Yii::app()->request->getParam('unikey', '');
        $memo = Yii::app()->request->getParam('memo', '');
        if($unikey) {
            $childid = $this->childid;
            $arr = explode("_", $unikey);
            $planid = $arr[0];
            $days = $arr[1];
            $times = $arr[2];
            $meet_index = $arr[3];
            Yii::import('common.models.ptc.*');
            $planModel = ParentMeetingPlan::model()->findByPk($planid);

            // 查找孩子是否在选择中
            $criteria = new CDbCriteria();
            $criteria->compare('childid', $childid);
            $criteria->compare('planid', $planid);
            $planChild = PmeetPlanChild::model()->find($criteria);
            if (!$planChild) {
                echo CJSON::encode(array(
                    'state' => 'fail',
                    'message' => Yii::t("support", "Student is not in the scheduling list.")
                ));
            }
            if(isset($planModel)) {
                if ($planModel->status == ParentMeetingPlan::STATUS_OFFLINE) {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message',Yii::t("support", "CCurrently there is no open scheduling.") );
                    $this->showMessage();
                }
                $op_timestamp = time();
                $twoday = $op_timestamp + 172800;
                $mon = date('Y-m', strtotime($days));
                $old_unikey = '';
                $editable = 1;

                $criteria = new CDbCriteria();
                $criteria->compare('childid', $childid);
                $criteria->compare('planid', $planid);
                $items = ParentMeetingItem::model()->find($criteria);
                $criter = new CDbCriteria();
                $criter->compare('planid', $planid);
                $criter->compare('target_date', $days);
                $criter->compare('timeslot', $times);
                $criter->compare('meet_index', $meet_index);
                $item = ParentMeetingItem::model()->find($criter);

                $criteria = new CDbCriteria();
                $criteria->compare('planid', $planid);
                $criteria->compare('childid', $childid);
                $itemmemo = ParentMeetingItemMemo::model()->find($criteria);
                if ($itemmemo == null) {
                    $itemmemo = new ParentMeetingItemMemo;
                }
                if ($twoday > strtotime($days)) {
                    $editable = 0;
                }
                $itemmemo->planid = $planid;
                $itemmemo->childid = $childid;
                $itemmemo->memo = $memo;
                if ($op_timestamp < strtotime($days)) {
                    $childModel = ChildProfileBasic::model()->findByPk($childid);

                    $branch = Branch::model()->findByPk($planModel->school_id);

                    $parent = array();
                    if ($childModel->fid) {
                        $parent['fid'] = $childModel->fid;
                    }
                    if ($childModel->mid) {
                        $parent['mid'] = $childModel->mid;
                    }

                    if ($parent) {
                        $criteria = new CDbCriteria();
                        $criteria->compare('t.userid', $parent);
                        $criteria->compare('t.valid', 1);
                        if ($this->state == 'ds') {
                            $criteria->compare('account', 'ds');
                        } else {
                            $criteria->compare('account', 'ivy');
                        }
                        $wechatModel = WechatUser::model()->findAll($criteria);
                    }

                    $this->accessToken = CommonUtils::getAccessToken($this->state);
                    $TitleInfo = json_decode($planModel->extra,true);
                    if ($items) {
                        $mon_old = date('Y-m', strtotime($items->target_date));
                        $target_date = strtotime($items->target_date);
                        $old_unikey = $items->planid . '_' . $items->target_date . '_' . $items->timeslot;
                        if ($twoday > $target_date) {
                            echo CJSON::encode(array(
                                'state' => 'fail',
                                'message' => Yii::t("support", "Scheduled appointment cannot be modified online within 2 days of the reserved time slot; please contact the lead teacher directly to reschedule in this situation.")
                            ));
                        } else {
                            if ($item->childid == 0 || $item->childid == $this->childid) {
                                $items->childid = 0;
                                $items->stem = 0;
                                $items->openid = '';
                                $item->childid = $childid;
                                $item->stem = 2;
                                $item->openid = $this->openid;
                                $item->op_userid = $this->userid;
                                $items->op_userid = 0;
                                $items->op_timestamp = 0;
                                $item->op_timestamp = $op_timestamp;
                                if ($items->save()) {
                                    if ($itemmemo->save()) {
                                        if ($item->save()) {
                                            $timeData = explode(',', $item->timeslot);
                                            //Yii::log($this->openid .'=>'. $this->childid . '=>'. $this->userid . '=>'. date("Y-m-d", time()), CLogger::LEVEL_INFO, 'reserve.update');
                                            echo CJSON::encode(array(
                                                'state' => 'success',
                                                'message' => Yii::t("global", "Success!"),
                                                'data' => array('mon' => $mon, 'mon_old' => $mon_old, 'uniKey' => $old_unikey, 'editable' => $editable)
                                            ));

                                            if ($wechatModel) {
                                                $data = array(
                                                    'childName' => $childModel->getChildName(true, true, true),
                                                    'time' => date("Y-m-d", strtotime($item->target_date)) . ' ' . $timeData[0] . '-' . $timeData[1],
                                                    'school' => $branch->title,
                                                    'class' => $childModel->ivyclass->title,//'待定 To Be Determined',
                                                    'memo' => (Yii::app()->language == 'zh_cn') ? $TitleInfo['title_cn'] : $TitleInfo['title_en'],//'待定 To Be Determined',
                                                    //'class' => '待定 To Be Determined',
                                                );

                                                if ($this->accessToken) {
                                                    foreach ($wechatModel as $wechatItem) {
                                                        $this->sendMessageData($this->childid, $wechatItem->openid, $data);
                                                    }
                                                }
                                            }
                                        } else {
                                            echo CJSON::encode(array(
                                                'state' => 'fail',
                                                'message' => Yii::t("global", "Failed!")
                                            ));
                                        }
                                    }
                                }
                            } else {
                                echo CJSON::encode(array(
                                    'state' => 'fail',
                                    'message' => Yii::t("support", "Sorry this time slot has just been booked and is no longer available. Please select another time slot.")
                                ));
                            }
                        }
                    } else {
                        if ($item->childid == 0) {
                            $item->childid = $childid;
                            $itemmemo->memo = $memo;
                            $item->op_userid = $this->userid;
                            $item->op_timestamp = $op_timestamp;
                            $item->openid = $this->openid;
                            $item->stem = 2;
                            if ($item->save()) {
                                if ($itemmemo->save()) {
                                    echo CJSON::encode(array(
                                        'state' => 'success',
                                        'message' => Yii::t("global", "Success!"),
                                        'data' => array('mon' => $mon, 'uniKey' => $old_unikey, 'editable' => $editable)
                                    ));
                                    if ($wechatModel) {
                                        $timeData = explode(',', $item->timeslot);
                                        $data = array(
                                            'childName' => $childModel->getChildName(true, true, true),
                                            'time' => date("Y-m-d", strtotime($item->target_date)) . ' ' . $timeData[0] . '-' . $timeData[1],
                                            'school' => $branch->title,
                                            'class' => $childModel->ivyclass->title,//'待定 To Be Determined',
                                            'memo' => (Yii::app()->language == 'zh_cn') ? $TitleInfo['title_cn'] : $TitleInfo['title_en'],//'待定 To Be Determined',
                                        );
                                        if ($this->accessToken) {
                                            foreach ($wechatModel as $wechatItem) {
                                                $this->sendMessageData($this->childid, $wechatItem->openid, $data);
                                            }
                                        }
                                    }
                                }
                            } else {
                                echo CJSON::encode(array(
                                    'state' => 'fail',
                                    'message' => Yii::t("global", "Failed!")
                                ));
                            }
                        } else {
                            echo CJSON::encode(array(
                                'state' => 'fail',
                                'message' => Yii::t("support", "Sorry this time slot has just been booked and is no longer available. Please select another time slot.")
                            ));
                        }
                    }
                } else {
                    echo CJSON::encode(array(
                        'state' => 'fail',
                        'message' => Yii::t("support", "Time slot invalid")
                    ));
                }
            }else {
                echo CJSON::encode(array(
                    'state' => 'fail',
                    'message' => Yii::t("support", "非法操作")
                ));
            }
        } else {
            echo CJSON::encode(array(
                'state' => 'fail',
                'message' => Yii::t("support", "参数不对")
            ));
        }
    }

    // 发送模板消息
    public function sendMessageData($childid, $openid, $reserveData)
    {
        $url = 'https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=' . $this->accessToken;
        // 微信模板消息内容
        if($this->state == 'ds') {
            $template_id = 'l_A1EyhJpDFaHiTn8NpTzGaguyqTTdtiBqAZmfOdc4I';
            $ceshiOpenid = 'oHBKPwQUsreFHcZkzsq0CdW7VqEQ';
            //$first = '您预约的WIDA测试信息如下。Your WIDA Test appointment is stated below:';
            //$remark = '请在预约的时间到校进行测试。Please join the test on time, thanks.';
            $first = '您的预约信息如下。Your appointment is stated below:';
            $remark = '期待与您见面！We look forward to seeing you!';
            $keyword3 = 'Daystar (Beigao)';

            $state = $this->state;
            $appid = $this->appId;
            $redirectUrl = "http://www.ivyonline.cn/wechat/wida/index";
            $touser = (Mims::isProduction()) ? $openid : $ceshiOpenid;

            $data = array(
                'touser' => $touser,
                'template_id' => $template_id,
                'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' . $appid . '&redirect_uri=' . $redirectUrl . '&response_type=code&scope=snsapi_base&state=' . $state . '#wechat_redirect',
                'data' => array(
                    'first' => array('value' => $first),
                    'keyword1' => array('value' => $reserveData['childName']), // 学生姓名
                    'keyword2' => array('value' => $reserveData['time']), // 预约时间
                    'keyword3' => array('value' => $reserveData['school']),//校区
                    //'keyword4' => array('value' => '待定 To Be Determined'),//班级
                    'keyword4' => array('value' => $reserveData['class']),//班级
                    'remark' => array('value' => $reserveData['memo']),
                ),
            );

            $jsonData = json_encode($data);
            $res = CommonUtils::httpGet($url, $jsonData);
            $jsonRes = CJSON::decode($res);
            Yii::log($state . '=>' . $childid . '=>' . $openid . '=>' . $jsonRes['errcode'], CLogger::LEVEL_INFO, 'wechatwida.parent');
        }
    }
}
