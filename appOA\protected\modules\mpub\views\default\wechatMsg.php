<style>
    .wechatList{
        height: 138px;
        background: #FFFFFF;
        border-radius: 8px;
        border: 1px solid #E8EAED;
        padding:24px;
        position: relative;
        margin-bottom:24px;
    }
    .teacherImg{
        width: 42px;
        height: 42px;
        border-radius: 8px;
        object-fit: cover;
    }
    .wechatInfo{
        line-height:32px;
        align-items:center
    }
    .wechatImg{
        width: 32px;
        height: 32px;
        border-radius: 16px;
    }
    .wechatIcon{
        width: 18px;
        height: 18px;
    }
    .count{
        font-size: 24px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #4D88D2;
        cursor: pointer;
    }
    .count:hover{
        text-decoration: underline;
    }
    .mt-4{
        margin-top:-4px
    }
    .absolute{
        position:absolute;
    }
    .number{
        width: 45px;
        height: 18px;
        background: #EBEDF0;
        border-radius: 8px 2px 2px;
        position: absolute;
        left: 0;
        top: 0;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #333333;
        line-height: 17px;
        text-align: center;
        
    }
    mark{
        background: yellow;
        padding:0
    }
    .titleList{
        border-bottom:1px solid #dddddd;
        line-height:22px;
        padding:16px 0
    }
    .line14{
        line-height:14px
    }
    .p0{
        padding:0
    }
    .loading span{
        background: url('<?php echo Yii::app()->themeManager->baseUrl . '/base/images/loading.gif' ?>')no-repeat top center;
    }
    .delSearchText{
        position: absolute;
        right: 10px;
        top: 10px;
        z-index: 99;
        color: #ccc;
    }
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo Yii::t('site', 'Ivy Family') ?></li>
        <li class="active"><?php echo Yii::t('site', 'Wechat Messages') ?></li>
    </ol>
    <div class="row" id="container">
        <div v-if='pageData.branch'>
            <div>
                <div class="col-md-6 mb16 text-left">
                    <select class="form-control" style="width: 200px;" v-model="optionVal">
                        <option v-for="(val, index) in options" :key="index" :value="val.value">{{val.label}}</option>
                    </select>
                </div>
                <div class="col-md-6  mb16 text-right">
                    <div class="input-group" style="width: 200px;float: right;">
                        <span class="input-group-addon" id="basic-addon1"><i class="glyphicon glyphicon-search"></i></span>
                        <input type="text" class="form-control" placeholder="请输入姓名" v-model='search' aria-describedby="basic-addon1" name="search">
                        <span class="glyphicon glyphicon-remove delSearchText cur-p" v-if='search!=""' @click='search=""'></span>
                    </div>
                </div>
            </div>
            <div class='clearfix'></div>
            <div class='relative' v-if='search==""' >
                <div class='loading' v-if='loading'>
                    <span></span>
                </div>
                <div v-if='wechatData.length!=0'>
                    <div class='col-md-12 mb24'>
                        <button type="button" class="btn  mr16" :class='schoolType=="all"?"btn-primary":"btn-default"' @click='findSchool("all")'>全部校园</button>
                        <button type="button" class="btn mr16" :class='schoolType==key?"btn-primary":"btn-default"' v-for='(list,key,index) in schoolList'  @click='findSchool(list)'>{{list.title}}</button>
                    </div>
                    <div class="col-md-6 col-sm-12 col-lg-4" v-for='(list,index) in wechatData'>
                        <div class='wechatList flex'>
                            <span class='number'>{{index+1}}</span>
                            <img :src="list.photo" alt="" class='teacherImg mr8'>
                            <div class='flex1'>
                                <div class='font14 color3 '><strong>{{list.name}}</strong></div>
                                <div class='font13 color6 mt5'>
                                    <span v-if='pageData.branch[list.school]'>{{pageData.branch[list.school].title}} <span>ǀ</span> </span> 
                                    <span> {{list.position}}</span>
                                </div>
                                <div class='mt16 flex wechatInfo'>
                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/default.png' ?>" alt="" class='wechatImg' v-if='list.wechatPhoto=="" || list.wechatPhoto==null'>
                                    <img :src="list.wechatPhoto" alt="" class='wechatImg' v-else>
                                    <span class='flex1 ml8 font14 color3 line14'>{{list.wechatName}}</span>
                                    <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/wechatDM.png' ?>" alt="" class='wechatIcon '> 
                                    <span class='color9 ml4'>消息推送/条</span>
                                    <span class='count ml10'  @click='showDetails(list)'>{{list.count}}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class='clearfix'></div>
                </div>
                <div v-else  class='col-md-12'>
                    <div class="alert alert-warning " role="alert"><?php echo Yii::t('ptc', 'No Data'); ?></div>
                </div>
                <div class='clearfix'></div>
            </div>
            <div v-else>
                <div v-if='searchData.length!=0'>
                    <div class="col-md-6 col-sm-12 col-lg-4" v-for='(list,index) in searchData'>
                        <div class='wechatList flex'>
                            <span class='number'>{{index+1}}</span>
                            <img :src="list.photo" alt="" class='teacherImg mr8'>
                            <div class='flex1'>
                                <div class='font14 color3 '><strong class='teacherName' v-html='list.name'></strong></div>
                                <div class='font13 color6 mt5'>
                                    <span v-if='pageData.branch[list.school]'>{{pageData.branch[list.school].title}} <span>ǀ</span> </span>
                                    <span> {{list.position}}</span>
                                </div>
                                <div class='mt16 flex wechatInfo'>
                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/default.png' ?>" alt="" class='wechatImg' v-if='list.wechatPhoto=="" || list.wechatPhoto==null'>
                                    <img :src="list.wechatPhoto" alt="" class='wechatImg' v-else>
                                    <span class='flex1 ml8 font14 color3 line14  wechatName' v-html='list.wechatName'></span>
                                    <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/wechatDM.png' ?>" alt="" class='wechatIcon '> 
                                    <span class='color9 ml4'>消息推送/条</span>
                                    <span class='count ml10' @click='showDetails(list)'>{{list.count}}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-else  class='col-md-12'>
                    <div class="alert alert-warning" role="alert"><?php echo Yii::t('ptc', 'No Data'); ?></div>
                </div>
            </div>
        </div>
        <div class="modal fade" id="detailsModal" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("reg", "消息推送");?></h4>
                </div>
                <div class="modal-body p0" v-if='showList.photo'  >
                    <div class='p15'>
                        <div class='flex'>
                            <img :src="showList.photo" alt="" class='teacherImg mr8'>
                            <div class='flex1'>
                                <div class='font14 color3 '><strong>{{showList.name}}</strong></div>
                                <div class='font13 color6 mt5'>
                                    <span v-if='pageData.branch[showList.school]'>{{pageData.branch[showList.school].title}} <span>ǀ</span> </span>
                                    <span> {{showList.position}}</span>
                                </div>
                            </div>
                        </div>
                        <div class='mt16 flex wechatInfo'>
                            <div class='flex1 text-center'>
                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/default.png' ?>" alt="" class='wechatImg' v-if='showList.wechatPhoto=="" || showList.wechatPhoto==null'>
                                <img :src="showList.wechatPhoto" alt="" class='wechatImg' v-else>
                                <span class='flex1 ml8 font14 color3'>{{showList.wechatName}}</span>
                            </div>
                            <div class='flex1  text-center'>
                                <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/wechatDM.png' ?>" alt="" class='wechatIcon mt-4'> 
                                <span class='color9 ml4'>消息推送/条</span>
                                <span class='count ml10 color3  absolute'>{{showList.count}}</span>
                            </div>
                        </div>
                    </div>
                    <div class='p15 overflow-y  scroll-box' :style="'max-height:'+(height)+'px'">
                        <div class='flex titleList' v-for='(list,index) in listData'>
                            <div class='flex1 font14 color3' v-html='list.title'></div>
                            <div class='font12 color9'>{{list.createdDate}}</div>
                        </div>
                    </div>
                </div>
                </div>
            </div> 
        </div>
    </div>
</div>

<script>
    new Vue({
        el: "#container",
        data: {
            options: [{
                value: '1d',
                label: '最近24小时'
            }, {
                value: '3d',
                label: '三天内'
            }, {
                value: '7d',
                label: '一周内'
            }, {
                value: '30d',
                label: '一个月'
            }, {
                value: '90d',
                label: '三个月'
            }],
            optionVal: '1d',
            schoolType:'all',
            pageData: {},
            schoolList:{},
            wechatData:[],
            search:''  ,
            searchData:[],
            showList:{},
            listData:[],
            height:0,
            loading:false
        },
        created: function () {
            this.fetchData();
            this.search = ''  
            this.height=document.documentElement.clientHeight-250
        },
        watch: {
            optionVal: function () {
                this.fetchData();
            },
            search: function () {
                this.searchList()
            }
        },
        methods: {
            searchList: function() {
                var search = this.search;
                var searchVal = ''; //搜索后的数据
                this.searchData=[]
                if(search) {
                    searchVal = this.pageData.wechatStatistics.filter(function(product) {
                        return Object.keys(product).some(function(key) {
                            return String(product['name'].toLowerCase()).indexOf(search.toLowerCase()) !== -1 || String(product['wechatName'].toLowerCase()).indexOf(search.toLowerCase()) !== -1;
                        })
                    })
                    this.searchData=searchVal
                    this.$nextTick(() => {   
                        var context = document.querySelectorAll('.teacherName');
                        var instance = new Mark(context);
                        instance.unmark().mark(search);
                        var context = document.querySelectorAll('.wechatName');
                        var instance = new Mark(context);
                        instance.unmark().mark(search);
                    })
                }
            },
            fetchData() {
                let that=this
                this.schoolType='all'
                this.search = ''  
                this.loading=true
                $.getJSON('<?php echo $this->createUrl("wechatMsgData") ?>', {type: this.optionVal}, function (res) {
                    that.pageData=res.data
                    that.wechatData=res.data.wechatStatistics
                    res.data.wechatStatistics.forEach((item) => {
                        if(res.data.branch[item.school]){
                            that.schoolList[item.school]=res.data.branch[item.school]
                        }
                    });
                    that.loading=false
                });
            },
            findSchool(data){
                if(data!='all'){
                    let res = this.pageData.wechatStatistics.filter((item) => {
                        return item.school == data.branchid;
                    });
                    this.wechatData=res
                    this.schoolType=data.branchid;
                }else{
                    this.wechatData=this.pageData.wechatStatistics
                    this.schoolType='all'
                }
            },
            showDetails(data){
                let that=this
                this.showList=data
                $.getJSON('<?php echo $this->createUrl("wechatMsgListData") ?>', {type: this.optionVal,openid:data.openid}, function (res) {
                    that.listData=res.data
                    $('#detailsModal').modal('show')
                });
            }
        }
    });
</script>
