
<!-- Modal -->
<?php echo CHtml::form('/workflow/entrance/index', 'post',array('class'=>'J_ajaxForm'));?>
<div class="modal fade" id="<?php echo $data['modalNameId']?>" tabindex="-1" role="dialog" aria-labelledby="workflowModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel"><?php echo CommonUtils::autoLang($data['workflow']->definationObj->defination_name_cn,$data['workflow']->definationObj->defination_name_en);?> - <?php echo CommonUtils::autoLang($data['workflow']->nodeObj->node_name_cn,$data['workflow']->nodeObj->node_name_en);?></h4>
            </div>
            <div class="modal-body" id="workflow-modal-body">
                <table class="table table-striped">
                    <tbody>
                        <tr>
                            <th>折扣名称</th>
                            <td><?php echo CommonUtils::autoLang($data['discountSchool']->discountTitle->title_cn, $data['discountSchool']->discountTitle->title_en). "(". $data['discountSchool']->discount .")"?></td>
                        </tr>
                        <tr>
                            <th>折扣审核人</th>
                            <td><?php echo $data['discountSchool']->approver->getName();?></td>
                        </tr>
                        <tr>
                            <th>孩子列表</th>
                            <td>
                                <?php foreach ($data['childModel'] as $child):?>
                                <div class="pull-left mr20 mb20">
                                    <?php
                                        if($child->photo):
                                            echo CHtml::image(CommonUtils::childPhotoUrl($child->photo), '', array("width"=>80,'class'=>'media-object'));
                                        endif;
                                    ?>
                                    <h4><?php echo CHtml::link($child->getChildName(),$this->createUrl('/child/profile/index',array('childid'=>$child->childid,'branchId'=>$child->schoolid,'t'=>'parent')),array('target'=>'_blank'));?></h4>
                                </div>
                                 <?php endforeach;?>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <!-- 显示附件 -->
                <div id="fileList">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>相关资料</th>
                            </tr>
                        </thead>
                        <?php foreach ($data['workflow']->getAttachmentList() as $uploadFile): ?>
                        <tr>
                            <td><a target="_blank" href="<?php echo $uploadFile['bigimages']; ?>"><?php echo $uploadFile['notes']; ?></a></td>
                        </tr>
                        <?php endforeach; ?>
                    </table>
                </div>
                <div class="retshow">
                    <h3><?php echo Yii::t('workflow', 'Comments:'); ?></h3>
                     <?php 
                        $processList = $data['workflow']->getWorkflowProcess();
                        foreach ($processList as $process):?>
                    <h4>
                        <em><?php echo Yii::t('workflow', 'Date submitted:');?>： <?php echo OA::formatDateTime($process->start_time);?></em>
                        <span><?php echo $process->user->getName();?></span>
                    </h4>
                    <div class="clearfix"></div>
                    <div class="comment">
                        <label><?php echo Yii::t('workflow', 'Option:'); ?></label>
                        <p><?php echo nl2br(htmlspecialchars($process->process_desc));?></p>
                    </div>
                    <?php endforeach;?>
                </div>
                <?php if ($data['workflow']->useRole && $data['workflow']->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_UNOP && !$data['workflow']->isFristNode()):?>
                <div class="form-group" style="padding-top:50px;">
                    <?php echo CHtml::textArea('memo','',array('class'=>'form-control','row'=>3));?>
                </div>
                <?php endif;?>
            </div>
            <?php if ($data['workflow']->useRole && $data['workflow']->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_UNOP && !$data['workflow']->isFristNode()):?>
            <div class="modal-footer">
                <?php echo CHtml::hiddenField('definationId', $data['workflow']->getWorkflowId());?>
                <?php echo CHtml::hiddenField('nodeId', $data['workflow']->getCurrentNodeId());?>
                <?php echo CHtml::hiddenField('branchId', $data['workflow']->schoolId);?>
                <?php echo CHtml::hiddenField('operationId', $data['workflow']->getOperateId());?>
                <?php echo CHtml::hiddenField('action', 'show');?>
                <?php echo CHtml::hiddenField('buttonCategory','');?>
                <button type="button" class="btn btn-primary J_ajax_submit_btn" data-category="<?php echo WorkflowOperation::WORKFLOW_STATS_OPED;?>"><?php echo Yii::t('workflow','Yes');?></button>
                <button type="button" class="btn btn-danger J_ajax_submit_btn" data-category="<?php echo WorkflowOperation::WORKFLOW_STATS_OPDENY;?>"><?php echo Yii::t('workflow','No');?></button>
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
            </div>
            <?php endif;?>
        </div>
    </div>
</div>
<?php echo  CHtml::endForm();?>