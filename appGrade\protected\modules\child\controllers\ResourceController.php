<?php

class ResourceController extends ProtectedController {

    public function init() {
        parent::init();
        Yii::import('common.models.content.*');
    }

    public function actionContent($category) {
        if ($category == "handbook") {
            $this->render('pdf');
            Yii::app()->end();
        }
        if ($category == "newsletter") {
            $this->render('newsletter');
            Yii::app()->end();
        }
        if ($category == "mi") {
            $this->render('mi', array('category' => $category));
            Yii::app()->end();
        }
        if ($category == 'prepare'){
            $this->render('prepare');
            Yii::app()->end();
        }
        $id = Yii::app()->request->getParam("id");

        $crit = new CDbCriteria;
        $crit->compare("category", $category);
        $crit->order = "weight ASC";
        $crit->index = 'id';

        $count = Content::model()->count($crit);
        $pager = new CPagination($count);
        $pager->pageSize = 30;
        $pager->applyLimit($crit);

        $contentAll = Content::model()->findAll($crit);
        //艾毅毕业生家长心声临时解决方案
        if ($category == 'graduate' && $id == 36) {
            Yii::import('common.models.resource.Testimonials');

            $tes_cri = new CDbCriteria();
            $tes_cri->compare('status', 0);
            $tes_cri->compare('is_graduated', 1);
            $tes_cri->order = 'id DESC';
            $tes_cri->limit = 60;
            $testimonialList = Testimonials::model()->findAll($tes_cri);

            $this->render('content_testimonials', array("category" => $category, "contentAll" => $contentAll, "id" => $id, 'testimonialList' => $testimonialList));
            Yii::app()->end();
        }
        $this->render('content', array("category" => $category, "contentAll" => $contentAll, "id" => $id, "pager" => $pager));
    }

    public function actionIndex() {
        $items = $this->nav['resource']['items'];
        unset($items['index']);
        $extra = array(
            'covers' => array(
                'library' => 'library.png',
                'handbook' => 'handbook.png',
                'newsletter' => 'newsletter.png',
                'mi' => 'mi.png',
                'newsongs' => 'songs.png',
                'graduate' => 'graduate.png',
                'prepare' => 'prepare.png',
            ),
            'desc' => array(
                'library' => Yii::t("resource", 'Every campus has a library that consists of a rich collection of both Chinese and English children’s books. Every book was carefully selected by our educational experts. You can view a history of borrowed books here.'),
                'handbook' => Yii::t("resource", 'Parent Handbook is intended to provide helpful information for parents about school procedures and policies.'),
                'newsletter' => Yii::t("resource", 'At Ivy and Beyond, a monthly e-newsletter, is an additional communication channel for Ivy to let parents know what is happening in our schools and in the community.'),
                'mi' => Yii::t("resource", 'Multiple Intelligences illustrate the eight intelligences as well as each of their everyday uses.'),
                'newsongs' => Yii::t("resource", 'Children songs that are selected and sung by our teachers can be found here. This will be updated on a yearly basis.'),
                'graduate' => Yii::t("resource", 'A list of elementary schools that Ivy graduates attend after having taken Ivy\'s Elementary School Readiness Program for kindergarten children can be found here.'),
                'prepare' => Yii::t("resource", 'Entering preschool is a significant change for young children and the family. Ivy educational experts has prepared the following articles to help making the smooth transition.'),
            ),
        );
        $this->render('index', array("items" => $items, "extra" => $extra));
    }

    public function actionReadPdf($category) {
        if ($category == "handbook") {
            $v = Yii::app()->getRequest()->getParam("v", Yii::app()->language);
            $file_name = ($v == "en_us") ? 'ivy_parent_handbook_en.pdf' : 'ivy_parent_handbook_cn.pdf';

            $ossObject = 'pdfs/' . $file_name;
            $ossSDK = CommonUtils::initOSS('private');
            $file = $ossSDK->get_object($ossObject);
            header('Content-Type: ' . $file->header['content-type']);
            echo $file->body;

//            $file = $ossSDK->get_sign_url($ossObject);
//            $this->render('pdftest', array('file'=>$file));
            /*
            $local_file = Yii::app()->params['xoopsVarPath'] . '/pdfs/' . $file_name;
            if (file_exists($local_file) && is_file($local_file)) {
                header('Content-Type: application/pdf');
                readfile($local_file);
            }*/
        }
    }

    public function actionReadPdfChunked() {

        $local_file = Yii::app()->params['xoopsVarPath'] . '/parent_handbook/ivy_parent_handbook.pdf';
        if (file_exists($local_file) && is_file($local_file)) {
            // set the download rate limit (=> 20,5 kb/s)
            $download_rate = 20.5;
            header('Cache-control: private');
//            header('Content-Type: application/octet-stream');
            header('Content-Type: application/pdf');
            header('Content-Length: ' . filesize($local_file));
            header('Content-Disposition: filename=' . 'parent_handbook.pdf');

            flush();
            $file = fopen($local_file, "r");
            while (!feof($file)) {
                // send the current file part to the browser
                print fread($file, round($download_rate * 1024));
                // flush the content to the browser
                flush();
                // sleep one second
                sleep(1);
            }
            fclose($file);
        } else {
            echo Yii::t('resource', 'Please Upload Parent Handbook.');
        }
    }

    public function actionPdfEachPage($category, $page = 0) {
        $currentPage = $page - 1;
        if ($currentPage < 0) {
            $currentPage = 0;
        }
        $local_file = Yii::app()->params['xoopsVarPath'] . '/parent_handbook/ivy_parent_handbook.pdf';
        $pagecount = 0;
        if (file_exists($local_file) && is_file($local_file)) {
            Yii::import('ext.FPDI.*');
            Yii::import('ext.FPDI.filters.*');

            $pdf = new FPDI();
            $pagecount = $pdf->setSourceFile($local_file);
            $pagination = new CPagination($pagecount);
            $pagination->currentPage = $currentPage;
            $pagination->pageSize = 1;
        }
        $this->render('pdf', array("pagination" => $pagination, 'page' => $page));
    }

    public function actionReadPdfEachPage($page = 0) {

        $local_file = Yii::app()->params['xoopsVarPath'] . '/parent_handbook/ivy_parent_handbook.pdf';
        if (file_exists($local_file) && is_file($local_file)) {
            //header('Content-type: application/pdf');
            Yii::import('ext.FPDI.*');
            Yii::import('ext.FPDI.filters.*');

            $pdf = new FPDI();

            $pagecount = $pdf->setSourceFile($local_file);
            if ($page > $pagecount) {
                $page = $pagecount;
            }
            if (empty($page)) {
                $page = 1;
            }
            $tplidx = $pdf->importPage($page, '/MediaBox');
            $pdf->addPage();
            $pdf->useTemplate($tplidx, 0, 0, 0, 0, true);
            $pdf->Output('parent_handbook.pdf', 'I');
        } else {
            echo Yii::t('resource', 'Please Upload Parent Handbook.');
        }
    }

    /**
     * 这块应该独立成模块
     * 现在只需要一些基本的信息和简单的功能
     * 所以...
     */
    public function actionLibrary() {

        Yii::app()->clientScript->registerCssFile(Yii::app()->theme->baseUrl . '/css/library.css?t=' . Yii::app()->params['refreshAssets']);
        Yii::import('common.models.ivyLibrary.*');

        $card_info = Cardlinkuser::model()->with('cardType')->findByAttributes(array('userid' => $this->getChildId(), 'usertype' => 'child'));
        $view_data = null;

        $borrowing_cri = new CDbCriteria();
        $borrowing_cri->compare('userid', $this->getChildId());
        $borrowing_cri->compare('borroworback', 0);
        $borrowing_cri->compare('returnstatus', 0);
        $borrowingDataProvider = new CActiveDataProvider('InoutRecords', array(
                    'criteria' => $borrowing_cri,
                    'pagination' => false,
                ));

        $bh_cri = new CDbCriteria();
        $bh_cri->compare('userid', $this->getChildId());
        $bh_cri->compare('borroworback', 1);
        $bh_cri->compare('returnstatus', 0);
        $borrowHistoryDataProvider = new CActiveDataProvider('InoutRecords', array(
                    'criteria' => $bh_cri,
                ));

        $view_data['card_info'] = $card_info;

        $view_data['borrowingDataProvider'] = $borrowingDataProvider;
        $view_data['borrowHistoryDataProvider'] = $borrowHistoryDataProvider;

        $this->render('library', $view_data);
    }

    /* 看着没有，这个目录都不存在
    public function actionMp3() {
        $mp3 = Yii::app()->getRequest()->getParam('mp3', '');
        $file = Yii::app()->params['xoopsVarPath'] . '/mp3/' . $mp3 . '.mp3';
        header("Content-Type: audio/mpeg");
        readfile($file);
    }*/

    // Uncomment the following methods and override them if needed
    /*
      public function filters()
      {
      // return the filter configuration for this controller, e.g.:
      return array(
      'inlineFilterName',
      array(
      'class'=>'path.to.FilterClass',
      'propertyName'=>'propertyValue',
      ),
      );
      }

      public function actions()
      {
      // return external action classes, e.g.:
      return array(
      'action1'=>'path.to.ActionClass',
      'action2'=>array(
      'class'=>'path.to.AnotherActionClass',
      'propertyName'=>'propertyValue',
      ),
      );
      }
     */
}