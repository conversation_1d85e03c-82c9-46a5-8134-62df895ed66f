<?php

class CoronavirusController extends WeChatVisualController
{
    public $layout = '//layouts/bootstrap4';
    public $childid;
    public $childObj;

    public function beforeAction($action)
    {
        $this->childid = isset($_COOKIE['childid']) ? $_COOKIE['childid'] : '';
        if ($this->childid && isset($this->myChildObjs[$this->childid])) {
            $this->childObj = $this->myChildObjs[$this->childid];
        } else {
            $this->childObj = end($this->myChildObjs);
            $this->childid = $this->childObj->childid;
        }
        return true;
    }

    public function init()
    {
        parent::init(); // TODO: Change the autogenerated stub
        Yii::app()->theme = 'app';
        Yii::import('common.models.support.*');
    }

    public function actionIndex()
    {
        $this->subPageTitle = Yii::t("reg", "COVID-19 Legal Guardian Affidavit");
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/app/js/signature_pad.min.js');
        $childid = $this->childid;
        $childObj = $this->childObj;
        $childObjs = $this->myChildObjs;

        // 查询是否已填写
        $crit = new CDbCriteria();
        $crit->compare('childid', array_keys($childObjs));
        $crit->compare('status', 10);
        $model = Covid19AffidavitChild::model()->findAll($crit);

        $isSubmit = array();
        if ($model) {
            foreach ($model as $childInfo){
                $isSubmit[$childInfo->childid] = 1;
            }
        }
        $parentsModel = $childObj->getParents();
        $parentData = array();
        if ($parentsModel['father']) {
            $selected = 0;
            if ($parentsModel['father']->uid == $this->wechatUser->userid) {
                $selected = 1;
            }
            $parentData[] = array(
                'parent_name' => $parentsModel['father']->getName(),
                'parent_relationship' => 1,
                'selected' => $selected,
                'parent_relationship_name' => Yii::t('global','Father'),
                'parent_tel' => $parentsModel['father']->parent->mphone,
            );
        }
        if ($parentsModel['mother']) {
            $selected = 0;
            if ($parentsModel['mother']->uid == $this->wechatUser->userid) {
                $selected = 1;
            }
            $parentData[] = array(
                'parent_name' => $parentsModel['mother']->getName(),
                'parent_relationship' => 2,
                'selected' => $selected,
                'parent_relationship_name' => Yii::t('global','Mother'),
                'parent_tel' => $parentsModel['mother']->parent->mphone,
            );
        }

        $parentData[] = array(
            'parent_name' => '',
            'parent_relationship' => 3,
            'selected' => 0,
            'parent_relationship_name' => Yii::t('global','Legal Guardian'),
            'parent_tel' => '',
        );

        $childData = array();
        foreach ($this->myChildObjs as $k => $item) {
            $childData[] = array('childid' => $k, 'schoolid' => $item->school->title, 'childname' => $item->getChildName(), 'classid' => $item->ivyclass->title, 'isSubmit' => isset($isSubmit) && isset($isSubmit[$k]) ? $isSubmit[$k] : 0);
        }

        $this->render('index', array(
            'childid' => $childid,
            'childname' => $childObj->getChildName(),
            'schoolname' => $childObj->school->title,
            'childData' => $childData,
            'parentData' => $parentData,
            'classTitle' => $childObj->ivyclass->title,
            'isSubmit' => $isSubmit,
        ));
    }

    public function actionSave()
    {
        if (Yii::app()->request->isPostRequest) {
            $childids = Yii::app()->request->getPost('childids');
            $parent_name = Yii::app()->request->getPost('parent_name');
            $parent_relationship = Yii::app()->request->getPost('parent_relationship');
            $parent_tel = Yii::app()->request->getPost('parent_tel');
            $type = Yii::app()->request->getPost('type');
            $destination = Yii::app()->request->getPost('destination');
            $return_date = Yii::app()->request->getPost('return_date');
            $filedata = Yii::app()->request->getPost('signature');

            $this->addMessage('state', 'fail');
            if (!$childids) {
                $this->addMessage('message', '孩子不能为空');
                $this->showMessage();
            }
            $crit = new CDbCriteria();
            $crit->compare('childid', $childids);
            $crit->index = 'childid';
            $childModel = ChildProfileBasic::model()->findAll($crit);

            if (!$childModel) {
                $this->addMessage('message', '孩子不存在');
                $this->showMessage();
            }

            if ($type == 2) {
                if (!$destination) {
                    $this->addMessage('message', '目的地不能为空');
                    $this->showMessage();
                }
                if (!$return_date) {
                    $this->addMessage('message', '返回日期不能为空');
                    $this->showMessage();
                }
            }
            if(!$parent_tel || !preg_match('/^1[3456789]\d{9}$/', $parent_tel)){
                $this->addMessage('message', '请填写正确的手机号');
                $this->showMessage();
            }

            $model = new Covid19Affidavit();
            $model->parent_name = $parent_name;
            $model->parent_relationship = $parent_relationship;
            $model->parent_tel = $parent_tel;
            $model->type = $type;
            $model->destination = $destination;
            $model->return_date = $return_date ? strtotime($return_date) : '';
            $model->filedata = $filedata;
            $model->status = 10;
            $model->updated_at = time();
            $model->updated_by = $this->wechatUser->userid;

            if (!$model->validate()) {
                $error = current($model->getErrors());
                $this->addMessage('message', $error[0]);
                $this->showMessage();
            }
            // 上传签名
            if (!$model->uploadFile()) {
                $this->addMessage('message', Yii::t("reg", "Signature save failed"));
                $this->showMessage();
            }
            if (!$model->save()) {
                $error = current($model->getErrors());
                $this->addMessage('message', $error[0]);
                $this->showMessage();
            }
            foreach ($childids as $child){
                $covid19Model = new Covid19AffidavitChild();
                $covid19Model->childid = $childModel[$child]->childid;
                $covid19Model->schoolid = $childModel[$child]->schoolid;
                $covid19Model->classid = $childModel[$child]->classid;
                $covid19Model->aid = $model->id;
                $covid19Model->status = 10;
                $covid19Model->created_at = time();
                $covid19Model->created_by = $this->wechatUser->userid;
                $covid19Model->updated_at = time();
                $covid19Model->updated_by = $this->wechatUser->userid;
                $covid19Model->save();
            }

            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('vote','Success'));
            $this->showMessage();
        }
    }

    public function actionRewrite()
    {
        if (Yii::app()->request->isPostRequest) {
            $childid = Yii::app()->request->getPost('childid');

            $crit = new CDbCriteria();
            $crit->compare('childid', $childid);
            $crit->compare('status', 10);
            $model = Covid19AffidavitChild::model()->find($crit);
            if($model)
            {
                $model->status = 99;
                $model->updated_at = time();
                $model->updated_by = $this->wechatUser->userid;
                if(!$model->save()){
                    $error = current($model->getErrors());
                    $this->addMessage('message', $error[0]);
                    $this->addMessage('state', 'fail');
                    $this->showMessage();
                }
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('vote','Success'));
                $this->showMessage();
            }
        }
        $this->addMessage('state', 'fail');
        $this->addMessage('message', '非法操作');
        $this->showMessage();
    }

    public function actionLooksee()
    {
        $childid = Yii::app()->request->getPost('childid');
        $crit = new CDbCriteria();
        $crit->compare('childid', $childid);
        $crit->compare('status', 10);
        $model = Covid19AffidavitChild::model()->find($crit);
        if($model)
        {
            $covid19Model = Covid19Affidavit::model()->findByPk($model->aid);
            $data = array(
                'parent_name' => $covid19Model->parent_name,
                'parent_relationship' => $covid19Model->parent_relationship,
                'parent_tel' => $covid19Model->parent_tel,
                'type' => $covid19Model->type,
                'destination' => $covid19Model->destination,
                'return_date' => ($covid19Model->return_date) ? date("Y-m-d", $covid19Model->return_date) : '',
                'signature' => $this->getThumbOssImageUrl($covid19Model->signature),
                'updated_at' => date("Y-m-d H:i", $covid19Model->updated_at),
            );
            $this->addMessage('data', $data);
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('vote','Success'));
            $this->showMessage();
        }
        $this->addMessage('state', 'success');
        $this->addMessage('message', '未找到数据');
        $this->showMessage();
    }

    public function getThumbOssImageUrl($fileName)
    {
        $fileName = 'covid19/' . $fileName;
        // 获取文件临时地址
        $osscs = CommonUtils::initOSSCS('private');

        $object = $fileName;
        $style = 'style/w200';
        $imageUrl = $osscs->getImageUrl($object, $style);
        return $imageUrl;
    }
}
