<?php

/**
 * This is the model class for table "ivy_wechat_teacher_bind".
 *
 * The followings are the available columns in table 'ivy_wechat_teacher_bind':
 * @property integer $id
 * @property integer $teacher_id
 * @property integer $state
 * @property string $openid
 * @property string $nickname
 * @property string $headimgurl
 * @property string $wechat_info
 * @property integer $created_by
 * @property integer $updated_by
 * @property integer $created_at
 * @property integer $updated_at
 * @property integer $deleted_at
 */
class WechatTeacherBind extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_wechat_teacher_bind';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('state, created_by, updated_by, created_at, updated_at', 'required'),
			array('teacher_id, state, created_by, updated_by, created_at, updated_at, deleted_at', 'numerical', 'integerOnly'=>true),
			array('openid, nickname, headimgurl', 'length', 'max'=>255),
			array('wechat_info', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, teacher_id, state, openid, nickname, headimgurl, wechat_info, created_by, updated_by, created_at, updated_at, deleted_at', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'teacher_id' => 'Teacher',
			'state' => 'State',
			'openid' => 'Openid',
			'nickname' => 'Nickname',
			'headimgurl' => 'Headimgurl',
			'wechat_info' => 'Wechat Info',
			'created_by' => 'Created By',
			'updated_by' => 'Updated By',
			'created_at' => 'Created At',
			'updated_at' => 'Updated At',
			'deleted_at' => 'Deleted At',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('teacher_id',$this->teacher_id);
		$criteria->compare('state',$this->state);
		$criteria->compare('openid',$this->openid,true);
		$criteria->compare('nickname',$this->nickname,true);
		$criteria->compare('headimgurl',$this->headimgurl,true);
		$criteria->compare('wechat_info',$this->wechat_info,true);
		$criteria->compare('created_by',$this->created_by);
		$criteria->compare('updated_by',$this->updated_by);
		$criteria->compare('created_at',$this->created_at);
		$criteria->compare('updated_at',$this->updated_at);
		$criteria->compare('deleted_at',$this->deleted_at);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return WechatTeacherBind the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
