<!-- 加载底部导航栏 -->
<?php
$this->branchSelectParams['extraUrlArray'] = array('//mgrades/attendance/index');
$this->renderPartial('//layouts/common/branchSelectBottom');
?>

<script>
    var teachers = <?php echo CJSON::encode($teachers); ?>;
    var projects = [];
</script>
<style>
 [v-cloak] {
            display: none;
        }
</style>
<div class="container-fluid">
    <!-- 面包屑导航 -->
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('attends', 'My Courses') ?></li>
    </ol>
    <div class="row">
        <div class="col-md-2">
            <?php
            $this->widget('zii.widgets.CMenu',array(
                'items'=> $this->siderMenu,
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-right background-gray mb10'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>
        <div class="col-md-10" id="list" v-cloak>
            <h2><?php echo Yii::t('attends','My Courses');?></h2>
            <h4>
                <?php $this->renderPartial('_select'); ?>
            </h4>
            <h4>
                <span class="glyphicon glyphicon-user"></span>
                <span><?php echo $userModel ? $userModel->getName() : $uid ?></span>
                <span title="<?php echo Yii::t('attends', 'View Schedules of Other Teachers')?>" onclick="chooseTeacher(<?php echo $tid; ?>)" class="glyphicon glyphicon-chevron-down"></span>
            </h4>
            <div class="panel panel-default" v-if='courseList.length!=0'>
            <div class="panel-heading"><?php echo Yii::t('attends', 'My Courses') ?></div>
            <table class="table reportTbody">
               <thead>

               </thead>
                <tbody class="reportTbody">
                    <tr>
                       <td>#</td>

                       <td><?php echo Yii::t('attends', 'Course Code') ?></td>
                       <td><?php echo Yii::t('attends', 'Course Name') ?></td>
                       <td><?php echo Yii::t('attends', 'Course Alias') ?></td>
                       <td><?php echo Yii::t('global', 'Action') ?></td>

                    </tr>
                    <tr  v-for='(data,list) in courseList'>
                       <td width="50">{{list+1}}</td>
                       <td width="150">{{data.course_code}}</td>
                       <td width="200"> {{data.title }}</td>
                       <td width="200"> {{data.alias}}</td>
                       <td width="150">
                       <template v-if='status==1'>
                           <button type="button" class="btn btn-primary btn-xs"  @click='modify(data.title,data.id,list)'> <?php echo Yii::t('attends', 'Edit Alias') ?></button>
                       </template>
                       <template v-else>
                             <button type="button" class="btn btn-primary btn-xs disabled "  @click='modify(data.title,data.id,list)'> <?php echo Yii::t('attends', 'Edit Alias') ?></button>
                       </template>
                       </td>
                      <!--  <button type="button" class="btn btn-primary btn-sm">修改别名</button> -->
                       
                   </tr>
               </tbody>
            </table>
            </div>
            <div v-else>
               <div class="alert alert-warning" role="alert"><?php echo Yii::t('attends', 'No course has been assigned to you') ?></div>
            </div>
            <div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" data-keyboard="false" data-backdrop="static">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×
                        </button>
                        <h4 class="modal-title">
                          <?php echo Yii::t('asa', 'Edit') ?>
                        </h4>
                    </div>
                    <div class="modal-body">
                        <div class="form-horizontal">
                            <div class="form-group">
                                <label class="col-sm-3 control-label"><?php echo Yii::t('attends', 'Course Code') ?></label>
                                <div class="col-sm-9">
                                  <p class="form-control-static">{{course_code}}</p>
                                </div>
                              </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label"><?php echo Yii::t('attends', 'Course') ?></label>
                                <div class="col-sm-9">
                                  <p class="form-control-static">{{course}}</p>
                                </div>
                              </div>
                            <div class="form-group">
                            <label class="col-sm-3 control-label"><?php echo Yii::t('attends', 'Alias') ?></label>
                            <div class="col-sm-9">
                             <input type="text" :value="aliasname"  class="form-control input-sm eject_money name"/>
                            </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary paybtnsub" @click='remarks()'>
                            <?php echo Yii::t('global', 'Submit') ?>
                        </button>
                        <button type="button" class="btn btn-default" data-dismiss="modal">
                            <?php echo Yii::t('global', 'Cancel') ?>
                        </button>
                    </div>
                </div>

                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="remarks" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" ><?php echo Yii::t('attends','Select a Teacher') ?> <span id="remarks_t"></span></h4>
            </div>
            <div class="form-horizontal">
                <div class="modal-body">
                    <div name='teacherId' id="mySelect">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
var courseList = <?php echo CJSON::encode($courseList); ?>;
var status= '<?php echo $status ?>'
var teacher_id='<?php echo $teacher_id ?>'
var teacherList;
     var lists = new Vue({
        el: "#list",
        data: {
            courseList:courseList,
            aliasname:'',
            course:'',
            course_id:'' ,
            listindex:'',
            status:status,
            course_code:''
            },
            methods: {
            modify(name,id,index){
            this.course=name
            this.course_id=id
            this.listindex=index
              $.ajax({
                url: '<?php echo $this->createUrl("showAlias")?>',
                type: 'post',
                dataType: 'json',
                data: {
                    course_id:id
                },
                success: function(data) {
                    if(data.state == 'success') {
                        console.log(data)
                      lists.aliasname=data.data.alias
                      lists.course_code=data.data.course_code
                      $('#myModal').modal('show')
                    } else {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }

                },
                error: function(data) {
                    resultTip({
                        error: 'warning',
                        msg: '请求错误'
                    });
                }
            })
            },
             remarks(){
                $.ajax({
                url: '<?php echo $this->createUrl("UpdateAlias")?>',
                type: 'post',
                dataType: 'json',
                data: {
                    course_id:this.course_id,
                    alias:$('.name').val(),
                    teacherId:teacher_id
                },
                success: function(data) {
                    if(data.state == 'success') {
                     resultTip({"msg": data.message})
                     console.log('')
                     $('#myModal').modal('hide')
                     lists.courseList[lists.listindex].alias=$('.name').val()
                     //setTimeout("$('#myModal').modal('hide')", 2000)
                    } else {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }

                },
                error: function(data) {
                    resultTip({
                        error: 'warning',
                        msg: '请求错误'
                    });
                }
            })


             }
           }
        })

function chooseTeacher(tid) {
    var child = '';
    if(teacherList){
        $("#remarks").modal('show');
        $.each(teacherList,function(key,val){
            child += "<a class='mb5 btn btn-default' href='<?php echo $this->createUrl('courseAlias') ?>&teacherId="+ key +"'>" + val +  "</a> ";
        });
        $("#mySelect").html(child);
    }else{
        $.ajax({
            type: "POST",
            url: "<?php echo $this->createUrl('teacher')?>",
            data: {tid:tid},
            dataType: "json",
            success: function(data){
                $("#remarks").modal('show');
                var list = '';
                if(data){

                    $.each(data,function(key,val){
                        child += "<a class='mb5 btn btn-default' href='<?php echo $this->createUrl('courseAlias') ?>&teacherId="+ key +"'>" + val +  "</a> ";
                    });
                    // child += "<p class='clearfix'></p>";
                    $("#mySelect").html(child);
                    teacherList = data;
                }
            }
        });
    }
}
</script>

