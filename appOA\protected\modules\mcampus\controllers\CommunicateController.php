<?php
Class CommunicateController extends BranchBasedController
{
    public $dialogWidth=600;

    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        Yii::import('common.models.content.Dailymail');
        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mcampus/communicate/internal');
    }

    public function actionSelect(){
        $this->render('//layouts/common/branchSelect');
    }

    public function actionInternal($filter=''){
        $this->branchSelectParams['hideOffice'] = false;
        $page = Yii::app()->getRequest()->getParam("page", "");
        $t = strtotime('today');
        $criteria = new CDbCriteria();
        $criteria->compare('schoolid', $this->branchId);

        if($filter == 'history'){
//            $criteria1 = new CDbCriteria();
            $criteria->compare('duedate', '<'.$t);
//            $criteria1->compare('stat', 20, false, 'or');
//            $criteria->mergeWith($criteria1);
            $criteria->order = 'duedate desc';
        }
        else{
//            $criteria->compare('stat', 10);
//            $criteria->compare('startdate', '<='.$t);
            $criteria->compare('duedate', '>='.$t);
            $criteria->order = 'priority desc,duedate';
        }
        $pages = new CPagination(Dailymail::model()->count($criteria));
        $pages->route = "internal";
        $pages->pageSize = 10;
        $pages->applyLimit($criteria);
        $pages->setCurrentPage($page-1);

        $sort = new CSort('Dailymail');
        $sort->applyOrder($criteria);

        $items = Dailymail::model()->findAll($criteria);

        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile($cs->getCoreScriptUrl().'/jui/js/jquery-ui-i18n.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/tinymce/tinymce.min.js');
        // $baseDir = Yii::getPathOfAlias('common.extensions.ueditor.ueditor_full');
        // $assets = Yii::app()->getAssetManager()->publish($baseDir, true, -1);
        // $cs->registerScriptFile($assets.'/ueditor.teacher.config.js');
        // $cs->registerScriptFile($assets.'/ueditor.all.js');

        $this->render('index', array('filter'=>$filter, 'items'=>$items, 'pages' => $pages));
    }

    public function actionBulletinEdit()
    {
        if(isset($_POST['Dailymail'])){
            if(in_array('ivystaff_teacher', Yii::app()->user->roles)) {
                if (!in_array($_POST['Dailymail']['cate'], array(4, 5))) {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('dailymail', 'Please select a category.'));
                    $this->showMessage();
                }
                $depModel = DepPosLink::model()->findByPk($this->staff->profile->occupation_en);
                if ($depModel->department_id != 137) { // 中学部
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('dailymail', 'Selected category only applies to MS teachers only.'));
                    $this->showMessage();
                }
            }
            $id = $_POST['Dailymail']['id'];
            $model = Dailymail::model()->findByPk($id);
            if($model==null) {
                $model = new Dailymail();
                $files = Yii::app()->request->getPost('files');
            }
            $model->attributes  = $_POST['Dailymail'];
            $model->dueDay              = $_POST['Dailymail']['dueDay'];
            $model->startdate           = strtotime($model->startdate);
            $model->duedate             = $model->dueDay != 99 ? $model->startdate+86400*$model->dueDay : strtotime($model->duedate);
            $model->schoolid            = $this->branchId;
            $model->stat	            = isset($_POST['Dailymail']['stat']) && $_POST['Dailymail']['stat'] ? 10 : 20;
            $model->type                = 10;
            $model->oneitem             = time();
            $model->updated_timestamp   = time();
            $model->userid              = Yii::app()->user->id;
            if($model->save()){
                if (isset($files) && !empty($files)) {
                    $uploads = Uploads::model()->findAllByPk($files);
                    foreach ($uploads as $upload) {
                        $upload->link_id = $model->id;
                        $upload->save();
                    }
                }
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','Data saved!'));
                $this->addMessage('refresh', true);
            }
            else{
                $this->addMessage('state', 'fail');
                $errs = current($model->getErrors());
                $this->addMessage('message', $errs?$errs[0]:Yii::t('message','Saving Failed!'));
            }
            $this->showMessage();
        }
    }

    public function actionBulletinDel()
    {
        $id = Yii::app()->getRequest()->getParam("id", 0);
        if($id){
            $model = Dailymail::model()->findByPk($id);
            if($model->delete()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', '删除成功！');
                $this->addMessage('refresh', true);
            }
            else{
                $this->addMessage('state', 'fail');
                $errs = current($model->getErrors());
                $this->addMessage('message', $errs?$errs[0]:'删除失败！');
            }
            $this->showMessage();
        }
    }

    public function actionBulletinMgtline()
    {
        $id = Yii::app()->getRequest()->getParam("id", 0);
        $stat = Yii::app()->getRequest()->getParam("stat", 0);
        if($id && $stat){
            $model = Dailymail::model()->findByPk($id);
            $model->stat=$stat;
            if($model->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('refresh', true);
            }
            else{
                $this->addMessage('state', 'fail');
                $errs = current($model->getErrors());
                $this->addMessage('message', $errs?$errs[0]:Yii::t('message','Failed!'));
            }
            $this->showMessage();
        }
    }

    /**
     * 保存上传的附件
     * @return [type] [description]
     */
    public function actionSaveUploadFile()
    {       
        if ($file = CUploadedFile::getInstanceByName('file')) {
            $id = Yii::app()->request->getPost('id');
            if ($file->size > 5*1024*1024) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('workflow', 'Upload failed. Size should not exceed 5M'));
                $this->showMessage();
            }
            $needType = array('jpg','png','pdf','gif','jpeg', 'pptx', 'xlsx', 'docx', 'doc');
            $ext = strtolower($file->getExtensionName());

            if (!in_array($ext, $needType)) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('workflow', 'Upload failed. Wrong file type'));
                $this->showMessage();
            }
            $modName = 'bulletin';
            $funcName = 'attachment';
            $datePath = date('Y') .'/'. date('m') . '/';
            $basePath = Yii::app()->params['xoopsVarPath'] . '/uploads/';
            $filePath = $basePath . $datePath;
            $thumbPath = $basePath .'thumbs/'. $datePath;
            if (!is_dir($filePath)) {
                mkdir($filePath, 0777, true);
            }
            $baseUrl = Yii::app()->params['OABaseUrl'] . "/modules/myspace/task/getthumb2.php?img=";
            $fileName = uniqid() . '.' . $ext;
            if (!$file->saveAs($filePath . $fileName)) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('workflow', 'Upload failed'));
                $this->showMessage();
            }
            // 处理缩略图
            if (in_array($ext, array('jpg','png','jpeg'))) {
                Yii::import('application.extensions.image.Image');
                $image = new Image($filePath . $fileName);
                $image->quality(100);
                if ($image->width > 600) {
                    $image->resize(600);
                }
                if (!is_dir($thumbPath)) {
                    mkdir($thumbPath . $fileName, 0777, true);
                }
                $image->save( $thumbPath . $fileName);
            }
            //保存表ivy_uploads
            $uploadModel = new Uploads();
            $uploadModel->link_id = $id;
            $uploadModel->mod_name = $modName;
            $uploadModel->func_name = $funcName;
            $uploadModel->file_name = $datePath . $fileName;

            $uploadModel->notes = pathinfo($file->name,PATHINFO_FILENAME);
            $uploadModel->update_time = time();
            $uploadModel->update_user = Yii::app()->user->id;
            if (!$uploadModel->save()) {
                unlink($filePath . $fileName);
                $error = current($uploadModel->getErrors());
                $this->addMessage('message', Yii::t('workflow', 'Upload failed'.$error[0]));
                $this->showMessage();
            }
            $this->addMessage('data', array('fileId'=>$uploadModel->id,'fileName'=>$uploadModel->notes .'.'. $ext,'url'=>$baseUrl . $uploadModel->file_name));
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('workflow', 'Upload successful'));
            $this->showMessage();
        }
        Yii::app()->end();
    }

    /**
     * 删除附件
     * @return [type] [description]
     */
    public function actionDeleteUploadFile()
    {
        if (Yii::app()->request->isPostRequest) {
            $id = Yii::app()->request->getPost('id',0);
            if ($uploadModel = Uploads::model()->findByPk($id)) {
                //删除附件
                $fileName = $uploadModel->file_name;
                $filePath = Yii::app()->params['xoopsVarPath'] . '/uploads/' ;
                if ($uploadModel->delete()) {
                    @unlink($filePath . $fileName);
                    @unlink($filePath .'thumbs/'. $fileName);
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('workflow', 'Delete successful'));
                    $this->showMessage();
                }
            }
        }
        $this->addMessage('state', 'fail');
        $this->addMessage('message', Yii::t('workflow', 'Delete failed'));
        $this->showMessage();
    }

    /**
     * 修改附件显示名
     * @param string $value [description]
     */
    public function actionChangeFileName()
    {
        if (Yii::app()->request->isPostRequest) {
            $id = Yii::app()->request->getPost('id',0);
            $notes = Yii::app()->request->getPost('notes',0);
            if ($notes && $uploadModel = Uploads::model()->findByPk($id)) {
                $uploadModel->notes = $notes;
                $ext = end(explode('.', $uploadModel->file_name));
                if ($uploadModel->save()) {
                    $this->addMessage('state', 'success');
                    $this->addMessage('data', $notes .'.'. $ext);
                    $this->addMessage('message', Yii::t('workflow', 'Modify successful'));
                    $this->showMessage();
                }
            }
        }
        $this->addMessage('state', 'fail');
        $this->addMessage('message', Yii::t('workflow', 'Modify failed'));
        $this->showMessage();
    }
}