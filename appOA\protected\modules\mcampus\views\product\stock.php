<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Product list'), array('//mcampus/product/index')) ?></li>
        <li><?php echo $model->getTitle() ?></li>
    </ol>
    <div class="modal-header">
        <h4 class="modal-title" id="exampleModalLabel" style="font-size: 16px"><?php echo $model->getTitle() ?></h4>
        <h4 class="modal-title" id="exampleModalLabel">
            <span style="font-size: 14px">所属分类：<?php echo CommonUtils::autoLang($productsCatModel->title_cn, $productsCatModel->title_en)?></span>
        </h4>
    </div>
    <?php $config = Products::config();
    $attrType = array();
    foreach ($config as $ckey=>$cfg) {
        $attrType[$ckey] = $cfg['title'];
    }
    ?>
    <div class="modal-body">
        <?php if($model->school_id == $branchId):?>
            <div class="mb10 row">
                <div class="col-sm-2 form-group">
                    <button type="button" class="btn btn-primary" onclick="showUpdateAttr()"><?php echo Yii::t('site','Add attribute') ?></button>
                </div>
            </div>
        <?php endif; ?>
            <div class="form-group">
                <div class="col-xs-12">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <?php foreach($config[$model->type]['item'] as $key =>$types){ ?>
                                    <th><?php echo Yii::t('products', $types['title']); ?></th>
                                <?php } ?>
                                <th><?php echo Yii::t('site','Price') ?></th>
                                <th><?php echo Yii::t('site','Scribing Price') ?></th>
                                <?php if($is_share_stock === true){?>
                                    <th><?php echo Yii::t('site','本校库存') ?></th>
                                    <th><?php echo Yii::t('site','共享库存') ?></th>
                                <?php }else{?>
                                    <th><?php echo Yii::t('site','Inventory') ?></th>
                                <?php }?>
                                <th><?php echo Yii::t('site','缺货登记') ?></th>
                                <th><?php echo Yii::t('global','Action') ?></th>
                            </tr>
                        </thead>
                        <?php
                        if($arrtType):
                        foreach($arrtType as $item):
                            ?>
                        <tbody>
                            <tr>
                                <?php
                                $i = 0;
                                foreach($config[$model->type]['item'] as $key =>$types){ ?>
                                    <td class="col-md-1"><?php echo $arrt[$item['id']][$i]?></td>
                                <?php
                                $i++;
                                } ?>
                                <!--售价-->
                                <td class="col-md-2">
                                    <?php
                                        if($model->school_id == $branchId){
                                            echo CHtml::textField('price', $item['price'], array('class'=>'form-control price_' . $item['id'], 'onchange'=>'exchangeAttrPrice('. $item['id'] .')'));
                                        }else{
                                            echo $item['price'];
                                        }
                                    ?>
                                </td>
                                <!-- 划线价格-->
                                <td class="col-md-2">
                                    <?php
                                    if($model->school_id == $branchId){
                                        echo CHtml::textField('scribing_price', $item['scribing_price'], array('class'=>'form-control scribing_price_' . $item['id'], 'onchange'=>'exchangeAttrScribingPrice('. $item['id'] .')'));
                                    }else{
                                        echo $item['scribing_price'];
                                    }
                                    ?>
                                </td>
                                <?php if($is_share_stock === true){?>
                                    <td class="col-md-2"><?php echo $item['stock']?></td>
                                    <td class="col-md-2"><?php echo $item['share_stock']?></td>
                                <?php }else{?>
                                    <td class="col-md-2"><?php echo $item['stock']?></td>
                                <?php }?>

                                <td class="col-md-2"><?php echo $item['count']?></td>
                                <td class="col-md-2">
                                    <?php
                                    if($branchId == $model->school_id){
                                        if($item['status']){
                                            echo '<a title="点击下架" class="btn btn-success btn-xs" href="javascript:;" onclick="updateAttrStatus('.$item['id']. "," .$item['status'].')"><span class="glyphicon glyphicon-check">上架</span></a>'. " ";
                                        }else{
                                            echo '<a title="点击上架" class="btn btn-warning btn-xs" href="javascript:;" onclick="updateAttrStatus('.$item['id']. "," .$item['status'].')"><span class="glyphicon glyphicon-unchecked">下架</span></a>'. " ";
                                        }
                                        echo '<a title="删除" class="btn btn-danger btn-xs" href="javascript:;" onclick="removeAttr('.$item['id'].')"><span class="glyphicon glyphicon-remove">删除</span></a>'. " ";
                                    }
                                    //共享库存-可调整本校库存
                                    echo CHtml::link(Yii::t('site', 'Inventory management'), array('updatestock', 'paid' => $item['id'], "branchId" => Yii::app()->controller->branchId), array('class' => 'J_modal btn btn-xs btn-info'));
                                    ?></td>
                            </tr>
                        </tbody>
                        <?php endforeach;
                        endif; ?>
                    </table>
                </div>
            </div>
    </div>
</div>
<div class="modal fade" id="modal1" class="1123" tabindex="-1" role="dialog" aria-labelledby="modal">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="exampleModalLabel"><?php echo Yii::t('global','Add') ?></h4>
            </div>
            <?php
            $typeNum = 5;
            $form=$this->beginWidget('CActiveForm', array(
                'id'=>'courseGroup-form',
                'enableAjaxValidation'=>false,
                'action' => $this->createUrl('productsAttr'),
                'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
            )); ?>
            <div class="modal-body">
                <?php
                $key = 1;
                for ($x = 0; $x < 5; $x++) {
                    ?>
                <div class="form-group">
                    <label class="col-xs-2 control-label"><?php echo  Yii::t('site','Attribute') ?></label>
                        <?php
                        $i = 1;
                        foreach($config[$model->type]['item'] as $k =>$types){
                            $type = array();
                            foreach($types['option'] as $keya=>$itea){
                                $type[$keya] = Yii::t('products',$itea);
                            }

                            echo '<div class="col-xs-2">';
                            $a = ( $i ==1) ? $config[$model->type]["title"]: "";
                            echo CHtml::dropDownList('type[' .$key. '][' . $i . ']', "",  $type, array('empty'=> Yii::t('products', $types['title']), 'class'=>'col-xs-2 form-control'));
                            if($i%2 == 0){
                                echo '<br/>';
                            }
                            echo "</div>";
                            $i++;
                        }

                        ?>
                </div>
                <div class="form-group">
                    <label class="col-xs-2 control-label"><?php echo Yii::t('user','Price'); ?></label>
                    <div class="col-xs-9">
                        <?php echo CHtml::textField('price['.$key.']', "", array('class'=>'form-control', 'placeholder'=>Yii::t("site","Price"))); ?>
                        <?php echo CHtml::hiddenField('paid', $model->id) ?>
                        <?php echo Chtml::hiddenField('branchId',$this->branchId); ?>
                        <?php echo Chtml::hiddenField('attrType', $model->type); ?>
                    </div>
                </div>
                <?php  $key++;
                } ?>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
                <button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
            </div>
            <?php $this->endWidget(); ?>
        </div>
    </div>
</div>

<script>
    var modal = '<div class="modal fade" id="modal" class="1123" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog  modal-lg" role="document"><div class="modal-content"></div></div></div>';
    $('body').append(modal);

    function showUpdateAttr() {
        $('#modal1').modal({backdrop:'static'})
    }

    function exchangeAttrPrice(paid) {
        var price = $(".price_" + paid).val();
        var url = '<?php echo $this->createUrl('updatePrice');?>';
        $.ajax({
            type : "post",
            data: {price:price,paid:paid},
            url  : url,
            dataType: 'json',
            success : function(data) {
                if(data.state == "success") {
                    resultTip({msg: '修改成功'});
                }else{
                    resultTip({msg: data.message, error: 'warning'});
                }
            }
        });
    }

    function exchangeAttrScribingPrice(paid)
    {
        var price = $(".scribing_price_" + paid).val();
        var url = '<?php echo $this->createUrl('updatePrice');?>';
        $.ajax({
            type : "post",
            data: {price:price,paid:paid,scribing:1},
            url  : url,
            dataType: 'json',
            success : function(data) {
                if(data.state == "success") {
                    resultTip({msg: '修改成功'});
                }else{
                    resultTip({msg: data.message, error: 'warning'});
                }
            }
        });
    }

    function updateAttrStatus(id,status) {
        var url = '<?php echo $this->createUrl('updateAttrStatus');?>';
        $.ajax({
            type : "post",
            data: {id:id,status:status},
            url  : url,
            dataType: 'json',
            success : function(data) {
                if(data.state == "success") {
                    resultTip({msg: '修改成功'});
                        location.reload();
                }else{
                    resultTip({msg: data.message, error: 'warning'});
                }
            }
        });
    }

    function removeAttr(id) {
        if (confirm("您确定要删除吗？")) {
            var url = '<?php echo $this->createUrl('removeAttr');?>';
            $.ajax({
                type : "post",
                data: {id:id},
                url  : url,
                dataType: 'json',
                success : function(data) {
                    if(data.state == "success") {
                        resultTip({msg: 'success!'});
                        location.reload();
                    }else{
                        resultTip({msg: data.message, error: 'warning'});
                    }
                }
            });
        }
    }

    function cbSuccess(){
        location.reload();
    }
</script>

