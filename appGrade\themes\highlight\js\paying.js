
function initTotalDueAmount(){
    var da = 0;
    $("input[name^='invoice_id']:checked").each(function(index,item){
        da += parseFloat($("#due_amount_"+item.value).val());
    });
    $('#due_amount_total').html(setCurrency(da.toFixed(2)));
    $("input[name^='submoney'],input[name^='submoney2']").val(da.toFixed(2));
    $("#paymoney,#paymoney2").html(setCurrency(da.toFixed(2)));
}

/*
 * 格式化货币
 * http://www.aspxhome.com/javascript/jsmagic/20081/291730.htm
 */
function setCurrency(s){

    if(/[^0-9\.]/.test(s)) return "invalid value";
    s=s.replace(/^(\d*)$/,"$1.");
    s=(s+"00").replace(/(\d*\.\d\d)\d*/,"$1");
    s=s.replace(".",",");
    var re=/(\d)(\d{3},)/;
    while(re.test(s))
        s=s.replace(re,"$1,$2");
    s=s.replace(/,(\d\d)$/,".$1");
    return s.replace(/^\./,"0.");

}
$(document).ready(function(){

    initTotalDueAmount();
    $("input[name^='invoice_id'],input[name^='unpaid-list_c0_all']").live('change', function() {
        initTotalDueAmount();
    });

    $(".bankinfo input[type=radio]").click(function(){
        $(".bankinfo label").removeClass("current");
        $(".bankinfo label[for=" + $(this).attr('id') + "]").addClass("current");
    });

    $("input[name^='sub']").live('change', function() {
        if ($("#sub").attr("checked") == 'checked') {
            $("#submoney").css("display","inline"); 
            $("#paymoney").css("display","none"); 
        }else{
            $("#submoney").css("display","none"); 
            $("#paymoney").css("display","inline"); 
        }
    });
});


function confirmInvoiceInfo(pm,notice1,notice2,notice3){
    var invoice_num = $("#invoiceinfo input[name^=invoice_id]:checked").length;
    if(invoice_num < 1){
        $("#mydialog").html("<div>"+notice1+"</div>");
        $("#mydialog").dialog("open");
        return false;
    }else if(invoice_num > 6){
        $("#mydialog").html("<div>"+notice3+"</div>");
        $("#mydialog").dialog("open");
        return false;
    }
    var selectBank = 0;
    var payType = 0;
    if('yeepay' == pm){
        var selectBank =  $("#invoiceinfo input[name=yeeBankId]:checked").length;
        var payType = $("#invoiceinfo input[name=payType]:checked").val();
        if(selectBank < 1 ){
            $("#mydialog").html("<div>"+notice2+"</div>");
            $("#mydialog").dialog("open");
            return false;
        }
    }else if('alipay' == pm){
        var selectBank =  $("#invoiceinfo input[name=aliBankId]:checked").length;
        var payType = $("#invoiceinfo input[name=payType]:checked").val();
        if(selectBank < 1 ){
            $("#mydialog").html("<div>"+notice2+"</div>");
            $("#mydialog").dialog("open");
            return false;
        }
    }else if ('allinpay' == pm) {
        var selectBank =  $("#invoiceinfo input[name=allinBankId]:checked").length;
        var payType = $("#invoiceinfo input[name=payType]:checked").val();
        if (!(payType == '1' || payType =='11')) {
            $("#mydialog").html("<div>"+notice4+"</div>");
            $("#mydialog").dialog("open");
            return false;
        }
        if ($("#sub").attr('checked') == 'checked'){
            if (invoice_num != 1) {
                $("#mydialog").html("<div>多次付款功能只可以选择一个账单支付</div>");
                $("#mydialog").dialog("open");
                return false;
            }
            var invoice_id = $("input[name^=invoice_id]:checked").val();
            //支付金额不能超过账单金额
            var due_amount = parseFloat($('#due_amount_'+invoice_id).val());
            var submoney = parseFloat($("#submoney").val().replace(/\,/,''));
            var patrn = new RegExp(/^(([1-9]\d*)|0)(\.\d{1,2})?$/);
            if (!patrn.test(submoney) || submoney > due_amount || submoney == 0 ) {
                $("#mydialog").html("<div>请正确填写支付金额，支付金额不能超过账单金额</div>");
                $("#mydialog").dialog("open");
                return false;
            }
        }
    }else if ('wxpay' == pm){
        var dueTotal = parseFloat($('#due_amount_total').html().replace(/\,/,''));
        var submoney = parseFloat($("#submoney2").val().replace(/\,/,''));
        var patrn = new RegExp(/^(([1-9]\d*)|0)(\.\d{1,2})?$/);
        if(!patrn.test(submoney) || submoney == 0) {
            $("#mydialog").html(notice5);
            $("#mydialog").dialog("open");
            return false;
        }
        if (dueTotal != submoney){
            if (invoice_num != 1) {
                $("#mydialog").html(notice4);
                $("#mydialog").dialog("open");
                return false;
            }
            var invoice_id = $("input[name^=invoice_id]:checked").val();
            //支付金额不能超过账单金额
            var due_amount = parseFloat($('#due_amount_'+invoice_id).val());
            if (submoney > due_amount || submoney == 0 ) {
                $("#mydialog").html(notice6);
                $("#mydialog").dialog("open");
                return false;
            }
        }
    }

    //    $("#invoiceinfo input[name='due_amount\[\]']").remove();
    $("#payment_mothod").val(pm);
    $("#confirm_invoice_onlineBank").submit();

}
