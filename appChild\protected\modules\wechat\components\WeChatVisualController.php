<?php

class WeChatVisualController extends WeChatBasedController
{
	public $access_token;	
	public $msg = array();
	public $state = 'ivy';

	public function init(){

		if(isset($_GET['lang'])){
			$lang = strtolower($_GET['lang']);
			if(!in_array($lang, array("en_us","zh_cn"))){
				$lang = "en_us";
			}
			Mims::setLangCookie($lang);
		}
		else{
			$langcookie = Yii::app()->request->cookies['child_mims_lang'];
			$lang = isset($langcookie->value) ? $langcookie->value : '';
		}
		Yii::app()->language = $lang;

		$this->siteTitle = Yii::t("global","IvyOnline");
		if ($state = Yii::app()->request->getParam('state', '')) {
			$this->state = $state;
			$this->setAppid($state);
		}

		Yii::app()->theme = 'mobile2';
		
		Yii::import('common.models.wechat.*');
		$this->setOpenid();

		$this->wechatUser = WechatUser::model()->findByPk($this->openid);
		if(is_null($this->wechatUser)){
			$this->isBound = self::STAT_BOUND_NONE;
		}else{
			if($this->wechatUser->valid && $this->wechatUser->userid)
				$this->isBound = self::STAT_BOUND;
			else
				$this->isBound = self::STAT_BOUND_EXPIRED;
		}

		if( $this->isBound == self::STAT_BOUND ){

			$parent = IvyParent::model()->findByPk($this->wechatUser->userid);
			if (!empty($parent) && count(@unserialize($parent->childs))) {
				$cids = @unserialize($parent->childs);
				$this->myChildObjs = ChildProfileBasic::model()->findAllByPk($cids, array("index" => "childid","order"=>"childid DESC"));

				foreach($this->myChildObjs as $child){
					$this->activeChildIds[] = $child->childid;
					if(!empty($child->schoolid)){
						$this->campusId = $child->schoolid;
					}
				}
				//孩子id从大到小排列
				$this->branchList = Branch::model()->getBranchList(null, true);
				rsort($this->activeChildIds);

				$this->updateLastContact();
			}
		}
		else{
			$redirectUrl = $this->createUrl('/wechat/bind/index');
			$this->redirect($redirectUrl);
			// $this->forward('/wechat/bind/index');
		}
	}

	public function setOpenid()
	{
		if (!Mims::isProduction()) {
			if (isset($_GET['code'])) {
				// 设置跳转 URL
				$pathInfo = Yii::app()->request->pathInfo;
				Yii::app()->request->cookies['startPath'] = new CHttpCookie('startPath', $pathInfo);
			}
			$this->openid = 'oHBKPwV23NHyswXhsw91S9BIQ_Hw_test';
			// $this->openid = 'ouwmTjoLoT65BLJfGnQQWJkSKHYE';
			return;	
		}
		if(isset($_GET['openid']) && isset($_GET['access_token'])){
			$url = 'https://api.weixin.qq.com/sns/auth?access_token=%s&openid=%s';
			$ret = json_decode(file_get_contents(sprintf($url, $_GET['access_token'], $_GET['openid'])));
			if(isset($ret->errcode) && $ret->errcode == 0){
				$this->openid = $_GET['openid'];
				$this->access_token = $_GET['access_token'];
			}
		}
		elseif(isset($_GET['code'])){
			$code = $_GET['code'];
			$url = 'https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=authorization_code';
			$ret = json_decode(file_get_contents(sprintf($url, $this->appId, $this->appSecret, $code)));
			if(isset($ret->openid)){
				$this->openid = $ret->openid;
				$this->access_token = $ret->access_token;
				// 设置cookie
				Yii::app()->request->cookies['openid'] = new CHttpCookie('openid', $ret->openid);
				Yii::app()->request->cookies['access_token'] = new CHttpCookie('access_token', $ret->access_token);
				Yii::app()->request->cookies['unionid'] = new CHttpCookie('unionid', $ret->unionid);
				Yii::app()->request->cookies['state'] = new CHttpCookie('state', $this->state);
				// 设置跳转 URL
				$pathInfo = Yii::app()->request->pathInfo;
				Yii::app()->request->cookies['startPath'] = new CHttpCookie('startPath', $pathInfo);

				$redirectUrl = Yii::app()->createUrl($pathInfo) . "/?state=$this->state&openid=$this->openid&access_token=$this->access_token";
				$this->redirect($redirectUrl);
			}
		}
	}

	public function createUrl($route, $params = array(), $ampersand = '&', $parentOnly = false)
	{
		if (!$parentOnly) {
			if (empty($params['openid'])) {
				$params['openid'] = $this->openid;
			}	
			if (empty($params['access_token'])) {
				$params['access_token'] = $this->access_token;
			}
			if (empty($params['state'])) {
				$params['state'] = $this->state;
			}
		}
		return parent::createUrl($route, $params, $ampersand);
	}

    public function addMessage($key = '', $value='') {
        $accept = array('referer', 'refresh', 'state', 'message', 'callback', 'data', 'error');
        if ( in_array($key, $accept) ){
            $this->msg[$key]=$value;
        }
	}

	public function showMessage() {
		echo CJSON::encode($this->msg);
        Yii::app()->end();
	}

	public function setAppid($state)
	{	
		$config = array(
			'ivy' => array(
				'appId' => 'wx903fba9d4709cf10',
				'appSecret' => '388486edf736f1c078c808f291ce0075',
				'siteTitle' => Yii::t("global","IvyOnline"),
			),		
			'ds' => array(
				'appId' => 'wxb1a42b81111e29f3',
				'appSecret' => '7fec437b1b5cbbb64b32e170e6857a7f',
				'siteTitle' => Yii::t("global","Daystar Online"),
			),
		);

		if ($state) {
			$this->appId = $config[$state]['appId'];
			$this->appSecret = $config[$state]['appSecret'];
			$this->siteTitle = $config[$state]['siteTitle'];
			if ($state == 'ds') {
				Yii::app()->params['unIvy'] = true;
			}
		}
	}

	public function updateLastContact()
	{
		$time = time();
		if(!is_null( $this->wechatUser ) && isset($this->branchList[$this->campusId]) && isset($this->branchList[$this->campusId]['abb']) ){
			//更新本地微信用户数据
			if( ( $time - $this->wechatUser->last_contact ) > 1800	){
				if(!$this->wechatUser->account){
					$this->wechatUser->account = $this->state;
				}

				$this->wechatUser->last_contact = $time;
				$this->wechatUser->child_active = count($this->activeChildIds);
				$this->wechatUser->campus = $this->branchList[$this->campusId]['abb'];
				$this->wechatUser->save();
				// 更新用户信息表
				$this->wechatUser->updateInfov2();

				$usermodel = User::model()->findByPk($this->wechatUser->userid);
				if($usermodel){
					$usermodel->last_login = $time;
					$usermodel->save(false);
				}
			}
			//更新远程微信用户组
			if( ( $this->wechatUser->group_sync == 0 ) || ($time - $this->wechatUser->group_sync) > 2592000 ){

				$ch = curl_init();
				$command = 'doGroup';
				$data = array(
					"postTime" => $time,
					"command" => $command,
					"params" => base64_encode(serialize(array(
						'openid' => $this->wechatUser->openid,
						'groupKey' => $this->branchList[$this->campusId]['abb'],
					))),
					"postKey" => md5(sprintf("%s&%s&%s", $time, $command, $this->securityKey))
				);
				curl_setopt($ch, CURLOPT_URL,  $this->createUrl("/weixin/remoteCall/doGroup"));
				curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
				curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
				curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
				$return = curl_exec($ch);
				if($return){
					$array = CJSON::decode($return);
					if($array['errcode'] < 1){
						$this->wechatUser->group_sync = time();
						$this->wechatUser->save();
					}
				}
				curl_close($ch);
			}
		}
	}
}