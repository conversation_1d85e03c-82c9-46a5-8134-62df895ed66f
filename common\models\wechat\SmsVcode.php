<?php

/**
 * This is the model class for table "ivy_sms_vcode".
 *
 * The followings are the available columns in table 'ivy_sms_vcode':
 * @property integer $id
 * @property integer $type
 * @property string $phone
 * @property string $code
 * @property integer $phone_valid
 * @property integer $auth_ok
 * @property string $ip
 * @property integer $created
 * @property string $memo
 */
class SmsVcode extends CActiveRecord
{
	const TYPE_ASA_PARENT = 1;

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_sms_vcode';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('type, phone, code, phone_valid, auth_ok, ip, created', 'required'),
			array('type, phone_valid, auth_ok, created', 'numerical', 'integerOnly'=>true),
			array('phone', 'length', 'max'=>11),
			array('code', 'length', 'max'=>4),
			array('ip', 'length', 'max'=>64),
			array('memo', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, type, phone, code, phone_valid, auth_ok, ip, created, memo', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'type' => 'Type',
			'phone' => 'Phone',
			'code' => 'Code',
			'phone_valid' => 'Phone Valid',
			'auth_ok' => 'Auth Ok',
			'ip' => 'Ip',
			'created' => 'Created',
			'memo' => 'Memo',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('type',$this->type);
		$criteria->compare('phone',$this->phone,true);
		$criteria->compare('code',$this->code,true);
		$criteria->compare('phone_valid',$this->phone_valid);
		$criteria->compare('auth_ok',$this->auth_ok);
		$criteria->compare('ip',$this->ip,true);
		$criteria->compare('created',$this->created);
		$criteria->compare('memo',$this->memo,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return SmsVcode the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}