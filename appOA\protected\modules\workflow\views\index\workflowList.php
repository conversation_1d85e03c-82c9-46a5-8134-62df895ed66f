
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Workspace'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Workflow Workspace'), array('//workflow/index/index'))?></li>
        <li class="active">工作流统计</li>
    </ol>

    <div class="row">
        <!-- 左侧菜单 -->
        <div class="col-md-2 col-sm-2 mb10">
            <ul class="nav nav-pills nav-stacked background-gray" id="pageCategory">
                <?php foreach($wordDlowObj as $item){ ?>
                    <li class="<?php echo ($item->defination_id == $definationId) ? "active" : ""; ?>"><?php echo CHtml::link((Yii::app()->language == 'zh_cn' ? $item->defination_name_cn : $item->defination_name_en ), array('//workflow/index/workflowCount', 'definationId' => $item->defination_id)) ?></a></li>
                <?php } ?>
            </ul>

        </div>
        <?php if($dataProvider) {?>
            <div class="col-md-10 col-sm-10">
                <div class="panel panel-default">
                    <div class="panel-body">
                        <?php
                        $this->widget('ext.ivyCGridView.BsCGridView', array(
                            'id'=>'confirmed-visit-grid',
                            'afterAjaxUpdate'=>'js:head.Util.modal',
                            'dataProvider'=>$dataProvider,
                            'template'=>"{items}{pager}{summary}",
                            'colgroups'=>array(
                                array(
                                    "colwidth"=>array(100,100,100,100),
                                )
                            ),
                            'columns'=>array(
                                array(
                                    'name'=> '标题',
                                    'value'=> array($this,'showWorkflow'),
                                ),
                                array(
                                    'name' => Yii::t('asa','申请状态'),
                                    'value'=> array($this, 'getWays'),
                                ),
                                array(
                                    'name' => Yii::t('asa','申请时间'),
                                    'value'=>'date("Y-m-d", $data->start_time)',
                                ),
                                array(
                                    'name'=> Yii::t('asa','操作'),
                                    'value'=> array($this, 'getButton'),
                                ),
                            ),
                        ));
                        ?>
                    </div>
                </div>
            </div>
        <?php } ?>
    </div>
</div>
<div id="workflow-modal-template"></div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>