<div class="container" id="container">
 	<div class="cell">
 		<div class="hd">
 			<h1 class="page_title"><?php echo Yii::t('wechat', 'Invoice'); ?></h1>
 		</div>
 		<div class="bd">
 		<p class="twodimensioncode_box">
			<?php 
				if (!empty($QRcodeUrl)) {
				    $filename = 'qrcode_'.Yii::app()->user->id.'_'.uniqid();
				    $this->widget('ext.qrcode.QRCodeGenerator',array(
				        'data' => $QRcodeUrl,
				        'subfolderVar' => true,
				        'matrixPointSize' => 8,
				        'filename' => $filename,
				        'filePath' => Yii::app()->params['uploadPath'],
				        'fileUrl' => Yii::app()->params['uploadBaseUrl'],
				    ));
				}else{
			          echo Yii::t('wechat', 'Error in payment process. Please try again.');
			    }
			 ?>
<!-- 			 <i>
			 	<img src="<?php echo Yii::app()->theme->baseUrl . '/images/fingerprints2.png'; ?>">
			 </i> -->
		 </p>
<!--		 <div>--><?php //echo Yii::t('wechat', 'Press the QR code for 2 seconds and Extract QR code to pay'); ?><!--</div>-->
		 <div><?php echo Yii::t('wechat', 'Long-press the QR code to make payment, or scan this on another device, or take a photo of this QR code on another device and scan it with your current device.'); ?></div>
 		</div>
 	</div>
</div>

<script>
	//监听支付结果
	var orderId = '<?php echo $orderId;?>';
	function checkState()
	{
	    $.ajax({
	        type: 'post',
	        url: '<?php echo $this->createUrl('checkState')?>',
	        data: {id: orderId},
	        dataType: 'json',
	        success:function (data) {
		        if(data.state == 'success'){
		            window.location.href='<?php echo $this->createUrl("paymentResult", array("result"=>"success"));?>';
		        }
		        else {
		            checkState();
		        }
	        }
	    });
	}
	checkState();
</script>
