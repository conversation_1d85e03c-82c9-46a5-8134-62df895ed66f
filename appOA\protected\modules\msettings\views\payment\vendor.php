<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','System Management'), array('/msettings'))?></li>
        <li class="active">第三方支付</li>
    </ol>

    <div class="row">
        <div class="col-md-2 col-sm-2">
            <?php
            $mainMenu = array(
                array('label'=>Yii::t('user','银行账户'), 'url'=>array("//msettings/payment/vendor", "category"=>"account")),
                array('label'=>Yii::t('user','账户关联'), 'url'=>array("//msettings/payment/vendor", "category"=>"relation")),
            );

            $this->widget('zii.widgets.CMenu',array(
                'items'=> $mainMenu,
                'id'=>'pageCategory',
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-right background-gray'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>
        <?php if($category == 'account'):?>
            <div class="col-md-10 col-sm-10">
                <?php
                if(!empty($category))
                    $this->renderPartial('vendor/'.$category, array('model'=>$model));
                ?>
            </div>
        <?php else:?>
            <div class="col-md-2 col-sm-2">
                <div class="list-group" id="account-list">
                    <?php foreach($items as $item):?>
                        <a href="javascript:;" class="list-group-item" data-id="<?php echo $item->id?>"><?php echo $item->name?></a>
                    <?php endforeach;?>
                </div>
            </div>
            <div class="col-md-8 col-sm-8">
                <?php
                if(!empty($category))
                    $this->renderPartial('vendor/'.$category, array('model'=>$model));
                ?>
            </div>
        <?php endif;?>
    </div>

</div>