<?php

/**
 * This is the model class for table "ivy_yp_response_info".
 *
 * The followings are the available columns in table 'ivy_yp_response_info':
 * @property string $r6_Order
 * @property integer $p1_MerId
 * @property string $r0_Cmd
 * @property integer $r1_Code
 * @property string $r2_TrxId
 * @property double $r3_Amt
 * @property string $r4_Cur
 * @property string $r5_Pid
 * @property string $r7_Uid
 * @property string $r8_MP
 * @property integer $r9_BType
 * @property string $rb_BankId
 * @property string $ro_BankOrderId
 * @property string $rp_PayDate
 * @property string $rq_CardNo
 * @property string $ru_Trxtime
 * @property string $hmac
 */
class YeepayResponse extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return YeepayResponse the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_yp_response_info';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('r6_Order', 'required'),
			array('p1_MerId, r1_Code, r9_BType', 'numerical', 'integerOnly'=>true),
			array('r3_Amt', 'numerical'),
			array('r6_Order, r2_TrxId, r7_Uid, rb_BankId', 'length', 'max'=>50),
			array('r0_Cmd, r5_Pid, rp_PayDate, ru_Trxtime', 'length', 'max'=>20),
			array('r4_Cur', 'length', 'max'=>10),
			array('r8_MP', 'length', 'max'=>255),
			array('ro_BankOrderId, rq_CardNo', 'length', 'max'=>100),
			array('hmac', 'length', 'max'=>32),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('r6_Order, p1_MerId, r0_Cmd, r1_Code, r2_TrxId, r3_Amt, r4_Cur, r5_Pid, r7_Uid, r8_MP, r9_BType, rb_BankId, ro_BankOrderId, rp_PayDate, rq_CardNo, ru_Trxtime, hmac', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'r6_Order' => 'R6 Order',
			'p1_MerId' => 'P1 Mer',
			'r0_Cmd' => 'R0 Cmd',
			'r1_Code' => 'R1 Code',
			'r2_TrxId' => 'R2 Trx',
			'r3_Amt' => 'R3 Amt',
			'r4_Cur' => 'R4 Cur',
			'r5_Pid' => 'R5 Pid',
			'r7_Uid' => 'R7 Uid',
			'r8_MP' => 'R8 Mp',
			'r9_BType' => 'R9 Btype',
			'rb_BankId' => 'Rb Bank',
			'ro_BankOrderId' => 'Ro Bank Order',
			'rp_PayDate' => 'Rp Pay Date',
			'rq_CardNo' => 'Rq Card No',
			'ru_Trxtime' => 'Ru Trxtime',
			'hmac' => 'Hmac',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('r6_Order',$this->r6_Order,true);
		$criteria->compare('p1_MerId',$this->p1_MerId);
		$criteria->compare('r0_Cmd',$this->r0_Cmd,true);
		$criteria->compare('r1_Code',$this->r1_Code);
		$criteria->compare('r2_TrxId',$this->r2_TrxId,true);
		$criteria->compare('r3_Amt',$this->r3_Amt);
		$criteria->compare('r4_Cur',$this->r4_Cur,true);
		$criteria->compare('r5_Pid',$this->r5_Pid,true);
		$criteria->compare('r7_Uid',$this->r7_Uid,true);
		$criteria->compare('r8_MP',$this->r8_MP,true);
		$criteria->compare('r9_BType',$this->r9_BType);
		$criteria->compare('rb_BankId',$this->rb_BankId,true);
		$criteria->compare('ro_BankOrderId',$this->ro_BankOrderId,true);
		$criteria->compare('rp_PayDate',$this->rp_PayDate,true);
		$criteria->compare('rq_CardNo',$this->rq_CardNo,true);
		$criteria->compare('ru_Trxtime',$this->ru_Trxtime,true);
		$criteria->compare('hmac',$this->hmac,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}