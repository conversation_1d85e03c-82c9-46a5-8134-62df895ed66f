<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'bus-chilren-form',
	'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm'),
)); ?>
<div class="pop_cont pop_table" style="height:auto;">
    <table width="100%">
        <col width="150" class="th" />
        <col width="400" />
        <col />
        <tbody>
            <?php if (!$model->childid):?>
            <tr>
                <th><?php echo $form->labelEx($model,'childid'); ?></th>
                <td>
                    <?php
                    $this->widget('ext.search.ChildSearchBox', array(
                        'acInputCSS' => 'length_4',
                        'allowMultiple' => true,
                        'useModel'=>true,
                        'model'=>$model,
                        'attribute'=>'childid'
                    ));
                    ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'childid'); ?></div></td>
            </tr>
            <?php else:
                echo $form->hiddenField($model, 'childid');
            endif;?>
            <tr>
                <th><?php echo $form->labelEx($model,'busid'); ?></th>
                <td>
                    <?php
                    echo $form->dropDownList($model,'busid',CHtml::listData(SchoolBus::model()->getSchoolBuses($this->branchId), 'bid', 'bus_title'), array('empty'=>Yii::t('global','Please Select')));
                    ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'busid'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'startyear'); ?></th>
                <td>
                    <?php
                    echo $form->dropDownList($model,'startyear',Calendar::model()->getUpdownYear($this->branchObj->schcalendar), array('empty'=>Yii::t('global','Please Select')));
                    ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'startyear'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'time1'); ?></th>
                <td>
                    <?php echo $form->textField($model,'time1',array('maxlength'=>255,'class'=>'input', 'placeholder'=>'8:00 AM.')); ?>
                    <label><input type="checkbox" id="t1check">无接站服务</label>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'time1'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'addr1'); ?></th>
                <td>
                    <?php echo $form->textField($model,'addr1',array('maxlength'=>255,'class'=>'input')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'addr1'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'time2'); ?></th>
                <td>
                    <?php echo $form->textField($model,'time2',array('maxlength'=>255,'class'=>'input', 'placeholder'=>'4:00 PM.')); ?>
                    <label><input type="checkbox" id="t2check">无送达服务</label>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'time2'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'addr2'); ?></th>
                <td>
                    <?php echo $form->textField($model,'addr2',array('maxlength'=>255,'class'=>'input')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'addr2'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'remark'); ?></th>
                <td>
                    <?php echo $form->textArea($model,'remark',array('class'=>'length_5')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'remark'); ?></div></td>
            </tr>
        </tbody>
    </table>
</div>
<div class="pop_bottom">
    <button type="button" class="btn fr" id="J_dialog_close"><?php echo Yii::t('global','Cancel');?></button>
    <button class="btn fr btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
</div>
<?php $this->endWidget(); ?>

<script type="text/javascript">
$('#t1check').change(function(){
    if ( $(this).attr('checked') ){
        $('#SchoolBusChild_time1').attr('disabled', true).addClass('disabled');
        $('#SchoolBusChild_addr1').attr('disabled', true).addClass('disabled');
    }
    else {
        $('#SchoolBusChild_time1').attr('disabled', false).removeClass('disabled');
        $('#SchoolBusChild_addr1').attr('disabled', false).removeClass('disabled');
    }
});
$('#t2check').change(function(){
    if ( $(this).attr('checked') ){
        $('#SchoolBusChild_time2').attr('disabled', true).addClass('disabled');
        $('#SchoolBusChild_addr2').attr('disabled', true).addClass('disabled');
    }
    else {
        $('#SchoolBusChild_time2').attr('disabled', false).removeClass('disabled');
        $('#SchoolBusChild_addr2').attr('disabled', false).removeClass('disabled');
    }
});
</script>