<?php $form=$this->beginWidget('CActiveForm', array(
    'id'=>'visits-form',
    'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm'),
));
?>

<div class="pop_cont pop_table">
    <table width="100%" class="" style="height:auto;">
        <col width="150" />
        <col width="400" />
        <col />
        <tbody>
        <tr>
            <th><?php echo $form->labelEx($model,'visit_date'); ?></th>
            <td>
                <?php
                $this->widget('common.extensions.datePicker.JMy97DatePicker', array(
                    'name'=>CHtml::activeName($model,'visit_date'),
                    'value'=>$model->visit_date,
                    'options'=>array('dateFmt'=>'yyyy-MM-dd','opposite'=>true),
                    'htmlOptions'=>array('readonly'=>'readonly', 'class'=>'input'),
                ));
                ?>
            </td>
            <td><div class="fun_tips"><?php echo $form->error($model,'visit_date'); ?></div></td>
        </tr>
        <tr>
            <th><?php echo $form->labelEx($model,'visit_time'); ?></th>
            <td>
                <?php echo $form->textField($model,'visit_time',array('maxlength'=>255,'class'=>'input length_6')); ?>
            </td>
            <td><div class="fun_tips"><?php echo $form->error($model,'visit_time'); ?></div></td>
        </tr>
        <tr>
            <th><?php echo $form->labelEx($model,'child_name'); ?></th>
            <td>
                <?php echo $form->textField($model,'child_name',array('maxlength'=>255,'class'=>'input length_6')); ?>
            </td>
            <td><div class="fun_tips"><?php echo $form->error($model,'child_name'); ?></div></td>
        </tr>
        <tr>
            <th><?php echo $form->labelEx($model,'parent_name'); ?></th>
            <td>
                <?php echo $form->textField($model,'parent_name',array('maxlength'=>255,'class'=>'input length_6')); ?>
            </td>
            <td><div class="fun_tips"><?php echo $form->error($model,'parent_name'); ?></div></td>
        </tr>
        <tr>
            <th><?php echo $form->labelEx($model,'phone'); ?></th>
            <td>
                <?php echo $form->textField($model,'phone',array('maxlength'=>255,'class'=>'input length_6')); ?>
            </td>
            <td><div class="fun_tips"><?php echo $form->error($model,'phone'); ?></div></td>
        </tr>
        <tr>
            <th><?php echo $form->labelEx($model,'email'); ?></th>
            <td>
                <?php echo $form->textField($model,'email',array('maxlength'=>255,'class'=>'input length_6')); ?>
            </td>
            <td><div class="fun_tips"><?php echo $form->error($model,'email'); ?></div></td>
        </tr>
        <tr>
            <th><?php echo $form->labelEx($model,'status'); ?></th>
            <td>
                <?php echo $form->radioButtonList($model, 'status', array(1=>'信息准确，人准备来参观（用户信息将进入潜客列表，需后续跟进）', 2=>'信息准确，人不来（用户信息将进入潜客列表，需后续跟进）', 3=>'过期、虚假或重复信息（用户信息将不进入潜客列表，无需后续跟进）')); ?>
            </td>
            <td><div class="fun_tips"><?php echo $form->error($model,'status'); ?></div></td>
        </tr>
        </tbody>
    </table>
</div>
<div class="pop_bottom">
    <button type="button" class="btn fr" id="J_dialog_close"><?php echo Yii::t('global','Cancel');?></button>
    <button class="btn fr btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
</div>

<?php $this->endWidget(); ?>
<script>
    function updateCB(data)
    {
        window.parent.showAll('pending-visit-grid');
        window.parent.showAll('confirmed-visit-grid');
    }
</script>