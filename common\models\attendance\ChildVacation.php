<?php

/**
 * This is the model class for table "ivy_child_vacation".
 *
 * The followings are the available columns in table 'ivy_child_vacation':
 * @property integer $id
 * @property integer $child_id
 * @property string $school_id
 * @property integer $class_id
 * @property integer $vacation_time_start
 * @property integer $vacation_time_end
 * @property integer $est_begin_time
 * @property integer $begin_time
 * @property integer $cancel_lunch
 * @property string $type
 * @property string $vacation_reason
 * @property integer $uid
 * @property integer $updata_time
 * @property integer $stat
 */
class ChildVacation extends CActiveRecord
{
	public $name;
	public $time;
	public $minute;
	public $vacation_reason_late;
	public $child_id_late;
	public $child_id_leave;
	public $vacation_time_start_late;
	public $vacation_time_start_leave;

	const VACATION_SICK_LEAVE = 10;     // 病假
	const VACATION_ONLINE_PRESENT = 11; // 线上出勤
	const VACATION_AFFAIR_LEAVE = 20;   // 事假
	const VACATION_OTHER = 30; //其他
	const VACATION_LATE = 40; //迟到
	const VACATION_LEAVE= 50; //早退
    const VACATION_ABSENT= 60; //旷课

	const STATUS_CHECKED = 1;
	const STATUS_UNCHECKED = 0;

	/**
     * @desc 此表存放 除正常出勤以外的其他签到数据（线上出勤，病假，事假，迟到，旷课）
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_child_vacation';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('child_id, school_id, vacation_time_start, vacation_time_end, type, vacation_reason, uid, updata_time', 'required'),
			array('child_id, class_id, vacation_time_start, vacation_time_end, begin_time, cancel_lunch, est_begin_time, uid, updata_time, stat', 'numerical', 'integerOnly'=>true),
			array('school_id, type', 'length', 'max'=>255),
			array('vacation_reason', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, child_id, school_id, class_id, vacation_time_start, vacation_time_end, cancel_lunch, begin_time, est_begin_time, type, vacation_reason, uid, updata_time, stat', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'childProfile' => array(self::BELONGS_TO, 'ChildProfileBasic', 'child_id'),
			'user' => array(self::BELONGS_TO, 'User', 'uid'),
			'ivyclass' => array(self::BELONGS_TO, 'IvyClass', array('class_id' => 'classid')),
			'tracks' => array(self::HAS_MANY, 'ChildVacationTrack', 'vid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'child_id' =>  Yii::t("campus",'Students on leave/tardy'),
			'school_id' =>  Yii::t("campus",'school_id'),
			'class_id' =>  Yii::t("campusreport",'classid'),
			'vacation_time_start' =>  Yii::t("campus",'Start Date'),
			'vacation_time_end' =>  Yii::t("campus",'End Date'),
			'est_begin_time' =>  Yii::t("campus",'Expected Time'),
			'begin_time' =>  Yii::t("campus",'begin_time'),
			'cancel_lunch' =>  Yii::t("campus",'cancel_lunch'),
			'type' =>  Yii::t("campus",'Type of Leave/tardy'),
			'vacation_reason' => Yii::t("labels",'Memo'),
			'uid' =>  Yii::t("labels",'Operator'),
			'updata_time' => Yii::t('global', 'Update Time'),
			'stat' => 'Stat',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('child_id',$this->child_id);
		$criteria->compare('school_id',$this->school_id,true);
		$criteria->compare('class_id',$this->class_id);
		$criteria->compare('vacation_time_start',$this->vacation_time_start);
		$criteria->compare('vacation_time_end',$this->vacation_time_end);
		$criteria->compare('est_begin_time',$this->est_begin_time);
		$criteria->compare('cancel_lunch',$this->cancel_lunch);
		$criteria->compare('begin_time',$this->begin_time);
		$criteria->compare('type',$this->type,true);
		$criteria->compare('vacation_reason',$this->vacation_reason,true);
		$criteria->compare('uid',$this->uid);
		$criteria->compare('updata_time',$this->updata_time);
		$criteria->compare('stat',$this->stat);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return ChildVacation the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	public static function typeConf($type=0)
	{
		$conf = array(
			self::VACATION_SICK_LEAVE => Yii::t('campus', 'Sick Leave'),
			self::VACATION_ONLINE_PRESENT => Yii::t('attends', 'OP'),
			self::VACATION_AFFAIR_LEAVE => Yii::t('campus', 'Personal Leave'),
			self::VACATION_LATE => Yii::t('campus', 'Tardy'),
			self::VACATION_OTHER => Yii::t('user', 'Others'),
			self::VACATION_ABSENT => Yii::t('attends', 'Absent'),
		);
		if($type){
			return $conf[$type];
		}
		return $conf;
	}

	public function getVacations($timestamp = '', $schoolid = '')
	{
		if (!$timestamp) {
			$timestamp = strtotime('today');
		}

		$ret = array();

		if($timestamp && $schoolid){
			$criteria = new CDbCriteria();
			$criteria->select = array('id', 'stat', 'type', 'class_id', 'child_id', 'vacation_reason', 'est_begin_time', 'begin_time', 'uid', 'school_id', 'vacation_time_start', 'vacation_time_end');
			$criteria->compare('stat', ChildVacation::STATUS_CHECKED);
			$criteria->compare('school_id', $schoolid);
			$criteria->compare('vacation_time_start', '<='.$timestamp);
			$criteria->compare('vacation_time_end', '>='.$timestamp);
			// $criteria->order = 'id DESC, type DESC';
			$items = $this->findAll($criteria);
			foreach($items as $item){
				$ret[$item->child_id] = array(
					'type' => $item->type,
					'title' => self::typeConf($item->type),
					'classid' => $item->class_id,
					'child_id' => $item->child_id,
					'name' => $item->childProfile->getChildName(),
					'photo' => $item->childProfile->photo,
					'vacation_reason' => $item->vacation_reason,
					'est_begin_time' => $item->est_begin_time,
					'begin_time' => $item->begin_time,
                    'uid'=>$item->uid,
				);
			}
		}

		return $ret;
	}

	public static function getConfig()
	{
		return array(
			"10" => Yii::t('campus','Sick Leave'),
			"11" => Yii::t('attends','Online Present'),
			"20" => Yii::t('campus','Personal Leave'),
			"40" => Yii::t('campus','Tardy'),
			"50" => Yii::t('campus','early leaving'),
            "60" => Yii::t('attends','Absent'),
			/*"30" => Yii::t('payment','Other'),
			"40" => Yii::t('campus','Tardy'),*/
		);
	}

    public static function getMonth()
    {
        $hour = array();
        for($i = 7;$i<24; $i++){
            $hour[sprintf("%02d",$i)] = sprintf("%02d",$i);
        }

        $minute = array();
        for($m=0;$m<60;$m++){
            $minute[sprintf("%02d",$m)] = sprintf("%02d",$m);
        }

        return array(
            "hour" => $hour,
            "minute" => $minute,
        );
	}

	protected function afterSave()
	{
		Yii::import('common.models.child.ChildDailySignLog');
		// 保存日志
		$status = $this->type;
		if ($this->stat == 0) {
			$status = $this->type + 9;
		}
        if($status == 60){//60是旷课 但是log表的60已经被占用重新赋值
            $status = 61;
        }
		$start = $this->vacation_time_start;
		$end = $this->vacation_time_end;
		ChildDailySignLog::signLog($this->school_id, $this->class_id, $this->child_id, $status, $start, $end, $this->uid);
		parent::afterSave();
	}

	protected function afterDelete()
	{
		Yii::import('common.models.child.ChildDailySignLog');
        if($this->type == 60){//60是旷课 但是log表的60已经被占用重新赋值
            $status = 62;#取消旷课
        }elseif ($this->type == 11){
            $status = 12;#取消线上出勤
        }else{
            $status = $this->type + 9;
        }
		$start = $this->vacation_time_start;
		$end = $this->vacation_time_end;
		ChildDailySignLog::signLog($this->school_id, $this->class_id, $this->child_id, $status, $start, $end, $this->uid);
		parent::afterDelete();
	}

	public function getVacationType()
    {
        $type = array(
            10 => '病假',
            11 => '线上出勤',
            20 => '事假',
            30 => '其他',
            40 => '迟到',
            50 => '早退',
		);
        return isset($type[$this->type]) ? $type[$this->type] : '';
    }

    public function getVacationTypeEn()
    {
        $type = array(
            10 => 'Sick Leave',
            11 => 'Online Present',
            20 => 'Personal Leave',
            30 => 'Other',
            40 => 'Tardy',
            50 => 'early leaving',
		);
        return isset($type[$this->type]) ? $type[$this->type] : '';
    }
}
