<?php

/**
 * This is the model class for table "ivy_dailymail".
 *
 * The followings are the available columns in table 'ivy_dailymail':
 * @property integer $id
 * @property string $en_content
 * @property string $cn_content
 * @property string $schoolid
 * @property integer $stat
 * @property integer $type
 * @property integer $issend
 * @property integer $sourceid
 * @property integer $oneitem
 * @property integer $priority
 * @property integer $startdate
 * @property integer $duedate
 * @property integer $updated_timestamp
 * @property integer $userid
 */
class Dailymail extends CActiveRecord
{
    public $dueDay=3;
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_dailymail';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('cate, en_content, schoolid, stat, type, oneitem, updated_timestamp, userid', 'required'),
			array('stat, type, issend, sourceid, oneitem, priority, startdate, duedate, updated_timestamp, userid, cate', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>255),
            array('title', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, en_content, cn_content, schoolid, stat, type, issend, sourceid, oneitem, priority, startdate, duedate, updated_timestamp, userid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'files' => array(self::HAS_MANY, 'Uploads', 'link_id', 'condition'=>'files.mod_name="bulletin"'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'title' => Yii::t('dailymail', 'Title'),
			'en_content' => Yii::t('dailymail', 'Content'),
			'cn_content' => 'Cn Content',
			'schoolid' => 'Schoolid',
			'stat' => Yii::t('dailymail', 'Stat'),
			'type' => 'Type',
			'issend' => 'Issend',
			'sourceid' => 'Sourceid',
			'oneitem' => 'Oneitem',
			'priority' => Yii::t('dailymail', 'Priority'),
			'startdate' => Yii::t('dailymail', 'Start Date'),
			'duedate' => 'Duedate',
			'updated_timestamp' => 'Updated Timestamp',
			'userid' => 'Userid',
			'cate' => Yii::t('dailymail', 'Cate'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('en_content',$this->en_content,true);
		$criteria->compare('cn_content',$this->cn_content,true);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('stat',$this->stat);
		$criteria->compare('type',$this->type);
		$criteria->compare('issend',$this->issend);
		$criteria->compare('sourceid',$this->sourceid);
		$criteria->compare('oneitem',$this->oneitem);
		$criteria->compare('priority',$this->priority);
		$criteria->compare('startdate',$this->startdate);
		$criteria->compare('duedate',$this->duedate);
		$criteria->compare('updated_timestamp',$this->updated_timestamp);
		$criteria->compare('userid',$this->userid);
		$criteria->compare('cate',$this->cate);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return Dailymail the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public static function getPrioritys()
    {
        return array(
            0 => Yii::t('dailymail', 'General'),
            1 => Yii::t('dailymail', 'Important'),
            2 => Yii::t('dailymail', 'Critical'),
        );
    }

    public static function getDueDay()
    {
        return array(
            1 => Yii::t('dailymail', 'One Day'),
            2 => Yii::t('dailymail', 'Two Day'),
            3 => Yii::t('dailymail', 'Three Day'),
            5 => Yii::t('dailymail', 'Five Day'),
            10 => Yii::t('dailymail', 'Ten Day'),
            99 => Yii::t('dailymail', 'Custom'),
        );
    }    

    public static function getCate($schoolid)
    {
        $array = array(
    		'BJ_DS' => array(
    			0 => Yii::t('dailymail', 'All School'),
    			1 => Yii::t('dailymail', 'Kindergarten'),
    			2 => Yii::t('dailymail', 'Elementary School'),
//    			3 => Yii::t('dailymail', 'Middle School'),
    			4 => Yii::t('dailymail', 'Secondary School (Students)'),
    			5 => Yii::t('dailymail', 'Secondary School (Staff)'),
    		),
        );
        if ($schoolid == 'all') {
        	return $array;
        }
        if (!isset($array[$schoolid])) {
        	return false;
        }        
        return $array[$schoolid];
    }
}
