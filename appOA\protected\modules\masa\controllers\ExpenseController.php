<?php

class ExpenseController extends BranchBasedController
{
    public $printFW = array();
    public $usersName = array();
    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        $this->branchSelectParams['urlArray'] = array('//masa/expense/index');

        // jquery ui
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue2.js');
    }

	public function actionIndex($groupId = 0)
    {
        $courseGroup = AsaCourseGroup::getGroup($this->branchId, false, false, false);
        $crit = new CDbCriteria;
        $crit->compare('branch', $this->branchId);
        $crit->compare('type', 1);
        $crit->compare('status', array(AsaExpense::STATS_DRAFT, AsaExpense::STATS_REFER, AsaExpense::STATS_PAST, AsaExpense::STATS_REFUSAL));
        $model = new CActiveDataProvider('AsaExpense', array(
            'criteria' => $crit,
            'sort' => array(
                'defaultOrder' => 'updated DESC',
            ),
            'pagination'=>array(
                'pageSize'=>50,
            ),
        ));

        $users = array();
        foreach($model->getData() as $child){
            $users[$child->expense_uid] = $child->expense_uid;
        }
        if($users){
            $usersModel = User::model()->findAllByPk($users);

            foreach($usersModel as $user){
                $this->usersName[$user->uid] = $user->getName();
            }
        }

        $crit = new CDbCriteria;
        $crit->compare('branch', $this->branchId);
        $crit->compare('type', 2);
        $crit->compare('status', array(AsaExpense::STATS_DRAFT, AsaExpense::STATS_REFER, AsaExpense::STATS_PAST, AsaExpense::STATS_REFUSAL));
        $model2 = new CActiveDataProvider('AsaExpense', array(
            'criteria' => $crit,
            'sort' => array(
                'defaultOrder' => 'updated DESC',
            ),
            'pagination'=>array(
                'pageSize'=>50,
            ),
        ));

        $users = array();
        foreach($model2->getData() as $child){
            $users[$child->expense_uid] = $child->expense_uid;
        }
        if($users){
            $usersModel = User::model()->findAllByPk($users);

            foreach($usersModel as $user){
                $this->usersName[$user->uid] = $user->getName();
            }
        }

        // 当前课程组下的所有课程
        $this->render('index', array(
            'model' => $model,
            'model2' => $model2,
        ));
	}

    public function actionExpenesAdd()
    {
        $expenesId = Yii::app()->request->getParam('expenesId', 0);
        $type = Yii::app()->request->getParam('type', 1);

        $crit = new CDbCriteria;
        $crit->compare('status', AsaCourseGroup::STATUS_ACTIVE);
        $crit->compare('schoolid', $this->branchId);
        $groupObj = AsaCourseGroup::model()->findAll($crit);
        $group = array();
        $course = array();
        foreach($groupObj as $item){
            $group[$item->id] = $item->getName();
            foreach($item->course as $courseItem){
                $course[$item->id][$courseItem->id] = $courseItem->getTitle();
            }
        }

        $criteria = new CDbCriteria;
        $criteria->compare('site_id', $this->branchId);
        $criteria->compare('type',1);
        $vendor = AsaVendor::model()->findAll($criteria);
        $teacher = array();
        $teacherInfo = array();
        if($vendor){
            foreach($vendor as $item){
                $teacher[$item->ivy_uid] = $item->getName();
                $teacherInfo[$item->ivy_uid]['bankName'] = $item->vendorInfo->bank_name;
                $teacherInfo[$item->ivy_uid]['bank'] = $item->vendorInfo->bank;
                $teacherInfo[$item->ivy_uid]['bankAccount'] = $item->vendorInfo->bank_account;
            }
        }
        $model = $this->loadModel($expenesId);
        $modelItem = array();
        if($model->id){
            $criteria = new CDbCriteria;
            $criteria->compare('eid', $model->id);
            $asaExpenseItemObj = AsaExpenseItem::model()->findAll($criteria);
            if($asaExpenseItemObj){
                $courseIds = array();
                foreach($asaExpenseItemObj as $item){
                    $courseIds[] = $item->courseid;
                    $modelItem[$item->id] = array(
                        'id' => $item->id,
                        'eid' => $item->eid,
                        'content' => $item->content,
                        'groupid' => $item->groupid,
                        'courseid' => $item->courseid,
                        'amount' => $item->amount,
                        'files' => array(),
                        'status' => 0,  // 同一门课程是否已有付款
                    );
                    foreach(CJSON::decode($item->files) as $fileItem){
                        $modelItem[$item->id]['files'][] = $this->createUrl('downloadsExpense', array('fileName'=>$fileItem));
                    }
                }
                if ($model->type == 2) {
                    $crit = new CDbCriteria();
                    $crit->compare('t.courseid', $courseIds);
                    $crit->compare('asaExpense.type', 2);
                    $crit->compare('asaExpense.status', array(0, 10, 20));
                    $crit->with = 'asaExpense';
                    $otherExpenses = AsaExpenseItem::model()->findAll($crit);
                    foreach ($otherExpenses as $otherExpense) {
                        foreach ($modelItem as $k => $item) {
                            if ($item['courseid'] == $otherExpense->courseid) {
                                $modelItem[$k]['status'] += 1;
                            }
                        }
                    }
                }
            }
        }
        if (Yii::app()->request->isPostRequest) {
            if (empty($model->id)) {
                $model->amount = 0;
            }
            $model->attributes = $_POST['AsaExpense'];
            $model->status = AsaExpense::STATS_DRAFT;
            // $model->created = strtotime($model->created);
            $model->branch = $this->branchId;
            $model->updated = time();
            $model->uid = Yii::app()->user->id;
            if ($model->save()) {
                $data = array(
                    'eid' => $model->id,
                    'expense_uid' => $model->expense_uid,
                    'title' => $model->title,
                    'payee_user' => $model->payee_user,
                    'payee_bank' => $model->payee_bank,
                    'payee_account' => $model->payee_account,
                    'created' => date("Y-m-d", $model->created),
                );
                $this->addMessage('state', 'success');
                $this->addMessage('data', $data);
                $this->addMessage('message', Yii::t('message', '提交成功!'));
                $this->addMessage('callback', 'cbSuccess');
            }else{
                $error = current($model->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', $error[0]);
            }
            $this->showMessage();
        }

        if($model->id){
            $model->created = date("Y-m-d", $model->created);
        }

        $typeList = CommonUtils::LoadConfig('CfgASA');
        $bank = array();
        foreach($typeList['bank'] as $k=>$item){
            $bank[$k] = Yii::app()->language == 'zh_cn' ? $item['cn'] : $item['en'];
        }
        $bank[0] = '其它';

        $view = 'update';
        $typeArr = array(1 => '报销', 2 => '付款');
        if ($type == 2) {
            $model->uid = $this->staff->uid;
            $view = 'update2';
        }

        $this->renderpartial($view, array(
            'model' => $model,
            'teacher' => $teacher,
            'group' => $group,
            'course' => $course,
            'modelItem' => $modelItem,
            'teacherInfo' => $teacherInfo,
            'bank' => $bank,
            'type' => $type,
            'typeArr' => $typeArr,
        ));
    }

    public function actionExpenseItemAdd()
    {
        if(Yii::app()->request->isPostRequest){
            $expenesId = intval(Yii::app()->request->getParam('expenesId',2));
            $model = new AsaExpenseItem();
            $model->eid = $expenesId;
            $model->attributes = $_POST['AsaExpenseItem'];
            $model->files = CJSON::encode($_POST['AsaExpenseItem']['files']);
            $model->courseid = ($model->courseid) ? $model->courseid : 0;
            $model->uid = Yii::app()->user->id;
            $model->updated = time();

            if($model->save()){
                $expenseObj = AsaExpense::model()->findByPk($model->eid);
                $expenseObj->amount = $model->amount + $expenseObj->amount;
                if($expenseObj->save()){
                    $data = array(
                        'id' => $model->id,
                        'eid' => $model->eid,
                        'content' => $model->content,
                        'groupid' => $model->groupid,
                        'courseid' => $model->courseid,
                        'amount' => $model->amount,
                        'files' => array(),
                        'status' => 0,
                    );
                    if ($expenseObj->type == 2) {
                        $crit = new CDbCriteria();
                        $crit->compare('t.courseid', $model->courseid);
                        $crit->compare('asaExpense.type', 2);
                        $crit->compare('asaExpense.status', array(0, 10, 20));
                        $crit->with = 'asaExpense';
                        $otherExpenses = AsaExpenseItem::model()->findAll($crit);
                    }
                    foreach ($otherExpenses as $otherExpense) {
                        if ($data['courseid'] == $otherExpense->courseid) {
                            $data['status'] += 1;
                        }
                    }
                    foreach(CJSON::decode($model->files) as $item){
                        $data['files'][] =  $this->createUrl('downloadsExpense', array('fileName'=>$item));
                    }
                    $this->addMessage('state', 'success');
                    $this->addMessage('data', $data);
                    $this->addMessage('message', Yii::t('message','success'));
                    $this->addMessage('callback', 'cbAddEalist');
                    $this->showMessage();
                }else{
                    $error = current($expenseObj->getErrors());
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', $error[0]);
                    $this->showMessage();
                }
            }else{
                $error = current($model->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', $error[0]);
                $this->showMessage();
            }
        }
    }

    //预览
    public function actionPrintReports()
    {
        $this->layout = '//layouts/print_mmx';

        $crit = new CDbCriteria;
        $crit->compare('status', AsaCourseGroup::STATUS_ACTIVE);
        $crit->compare('schoolid', $this->branchId);
        $groupObj = AsaCourseGroup::model()->findAll($crit);
        $group = array();
        $course = array();
        foreach($groupObj as $item){
            $group[$item->id] = $item->getName();
            foreach($item->course as $courseItem){
                $course[$item->id][$courseItem->id] = $courseItem->getTitle();
            }
        }
        $typeList = CommonUtils::LoadConfig('CfgASA');

        $bank = array();
        foreach($typeList['bank'] as $k=>$item){
            $bank[$k] = Yii::app()->language == "zh_cn" ? $item['cn']  : $item['en'] ;
        }
        $bank[0] = '其它';

        $expenesId = Yii::app()->request->getParam('expenesId', 0);
        $model = AsaExpense::model()->findByPk($expenesId);
        $this->printFW['title'] = $model->type == 1 ? '报销单' : '付款单';
        $userName = User::model()->findByPk($model->expense_uid);
        $schoolsName = $this->getAllBranch();
        $this->render('print', array(
            'model' => $model,
            'schoolsName' => $schoolsName,
            'group' => $group,
            'course' => $course,
            'userName' => $userName,
            'bank' => $bank,
        ));
    }

    public function actionExpenseFiles()
    {
        $file = CUploadedFile::getInstanceByName('upload_file');
        $msg = Yii::t('asa','没有附件选中');
        $saveName = "";
        if ($file) {
            if ($file->size > 10*1024*1024) {
                $msg = Yii::t('asa','文件过大');
            } else{
                $needType = array('jpg','jpeg','png','pdf');
                if (!in_array($file->getExtensionName(), $needType)) {
                    $msg = Yii::t('asa','此文件类型不允许上传');
                }else{
                    $filePath = Yii::app()->params['xoopsVarPath'] . '/asa/expense/';
                    if(!is_dir($filePath)){
                        mkdir($filePath,0777);
                    }
                    $ext = $file->getExtensionName();
                    $saveName = date("Ydm") . '_' . uniqid() . '.' . $ext;
                    if ($file->saveAs($filePath . $saveName)){
                        $msg = 'success';
                        $baseUrl = $this->createUrl('downloadsExpense', array('fileName'=>$saveName));
                    }else{
                        $msg = Yii::t('asa','文件上传失败');
                    }
                }
            }
        }
        echo CJSON::encode(array(
            'url' => $baseUrl,
            'saveName' => $saveName,
            'msg' => $msg,
        ));
    }

    public function actionDownloadsExpense()
    {

        ob_end_clean();
        $fileName = Yii::app()->request->getParam('fileName', "");
        $extension = substr(strrchr($fileName, '.'), 1);
        $file_dir = Yii::app()->params['xoopsVarPath'] . '/asa/expense/';

        $fileres = file_get_contents($file_dir . $fileName);
        if($fileres){
            if($extension == "pdf"){
                header('Content-type: application/pdf');
            }else{
                header('Content-type: image/jpeg');
            }
            echo $fileres;
        }else{
            $oss = CommonUtils::initOSS('private');
            $filePath = 'asa/expense/' . $fileName;
            $url = $oss->get_sign_url($filePath);
            Yii::app()->request->redirect($url);
        }

    }


    //删除报销凭证
    public function actionDelExpenes()
    {
        if (Yii::app()->request->isPostRequest) {
            $expenesId = intval(Yii::app()->request->getParam('expenesId',0));
            if($expenesId) {
                $expenesObj = AsaExpense::model()->findByPk($expenesId);
                if(in_array($expenesObj->status, array(AsaExpense::STATS_DRAFT, AsaExpense::STATS_REFUSAL))){
                    $expenesObj->status = 99;
                    if ($expenesObj->save()) {
                        $crit = new CDbCriteria;
                        $crit->compare('expense_id', $expenesObj->id);
                        $varationModel = AsaMonthReportVariation::model()->findAll($crit);
                        if($varationModel){
                            foreach ($varationModel as $val) {
                                $val->expense_id = 0;
                                $val->save();
                            }
                        }
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', Yii::t('message', 'success'));
                        $this->addMessage('callback', 'cbExpenesList');
                        $this->showMessage();
                    } else {
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message', '参数错误'));
                        $this->showMessage();
                    }
                }else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message','报销已经确定, 删除请联系管理人员'));
                    $this->showMessage();
                }
            }else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message','参数错误'));
                $this->showMessage();
            }
        }
    }

    //删除Item
    public function actionDelExpenesItem()
    {
        $expenesItemId = intval(Yii::app()->request->getParam('expenesItemId', 0));
        $filePath = Yii::app()->params['xoopsVarPath'] . '/asa/expense/';
        if($expenesItemId){
            $expenseItemObj = AsaExpenseItem::model()->findByPk($expenesItemId);
            if($expenseItemObj){
                $expenseObj = AsaExpense::model()->findByPk($expenseItemObj->eid);
                $filesArr = CJSON::decode($expenseItemObj->files);
                $amount = $expenseItemObj->amount;
                if($expenseItemObj->delete()){
                    if($filesArr){
                        foreach($filesArr as $item){
                            unlink($filePath . $item);
                        }
                    }
                    $expenseObj->amount = $expenseObj->amount - $amount;
                    $expenseObj->save();
                    $this->addMessage('state', 'success');
                    $this->addMessage('data', $expenseItemObj->id);
                    $this->addMessage('message', Yii::t('message', 'success'));
                    $this->addMessage('callback', 'cbDelEaList');
                    $this->showMessage();
                }
            }
        }else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message','参数错误'));
            $this->showMessage();
        }

    }

    //删除单个图片
    public function actionDelFiles()
    {
        if (Yii::app()->request->isPostRequest){
            $fileName = Yii::app()->request->getParam('fileName', '');
            $filePath = Yii::app()->params['xoopsVarPath'] . '/asa/expense/';
            unlink($filePath . $fileName);
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message','success'));
            $this->showMessage();

        }
    }

    //是否提交
    public function actionExpenesStatusUpdate()
    {
        if (Yii::app()->request->isPostRequest){
            $id = Yii::app()->request->getParam('id', '');
            $status = Yii::app()->request->getParam('status', '');
            $expenseObj = AsaExpense::model()->findByPk($id);
            if($expenseObj){
                $expenseObj->status = $status;
                if($expenseObj->save()){
                    $data = array(
                        'state' => 'success',
                        'message' => '成功',
                    );
                }else{
                    $error = current($expenseObj->getErrors());
                    echo $data = array(
                        'state' => 'fail',
                        'message' => $error,
                    );
                }
                echo CJSON::encode($data);
            }else{
                $data = array(
                    'state' => 'fail',
                    'message' => '没有找到数据',
                );
                echo CJSON::encode($data);
            }
        }else{
            $data = array(
                'state' => 'fail',
                'message' => '参数错误',
            );
            echo CJSON::encode($data);
        }
    }

    public function getButton($data)
    {
        if ($this->checkAsaAccess()) {
            $present = '查看';
            if($data->status == AsaExpense::STATS_REFER){
                $present = '审核';
            }
            echo CHtml::link($present, array('/mfinance/asa/showExpense', 'expenseId' => $data->id), array('class' => 'J_modal btn btn-xs btn-info'));
        } else {
            if(in_array($data->status, array(AsaExpense::STATS_DRAFT, AsaExpense::STATS_REFUSAL) )){
                echo '<a title="提交审核"  class="btn btn-warning btn-xs" href="javascript:;" onclick="addExpense('.$data->id.',10)"><span class="glyphicon glyphicon-unchecked"></span></a> ';
            }else if($data->status == AsaExpense::STATS_REFER){
                echo '<a title="取消提交" class="btn btn-default  btn-xs" href="javascript:;" onclick="addExpense('.$data->id.',0)"><span class="glyphicon glyphicon-check"></span></a> ';
            }else if($data->status != AsaExpense::STATS_INVALID){
                echo '<a title="审核通过" class="btn btn-success btn-xs" href="javascript:;"><span class="glyphicon glyphicon-record"></span></a> ';
            }
            if(in_array($data->status, array(AsaExpense::STATS_DRAFT,  AsaExpense::STATS_REFUSAL))){
                echo '<a class="J_modal btn btn-primary btn-xs" title="编辑" href="'.$this->createUrl("expenesAdd", array("expenesId"=>$data->id, 'type' => $data->type)).'"><span class="glyphicon glyphicon-pencil"></span></a> ';
            }else{
                echo '<a class="J_modal btn btn-primary btn-xs" title="编辑" ><span class="glyphicon glyphicon-ban-circle"></span></a> ';
            }
            echo '<a class="btn btn-info btn-xs"  target = "_blank" title="打印"  href="'.$this->createUrl("printReports", array("expenesId"=>$data->id, 'type' => $data->type)).'"><span class="glyphicon glyphicon-print"></span></a> ';
            echo '<a class="J_ajax_del btn btn-danger btn-xs" title="删除"  href="'.$this->createUrl("delExpenes", array("expenesId"=>$data->id)).'"><span class="glyphicon glyphicon-remove"></span></a>';
        }
    }

    /**
	 * 判断用户在指定校园是否拥有指定类型的权限
	 *
	 * @param [string] $schoolid
	 * @param [int] $uid
	 * @param [array] $type
	 * @return bool
	 */
    public function checkAsaAccess()
    {
        $type = array(AdmBranchLink::ADM_TYPE_ASA, AdmBranchLink::ADM_TYPE_CD);
        $crit = new CDbCriteria();
        $crit->compare('schoolid', $this->branchId);
        $crit->compare('uid', $this->staff->uid);
        $crit->compare('type', 'asa');
		$exist = AdmBranchLink::model()->exists($crit);
		return $exist;
    }

    public function getStatus($data)
    {
        $status = AsaExpense::getConfig();
        $html = "<span class='label label-default'>" . $status[$data->status] . "</span>";
        if($data->status == AsaExpense::STATS_REFER){
            $html = "<span class='label label-info'>" . $status[$data->status] . "</span>";
        }
        if($data->status == AsaExpense::STATS_PAST){
            $html = "<span class='label label-success'>" . $status[$data->status] . "</span>";
        }
        if($data->status == AsaExpense::STATS_REFUSAL){
            $html =  "<span class='label label-warning'>" . $status[$data->status] . "</span>";
        }
        if($data->status == AsaExpense::STATS_INVALID){
            $html =  "<span class='label label-danger'>" . $status[$data->status] . "</span>";
        }

        echo "<div class='mb5'>" . $html . "</div><span>" . $data->memo . "</span>";
    }

    public function getUsersName($data)
    {
        echo $this->usersName[$data->expense_uid];
    }

    public function loadModel($id)
    {
        $model = AsaExpense::model()->findByPk($id);
        if($model===null) {
            $model = new AsaExpense;
            $model->created = time();
        }
        return $model;
    }

}