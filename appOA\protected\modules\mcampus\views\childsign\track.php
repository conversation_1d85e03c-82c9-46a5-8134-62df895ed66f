<style>
	 [v-cloak] {
        display: none;
    }
    ul, li {
				/*做时间轴的线*/
				margin: 0;
				padding: 0;
			}
			
			.layui-timeline {
            /* padding-left: 21%; */
            padding-top: 20px;
            /* width: 600px; */
            margin-left:10px
			}
			
			.layui-timeline-item {
				position: relative;
				padding-bottom: 20px;
				margin-top: -12px;
			}
			
			li {
				list-style: none;
			}
			
			.layui-timeline-item:first-child::before {
				display: block;
			}
			
			.layui-timeline-item:last-child::before {
				content: '';
				position: absolute;
				left: 5px;
				top: 0;
				z-index: 0;
				width: 0;
			}
			
			.layui-timeline-item::before {
				content: '';
				position: absolute;
				left: -1px;
				top: 0;
				z-index: 0;
				width: 1.5px;
				height: 100%;
			}
			
			.layui-timeline-item::before,
			hr {
				background-color: #e6e6e6;
			}
			
			.layui-timeline-axis {
				position: absolute;
				left: -6px;
				top: 0;
				z-index: 10;
				width:13px;
				height:13px;
				line-height:30px;
				background-color: #fff;
				color: #5FB878;
				border-radius: 50%;
				text-align: center;
				cursor: pointer;
			}
			
			/* .layui-icon {
				font-size: 16px;
				font-style: normal;
			}
			 */
			.layui-timeline-content {
				padding-left: 25px;
			}
			
			.layui-text {
				line-height: 22px;
				font-size: 14px;
				color: #666;
				top: -10px;
			}
			.layui-text p{
            margin-bottom:30px;
            color:#666
         }
         .layui-text p i{
            color:#999;
            padding-left:10px
         }
			.layui-timeline-title {
				position: relative;
				margin-bottom: 10px;
            font-size:18px;
            color:#333;
            margin-top:0
			}
			.layui-timeline-title p i{
            text-align:center
         }
			.circle {
				/* width: 10px;
				height: 10px;
				border-radius: 50%; */
				border: 1.5px solid #569fff;
			}
			
			.etime-first {
				top: 45%;
				left: -60px;
				position: absolute;
			}
			.time-first {
				position: absolute;
				left: -100px;
			}
			.dates {
            position: absolute;
            left: -180px;
            top: 0;
            text-align: right;
            width: 170px;
			}
			p {
				margin: 0
			}
         .clear{
            clear:both
         }
         .information p{
            line-height:30px
         }
         h4{
            margin-top:0
         }
         .checkbox span{
            font-size:14px
         }
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('default/index')); ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Routines'), array('//mcampus/default/index')) ?></li>
        <?php if(Yii::app()->params['siteFlag'] == 'daystar'){?>
            <li><?php echo CHtml::link(Yii::t('site', 'Student Attendance Management'), array('//mcampus/childsign/index2')) ?></li>
        <?php }else{?>
            <li><?php echo CHtml::link(Yii::t('site', 'Student Attendance Management'), array('//mcampus/childsign/index')) ?></li>
        <?php }?>
        <li class="active"><?php echo Yii::t('campus', 'Student Attendance'); ?></li>
    </ol>
    <div class="row">
        <?php
        $this->renderPartial('/childsign/left');
        ?>
        <div class="col-md-10">
            <div class="form-inline mb15">
               <input type="text" class="form-control form-group" id="datepicker" placeholder="请选择日期"> 
               <button type="button" class="btn btn-primary btn-sm ml15" id="search" onclick='search()'>搜索</button>
               <button type="button" class="btn btn-primary btn-sm pull-right" onclick="chooseTimeView()"> 导出 </button>
            </div>
            <div id='container' v-cloak>
               <template v-if='searchData==1'>
                  <table class='table table-hover' v-if='dataList!=""'>
                     <thead>
                        <tr>
                           <th style="width:10%"   class="nosort">学生</th>
                           <th style="width:14%" >班级</th>
                           <th style="width:10%"   class="nosort">开始时间</th>
                           <th style="width:10%"   class="nosort">结束时间</th>
                           <th  class="nosort">病假原因</th>
                           <th style="width:6%"   class="nosort">教师追踪</th>
                           <th style="width:6%"   class="nosort">保健医追踪</th>
                           <th style="width:6%"   class="nosort">操作</th>
                        </tr>
                     </thead>
                     <tbody>
                           <tr v-for='(key,index,data) in dataList'>
                              <td>{{data.childname}}</td>
                              <td>{{data.classname}}</td>
                              <td>{{data.vacation_time_start}}</td>
                              <td>{{data.vacation_time_end}}</td>
                              <td>{{data.vacation_reason}}</td>
                              <td>
                                 <template v-if='data.teacherTrack==1'>
                                    <span class="label label-success">已追踪</span>
                                 </template>
                                 <template v-else>
                                    <span class="label label-danger">未追踪</span>
                                 </template>
                              </td>
                              <td>
                                 <template v-if='data.healthTrack==1'>
                                    <span class="label label-success">已追踪</span>
                                 </template>
                                 <template v-else>
                                    <span class="label label-danger">未追踪</span>
                                 </template>
                              </td>
                              <td>
                                 <button type="button" class="btn btn-primary btn-xs" @click='editTarck(data)'>追踪记录</button>
                                 <!-- <button type="button" class="btn btn-danger btn-sm">删除</button> -->
                              </td>
                           </tr>
                     </tbody>
                  </table> 
                  <div v-else>
                     <div class="alert alert-warning" role="alert">暂无数据</div>
                  </div>
               </template>
               <div class="modal fade bs-example-modal-lg" data-backdrop="static" id="trackModel" tabindex="-1" role="dialog">
                  <div class="modal-dialog modal-lg" >
                     <div class="modal-content">
                        <div class="modal-header">
                           <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                           &times;
                           </button>
                           <h4 class="modal-title" id="myModalLabel">
                           查看
                           </h4>
                        </div>
                        <div class="modal-body">
                           <div class='col-md-4 information'>
                              <h4><strong>学生信息</strong>  </h4>
                              <p>姓名：{{trackData.childname}}</p>
                              <p>班级：{{trackData.classname}}</p>
                              <p>开始时间：{{trackData.vacation_time_start}}</p>
                              <p>结束时间：{{trackData.vacation_time_end}}</p>
                              <p>请假原因：{{trackData.vacation_reason}}</p>
                              <p>父亲姓名：{{trackData.fname}}</p>
                              <p>父亲电话：{{trackData.fmphone}}</p>
                              <p>父亲邮件：{{trackData.femail}}</p>
                              <p>母亲姓名：{{trackData.mname}}</p>
                              <p>母亲电话：{{trackData.mmphone}}</p>
                              <p>母亲邮件：{{trackData.memail}}</p>
                           </div>
                           <div class='col-md-8'>
                           <h4><strong>追踪记录</strong> </h4>
                           <ul class="layui-timeline" v-if='trackData.track_data!=""'>
                              <li class="layui-timeline-item relative" v-for='(key,index,list) in trackData.track_data'>
                                 <!-- <div class='dates'>
                                    <p><strong>{{list.track_at}}</strong> </p>
                                 </div> -->
                                 <i class="layui-icon layui-timeline-axis circle"></i>
                                 <div class="layui-timeline-content layui-text">
                                    <h3 class="layui-timeline-title">{{list.track_at}} </h3>
                                    <p>
                                      <span v-html='list.track_memo'></span>
                                       <i class='text-right'>——{{list.track_user}}</i>
                                    
                                    </p>
                                 </div>
                              </li>
                              <li class="layui-timeline-item">
                                 <i class="layui-icon layui-timeline-axis circle"></i>
                                 <div class="layui-timeline-content layui-text">
                                    <div class="layui-timeline-title">                          
                                       <!-- <button type="button" class="btn btn-primary" v-if='isAdd' @click='isAdd=false'>继续追踪</button> -->
                                       <textarea class="form-control" rows="3" v-model='addTrack' placeholder='请输入追踪记录' ></textarea>
                                       <form class="form-horizontal">
                                             <div class="checkbox">
                                                <label>
                                                   <input type="checkbox" v-model='Leave' value='1'><span>转为事假</span>  
                                                </label>
                                             </div>
                                          </form>
                                    </div>
                                 </div>
                              </li>
                           </ul>
                           <div v-else>
                              <ul class="layui-timeline">
                                 <li class="layui-timeline-item">
                                    <i class="layui-icon layui-timeline-axis circle"></i>
                                    <div class="layui-timeline-content layui-text">
                                       <div class="layui-timeline-title">                          
                                          <!-- <button type="button" class="btn btn-primary" v-if='isAdd' @click='isAdd=false'>继续追踪</button> -->
                                          <textarea class="form-control" rows="3" v-model='addTrack' placeholder='请输入追踪记录' ></textarea>
                                          <form class="form-horizontal">
                                             <div class="checkbox">
                                                <label>
                                                   <input type="checkbox" v-model='Leave' value='1'><span>转为事假</span>  
                                                </label>
                                             </div>
                                          </form>
                                       </div>
                                    </div>
                                 </li>
                              </ul>
                              <div style='clear:both'></div>
                           </div>
                           </div>
                           <div class='clear'></div>
                        </div>
                        <div class="modal-footer">
                              <button type="button" class="btn btn-primary" @click='addConfirm'>确认</button>
                              <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                        </div>
                     </div>
                  </div>
               </div>
               <div class="modal fade bs-example-modal-lg" data-backdrop="static" id="exportDataTime" tabindex="-1" role="dialog">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                                <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("user", "Export");?></h4>
                            </div>
                            <div class="modal-body">
                                <div class="alert alert-warning" role="alert">请选择导出时间段，不可跨学年</div>
                                <div class="row">
                                    <div class="col-md-6 form-inline mb15">
                                        <span>开始时间：</span><input type="text" class="form-control form-group" id="export_start_time" placeholder="请选择日期">
                                    </div>
                                    <div class="col-md-6 form-inline mb15">
                                        <span>结束时间：</span><input type="text" class="form-control form-group" id="export_end_time" placeholder="请选择日期">
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-primary export" @click='exportData' :disabled="export_disabled"><?php echo Yii::t("user", "Export");?></button>
                                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<script>
      $(function() {
         var date = new Date();
         var year = date.getFullYear();
         var month = date.getMonth() + 1;
         var strDate = date.getDate();
         if (month >= 1 && month <= 9) {
               month = "0" + month;
         }
         if (strDate >= 0 && strDate <= 9) {
               strDate = "0" + strDate;
         }
         var currentdate = year + "-" + month + "-" + strDate;
         $('#datepicker').val(currentdate)
         $( "#datepicker" ).datepicker({
            dateFormat: "yy-mm-dd",
         });
         $("#export_start_time").datepicker({
             dateFormat: "yy-mm-dd",
         });
          $("#export_end_time").datepicker({
              dateFormat: "yy-mm-dd",
          });
          search()
      });
      $(document).ready(function() {
         
      });
      function search(){
         if($('#datepicker').val()==''){
            resultTip({
               error: 'warning',
               msg: '请选择日期'
            });
            return
         }
         $('#search').addClass('disabled').attr('disabled', 'disabled')
		   $('#search').html('查询中...')
         $.ajax({
					url: '<?php echo $this->createUrl("trackData")?>',
					type: "post",
					dataType: 'json',
					data: {
                  vacation_day:$('#datepicker').val()
               },
					success: function(res) {
                  $('#search').removeClass('disabled').removeAttr('disabled');
		            $('#search').html('搜索')
						if(res.state=="success"){
						   container.dataList=res.data
                     container.searchData=1
                     if(res.data.length!=0){
                        container.tableWat=1
                     }else{
						      container.tableWat=0
                     }
						}else{
                     resultTip({
                        error: 'warning',
                        msg: res.message
                     });
						}
					},
					error: function() {
						$("#subbtn").attr('disabled',true);
						alert("请求错误")
					}
				});
      }
      function chooseTimeView(){
          $("#exportDataTime").modal('show')
      }
      var container = new Vue({
         el: "#container",
         data: {
            dataList:'',
            trackData:'',
            isAdd:true,
            addTrack:'',
            searchData:0,
             Leave:'',
            tableWat:0,
            export_disabled:false,
         },
         watch: {
            tableWat: function () {
               this.$nextTick(function () {
                  this.tableData()
               })
            }
         },
         updated: function () {
            
         },
         methods: {
            tableData(){
               var table = $('.table').DataTable({
                  aaSorting: [1, 'desc'], // 默认排序
                  paging: false,
                  info: false,
                  searching: false,
                  columnDefs: [{
                        "targets": 'nosort',
                        "orderable": false
                  }],
               });
            },
            editTarck(data){
               this.trackData=data
               this.isAdd=true
               this.addTrack=''
               this.Leave=''
               $('#trackModel').modal('show')
            },
            addConfirm(){
               let that=this
               if(that.addTrack==''){
                  resultTip({
                     error: 'warning',
                     msg: '请填写追踪记录'
                  });
                  return
               }
               var data={}
               if(that.Leave==''){
                  data={
                     vid:that.trackData.vid,
                     memo:that.addTrack,
                  }
               }else{
                  data={
                     vid:that.trackData.vid,
                     memo:that.addTrack,
                     change_type:that.Leave
                  }
               }
               $.ajax({
                  url: '<?php echo $this->createUrl("addTrack")?>',
                  type: "post",
                  dataType: 'json',
                  data:data,
                  success: function(res) {
                     if(res.state=="success"){
                        search()
                        resultTip({
                           msg: res.message
                        });
                        $('#trackModel').modal('hide')
                     }else{
                        resultTip({
                           error: 'warning',
                           msg: res.message
                        });
                     }
                  },
                  error: function() {
                     $("#subbtn").attr('disabled',true);
                     alert("请求错误")
                  }
               });
            },
             exportData(){
                let that = this;
                 if($('#export_start_time').val()=='' || $('#export_end_time').val()==''){
                     resultTip({
                         error: 'warning',
                         msg: '请选择日期'
                     });
                     return
                 }
                 if($('#export_start_time').val() > $('#export_end_time').val()){
                     resultTip({
                         error: 'warning',
                         msg: '请正确选择日期'
                     });
                     return
                 }
                 that.export_disabled = true;
                 $.ajax({
                     url: '<?php echo $this->createUrl("trackData")?>',
                     type: "post",
                     dataType: 'json',
                     data:{
                         start_day:$('#export_start_time').val(),
                         end_day:$('#export_end_time').val(),
                         is_export:1
                     },
                     success: function(res) {
                         if(res.state=="success"){
                             if(res.data.length <= 0 ){
                                 resultTip({
                                     error: 'warning',
                                     msg: '暂无数据'
                                 });
                                 that.export_disabled = false;
                                 return ;
                             }
                             const filename = "病假追踪"+$('#export_start_time').val()+'至'+$('#export_end_time').val()+".xlsx"
                             const ws_name = "SheetJS";
                             const exportDatas = [];
                             for(var i=0;i<res.data.length;i++){
                                 let track_data = ''
                                 for (let j=0;j<res.data[i]['track_data'].length;j++){
                                     track_data += res.data[i]['track_data'][j]['track_at'] +' -- '+ res.data[i]['track_data'][j]['track_memo'] +' -- '+ res.data[i]['track_data'][j]['track_user']+'\n'
                                 }
                                 track_data = track_data.substring(0,track_data.length-1)
                                 exportDatas.push(
                                     {
                                         "学生":res.data[i].childname,
                                         "班级":res.data[i].classname,
                                         "开始时间":res.data[i].vacation_time_start,
                                         "结束时间":res.data[i].vacation_time_end,
                                         "病假原因":res.data[i].vacation_reason,
                                         "教师追踪":res.data[i].teacherTrack == 1 ? '已追踪' : '未追踪',
                                         "保健医追踪":res.data[i].healthTrack == 1 ? '已追踪' : '未追踪',
                                         "追踪记录":track_data
                                     });
                             }
                             var worksheet = XLSX.utils.json_to_sheet(exportDatas,{
                                 origin:'A1',// 从A1开始增加内容
                                 header: [
                                     '学生',
                                     '班级',
                                     '开始时间',
                                     '结束时间',
                                     '病假原因',
                                     '教师追踪',
                                     '保健医追踪',
                                     '追踪记录'
                                 ],
                             });

                             for (let key in worksheet){
                                 worksheet[key]['s'] = {
                                     font: {
                                         sz: 10,
                                         bold: false,//设置标题是否加粗
                                     },
                                     alignment: {
                                         horizontal: 'center',
                                         vertical: 'center',
                                         wrapText: true
                                     },//设置标题水平竖直方向居中，并自动换行展示
                                     fill: {
                                         fgColor: { rgb: 'ebebeb' }//设置标题单元格的背景颜色
                                     },
                                     border: {//添加边框
                                         bottom: {
                                             style: 'thin',
                                             color: '000000'
                                         },
                                         left: {
                                             style: 'thin',
                                             color: '000000'
                                         },
                                         right: {
                                             style: 'thin',
                                             color: '000000'
                                         },
                                         top: {
                                             style: 'thin',
                                             color: '000000'
                                         }
                                     }
                                 };
                             }
                             worksheet['!cols'] = [{wpx:100},{wpx:120},{wpx:100},{wpx:100},{wpx:200},{wpx:100},{wpx:100},{wpx:300}];
                             openDownload(sheet2blob(worksheet,ws_name), filename);
                             that.export_disabled = false;
                         }else{
                             resultTip({
                                 error: 'warning',
                                 msg: res.message
                             });
                             that.export_disabled = false;
                         }
                     },
                     error: function() {
                         that.export_disabled = false;
                         alert("请求错误")
                     }
                 });

             }
         }
      })
</script>
    <!-- 显示底部学校选择栏 -->
<?php $this->renderPartial('//layouts/common/branchSelectBottom'); ?>


  
    