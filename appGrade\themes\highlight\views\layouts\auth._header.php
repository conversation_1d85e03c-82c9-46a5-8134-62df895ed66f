<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta name="language" content="en" />

	<!-- blueprint CSS framework -->
	<link rel="stylesheet" type="text/css" href="<?php echo Yii::app()->theme->baseUrl; ?>/css/screen.css" media="screen, projection" />
	<!--[if lt IE 8]>
	<link rel="stylesheet" type="text/css" href="<?php echo Yii::app()->themeManager->baseUrl; ?>/base/css/ie.css" media="screen, projection" />
	<![endif]-->
	<link rel="stylesheet" type="text/css" href="<?php echo Yii::app()->theme->baseUrl; ?>/css/form.css" />

	<link rel="stylesheet" type="text/css" href="<?php echo Yii::app()->theme->baseUrl; ?>/css/auth.css?t=<?php echo Yii::app()->params['refreshAssets'];?>" />
	<link rel="stylesheet" type="text/css" href="<?php echo Yii::app()->theme->baseUrl; ?>/css/main.css" />
	<link rel="stylesheet" type="text/css" href="<?php echo Yii::app()->theme->baseUrl; ?>/css/<?php echo Yii::app()->getLanguage()?>.css" />	
	<link rel="SHORTCUT ICON" href="/favicon.ico" />
	
	<script type="text/javascript" src="<?php echo Yii::app()->themeManager->baseUrl; ?>/base/js/callback.js"></script>
	<?php
	if(Yii::app()->params['baiduStats'])
		$this->renderPartial('//statsScripts/baiduStats_head');
	?>
	
	<title><?php echo CHtml::encode($this->pageTitle); ?></title>
</head>

<body>
<div id="topbar">
	
</div>

<div class="container show-grid" id="page">

	<div id="header">
		<div id="logo" class="span-8">
			<?php echo CHtml::link(CHtml::encode(Yii::app()->name), Yii::app()->urlManager->baseUrl, array(
                "class"=>"logo-anonymous",
                "style"=>Mims::unIvy()?'background-image:url("'.Yii::app()->params['ownerConfig']['logo'].'");height:73px;':'',
            )); ?>
		</div>
		<div class="span-16 last links">
			<div class="links">
				<?php if(Yii::app()->user->getId()):?>
				<span>
					<?php echo Yii::t("user",'Welcome back, :username', array(":username"=>Yii::app()->user->name));?>
				</span>
	
				<span>
					<?php echo CHtml::link(Yii::t("user",'Logout'), $this->createUrl("//user/logout"));?>
				</span>
				
							
				<?php endif; ?>
				<span> <?php echo CHtml::ajaxLink(Yii::t("global", '中文' ),
												$this->createUrl("//site/lang",
																	  array("lang"=>"zh_cn")
																),
												array(
													  "dataType"=>"json",
													  "success"=>"function(data, textStatus, jqXHR){callback(data,'reload')}",
												))
				?> </span>
				<span> <?php echo CHtml::ajaxLink(Yii::t("global", 'English' ),
												$this->createUrl("//site/lang",
																	  array("lang"=>"en_us")
																),
												array(
													  "dataType"=>"json",
													  "success"=>"function(data, textStatus, jqXHR){callback(data,'reload')}",
												))
				?> </span>
			</div>
		</div>
	</div><!-- header -->
    
    <?php if (!isset(Yii::app()->request->cookies['child_mims_lang'])):?>
		<?php $colorbox = $this->widget('application.extensions.colorbox.JColorBox');?>
		<div id="select-lang-fake-box" style="display: none;">
			<?php //$plang = Yii::app()->request->preferredLanguage?>
			<?php $plang = Mims::getRequestLang();?>
			<?php $this->renderPartial('//lang/lang', array('plang'=>$plang));?>
		</div>
		<script>
		$(document).ready(function(){
			var html = $("#select-lang-fake-box").html();
			$.colorbox({html: html, opacity: '0.2','iframe':false, 'width':'500', 'height':'290','top':'170'});
			$.get('<?php echo $this->createUrl("//site/lang", array("lang" => $plang));?>');
		});
		</script>
    <?php endif; ?>
