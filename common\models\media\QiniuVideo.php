<?php

/**
 * This is the model class for table "ivy_child_media_qiniu_video".
 *
 * The followings are the available columns in table 'ivy_child_media_qiniu_video':
 * @property string $inputKey
 * @property string $startyear
 * @property string $itemids
 * @property integer $created
 * @property string $notifyData
 * @property integer $notified
 * @property integer $server_id
 * @property integer $notify_done
 */
class QiniuVideo extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_child_media_qiniu_video';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('inputKey, startyear', 'required'),
			array('created, notified, server_id, notify_done', 'numerical', 'integerOnly'=>true),
			array('inputKey', 'length', 'max'=>255),
			array('startyear', 'length', 'max'=>64),
			array('itemids, notifyData', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('inputKey, startyear, itemids, created, notifyData, notified, server_id, notify_done', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'inputKey' => 'Input Key',
			'startyear' => 'Startyear',
			'itemids' => 'Itemids',
			'created' => 'Created',
			'notifyData' => 'Notify Data',
			'notified' => 'Notified',
			'server_id' => 'Server',
			'notify_done' => 'Notify Done',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('inputKey',$this->inputKey,true);
		$criteria->compare('startyear',$this->startyear,true);
		$criteria->compare('itemids',$this->itemids,true);
		$criteria->compare('created',$this->created);
		$criteria->compare('notifyData',$this->notifyData,true);
		$criteria->compare('notified',$this->notified);
		$criteria->compare('server_id',$this->server_id);
		$criteria->compare('notify_done',$this->notify_done);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return QiniuVideo the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
