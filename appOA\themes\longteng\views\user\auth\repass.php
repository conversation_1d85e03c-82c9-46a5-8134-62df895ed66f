<div class="container">
    <div class="row vertical-offset-100">
        <div class="col-md-6 col-md-offset-3">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title"><span class="glyphicon glyphicon-lock"></span> <?php echo Yii::t('user','Change Password');?></h3>
                </div>
                <div class="panel-body">

                    <?php if($validLink): ?>
                        <h4 class="text-muted">
                            <span class="glyphicon glyphicon-user"></span> <?php echo $email; ?>
                        </h4>

                        <?php if ( Yii::app()->user->hasFlash('loginMessage')): ?>

                            <div class="well bg-warning">
                                <span class="text-danger">
                                <?php echo Yii::app()->user->getFlash('loginMessage');?>
                                </span>
                            </div>

                        <?php endif; ?>

                        <div class="form">
                            <?php echo CHtml::beginForm(); ?>
                                <fieldset>
                                    <div class="form-group">
                                        <?php echo CHtml::activePasswordField($model, 'pass', array('class' => 'form-control', "placeholder"=>$model->getAttributeLabel('pass'))) ?>
                                    </div>
                                    <div class="form-group">
                                        <?php echo CHtml::activePasswordField($model, 'repass', array('class' => 'form-control', "placeholder"=>$model->getAttributeLabel('repass'))) ?>
                                    </div>
                                    <div class="form-group">
                                        <button type="button" id="J_submit" class="btn btn-lg btn-success btn-block" disabled><?php echo Yii::t("user", "Change Password") ?></button>
                                    </div>

                                </fieldset>
                            <?php echo CHtml::endForm(); ?>
                        </div><!-- form -->
                    <?php else: ?>
                        <div class="alert alert-danger p20" role="alert">
                            <span class="glyphicon glyphicon-exclamation-sign" aria-hidden="true"></span>
                            <span class="sr-only">Error:</span>
                            <?php echo Yii::t('message','Invalid Link');?>
                        </div>
                    <?php endif;; ?>
                </div>
<!--                 <div class="panel-footer">
                    <div class="text-right">
                        <img src="<?php echo Yii::app()->theme->baseUrl.'/images/logo_ig_small.png'?>">
                        an <a href="http://www.ivygroup.cn" target="_blank">IvyGroup</a> site
                    </div>
                </div> -->
            </div>
        </div>
    </div>
</div>

<style>
    body{
        background: url('<?php echo Yii::app()->theme->baseUrl;?>/images/ds_big_<?php echo rand(1,4);?>.jpg');
        background-size: cover;
    }
</style>


<script>
    var submitBtn = $('#J_submit');
    submitBtn.removeAttr('disabled');
    submitBtn.click(function(){
        var pass = $('#UserRepass_pass').val();
        var newpass = $('#UserRepass_repass').val();
        if(!pass){
            resultTip({error: 'warning', msg: '<?php echo Yii::t('message', 'Input new password, min 8 with numbers and characters.')?>'});
        }
        else if(!newpass){
            resultTip({error: 'warning', msg: '<?php echo Yii::t('message', 'Repeat your password')?>'});
        }
        else if(pass != newpass){
            resultTip({error: 'warning', msg: '<?php echo Yii::t('message', 'Passwords not match, please correct.')?>'});
        }
        else{
            var text = submitBtn.text();
            submitBtn.text(text+'...').attr('disabled', true);
            head.load(GV.themeManagerBaseUrl+'/base/js/util_libs/ajaxForm.js',function() {
                var form = submitBtn.parents('form');
                form.ajaxSubmit({
                    dataType	: 'json',
                    success     : function(data){
                        submitBtn.text(text).attr('disabled', false);
                        if(data.state == 'success'){
                            var opts = {msg: data.message, callback: function(){
                                window.location.href = '<?php echo Yii::app()->urlManager->baseUrl;?>';
                            }};
                        }
                        else{
                            var opts = {error: 1, msg: data.message};
                        }
                        resultTip(opts);
                    }
                });
            });
        }
    });
    $('#UserRepass_pass, #UserRepass_repass').keydown(function(e){
        if(e.keyCode==13){
            submitBtn.click();
            return false;
        }
    });
</script>