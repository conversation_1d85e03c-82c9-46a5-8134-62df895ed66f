<?php
if(!defined("IVY_POLICY"))
throw new CException('DO NOT USE THIS FILE DIRECTLY');

return array( 'policy'=>array(

	//是否老生老办法
	ENABLE_CONSTANT_TUITION => true,

	//年保育费
	//ANNUAL_CHILDCARE_AMOUNT => 8000,

	//是否开放课外活动账单
	ENABLE_AFTERSCHOOLS => true,

	//图书馆工本费
	FEETYPE_LIBCARD => 10,

	//学期四个时间点；取前后两端
	TIME_POINTS   => array(20209,202102,202103,202108),

	//学期付开通哪些缴费类型
	PAYGROUP_SEMESTER => array(
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
			FEETYPE_SCHOOLBUS,
		),
		TUITION_CALC_BASE => BY_MONTH,
	),
//
	//月份开通哪些缴费类型
	PAYGROUP_MONTH => array(
	    ITEMS => array(
           // FEETYPE_TUITION,
	       FEETYPE_LUNCH,
           // FEETYPE_SCHOOLBUS,
	    ),
	    TUITION_CALC_BASE => BY_MONTH,
	),

	//计算基准：BY_MONTH, 或者 BY_SETTING, 前者表示所有的学费都基于月费用计算，后者表示所有的学费都基于学期或学年
	TUITION_CALCULATION_BASE => array(

		BY_MONTH => array(

			//月收费金额
			AMOUNT => array(
				FULL5D => 7000,
                PROGRAM_MIK => 5300,
                HALF5D => 2650,
			),

			//收费月份，及其权重
			MONTH_FACTOR => array(
				202009=>1,
				202010=>1,
				202011=>1,
				202012=>1,
				202101=>1,
				202102=>.5,
				202103=>1,
				202104=>1,
				202105=>1,
				202106=>1,
				202107=>1,
				202108=>.5,
			),
		),

	),

	//每餐费用
	//LUNCH_PERDAY => 30,
    LUNCH_PERDAY => array(
        LUNCHA => 30,
        LUNCHB => 15,
    ),

	//账单到期日和账单开始日之差
	DUEDAYS_OFFSET => 1,

));