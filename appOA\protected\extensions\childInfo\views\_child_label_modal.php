<div class="bootstrap-iso">
    <div class="flag_div mb6">
        <div class="" v-if="Object.keys(flagList).length >0 ">
            <a href="javascript:;" class="mr10" @click="showFlag"><?php echo Yii::t('global','Tagging')?></a>
        </div>
        <div class="choose_flag_div" v-if="mainInfo['label']">
            <span v-for="(item,index) in Object.values(mainInfo['label'])" class="flag_span"
                  :title="item['desc']"
                  :style="'color:rgb('+item['color']+')'" >
                <span  v-html="item['name'] || ''"></span>
                <span v-html="item.flag_text || ''"></span>
                  
            </span>
        </div>
    </div>
    <div class="modal fade bs-example-modal-lg" id="childFlag" tabindex="-1" role="dialog"  aria-labelledby="childFlag" aria-hidden="true">
        <div class="modal-dialog modal-lg" >
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                    <h4 class="modal-title color3"><?php echo Yii::t('global','Setting tags for child')?></h4>
                </div>
                <div class="modal-body min-height-300 p24">
                    <div>
                        <p class="child_name">{{mainInfo.name}}</p>
                        <span class="flag_name">{{mainInfo.class_name}}</span>
                    </div>
                    <div class="mt28" v-if="changeable.length>0">
                        <div class="mb10" style="margin-right:20px;font-size: 14px" >
                            <span class="lh30"><?php echo Yii::t('global','Changeable Tags')?></span>
                        </div>
                        <div class="choose_flag_div">
                            <span v-for="(item,index) in flagList"
                                  :key="index"
                                  :ref="`flag_${item.flag}`"
                                  v-if="item['editable_by_staff'] != 'false' && item.is_dynamic===0"
                                  @click="chooseFlag(item.flag,'flag')"
                                  class="label-no-select"
                                  :title="item['desc']"
                                  :style="active_style[item.flag]">
                                  <span class='flagSselected' :style="'background:rgb('+item.color+')'" >✓</span>
                                  <span :style="'color:rgb('+item.color+')'"   class='text'>
                                    <span  v-html="item['name'] || ''"></span>
                                    <span v-html="item.flag_text || ''"></span>
                                  </span>
                            </span>
                        </div>
                    </div>
                    <!--  动态标签 -->
                    <div class="mt28" v-if="Object.keys(labelList).length >0">
                        <div class="mb10" style="margin-right:20px;font-size: 14px" >
                            <span class="lh30"><?php echo Yii::t('global','Dynamic Tags')?></span>
                        </div>
                        <div class="">
                            <div v-for="(item,key,index) in labelList" :key="index" >
                                <div  class='flex item_center' >
                                    <div style='width:200px'>
                                        <span
                                        :ref="`flag_${item.flag}`"                                    
                                        @click="chooseFlag(item.flag,'label')"
                                        class="label-no-select"
                                        :title="item['desc']"
                                        :style="active_style[item.flag]">
                                            <span class='flagSselected' :style="'background:rgb('+item.color+')'" >✓</span>
                                            <span :style="'color:rgb('+item.color+')'"   class='text'>
                                                <span  v-html="item['name'] || ''"></span>
                                                <span v-html="item.flag_text || ''"></span>
                                            </span>
                                        </span>
                                    </div>
                                    <div class='flex1'>
                                        <div class='mb10 flex item_center'>
                                            <span class='color6 font12 mr5'><?php echo Yii::t('global','Effective Date:')?></span>
                                            <el-date-picker
                                                size='small'
                                                v-model="item.label.start_time_date"
                                                type="date"
                                                style='width:200px'
                                                @change='setDate(item,"start_time_")'
                                                format="yyyy-MM-dd"
                                                value-format="yyyy-MM-dd"
                                                placement="bottom-start"
                                                placeholder="<?php echo Yii::t('ptc','Select a date') ?>">
                                            </el-date-picker>
                                            <label  class="checkbox-inline ml10 color3">
                                                <input type="checkbox" v-model='item.label.start_time_checked' @change='setCheck(item,"start_time_")'> <?php echo Yii::t('global','Immediate Effect')?>
                                            </label>
                                            <span class='color6 font12 mr5 ml24'><?php echo Yii::t('global','Send effective notification to:')?></span>
                                            <span class='addNum el-icon-circle-plus-outline'  @click='addTeacher(item,"start")'></span>
                                            <span>
                                                <el-popover
                                                placement="bottom"
                                                width="200"
                                                trigger="click">
                                                <div class=' overflow-y scroll-box scroll' style='max-height:300px;overflow-y:auto'>
                                                    <div v-for='(list,idx) in item.label.start_push' class='flex item_center mb10'>
                                                        <img class='imgs ml5' :src="staffInfo[list].photoUrl" alt="">
                                                        <span class='font12 color3 ml10 wordInherit'>{{staffInfo[list].name}}</span>
                                                    </div>
                                                </div>
                                                    <div slot="reference" v-if="item.label.start_push">
                                                        <img class='imgs' v-for='(list,idx) in (item.label.start_push).slice(0,7)' :src="staffInfo[list].photoUrl" alt="">
                                                        <span class='lastNum'  v-if='item.label.start_push.length>7' >+{{item.label.start_push.length-7}}</span>
                                                    </div>
                                                </el-popover>
                                            </span>
                                        </div>
                                        <div class='flex item_center'>
                                            <span  class='color6 font12 mr5'><?php echo Yii::t('global','Expire Date:')?></span>
                                            <el-date-picker
                                                size='small'
                                                v-model="item.label.end_time_date"
                                                format="yyyy-MM-dd"
                                                value-format="yyyy-MM-dd"
                                                type="date"
                                                @change='setDate(item,"end_time_")'
                                                style='width:200px'
                                                placement="bottom-start"
                                                placeholder="<?php echo Yii::t('ptc','Select a date') ?>">
                                            </el-date-picker>
                                            <label  class="checkbox-inline ml10 color3">
                                                <input type="checkbox"  v-model='item.label.end_time_checked' @change='setCheck(item,"end_time_")'> <?php echo Yii::t('global','Never Expire')?>
                                            </label>
                                            <span class='color6 font12 mr5 ml24'><?php echo Yii::t('global','Send expire notification to:')?></span>
                                            <span class='addNum el-icon-circle-plus-outline' @click='addTeacher(item,"end")'></span>
                                            <span>
                                                
                                                <el-popover
                                                    placement="bottom"
                                                    width="200"
                                                    trigger="click">
                                                    <div class=' overflow-y scroll-box scroll' style='max-height:300px;overflow-y:auto'>
                                                        <div v-for='(list,idx) in item.label.end_push' class='flex item_center mb10'>
                                                            <img class='imgs ml5' :src="staffInfo[list].photoUrl" alt="">
                                                            <span class='font12 color3 ml10 wordInherit'>{{staffInfo[list].name}}</span>
                                                        </div>
                                                    </div>
                                                    <div slot="reference"  v-if="item.label.end_push">
                                                        <img class='imgs' v-for='(list,idx) in (item.label.end_push).slice(0,7)' :src="staffInfo[list].photoUrl" alt="">
                                                        <span class='lastNum' v-if='item.label.end_push.length>7' >+{{item.label.end_push.length-7}}</span>
                                                    </div>
                                                </el-popover>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <hr  >
                            </div>
                        </div>
                    </div>
                    <!--系统打的标签不能选择-->
                    <div class="mt30" v-if="showNonSelectable">
                        <div class="mb10" style="margin-right:20px;font-size: 14px">
                            <span class="lh30"><?php echo Yii::t('global','System Tags')?></span>
                        </div>
                        <div class="choose_flag_div">
                            <span v-for="(item,index) in flagList"
                                  v-if="item['editable_by_staff'] == 'false'"
                                  :key="index"
                                  :ref="`flag_${item.flag}`"
                                  :style="active_style[item.flag]"
                                  :title="item.desc"
                                  class="label-no-select" >
                                  <span class='flagSselected' :style="'background:rgb('+item.color+')'" >✓</span>
                                  <span :style="'color:rgb('+item.color+')'"  class='text' >
                                    <span  v-html="item['name'] || ''"></span>
                                    <span v-html="item.flag_text || ''"></span>
                                  </span>
                            </span>
                        </div>

                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel') ?></button>
                    <button type="button" class="btn btn-primary" @click='setFlag()'><?php echo Yii::t('global', 'Save') ?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 选择成员 -->
    <div class="modal fade" id="addStaffModal" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title color3"><?php echo Yii::t('leave', '通知人员') ?></h4>
                </div>
                <div class="modal-body relative" style='padding:0'>
                    <span class='borderLeftpos'></span>
                    <div style='max-height:600px;' class='p24 row'>
                        <div class='col-md-6 col-sm-6'>
                          <div>
                            <el-select v-model="schoolId" class='width100 color3' size='small' placeholder="<?php echo Yii::t('teaching', 'Please select') ?>" @change='addStaff()'>
                                <el-option
                                    v-for="(item,key,index) in schoolList"
                                    :key="key"
                                    :label="item.title"
                                    :value="key">
                                </el-option>
                            </el-select>
                          </div>
                          <div class='mt10'>
                              <el-input
                              placeholder="<?php echo Yii::t('global', 'Search') ?>"
                              v-model='searchText' 
                              size='small'
                              clearable>
                              </el-input>
                          </div>
                          <div  class="tab-pane active mt14 scroll-box" id="class" v-if='searchText==""'  style='max-height:460px;overflow-y:auto'>
                              <div v-for='(list,index) in currentDept.list' class='relative  mb16'>
                                  <p  @click='showDepName(list)'>
                                      <span  class='font14 color606 cur-p'>{{list.dep_name}} </span>
                                      <span class='el-icon-arrow-down ml10' v-if='dep_name!=list.dep_name'></span>
                                      <span class='el-icon-arrow-up ml10' v-else></span>
                                  </p>
                                  <div  class='border scroll-box mr10 childList' v-if='dep_name==list.dep_name'>
                                      <div class="flex item_center listMedia" v-for='(item,key,idx) in list.user'>
                                          <div class='flex flex1' v-if='currentDept.user_info[item.uid]'>
                                              <img :src="currentDept.user_info[item.uid].photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                              <div class="flex1 ml10 flex1Text">
                                                  <div class=" font14 mt8 color3 text_overflow">{{currentDept.user_info[item.uid].name}}</div>
                                                  <div class="font12 mt5 color6 text_overflow">{{currentDept.user_info[item.uid].hrPosition}}</div>
                                              </div>
                                          </div>
                                          <div >
                                              <span class='cur-p font16 text-primary  el-icon-circle-plus-outline' v-if='!currentDept.user_info[item.uid].disabled' @click='assignStaff(item,index,idx)'></span>
                                              <span v-else class='color3'><?php echo Yii::t('directMessage', 'selected') ?></span>
                                          </div>
                                      </div>
                                  </div>
                              </div>
                          </div>
                          <div v-else>
                              <div v-if='searchStaffList.length!=0' class='mt14 scroll-box'  style='max-height:460px;overflow-y:auto'>  
                                  <div class="flex item_center listMedia" v-for='(item,idx) in searchStaffList'>
                                      <div class='flex flex1' >
                                          <img :src="item.photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                          <div class="flex1 ml10 flex1Text">
                                              <div class=" font14 mt8 color3 text_overflow">{{item.name}}</div>
                                              <div class="font12 mt5 color6 text_overflow">{{item.hrPosition}}</div>
                                          </div>
                                      </div>
                                      <div >
                                          <span class='cur-p font16 text-primary  el-icon-circle-plus-outline' v-if='!currentDept.user_info[item.uid].disabled' @click='assignStaff(item,idx,"search")'></span>
                                          <span v-else class='color3'><?php echo Yii::t('directMessage', 'selected') ?></span>
                                      </div>
                                  </div> 
                              </div>
                              <div v-else-if='searchText!=""'>
                                      <div class='font14 color6 text-center mt20'><?php echo Yii::t('ptc', 'No Data') ?></div>    
                              </div>
                          </div>
                        </div>
                        <div class='col-md-6 col-sm-6'>
                            <p class='mt10 font14 color6'>
                            <?php echo Yii::t("newDS", " ");?>{{staffSelected.length}} <?php echo Yii::t('leave', 'Applicant') ?>
                            </p>
                            <div class='scroll-box p10 overflow-y' style='height:500px;overflow-y:auto'>
                                <div class="flex item_center listMedia" v-for='(item,idx) in staffSelected'>
                                    <div class='flex flex1' v-if='allDept[item]'>
                                        <img :src="allDept[item].photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                        <div class="flex1 ml10 flex1Text">
                                            <div class=" font14 mt8 color3 text_overflow">{{allDept[item].name}}</div>
                                            <div class="font12  mt5 color6 text_overflow">{{allDept[item].hrPosition}}</div>
                                        </div>
                                    </div>
                                    <div @click='Unassign(item,idx)'>
                                        <span class='closeChild cur-p mt10 font16 el-icon-circle-close'></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                    </div>
                </div> 
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel");?></button>
                    <button type="button" class="btn btn-primary"  @click='confirmSatff()'><?php echo Yii::t("global", "OK");?></button>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="mask" style="display: none"></div>
