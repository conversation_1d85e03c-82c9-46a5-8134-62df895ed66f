<div class="container-fluid">
    <!-- 面包屑导航 -->
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Teaching Tasks'), array('//mteaching/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Curriculum'), array('//mteaching/curriculum/project'))?></li>
        <li class="active"><?php echo Yii::t('curriculum',$activity->cn_title).' '.Yii::t('curriculum',$activity->en_title);?></li>
    </ol>
    <div class="row">
        <!-- 左侧菜单栏 -->
        <div class="col-md-2">
            <div class="list-group" id="classroom-status-list">
                <?php foreach ($this->leftMenu as $key => $value) { ?>
                <a href="<?php echo $this->createUrl($value[0]);?>" class="list-group-item status-filter <?php echo $this->getAction()->getId()==$value[0]?'active':''; ?>"><?php echo $value[1]; ?></a></li>
                <?php } ?>
            </div>
        </div>
        <div class="col-md-10">
            <div class="panel panel-default">
            <div class="panel-body">
                <?php
                $form=$this->beginWidget('CActiveForm', array(
                    'id'=>'catering-form',
                    'htmlOptions'=>array('class'=>'form-horizontal J_ajaxForm'),
                ));
                ?>
                <!-- 标题 -->
                <div class="form-group">
                    <div class="col-sm-5">
                        <?php echo $form->labelEx($activity, 'cn_title', array('class'=>'')); ?>
                        <?php echo $form->textField($activity, 'cn_title', array('class'=>'form-control')); ?>
                    </div>
                    <div class="col-sm-5">
                        <?php echo $form->labelEx($activity, 'en_title', array('class'=>'')); ?>
                        <?php echo $form->textField($activity, 'en_title', array('class'=>'form-control')); ?>
                    </div>
                </div>
                <!-- 子标题 -->
                <div class="form-group">
                    <div class="col-sm-5">
                    <?php echo $form->labelEx($activity, 'cn_memo', array('class'=>'')); ?>
                        <?php echo $form->textField($activity, 'cn_memo', array('class'=>'form-control')); ?>
                    </div>
                    <div class="col-sm-5">
                    <?php echo $form->labelEx($activity, 'en_memo', array('class'=>'')); ?>
                        <?php echo $form->textField($activity, 'en_memo', array('class'=>'form-control')); ?>
                    </div>
                </div>
                <!-- 所属项目 -->
                <div class="form-group">
                    <?php echo $form->labelEx($activity, 'pids', array('class'=>'col-sm-2')); ?>
                    <div class="col-sm-3"">
                        <label>教育组添加（按住 ctrl 键多选）</label>
                        <?php echo $form->dropDownList($activity,'pids',$eduProjects, array('class'=>'form-control select_4', 'multiple'=>'multiple', 'size'=>'10')); ?>
                    </div>   
                    <div class="col-sm-3"">
                        <label>教师添加（按住 ctrl 键多选）</label>
                        <?php echo $form->dropDownList($activity,'pids',$projects, array('class'=>'form-control select_4', 'multiple'=>'multiple', 'size'=>'10')); ?>
                    </div>
                </div>
                <!-- 适合季节 -->
                <div class="form-group">
                    <?php echo $form->labelEx($activity,'season', array('class'=>'col-sm-2 ')); ?>
                    <div class="col-sm-10">
                        <?php echo $form->dropDownList($activity,'season',$diglossia['season'], array('class'=>'form-control select_4', )); ?>
                    </div>
                </div>
                <!-- 适合地点 -->
                <div class="form-group">
                    <?php echo $form->labelEx($activity, 'activity_type', array('class'=>'col-sm-2 ')); ?>
                    <div class="col-sm-10">
                        <?php echo $form->dropDownList($activity, 'activity_type',$diglossia['room'], array('class'=>'form-control select_4', )); ?>
                    </div>
                </div>
                <!-- 年龄 -->
                <div class="form-group">
                    <?php echo $form->labelEx($activity,'age', array('class'=>'col-sm-2 ')); ?>
                    <div class="col-sm-10">
                        <?php echo $form->checkBoxList($activity,'age',$diglossia['age'], array('class'=>'',)); ?>
                    </div>
                </div>
                <!-- 多元智能 -->
                <div class="form-group">
                    <?php echo $form->labelEx($activity, 'intelligence', array('class'=>'col-sm-2 ')); ?>
                    <div class="col-sm-10">
                        <?php echo $form->checkBoxList($activity, 'intelligence',$diglossia['intelligence'], array('class'=>'', 'empty'=>Yii::t('global', 'Please Select'), 'onchange'=>'')); ?>
                    </div>
                </div>
                <!-- 文化背景 -->
                <div class="form-group">
                    <?php echo $form->labelEx($activity, 'cultural', array('class'=>'col-sm-2 ')); ?>
                    <div class="col-sm-10">
                        <?php echo $form->checkBoxList($activity, 'cultural', $diglossia['cultural'], array('class'=>'', 'empty'=>Yii::t('global', 'Please Select'), 'onchange'=>'')); ?>
                    </div>
                </div>
                <!-- 状态 -->
                <div class="form-group">
                    <?php echo $form->labelEx($activity, 'state', array('class'=>'col-sm-2 ')); ?>
                    <div class="col-sm-10">
                        <?php echo $form->dropDownList($activity, 'state',$diglossia['state'], array('class'=>'form-control select_4', )); ?>
                    </div>
                </div>
                <!-- 学习领域 -->
                <div class="form-group">
                    <?php echo $form->labelEx($activity, 'learning', array('class'=>'col-sm-2 ')); ?>
                    <div class="col-sm-10">
                        <?php echo $form->checkBoxList($activity, 'learning', $diglossia['learning'], array('class'=>'', 'empty'=>Yii::t('global', 'Please Select'), 'onchange'=>'')); ?>
                    </div>
                </div>
                <!-- 学习目标 -->
                    <div class="form-group">
                        <div class="col-sm-5">
                        <?php echo $form->labelEx($activity, 'cn_learning_obj', array('class'=>'')); ?>
                        <?php echo $form->textArea($activity, 'cn_learning_obj', array('class'=>'form-control', 'rows'=>8)); ?>
                        </div>
                        <div class="col-sm-5">
                        <?php echo $form->labelEx($activity, 'en_learning_obj', array('class'=>'')); ?>
                        <?php echo $form->textArea($activity, 'en_learning_obj', array('class'=>'form-control', 'rows'=>8)); ?>
                        </div>
                    </div>
                <!-- 材料用具 -->
                <div class="form-group">
                    <div class="col-sm-5">
                    <?php echo $form->labelEx($activity, 'materials_cn_content', array('class'=>'ontrol-label')); ?>
                    <?php echo $form->textArea($activity, 'materials_cn_content', array('class'=>'form-control', 'rows'=>8)); ?>
                    </div>
                    <div class="col-sm-5">
                    <?php echo $form->labelEx($activity, 'materials_en_content', array('class'=>'ontrol-label')); ?>
                    <?php echo $form->textArea($activity, 'materials_en_content', array('class'=>'form-control', 'rows'=>8)); ?>
                    </div>
                </div>
                <!-- 准备 -->
                <div class="form-group">
                    <div class="col-sm-5">
                    <?php echo $form->labelEx($activity, 'preparation_cn_content', array('class'=>'')); ?>
                    <?php echo $form->textArea($activity, 'preparation_cn_content', array('class'=>'form-control', 'rows'=>8)); ?>
                    </div>
                    <div class="col-sm-5">
                    <?php echo $form->labelEx($activity, 'preparation_en_content', array('class'=>'')); ?>
                    <?php echo $form->textArea($activity, 'preparation_en_content', array('class'=>'form-control', 'rows'=>8)); ?>
                    </div>
                </div>
                <!-- 步骤 -->
                <div class="form-group">
                    <div class="col-sm-5">
                    <?php echo $form->labelEx($activity, 'presentation_cn_content', array('class'=>'')); ?>
                    <?php echo $form->textArea($activity, 'presentation_cn_content', array('class'=>'form-control', 'rows'=>8)); ?>
                    </div>
                    <div class="col-sm-5">
                    <?php echo $form->labelEx($activity, 'presentation_en_content', array('class'=>'')); ?>
                    <?php echo $form->textArea($activity, 'presentation_en_content', array('class'=>'form-control', 'rows'=>8)); ?>
                    </div>
                </div>
                <!-- 提示 -->
                <div class="form-group">
                    <div class="col-sm-5">
                    <?php echo $form->labelEx($activity, 'tips_cn_content', array('class'=>'')); ?>
                    <?php 
                        if ($activity->tips_cn_content) {
                            echo $form->textArea($activity, 'tips_cn_content', array('class'=>'form-control', 'rows'=>8)); 
                        }else{
                            echo $form->textArea($activity, 'tips_cn_content', array('class'=>'form-control', 'rows'=>8,'value'=>'当幼儿进行活动的时候，教师的提问及建议不需要局限于以下的例子。在所提供的建议的基础上，教师需进行深入思考。根据相关的真实场景，就他们正在进行的这个活动和幼儿交谈。以便了解他们已经知道了哪些，还有什么是他们想要知道的。')); 
                        }
                    ?>
                    </div>
                    <div class="col-sm-5">
                    <?php echo $form->labelEx($activity, 'tips_en_content', array('class'=>'')); ?>
                    <?php 
                        if ($activity->tips_en_content) {
                            echo $form->textArea($activity, 'tips_en_content', array('class'=>'form-control', 'rows'=>8)); 
                        }else{
                            echo $form->textArea($activity, 'tips_en_content', array('class'=>'form-control', 'rows'=>8,'value'=>'When children are engaged in this activity, your questions and suggested inquiries should not be limited to the examples below. Think beyond the suggestions given to talk with children. Have real life conversations about the activity they are participating in so you can gain an understanding of what they already know and what they might want to know more about. ')); 
                        }
                    ?>
                    </div>
                </div>
                <!-- 活动延伸 -->
                <div class="form-group">
                    <div class="col-sm-5">
                    <?php echo $form->labelEx($activity, 'extension_cn_content', array('class'=>'')); ?>
                    <?php echo $form->textArea($activity, 'extension_cn_content', array('class'=>'form-control', 'rows'=>8)); ?>
                    </div>
                    <div class="col-sm-5">
                    <?php echo $form->labelEx($activity, 'extension_en_content', array('class'=>'')); ?>
                    <?php echo $form->textArea($activity, 'extension_en_content', array('class'=>'form-control', 'rows'=>8)); ?>
                    </div>
                </div>
                <!-- 是否为教育部门 -->
                <div class="form-group">
                    <div class="col-sm-10">
                        <?php echo $form->labelEx($activity, 'official', array('class'=>'')); ?>
                        <?php echo $form->checkBox($activity, 'official', array('class'=>'')); ?>
                    </div>
                </div>
                <!-- 添加相关活动 -->
                <div class="form-group">
                    <div class='col-sm-10'>
                        <?php 
                            echo '<h4 id="similaract">'.Yii::t('curriculum','Related activities').'</h4>';
                            foreach ($similarActivity as $key => $value) {
                                echo "<label class='btn btn-primary btn-xs'><a style='color:#fff;' href='{$this->createUrl('showactivity',array('aid'=>$value->aid))}'>$value->cn_title $value->en_title</a> <span onclick='removeSimAct(this,$activity->aid,$value->aid)'>X</span>";
                                echo '<input type="hidden" name="sc_id[]" value="'.$value->aid.'"></label> ';
                            } 
                            echo '<button type="button" class="btn btn-info btn-xs" data-toggle="modal" data-target="#myModal">添加</button>';
                        ?>
                    </div>
                </div>
                <!-- 附件 -->
                <div class="form-group">
                    <div class="col-sm-10">
                        <?php echo $form->labelEx($activity, 'attachments', array(' ')); ?>
                        <?php $this->showAttachments($activity->attachments); ?>
                        <input id="inputfile" onchange="uploadatta()" type="file" ><br>
                        <p><?php echo Yii::t('curriculum','Allowed extensions: rar ,doc ,txt ,jpg ,pdf ,gif');?></p>
                        <h4 class="" id="info"></h4>
                    </div>
                </div>  
                <hr>
                <!-- 添加标签 -->
                <h4><?php echo Yii::t('curriculum','Tags'); ?>：</h4>
                <div class="form-group">
                    <div class="col-sm-3"><input type="text" class="form-control" id="tagval"></div>
                    <div class="col-sm-2"><button class="btn btn-success btn-xs" onclick="addtag(this)" type="button">添加</button></div>
                    <div class="col-sm-5" id="tag">
                        <?php foreach ($tag as $key => $value) {
                            echo "<label class='label label-success' onclick='deltag(this)' title='单击删除'>{$value->tag->tag_term}<input type='hidden' name='tag[]' value='{$value->tag->tag_term}'></label>  ";
                        } ?>
                    </div>
                </div>
                <hr>
                <!-- 提交按钮 -->
                <div class="form-group">
                    <div class="col-sm-offset-5 col-sm-7">
                        <!-- 更新时间 -->
                        <input type="hidden" name="addtime" value="<?php echo $activity->addtime ?$activity->addtime:time(); ?>">
                        <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('curriculum', 'Save')?></button>
                    </div>
                </div>
                <?php $this->endWidget(); ?>
            </div>
          </div>
        </div>
    </div>
</div>

<!-- 添加相关活动模态框 -->
<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title" id="myModalLabel"><?php echo Yii::t('curriculum','Related activities');?></h4>
        </div>
        <div class="modal-body">
            <div class="input-group">
                <div class="col-md-6">
                    <select name="project" onchange="proselect(this)" class="form-control">
                        <option value=""><?php echo Yii::t('curriculum','Choose'); ?></option>
                        <?php 
                            foreach ($eduProjects as $key => $value) {
                                echo "<option value='{$key}'>{$value}</option>";
                            }
                            foreach ($projects as $key => $value) {
                                echo "<option value='{$key}'>{$value}</option>";
                            } 
                        ?>
                    </select> 
                </div>
                <div class="col-md-6">
                    <select id="actselect" name="activity" class="form-control">
                    </select> 
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('curriculum','Close');?></button>
            <button type="button" class="btn btn-primary" onclick="save(this)"><?php echo Yii::t('curriculum','Save');?></button>
      </div>
            <h3 id="msg" class="text-center text-"></h3> 
    </div>
  </div>
</div>

<script>
    //删除附件
    function removeatta (span,id) {
        if (confirm('确定删除？')) {
            var aid = <?php echo $activity->aid?$activity->aid:0; ?>;
            var atta = '<?php echo $activity->attachments; ?>';
            $.ajax({
                url:'<?php echo $this->createUrl('deleteAttachments'); ?>',
                data:{atta : atta,id : id,aid : aid},
                type:'post',
                dataType:'json',
                success : function (data) {
                    if (data.msg == 'success') {
                        $(span).parent().parent().remove();
                    }
                },
                error : function (data) {
                    // body...
                }
            })
        };
    }
    //上传附件
    function uploadatta() {
        var data = new FormData();
        var aid = <?php echo $activity->aid?$activity->aid:0; ?>;
        $.each($('#inputfile')[0].files, function(i, file) {
                data.append('upload_file', file);
        });
        data.append('aid',aid);
        $.ajax({
            url:'<?php echo $this->createUrl('saveattachments'); ?>',
            data:data,
            type:'post',
            dataType:'json',
            contentType: false,    //不可缺
            processData: false,    //不可缺
            success : function (data) {
                if (data.msg == 'success') {
                    $("#info").text('上传成功');
                    $("#inputfile").after($("#inputfile").clone().val(""));
                    $("#inputfile").remove();
                    if(data.ext=='jpg'||data.ext=='png'||data.ext=='jpeg'){
                        var div = "<div><img src='"+data.url+"' /><br><br>";
                        div += "<label class='btn btn-success btn-xs'><a style='color:#fff' target='_blank' href='"+data.url+"'>"+data.name+"</a> <span class='glyphicon glyphicon-remove' aria-hidden='true' onclick='removeatta(this,"+data.key+")'></span></label><hr /></div>";
                        $("#inputfile").before(div);
                    }else{
                        var div = "<div><label class='btn btn-success btn-xs'><a style='color:#fff' target='_blank' href='"+data.url+"'>"+data.name+"</a> <span class='glyphicon glyphicon-remove' aria-hidden='true' onclick='removeatta(this,"+data.key+")'></span></label><hr /></div>";
                        $("#inputfile").before(div);
                    }
                    if(aid == 0){
                        var input = "<input type='hidden' name='atta[]' value='"+data.atta+"' />";
                        $("#inputfile").before(input);
                    }
                }else{
                    $("#info").text(data.msg);
                }
            },
            error : function (data) {
                $("#info").text(data.msg);
            }
        });
    }
    //添加标签
    function addtag (btntag) {
        if($("#tagval").val() != ''){
            var tag = "<label class='label label-success' onclick='deltag(this)' title='单击删除'>"+$("#tagval").val()+"<input type='hidden' name='tag[]' value='"+$("#tagval").val()+"'></label>  ";
            $("#tag").append(tag);
        }
    }
    // 删除标签
    function deltag (tag) {
        $(tag).remove();
    }
    //项目显示、隐藏
    function proToggle (span) {
        $(span).next().toggle();
    }
    //二级联动菜单
    function proselect (proselect) {
        var pid = $(proselect).val();
        console.log(pid);
        $.ajax({
            url:'<?php echo $this->createUrl('similaractivity'); ?>',
            data:{type : 'search',pid : pid},
            type:'post',
            dataType:'json',
            success : function (data) {
                console.log(data);
                $('#actselect').empty();
                for(x in data.activity){
                    $('#actselect').prepend("<option value="+x+" >"+data.activity[x]+"</option>");
                }
            },
            error : function (){
            }
        });
    }
    //搜索活动
    function search (btnsearch) {
        var search = $('#search').val();
        // console.log(search);
        $.ajax({
            url:'<?php echo $this->createUrl('similaractivity'); ?>',
            data:{type : 'search',search : search},
            type:'post',
            dataType:'json',
            success : function (data) {
                $('#list').empty();
                for(x in data.activity){
                    // console.log(x);
                    // console.log(data.activity[x]);
                    $('#list').prepend("<input type='checkbox' name='list[]' value="+x+" />"+data.activity[x]+"<br>");
                }
            },
            error : function (){
            }
        });
    }
    //添加相关
    function save (btnsave) {
        var sc_id = $('#actselect').val();
        var bc_id = '<?php echo $activity->aid; ?>';
        var sc_name = $('#actselect').find("option:selected").text();
        $.ajax({
            url:'<?php echo $this->createUrl('similaractivity'); ?>',
            data:{type : 'save',sc_id : sc_id,bc_id : bc_id},
            type:'post',
            dataType:'json',
            success : function (data) {
                if (data.msg=='success') {
                    $('#msg').text('更新成功');
                    console.log(sc_name);
                    var str = "<label class='btn btn-primary btn-xs'><a style='color:#fff;' href=''>"+sc_name+"</a> <span onclick='removeSimAct(this,"+bc_id+","+sc_id+")'>X</span> ";
                        str +='<input type="hidden" name="sc_id[]" value="'+sc_id+'"></label> ';
                    $('#similaract').after(str);
                    $('#myModal').modal('hide');
                }else{
                    $('#msg').text(data.info);
                }
            },
            error : function (data){
                $('#msg').text(data.info);
            }
        });
    }
    //删除相关活动
    function removeSimAct (btnremove,bc_id,sc_id) {
        if (confirm("你确定要删除吗")) {
            $.ajax({
                url:'<?php echo $this->createUrl('deletesimilaractivity') ?>',
                data:{bc_id : bc_id,sc_id : sc_id},
                type:'post',
                dataType:'json',
                success : function (data) {
                    if (data.msg=='success') {
                        $(btnremove).parent().remove();
                    }
                },
                error : function (data) {
                
                }
            }); 
        };  
    }
</script>