<?php

/**
 * This is the model class for table "ivy_as_user_leave".
 *
 * The followings are the available columns in table 'ivy_as_user_leave':
 * @property integer $id
 * @property integer $type
 * @property integer $staff_uid
 * @property integer $hours
 * @property integer $startdate
 * @property integer $enddate
 * @property integer $startyear
 * @property integer $created
 * @property integer $creator
 */
class UserLeave extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return UserLeave the static model class
	 */
	//分类
    const USER_TYPE_LEAVE = 1;			//休假
    const USER_TYPE_OVERTIME = 2;       //加班

	//休假分类
    const TYPE_ANNUAL_LEAVE = 11;       //年假
    const TYPE_SICK_LEAVE = 12;         //病假
    const TYPE_NO_PAY_LEAVE = 13;       //无薪事假
    const TYPE_MARRIAGE_LEAVE = 14;     //婚嫁
    const TYPE_MATERNITY_LEAVE = 15;    //产假
    const TYPE_BEREAVEMENT_LEAVE = 16;  //丧假
    const TYPE_DAYS_OFF = 17;           //调休
    const TYPE_OTHER_LEAVE = 18;        //其它
    const TYPE_DISCOUNTED = 19;         //折现

	//加班分类
	const OT_WEEK_DAY = 21;             //周中延时加班
    const OT_WEEKEN = 22;				//周末加班
	const OT_HOLIDAY = 23;				//法定假日加班
	const OT_CONVER_HOLIDAY = 24;		//调休

	//換算
    const DAY = 16;						//小时换算分钟（8（小时）*2（分钟）=16半小时）
    const FLAG = 2;						//换算介质（1小时=2个半小时）

	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_as_user_leave';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('type, startdate, enddate, startyear, created, staff_uid, hours, branchid', 'required'),
			array('type, staff_uid, hours, startdate, enddate, startyear, created, creator,balance', 'numerical', 'integerOnly'=>true),
            array('branchid', 'length', 'max' => 10),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, type, staff_uid, hours, startdate, enddate, startyear, created, creator, balance, flag', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'appUser' => array(self::BELONGS_TO, 'User', 'staff_uid','with'=>'profile'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'type' => 'Type',
			'staff_uid' => 'Staff Uid',
			'hours' => 'Hours',
			'startdate' => 'Startdate',
			'enddate' => 'Enddate',
			'startyear' => 'Startyear',
			'created' => 'Created',
			'creator' => 'Creator',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('type',$this->type);
		$criteria->compare('staff_uid',$this->staff_uid);
		$criteria->compare('hours',$this->hours);
		$criteria->compare('startdate',$this->startdate);
		$criteria->compare('enddate',$this->enddate);
		$criteria->compare('startyear',$this->startyear);
		$criteria->compare('created',$this->created);
		$criteria->compare('creator',$this->creator);

		return new CActiveDataProvider(get_class($this), array(
			'criteria'=>$criteria,
		));
	}

    /*
     * 取放假的类型
     * @return array
     */
    static public function getLeaveType($id=0){
        $data = array(
            self::TYPE_ANNUAL_LEAVE=>Yii::t("hr", "年假"),
            self::TYPE_SICK_LEAVE =>Yii::t("hr", "病假"),
            self::TYPE_NO_PAY_LEAVE=>Yii::t("hr", "事假"),
            self::TYPE_MARRIAGE_LEAVE=>Yii::t("hr", "婚假"),
            self::TYPE_MATERNITY_LEAVE=>Yii::t("hr", "产假"),
            self::TYPE_BEREAVEMENT_LEAVE=>Yii::t("hr", "丧假"),
            self::TYPE_OTHER_LEAVE=>Yii::t("hr", "其它"),
            self::TYPE_DAYS_OFF=>Yii::t("hr", "调休"),
            self::OT_WEEK_DAY =>Yii::t("hr","周中延时加班"),
            self::OT_WEEKEN => Yii::t("hr","周末加班"),
            self::OT_HOLIDAY => Yii::t("hr","法定假日加班"),
            self::OT_CONVER_HOLIDAY => Yii::t("hr","调休"),
        );
        return ($id) ? $data[$id] : $data;
    }

    static public function showLeavePeriod($model, $dayPattern = null, $timePattern = null){
        $dayPattern = is_null($dayPattern) ? "%s ~ %s" : $dayPattern;
        $timePattern = is_null($timePattern) ? "%s <small>%s ~ %s</small>" : $timePattern;
        if($model['hours'] == UserLeave::DAY){
            return OA::formatDateTime($model['startdate']);
        }elseif($model['hours'] > UserLeave::DAY){
            return sprintf($dayPattern, OA::formatDateTime($model['startdate']), OA::formatDateTime($model['enddate']) );
        }else{
            Yii::app()->format->timeFormat = 'H:i';
            return sprintf($timePattern,
                OA::formatDateTime($model['startdate']),
                Yii::app()->format->formatTime($model['startdate']),
                Yii::app()->format->formatTime($model['enddate']));
        }
    }

	/*
	 *　休假加班申请列表
	 */
	static public function getApplicationLabel($id=0){
		$data = array(
			self::TYPE_ANNUAL_LEAVE=>  array(
				'label'=>Yii::t("hr", "年假"),
				'menu' =>array(
					array(
						'label'=> Yii::t('hr', '按小时请假'),
						'htmlOption'=>array(
							'href'=>'javascript:void(0)',
							'onclick'=>"showUserForm(".self::TYPE_ANNUAL_LEAVE.",'hour')",
						),
					),
					array(
						'label'=> Yii::t('hr', '按天请假'),
						'htmlOption'=>array(
							'href'=>'javascript:void(0)',
							'onclick'=>"showUserForm(".self::TYPE_ANNUAL_LEAVE.",'day')",
						)
					),
				),
				'appButton'=>true
			),
			self::TYPE_SICK_LEAVE=>  array(
				'label'=>Yii::t("hr", "病假"),
				'menu' =>array(
					array(
						'label'=> Yii::t('hr', '按小时请假'),
						'htmlOption'=>array(
							'href'=>'javascript:void(0)',
							'onclick'=>"showUserForm(".self::TYPE_SICK_LEAVE.",'hour')",
						),
					),
					array(
						'label'=> Yii::t('hr', '按天请假'),
						'htmlOption'=>array(
							'href'=>'javascript:void(0)',
							'onclick'=>"showUserForm(".self::TYPE_SICK_LEAVE.",'day')",
						)
					),
					array(
						'label'=> Yii::t('hr', '长期请假'),
						'htmlOption'=>array(
							'href'=>'javascript:void(0)',
							'onclick'=>"showUserForm(".self::TYPE_SICK_LEAVE.",'more')",
						)
					),
				),
				'appButton'=>true,
			),
			self::TYPE_NO_PAY_LEAVE=>  array(
				'label'=>Yii::t("hr", "无薪事假"),
				'menu' =>array(
					array(
						'label'=> Yii::t('hr', '按小时请假'),
						'htmlOption'=>array(
							'href'=>'javascript:void(0)',
							'onclick'=>"showUserForm(".self::TYPE_NO_PAY_LEAVE.",'hour')",
						),
					),
					array(
						'label'=> Yii::t('hr', '按天请假'),
						'htmlOption'=>array(
							'href'=>'javascript:void(0)',
							'onclick'=>"showUserForm(".self::TYPE_NO_PAY_LEAVE.",'day')",
						)
					),
				),
				'appButton'=>true,
			),
			self::TYPE_DAYS_OFF=> array(
				'label'=>Yii::t("hr", "剩余调休"),
				'menu' =>array(
					array(
						'label'=> Yii::t('hr', '按小时请假'),
						'htmlOption'=>array(
							'href'=>'javascript:void(0)',
							'onclick'=>"showUserForm(".self::TYPE_DAYS_OFF.",'hour')",
						),
					),
					array(
						'label'=> Yii::t('hr', '按天请假'),
						'htmlOption'=>array(
							'href'=>'javascript:void(0)',
							'onclick'=>"showUserForm(".self::TYPE_DAYS_OFF.",'day')",
						)
					),
				),
				'appButton'=>true
			),
			self::TYPE_OTHER_LEAVE=> array(
				'label'=>Yii::t("hr", "其它假期"),
				'subtitle'=>array(
					self::TYPE_MARRIAGE_LEAVE=>Yii::t("hr", "婚假"),
					self::TYPE_MATERNITY_LEAVE=>Yii::t("hr", "产假"),
					self::TYPE_BEREAVEMENT_LEAVE=>Yii::t("hr", "丧假"),
					self::TYPE_OTHER_LEAVE=>Yii::t("hr", "其它"),
				),
				'htmlOption'=>array(
					'href'=>'javascript:void(0)',
					'onclick'=>"showUserForm(".self::TYPE_OTHER_LEAVE.",'more')",
				),
				'appButton'=>true
			),
			self::USER_TYPE_OVERTIME=>  array(
				'label'=>Yii::t("hr", "本月累计加班"),
				'subtitle'=>array(
					self::OT_WEEK_DAY=>Yii::t("hr", "周中延时加班"),
					self::OT_WEEKEN=>Yii::t("hr", "周末加班"),
					self::OT_HOLIDAY=>Yii::t("hr", "法定假日加班"),
					self::OT_CONVER_HOLIDAY=>Yii::t("hr", "调休"),
				),
				'appButton'=>true,
				'menu' =>array(
					array(
						'label'=> Yii::t('hr', '按小时加班'),
						'htmlOption'=>array(
							'href'=>'javascript:void(0)',
							'onclick'=>"showUserForm(".self::USER_TYPE_OVERTIME.",'hour')",
						),
					),
					array(
						'label'=> Yii::t('hr', '按天加班'),
						'htmlOption'=>array(
							'href'=>'javascript:void(0)',
							'onclick'=>"showUserForm(".self::USER_TYPE_OVERTIME.",'day')",
						)
					),
				),
			),
			self::USER_TYPE_LEAVE=> array(
				'label'=>Yii::t("hr", "本月累计休假"),
				'appButton'=>false
			),
		);
		return ($id) ? $data[$id] : $data;
	}

	/*
     * 获取某员工剩余假期信息
     */
    static public function getUserLeaveAllData($userId){
        $result = array();
        $leaveData = Yii::app()->db->createCommand()
                    ->select()
                    ->from('ivy_as_user_leave')
                    ->where('staff_uid=:staff_uid and enddate>=:enddate',array(':staff_uid'=>$userId,':enddate'=>strtotime('today')))
                    ->order('enddate ASC')
                    ->queryAll();
        if (is_array($leaveData) && count($leaveData)){
            foreach ($leaveData as $k => $val){
                $result[$val['type']]['hours'] = isset($result[$val['type']]['hours']) ? $result[$val['type']]['hours']+$val['hours'] : $val['hours'];
            }
        }
        return $result;
    }


    /*
     * 获取某员工有效的假期信息（年假、病假）
     * @return array(type=>array())
     */
    public function getUserLeaveData($userId,$type=null){
        $leaveList = array();
        if (!$userId){
            return $leaveList;
        }
        $command = Yii::app()->db->createCommand();
		$command->select('id,hours,startdate,enddate,startyear,type')->from('ivy_as_user_leave');
        $command->where('staff_uid=:staff_uid and enddate>=:enddate',array(':staff_uid'=>$userId,':enddate'=>strtotime('today')));
		if (!empty($type)){
			$command->andWhere('type=:type', array(':type'=>$type));
		}
        $command->order('enddate ASC');
        $leaveData = $command->queryAll();
        if (is_array($leaveData) && count($leaveData)){
            foreach ($leaveData as $val){
				if (empty($type)){
					$leaveList['ids'][$val['id']] = $val['id'];
					$leaveList['type'][$val['type']]['sum'] = isset($leaveList['type'][$val['type']]['sum']) ? $leaveList['type'][$val['type']]['sum']+ $val['hours'] : $val['hours'];
					$leaveList['type'][$val['type']]['list'][$val['id']] = array(
						'hours'=>$val['hours'],
						'startdate'=>$val['startdate'],
						'enddate'=>$val['enddate'],
						'startyear'=>$val['startyear'],
					);
				}else{
					$leaveList[$val['id']] = array(
						'hours'=>$val['hours'],
						'startdate'=>$val['startdate'],
						'enddate'=>$val['enddate'],
						'startyear'=>$val['startyear'],
					);
				}
            }
            unset($leaveData);
        }
        return $leaveList;
    }

    /*
     * 获取某员工有效的假期天数
     * @return array() e.g. valid:true[有剩余假期]valid:false[无假期]
     */
    public function getCountUserLeave($userId,$type=null){
        $leaveSum = 0;
        $useLeaveSum = 0;
        $leaveList = array();
        $leaveData = $this->getUserLeaveData($userId,$type);
        if (is_array($leaveData) && count($leaveData)){
			$leaveDataIds = (empty($type)) ? $leaveData['ids'] : array_keys($leaveData);
            $useLeaveItem = LeaveItem::model()->getUsedLeaveList($userId,$leaveDataIds,FALSE);
            if (is_array($useLeaveItem) && count($useLeaveItem)){
                foreach ($useLeaveItem as $val){
                    $useLeaveSum = $useLeaveSum + $val['sum'];
                }
            }
			if (empty($type)){
				foreach ($leaveData['type'] as $tKey => $tVal) {
					$useSum = 0;
					foreach ($tVal['list'] as $k=>$val){
						$leaveList[$tKey]['list'][$k] = $val;
						$leaveList[$tKey]['list'][$k]['hours'] = $val['hours'] - (isset($useLeaveItem[$k]) ? $useLeaveItem[$k]['sum'] : 0);
                        $leaveList[$tKey]['list'][$k]['formatHours'] = self::getLeaveFormat($leaveList[$tKey]['list'][$k]['hours']);
                        $leaveList[$tKey]['list'][$k]['formatStartdate'] = OA::formatDateTime($val['startdate']);
                        $leaveList[$tKey]['list'][$k]['formatEnddate'] = OA::formatDateTime($val['enddate']);
						$useSum = (isset($useLeaveItem[$k]) ? $useLeaveItem[$k]['sum'] : 0);
					}
					$leaveList[$tKey]['sum'] = $leaveData['type'][$tKey]['sum']-$useSum;
				}
			}else{
				foreach ($leaveData as $k => $val) {
					$leaveList['info'][$k] = $val;
					$leaveList['info'][$k]['hours'] = $val['hours'] - (isset($useLeaveItem[$k]) ? $useLeaveItem[$k]['sum'] : 0);
					$leaveSum = $leaveSum + $val['hours'];
				}
				$leaveList['valid'] = ($leaveSum-$useLeaveSum>0) ? TRUE : FALSE;
			}
            unset($leaveData);
            unset($useLeaveItem);
        }
        return $leaveList;
    }

    /*
     * 将数据库（半小时为基点）取值换算成（天~小时）
     */
    static public function getLeaveFormat($data,$flag=true){
        $daystr = '';
        if ($data>=self::DAY){
            $daystr .= ($flag == true) ? floor($data/self::DAY).Yii::t('hr', '天') : floor($data/self::DAY);
        }
        $mod = $data%self::DAY;
        if ($mod){
            $daystr .= ($flag == true) ? $mod/self::FLAG.Yii::t('hr', '小时') : $mod/self::FLAG;
        }
        return $daystr;
    }

    /*
     * 将（天~小时）换算成数据库保存格式（半小时为基点）
     */
    static public function setLeaveFormat($day,$hour,$minute=0){
       $num = 0;
       if ($day){
           $num += $day*self::DAY;
       }
       if ($hour){
           $num += $hour*self::FLAG;
       }
       if ($minute){
           $num += 1;
       }
       return $num;
    }

    static public function getStartYear($startYear){
        return ($startYear) ? $startYear.'-'.($startYear+1) : null;
    }

    static public function updateUserLeaveBalance($staffUid,$leaveId){
        //查询假期总数
        $leaveModel = self::model()->findByPk($leaveId);
        //已使用假期数
        $useNum = 0;
        $useLeave = LeaveItem::model()->getUsedLeaveList($staffUid, array($leaveId),false,array(self::STATUS_AGREE));
        if (is_array($useLeave) && count($useLeave)) {
            $useNum = $useLeave[$leaveId]['sum'];
        }
        $leaveModel->balance = $leaveModel->hours-$useNum;
        return $leaveModel->update();
    }


	/*
	 * 根据参数得到格式化后的假期数
	 * @return int
	 */
	static public function getHours($startDate,$endDate,$methods,$model){
		$methods = strtolower($methods);
		$num = 0;
		switch ($methods):
			case 'hour':
				$td =strtotime($startDate." ".$model->endTime) - strtotime($startDate." ".$model->startTime);
				if ($td>0){
					$hour = (strtotime($startDate." ".$model->endTime) - strtotime($startDate." ".$model->startTime))/3600;
					$minute = 0;
					if (is_int($hour)===false){
						$hour = ($hour<1) ? 0 : floor($hour);
						$minute = 1;
					}
					$num = self::setLeaveFormat(0, $hour, $minute);
				}
				break;
			case 'day':
				$startDateList = explode(',', $startDate);
				$num = self::setLeaveFormat(count($startDateList), 0);
				break;
			case 'more':
				$td = strtotime($endDate) - strtotime($startDate);
				if ($td>=0){
					$hour = (strtotime($endDate) - strtotime($startDate)+86400)/86400;
					$num = self::setLeaveFormat($hour, 0);
				}
				break;
		endswitch;
		return $num;
	}

}