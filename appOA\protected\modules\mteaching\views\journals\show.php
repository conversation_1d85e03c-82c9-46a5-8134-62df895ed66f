<style>
    [v-cloak] {
        display: none;
    }
    .borderRight {
        border-right: 1px solid #ccc;
        padding-right: 15px
    }
    .inputStyle {
        border: none;
        border-bottom: 1px solid #ccc;
        padding: 0;
        height: 25px;
        line-height: 25px;
        width: 100%;
    }
    .color606{
        color:#606266;
        line-height:30px
    }
    .image{
        width: 32px; 
        height: 32px;
        object-fit:cover;
    }
    .lineHeight{
        line-height:32px
    }
    .closeChild{
        width: 16px;
        height: 16px;
        display: inline-block;
        border: 1px solid #999999;
        text-align: center;
        line-height: 15px;
        border-radius: 50%;
        color:#999999
    }
    .closeChild:hover{
        color:red;
        border: 1px solid red;
    }
    .addChild{
        font-size: 17px;
        color: #409EFF;
        width: 16px;
        height: 16px;
        display: inline-block;
        border: 1px solid #409EFF;
        text-align: center;
        line-height: 15px;
        border-radius: 50%
    }
    .borderLeft{
        border-left:1px solid #DCDFE6
    }
    .border{
        border: 1px solid #DCDFE6;
        border-radius: 5px;
        padding:10px 14px;
        overflow-y: auto;
    }
    .media:hover{
        background:#F7F7F8
    }
    .allCheck{
        position: absolute;
        right:10px;
        top: 4px;
    }
    .flexWidth{
        width:80px;
    }
    .media{
        padding:5px;
        margin-top:0px;
        position: relative;
    }
    .media-heading{
        margin:0
    }
    .hoursMinutes{
        width:70px;
        float:left
    }
    .loading{
        width:90%;
        height:20px;
        background:#fff;
        position: absolute; 
        opacity: 0.5;
        z-index: 99
    }
    .loading span{
        width:100%;
        height:100%;
        display:block;
        background: url("<?php echo Yii::app()->theme->baseUrl?>/images/loading.gif")no-repeat center center ;
    }
    .loading .childLoading{
        background: url("<?php echo Yii::app()->theme->baseUrl?>/images/loading.gif")no-repeat center right ;
    }
    .childLoading{
        width: 20px;
        height: 20px;
        background: #fff;
        position: absolute;
        opacity: 0.5;
        z-index: 99;
        right: 5px;
        top: 13px;
    }
    .childLoading span{
        width:100%;
        height:100%;
        display:block;
        background: url("<?php echo Yii::app()->theme->baseUrl?>/images/loading.gif")no-repeat center center ;
    }
    .content img {
        max-width: 100%;
        margin-bottom: 1em; 
    }
    .imgAvatar{
        height: auto;display: block
    }
    .childList{
        max-height:250px;
        min-height:40px
    }
    .content video {
        max-width: 100%;
        margin-bottom: 1em; 
    }
    .newIcon{
        background: red !important;
        position: absolute !important;
        right: -16px !important;
        top: -10px !important;
        height: 15px !important;
        line-height: 12px !important;
        border-radius: 40px !important;
        color: #fff !important;
        font-size: 12px !important;
        padding: 0 2px !important;
    }
    .tox .tox-tbtn{
        overflow: inherit !important;
    }
    .borderRadio {
        border: 1px solid #dddddd;
        border-radius: 8px;
        display: flex;
        align-items: center;
        padding:12px 16px;
        font-weight: normal;
        transition: border-color 0.3s ease;
    }
    .borderRadio.selected {
        border-color: #428bca; /* 蓝色 */
    }
    .borderRadio img {
        width: 44px;
        height: 44px;
        border-radius: 50%;
        object-fit: cover;
    }
    .width0 {
        width: 0;
    }
    .borderRadio .nowrap {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Teaching Tasks'), array('//mteaching/default/index')) ?></li>
        <li><?php echo Yii::t('newDS', 'Journal') ?></li>
    </ol>
    <div class="row">
        <div class="col-md-2 col-sm-12">
            <div class="list-group" id="classroom-status-list">
                <a href="<?php echo $this->createUrl('index'); ?>" class="list-group-item status-filter"><?php echo Yii::t("newDS", "All Journals");?></a>
                <a href="<?php echo $this->createUrl('edit'); ?>" class="list-group-item status-filter"><?php echo Yii::t("newDS", "Mine");?></a>
                <a href="<?php echo $this->createUrl('feedback'); ?>" class="list-group-item status-filter"><?php echo Yii::t("newDS", "Parent Feedback");?></a>
            </div>
        </div>
        <div id='container' v-cloak>
            <div class='col-md-6 col-sm-6 mb20' style='padding-bottom:80px'>
                <div class="panel panel-default">
                    <div class="panel-heading"><?php echo Yii::t("newDS", "Edit a journal");?> </div>
                    <div class="panel-body">
                        <form class="form-horizontal">
                            <div class="form-group" v-if='type=="teacher"'>
                                <label for="exampleInputEmail1" class="col-sm-2 control-label"><?php echo Yii::t("newDS", "School Year");?></label>
                                <div class="col-sm-10 pt7">
                                    {{startyear}}
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="exampleInputEmail1" class="col-sm-2 control-label"><?php echo Yii::t("newDS", "Type");?></label>
                                <div class="col-sm-10">
                                    <div class="radio " v-for='(list,key,index) in journalCategory'>
                                        <label>
                                            <input type="radio" :disabled='type=="leader" && key=="2"?true:false' v-model='journal.category' :value="key"> {{list}}
                                            <span class='glyphicon glyphicon-question-sign' v-if="key=='1'" onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" title="<?php echo Yii::t("newDS", "Write a public message that will be visible to all or most parents in your class");?>"></span>
                                            <span class='glyphicon glyphicon-question-sign' v-if="key=='2'" onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" title="<?php echo Yii::t("newDS", "Write a private message to the parents of some individual students");?>"></span>
                                        </label>
                                    </div>
                                    <p class="text-warning mt5 ml20"  v-if="journal.category=='2'" >
                                        <span  class='glyphicon glyphicon-exclamation-sign mr5'></span><?php echo Yii::t("newDS", "Parents of assigned students will be email notified when this entry is published.");?>
                                    </p>
                                </div>
                            </div>
                           
                            <div class="form-group" >
                                <label for="exampleInputEmail1" v-if='type=="leader"' class="col-sm-2 control-label"><?php echo Yii::t("newDS", "Title (Cn)");?></label>
                                <label for="exampleInputEmail1" v-else class="col-sm-2 control-label"><?php echo Yii::t("newDS", "Title");?></label>
                                <div class="col-sm-10">
                                    <input type="input" class="form-control"  v-if='type=="leader"' v-model='journal.title' placeholder="<?php echo Yii::t("newDS", "Title (Cn)");?>">
                                    <input type="input" class="form-control"  v-else v-model='journal.title' placeholder="<?php echo Yii::t("newDS", "Title");?>">
                                </div>
                            </div>
                            <div class="form-group" v-if='type=="leader"'>
                                <label for="exampleInputEmail1" class="col-sm-2 control-label"><?php echo Yii::t("newDS", "Title (En)");?></label>
                                <div class="col-sm-10">
                                    <input type="input" class="form-control" v-model='journal.title_en' placeholder="<?php echo Yii::t("newDS", "Title (En)");?>">
                                </div>
                            </div>
                            <div class="form-group" v-if='type=="teacher"'>
                                <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t("newDS", "Subjects");?></label>
                                <div class="col-sm-10">
                                    <div v-if='teacherSubject.length!=0'>
                                        <div class="checcheckbox-inlinekbox"  v-for='(list,index) in teacherSubject'>
                                            <label>
                                                <input type="checkbox" v-model='journal.subjects' :value="list"> {{subjectList[list]}}
                                            </label>
                                        </div>
                                    </div>
                                    <div class="alert alert-info" v-else role="alert"><?php echo Yii::t("newDS", "Cannot find your subjects? please contact campus admin to assign subjects.");?></div>
                                </div>
                            </div>
                            <div class="form-group" v-if='type=="leader"'>
                                <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t("newDS", "Applies to");?> </label>
                                <div class="col-sm-10">
                                    <div class="checkbox-inline" v-for='(list,id,index) in gradeGroupList'>
                                        <label>
                                            <input type="checkbox" v-model='journal.grade_group_id' :value="id"> {{list}}
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="exampleInputEmail1" v-if='type=="leader"'  class="col-sm-2 control-label"><?php echo Yii::t("newDS", "Content (Cn)");?></label>
                                <label for="exampleInputEmail1" v-else  class="col-sm-2 control-label"><?php echo Yii::t("labels", "Content");?></label>
                                <div class="col-sm-10">
                                    <input id="tinymce" type="textarea" v-model='journal.content'>
                                    <div class="alert alert-warning mt20" role="alert">
                                        <p><?php echo Yii::t("newDS", "How to insert a photo or video?");?></p> 
                                        <p>
                                            <?php echo sprintf(Yii::t("newDS", "From Media Repository: Click <a target='_blank'  href='%s'>here</a> to upload photos or videos, then insert files by clicking the gallery icon."), $this->createUrl('/mteaching/weekly/index'));?>
                                            <br>
                                            <?php echo Yii::t("newDS", "Direct Upload: Just drag images to editor area (Images Only).");?>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group"  v-if='type=="leader"'>
                                <label for="exampleInputEmail1" class="col-sm-2 control-label"><?php echo Yii::t("newDS", "Content (En)");?></label>
                                <div class="col-sm-10">
                                    <input id="tinymceEn" type="textarea" v-model='journal.content_en'>
                                    <div class="alert alert-warning mt20" role="alert">
                                        <p><?php echo Yii::t("newDS", "How to insert a photo or video?");?></p> 
                                        <p>
                                            <?php echo sprintf(Yii::t("newDS", "From Media Repository: Click <a target='_blank'  href='%s'>here</a> to upload photos or videos, then insert files by clicking the gallery icon."), $this->createUrl('/mteaching/weekly/index'));?>
                                            <br>
                                            <?php echo Yii::t("newDS", "Direct Upload: Just drag images to editor area (Images Only).");?>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group" v-if='type=="leader"'>
                                <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t("newDS", "In the name of");?></label>
                                <div class="col-sm-10">
                                    <!-- <div class='col-md-4 col-sm-4 pt10 scroll-box' style='height:230px;overflow-y:auto' v-for='(list,index) in leaderList'>
                                        <div class="radio">
                                            <label>
                                            <img :src="list.photo" data-holder-rendered="true" class='imgAvatar'>
                                                <p class='mt5'><input type="radio" name="sign_as_uid" :value="list.uid" v-model='journal.sign_as_uid'>{{list.name}}</p>
                                                <div class='text-muted'>{{list.title}}</div>
                                            </label>
                                        </div>
                                    </div> -->
                                    <div class='font12 color6 mt8'>按拼音和字母排序</div>
                                    <div class='row'>
                                        <div class='col-md-6 col-sm-6 mt10 '  v-for='(list,index) in leaderList'>
                                            <label :class="['borderRadio', { 'selected': journal.sign_as_uid === list.uid }]">
                                                <input type="radio"  name="sign_as_uid" :value="list.uid" v-model='journal.sign_as_uid'>
                                                <img :src="list.photo" data-holder-rendered="true" class='ml10'>
                                                <div class='ml10 flex1 width0'>
                                                    <div class='color3 font14 nowrap'>{{list.name}}</div>
                                                    <div class='color6 font12 nowrap'>{{list.title}}</div>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                    <div class='clearfix'></div>
                                    <div class="alert alert-warning mt20" role="alert"><?php echo Yii::t("newDS", "This photo is for parent view and mangaged by HR. If the photo is not uploaded or mis-uploaded, please contact HR.");?></div>
                                </div>
                            </div>
                            <div class="form-group" v-if='type=="teacher"'>
                                <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t("newDS", "In the name of");?></label>
                                <div class="col-sm-10">
                                    <!-- <div class='col-md-4 col-sm-4 pt10 scroll-box' style='height:230px;overflow-y:auto'>
                                        <div class="radio">
                                            <label>
                                            <img :src="teacherInfo.photo" data-holder-rendered="true" class='imgAvatar'>
                                                <p class='mt5'><input type="radio" name="sign_as_uid" :value="teacherInfo.uid" v-model='journal.sign_as_uid'>{{teacherInfo.name}}</p>
                                                <div class='text-muted'>{{teacherInfo.title}}</div>
                                            </label>
                                        </div>
                                    </div> -->
                                    <div class='row'>
                                        <div class='col-md-6 col-sm-6 mt10 '  >
                                            <label :class="['borderRadio', { 'selected': journal.sign_as_uid === teacherInfo.uid }]">
                                                <input type="radio"  name="sign_as_uid" :value="teacherInfo.uid" v-model='journal.sign_as_uid'>
                                                <img :src="teacherInfo.photo" data-holder-rendered="true" class='ml10'>
                                                <div class='ml10 flex1 width0'>
                                                    <div class='color3 font14 nowrap'>{{teacherInfo.name}}</div>
                                                    <div class='color6 font12 nowrap'>{{teacherInfo.title}}</div>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                    <div class='clearfix'></div>
                                    <div class="alert alert-warning mt20" role="alert"><?php echo Yii::t("newDS", "This photo is for parent view and mangaged by HR. If the photo is not uploaded or mis-uploaded, please contact HR.");?></div>
                                </div>
                            </div>
                            <div class="form-group mb20">
                                <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t("newDS", "Status");?>
                                </label>
                                <div class="col-sm-10">
                                    <div class="radio" v-for='(list,index) in postStatus'  >
                                        <label>
                                            <input type="radio" :value="list.id" v-model='journal.status'>{{list.title}}
                                        </label>
                                    </div>
                                    <div class='mt20'  v-show='journal.status==1'>
                                        <div class="col-sm-3">
                                            <input type="text" class="form-control form-group" id="expired_at" v-model='expired_at' placeholder="<?php echo Yii::t("newDS", "Select a date");?>" >
                                        </div>
                                        <select class="form-control hoursMinutes" v-model='expired_atHours'>
                                            <option v-for='(list,index) in 25'  >{{index < 10?'0'+index:index}}</option>
                                        </select>
                                        <select class="form-control ml5 hoursMinutes"   v-model='expired_atMinutes'>
                                            <option v-for='(list,index) in 61' >{{index < 10?'0'+index:index}}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </form>
                        <div class='clearfix'>
                            <hr>
                            <p> <button  class="btn  btn-primary pull-right" :disabled='disabled' @click='saveData'><?php echo Yii::t("global", "Save");?></button></p>
                        </div> 
                    </div>
                </div>
            </div>
            <div class='col-md-4 col-sm-4' >
                <div class="panel panel-default">
                    <div class="panel-heading"><?php echo Yii::t("newDS", "Attachments");?></div> 
                    <div class="panel-body">
                        <div v-if='journal._id'>
                            <button type="button" id="pickfilesPhoto" class="btn  btn-primary " :disabled='disabledUpload'>
                                <span class='glyphicon glyphicon-paperclip'></span> {{disabledUpload?'<?php echo Yii::t("newDS", "Uploading...");?>':'<?php echo Yii::t("newDS", "Add");?>'}}
                            </button>
                            <div class='mt20'>
                                <p class='flex' v-for='(list,index) in attachments'>
                                    <span style='width:18px;line-height:25px'class='inline-block'class='glyphicon glyphicon-list-alt'> </span>
                                    <a target="_blank"  class='flex1' style='line-height:26px' :href='list.file_key' v-if='!list.isEdit'>{{list.title}}</a>
                                    <span class='flex1' v-else><input type="input" class='inputStyle' v-model='attachmentName' @keyup.enter="saveFile(list)"  placeholder="请输入标题"></span>
                                    <span style='width:85px' class='inline-block text-right' v-if='!list.isEdit'>
                                        <a href="javascript:;" class="btn btn-xs btn-primary" 
                                            draggable="true"
                                            @dragstart="handleDragStart($event, list)" 
                                            @dragover.prevent="handleDragOver($event, list)" 
                                            @dragenter="handleDragEnter($event, list)" 
                                            @dragend="handleDragEnd($event, list)" 
                                            onMouseOver="$(this).tooltip('show')" 
                                            data-toggle="tooltip" 
                                            data-placement="top"  
                                            title="<?php echo Yii::t("newDS", "可上下拖动");?>">
                                            <span class="glyphicon glyphicon-move"></span>
                                        </a>

                                        <button type="button" class="btn btn-primary btn-xs" aria-label="Left Align" @click='list.isEdit=true,attachmentName=list.title'>
                                            <span class="glyphicon glyphicon-pencil" aria-hidden="true"></span>
                                        </button>
                                        <button type="button" class="btn btn-danger btn-xs" aria-label="Left Align" @click='delFile(list)'>
                                            <span class="glyphicon glyphicon-trash" aria-hidden="true"></span>
                                        </button>
                                    </span>
                                    <span style='width:80px'class='inline-block text-right' v-else>
                                        <button type="button" class="btn btn-primary btn-xs" @click='saveFile(list)'><?php echo Yii::t("global", "Save");?></button>
                                        <button type="button" class="btn btn-default btn-xs" @click='list.isEdit=false'><?php echo Yii::t("global", "Cancel");?></button>
                                    </span>
                                </p>
                            </div>
                        </div>
                        <div  v-else class="alert alert-info" role="alert"><?php echo Yii::t("newDS", "Save content first before upload attachment.");?></div>
                    </div>
                </div>
                <div class="panel panel-default" v-if='type=="teacher"'>
                    <div class="panel-heading"><?php echo Yii::t("newDS", "Subscribers");?></div>
                    <div class="panel-body">
                        <div  v-if='journal._id'>
                            <p style='line-height:30px'><?php echo Yii::t("newDS", " ");?><a  href="javascript:;" @click='addChild()'>{{selected.length}}</a><?php echo Yii::t("newDS", " student(s) selected");?>
                                <button type="button" class="btn btn-default btn-sm pull-right" @click='addChild()'>
                                    <span class="glyphicon glyphicon-plus" aria-hidden="true"></span> <?php echo Yii::t("report", "Add");?>
                                </button>
                            </p>
                        </div>
                        <div  v-else class="alert alert-info" role="alert"><?php echo Yii::t("newDS", "Save content first before assign subscribers");?></div>
                    </div>
                </div>
            </div>
            <div class="modal fade" id="addClassModal" tabindex="-1" role="dialog" >
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("newDS", "Add subscribers");?></h4>
                        </div>
                        <div class="modal-body">
                            <div style='max-height:600px;'>
                                <div class='col-md-6 col-sm-6 '>
                                    <ul class="nav nav-tabs" role="tablist">
                                        <li role="presentation" :class="tabActive=='class'?'active':''"  @click='tabLabel("class")'><a href="javascript:;"><?php echo Yii::t("newDS", "By class");?></a></li>
                                        <li role="presentation"  :class="tabActive=='profile'?'active':''"   @click='tabLabel("profile")'><a href="javascript:;"><?php echo Yii::t("newDS", "By course-code");?></a></li>
                                        <li role="presentation"  :class="tabActive=='custom'?'active':''"  @click='tabLabel("custom")'><a href="javascript:;"><?php echo Yii::t("newDS", "By group");?></a></li>
                                    </ul>
                                    <div class="tab-content" >
                                        <div role="tabpanel" v-if='tabActive=="class"' class="tab-pane active mt15 scroll-box" id="class"  style='max-height:540px;overflow-y:auto'>
                                            <div v-for='(list,index) in classList' class='relative'>
                                                <p  @click='getChild(list)'>
                                                    <span  class='font14 color606 cur-p'>{{list.title}} </span>
                                                    <span class='glyphicon glyphicon-chevron-down ml5' v-if='classId!=list.classid'></span>
                                                    <span class='glyphicon glyphicon-chevron-up ml5' v-else></span>
                                                </p>
                                                <p  class='allCheck' v-if='classId==list.classid'><button class="btn btn-primary pull-right btn-xs" type="button" @click='selectAll(list,"classList")'  v-if='list.childData && list.childData.length!=0'><?php echo Yii::t("global", "Select All");?></button></p>
                                                <div  class='border scroll-box mr10 childList' v-if='classId==list.classid'> 
                                                    <div v-if='!childLoading'>
                                                        <div class='' v-if='list.childData && list.childData.length!=0'>
                                                            <div class="media mt10" v-for='(item,index) in list.childData'>
                                                                <div class="media-left pull-left media-middle">
                                                                    <a href="javascript:void(0)">
                                                                        <img :src="item.photo" data-holder-rendered="true" class="media-object img-circle image">
                                                                    </a>
                                                                </div> 
                                                                <div v-if='item.stuLoading'>
                                                                    <div v-if='item.disabled' class="media-right pull-right text-muted lineHeight">
                                                                        <span class='cur-p mt10'><?php echo Yii::t("newDS", "Subscribed");?></span>
                                                                    </div> 
                                                                    <div v-else class="media-right pull-right text-muted" @click='assignChildren(item,"one","classList")'>
                                                                        <span class='cur-p addChild mt10'>+</span>
                                                                    </div> 
                                                                </div>
                                                                <div class='childLoading' v-else>
                                                                    <span></span>
                                                                </div>
                                                                <div class="media-body media-middle">
                                                                    <h4 class="media-heading font12 lineHeight">{{item.name}}</h4>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div v-else>
                                                            <div class='font12 text-muted text-center'><?php echo Yii::t("newDS", "no student in this class");?></div> 
                                                        </div>
                                                    </div>
                                                    <div class='loading' v-else>
                                                        <span></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-if='tabActive=="profile"'>
                                            <div v-if='courseList.length!=0'>
                                                <div v-for='(list,index) in courseList' class='relative'>
                                                    <p  @click='getCourseChild(list)'>
                                                        <span  class='font14 color606 cur-p'>{{list.title}} </span>
                                                        <span class='glyphicon glyphicon-chevron-down ml5' v-if='courseId!=list.id'></span>
                                                        <span class='glyphicon glyphicon-chevron-up ml5' v-else></span>
                                                    </p>
                                                    <p  class='allCheck' v-if='courseId==list.id'><button class="btn btn-primary pull-right btn-xs" type="button" @click='selectAll(list,"courseList")'  v-if='list.childData && list.childData.length!=0'><?php echo Yii::t("global", "Select All");?></button></p>
                                                    <div  class='border scroll-box mr10 childList' v-if='courseId==list.id'> 
                                                        <div v-if='!childLoading'>
                                                            <div class='' v-if='list.childData && list.childData.length!=0'>
                                                                <div class="media mt10" v-for='(item,index) in list.childData'>
                                                                    <div class="media-left pull-left media-middle">
                                                                        <a href="javascript:void(0)">
                                                                            <img :src="item.photo" data-holder-rendered="true" class="media-object img-circle image">
                                                                        </a>
                                                                    </div> 
                                                                    <div v-if='item.stuLoading'>
                                                                        <div v-if='item.disabled' class="media-right pull-right text-muted lineHeight">
                                                                            <span class='cur-p mt10'><?php echo Yii::t("newDS", "Subscribed");?></span>
                                                                        </div> 
                                                                        <div v-else class="media-right pull-right text-muted" @click='assignChildren(item,"one","courseList")'>
                                                                            <span class='cur-p addChild mt10'>+</span>
                                                                        </div> 
                                                                    </div>
                                                                    <div class='childLoading' v-else>
                                                                        <span></span>
                                                                    </div>
                                                                    <div class="media-body media-middle">
                                                                        <h4 class="media-heading font12 lineHeight">{{item.name}}</h4>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div v-else>
                                                                <div class='font12 text-muted text-center'><?php echo Yii::t("newDS", "no student in this class");?></div> 
                                                            </div>
                                                        </div>
                                                        <div class='loading' v-else>
                                                            <span></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div v-else class="alert alert-success mt20" role="alert"><?php echo Yii::t("newDS", "No MS courses assigned to you yet.");?></div>
                                        </div>
                                        <div role="tabpanel" v-if='tabActive=="custom"' class="tab-pane active mt15" id="class"  >
                                            <div class='scroll-box'  style='height:500px;overflow-y:auto'> 
                                                <div v-for='(list,index) in groupList' class='relative'>
                                                    <p  @click='getGroupList(list)'>
                                                        <span  class='font14 color606 cur-p'>{{list.title}} </span>
                                                        <span class='glyphicon glyphicon-chevron-down ml5' v-if='groupId!=list.id'></span>
                                                        <span class='glyphicon glyphicon-chevron-up ml5' v-else></span>
                                                    </p>
                                                    <p  class='allCheck' v-if='groupId==list.id'><button class="btn btn-primary pull-right btn-xs" type="button" @click='selectAll(list,"groupList")'><?php echo Yii::t("global", "Select All");?></button></p>
                                                    <div  class='border scroll-box mr10 childList'  v-if='groupId==list.id'>
                                                        <div v-if='!childLoading'>
                                                            <div class="media mt10" v-for='(item,index) in list.childData'>
                                                                <div class="media-left pull-left media-middle">
                                                                    <a href="javascript:void(0)">
                                                                        <img :src="item.avatar" data-holder-rendered="true" class="media-object img-circle image">
                                                                    </a>
                                                                </div> 
                                                                <div v-if='item.stuLoading'>
                                                                    <div v-if='item.disabled' class="media-right pull-right text-muted lineHeight">
                                                                        <span class='cur-p mt10'><?php echo Yii::t("newDS", "Subscribed");?></span>
                                                                    </div> 
                                                                    <div v-else class="media-right pull-right text-muted" @click='assignChildren(item,"one","groupList")'>
                                                                        <span class='cur-p addChild mt10'>+</span>
                                                                    </div> 
                                                                </div>
                                                                <div class='childLoading' v-else>
                                                                    <span></span>
                                                                </div>
                                                                <div class="media-body media-middle">
                                                                    <h4 class="media-heading font12 lineHeight">{{item.name}}</h4>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class='loading' v-else>
                                                            <span></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <p class='mt15'>
                                                <button type="button" class="btn btn-default  btn-sm"  @click='addGroup("add")'>
                                                    <span class="glyphicon glyphicon-plus" aria-hidden="true"></span> <?php echo Yii::t("newDS", "Create a group");?>
                                                </button>
                                                <button type="button" class="btn btn-default  btn-sm"  @click='addGroup("edit")' v-if='groupList.length!=0'>
                                                    <span class="glyphicon glyphicon-edit" aria-hidden="true"></span> <?php echo Yii::t("newDS", "Manage groups");?>
                                                </button>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class='col-md-6 col-sm-6 borderLeft'>
                                    <ul class="nav nav-tabs" role="tablist">
                                        <li role="presentation" class="active"><a href="#chosen" aria-controls="home" role="tab" data-toggle="tab"><?php echo Yii::t("newDS", "Subscribed");?></a></li>
                                    </ul>
                                    <div class="tab-content">
                                        <div role="tabpanel" class="tab-pane active" id="chosen">
                                            <p class='mt15 font14 color606'><?php echo Yii::t("newDS", " ");?>{{selected.length}}<?php echo Yii::t("newDS", " student(s) selected");?>
                                                <button class="btn btn-primary pull-right btn-xs" v-if='selected.length!=0' type="button" @click='batchDel("modal")'><?php echo Yii::t("newDS", "Remove All");?></button>
                                            </p>
                                            <div class='border scroll-box' style='height:500px'>
                                            <div class="media" v-for='(list,index) in selected'>
                                                <div class="media-left pull-left media-middle">
                                                    <a href="javascript:void(0)">
                                                        <img :src="list.avatar" data-holder-rendered="true" class="media-object img-circle image">
                                                    </a>
                                                </div> 
                                                <div class="media-right pull-right text-muted" v-if='!list.loading' @click='Unassign(list.id,index)'>
                                                    <span class='closeChild cur-p mt10 glyphicon glyphicon-minus'></span>
                                                </div> 
                                                <div class='childLoading' v-else>
                                                    <span></span>
                                                </div>
                                                <div class="media-body media-middle">
                                                    <h4 class="media-heading font12">{{list.name}}</h4> 
                                                    <div class="text-muted">{{list.class_name}}</div>
                                                </div>
                                            </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class='clearfix'></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal fade" id="groupModal" tabindex="-1" role="dialog" aria-hidden='true' data-backdrop="static">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" v-if='groupType=="add"' id="parentCommentsModalLabel"><?php echo Yii::t("newDS", "Create a group");?></h4>
                        <h4 class="modal-title" v-else id="parentCommentsModalLabel"><?php echo Yii::t("newDS", "Manage groups");?></h4>
                    </div>
                    <div class="modal-body">     
                        <div class='col-md-12 col-sm-12 pb20' style='border-bottom:1px solid #dddddd;' v-if='groupType=="edit"'>
                             <?php echo Yii::t("newDS", "Select a group");?> 
                             <select class="form-control select_4  ml10 inline-block"  v-model='editGroupId'  @change='viewGroup()'>
                                    <option v-for='(list,index) in groupList' :value='list.id'>{{list.title}}</option>
                                </select>
                        </div>     
                        <div class='clearfix'></div>
                        <div style='max-height:600px;'>
                            <div class='col-md-6 col-sm-6 '>
                                <div  class="tab-pane active scroll-box mt10"  style='max-height:600px;overflow-y:auto'>
                                    <div v-for='(list,index) in classList' class='relative'>
                                        <p @click='getGroupChild(list)'>
                                            <span  class='font14 color606 cur-p' >{{list.title}} </span>
                                            <span class='glyphicon glyphicon-chevron-down ml5' v-if='classId!=list.classid'></span>
                                            <span class='glyphicon glyphicon-chevron-up ml5' v-else></span>
                                        </p>
                                        <p  class='allCheck' v-if='classId==list.classid'><button class="btn btn-primary pull-right btn-xs" type="button" @click='groupAll(list)' v-if='list.childData.length!=0'><?php echo Yii::t("global", "Select All");?></button></p>
                                        <div  class='border scroll-box mr10 classList' v-if='classId==list.classid'>
                                            <div v-if='!childLoading'>
                                                <div class=''  v-if='list.childData.length!=0'>
                                                    <div class="media mt10" v-for='(item,index) in list.childData'>
                                                        <div class="media-left pull-left media-middle">
                                                            <a href="javascript:void(0)">
                                                                <img :src="item.photo" data-holder-rendered="true" class="media-object img-circle image">
                                                            </a>
                                                        </div> 
                                                        <div v-if='item.groupDisabled' class="media-right pull-right text-muted lineHeight">
                                                            <span class='cur-p mt10'><?php echo Yii::t("newDS", "Subscribed");?></span>
                                                        </div> 
                                                        <div v-else class="media-right pull-right text-muted" @click='addGroupChild(item,"one")'>
                                                            <span class='cur-p addChild mt10'>+</span>
                                                        </div> 
                                                        <div class="media-body media-middle">
                                                            <h4 class="media-heading font12 lineHeight">{{item.name}}</h4>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div v-else>
                                                    <div class='font12 text-muted text-center'><?php echo Yii::t("newDS", "no student in this class");?></div> 
                                                </div>
                                            </div>
                                            <div class='loading' v-else>
                                                <span></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class='col-md-6 col-sm-6 borderLeft'>  
                                <p class='flex mt10'><span class='font14 color606 flexWidth'><?php echo Yii::t("newDS", "Group Title");?></span> 
                                    <input type="text" v-model='groupTitle' class="form-control flex1" id="exampleInputEmail1" placeholder="<?php echo Yii::t("newDS", "Group Title");?>">
                                </p>
                                <p class='mt15 font14 color606'><?php echo Yii::t("newDS", " ");?>{{groupSelect.length}}<?php echo Yii::t("newDS", " student(s) selected");?>
                                    <button class="btn btn-primary pull-right btn-xs" v-if='groupSelect.length!=0' type="button" @click='groupSelect=[];classId=""'><?php echo Yii::t("newDS", "Remove All");?></button>
                                </p>
                                <div class='border scroll-box' style='height:485px'>
                                    <div class="media" v-for='(list,index) in groupSelect'>
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img :src="list.avatar" data-holder-rendered="true" class="media-object img-circle image">
                                            </a>
                                        </div> 
                                        <div class="media-right pull-right text-muted" @click='delGroupChild(list.id,index)'>
                                            <span class='closeChild cur-p mt10 glyphicon glyphicon-minus'></span>
                                        </div> 

                                        <div class="media-body media-middle">
                                            <h4 class="media-heading font12">{{list.name}}</h4> 
                                            <div class="text-muted">{{list.class_name}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class='clearfix'></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal" ><?php echo Yii::t("global", "Close");?></button>
                        <button type="button" v-if='groupType=="edit"' class="btn btn-default"  @click='delGroup()'><?php echo Yii::t("global", "Delete");?></button>
                        <button type="button" class="btn btn-primary" @click='saveGroup()'><?php echo Yii::t("global", "Save");?></button>
                    </div>
                    </div>
                </div>
            </div>
            <div class="modal fade" tabindex="-1" role="dialog" id="editModal">
                <div class="modal-dialog modal-sm" role="document">
                    <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title"><?php echo Yii::t("newDS", "Data Saved!");?></h4>
                    </div>
                    <div class="modal-footer">
                        <a type="button" class="btn btn-default" href="<?php echo $this->createUrl('edit'); ?>" ><?php echo Yii::t("newDS", "Go to My Journal List");?></a>
                        <button type="button" v-if='isEdit' class="btn btn-primary" data-dismiss="modal"><?php echo Yii::t("newDS", "Stay in this page");?></button>
                        <a type="button" v-else class="btn btn-primary" :href="'<?php echo $this->createUrl('show', array('type' => Yii::app()->request->getParam('type'))); ?>&id='+journal._id"  ><?php echo Yii::t("newDS", "Stay in this page");?></a>
                    </div>
                    </div>
                </div>
            </div>
            <div class="modal fade" tabindex="-1" role="dialog" id="delModal">
                <div class="modal-dialog modal-sm" role="document">
                    <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title"><?php echo Yii::t("newDS", "Remove All");?></h4>
                    </div>
                    <div class="modal-body">
                        <p><?php echo Yii::t("newDS", "Proceed to remove all?");?></p>
                    </div>
                    <div class="modal-footer">
                        <a type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel");?></a>
                        <button type="button" class="btn btn-primary" @click='batchDel()'><?php echo Yii::t("newDS", "Delete");?></button>
                    </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>

<script>
    tinymce.init({
        selector: '#tinymce',
        content_style: "img{max-width:100%}",
        language: '<?php echo CommonUtils::autoLang("zh_CN" , "en"); ?>',
        height: 600,
        plugins: 'fullscreen image link media imagetools preview table code wordcount paste my-example-plugin lists, advlist',
        imagetools_cors_hosts: ['picsum.photos'],
        menubar: 'file edit view insert format help',
        contextmenu: 'link image imagetools table spellchecker lists editimage',
        toolbar_sticky: true,
        image_uploadtab: false,
        image_dimensions: false,
        automatic_uploads: true,
        paste_data_images: true,
        menu: {
            insert: { title: 'Insert', items: 'image link media template codesample inserttable | charmap emoticons hr | pagebreak nonbreaking anchor toc | insertdatetime | editimage' },
        },
        content_style: "img{max-width:100%}",
        automatic_uploads: true,
        paste_data_images: true,
        // 上传文件，成功回调，失败回调，进度
        images_upload_handler: function (blobInfo, success, failure, progress) {
            handleUploadImage(blobInfo, success, failure, progress);
        },
        toolbar: 'mediaDialog shareDialog bullist numlist',
        setup: function(editor) {
            // 实时同步编辑器内容到 selector
            editor.on('change', function() {
                tinymce.triggerSave();
            });
            editor.ui.registry.addButton('mediaDialog', {
                // icon: 'gallery',
                text:'<span title="<?php echo Yii::t("newDS", "Media Gallery");?>"><svg width="24" height="24"><path fill-rule="nonzero" d="M5 15.7l2.3-2.2c.3-.3.7-.3 1 0L11 16l5.1-5c.3-.4.8-.4 1 0l2 1.9V8H5v7.7zM5 18V19h3l1.8-1.9-2-2L5 17.9zm14-3l-2.5-2.4-6.4 6.5H19v-4zM4 6h16c.6 0 1 .4 1 1v13c0 .6-.4 1-1 1H4a1 1 0 0 1-1-1V7c0-.6.4-1 1-1zm6 7a2 2 0 1 1 0-4 2 2 0 0 1 0 4zM4.5 4h15a.5.5 0 1 1 0 1h-15a.5.5 0 0 1 0-1zm2-2h11a.5.5 0 1 1 0 1h-11a.5.5 0 0 1 0-1z"></path></svg></span>',
                onAction: function () {
                    container.tinymceList=[]
                    var instanceApi = editor.windowManager.openUrl({
                        onMessage: function (dialogApi, details) {
                            switch (details.mceAction) {
                                case 'loading':
                                    dialogApi.unblock()
                                    break;
                                case 'addItem':
                                    container.tinymceList.push(details.content)
                                    break;
                                case 'removeItem':
                                    for(var i=0;i<container.tinymceList.length;i++){
                                        if(container.tinymceList[i].id==details.content.id){
                                            container.tinymceList.splice(i,1)
                                        }
                                    }
                                break;
                            }
                        },
                        onAction: function (dialogApi, details) {
                            dialogApi.close()
                            for(var i=0;i<container.tinymceList.length;i++){
                                if(container.tinymceList[i].type=='photo'){
                                    editor.insertContent('<div><img style="max-width:100%" src='+container.tinymceList[i]._url+'></div>')
                                }else{
                                    let url=container.tinymceList[i].url.split('!vh120')[0]
                                    editor.insertContent(
                                    '<p><video controls  style="max-width:100%;object-fit:cover"  width="600" height="auto" poster='+url+' >'+
                                        '<source src='+container.tinymceList[i]._url+'  type="video/mp4">'+
                                    '</video></p>')
                                }
                            }
                        },
                        title: '<?php echo Yii::t("newDS", "Media Gallery");?>',
                        url: '<?php echo $this->createUrl('media'); ?>',
                        height: 500,
                        width: 730,
                        buttons: [{
                            type:'custom',
                            text:'Insert',
                            name:'btn-insert',
                            primary: true,
                            align: 'end'
                        },
                            {
                            type:'cancel',
                            text:'Close',
                            name:'btn-close',
                            primary: false,
                            align: 'end'
                        }],

                    });
                    instanceApi.block('loading')
                }
            })
            editor.ui.registry.addButton('shareDialog', {
                text: '<span title="<?php echo Yii::t("directMessage", "Insert Department Contact QrCode"); ?>" style="position:relative"><span class="newIcon">new</span><svg xmlns="http://www.w3.org/2000/svg" version="1.0" width="24" height="24" viewBox="0 0 36.000000 36.000000" preserveAspectRatio="xMidYMid meet"><g transform="translate(0.000000,36.000000) scale(0.100000,-0.100000)" fill="#000000" stroke="none"><path d="M67 324 c-10 -11 -8 -74 2 -74 5 0 11 12 13 28 l3 27 103 3 102 3 0 -126 0 -125 -105 0 -105 0 0 25 c0 14 -5 25 -11 25 -6 0 -9 -16 -7 -37 l3 -38 120 0 120 0 0 145 0 145 -116 3 c-63 1 -118 -1 -122 -4z"/><path d="M161 242 c-12 -23 4 -52 29 -52 26 0 45 36 29 56 -16 19 -46 18 -58 -4z m44 -11 c7 -12 -12 -24 -25 -16 -11 7 -4 25 10 25 5 0 11 -4 15 -9z"/><path d="M40 220 c0 -5 14 -10 30 -10 17 0 30 5 30 10 0 6 -13 10 -30 10 -16 0 -30 -4 -30 -10z"/><path d="M40 180 c0 -5 14 -10 30 -10 17 0 30 5 30 10 0 6 -13 10 -30 10 -16 0 -30 -4 -30 -10z"/><path d="M152 154 c-12 -8 -22 -24 -22 -35 0 -26 13 -24 25 4 7 14 19 22 35 22 16 0 28 -8 35 -22 12 -28 25 -30 25 -4 0 22 -35 51 -60 51 -9 0 -26 -7 -38 -16z"/><path d="M40 140 c0 -5 14 -10 31 -10 17 0 28 4 24 10 -3 6 -17 10 -31 10 -13 0 -24 -4 -24 -10z"/></g></svg></span>',
                onAction: function () {
                    container.tinymceList=[]
                    var instanceApi = editor.windowManager.openUrl({
                        onMessage: function (dialogApi, details) {
                            switch (details.mceAction) {
                                case 'loading':
                                    dialogApi.unblock()
                                    break;
                                case 'insertImg':
                                    editor.insertContent('<div style="text-align:center;margin:0 auto"><img src='+details.content+'></div><div></div>')
                                    dialogApi.close()
                                break;
                            }
                        },    
                        title: '<?php echo Yii::t("directMessage", "Insert Department Contact QrCode"); ?>',
                        url: '<?php echo $this->createUrl('directMessage/qrcode'); ?>',
                        height: 350,
                        width:400,
                    });
                    instanceApi.block('loading')
                }
            })
        },
    });
    tinymce.init({
        selector: '#tinymceEn',
        content_style: "img{max-width:100%}",
        language: '<?php echo CommonUtils::autoLang("zh_CN" , "en"); ?>',
        height: 600,
        plugins: 'fullscreen image link media imagetools preview table code wordcount paste my-example-plugin lists, advlist',
        imagetools_cors_hosts: ['picsum.photos'],
        menubar: 'file edit view insert format help',
        contextmenu: 'link image imagetools table spellchecker lists editimage',
        toolbar_sticky: true,
        image_uploadtab: false,
        image_dimensions: false,
        automatic_uploads: true,
        paste_data_images: true,
        menu: {
            insert: { title: 'Insert', items: 'image link media template codesample inserttable | charmap emoticons hr | pagebreak nonbreaking anchor toc | insertdatetime | editimage' },
        },
        // 上传文件，成功回调，失败回调，进度
        images_upload_handler: function (blobInfo, success, failure, progress) {
            handleUploadImage(blobInfo, success, failure, progress);
        },
        toolbar: 'mediaDialog shareDialog bullist numlist ',
        setup: function(editor) {
            // 实时同步编辑器内容到 selector
            editor.on('change', function() {
                tinymce.triggerSave();
            });
            editor.ui.registry.addButton('mediaDialog', {
                // icon: 'gallery', 
                text:'<span title="<?php echo Yii::t("newDS", "Media Gallery");?>"><svg width="24" height="24"><path fill-rule="nonzero" d="M5 15.7l2.3-2.2c.3-.3.7-.3 1 0L11 16l5.1-5c.3-.4.8-.4 1 0l2 1.9V8H5v7.7zM5 18V19h3l1.8-1.9-2-2L5 17.9zm14-3l-2.5-2.4-6.4 6.5H19v-4zM4 6h16c.6 0 1 .4 1 1v13c0 .6-.4 1-1 1H4a1 1 0 0 1-1-1V7c0-.6.4-1 1-1zm6 7a2 2 0 1 1 0-4 2 2 0 0 1 0 4zM4.5 4h15a.5.5 0 1 1 0 1h-15a.5.5 0 0 1 0-1zm2-2h11a.5.5 0 1 1 0 1h-11a.5.5 0 0 1 0-1z"></path></svg></span>',
                onAction: function () {
                    container.tinymceList=[]
                    var instanceApi = editor.windowManager.openUrl({
                        onMessage: function (dialogApi, details) {
                            switch (details.mceAction) {
                                case 'loading':
                                    dialogApi.unblock()
                                    break;
                                case 'addItem':
                                    container.tinymceList.push(details.content)
                                    break;
                                case 'removeItem':
                                    for(var i=0;i<container.tinymceList.length;i++){
                                        if(container.tinymceList[i].id==details.content.id){
                                            container.tinymceList.splice(i,1)
                                        }
                                    }
                                break;
                            }
                        },
                        onAction: function (dialogApi, details) {
                            dialogApi.close()
                            for(var i=0;i<container.tinymceList.length;i++){
                                if(container.tinymceList[i].type=='photo'){
                                    editor.insertContent('<div><img  style="max-width:100%"  src='+container.tinymceList[i]._url+'></div>')
                                }else{
                                    let url=container.tinymceList[i].url.split('!vh120')[0]
                                    editor.insertContent(
                                    '<p><video controls  style="max-width:100%;object-fit:cover"  width="600" height="auto" poster='+url+' >'+
                                        '<source src='+container.tinymceList[i]._url+'  type="video/mp4">'+
                                    '</video></p>')
                                }
                            }
                        },
                        title: '<?php echo Yii::t("newDS", "Media Gallery");?>',
                        url: '<?php echo $this->createUrl("journals/media") ?>',
                        height: 500,
                        width:730,
                        buttons: [{
                            type:'custom',
                            text:'Insert',
                            name:'btn-insert',
                            primary: true,
                            align: 'end'
                        },
                            {
                            type:'cancel',
                            text:'Close',
                            name:'btn-close',
                            primary: false,
                            align: 'end'
                        }],

                    });
                    instanceApi.block('loading')
                }
            });
            editor.ui.registry.addButton('shareDialog', {
                text: '<span title="<?php echo Yii::t("directMessage", "Insert Department Contact QrCode"); ?>" style="position:relative"><span class="newIcon">new</span><svg xmlns="http://www.w3.org/2000/svg" version="1.0" width="24" height="24" viewBox="0 0 36.000000 36.000000" preserveAspectRatio="xMidYMid meet"><g transform="translate(0.000000,36.000000) scale(0.100000,-0.100000)" fill="#000000" stroke="none"><path d="M67 324 c-10 -11 -8 -74 2 -74 5 0 11 12 13 28 l3 27 103 3 102 3 0 -126 0 -125 -105 0 -105 0 0 25 c0 14 -5 25 -11 25 -6 0 -9 -16 -7 -37 l3 -38 120 0 120 0 0 145 0 145 -116 3 c-63 1 -118 -1 -122 -4z"/><path d="M161 242 c-12 -23 4 -52 29 -52 26 0 45 36 29 56 -16 19 -46 18 -58 -4z m44 -11 c7 -12 -12 -24 -25 -16 -11 7 -4 25 10 25 5 0 11 -4 15 -9z"/><path d="M40 220 c0 -5 14 -10 30 -10 17 0 30 5 30 10 0 6 -13 10 -30 10 -16 0 -30 -4 -30 -10z"/><path d="M40 180 c0 -5 14 -10 30 -10 17 0 30 5 30 10 0 6 -13 10 -30 10 -16 0 -30 -4 -30 -10z"/><path d="M152 154 c-12 -8 -22 -24 -22 -35 0 -26 13 -24 25 4 7 14 19 22 35 22 16 0 28 -8 35 -22 12 -28 25 -30 25 -4 0 22 -35 51 -60 51 -9 0 -26 -7 -38 -16z"/><path d="M40 140 c0 -5 14 -10 31 -10 17 0 28 4 24 10 -3 6 -17 10 -31 10 -13 0 -24 -4 -24 -10z"/></g></svg></span>',
                onAction: function () {
                    container.tinymceList=[]
                    var instanceApi = editor.windowManager.openUrl({
                        onMessage: function (dialogApi, details) {
                            switch (details.mceAction) {
                                case 'loading':
                                    dialogApi.unblock()
                                    break;
                                case 'insertImg':
                                    editor.insertContent('<div style="text-align:center;margin:0 auto"><img src='+details.content+'></div><div></div>')
                                    dialogApi.close()
                                break;
                            }
                        },                    
                        title: '<?php echo Yii::t("directMessage", "Insert Department Contact QrCode"); ?>',
                        url: '<?php echo $this->createUrl('directMessage/qrcode'); ?>',
                        height: 350,
                        width:400,
                    });
                    instanceApi.block('loading')
                }
            })
        },
    });
    tinymce.PluginManager.add('my-example-plugin', function (editor) {
        editor.ui.registry.addMenuItem('editimage', {
            icon: 'image',
            text: '<?php echo Yii::t("newDS", "Add/Remove Watermark");?>',
            onAction: function () {
                if(container.Watermark.indexOf(container.WatermarkImg) == -1){
                    container.Watermark=container.Watermark+container.WatermarkImg
                }else{
                    container.Watermark=container.Watermark.replace(container.WatermarkImg,"")
                }
                editor.insertContent('<div><img  style="max-width:100%"  src='+container.Watermark+'></div>')
            }
        });
        editor.ui.registry.addContextMenu('image', {
            update: function (element) {
                container.Watermark=element.src
                return !container.Watermark ? '' : 'image';
            }
        });
    });
    function handleUploadImage (blobInfo, success, failure, progress) {
        $.ajax({
            url: '<?php echo $this->createUrl("getQiniuTokenSimple") ?>',
            type: "post",
            dataType: 'json',
            data: {
                isPrivate: 0,
                prefix: 'notice',
            },
            success: function(data) {
                if (data.state == 'success') {
                    var token = data.data.token;
                    var domain = data.data.domain;
                    // 上传文件

                    var xhr = new XMLHttpRequest();
                    xhr.withCredentials = false;
                    xhr.open("post", "https://up-z0.qiniup.com");
                    xhr.upload.onprogress = function (e) {
                        progress(Math.round(e.loaded / e.total * 100) | 0);
                    };
                    xhr.onerror = function () {
                        failure('Image upload failed due to a XHR Transport error. Code: ' + xhr.status);
                    };
                    xhr.onload = function() {
                        var json;

                        if (xhr.status === 403) {
                            failure('HTTP Error: ' + xhr.status, { remove: true });
                            return;
                        }

                        if (xhr.status < 200 || xhr.status >= 300) {
                            failure('HTTP Error: ' + xhr.status);
                            return;
                        }

                        json = JSON.parse(xhr.responseText);

                        if (!json || typeof json.name != 'string') {
                            failure('Invalid JSON: ' + xhr.responseText);
                            return;
                        }
                        if(json.h<=500){
                            success( domain + "/" + json.name);
                        }else{
                            success( domain + "/" + json.name+container.WatermarkImg);
                        }
                        
                    };
                    var formData = new FormData();
                    var file = blobInfo.blob();
                    formData.append('file', file, file.name);
                    formData.append('token', token);
                    xhr.send(formData);
                } else {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                }
            },
            error: function(data) {
                resultTip({
                    error: 'warning',
                    msg: "上传失败"
                });
            },
        })
    }
    $(document).ready(function () {
    // 通过该方法来为每次弹出的模态框设置最新的zIndex值，从而使最新的modal显示在最前面
        $(document).on('show.bs.modal', '.modal', function (event) {
            var zIndex = 1040 + (10 * $('.modal:visible').length);
            $(this).css('z-index', zIndex);
            setTimeout(function() {
                $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
            }, 0);
        });
    });
    $(function() {
		$("#expired_at").datepicker({
			dateFormat: "yy-mm-dd ",
		});
        $( "#expired_at").on('change', function () {
            container.expired_at = $('#expired_at').val();
        });
	});
    var data = <?php echo json_encode($data); ?>;
    var type = '<?php echo Yii::app()->request->getParam('type'); ?>';
    var container = new Vue({
        el: "#container",
        data: {
            Watermark:'',
            WatermarkImg:'!v1000',
            dataList: data.list,
            journalTypeList: data.journalTypeList,
            headList: data.headList,
            gradeGroupList: data.gradeGroupList,
            classList: data.classList,
            pagination: data.pagination,
            journalCategory: data.journalCategory,
            type: type,
            leaderList: data.leaderList,
            journal: {},
            attachments: [],
            startyear: data.startyear,
            token: '',
            teacherInfo: data.teacherInfo,
            yid:data.yid,
            teacherSubject:data.teacherSubject,
            subjectList:data.subjectList,
            attachmentName:'',
            disabled:false,
            disabledUpload:false,
            expired_at:'',
            expired_atHours:'00',
            expired_atMinutes:'00',
            postStatus:[
                {
                    title:'<?php echo Yii::t("newDS", "Make offline");?>',
                    id:'0'
                },
                {
                    title:'<?php echo Yii::t("newDS", "Publish at (also as archive time from parent view)");?>',
                    id:'1'
                }
            ],
            tabActive:"class",
            classId:'',
            selected:[],
            classData:{},
            groupType:'',
            groupSelect:[],
            groupTitle:'',
            groupList:[],
            groupListChild:[],
            groupId:'',
            editGroupId:'',
            childLoading:false,
            isEdit:false,
            courseList:[],
            courseId:'',
            courseData:{}
        },
        watch: {
            'journal._id': {
                handler(newVal, oldVal) {
                    if(newVal!=undefined){
                       this.uploadFile()
                    }
                },
            deep: true
            }    
        },
        created: function() {
            const dataArray = Object.values(data.leaderList);
            this.leaderList=this.sortTea(dataArray)
            if(data.journal.targets_info && data.journal.targets_info!=null){
                this.selected=data.journal.targets_info
                this.sortData(this.selected)
            }
            
            this.startyear=data.startyear+'-'+ (parseInt(data.startyear)+1)
            if (data.journal == null || data.journal == 'null' || Object.keys(data.journal).length == 0) {
                this.journal = {
                    'attachments': [],
                    'class_id': [],
                    'content': '',
                    'grade_group_id': [],
                    'sign_as_uid':type=='teacher'?this.teacherInfo.uid:'',
                    'status': '',
                    'title': '',
                    'sign_title_cn':'',
                    'sign_title_cn':'',
                    'start_year':data.startyear,
                    'yid':this.yid,
                    'subjects':[],
                    'category':'1'
                }
                this.expired_at=this.newDate('','date')
            } else {
                this.journal = data.journal
                if(this.journal.status==1){
                    this.expired_atHours=this.newDate(this.journal.format_publish_at,'hours')
                    this.expired_atMinutes=this.newDate(this.journal.format_publish_at,'minutes')
                    this.expired_at=this.newDate(this.journal.format_publish_at,'date')
                }
            }
            for(var i=0;i<this.classList.length;i++){
                this.classList[i].childData=[]  
            }
        },
        methods: {
            sortTea(list){
                list.sort((x,y)=>{
                    return x['name'].localeCompare(y['name'])
                })
                return list
            },
            newDate(dateTime,type){
                if(dateTime!=''){                    
                    var date = new Date(dateTime.replace(/\-/g, '/'));
                }else{
                    var date = new Date();
                }
                const Y = date.getFullYear() + '-';
                const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
                const D = (date.getDate() < 10 ? '0'+date.getDate() : date.getDate()) + ' ';
                const h = (date.getHours() < 10 ? '0'+date.getHours() : date.getHours()) ;
                const m = (date.getMinutes() <10 ? '0'+date.getMinutes() : date.getMinutes());
                const s = date.getSeconds(); // 秒
                const dateString = Y + M + D + h+':' + m+':' + s;
                if(type=='hours'){
                    return h
                } if(type=='minutes'){
                    return m
                } if(type=='date'){
                    return Y + M + D 
                }
                if(!type){
                    return dateString;
                }
            },
            saveData() {
                this.journal.content =  tinymce.get('tinymce').getContent() 
                this.journal.type = this.type
                let that = this
                if(this.journal.title==''){
                    resultTip({
                        error: 'warning',
                        msg:'请填写标题'
                    });
                    return
                }
                if(this.type=='leader'){
                    this.journal.content_en = tinymce.get('tinymceEn').getContent()
                    if(!this.journal.title_en || this.journal.title_en==''){
                        resultTip({
                            error: 'warning',
                            msg:'请填写英文标题'
                        });
                        return
                    }
                    if(!this.journal.content_en || this.journal.content_en==''){
                        resultTip({
                            error: 'warning',
                            msg:'请填写英文内容'
                        });
                        return
                    }
                }
                if(this.journal.content==''){
                    resultTip({
                        error: 'warning',
                        msg:'请填写内容'
                    });
                    return
                }
                if(this.journal.sign_as_uid==''){
                    resultTip({
                        error: 'warning',
                        msg:'请选择署名人'
                    });
                    return
                }
                if(this.journal.status==2){
                    if($('#expired_at').val().trim()==''){
                        resultTip({
                            error: 'warning',
                            msg:'请选择定时发布时间'
                        });
                        return
                    }
                }
                this.journal.publish_at=this.journal.status==1?$('#expired_at').val().trim()+' '+this.expired_atHours+':'+this.expired_atMinutes:''
                this.disabled=true
                if (this.journal._id) {
                    $.ajax({
                        url: '<?php echo $this->createUrl("update") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            data: that.journal
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.disabled=false
                                that.isEdit=true
                                $('#editModal').modal('show') 
                                
                            } else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                                that.disabled=false
                            }
                        },
                        error: function(data) {
                            that.disabled=false
                        },
                    })
                } else {
                    if(this.type=='teacher'){
                        this.journal.sign_title_cn=this.teacherInfo.title_cn;
                        this.journal.sign_title_en=this.teacherInfo.title_en;
                    }else{
                        
                        this.leaderList.forEach(item => {
                            if (item.uid==this.journal.sign_as_uid) {
                                this.journal.sign_title_cn=item.title_cn;
                                this.journal.sign_title_en=item.title_en;
                            } 
                        })
                    }
                    $.ajax({
                        url: '<?php echo $this->createUrl("add") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            data: that.journal
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.journal = data.data.data
                                that.disabled=false
                                that.isEdit=false
                                $('#editModal').modal('show') 
                            } else {
                                resultTip({
                                    error: 'warning',
                                    msg:data.message
                                });
                                that.disabled=false
                            }
                        },
                        error: function(data) {
                            this.disabled=false
                        },
                    })
                }
            },
            uploadFile(){
                var that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getAttachments") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        linkId: this.journal._id,
                        linkType:'journal'
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            data.data.data.forEach(item => {
                                item.isEdit=false
                            })
                            that.attachments=data.data.data
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
                $.ajax({
                    url: '<?php echo $this->createUrl("getQiniuToken") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        linkId: this.journal._id,
                        linkType:'journal'

                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.token=data.data.data
                            config['token'] = data.data.data;
                            var uploader = new plupload.Uploader(config);
                            uploader.init();
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            delFile(list) {
                var that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("deleteAttachment") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        attachmentId:list._id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.attachments.forEach((item,index) => {
                                if (item._id==list._id) {
                                    that.attachments.splice(index, 1)
                                } 
                            })
                        } else {
                            resultTip({  
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            saveFile(list) {
                var that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("updateAttachmentName") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        attachmentId:list._id,
                        attachmentName:this.attachmentName

                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.attachments.forEach((item,index) => {
                                if (item._id==list._id) {
                                    Vue.set(that.attachments[index], 'title', that.attachmentName);
                                    Vue.set(that.attachments[index], 'isEdit', false);
                                } 
                            })
                            resultTip({
                                msg:'保存成功'
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            handleDragStart(e, item) {
                this.dragging = item;
            },
            handleDragEnd(e, item) {
                this.dragging = null
                var fileList=[];
                this.attachments.forEach((item,index) => {
                    fileList.push(item._id)
                })
                $.ajax({
                    url: '<?php echo $this->createUrl("sortAttachment") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        fileSortList:fileList,

                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg:'排序成功'
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })

            },
            handleDragOver(e) {
                e.dataTransfer.dropEffect = 'move' // e.dataTransfer.dropEffect="move";//在dragenter中针对放置目标来设置!
            },
            handleDragEnter(e, item) {
                e.dataTransfer.effectAllowed = "move" //为需要移动的元素设置dragstart事件
                if(item === this.dragging) {
                    return
                }
                const newItems = [...this.attachments]
                const src = newItems.indexOf(this.dragging)
                const dst = newItems.indexOf(item)
                newItems.splice(dst, 0, ...newItems.splice(src, 1))
                this.attachments = newItems
            },
            addChild(){
                $('#addClassModal').modal('show') 
            },
            tabLabel(type){
                this.tabActive=type
                var that=this
                this.groupId=''
                this.classId=''
                this.courseId=''
                if(type=='custom'){
                    $.ajax({
                        url: '<?php echo $this->createUrl("getGroupLink") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                for(var i=0;i<data.data.length;i++){
                                    data.data[i].childData=[]
                                }
                                that.groupList=data.data      
                            } else {
                                resultTip({  
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                        },
                        error: function(data) {
                        },
                    })
                }
                if(type=='profile'){
                    $.ajax({
                        url: '<?php echo $this->createUrl("getCourseList") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            type:'course'
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                for(var i=0;i<data.data.list.length;i++){
                                    data.data.list[i].childData=[]
                                }
                                that.courseList=data.data.list
                            } else {
                                resultTip({  
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                        },
                        error: function(data) {
                        },
                    })
                }
            },
            getchildIds(){
                var childId=[]
                if(this.selected!=null){
                    for(var i=0;i<this.selected.length;i++){
                        childId.push(this.selected[i].id)
                    }
                }
                return childId
            },
            getGroupList(list,type){
                let that=this
                if(!type){
                    if(that.groupId==list.id){    
                        that.groupId=''
                        return
                    }
                }
                that.groupId=list.id
                that.childLoading=true
                var childId=[]
                if(this.selected!=null){
                    for(var i=0;i<this.selected.length;i++){
                        childId.push(+this.selected[i].id)
                    }
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("getGroupLinkChild") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:list.id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.sortData(data.data)
                            if(type){
                                that.groupSelect=data.data
                            }
                            for(var i=0;i<data.data.length;i++){
                                data.data[i].stuLoading=true
                                data.data[i].classTitle= data.data[i].class_name
                                data.data[i].photo= data.data[i].avatar
                                if (childId.indexOf(data.data[i].id)!=-1) {
                                    data.data[i].disabled=true
                                }else{
                                    data.data[i].disabled=false
                                }   
                            }
                            for(var i=0;i<that.groupList.length;i++){
                                if(that.groupList[i].id==list.id){
                                    that.groupList[i].childData=data.data
                                }
                            }
                            that.childLoading=false
                        } else {
                            resultTip({  
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            getChild(list,type){ 
                var that=this
                if(that.classId==list.classid){    
                    that.classId=''
                    return
                }
                that.classId=list.classid
                this.classData=list
                var childId=[]
                if(this.selected!=null){
                    for(var i=0;i<this.selected.length;i++){
                        childId.push(this.selected[i].id)
                    }
                }
                for(var i=0;i<that.classList.length;i++){
                    if(that.classList[i].classid==list.classid){
                        if(that.classList[i].childData.length!=0){
                            for(var j=0;j<that.classList[i].childData.length;j++){
                                if (childId.indexOf(that.classList[i].childData[j].id)!=-1) {
                                    that.classList[i].childData[j].disabled=true
                                }else{
                                    that.classList[i].childData[j].disabled=false
                                }   
                            }
                            that.$forceUpdate()
                            return
                        }
                    }
                }
                that.childLoading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("childList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        classId:list.classid
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            for(var i=0;i<data.data.length;i++){
                                data.data[i].classTitle=list.title
                                data.data[i].stuLoading=true
                                if(type){
                                    data.data[i].disabled=false
                                }else{
                                    if (childId.indexOf(data.data[i].id)!=-1) {
                                        data.data[i].disabled=true
                                    }else{
                                        data.data[i].disabled=false
                                    }   
                                }
                            }
                            that.sortData(data.data)
                            for(var i=0;i<that.classList.length;i++){
                                if(that.classList[i].classid==list.classid){
                                    that.classList[i].childData=data.data
                                }
                            }
                            that.childLoading=false
                            that.$forceUpdate()
                        } else {
                            resultTip({  
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            getCourseChild(list,type){
                var that=this
                if(that.courseId==list.id){    
                    that.courseId=''
                    return
                }
                that.courseId=list.id
                this.courseData=list
                var childId=[]
                if(this.selected!=null){
                    for(var i=0;i<this.selected.length;i++){
                        childId.push(this.selected[i].id)
                    }
                }
                for(var i=0;i<that.courseList.length;i++){
                    if(that.courseList[i].id==list.id){
                        if(that.courseList[i].childData.length!=0){
                            for(var j=0;j<that.courseList[i].childData.length;j++){
                                if (childId.indexOf(that.courseList[i].childData[j].id)!=-1) {
                                    that.courseList[i].childData[j].disabled=true
                                }else{
                                    that.courseList[i].childData[j].disabled=false
                                }   
                            }
                            that.$forceUpdate()
                            return
                        }
                    }
                }
                that.childLoading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("getCourseChild") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        courseId:list.id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            for(var i=0;i<data.data.length;i++){
                                data.data[i].classTitle=list.title
                                data.data[i].stuLoading=true
                                if(type){
                                    data.data[i].disabled=false
                                }else{
                                    if (childId.indexOf(data.data[i].id)!=-1) {
                                        data.data[i].disabled=true
                                    }else{
                                        data.data[i].disabled=false
                                    }   
                                }
                            }
                            that.sortData(data.data)
                            for(var i=0;i<that.courseList.length;i++){
                                if(that.courseList[i].id==list.id){
                                    that.courseList[i].childData=data.data
                                }
                            }
                            that.childLoading=false
                            that.$forceUpdate()
                        } else {
                            resultTip({  
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            selectAll(list,type){
                var targetId=[]
                for(var i=0;i<list.childData.length;i++){
                    if (!list.childData[i].disabled) {
                        targetId.push(list.childData[i].id)
                    }
                }
                if(targetId.length==0){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("newDS", "All added already.");?>'
                    });
                    return
                }
               this.assignChildren(targetId,'all',type)
            },
            addLoading(list,type,dataType){
                for(var i=0;i<this[dataType].length;i++){
                    for(var j=0;j<this[dataType][i].childData.length;j++){
                        if(type=='all'){
                        if (list.indexOf(this[dataType][i].childData[j].id)!=-1) {
                                Vue.set(this[dataType][i].childData[j], 'stuLoading', false);
                                this.$forceUpdate()
                            }
                        }
                        if(list.id==this[dataType][i].childData[j].id){
                            Vue.set(this[dataType][i].childData[j], 'stuLoading', false);
                            this.$forceUpdate()
                        }
                    }
                }
            },
            assignChildren(list,type,dataType){
                var data=[]
                let that=this
                this.addLoading(list,type,dataType)
                that.$forceUpdate()
                $.ajax({
                    url: '<?php echo $this->createUrl("assignTargets") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:that.journal._id,
                        targets:type=='all'?list:[parseInt(list.id)]
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            if(data.data==1){
                                for(var i=0;i<that[dataType].length;i++){
                                    if(dataType=='classList'){
                                        if(that[dataType][i].classid==that.classId){
                                            that.shuttle(list,type,dataType,i)
                                        }
                                    }else if(dataType=='courseList'){
                                        if(that[dataType][i].id==that.courseId){
                                            that.shuttle(list,type,dataType,i)
                                        }
                                    }else{
                                        if(that[dataType][i].id==that.groupId){
                                            that.shuttle(list,type,dataType,i)
                                        }
                                    }
                                }
                            }
                        } else {
                            resultTip({  
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            sortData(list){
                <?php if (Yii::app()->language == 'zh_cn') { ?>
                    list.sort((a, b)=> a.name.localeCompare(b.name, 'zh'));
                <?php } else { ?>
                    list.sort((a, b) => a.name.charCodeAt(0) - b.name.charCodeAt(0));
                <?php } ?>
            },
            shuttle(list,type,dataType,i){
                let that=this
                for(var j=0;j<that[dataType][i].childData.length;j++){
                    if(type=='all'){
                        if (list.indexOf(that[dataType][i].childData[j].id)!=-1) {
                            var data ={
                                'avatar':that[dataType][i].childData[j].photo,
                                'class_name':that[dataType][i].childData[j].classTitle,
                                'name':that[dataType][i].childData[j].name,
                                'loading':false,
                                'id':that[dataType][i].childData[j].id
                            };
                            Vue.set(that[dataType][i].childData[j], 'disabled', true);
                            Vue.set(that[dataType][i].childData[j], 'stuLoading', true);
                            that.selected.push(data)
                            that.sortData(that.selected)
                           
                        }
                    }else{
                        if(that[dataType][i].childData[j].id==list.id){
                            var data ={
                                'avatar':list.photo,
                                'class_name':list.classTitle,
                                'name':list.name,
                                'loading':false,
                                'id':list.id
                            };
                            Vue.set(that[dataType][i].childData[j], 'disabled', true);
                            Vue.set(that[dataType][i].childData[j], 'stuLoading', true);
                            that.selected.push(data)
                            that.sortData(that.selected)
                        }
                    }
                }
                Vue.set(list, 'stuLoading', true);
            },
            Unassign(id,index){
                let that=this
                var dataType=''
                Vue.set(that.selected[index], 'loading', true);
                if(this.tabActive=='custom'){
                    dataType='groupList'
                }else{
                    dataType='classList'
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("removeTargets") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:that.journal._id,
                        targets:id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            if(data.data==1){
                                for(var i=0;i<that[dataType].length;i++){
                                    if(dataType=='classList'){
                                        if(that[dataType][i].classid==that.classId){
                                            that.delChild(dataType,i,id)
                                        }
                                    }else{
                                        if(that[dataType][i].id==that.groupId){
                                            that.delChild(dataType,i,id)
                                        }
                                    }
                                }
                                that.selected.splice(index,1)
                                that.$forceUpdate()
                            }
                        } else {
                            resultTip({  
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            delChild(dataType,i,id){
                let that=this
                for(var j=0;j<that[dataType][i].childData.length;j++){
                    if(that[dataType][i].childData[j].id==id){
                        Vue.set(that[dataType][i].childData[j], 'disabled', false);
                    }
                }
            },
            addGroup(type){
                this.groupType=type
                this.groupSelect=[]
                this.groupTitle=''
                this.classId=''
                if(type=='edit'){
                    this.editGroupId=this.groupList[0].id
                    this.groupTitle=this.groupList[0].title
                    this.getGroupList(this.groupList[0],'edit')
                }
                $('#groupModal').modal('show') 
            },
            getGroupChild(list,type){ 
                var that=this
                var groupChildId=[]
                if(that.classId==list.classid){    
                    that.classId=''
                    return
                }
                that.classId=list.classid
                if(that.groupType=='edit'){
                    for(var i=0;i<this.groupSelect.length;i++){
                        groupChildId.push(this.groupSelect[i].id+'')
                    }
                }
                for(var i=0;i<that.classList.length;i++){
                    if(that.classList[i].classid==list.classid){
                        if(that.classList[i].childData.length!=0){
                            for(var j=0;j<that.classList[i].childData.length;j++){
                                if(that.groupType=='add'){
                                    that.classList[i].childData[j].groupDisabled=false
                                }else{
                                    if (groupChildId.indexOf(that.classList[i].childData[j].id)!=-1) {
                                        that.classList[i].childData[j].groupDisabled=true
                                    }else{
                                        that.classList[i].childData[j].groupDisabled=false
                                    }   
                                }
                            }
                            return
                        }
                    }
                }
                this.childLoading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("childList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        classId:list.classid
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.sortData(data.data)
                            for(var i=0;i<data.data.length;i++){
                                data.data[i].classTitle=list.title
                                if(that.groupType=='add'){
                                    data.data[i].groupDisabled=false
                                }else{
                                    if (groupChildId.indexOf(data.data[i].id)!=-1) {
                                        data.data[i].groupDisabled=true
                                    }else{
                                        data.data[i].groupDisabled=false
                                    }   
                                }
                            }
                            for(var i=0;i<that.classList.length;i++){
                                if(that.classList[i].classid==list.classid){
                                    that.classList[i].childData=data.data
                                }
                            }
                            that.childLoading=false
                        } else {
                            resultTip({  
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            groupAll(list){
                var targetId=[]
                for(var i=0;i<list.childData.length;i++){
                    if (!list.childData[i].groupDisabled) {
                        targetId.push(list.childData[i].id)
                    }
                }
               this.addGroupChild(targetId,'all')
            },
            addGroupChild(list,type){
                let that=this
                for(var i=0;i<that.classList.length;i++){
                    if(that.classList[i].classid==that.classId){
                        for(var j=0;j<that.classList[i].childData.length;j++){
                            if(type=='all'){
                                if (list.indexOf(that.classList[i].childData[j].id)!=-1) {
                                    var data ={
                                        'avatar':that.classList[i].childData[j].photo,
                                        'class_name':that.classList[i].childData[j].classTitle,
                                        'name':that.classList[i].childData[j].name,
                                        'id':that.classList[i].childData[j].id
                                    };
                                    Vue.set(that.classList[i].childData[j], 'groupDisabled', true);
                                    that.groupSelect.push(data)
                                    that.sortData(that.groupSelect)
                                }
                            }else{
                                if(that.classList[i].childData[j].id==list.id){
                                    var data ={
                                        'avatar':list.photo,
                                        'class_name':list.classTitle,
                                        'name':list.name,
                                        'id':list.id
                                    };
                                    Vue.set(that.classList[i].childData[j], 'groupDisabled', true);
                                    that.groupSelect.push(data)
                                    that.sortData(that.groupSelect)
                                }
                            }
                        }
                    }
                }
            },
            delGroupChild(id,index){
                let that=this
                for(var i=0;i<that.classList.length;i++){
                    if(that.classList[i].classid==that.classId){
                        for(var j=0;j<that.classList[i].childData.length;j++){
                            if(that.classList[i].childData[j].id==id){
                                Vue.set(that.classList[i].childData[j], 'groupDisabled', false);
                            }
                        }
                    }
                }
                that.groupSelect.splice(index,1)
                that.$forceUpdate()
            },
            saveGroup(){
                var targetId=[]
                for(var i=0;i<this.groupSelect.length;i++){
                    targetId.push(this.groupSelect[i].id)
                }
                if(this.groupTitle==''){
                    resultTip({  
                        error: 'warning',
                        msg: '<?php echo Yii::t("newDS", "Please input group title.");?>'
                    });
                    return
                }
                if(targetId.length==0){
                    resultTip({  
                        error: 'warning',
                        msg: '请选择分组学生'
                    });
                    return
                }
                let that=this
                if(that.groupType=='edit'){
                    $.ajax({
                        url: '<?php echo $this->createUrl("editGroupLink") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            title:this.groupTitle,
                            childIds:targetId,
                            id:that.editGroupId
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.groupId=''
                                that.tabLabel('custom') 
                                $('#groupModal').modal('hide')
                            } else {
                                resultTip({  
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                        },
                        error: function(data) {

                        },
                    })
                }else{
                    $.ajax({
                        url: '<?php echo $this->createUrl("addGroupLink") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            title:this.groupTitle,
                            childIds:targetId
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.groupId=''
                                that.tabLabel('custom') 
                                $('#groupModal').modal('hide')
                            } else {
                                resultTip({  
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                        },
                        error: function(data) {

                        },
                    })
                }
               
            },
            viewGroup(list){
                for(var i=0;i<this.groupList.length;i++){
                    if(this.editGroupId==this.groupList[i].id){
                        this.groupTitle=this.groupList[i].title
                        this.getGroupList(this.groupList[i],'edit')
                    }
                }
            },
            delGroup(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("delGroupLink") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:this.editGroupId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.groupId=''
                            that.tabLabel('custom') 
                            $('#groupModal').modal('hide')
                        } else {
                            resultTip({  
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            batchDel(Modal){
                var targetId=[]
                for(var i=0;i<this.selected.length;i++){
                    targetId.push(this.selected[i].id)
                }
                let that=this
                if(Modal){
                    $('#delModal').modal('show')
                }else{                   
                    $.ajax({
                        url: '<?php echo $this->createUrl("removeAllTargets") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            id:this.journal._id,
                            targets:targetId
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                $('#delModal').modal('hide')
                                that.groupId=''
                                that.selected=[]
                                that.classId=''
                                that.tabLabel(that.tabActive) 
                            } else {
                                resultTip({  
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                        },
                        error: function(data) {

                        },
                    })
                }
            }
        }
    })
    var config = {
        runtimes: 'html5,flash,html4', //上传模式,依次退化
        container: 'container', //上传区域DOM ID，默认是browser_button的父元素，
        browse_button: 'pickfilesPhoto', //上传选择的点选按钮，**必需**
        token: '',
        flash_swf_url: '/js/plupload/Moxie.swf', // flash的相对地址
        auto_start: true, //选择文件后自动上传，若关闭需要自己绑定事件触发上传
        multi_selection: false, //不允许多选
        url:'https://upload-z1.qiniup.com',
        
        init: {
            'FilesAdded': function(up, files) {
                container.disabledUpload=true
                up.start();
            },
            BeforeUpload: function(up, file) {
                //设置参数
                up.setOption({
                    multipart_params:{token:container.token}
                })
            },
            'FileUploaded': function(up, file, info) {       
                var response = eval('('+info.response+')');
                if(info.status == 200){
                    response.data.isEdit=false   
                    container.attachments.push(response.data)
                    container.disabledUpload=false
                }
            },
            'Error': function(up, err, errTip) {
                if(err.response){
                    var response = eval('('+err.response+')');
                }
                else{
                    var response = {message: err.message};
                }
                if(response.error == 'token not specified'){
                    up.stop();
                    $.ajax({
                        url: '<?php echo $this->createUrl("getQiniuToken") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            journalId: container.journal._id
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                container.uptoken=data.data.data
                                config['uptoken'] = data.data.data;
                                up.setOption("multipart_params", {"token":data.data.token,})
                                up.start();
                            } else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.msg
                                });
                            }
                        },
                        error: function(data) {

                        },
                    })
                }
            },
            'UploadComplete': function(up, file) {
                //队列文件处理完毕后,处理相关的事情
            }
        }
    };
</script>