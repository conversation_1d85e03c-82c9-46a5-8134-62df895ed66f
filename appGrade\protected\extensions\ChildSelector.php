<?php
/*
 * Author: RAN Xin
 */

class ChildSelector extends CWidget{
	public $currentCid;
	public $myChildObjs;
	public $id;
	public $class;
	
	public function init(){
		
	}
	
	public function run(){
		echo CHtml::openTag('div', array('id'=>$this->id));
		echo CHtml::openTag('h2');
		echo $this->myChildObjs[$this->currentCid]->getChildName();
		echo CHtml::openTag("a", array(
			"href"=>$this->getController()->createUrl("//child/profile/welcome", array("childid"=>$this->currentCid)),
		));
		echo CHtml::image(CommonUtils::childPhotoUrl($this->myChildObjs[$this->currentCid]->photo), $this->myChildObjs[$this->currentCid]->getChildName(), array("class"=>"thumb--small") );
		echo CHtml::closeTag("a");
		
		echo CHtml::closeTag('h2');
		echo CHtml::closeTag('div');
		$this->render('selector');

	}
}

?>