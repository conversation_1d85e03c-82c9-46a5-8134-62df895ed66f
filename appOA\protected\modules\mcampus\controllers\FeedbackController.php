<?php

class FeedbackController extends BranchBasedController{

    public $actionAccessAuths = array(
        'index'             => 'o_A_Access',
        'SaveComment'       => 'o_A_Adm_Student',
        'DelComment'        => 'o_A_Adm_Student',
        'AsReplied'         => 'o_A_Adm_Student',
        'BeforeForward'     => 'o_A_Adm_Student',
        'Forward'           => 'o_A_Adm_Student',
    );

    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mcampus/feedback/index');

        Yii::import('common.models.feedback.Comments');
    }

    public function actionSelect()
    {
        $sql = "SELECT com_school_id,count(*) as count FROM ivy_comments WHERE com_root_id=0 and com_ifreply=0 group by com_school_id";
        $ret = Yii::app()->db->createCommand($sql)->queryAll();
        foreach($ret as $rt){
            $this->branchSelectParams['branchCount'][$rt['com_school_id']] = $rt['count'];
        }
        $this->render('//layouts/common/branchSelect');
    }

    public function actionIndex($type='')
    {
        Yii::import('common.models.classTeacher.InfopubStaffExtend');
        Yii::import('common.models.calendar.Calendar');

        $ifreply = $type == 'replied' ? 1 : 0;

        $criteria = new CDbCriteria();
        $criteria->compare('t.com_ifreply', $ifreply);
        $criteria->compare('t.com_user_type', 0);
        $criteria->compare('t.com_root_id', 0);
        $criteria->compare('t.com_school_id', $this->branchId);
        $criteria->order = 't.com_created_time desc';

        $count = Comments::model()->count($criteria);
        $pager = new CPagination($count);
        $pager->pageSize=20;
        $pager->applyLimit($criteria);

        $items = Comments::model()->with(array('userWithProfile','replies','classInfo', 'childInfo', 'yInfo'))->findAll($criteria);

        $sql = "SELECT com_school_id,count(*) as count FROM ivy_comments WHERE com_root_id=0 and com_ifreply=0 group by com_school_id";
        $ret = Yii::app()->db->createCommand($sql)->queryAll();
        foreach($ret as $rt){
            $this->branchSelectParams['branchCount'][$rt['com_school_id']] = $rt['count'];
        }

        $this->render('index', array('items'=>$items, 'type'=>$type, 'pager'=>$pager));
    }

    public function actionSaveComment($id=0)
    {
        if($id){
            if(Yii::app()->request->isPostRequest){
                $content = Yii::app()->request->getPost('content', '');
                if($content){
                    $feedback = Comments::model()->findByPk($id);

                    $model = new Comments;
                    $model->com_root_id = $id;
                    $model->com_content = $content;
                    $model->com_child_id = $feedback->com_child_id;
                    $model->com_uid = Yii::app()->user->id;
                    $model->com_class_id = $feedback->com_class_id;
                    $model->com_yid = $feedback->com_yid;
                    $model->com_week_num = $feedback->com_week_num;
                    $model->com_school_id = $feedback->com_school_id;
                    $model->com_user_type = 1;
                    $model->com_created_time = time();
                    if($model->save()){
//                        是否自动标记为已回复
                        $feedback->com_ifreply=1;
                        $feedback->save(false);
                        $user = User::model()->findByPk(Yii::app()->user->id);
                        $data = array(
                            'id' => $id,
                            'cid' => $model->id,
                            'content' => $content,
                            'timestamp' => date('Y-m-d H:i', $model->com_created_time),
                            'timestampshow' => CommonUtils::time_elapsed_string($model->com_created_time),
                            'name' => $user->getName(),
                            'photo' => $user->getPhotoSubUrlforOA(),
                        );
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', '');
                        $this->addMessage('callback', 'cbSaveComment');
                        $this->addMessage('data', $data);
                    }
                    else{
                        $this->addMessage('state', 'fail');
                        $err = current($model->getErrors());
                        $this->addMessage('message', $err[0]);
                    }
                }
                else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', 'not balnk');
                }
            }
        }
        $this->showMessage();
    }

    public function actionDelComment()
    {
        if(Yii::app()->request->isPostRequest){
            $id = Yii::app()->request->getPost('id', 0);
            if($id){
                $model = Comments::model()->findByPk($id);
                if($model->com_uid == Yii::app()->user->id){
                    $model->delete();
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', '');
                }
                else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '只能删除自己提交的信息');
                }
            }
        }
        $this->showMessage();
    }

    public function actionAsReplied()
    {
        if(Yii::app()->request->isPostRequest){
            $id = Yii::app()->request->getPost('id', 0);
            $model = Comments::model()->findByPk($id);
            $model->com_ifreply = 1;
            if($model->save(false)){
                $this->addMessage('state', 'success');
                $this->addMessage('message', '');
            }
            else{
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err[0]);
            }
        }
        $this->showMessage();
    }

    public function actionBeforeForward($id=0, $classid=0)
    {
        if($id && $classid){
            Yii::import('common.models.feedback.CommentForward');
            Yii::import('common.models.classTeacher.ClassTeacher');

            $uids = array();
            $items = CommentForward::model()->findAllByAttributes(array('post_id'=>$id));
            foreach($items as $item){
                foreach(unserialize($item->to_userid) as $userid){
                    $uids[$userid] = $userid;
                }
            }

            $forwards = array();
            if($uids){
                $criteria = new CDbCriteria();
                $criteria->compare('t.uid', $uids);
                $uItems = User::model()->with('profile')->findAll($criteria);
                foreach($uItems as $item){
                    $forwards[$item->uid] = $item->getName();
                }
            }

            $teachers = array();
            $criteria = new CDbCriteria();
            $criteria->compare('t.classid', $classid);
            $criteria->compare('userWithProfile.level', 1);
            $criteria->order='weight';
            $tItems = ClassTeacher::model()->with('userWithProfile')->findAll($criteria);
            foreach($tItems as $item){
                $teachers[$item->teacherid] = $item->userWithProfile->getName();
            }


            echo CJSON::encode(array('forwards'=>$forwards, 'teachers'=>$teachers));
        }
    }

    public function actionForward()
    {
        if(Yii::app()->request->isPostRequest){
            $id = Yii::app()->request->getPost('id', 0);
            $uid = Yii::app()->request->getPost('uid', array());

            if($id && $uid){
                Yii::import('common.models.feedback.CommentForward');

                $emails = array();
                $criteria = new CDbCriteria();
                $criteria->compare('uid', $uid);
                $items = User::model()->findAll($criteria);
                foreach($items as $item){
                    $emails[] = $item->email;
                }

                $feedback = Comments::model()->with(array('userWithProfile', 'childInfo', 'classInfo'))->findByPk($id);

                $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
                $emailsubject = 'Weekly journal feedback 周报告反馈';
                $mailer->Subject = (OA::isProduction()) ? $emailsubject : '[测试] '.$emailsubject;
                foreach($emails as $email){
                    $mailer->AddAddress($email);
                }
                $mailer->iniMail( OA::isProduction() );
                $mailer->getView('journal_feedback', array(
                    'feedback' => $feedback,
                    'parent_name' => $feedback->userWithProfile->getName(),
                    'child_name' => $feedback->childInfo->getChildName(),
                    'class_name' => $feedback->classInfo->title,
                    'week_num' => $feedback->com_week_num,
                    'update_time' => date('Y-m-d H:i', $feedback->com_created_time),
                ), 'main');
                if($mailer->send()){
                    $model = new CommentForward;
                    $model->post_id = $id;
                    $model->to_userid = serialize($uid);
                    $model->update_user = Yii::app()->user->id;
                    $model->update_time = time();
                    if($model->save()){
                        $this->addMessage('state', 'success');
                        $this->addMessage('callback', 'cbSend');
                        $this->addMessage('message', Yii::t('campus', 'The message has been send successflly.'));
                    }
                    else{
                        $this->addMessage('state', 'fail');
                        $err = current($model->getErrors());
                        $this->addMessage('message', $err[0]);
                    }
                }
                else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('campus', 'Delivery failed.'));
                }
            }
        }
        $this->showMessage();
    }
}

?>
