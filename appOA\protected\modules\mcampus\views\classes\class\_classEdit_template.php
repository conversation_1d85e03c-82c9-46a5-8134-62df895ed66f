<?php $labels = IvyClass::attributeLabels();
$calendarSelect = $this->calendarStartYear;
foreach($calendarSelect as $_k=>$_v){
    $calendarSelect[$_k] = sprintf("%d-%d", $_v, $_v+1);
}
for($i=8;$i<19;$i++){$hours[$i]=$i;}
for($i=0;$i<13;$i++){$_v = str_pad($i*5,2,'0',STR_PAD_LEFT); $mins[$_v]=$_v;}

$classCodes = 'ABCDEF';

$criteria = new CDbCriteria();
$criteria->compare('schoolid', $this->branchId);
$criteria->compare('status', 1);
$criteria->order = 'code';
$modals = EClassroom::model()->findAll($criteria);
?>

<?php
echo CHtml::hiddenField('IvyClass[classid]','<%= classid %>',array('encode'=>false));
echo CHtml::hiddenField('IvyClass[schoolid]','<%= schoolid %>',array('encode'=>false));
echo CHtml::hiddenField('IvyClass[yid]','<%= yid %>',array('encode'=>false));
?>
<div class="form-group">
    <?php echo CHtml::label($labels['yid'], CHtml::getIdByName('IvyClass[yid]'), array('class'=>'col-sm-3 control-label'));?>
    <div class="col-sm-9">
        <?php echo CHtml::dropDownList('yid4show','<%= yid %>', $calendarSelect, array('class'=>'form-control select_3','encode'=>false, 'disabled'=>'disabled'));?>
    </div>
</div>

<div class="form-group" model-attribute="classtype">
    <?php echo CHtml::label($labels['classtype'], CHtml::getIdByName('IvyClass[classtype]'), array('class'=>'col-sm-3 control-label'));?>
    <div class="col-sm-9">
        <?php echo CHtml::dropDownList('IvyClass[classtype]','<%= classtype %>', $classTypes,
            array('class'=>'form-control select_3','encode'=>false, 'onChange'=>'emptyTitle()', 'empty'=>Yii::t('global','Please Select')));?>
    </div>
</div>

<div class="form-group" model-attribute="title">
    <?php echo CHtml::label($labels['title'], CHtml::getIdByName('IvyClass[title]'), array('class'=>'col-sm-3 control-label'));?>
    <div class="col-sm-9">
        <div class="input-group">
            <?php echo CHtml::textField('IvyClass[title]', '<%= title %>',
                array('class'=>'form-control','encode'=>false, 'readonly'=>'readonly','data-toggle'=>"collapse", 'data-target'=>"#title-selection-box",
                    onclick=>"displayTitles()"
                ));?>
            <span onclick="displayTitles()" class="input-group-addon" data-toggle="collapse" data-target="#title-selection-box"><a href="#"><span class="glyphicon glyphicon-th"></span></a></span>
        </div>
        <div id="title-selection-box" class="panel-collapse collapse background-gray">

            <div class="panel panel-default">
                <div class="panel-heading">
                    <div class="form-inline">
                        <div class="checkbox">
                            <label class="mr15">
                                备选名称：
                            </label>
                        </div>
                        <?php
                        for($i=0; $i<strlen($classCodes);$i++):
                            ?>
                            <div class="checkbox">
                                <label class="mr15" onclick="showTitles();">
                                    <input genTitleList classCode="<?php echo $classCodes[$i];?>" class="classCode" name="classCode[]" type="checkbox" <?php echo ($i<3)?"checked":"";?> > <?php echo $classCodes[$i];?>
                                </label>
                            </div>
                        <?php
                        endfor;
                        ?>
                        <div class="checkbox" id="mixed-class" onclick="showTitles();">
                                <input genTitleList mixed type="checkbox"> 混龄班
                            </label>
                        </div>
                    </div>
                </div>
                <div class="panel-body">
                    <div id="title-list" class="list-group">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="form-group" model-attribute="capacity">
    <?php echo CHtml::label($labels['capacity'], CHtml::getIdByName('IvyClass[capacity]'), array('class'=>'col-sm-3 control-label'));?>
    <div class="col-sm-9">
        <?php echo CHtml::textField('IvyClass[capacity]', '<%= capacity %>', array('class'=>'form-control length_1','encode'=>false));?>
    </div>
</div>

<div class="form-group" model-attribute="stat" id="class_stat">
    <?php echo CHtml::label($labels['stat'], CHtml::getIdByName('IvyClass[stat]'), array('class'=>'col-sm-3 control-label'));?>
    <div class="col-sm-9">
        <?php echo CHtml::dropDownList('IvyClass[stat]','<%= stat %>', array(0=>Yii::t('campus', 'Preparing'), 10=>Yii::t('campus', 'Open'),20=>Yii::t('campus', 'Closed')),
            array('class'=>'form-control select_3', 'encode'=>false, 'empty'=>Yii::t('global', 'Please Select')));?>
    </div>
</div>

<?php if($modals):?>
<div class="form-group" model-attribute="introduction">
    <?php echo CHtml::label($labels['introduction'], CHtml::getIdByName('IvyClass[introduction]'), array('class'=>'col-sm-3 control-label'));?>
    <div class="col-sm-9">
        <?php echo CHtml::dropDownList('IvyClass[introduction]','<%= introduction %>', CHtml::listData($modals, 'id', 'code'),
            array('class'=>'form-control select_3','encode'=>false, 'empty'=>Yii::t('global','Please Select')));?>
    </div>
</div>
<?php endif;?>

<div class="form-group" id="period">
    <?php echo CHtml::label($labels['periodData'], CHtml::getIdByName('IvyClass[periodData]'), array('class'=>'col-sm-3 control-label'));?>
    <div class="col-sm-9 form-inline">
        <?php echo CHtml::dropDownList('IvyClass[periodData][0]','', $hours, array('class'=>'form-control select_2','encode'=>false));?>
        <?php echo CHtml::dropDownList('IvyClass[periodData][1]','', $mins, array('class'=>'form-control select_2','encode'=>false));?>
        -
        <?php echo CHtml::dropDownList('IvyClass[periodData][2]','', $hours, array('class'=>'form-control select_2','encode'=>false));?>
        <?php echo CHtml::dropDownList('IvyClass[periodData][3]','', $mins, array('class'=>'form-control select_2','encode'=>false));?>

        <button onclick="saveDefaultClassTime(this)" type="button" class="btn btn-default mr5"><span class="glyphicon glyphicon-floppy-disk"></span> 保存为默认值</button>
    </div>
</div>