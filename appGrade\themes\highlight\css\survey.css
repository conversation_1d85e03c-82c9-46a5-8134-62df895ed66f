.survey_box {
    background: #FEF6EB;
}
.survey_box .template {
    padding: 0px;
    margin: 20px 0 0 20px;
}
.survey_box .topic {
    margin: 0px;
    padding: 0px;
    margin-top: 20px;
}
.survey_box .topic h3 {
    width: 40px;
    height: 30px;
    font-size: 13px;
    color: #585947;
    float: left;
    display: block;
    font-family: 'Trebuchet MS';
    background: #E8E6DA;
    margin-left: 0px;
    border-top: 2px solid #E8E6DA;
    text-align: center;
    line-height: 30px;
    position: relative;
}
.survey_box .topic h3 .h3required{
    top: 0;
    right: 0;
    width: 15px;
    height: 15px;
    color: #C73B0B;
    display: block;
    line-height: 15px;
    text-align: center;
    position: absolute;
}
.survey_box .topic .item {
    float: left;
    width: 550px;
    padding-top: 4px;
    padding-left: 10px;
    border-top: 2px solid #E8E6DA;
}
.survey_box .required h3 {
    border-top: 2px solid #C73B0B;
    background: #C73B0B;
    color: #fff;
}
.survey_box .required .item {
    border-top: 2px solid #C73B0B;
}
.survey_box .topic span.warn {
    display: none;
}
.survey_box .required span.warn {
    display: inline-block;
    border: 1px solid #FEBF90;
    background: #FFFBE1;
    color: #C73B0B;
    padding: 2px 4px;
    margin: 0;
}
.survey_box .topic hr {
    height: 0;
    border-top: 1px dotted #ccc;
    background: #f2f2f2;
    color: #fff;
    margin: 4px 0;
}
.survey_box .topic .item .userinput {
    font-size: 13px;
}
.survey_box .topic .item .userinput textarea {
    border: 1px solid #dedede;
    padding: 2px;
    width: 100%;
    font-size: 12px;
    font-family: "Microsoft Yahei";
}
.survey_box .topic .item p {
    padding-bottom: 6px;
    line-height: 22px;
}
.survey_box .topic .item .option {
    padding-bottom: 6px;
    margin: 5px;
    list-style-type: none;
}
.survey_box .topic .item .title {
    color: #444;
    font-size: 13px;
}
.survey_box .topic .item .title span {
    color: #C73B0B;
    padding-right: 0.5em;
}
.survey_box .topic .item .en {
    font-family: 'Trebuchet MS';
}
.item p.option, item p.feedback {
    padding: 4px 0;
}
.survey_box .topic .item .option span.qLabel:hover {
    text-decoration: underline;
}
/*
 * 美化按钮
 */
.topic .option .qOption {
/*    cursor: pointer;*/
    margin-right: 4px;
    padding: 0;
    position: relative;
}
.topic .option .qOption label {
/*    display: block;*/
    cursor: pointer;
    padding: 4px;
}
input.cb, input.rb {
    position: absolute;
    left: 6px;
    top: 2px;
}
label.cb_off img, label.cb_on img, label.rb_off img, label.rb_on img {
    vertical-align: top;
    width: 20px;
    height: 20px;
    margin: -1px 8px 1px 1px;
    position: relative;
    z-index: 11;
}
label.rb_on img {
    background: url("../images/sprite_checkmark_alpha.png") repeat scroll -20px 0 transparent;
}
label.rb_off img {
    background: url("../images/sprite_checkmark_alpha.png") repeat scroll 0 0 transparent;
}
label.cb_on img {
    background: url("../images/sprite_checkmark_alpha.png") repeat scroll -60px 0 transparent;
}
label.cb_off img {
    background: url("../images/sprite_checkmark_alpha.png") repeat scroll -40px 0 transparent;
}
div.hlbl, span.hlbl, #altver {
    position: absolute;
    left: 0px;
    top: -5000px;
    width: 1px;
    height: 1px;
    overflow: hidden;
}
/*
 * 美化按钮 end
 */
.topic .option .qOption {
/*    width: 150px;
    float: left;*/
    margin: 10px;
}
/*
 * 首页的 问卷 box
 */

#survey_box {
    height: 161px;
    width: 660px;
    box-shadow: 0 0 4 pxrgba(153, 221, 133, 0.7);
}
.survey-box {
    background-color: #F3F8EE;
    position: relative;
    margin-bottom: 8px;
}
#survey_box .thanks {
    height: 141px;
}
.thanks {
    padding: 10px 20px;
    background-image: url('../images/survey_bg.png');
}
#survey_box h3 {
    position: absolute;
    top: 20px;
    left: 20px;
    color: #FF663A;
}
#survey_box .cn h3 {
    font-size: 18px;
    font-family: "Microsoft Yahei";
    line-height: 22px;
    font-weight: normal;
}
#survey_box .en h3 {
    font-size: 18px;
    font-family: 'Trebuchet MS';
    font-style: italic;
    line-height: 22px;
}
#survey_box .cn h5 {
    font-size: 13px;
    font-weight: normal;
    font-family: "Microsoft Yahei";
    line-height: 22px;
}
#survey_box .en h5 {
    font-size: 13px;
    font-weight: normal;
    font-family: 'Trebuchet MS';
    font-style: italic;
    line-height: 22px;
}
#survey_box h5 {
    position: absolute;
    top: 80px;
    left: 100px;
    color: #FF663A;
}
#survey_box h5 a {
    background: url("../images/pollitem.gif") no-repeat scroll 0 0 transparent;
    padding-left: 20px;
}
#survey_box h5 span {
    color: #999;
    display: block;
    font-size: 12px;
    padding-left: 4em;
}
.thanks a:active, .thanks a:link, .thanks a:visited {
    color: #FF8F00;
}
.votebox .voteinfo .desc{
    background-color:#f2f2f2;
	color:#514721;
	border-color:#58A8E3;
    margin:1em 0;
	border: 1px solid #fff;
	padding:15px;
	background-repeat: no-repeat;
	background-position: 10px center;
	border-radius:2px;
}