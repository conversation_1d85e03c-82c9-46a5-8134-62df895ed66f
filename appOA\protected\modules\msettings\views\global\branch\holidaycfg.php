<?php
$bModel = Branch::model();
$bModel->getMetaData()->addRelation(
    'holidaycfg',
    array(
        CActiveRecord::HAS_ONE,
        'BranchVar',
        'branchid',
        'on'=>'category=:category',
        'params'=>array(':category'=>'holidaycfg'),
    )
);
$crit = new CDbCriteria;
$crit->index = 'branchid';
//$crit->compare('type', '<>'.Branch::TYPE_OFFICE);
$crit->compare('status', Branch::STATUS_ACTIVE);
$crit->order='`group` ASC';
$models = $bModel->with('holidaycfg')->findAll($crit);
?>
<div class="col-md-5">
    <?php echo CHtml::beginForm('', 'post', array('class'=>'J_ajaxForm')); ?>
    <table class="table table-hover">
        <thead>
        <tr>
            <th>Campus</th>
            <th>开启</th>
            <th>假期有效期配置，请用半角分号（;）隔开</th>
        </tr>
        </thead>
        <tbody>
        <?php
        foreach($models as $branchid => $branch):
            if($branch->holidaycfg==null){
                $branch->holidaycfg = new BranchVar();
            }
            ?>
            <tr>
                <th style="vertical-align:middle">
                    <?php echo '<span class="glyphicon glyphicon-tags program'.$branch['group'].'"></span>'; ?>
                    <?php echo $branch['title'];?>
                </th>
                <td><div class="checkbox"><label><?php echo CHtml::activeCheckBox($branch->holidaycfg, 'flag', array('name'=>'BranchVar['.$branchid.'][flag]')); ?> 启用</label></div></td>
                <td><?php echo CHtml::activeTextField($branch->holidaycfg, "data", array('name'=>'BranchVar['.$branchid.'][data]', 'class'=>'form-control')); ?></td>
            </tr>
        <?php
        endforeach;
        ?>
        </tbody>
        <tfoot>
        <tr>
            <td colspan="3">
                <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
            </td>
        </tr>
        </tfoot>
    </table>
    <?php echo CHtml::endForm(); ?>
</div>
