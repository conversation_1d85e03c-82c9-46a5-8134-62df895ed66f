<script language="javascript">
    function viewSchedule(_this)
    {
        window.location = "<?php echo Yii::app()->getController()->createUrl('//portfolio/portfolio/teachingContent', array('classid'=>$this->classId, 'yid'=>$this->yId));?>&month="+_this.value;
    }
</script>
<div class="pop-box">
    <h5><?php echo Yii::t("portfolio", 'Monthly Teaching Plan');?></h5>
    <div class="sh-tools">
        <div class="fl"><?php echo CHtml::dropDownList('weeknum', $this->month, $months, array('onchange'=>'viewSchedule(this)'));?></div>
        <div class="fr" style="line-height: 24px; margin-right: 60px;">
            <?php
            $minm = min(array_keys($months));
            $maxm = max(array_keys($months));
            ?>
            <?php if ($this->month != $minm):?>
                <?php
                $linkText = "<span class='prev'>" . Yii::t("portfolio", "Prev Month") ."</span>";
                echo CHtml::link($linkText, Yii::app()->getController()->createUrl('//portfolio/portfolio/teachingContent', array('classid'=>$this->classId, 'yid'=>$this->yId, 'month'=>$pmonth)), array('class'=>'alink'))?>
            <?php endif;?>
            <?php if ($this->month != $maxm):?>
                <?php
                $linkText = "<span class='next'>" . Yii::t("portfolio", "Next Month") ."</span>";
                echo CHtml::link($linkText, Yii::app()->getController()->createUrl('//portfolio/portfolio/teachingContent', array('classid'=>$this->classId, 'yid'=>$this->yId, 'month'=>$nmonth)), array('class'=>'alink'))?>
            <?php endif;?>
        </div>
        <div class="clear"></div>
    </div>
    <?php if(is_object($model) && is_object($model->content)):?>
    <?php if($model->stat_cn == 20):?>
    <div>
        <p><?php echo Yii::t("portfolio", '<strong>Note:</strong> The information below is a summary of this month’s classroom learning. As children learn from repetition, parents can support children’s learning by singing the songs, reciting the rhymes and reading the stories at home. Parents can also discuss the monthly theme and the activities that took place with their child. Children are different in their own ways, so each child’s developmental progress is unique. Some children can grasp certain concepts quickly, while comprehension of other concepts may need more time. Differences in learning is natural and to be expected. Therefore, the information provided below should not to be used as a checklist to assess your child.');?></p>
        <p><?php echo Yii::t("portfolio", 'Monthly Learning Content', null, null, 'zh_cn');?></p>
        <table class="gtab" width="95%" cellpadding="1" cellspacing="1" border="0">
            <tr class="bg1">
                <th width="30%"><?php echo Yii::t("portfolio", 'Unit Theme', null, null, 'zh_cn');?></th>
                <td><?php echo CHtml::encode($model->content->title_cn);?></td>
            </tr>
            <tr class="bg2">
                <th><?php echo Yii::t("portfolio", 'Big Idea', null, null, 'zh_cn');?></th>
                <td><?php echo nl2br(CHtml::encode($model->content->newIdea_cn));?></td>
            </tr>
            <tr class="bg2">
                <th><?php echo Yii::t("portfolio", 'Key Questions', null, null, 'zh_cn');?></th>
                <td><?php echo nl2br(CHtml::encode($model->content->questions_cn));?></td>
            </tr>
            <tr class="bg2">
                <th><?php echo Yii::t("portfolio", 'Activities', null, null, 'zh_cn');?></th>
                <td><?php echo nl2br(CHtml::encode($model->content->activities_cn));?></td>
            </tr>
            <tr class="bg1">
                <th><?php echo Yii::t("portfolio", 'Field Trips', null, null, 'zh_cn');?></th>
                <td><?php echo nl2br(CHtml::encode($model->content->trips_cn));?></td>
            </tr>
            <tr class="bg2">
                <th><?php echo Yii::t("portfolio", 'Children\'s Song/Rhymes/Stories', null, null, 'zh_cn');?></th>
                <td><?php echo nl2br(CHtml::encode($model->content->song_cn));?></td>
            </tr>
            <tr class="bg1">
                <th><?php echo Yii::t("portfolio", 'Special Festivals', null, null, 'zh_cn');?></th>
                <td><?php echo nl2br(CHtml::encode($model->content->festivals_cn));?></td>
            </tr>
        </table>
    </div>
    <div>
        <p><?php echo Yii::t("portfolio", 'Monthly Learning Objectives', null, null, 'zh_cn');?></p>
        <table class="gtab" width="95%" cellpadding="1" cellspacing="1" border="0">
<!--            <tr class="bg1">-->
<!--                <th width="30%">--><?php //echo Yii::t("portfolio", 'Language and Literacy', null, null, 'zh_cn');?><!--</th>-->
<!--                <td>--><?php //echo CHtml::encode($model->content->language_cn);?><!--</td>-->
<!--            </tr>-->
<!--            <tr class="bg2">-->
<!--                <th>--><?php //echo Yii::t("portfolio", 'Mathematical Thinking', null, null, 'zh_cn');?><!--</th>-->
<!--                <td>--><?php //echo nl2br(CHtml::encode($model->content->mathematical_cn));?><!--</td>-->
<!--            </tr>-->
<!--            <tr class="bg1">-->
<!--                <th>--><?php //echo Yii::t("portfolio", 'Science Thinking', null, null, 'zh_cn');?><!--</th>-->
<!--                <td>--><?php //echo nl2br(CHtml::encode($model->content->science_cn));?><!--</td>-->
<!--            </tr>-->
<!--            <tr class="bg2">-->
<!--                <th>--><?php //echo Yii::t("portfolio", 'Physical/Health', null, null, 'zh_cn');?><!--</th>-->
<!--                <td>--><?php //echo nl2br(CHtml::encode($model->content->physical_cn));?><!--</td>-->
<!--            </tr>-->
<!--            <tr class="bg1">-->
<!--                <th>--><?php //echo Yii::t("portfolio", 'Social/Personal', null, null, 'zh_cn');?><!--</th>-->
<!--                <td>--><?php //echo nl2br(CHtml::encode($model->content->social_cn));?><!--</td>-->
<!--            </tr>-->
<!--            <tr class="bg2">-->
<!--                <th>--><?php //echo Yii::t("portfolio", 'Art', null, null, 'zh_cn');?><!--</th>-->
<!--                <td>--><?php //echo nl2br(CHtml::encode($model->content->art_cn));?><!--</td>-->
<!--            </tr>-->
<!--            <tr class="bg2">-->
<!--                <th>--><?php //echo Yii::t("portfolio", 'Social Studies', null, null, 'zh_cn');?><!--</th>-->
<!--                <td>--><?php //echo nl2br(CHtml::encode($model->content->social_studies_cn));?><!--</td>-->
<!--            </tr>-->
            <tr class="bg2">
                <th><?php echo Yii::t("portfolio", 'Physical & Health', null, null, 'zh_cn');?></th>
                <td><?php echo nl2br(CHtml::encode($model->content->newPhysical_cn));?></td>
            </tr>
            <tr class="bg2">
                <th><?php echo Yii::t("portfolio", 'Language', null, null, 'zh_cn');?></th>
                <td><?php echo nl2br(CHtml::encode($model->content->newLanguage_cn));?></td>
            </tr>
            <tr class="bg2">
                <th><?php echo Yii::t("portfolio", 'Social', null, null, 'zh_cn');?></th>
                <td><?php echo nl2br(CHtml::encode($model->content->newSocial_cn));?></td>
            </tr>
            <tr class="bg2">
                <th><?php echo Yii::t("portfolio", 'Science & Math', null, null, 'zh_cn');?></th>
                <td><?php echo nl2br(CHtml::encode($model->content->newScience_cn));?></td>
            </tr>
            <tr class="bg2">
                <th><?php echo Yii::t("portfolio", 'Art', null, null, 'zh_cn');?></th>
                <td><?php echo nl2br(CHtml::encode($model->content->newArt_cn));?></td>
            </tr>
        </table>
    </div>
    <?php endif;?>
    <?php if($model->stat_en == 20):?>
    <?php if($model->content->content_en || $model->content->objectives_en):?>
    <div>
        <p><?php echo Yii::t("portfolio", 'Monthly Learning Content', null, null, 'us_en');?></p>
        <table class="gtab" width="95%" cellpadding="1" cellspacing="1" border="0">
<!--            <tr class="bg1">-->
<!--                <th width="30%">--><?php //echo Yii::t("portfolio", 'Monthly Theme', null, null, 'us_en');?><!--</th>-->
<!--                <td>--><?php //echo CHtml::encode($model->content->title_en);?><!--</td>-->
<!--            </tr>-->
<!--            <tr class="bg2">-->
<!--                <th>--><?php //echo Yii::t("portfolio", 'Activities', null, null, 'us_en');?><!--</th>-->
<!--                <td>--><?php //echo nl2br(CHtml::encode($model->content->activities_en));?><!--</td>-->
<!--            </tr>-->
<!--            <tr class="bg1">-->
<!--                <th>--><?php //echo Yii::t("portfolio", 'Field Trips', null, null, 'us_en');?><!--</th>-->
<!--                <td>--><?php //echo nl2br(CHtml::encode($model->content->trips_en));?><!--</td>-->
<!--            </tr>-->
<!--            <tr class="bg2">-->
<!--                <th>--><?php //echo Yii::t("portfolio", 'Children\'s Song/Rhymes/Stories', null, null, 'us_en');?><!--</th>-->
<!--                <td>--><?php //echo nl2br(CHtml::encode($model->content->song_en));?><!--</td>-->
<!--            </tr>-->
<!--            <tr class="bg1">-->
<!--                <th>--><?php //echo Yii::t("portfolio", 'Special Festivals', null, null, 'us_en');?><!--</th>-->
<!--                <td>--><?php //echo nl2br(CHtml::encode($model->content->festivals_en));?><!--</td>-->
<!--            </tr>-->
            <tr class="bg1">
                <th width="30%"><?php echo Yii::t("portfolio", 'Unit Theme', null, null, 'us_en');?></th>
                <td><?php echo CHtml::encode($model->content->title_en);?></td>
            </tr>
            <tr class="bg2">
                <th><?php echo Yii::t("portfolio", 'Big Idea', null, null, 'us_en');?></th>
                <td><?php echo nl2br(CHtml::encode($model->content->newIdea_en));?></td>
            </tr>
            <tr class="bg2">
                <th><?php echo Yii::t("portfolio", 'Activities', null, null, 'us_en');?></th>
                <td><?php echo nl2br(CHtml::encode($model->content->activities_en));?></td>
            </tr>
            <tr class="bg2">
                <th><?php echo Yii::t("portfolio", 'Key Questions', null, null, 'us_en');?></th>
                <td><?php echo nl2br(CHtml::encode($model->content->questions_en));?></td>
            </tr>
            <tr class="bg1">
                <th><?php echo Yii::t("portfolio", 'Field Trips', null, null, 'us_en');?></th>
                <td><?php echo nl2br(CHtml::encode($model->content->trips_en));?></td>
            </tr>
            <tr class="bg2">
                <th><?php echo Yii::t("portfolio", 'Children\'s Song/Rhymes/Stories', null, null, 'us_en');?></th>
                <td><?php echo nl2br(CHtml::encode($model->content->song_en));?></td>
            </tr>
            <tr class="bg1">
                <th><?php echo Yii::t("portfolio", 'Special Festivals', null, null, 'us_en');?></th>
                <td><?php echo nl2br(CHtml::encode($model->content->festivals_en));?></td>
            </tr>
        </table>
    </div>
    <div>
        <p><?php echo Yii::t("portfolio", 'Monthly Learning Objectives', null, null, 'us_en');?></p>
        <table class="gtab" width="95%" cellpadding="1" cellspacing="1" border="0">
<!--            <tr class="bg1">-->
<!--                <th width="30%">--><?php //echo Yii::t("portfolio", 'Language and Literacy', null, null, 'us_en');?><!--</th>-->
<!--                <td>--><?php //echo nl2br(CHtml::encode($model->content->language_en));?><!--</td>-->
<!--            </tr>-->
<!--            <tr class="bg2">-->
<!--                <th>--><?php //echo Yii::t("portfolio", 'Mathematical Thinking', null, null, 'us_en');?><!--</th>-->
<!--                <td>--><?php //echo nl2br(CHtml::encode($model->content->mathematical_en));?><!--</td>-->
<!--            </tr>-->
<!--            <tr class="bg1">-->
<!--                <th>--><?php //echo Yii::t("portfolio", 'Science Thinking', null, null, 'us_en');?><!--</th>-->
<!--                <td>--><?php //echo nl2br(CHtml::encode($model->content->science_en));?><!--</td>-->
<!--            </tr>-->
<!--            <tr class="bg2">-->
<!--                <th>--><?php //echo Yii::t("portfolio", 'Physical/Health', null, null, 'us_en');?><!--</th>-->
<!--                <td>--><?php //echo nl2br(CHtml::encode($model->content->physical_en));?><!--</td>-->
<!--            </tr>-->
<!--            <tr class="bg1">-->
<!--                <th>--><?php //echo Yii::t("portfolio", 'Social/Personal', null, null, 'us_en');?><!--</th>-->
<!--                <td>--><?php //echo nl2br(CHtml::encode($model->content->social_en));?><!--</td>-->
<!--            </tr>-->
<!--            <tr class="bg2">-->
<!--                <th>--><?php //echo Yii::t("portfolio", 'Art', null, null, 'us_en');?><!--</th>-->
<!--                <td>--><?php //echo nl2br(CHtml::encode($model->content->art_en));?><!--</td>-->
<!--            </tr>-->
<!--            <tr class="bg2">-->
<!--                <th>--><?php //echo Yii::t("portfolio", 'Social Studies', null, null, 'us_en');?><!--</th>-->
<!--                <td>--><?php //echo nl2br(CHtml::encode($model->content->social_studies_en));?><!--</td>-->
<!--            </tr>-->
            <tr class="bg2">
                <th><?php echo Yii::t("portfolio", 'Physical & Health', null, null, 'us_en');?></th>
                <td><?php echo nl2br(CHtml::encode($model->content->newPhysical_en));?></td>
            </tr>
            <tr class="bg2">
                <th><?php echo Yii::t("portfolio", 'Language', null, null, 'us_en');?></th>
                <td><?php echo nl2br(CHtml::encode($model->content->newLanguage_en));?></td>
            </tr>
            <tr class="bg2">
                <th><?php echo Yii::t("portfolio", 'Social', null, null, 'us_en');?></th>
                <td><?php echo nl2br(CHtml::encode($model->content->newSocial_en));?></td>
            </tr>
            <tr class="bg2">
                <th><?php echo Yii::t("portfolio", 'Science & Math', null, null, 'us_en');?></th>
                <td><?php echo nl2br(CHtml::encode($model->content->newScience_en));?></td>
            </tr>
            <tr class="bg2">
                <th><?php echo Yii::t("portfolio", 'Art', null, null, 'us_en');?></th>
                <td><?php echo nl2br(CHtml::encode($model->content->newArt_en));?></td>
            </tr>
        </table>
    </div>
    <?php else:?>
        <div>
            <p><?php echo Yii::t("portfolio", '以下是英文教学的月度整体规划，更详细的课程内容参见艾毅在线周课表。由于幼儿园阶段孩子的母语（中文）也还处在学习与发展过程中，所以对于他们英文学习的主要期待应该是开始领悟部分含义（2-3岁），逐渐唱一些儿歌或讲一些简单的单词（3-4岁），说出几首学过的儿歌、童谣，使用几个学过的词汇（4-5岁），开始试着用学过的词汇组成简单的句子进行交流（5-6岁）。');?></p>
            <p><?php echo Yii::t("portfolio", 'Monthly MIK English Language Learning Content', null, null, 'us_en');?></p>
            <table class="gtab" width="95%" cellpadding="1" cellspacing="1" border="0">
                <tr class="bg1">
                    <th width="30%"><?php echo Yii::t("portfolio", 'Words', null, null, 'us_en');?></th>
                    <td><?php echo nl2br(CHtml::encode($model->content->words));?></td>
                </tr>
                <tr class="bg2">
                    <th><?php echo Yii::t("portfolio", 'Sentences', null, null, 'us_en');?></th>
                    <td><?php echo nl2br(CHtml::encode($model->content->sentences));?></td>
                </tr>
                <tr class="bg1">
                    <th><?php echo Yii::t("portfolio", 'Letters', null, null, 'us_en');?></th>
                    <td><?php echo nl2br(CHtml::encode($model->content->letters));?></td>
                </tr>
                <tr class="bg2">
                    <th><?php echo Yii::t("portfolio", 'Numbers', null, null, 'us_en');?></th>
                    <td><?php echo nl2br(CHtml::encode($model->content->numbers));?></td>
                </tr>
                <tr class="bg1">
                    <th><?php echo Yii::t("portfolio", 'Colors', null, null, 'us_en');?></th>
                    <td><?php echo nl2br(CHtml::encode($model->content->colors));?></td>
                </tr>
                <tr class="bg2">
                    <th><?php echo Yii::t("portfolio", 'Shapes', null, null, 'us_en');?></th>
                    <td><?php echo nl2br(CHtml::encode($model->content->shapes));?></td>
                </tr>
                <tr class="bg1">
                    <th><?php echo Yii::t("portfolio", 'Animals', null, null, 'us_en');?></th>
                    <td><?php echo nl2br(CHtml::encode($model->content->animals));?></td>
                </tr>
                <tr class="bg2">
                    <th><?php echo Yii::t("portfolio", 'Body Parts', null, null, 'us_en');?></th>
                    <td><?php echo nl2br(CHtml::encode($model->content->body));?></td>
                </tr>
                <tr class="bg1">
                    <th><?php echo Yii::t("portfolio", 'Others', null, null, 'us_en');?></th>
                    <td><?php echo nl2br(CHtml::encode($model->content->others));?></td>
                </tr>
            </table>
        </div>
    <?php endif;?>
    <?php endif;?>
    <?php endif;?>
</div>