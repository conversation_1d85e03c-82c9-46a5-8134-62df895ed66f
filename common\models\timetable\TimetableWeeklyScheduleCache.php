<?php

/**
 * This is the model class for table "ivy_timetable_weekly_schedule_cache".
 *
 * The followings are the available columns in table 'ivy_timetable_weekly_schedule_cache':
 * @property integer $id
 * @property integer $tid
 * @property integer $term
 * @property integer $type
 * @property integer $target_id
 * @property string $data
 * @property integer $updated_at
 */
class TimetableWeeklyScheduleCache extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_timetable_weekly_schedule_cache';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('tid, type, term, target_id', 'required'),
			array('tid, type, term, target_id, updated_at', 'numerical', 'integerOnly'=>true),
			array('data', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, tid, type, term, target_id, data, updated_at', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'tid' => 'Tid',
			'type' => 'Type',
			'term' => 'term',
			'target_id' => 'Target',
			'data' => 'Data',
			'updated_at' => 'Updated At',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('tid',$this->tid);
		$criteria->compare('type',$this->type);
		$criteria->compare('target_id',$this->target_id);
		$criteria->compare('data',$this->data,true);
		$criteria->compare('updated_at',$this->updated_at);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return TimetableWeeklyScheduleCache the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	// 增加孩子缓存
	public function childCache($child_id, $tid, $semesterId)
	{
	    $criteria = new CDbCriteria;
	    $criteria->compare('child_id', $child_id);
	    $criteria->compare('status', 1);
	    $criteria->compare('tid', $tid);
	    $criteria->order = "updated_at DESC";
	    $studentModel = TimetableStudentData::model()->findAll($criteria);
	    $all = array();
	    $last = array();
	    $next = array();
	    $cackeModel = array();
	    if($studentModel) {
	        foreach ($studentModel as $value){
	            if($value->term == 0){
	                $all[] = $value->course_id;
	            }
	            if($value->term == 1){
	                $last[] = $value->course_id;
	            }
	            if($value->term == 2){
	                $next[] = $value->course_id;
	            }

	        }

	        // 全部
	        $courseModel = array();
	        if($all) {
	            $criteria = new CDbCriteria;
	            $criteria->compare('course_id', $all);
	            $courseModel = TimetableCourseData::model()->findAll($criteria);
	        }
	        $courseDataAll = array();
	        if($courseModel) {
	            foreach ($courseModel as $item) {
	                $courseDataAll[$item->weekday . '-' . $item->period][] = $item->course_code;
	            }
	        }

	        $courseData2All= array();
	        if($courseDataAll){
	            foreach ($courseDataAll as $k=>$value) {
	                $courseData2All[$k] = implode(',', $value);
	            }
	        }


	        // 上学期
	        $courseModelList = array();
	        if($last) {
	            $criteria = new CDbCriteria;
	            $criteria->compare('course_id', $last);
	            $courseModelList = TimetableCourseData::model()->findAll($criteria);
	        }

	        $courseDatabAist = array();
	        if($courseModelList) {
	            foreach ($courseModelList as $item) {
	                $courseDatabAist[$item->weekday . '-' . $item->period][] = $item->course_code;
	            }
	        }

	        if($courseDatabAist){
	            $courseDataLists = array();
	            if($courseDataAll){
	                $courseDataLists = $courseDatabAist + $courseDataAll;
	            }else{
	                $courseDataLists = $courseDatabAist;
	            }

	            foreach ($courseDataLists as $k=>$value) {
	                $courseData2List[$k] = implode(',', $value);
	            }
	        }
	        //下学期
	        $courseModelC = array();
	        if($next) {
	            $criteria = new CDbCriteria;
	            $criteria->compare('course_id', $next);
	            $courseModelC = TimetableCourseData::model()->findAll($criteria);
	        }

	        $courseDataNext = array();
	        if($courseModelC) {
	            foreach ($courseModelC as $item) {
	                $courseDataNext[$item->weekday . '-' . $item->period][] = $item->course_code;
	            }
	        }

	        $courseData2Next= array();
	        if($courseDataNext){
	            $courseDataNexts = array();
	            if($courseDataAll){
	                $courseDataLists = $courseDataNext + $courseDataAll;
	            }else{
	                $courseDataLists = $courseDataNext;
	            }
	            foreach ($courseDataLists as $k=>$value) {
	                $courseData2Next[$k] = implode(',', $value);
	            }
	        }
	        if($courseData2All){
	            $cackeModel1 = new TimetableWeeklyScheduleCache();
	            $cackeModel1->tid = $tid;
	            $cackeModel1->type = 10;
	            $cackeModel1->term = 0;
	            $cackeModel1->target_id = $child_id;
	            $cackeModel1->data = json_encode($courseData2All);
	            $cackeModel1->updated_at = time();
	            $cackeModel1->save();
	        }
	        if($courseData2List){
	            $cackeModel2 = new TimetableWeeklyScheduleCache();
	            $cackeModel2->tid = $tid;
	            $cackeModel2->type = 10;
	            $cackeModel2->term = 1;
	            $cackeModel2->target_id = $child_id;
	            $cackeModel2->data = json_encode($courseData2List);
	            $cackeModel2->updated_at = time();
	            $cackeModel2->save();
	        }
	        if($courseData2Next){
	            $cackeModel3 = new TimetableWeeklyScheduleCache();
	            $cackeModel3->tid = $tid;
	            $cackeModel3->type = 10;
	            $cackeModel3->term = 2;
	            $cackeModel3->target_id = $child_id;
	            $cackeModel3->data = json_encode($courseData2Next);
	            $cackeModel3->updated_at = time();
	            $cackeModel3->save();
	        }
	    }

	    if($semesterId == 1) {
	        $data = ($cackeModel2) ? $cackeModel2 : (($cackeModel1) ? $cackeModel1 : "");
	    }
	    if($semesterId == 2) {
	        $data = ($cackeModel3) ? $cackeModel3 : (($cackeModel1) ? $cackeModel1 : "");
	    }
	    if($semesterId == 0) {
	        $data = ($cackeModel1) ? $cackeModel1 : "";
	    }

	    return $data;
	}
}
