<?php

/**
 * This is the model class for table "ivy_staff".
 *
 * The followings are the available columns in table 'ivy_staff':
 * @property integer $sid
 * @property string $card_id
 * @property string $party_affiliation
 * @property string $education_degree
 * @property string $specialty
 * @property string $qlcredentials
 * @property string $pr_credentials
 * @property string $archive_site
 * @property string $dwelling_place
 * @property string $homephone
 * @property string $emergency_contact
 * @property string $mobile_telephone
 * @property integer $valid_health_permit
 * @property string $education_experience
 * @property string $work_experience
 * @property string $social_insurance
 * @property integer $contract_period
 * @property string $pemail
 * @property string $data
 */
class Staff extends CActiveRecord
{
    var $education_degree; // 学历
    var $archive_site; // 档案所在地
    var $dwelling_place; //住址
    var $dwelling_place_pinyin; //住址
    var $native_place; //籍贯
    var $emergency_person; // 紧急联系人
    var $emergency_contact; // 紧急联络电话
    var $social_insurance; // 社会保险单位
    var $emailPass; // 公司邮箱密码

	public $retirement;
	public $dagree;
	public $releaseLetter;
	public $registration;
	public $bankcard;
	public $idcard;
	public $hiring;
	public $other;

	public $dagree_file;
	public $releaseLetter_file;
	public $registration_file;
	public $bankcard_file;
	public $idcard_file;
	public $hiring_file;
	public $other_file;
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return Staff the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public function afterFind() {
        parent::afterFind();
        if ($this->data){
            $this->data = unserialize($this->data);
            foreach ($this->data as $key=>$val){
                $this->{$key} = $val;
            }
        }

		if ($this->attachment){
            $this->attachment = CJSON::decode($this->attachment);
            foreach ($this->attachment as $key=>$val){
                $this->{$key} = $val;
            }
        }
    }

    public function save($runValidation = true, $attributes = null) {
        $data = array();
        if ($this->social_insurance)
            $data['social_insurance'] = $this->social_insurance;
        if ($this->emergency_person)
            $data['emergency_person'] = $this->emergency_person;
        if ($this->emergency_contact)
            $data['emergency_contact'] = $this->emergency_contact;
        if ($this->native_place)
            $data['native_place'] = $this->native_place;
        if ($this->dwelling_place)
            $data['dwelling_place'] = $this->dwelling_place;
        if ($this->archive_site)
            $data['archive_site'] = $this->archive_site;
        if ($this->education_degree)
            $data['education_degree'] = $this->education_degree;
        $this->data = serialize($data);
        return parent::save($runValidation, $attributes);
    }

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_staff';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('pemail', 'length', 'max'=>255),
            array('pemail', 'email'),
            array('mobile_telephone', 'length', 'min'=>11, 'max'=>11),
			array('card_id, party_affiliation, education_degree, specialty, qlcredentials, pr_credentials, archive_site,
			dwelling_place, homephone, emergency_contact, mobile_telephone, valid_health_permit, education_experience,
			work_experience, social_insurance, contract_period, contract_type, native_place, emergency_person, leavedate,
			data,startdate, birthday_search, passport_name, visa_expiry, relationship, relationship_other, relationship2_other,
			relationship2, conact_number2, contact_person2, relationship2_other, residence_card, highest_duniversity, highest_major
			dwelling_place_pinyin, home_of_record, highest_type, highest_type_other', 'safe'),

            # appOA 员工管理场景 addStaff editStaff
//          array('startdate', 'required', 'on'=>'addStaff, editStaff'),
            array('startdate, pemail, mobile_telephone', 'required', 'on'=>'addStaff'),
            array('startdate, pemail, mobile_telephone', 'required', 'on'=>'addUsers, editUser'),
            array('emailPass', 'required', 'on'=>'checkUser'),
            array('startdate', 'required', 'on'=>'reentry'),
			array("hiring_file","file", "types" => "jpg, gif, png, pdf, jpeg", "maxSize" => 1024*1024*2, "allowEmpty" => ($this->isNewRecord || !$this->hiring['photo']) ? false : true,'on'=>'addUsers', 'tooLarge' => '录取审批表太大. 文件大小不能超过 2MB ！'),
			array("idcard_file", "file", "types" => "jpg, gif, png, pdf, jpeg", "maxSize" => 1024*1024*2, "allowEmpty" =>  ($this->isNewRecord || !$this->idcard['photo']) ? false : true,'on'=>'addUsers', 'tooLarge' => '身份证复印件太大. 文件大小不能超过 2MB ！'),
			array("bankcard_file", "file",  "types" => "jpg, gif, png, pdf, jpeg", "maxSize" => 1024*1024*2, "allowEmpty" => ($this->isNewRecord || !$this->bankcard['photo']) ? false : true,'on'=>'addUsers', 'tooLarge' => '银行卡复印件太大. 文件大小不能超过 2MB ！'),
			array("registration_file", "file", "types" => "jpg, gif, png, pdf, jpeg", "maxSize" => 1024*1024*2, "allowEmpty" => ($this->isNewRecord || !$this->registration['photo']) ? false : true,'on'=>'addUsers', 'tooLarge' => '人事登记表太大. 文件大小不能超过 2MB ！'),
			array("dagree_file", "file", "types" => "jpg, gif, png, pdf, jpeg", "maxSize" => 1024*1024*2, "allowEmpty" => true,'on'=>'addUsers', 'tooLarge' => '学历和学位证明太大. 文件大小不能超过 2MB ！'),
			array("releaseLetter_file", "file", "types" => "jpg, gif, png, pdf, jpeg", "maxSize" => 1024*1024*2, "allowEmpty" => true,'on'=>'addUsers', 'tooLarge' => '离职证明太大. 文件大小不能超过 2MB ！'),
			array("other_file","file", "types" => "jpg, gif, png, pdf, jpeg", "maxSize" => 1024*1024*2, "allowEmpty" => true,'on'=>'addUsers', 'tooLarge' => '其他证件太大. 文件大小不能超过 2MB ！'),
		);
	}

	/**
	 * 上传附件
	 */
	protected function beforeSave()
	{
		if (parent::beforeSave()) {

			$attachment = is_array($this->attachment) ? $this->attachment : array();
			$picPath = Yii::app()->params['xoopsVarPath'] . '/staff/';

			if($file = $this->hiring_file){
				$picNames = $this->sid. '_hiring_file_' . uniqid() . '.' . $file->getExtensionName();
				$file->saveAs($picPath . $picNames);
				if($attachment['hiring']['photo']){
					unlink($picPath . $attachment['hiring']['photo']);
					$attachment['hiring']['photo'] = $picNames;
				}else{
					$attachment['hiring']['photo'] = $picNames;
					$attachment['hiring']['said'] = "";
				}
			}

			if($file = $this->other_file){
				$other = $file->getExtensionName();
				$picNames = $this->sid. '_other_file_' . uniqid() . '.' . $other;
				$file->saveAs($picPath . $picNames);
				if($attachment['other']['photo']){
					unlink($picPath . $attachment['other']['photo']);
					$attachment['other']['photo'] = $picNames;
				}else{
					$attachment['other']['photo'] = $picNames;
					$attachment['other']['said'] = "";
				}
			}

			if($registrationa = $this->registration_file){
				$registrationw = $registrationa->getExtensionName();
				$picNames = $this->sid. '_registration_file_' . uniqid() . '.' . $registrationw;
				$registrationa->saveAs($picPath . $picNames);
				if($attachment['registration']['photo']){
					unlink($picPath . $attachment['registration']['photo']);
					$attachment['registration']['photo'] = $picNames;
				}else{
					$attachment['registration']['photo'] = $picNames;
					$attachment['registration']['said'] = "";
				}
			}

			if($bankcarda = $this->bankcard_file){
				$bankcardw = $bankcarda->getExtensionName();
				$picNames = $this->sid. '_bankcard_file_' . uniqid() . '.' . $bankcardw;
				$bankcarda->saveAs($picPath . $picNames);
				$tp1 = json_decode($this->attachment,true);
				if($attachment['bankcard']['photo']){
					unlink($picPath . $attachment['bankcard']['photo']);
					$attachment['bankcard']['photo'] = $picNames;
				}else{
					$attachment['bankcard']['photo'] = $picNames;
					$attachment['bankcard']['said'] = "";
				}
			}

			if($idcarda = $this->idcard_file){
				$idcardw = $idcarda->getExtensionName();
				$picNames = $this->sid. '_idcard_file_' . uniqid() . '.' . $idcardw;
				$idcarda->saveAs($picPath . $picNames);
				if($attachment['idcard']['photo']){
					unlink($picPath . $attachment['idcard']['photo']);
					$attachment['idcard']['photo'] = $picNames;
				}else{
					$attachment['idcard']['photo'] = $picNames;
					$attachment['idcard']['said'] = "";
				}
			}

			if($dagreea = $this->dagree_file){
				$dagreew = $dagreea->getExtensionName();
				$picNames = $this->sid. '_dagree_file_' . uniqid() . '.' . $dagreew;
				$dagreea->saveAs($picPath . $picNames);
				if($attachment['dagree']['photo']){
					unlink($picPath . $attachment['dagree']['photo']);
					$attachment['dagree']['photo'] = $picNames;
				}else{
					$attachment['dagree']['photo'] = $picNames;
				}
			}

			if($releaseLettera = $this->releaseLetter_file){
				$releaseLetterw = $releaseLettera->getExtensionName();
				$picNames = $this->sid. '_releaseLetter_file_' . uniqid() . '.' . $releaseLetterw;
				$releaseLettera->saveAs($picPath . $picNames);
				if($attachment['releaseLetter']['photo']){
					unlink($picPath . $attachment['releaseLetter']['photo']);
					$attachment['releaseLetter']['photo'] = $picNames;
				}else{
					$attachment['releaseLetter']['photo'] = $picNames;
				}
			}
			if(isset($_POST['Staff']) && $_POST['Staff']){
				$attachment['releaseLetter']['said'] = $_POST['Staff']['releaseLetter'] ? $_POST['Staff']['releaseLetter'] : '';
				$attachment['dagree']['said'] = $_POST['Staff']['dagree'] ? $_POST['Staff']['dagree'] : '';
			}

			if($attachment){
				$str = CJSON::encode($attachment);
				$this->attachment = $str;
			}
			return true;
		}
		return false;
	}
	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'sid' => 'Sid',
			'card_id' => Yii::t("labels",'ID NO./Passport NO.'),
			'card_id_due' => Yii::t("userinfo",'ID/Passport Expiry Date'),
			'party_affiliation' => 'Party Affiliation',
			'education_degree' => Yii::t("labels",'Highest Degree'),
			'specialty' => 'Specialty',
			'qlcredentials' => 'Qlcredentials',
			'pr_credentials' => 'Pr Credentials',
			'archive_site' => Yii::t("labels",'Personal Files in'),
			'dwelling_place' => Yii::t("userinfo",'Residential Address (Chinese)'),
			'homephone' => 'Homephone',
			'mobile_telephone' => Yii::t("labels",'Mobile Phone'),
			'valid_health_permit' => 'Valid Health Permit',
			'education_experience' => 'Education Experience',
			'work_experience' => 'Work Experience',
			'social_insurance' => Yii::t("labels",'Social Insurance'),
			'contract_period' => Yii::t("userinfo",'Contract Completion Date'),
			'pemail' => Yii::t("labels",'Personal Email'),
			'data' => 'Data',
			'startdate' => Yii::t("userinfo",'Start Date at Ivy/Daystar'),
			'leavedate' => Yii::t("labels",'Leave Date'),
			'contract_type' => Yii::t("labels",'Contract Type'),
//			'emergency_person' => Yii::t("labels",'Emergency Contact'),
//			'emergency_contact' => Yii::t("labels",'Telephone (Confidential)'),
			'native_place' => Yii::t("labels",'Native Place'),
			'hiring_file' =>  Yii::t("labels",'Application Form'),
			'idcard_file' =>  Yii::t("labels",'Idcard'),
			'bankcard_file' =>  Yii::t("labels",'Bankcard'),
			'registration_file' =>  Yii::t("labels",'Registration Form'),
			'detection' => 'detection',
            'birthday_search' => Yii::t('userinfo', 'Date of Birth'),
            'passport_name' => Yii::t('userinfo', 'Passport Name (last name/first name/middle name)'),
            'visa_expiry' => Yii::t('userinfo', 'Current Chinese Visa Expiry Date'),
            'dwelling_place_pinyin' => Yii::t('userinfo', 'Residential Address (Pinyin)'),
            'home_of_record' => Yii::t('userinfo', 'Home of Record (Country/City)'),
            'relationship' => Yii::t('userinfo', 'Relationship to Employee (China)'),
            'emergency_person' => Yii::t("userinfo",'Emergency Contact Person'),
            'emergency_contact' => Yii::t("userinfo",'Emergency Contact Number'),
            'contact_person2' => Yii::t("userinfo",'Emergency Contact Person (Home country)'),
            'conact_number2' => Yii::t("userinfo",'Emergency Contact Number (Home country)'),
            'relationship2' => Yii::t("userinfo",'Relationship to Employee (Home country)'),
            'residence_card' => Yii::t("userinfo",'China Permanent Residence Card'),
            'highest_duniversity' => Yii::t("userinfo",'Highest Degree University'),
            'highest_major' => Yii::t("userinfo",'Major of Highest Degree'),
            'highest_type' => Yii::t("userinfo",'Type of Highest Degree'),
            'work_number' =>  Yii::t("userinfo",'Work Permit Number'),
            'position' =>  Yii::t("userinfo",'Position'),
            'teacher_license' =>  Yii::t("userinfo", "National Teacher's License"),
            'license_type' =>  Yii::t("userinfo",'Type of License'),
            'license_subject' =>  Yii::t("userinfo",'Subject of License'),
		);
	}

	public static function updateWechat($sid,$status)
    {
        if($sid) {
            Yii::import('common.components.AliYun.MQ.MQProducer');
            if($status == 'create'){
                CommonUtils::addProducer(MQProducer::TAG_WX_WORK, "WechatWork.createUser", $sid);
            }elseif ($status == 'update'){
                CommonUtils::addProducer(MQProducer::TAG_WX_WORK, "WechatWork.updateUser", $sid);
            }elseif ($status == 'delete'){
                CommonUtils::addProducer(MQProducer::TAG_WX_WORK, "WechatWork.deleteUser", $sid);
            }
        }
    }

	public static function contractTypeList() {
		return array(
			'full_time' => '全职',
			'part_time' => '兼职',
		);
	}

	public static function relationshipOpt() {
	    return array(
	        '' => Yii::t('teaching', 'Please select'),
            'spouse' => Yii::t('userinfo', 'Spouse'),
            'parent' => Yii::t('userinfo', 'Parent'),
            'child' => Yii::t('userinfo', 'Child'),
            'friend' => Yii::t('userinfo', 'Friend'),
            'other' => Yii::t('userinfo', 'Other'),
        );
    }

    public static function highestTypeOpt() {
        return array(
            '' => Yii::t('teaching', 'Please select'),
            'bachelor' => Yii::t('userinfo', 'Bachelor\'s Degree'),
            'master' => Yii::t('userinfo', 'Master\'s Degree'),
            'doctoral' => Yii::t('userinfo', 'Doctoral Degree'),
            'other' => Yii::t('userinfo', 'Other'),
        );
    }

    public static function licenseTypeOpt() {
        return array(
            '' => Yii::t('teaching', 'Please select'),
            'university' => Yii::t('userinfo', 'University'),
            'high_school' => Yii::t('userinfo', 'High School'),
            'middle_school' => Yii::t('userinfo', 'Middle School'),
            'elementary_school' => Yii::t('userinfo', 'Elementary School'),
            'other' => Yii::t('userinfo', 'Other'),
        );
    }
}
