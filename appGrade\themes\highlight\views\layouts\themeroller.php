<ul id="theme-roller">
	<li><a class="body-red" href="javascript:;">Red</a></li>
	<li><a class="body-green" href="javascript:;">Green</a></li>
	<li><a class="body-blue" href="javascript:;">Blue</a></li>
	<li><a class="body-gray" href="javascript:;">Gray</a></li>
</ul>
<script>
$("#theme-roller li a").click(function(){
	var bclass = $(this).attr("class");
	$('body').removeClass('body-red body-green body-blue body-gray').addClass(bclass);
	var date = new Date();
	date.setTime(date.getTime() + (30 * 60 * 1000));
	$.cookie('child_mims_bgset', bclass, {path:'/',expires: date});
	});
</script>
<style>
#theme-roller{
	position: absolute;
	top:0;
	left:0;
	height: 20px;
}
#theme-roller li{
	float: left;
	position: relative;
	padding: 6px 3px;
}
#theme-roller li a{
	margin: 0;
	display: block;
	height: 7px;
	width: 7px;
	text-indent: -999px;
	border: 1px solid #fff;
	box-shadow: 2px 2px 3px #333;
}
#theme-roller li a.body-red{
	background-color:#ff6e00;
}
#theme-roller li a.body-green{
	background-color:#8cbb2e;
}
#theme-roller li a.body-blue{
	background-color:#2564ab;
}
#theme-roller li a.body-gray{
	background-color:#f6f6ee;
}
#theme-roller li:hover{
	padding: 4px 1px;
}
#theme-roller li:hover a{
	height: 11px;
	width: 11px;
}

</style>