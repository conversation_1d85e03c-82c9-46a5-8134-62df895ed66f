<div class="h_a">待审核</div>
<?php
$this->widget('ext.ivyCGridView.IvyCGridView', array(
	'id'=>'checking_for_me',
	'dataProvider'=>$dataProvider,
        'htmlOptions'=>array(
            'class'=>'table_full',
        ),
	'template'=>"<div class='user_group' style='border:none;'>{items}</div><div class='table_full'>{summary}</div><div class='table_full'>{pager}</div>",
        'colgroups'=>array(
            array(
                "colwidth"=>array(150,80,120, 120, 120,120,80),
            )
        ),
	'columns'=>array(
		'leave_id'=>array(
                    'name'=>'leave_id',
                    'value'=>'UserLeave::getStartYear($data->userLeave->startyear)',
		),
		'type'=>array(
                    'name'=>'type',
                    'value'=>'UserLeave::getLeaveType($data->type)',
                ),
		'startdate'=>array(
                    'name'=>'startdate',
                    'value'=>'OA::formatDateTime($data->startdate)',
                ),
                'enddate'=>array(
                    'name'=>'startdate',
                    'value'=>'OA::formatDateTime($data->enddate)',
                ),
		'hours'=>array(
                    'name'=>'hours',
                    'value'=>'UserLeave::getLeaveFormat($data->hours)',
                ),
		'applydate'=>array(
                    'name'=>'applydate',
                    'value'=>'OA::formatDateTime($data->applydate)',
                ),
            array(
                'class'=>'CButtonColumn',
                'template' => '{delete}',
                'deleteButtonUrl'=>'Yii::app()->createUrl("/user/hr/delete", array("id" =>$data->id,"t"=>leave))',
                'visible'=>($status == LeaveItem::STATUS_WAITING) ? true : false,
            ),
	)
)); 
?>