<?php

/**
 * This is the model class for table "ivy_workflow_defination".
 *
 * The followings are the available columns in table 'ivy_workflow_defination':
 * @property integer $defination_id
 * @property string $defination_name_cn
 * @property string $defination_name_en
 * @property string $defination_type
 * @property string $defination_handle
 * @property string $node_order
 * @property string $start_user_role
 * @property integer $status
 * @property integer $display
 * @property string $defination_memo
 */
class WorkflowDefination extends CActiveRecord
{
    const WORKFLOW_DEFINATION_STATUS_SHOW = 1;
    const WORKFLOW_DEFINATION_STATUS_HIDE = 0;
    
    //权限范围
    const WORKFLOW_AUTH_SYSTEM_GROUP = 'user_group';   //管理多校园
    const WORKFLOW_AUTH_SYSTEM_POSITION = 'system_position';  //系统职位
    const WORKFLOW_AUTH_USER_LEVEL = 'external_user_level'; //用户级别
    const WORKFLOW_AUTH_ASSIGNED_USER = 'external_assigned_user'; //外部用户
    const WORKFLOW_AUTH_SYSTEM_RULE = 'system_group'; //系统角色
    
	/**
	 * Returns the static model of the specified AR class.
	 * @return WorkflowDefination the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_workflow_defination';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('defination_type, defination_handle, defination_type, defination_handle, branch_type, status', 'required'),
			array('status,branch_type,display', 'numerical', 'integerOnly'=>true),
			array('defination_name_cn, defination_name_en, defination_type, defination_handle, node_order, start_user_role', 'length', 'max'=>255),
			array('defination_memo', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('defination_id, defination_name_cn, defination_name_en, defination_type, defination_handle, node_order, start_user_role, status,display, defination_memo, branch_type', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'workflowNode'=>array(self::HAS_MANY,'WorkflowNode','defination_id','index'=>'node_id')
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'defination_id' => 'Defination',
			'defination_name_cn' => Yii::t('workflow', '工作流程中文名称'),
			'defination_name_en' => Yii::t('workflow', '工作流程英文名称'),
			'defination_type' => Yii::t('workflow', '工作流程类型'),
			'branch_type' => Yii::t('workflow', '校园类型'),
			'defination_handle' => Yii::t('workflow', '工作流程句柄'),
			'node_order' => Yii::t('workflow', '工作流程排序'),
			'start_user_role' => Yii::t('workflow', '发起人角色设置'),
			'status' => Yii::t('workflow', '工作流程状态'),
			'display' => Yii::t('workflow', '是否显示'),
			'defination_memo' => Yii::t('workflow', '工作流程描述'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('defination_id',$this->defination_id);
		$criteria->compare('defination_name_cn',$this->defination_name_cn,true);
		$criteria->compare('defination_name_en',$this->defination_name_en,true);
		$criteria->compare('defination_type',$this->defination_type,true);
		$criteria->compare('branch_type',$this->branch_type);
		$criteria->compare('defination_handle',$this->defination_handle,true);
		$criteria->compare('node_order',$this->node_order,true);
		$criteria->compare('start_user_role',$this->start_user_role,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('display',$this->display);
		$criteria->compare('defination_memo',$this->defination_memo,true);

		return new CActiveDataProvider(get_class($this), array(
			'criteria'=>$criteria,
		));
	}
    
    /**
     * 工作流类型
     */
    public static function getDefinationType($type =null){
        $result = array(
            'campus_daily_management'=>'校园日常管理',
            'staff_management'=>'员工管理'
        );
        return ($type === null) ? $result : $result[$type];
    }
    
    /**
     * 工作流校园类型
     */
    public static function getBranchType($type =null){
        $result = array(
            Branch::TYPE_CAMPUS_MI_PRESCHOOL=> '多元智能幼儿园',
            Branch::TYPE_CAMPUS_DAYSTAR=> '启明星模式校园',
            Branch::TYPE_OFFICE=> '总部'
        );
        return ($type === null) ? $result : $result[$type];
    }
    
    /**
     * 工作流状态
     */
    public static function getStatus($status = null){
        $result = array(
            1=> '有效',
            0=> '无效',
        );
        return ($status === null) ? $result : $result[$status];
    }

	public static function getDisplay($display = null){
        $result = array(
            1=> '显示',
            0=> '隐藏',
        );
        return ($display === null) ? $result : $result[$display];
    }
    
    
    public static function getWorkflowAuth($auth=null){
        $ret = array(
            self::WORKFLOW_AUTH_SYSTEM_GROUP => Yii::t('workflow', '管理多校园'),
            self::WORKFLOW_AUTH_SYSTEM_POSITION => Yii::t('workflow', '系统职位'),
            self::WORKFLOW_AUTH_USER_LEVEL => Yii::t('workflow', '用户级别'),
            self::WORKFLOW_AUTH_ASSIGNED_USER => Yii::t('workflow', '外部用户'),
        );
        return (empty($auth)) ? $ret : $ret[$auth];
    }
    
    public function delete() {
        $ret = parent::delete();
        Yii::app()->cache->delete(WorkflowNode::WORKFLOW_NODES_CACHE_ID);
        return $ret;
    }
}