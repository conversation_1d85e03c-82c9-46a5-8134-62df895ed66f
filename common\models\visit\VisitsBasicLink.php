<?php

/**
 * This is the model class for table "ivy_visits_basic_link".
 *
 * The followings are the available columns in table 'ivy_visits_basic_link':
 * @property integer $id
 * @property string $school_id
 * @property integer $basic_id
 * @property integer $visit_id
 * @property integer $status
 * @property integer $updated_at
 * @property integer $updated_by
 */
class VisitsBasicLink extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_visits_basic_link';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('school_id, basic_id, visit_id, updated_at, updated_by', 'required'),
			array('basic_id, visit_id, status, updated_at, updated_by', 'numerical', 'integerOnly'=>true),
			array('school_id', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, school_id, basic_id, visit_id, status, updated_at, updated_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'basic'=>array(self::HAS_ONE, 'IvyVisitsBasicInfo', array('id'=>'basic_id')),
			'records'=>array(self::HAS_MANY, 'IvyVisitsRecord', array('id'=>'basic_id')),
			'newrecord'=>array(self::HAS_ONE, 'IvyVisitsRecord', array('id'=>'visit_id')),
			'newlog'=>array(self::HAS_ONE, 'VisitsTraceLog', array('visit_id'=>'visit_id'), 'order'=>'newlog.update_timestamp desc'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'school_id' => 'School',
			'basic_id' => 'Basic',
			'visit_id' => 'Visit',
			'status' => 'Status',
			'updated_at' => 'Updated At',
			'updated_by' => 'Updated By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('school_id',$this->school_id,true);
		$criteria->compare('basic_id',$this->basic_id);
		$criteria->compare('visit_id',$this->visit_id);
		$criteria->compare('status',$this->status);
		$criteria->compare('updated_at',$this->updated_at);
		$criteria->compare('updated_by',$this->updated_by);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return VisitsBasicLink the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
