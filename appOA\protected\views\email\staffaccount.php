<?php
if ($model->profile->nationality == 36):
?>
<p>亲爱的<?php echo $model->name?></p>

<p>欢迎加入艾毅，我们首先向您介绍两个网络系统并附上您的登陆信息。</p>

<?php if($model->profile->branch == "BJ_TYG"):?>
1. 艾毅邮箱（http://mail.ivygroup.org）<br>
<?php else:?>
1. 艾毅邮箱（http://mail.ivyschools.com）<br>
<?php endif;?>
用户名：<?php echo $model->email;?><br>
密  码：<?php echo $model->iniPassword?><br>
<br><br>
2. 艾毅在线（https://apps.ivyonline.cn）<br>
用户名：<?php echo $model->email?><br>
密  码：<?php echo $model->iniPassword?><br>

<p>如果您在登录这两个系统的时候遇到困难，请联络IT@IVYGROUP.ORG 或致电总部电话（010-********）</p>
<?php else:?>
<p>Dear <?php echo $model->profile->first_name?>,</p>

<p>Welcome to Ivy! Please check your initial account info for Ivy web mail system and IvyOnline.</p>

<?php if($model->profile->branch == "BJ_TYG"):?>
1.Ivy Web Mail(http://mail.ivygroup.org)<br>
<?php else:?>
1.Ivy Web Mail(http://mail.ivyschools.com)<br>
<?php endif;?>
Login Name: <?php echo $model->email;?><br>
Password: <?php echo $model->iniPassword?><br>
<br><br>
2.IvyOnline (https://apps.ivyonline.cn)<br>
Login Name: <?php echo $model->email?><br>
Password: <?php echo $model->iniPassword?><br>

<p><NAME_EMAIL> if you have any login issue.</p>

<?php endif; ?>