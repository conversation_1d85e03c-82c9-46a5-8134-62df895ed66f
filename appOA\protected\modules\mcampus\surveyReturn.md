# surveyReturn 返校调查文旦
```

域名: http://apps.mims.cn/mcampus/surveyReturn/index?branchId=CD_FC

```

[TOC] 

## 一、返校调查

### 1、模板列表

**请求URI**
```
/surveyReturn/template
```

**请求方式**
```
POST
```

**请求参数**

| 参数     | 是否必选 | 类型   | 示例                     | 说明     |
| :------- | :------: | :----- | :----------------------- | :------- |

**返回说明**

| 参数  | 说明      |
| :---- | :-------- |
| $templateData | 模板数据 |


**返回示例**

```javascript
{
}

```
### 2、增加模板

**请求URI**
```
/surveyReturn/updateTemplate
```

**请求方式**
```
POST
```

**请求参数**

| 参数     | 是否必选 | 类型   | 示例                     | 说明     |
| :------- | :------: | :----- | :----------------------- | :------- |
| id   |    N     | INT | 1  | id |
| SurveyReturnTemplate[cn_title]   |    Y     | INT | 中文标题  | 中文标题 |
| SurveyReturnTemplate[en_title]    |    Y     | INT | EN title  | 英文标题 |
| SurveyReturnTemplate[en_intro]   |    Y     | INT | 中文介绍  | 中文介绍 |
| SurveyReturnTemplate[cn_intro]   |    Y     | INT | En Info  | 英文介绍 |
| SurveyReturnTemplate[status]   |    Y     | INT | 1  | 状态i |

**返回说明**

| 参数  | 说明      |
| :---- | :-------- |
| data | 学年 |


**返回示例**

```javascript
{
    "state": 'success',
    "message": "Success",
    "data": json数据
}

```
### 3、删除模板

**请求URI**
```
/surveyReturn/deleteTemplate
```

**请求方式**
```
POST
```

**请求参数**

| 参数     | 是否必选 | 类型   | 示例                     | 说明     |
| :------- | :------: | :----- | :----------------------- | :------- |
| survey_id    |    Y     | INT | 1  | id |

**返回说明**

| 参数  | 说明      |
| :---- | :-------- |
| survey_id | id |


**返回示例**

```javascript
{
    "state": 'success',
    "message": "Success",
    "data": 1
}

```
### 4、题目列表

**请求URI**
```
/surveyReturn/quetionList
```

**请求方式**
```
POST
```

**请求参数**

| 参数     | 是否必选 | 类型   | 示例                     | 说明     |
| :------- | :------: | :----- | :----------------------- | :------- |
| survey_id    |    Y     | string | 1  | survey_id |


**返回说明**

| 参数  | 说明      |
| :---- | :-------- |
| question | 数据 |


**返回示例**

```javascript
{
    "state": 'success',
    "message": "Success",
    "data": 1
}

```
### 5、删除题目

**请求URI**
```
/surveyReturn/deleteTopic
```

**请求方式**
```
POST
```

**请求参数**

| 参数     | 是否必选 | 类型   | 示例                     | 说明     |
| :------- | :------: | :----- | :----------------------- | :------- |
| id    |    Y     | string | 1  | 题目ID |


**返回说明**

| 参数  | 说明      |
| :---- | :-------- |
| survey_id | id |


**返回示例**

```javascript
{
    "state": 'success',
    "message": "Success",
    "data": 1
}

```
### 6、增加题目和选项

**请求URI**
```
/surveyReturn/updateQuetion
```

**请求方式**
```
POST
```

**请求参数**

| 参数     | 是否必选 | 类型   | 示例                     | 说明     |
| :------- | :------: | :----- | :----------------------- | :------- |
| type    |    Y     | string | 1  | question或者content |
| survey_id    |    Y     | INT | 1  | 模板iD |
| pid    |    N     | INT | 1  | 父类ID |
| id    |    N     | INT | 1  | 题目ID |
| option_ids    |    N     | array（） | 1  | 删除选项 |
| SurveyReturnQuestions[cn_title]    |    Y     | array() | 1  | 中文标题 |
| SurveyReturnQuestions[en_title]    |    Y     | array() | 1  | 英文标题 |
| SurveyReturnQuestions[content_ext]    |    N     | string | 1  | 图片 |
| SurveyReturnQuestions[is_required]    |    N     | array() | 1  | 是否必选|
| SurveyReturnQuestions[is_multiple]    |    N     | array() | 1  | 是否多选|
| SurveyReturnQuestions[is_memo]    |    N     | array() | 1  | 是否允许备注 |
| SurveyReturnQuestions[status]    |    N     | array() | 1  | 状态 |
| option[1][cn_title]    |    Y     | array() | 1  | 中文标题 |
| option[1][en_title]    |    Y     | array() | 1  | 英文标题 |
| option[1][id]    |    N     | array() | 1  | 只有修改时候会有值 为id |
| option[1][is_invoice]    |    N     | array() | 1  | 选项 |


**返回说明**

| 参数  | 说明      |
| :---- | :-------- |
| survey_id | id |


**返回示例**

```javascript
{
    "state": 'success',
    "message": "Success",
    "data": 1
}

```
### 7、获取已经分配给模板校园的数据

**请求URI**
```
/surveyReturn/assigntemplate
```

**请求方式**
```
POST
```

**请求参数**

| 参数     | 是否必选 | 类型   | 示例                     | 说明     |
| :------- | :------: | :----- | :----------------------- | :------- |
| survey_id    |    Y     | INT | 1  | 返校模板ID |


**返回说明**

| 参数  | 说明      |
| :---- | :-------- |
| data | 返回已经增加的数据 |


**返回示例**

```javascript
{
    "state":"success",
    "data":[
        {
            "branch":"CD_FC",
            "start_timestamp":"2019-01-01",
            "end_timestamp":"2019-12-21",
            "class_type":["c","n","b","p","k"]
        }
        ],
    "message":"success"
}
```
## 二、校园查看

### 1、校园已分配模板

**请求URI**
```
/surveyReturnAnswer/index
```

**请求方式**
```
POST
```

**请求参数**

| 参数     | 是否必选 | 类型   | 示例                     | 说明     |
| :------- | :------: | :----- | :----------------------- | :------- |
| yid | Y | INT | 119 | 校历ID |


**返回说明**

| 参数  | 说明      |
| :---- | :-------- |
| data | 返回已经增加的数据 |


**返回示例**

```javascript
{
    "state":"success",
    "data":[],
    "message":"success"
}
```
### 2、根据模板ID获取所有孩子信息

**请求URI**
```
/surveyReturnAnswer/result
```

**请求方式**
```
POST
```

**请求参数**

| 参数     | 是否必选 | 类型   | 示例                     | 说明     |
| :------- | :------: | :----- | :----------------------- | :------- |
| template_id | Y | INT | 1 | 模板ID |
| return_id | Y | INT | 1 | 关联ID |


**返回说明**

| 参数  | 说明      |
| :---- | :-------- |
| childInfo | 返回已经增加的数据 |
| question | 返回已经增加的数据 |


**返回示例**

```javascript
{
    "state":"success",
    "data":[],
    "message":"success"
}
```
### 3、同步数据

**请求URI**
```
/surveyReturnAnswer/synchronizeChild
```

**请求方式**
```
POST
```

**请求参数**

| 参数     | 是否必选 | 类型   | 示例                     | 说明     |
| :------- | :------: | :----- | :----------------------- | :------- |
| template_id | Y | INT | 1 | 模板ID |
| return_id | Y | INT | 1 | 关联ID |


**返回说明**

| 参数  | 说明      |
| :---- | :-------- |
| data | 返回已经增加的数据 |


**返回示例**

```javascript
{
    "state":"success",
    "data":Array
            (
                [0] => Array
                    (
                        [id] => 1217
                        [classid] => 2019-2020  Kindergarten 
                        [classType] => k
                        [childid] => 陈熹
                        [is_answer] => 0
                        [answerInfo] => Array
                            (
                            )

                        [updated_at] => 
                    )

                [1] => Array
                    (
                        [id] => 1218
                        [classid] => 2019-2020  Kindergarten 
                        [classType] => k
                        [childid] => 漆也慢
                        [is_answer] => 0
                        [answerInfo] => Array
                            (
                            )

                        [updated_at] => 
                    )

            );
    "message":"success"
}
```
### 4、修改开始时间和结束时间，状态

**请求URI**
```
/surveyReturnAnswer/updateReturn
```

**请求方式**
```
POST
```

**请求参数**

| 参数     | 是否必选 | 类型   | 示例                     | 说明     |
| :------- | :------: | :----- | :----------------------- | :------- |
| id | Y | INT | 1 | id |
| start_timestamp | Y | INT | 1 | 开始时间 |
| end_timestamp | Y | INT | 1 | 结束时间 |
| status | Y | INT | 1 | 状态 |
| class_type | Y | array() | array() | 班级 |


**返回说明**

| 参数  | 说明      |
| :---- | :-------- |
| data | 返回已经增加的数据 |


**返回示例**

```javascript
{
    "state":"success",
    "data":"",
    "message":"success"
}
```
### 5、删除结果

**请求URI**
```
/surveyReturnAnswer/deleteAnswer
```

**请求方式**
```
POST
```

**请求参数**

| 参数     | 是否必选 | 类型   | 示例                     | 说明     |
| :------- | :------: | :----- | :----------------------- | :------- |
| id | Y | INT | 1 | id |


**返回说明**

| 参数  | 说明      |
| :---- | :-------- |
| data | 返回已经增加的数据 |


**返回示例**

```javascript
{
    "state":"success",
    "data":"",
    "message":"success"
}
```