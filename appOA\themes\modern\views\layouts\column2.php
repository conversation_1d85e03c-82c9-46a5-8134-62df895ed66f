<?php $this->beginContent('//layouts/inner_main'); ?>
<?php if ($this->menu):?>
<div class="mb10">
    <?php
    foreach($this->menu as $ii=>$_menu):
        if(isset($_menu['visible']) && !$_menu['visible'])
        {
            unset($this->menu[$ii]);
            continue;
        }
        if(!isset($_menu['label']))
            $_menu['label']='';
        if(!isset($_menu['url']))
        {
            unset($this->menu[$i]);
            continue;
        }
        $llabel= isset($_menu['role']) ? '<span class="'.$_menu['role'].'"></span>' : '';
        $llabel .= $_menu['label'];
        echo CHtml::link($llabel, $_menu['url'], array('class'=>'btn', 'role'=>'button')).' ';
    endforeach;
    ?>
</div>
<?php endif;?>

<div class="span-19">
	<div id="content">
		<?php echo $content; ?>
	</div><!-- content -->
</div>
<?php $this->endContent(); ?>