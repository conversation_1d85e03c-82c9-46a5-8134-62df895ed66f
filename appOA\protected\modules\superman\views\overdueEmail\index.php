<style>
    .with80 {
        width: 80%;
    }

    p {
        margin: 12px 0;
    }
</style>
<div id='container' v-cloak class="ml20">
    <div class="col-md-6" style="border-right: 1px dashed #ccc;">
        <div class="btn-group">
            <h3>选择催缴类型:</h3>
            <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true"
                    aria-expanded="false">
                {{comment_type ? comment_type_list[comment_type] : '请选择'}} <span class="caret"></span>
            </button>
            <ul class="dropdown-menu">
                <li>
                    <a href="<?php echo $this->createUrl('/superman/overdueEmail/index', array('type' => 'not_overdue')) ?>">未逾期账单</a>
                </li>
                <li>
                    <a href="<?php echo $this->createUrl('/superman/overdueEmail/index', array('type' => 'overdue')) ?>">逾期账单</a>
                </li>
                <li><a href="<?php echo $this->createUrl('/superman/overdueEmail/index', array('type' => 'mix')) ?>">混合账单（未逾期+逾期账单）</a>
                </li>
            </ul>
        </div>
        <div>
            <div class='relative with80'>
                <h3>中文-message1:</h3>
                <input id="tinymce1" type="textarea" v-model="comment_cn.message1">
            </div>
            <div class='relative with80'>
                <h3>中文-message2:</h3>
                <input id="tinymce2" type="textarea" v-model='comment_cn.message2'>
            </div>

            <div class='relative with80'>
                <h3><label style="color: red">英文</label>-message1:</h3>
                <input id="tinymce3" type="textarea" v-model='comment_en.message1'>
            </div>
            <div class='relative with80'>
                <h3><label style="color: red">英文</label>-message2:</h3>
                <input id="tinymce4" type="textarea" v-model='comment_en.message2'>
            </div>
        </div>
        <div class="ml20 mt15 mb20">
            <button type="button" id="myButton" data-loading-text="Loading..." class="btn btn-primary" @click="setTip">
                保存
            </button>
        </div>
    </div>
    <div class="col-md-6">
        <h2>预览邮件</h2>
        <div id="mailContentContainer"
             style="height: auto; min-height: 100px; font-size: 14px; padding-top: 50px;">
            <table width="650" cellspacing="0" cellpadding="0" border="0" bgcolor="#428BCA" align="center">
                <tbody>
                <tr>
                    <td width="25" bgcolor="#428BCA">&nbsp;</td>
                    <td>
                        <table width="600" cellspacing="0" cellpadding="0" border="0" align="center">
                            <tbody>
                            <tr>
                                <td bgcolor="#ffffff" style="padding: 0.8em;">
                                    <div style="font-family: 'Microsoft Yahei'; color:#274257;font-size: 12px;">
                                        <div style="color:#274257;">
                                            <div style="font-family: 'Microsoft Yahei'">
                                                <p>尊敬的家长:</p>
                                                <span v-html="comment_cn.message1"></span>
                                                <p>姓名: 学生名字 </p>
                                                <p>
                                                    账单标题：账单标题（这是预览的）<br>
                                                    校园：school (校园)<br>
                                                    账单金额：20,000.00 RMB<br>
                                                    已付金额：10,000.00 RMB<br>
                                                    未付金额：10,000.00 RMB<br>
                                                </p>
                                                <span v-html="comment_cn.message2"></span>
                                                <p>自动邮件，无需回复
                                                <p><?php echo date('Y-m-d') ?></p>
                                            </div>
                                        </div>
                                        <br/><br/>
                                        <div style="color:#644436;">
                                            <div style="font-family: 'Trebuchet MS','Microsoft Yahei',Arial,Helvetica,sans-serif">
                                                <p>Dear Parents,</p>
                                                <span v-html="comment_en.message1"></span>
                                                <p>Student Name: student name</p>
                                                <p> Invoice Title: Invoice Title（This is a preview）<br>
                                                    Campus: school (校园)<br>
                                                    Invoice Amount: 20,000.00 RMB<br>
                                                    Amount Paid: 10,000.00 RMB<br>
                                                    Amount Unpaid: 10,000.00 RMB<br>
                                                    Date Due: 2020-9-1<br>
                                                </p>
                                                <span v-html="comment_en.message2"></span>
                                                <p><?php echo date('Y-m-d') ?></p>
                                            </div>
                                        </div>
                                        <p></p>
                                        <hr>
                                        <div>
                                            <p style="font-weight: bold;">Bank Information / 银行信息</p>
                                            <table cellpadding="0" border="0">
                                                <?php foreach ($bankinfo as $info): ?>
                                                    <tr style="padding: 5px 0;">
                                                        <td style="font-family: 'Trebuchet MS','Microsoft Yahei',Arial,Helvetica,sans-serif;font-size: 12px;"
                                                            valign="top">
                                                            <?php echo $info['title_en']; ?>：<br/>
                                                            <?php echo $info['title_cn']; ?>：
                                                        </td>
                                                        <td style="font-family: 'Trebuchet MS','Microsoft Yahei',Arial,Helvetica,sans-serif;font-size: 12px;">
                                                            <?php echo $info['content']; ?>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </table>
                                        </div>

                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td height="30" align="right" style="font-size: 12px;color:#ffffff;">
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </td>
                    <td width="25" bgcolor="#428BCA">&nbsp;</td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>

</div>

<script>
    var comment_data = <?php echo CJSON::encode($data)?>;
    var type = '<?php echo Yii::app()->request->getParam('type', ''); ?>'
    $(function () {
        initTinymce()
    })

    function initTinymce() {
        tinymce.init({
            selector: '#tinymce1',
            content_style: "img{max-width:100%}",
            language: '<?php echo CommonUtils::autoLang("zh_CN", "en"); ?>',
            height: 200,
            plugins: 'fullscreen image link media  table code wordcount paste lists, advlist',
            menubar: 'file edit view insert format help',
            contextmenu: 'link image imagetools table spellchecker lists editimage',
            toolbar_sticky: true,
            image_uploadtab: false,
            image_dimensions: false,
            automatic_uploads: false,
            paste_data_images: false,
            menu: {
                insert: {
                    title: 'Insert',
                    items: 'image link media template codesample inserttable | charmap emoticons hr | pagebreak nonbreaking anchor toc | insertdatetime | editimage'
                },
            },
            toolbar: 'mediaDialog bullist numlist',
            setup: function (ed) {
                ed.on('change undo keyup', function (e) {
                    container.comment_cn['message1'] = ed.getContent()
                })
            }
        });
        tinymce.init({
            selector: '#tinymce2',
            content_style: "img{max-width:100%}",
            language: '<?php echo CommonUtils::autoLang("zh_CN", "en"); ?>',
            height: 300,
            plugins: 'fullscreen image link media table code wordcount paste lists, advlist',
            menubar: 'file edit view insert format help',
            contextmenu: 'link image imagetools table spellchecker lists editimage',
            toolbar_sticky: true,
            image_uploadtab: false,
            image_dimensions: false,
            automatic_uploads: false,
            paste_data_images: false,
            menu: {
                insert: {
                    title: 'Insert',
                    items: 'image link media template codesample inserttable | charmap emoticons hr | pagebreak nonbreaking anchor toc | insertdatetime | editimage'
                },
            },
            toolbar: 'mediaDialog bullist numlist',
            setup: function (ed) {
                ed.on('change undo keyup', function (e) {
                    container.comment_cn['message2'] = ed.getContent()
                })
            }
        });
        tinymce.init({
            selector: '#tinymce3',
            content_style: "img{max-width:100%}",
            language: '<?php echo CommonUtils::autoLang("zh_CN", "en"); ?>',
            height: 200,
            plugins: 'fullscreen image link media table code wordcount paste lists, advlist',
            menubar: 'file edit view insert format help',
            contextmenu: 'link image imagetools table spellchecker lists editimage',
            toolbar_sticky: true,
            image_uploadtab: false,
            image_dimensions: false,
            automatic_uploads: false,
            paste_data_images: false,
            menu: {
                insert: {
                    title: 'Insert',
                    items: 'image link media template codesample inserttable | charmap emoticons hr | pagebreak nonbreaking anchor toc | insertdatetime | editimage'
                },
            },
            toolbar: 'mediaDialog bullist numlist',
            setup: function (ed) {
                ed.on('change undo keyup ', function (e) {
                    container.comment_en['message1'] = ed.getContent()
                })
            }
        });
        tinymce.init({
            selector: '#tinymce4',
            content_style: "img{max-width:100%}",
            language: '<?php echo CommonUtils::autoLang("zh_CN", "en"); ?>',
            height: 300,
            plugins: 'fullscreen image link media table code wordcount paste lists, advlist',
            menubar: 'file edit view insert format help',
            contextmenu: 'link image imagetools table spellchecker lists editimage',
            toolbar_sticky: true,
            image_uploadtab: false,
            image_dimensions: false,
            automatic_uploads: false,
            paste_data_images: false,
            menu: {
                insert: {
                    title: 'Insert',
                    items: 'image link media template codesample inserttable | charmap emoticons hr | pagebreak nonbreaking anchor toc | insertdatetime | editimage'
                },
            },
            toolbar: 'mediaDialog bullist numlist',
            setup: function (ed) {
                ed.on('change undo keyup', function (e) {
                    container.comment_en['message2'] = ed.getContent()
                })
            }
        });
    }

    var container = new Vue({
        el: "#container",
        data: {
            comment_type_list: {'overdue': '逾期账单', 'not_overdue': '未逾期账单', 'mix': '混合账单（未逾期+逾期账单）'},
            comment_type: type,//overdue 逾期 not_overdue 未逾期  mix 混合
            comment_cn: {'message1': '', 'message2': ''},
            comment_en: {'message1': '', 'message2': ''}
        },
        created: function () {
            this.iniData()
        },
        watch: {},
        methods: {
            iniData() {
                if (comment_data) {
                    this.comment_cn = comment_data[this.comment_type]['cn']
                    this.comment_en = comment_data[this.comment_type]['en']
                }
            },
            setTip() {
                var tinymce1 = tinymce.get('tinymce1').getContent();
                var tinymce2 = tinymce.get('tinymce2').getContent();
                var tinymce3 = tinymce.get('tinymce3').getContent();
                var tinymce4 = tinymce.get('tinymce4').getContent();
                var url = '<?php echo $this->createUrl('setOverdueEmailTip'); ?>';
                var params = {
                    'comment_type': this.comment_type,
                    'email_view': {
                        'cn': {'message1': tinymce1, 'message2': tinymce2},
                        'en': {'message1': tinymce3, 'message2': tinymce4}
                    }
                }
                $.post(url, params, function (data) {
                    if (data.state === 'success') {
                        resultTip({
                            msg: data.message
                        });
                    } else if (data.state === 'fail') {
                        head.dialog.alert(data.message);
                    }
                }, 'json');
            }
        }
    })
</script>