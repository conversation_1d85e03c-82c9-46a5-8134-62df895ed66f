<?php

/**
 * This is the model class for table "ivy_vote_result".
 *
 * The followings are the available columns in table 'ivy_vote_result':
 * @property integer $id
 * @property integer $vote_id
 * @property integer $operate_id
 * @property integer $result
 * @property integer $vote_time
 */
class WVoteResult extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return VoteResult the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_vote_result';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
				array('vote_id, operate_id, result, vote_time', 'numerical', 'integerOnly'=>true),
				// The following rule is used by search().
				// Please remove those attributes that should not be searched.
				array('id, vote_id, operate_id, result, vote_time', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
				'id' => 'ID',
				'vote_id' => 'Vote',
				'operate_id' => 'Operate',
				'result' => 'Result',
				'vote_time' => 'Vote Time',
		);
	}
	/**
	 * 判断某人是否已经参与了某投票
	 * @param int $vote_id
	 * @param int $operate_id
	 * @return boolean
	 */
	public function checkVoted($vote_id =0 ,$operate_id = 0) {
		$vr_cri = new CDbCriteria();
		$vr_cri->compare('vote_id', $vote_id);
		$vr_cri->compare('operate_id', $operate_id);
		$vr_count = $this->count($vr_cri);
		if($vr_count){
			return true;
		}
		return false;
	}

	public function getOptionCounts($option_id) {

		$vr_cri = new CDbCriteria();
		$vr_cri->compare('result', $option_id);
		$whereCondition = $vr_cri->toArray();

		$info = Yii::app()->db->createCommand()
		->select('`t`.result,count(*) as count')
		->from($this->tableName() . ' as t')
		->where($whereCondition['condition'],$whereCondition['params'])
		->group('result')
		->queryAll();

		$counts = null;

		if(!empty($info)){
			foreach ($info as $value) {
				if(!empty($value['result'])){
					if(empty($value['result'])){
						$counts[$value['result']] = 0;
					}else{
						$counts[$value['result']] = $value['count'];
					}
				}
			}
		}
		return $counts;
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('vote_id',$this->vote_id);
		$criteria->compare('operate_id',$this->operate_id);
		$criteria->compare('result',$this->result);
		$criteria->compare('vote_time',$this->vote_time);

		return new CActiveDataProvider($this, array(
				'criteria'=>$criteria,
		));
	}
}