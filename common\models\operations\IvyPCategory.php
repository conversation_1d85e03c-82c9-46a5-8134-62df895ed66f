<?php

/**
 * This is the model class for table "ivy_p_category".
 *
 * The followings are the available columns in table 'ivy_p_category':
 * @property string $id
 * @property string $title
 * @property integer $status
 * @property integer $create_userid
 * @property integer $update_timestamp
 */
class IvyPCategory extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return IvyPCategory the static model class
	 */
	public $fullId = null;
	public $parentCategory = null;
	public $abbId = null;
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_p_category';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('id, title, create_userid, update_timestamp', 'required'),
			array('status, create_userid, update_timestamp', 'numerical', 'integerOnly'=>true),
			array('id', 'length', 'max'=>10),
			array('id','unique'),
			array('abbId','dataValida'),
			array('title', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, title, status, create_userid, update_timestamp', 'safe', 'on'=>'search'),
		);
	}

	public function dataValida()
	{
		if (!empty($this->abbId))
		{
			if (substr_count($this->abbId,'_'))
			{
				$this->addError('abbId', Yii::t('operations', '标识中包含非法字符串"_"'));
			}
			if ($_POST['IvyPCategory']['parentCategory'] != $this->parentCategory)
			{
				if ($this->existsChildCategory($this->abbId) === true)
				{
					$this->addError('parentCategory', Yii::t('operations', Yii::t('operations', '此类别下已经存在子类,请先把子类更改后再修改')));
				}
			}
			if ($_POST['IvyPCategory']['parentCategory'] != $this->parentCategory)
			{
				if ($this->existsProduct($this->fullId) === true)
				{
					$this->addError('parentCategory', Yii::t('operations', Yii::t('operations', '产品库已经使用此类别，不能修改')));
				}
			}
		}
	}
	
	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'abbId' =>Yii::t('operations', '标识'),
			'fullId' =>Yii::t('operations', '完整标识'),
			'title' => Yii::t('operations', '标题'),
			'parentCategory' => Yii::t('operations', '父级分类'),
			'status' => Yii::t('operations', '状态'),
			'create_userid' => 'Create Userid',
			'update_timestamp' => Yii::t('operations', '更新日期'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id,true);
		$criteria->compare('title',$this->title,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('create_userid',$this->create_userid);
		$criteria->compare('update_timestamp',$this->update_timestamp);
		$criteria->order = 'id ASC';
		return new CActiveDataProvider(get_class($this), array(
			'criteria'=>$criteria,
		));
	}
	
	/**
	 * 显示类别的状态
	 * @return string
	 * <AUTHOR>
	 * @copyright IvyGroup
	 */
	public function showStatus()
	{
		return ($this->status) ? Yii::t('operations', '隐藏') : Yii::t('operations', '显示');
	}
	

	public function categoryList($status=0)
	{
		$categoryList = array();
		$criteria = new CDbCriteria();
		if ($status ==1 || $status==0)
		{
			$criteria->compare('status', $status);
		}
		$criteria->order = 'id ASC';
		$category = IvyPCategory::model()->findAll($criteria);
		if (!empty($category))
		{
			foreach ($category as $v)
			{
				$categoryList[$v->id] = str_repeat('-', substr_count($v->id,'_')).$v->title;
			}
		}
		return $categoryList;
	}
	
	/**
	 * 获取父类的ID
	 * @param string $id
	 * @return string
	 * <AUTHOR>
	 * @copyright IvyGroup
	 */
	static public function getParentCategory($id)
	{
		$treatedId = substr($id, 0,strrpos($id,'_'));
		return $treatedId;
	}
	
	/**
	 * 获取ID的缩写
	 * @param string $id
	 * @return string
	 * <AUTHOR>
	 * @copyright IvyGroup
	 */
	static public function getAbbId($id)
	{
		$abbId = $id;
		if (substr_count($id,'_'))
		{
			$abbId = substr($id, strrpos($id,'_')+1);
		}
		return $abbId;
	}
	
	/**
	 * 判断是否有子类
	 * @param string $id
	 * @return string
	 * <AUTHOR>
	 * @copyright IvyGroup
	 */
	public function existsChildCategory($id)
	{
		$result = false;
		$criteria = new CDbCriteria;
		$criteria->addSearchCondition('id', $id);
		$criteria->index = 'id';
		$categoryList = $this->model()->findAll($criteria);
		if (count($categoryList)>1)
		{
			$result = true;
		}
		return $result;
	}
	
	/**
	 * 判断是否在产品使用
	 * @param string $id
	 * @return string
	 * <AUTHOR>
	 * @copyright IvyGroup
	 */
	public function existsProduct($id)
	{
		return IvyPProduct::model()->exists('category_id=:category_id',array(':category_id'=>$id));
	}
}