<?php
$form = $this->beginWidget('CActiveForm', array(
    'id' => 'child_interivew',
    'enableAjaxValidation' => false,
    'action' => $this->createUrl('Transferstudents', array('childid' => $child->id)),
    'htmlOptions' => array('class' => 'J_ajaxForm', 'role' => 'form'),
)); ?>

<div class="modal-body">
    <?php if($before_child){ ?>
            <div class="panel panel-default">
                <div class="panel-heading"><?php echo Yii::t('user','Sibling Information') ?></div>
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <tbody>
                        <tr>
                            <th style="width: 189px"><strong><?php echo Yii::t('child','Name') ?></strong>
                            </th>
                            <th style="width: 189px"><strong><?php echo Yii::t('labels','Date of Birth') ?></strong>
                            </th>
                            <th style="width: 189px"><strong><?php echo Yii::t('labels','Gender') ?></strong>
                            </th>
                        </tr>
                        <?php foreach($before_child as $_child){?>
                            <tr>
                                <td style="width: 189px"><h4><?php echo $_child->getChildName(); ?></h4>
                                </td>
                                <td style="width: 189px"><h4><?php echo  date("Y-m-d", $_child->birthday); ?></h4>
                                </td>
                                <td style="width: 189px"><h4><?php echo ($_child->gender == 1) ?  Yii::t('child','Boy') :  Yii::t('child','Girl') ?></h4>
                                </td>
                            </tr>
                        <?php } ?>
                        </tbody>
                    </table>
                </div>
            </div>
    <?php } ?>
    <?php if($child->father_name || $child->mother_name){ ?>
        <div class="panel panel-default">
            <div class="panel-heading"><?php echo Yii::t('child','Parent Info')?></div>
            <div class="table-responsive">
                <table class="table table-bordered">
                    <tbody>
                    <tr>
                        <th style="width: 189px"><strong></strong>
                        </th>
                        <th style="width: 189px"><strong><?php echo Yii::t('child','Name') ?></strong>
                        </th>
                        <th style="width: 189px"><strong><?php echo Yii::t('labels','Tel') ?></strong>
                        </th>
                        <th style="width: 189px"><strong><?php echo Yii::t('child','Email') ?></strong>
                        </th>
                    </tr>
                    <?php if($child->father_name){ ?>
                        <tr>
                        <td style="width: 189px"><h4><?php echo Yii::t('child','Father') ?></h4>
                        </td>
                        <td style="width: 189px"><h4><?php echo $child->father_name; ?></h4>
                        </td>
                        <td style="width: 189px"><h4><?php echo $child->father_phone; ?></h4>
                        </td>
                        <td style="width: 189px"><h4><?php echo $child->father_email; ?></h4>
                        </td>
                    </tr>
                    <?php } ?>
                    <?php if($child->mother_name){ ?>
                    <tr>
                        <td style="width: 189px"><h4><?php echo Yii::t('child','Mother') ?></h4>
                        </td>
                        <td style="width: 189px"><h4><?php echo $child->mother_name; ?></h4>
                        </td>
                        <td style="width: 189px"><h4><?php echo $child->mother_phone; ?></h4>
                        </td>
                        <td style="width: 189px"><h4><?php echo $child->mother_email; ?></h4>
                        </td>
                    </tr>
                    <?php } ?>
                    </tbody>
                </table>
            </div>
        </div>
    <?php } ?>


    <?php echo CHtml::hiddenField('old_child',$before_child[0]->childid);?>
    <div class="panel panel-default">
        <div class="panel-heading"><?php echo Yii::t('management','Student information')?></div>
        <div class="table-responsive">
            <table class="table table-bordered">
                <tbody>
                <tr>
                    <th style="width: 189px"><strong><?php echo Yii::t('child','Name') ?></strong>
                    </th>
                    <th style="width: 189px"><strong><?php echo Yii::t('labels','Date of Birth') ?></strong>
                    </th>
                    <th style="width: 189px"><strong><?php echo Yii::t('labels','Gender') ?></strong>
                    </th>
                </tr>
                <tr>
                    <td style="width: 189px"><h4><?php echo $child->getName(); ?></h4>
                    </td>
                    <td style="width: 189px"><h4><?php echo date("Y-m-d", $child->birthday); ?></h4>
                    </td>
                    <td style="width: 189px"><h4><?php echo ($child->gender == 1) ? Yii::t('child','Boy') :  Yii::t('child','Girl'); ?></h4>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit')?></button>
    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global','Cancel')?></button>
</div>
<?php $this->endWidget(); ?>
