<?php

class ProductController extends ProtectedController
{
	/**
	 * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
	 * using two-column layout. See 'protected/views/layouts/column2.php'.
	 */
	public $layout='//layouts/column2';

	/**
	 * Creates a new model.
	 * If creation is successful, the browser will be redirected to the 'view' page.
	 */
	public function actionCreate()
	{
		$id = Yii::app()->request->getParam('id',0);
		$oldPic = null;
		if ($id)
		{
			$model = $this->loadModel($id);
			$oldPic = $model->image;
            $model->imageUrl = $model->showPic();
		 	foreach($model->Region as $val) {
      			$model->region[] = $val->region_id;
    		}
		}
		else
		{
			$model = new IvyPProduct;
		}
		// Uncomment the following line if AJAX validation is needed
		// $this->performAjaxValidation($model);

		if(isset($_POST['IvyPProduct']))
		{
			$model->attributes = $_POST['IvyPProduct'];
			$model->image = CUploadedFile::getInstance($model, 'image');
            $model->user_id = Yii::app()->user->id;
            $model->region = (current($_POST['IvyPProduct']['region'])) ? 1 : '';
            if ($model->image)
            {
            	$picInfo = OA::processPicUpload($model->image, 'operations', $oldPic);
            	$model->image = $picInfo['filename'];
            }
            else
            {
            	 $model->image = $oldPic;
            }
			if($model->save())
			{
				IvyPRegionLink::model()->deleteAll('product_id=:product_id',array(':product_id'=>$model->id));
				foreach ($_POST['IvyPProduct']['region'] as $value)
				{
					$linkModel = new IvyPRegionLink();
					$linkModel->region_id = $value;
					$linkModel->product_id = $model->id;
					if (!$linkModel->save())
					{
						print_r($linkModel->getErrors());
						exit;
					}
				}
				$model->refresh();
				$this->redirect(array('admin'));
			}
		}

		$this->render('create',array(
			'model'=>$model,
		));
	}


	/**
	 * Deletes a particular model.
	 * If deletion is successful, the browser will be redirected to the 'index' page.
	 * @param integer $id the ID of the model to be deleted
	 */
	public function actionDelete($id)
	{
		if(Yii::app()->request->isPostRequest)
		{
			// we only allow deletion via POST request
			 $exist = IvyPPromot::model()->exists('main_pid=:main_pid',array(':main_pid'=>$id));
			 if ($exist)
			 {
			 	throw new CHttpException(400,'Not to be deleted.');
			 }
			 else
			 {
			 	$model = $this->loadModel($id);
			 	if ($model->delete())
			 	{
			 		IvyPRegionLink::model()->deleteAll('product_id=:product_id',array(':product_id'=>$id));
			 		$cfgs = OA::LoadConfig('CfgPhoto');
			 		$params = $cfgs["operations"];
			 		$subPath = $params['subDir'];
			 		$subThumbPath = $params['subDir'] . 'thumbs/';
			 		@unlink($subPath . $model->image);
			 		@unlink($subThumbPath . $model->image);
			 	}
			 }
			
			// if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
			if(!isset($_GET['ajax']))
				$this->redirect(isset($_POST['returnUrl']) ? $_POST['returnUrl'] : array('admin'));
		}
		else
			throw new CHttpException(400,'Invalid request. Please do not repeat this request again.');
	}


	/**
	 * Manages all models.
	 */
	public function actionAdmin()
	{
		$model=new IvyPProduct('search');
		$model->unsetAttributes();  // clear any default values
		if(isset($_GET['IvyPProduct']))
			$model->attributes=$_GET['IvyPProduct'];

		$this->render('admin',array(
			'model'=>$model,
		));
	}

	/**
	 * Returns the data model based on the primary key given in the GET variable.
	 * If the data model is not found, an HTTP exception will be raised.
	 * @param integer the ID of the model to be loaded
	 */
	public function loadModel($id)
	{
		$model=IvyPProduct::model()->findByPk((int)$id);
		if($model===null)
			throw new CHttpException(404,'The requested page does not exist.');
		return $model;
	}

	/**
	 * Performs the AJAX validation.
	 * @param CModel the model to be validated
	 */
	protected function performAjaxValidation($model)
	{
		if(isset($_POST['ajax']) && $_POST['ajax']==='ivy-pproduct-form')
		{
			echo CActiveForm::validate($model);
			Yii::app()->end();
		}
	}
	
	public function actionPromot()
	{
		$id = Yii::app()->request->getParam('id',null);
		$str = '';
		if (!empty($id))
		{
			$model = $this->loadModel($id);
			$productList = IvyPPromot::model()->getProduct($id);
			if (!empty($productList))
			{
				foreach ($productList as $value)
				{
					$str .= "<option value='$value->pid'>".$value->Product->title."</option>";
				}
			}
		}
		else
		{
			throw new CHttpException(404,'The requested page does not exist.');
		}
		$this->renderPartial('promot', array('model'=>$model,'str'=>$str));
	}
	
	public function actionSearchProduct()
	{
		$title = Yii::app()->request->getParam('title',null);
		$mainPid = Yii::app()->request->getParam('mainPid',0);
		if (!empty($title) && $mainPid)
		{
			$product = IvyPProduct::model()->searchProduct($title);
			$str = '';
			if (!empty($product))
			{
				foreach ($product as $value)
				{
					if ($mainPid != $value->id)
					{
						$str .= "<option value='$value->id'>".$value->title."</option>";
					}
				}
			}
			else
			{
				$str .= "<option value='0'>".Yii::t('operations', '没找到相关产品')."</option>";
			}
			echo $str;
		}
	}
	
	public function actionAssignProduct()
	{
		$mainPid = Yii::app()->request->getParam('mainPid',0);
		$productIds = Yii::app()->request->getParam('productIds',null);
		if ($mainPid && $productIds)
		{
			$productIds = explode(',', $productIds);
			IvyPPromot::model()->deleteAll('main_pid=:main_pid',array(':main_pid'=>$mainPid));
			foreach ($productIds as $value)
			{
				$model = new IvyPPromot();
				$model->pid = $value;
				$model->main_pid = $mainPid;
				$model->save();
			}
			$productList = IvyPPromot::model()->getProduct($mainPid);
			if (!empty($productList))
			{
				$str = '';
				foreach ($productList as $value)
				{
					$str .= "<option value='$value->pid'>".$value->Product->title."</option>";
				}
				echo $str;
			}
		}
	}
	
	
	public function actionDelProduct()
	{
		$mainPid = Yii::app()->request->getParam('mainPid',0);
		$productIds = Yii::app()->request->getParam('productIds',null);
		if ($mainPid && !empty($productIds))
		{
			$productIds = explode(',', $productIds);
			foreach ($productIds as $value)
			{
				 $model = IvyPPromot::model()->find('main_pid=:main_pid AND pid=:pid',array(':main_pid'=>$mainPid,':pid'=>$value));
				 $model->delete();
			}
			$productList = IvyPPromot::model()->getProduct($mainPid);
			if (!empty($productList))
			{
				$str = '';
				foreach ($productList as $value)
				{
					$str .= "<option value='$value->pid'>".$value->Product->title."</option>";
				}
				echo $str;
			}
		}
	}
}
