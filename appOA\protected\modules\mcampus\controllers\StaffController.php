<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
class StaffController extends BranchBasedController
{
    /*
    public $cfg;
    public $causeCfg;
    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle =Yii::t('site', 'Campus Operations');

        $this->cfg['level'] = array(
            '-1'=>Yii::t('staff', '等待IT审核'),
            '-2'=>Yii::t('staff', '等待人事审核'),
        );
        //离职原因配置
        $this->causeCfg = array(
            '1'=>'AA',
            '2'=>'BB',
            '3'=>'CC',
            '4'=>'DD',
            '5'=>'EE',
            '6'=>'FF',
        );
        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mcampus/staff/index','category'=>'join');
    }

    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function beforeAction($action) {
        parent::beforeAction($action);
        //初始化校园列表
        $allBranch = $this->getAllBranch();
        $branch = count($this->accessBranch) ? $this->accessBranch : array($this->branchId);
        foreach ($branch as $val){
            $this->cfg['branch'][$val] = $allBranch[$val]['title'];
        }
        return true;
    }

    public function actionIndex(){
        Yii::import('common.models.staff.Staff');
        $category = Yii::app()->request->getParam('category','join');
        switch ($category){
            case 'join':
                $level = -2;
                if (Yii::app()->user->checkAccess("o_StaffCreate")){
                    $level = -1;
                }
                $criteria = new CDbCriteria;
                $criteria->compare('t.level', $level);
                $criteria->compare('t.rank', 0);
                $criteria->compare('t.isstaff', 1);
                $criteria->with='profile';
                $criteria->compare('profile.branch', $this->branchId);
                $_name = Yii::app()->request->getParam('name', '');
                $_email = Yii::app()->request->getParam('email', '');
                if (isset($_POST['action']) && $_POST['action'] == 'search'){
                    if ($_name)
                        $criteria->compare('name', $_name, true);
                    if ($_email)
                        $criteria->compare('email', $_email, true);
                }

                $dataProvider = new CActiveDataProvider('User', array(
                    'criteria'=>$criteria,
                    'sort' => array(
                        'defaultOrder' => 'uname asc',
                    ),
                ));

                $nationalityID = array();
                foreach ($dataProvider->getData() as $val){
                    $nationalityID[$val->profile->nationality]=$val->profile->nationality;
                }
                if ($nationalityID){
                    $criteria = new CDbCriteria;
                    $criteria->compare('id', $nationalityID);
                    $items=Country::model()->findAll($criteria);
                    foreach ($items as $item){
                        $this->cfg['country'][$item->id]=CommonUtils::autoLang($item->country, $item->country_cn);
                    }
                }
                break;
            case 'update':
                $id = Yii::app()->request->getParam('id',0);
                if ($id){
                    $model = User::model()->findByPk($id);
                        if (empty($model->staffApprover)){
                        $model->staffApprover = new StaffApprover;
                    }
                    $model->setScenario('editUser');
                    $model->profile->setScenario('editUser');
                    $model->staff->setScenario('editUser');
                    $model->staffApprover->setScenario('editUser');
                    $model->staff->card_id_due = OA::formatDateTime($model->staff->card_id_due);
                    $model->staff->startdate = OA::formatDateTime($model->staff->startdate);
                    $model->staff->leavedate = OA::formatDateTime($model->staff->leavedate);
                    $model->staff->contract_period = OA::formatDateTime($model->staff->contract_period);
                }else{
                    $model = new User;
                    $model->profile = new UserProfile;
                    $model->staff = new Staff;
                    $model->staffApprover = new StaffApprover;
                    $model->profile->branch = $this->branchId;
                    $model->setScenario('addUser');
                    $model->profile->setScenario('addUser');
                    $model->staff->setScenario('addUser');
                    $model->staffApprover->setScenario('addUser');
                    $model->staff->unsetAttributes(array('contract_period'));
                }
                $model->staffApprover->approvers = StaffApprover::getApproverUserBySystem($model->profile->branch);
                if (Yii::app()->request->isAjaxRequest) {
                    if (isset($_POST['User'], $_POST['UserProfile'], $_POST['Staff'])) {
                        $model->attributes = $_POST['User'];
                        $model->profile->attributes = $_POST['UserProfile'];
                        $model->staff->attributes = $_POST['Staff'];
                        $model->staffApprover->attributes = $_POST['StaffApprover'];

                        $newRecord = $model->isNewRecord;
                        if ($newRecord) {
                            $model->user_regdate = time();
                        }
                        $model->isstaff = 1;
                        $model->level = -2;

                        $model->profile->first_name = ucfirst(strtolower($model->profile->first_name));
                        $model->profile->last_name = ucfirst(strtolower($model->profile->last_name));
                        $model->uname = $model->profile->first_name . ' ' . substr($model->profile->last_name, 0, 1);
                        $model->timezone_offset = 8.0;
                        $model->flag = 2;
                        $model->staff->card_id_due = strtotime($_POST['Staff']['card_id_due']);
                        $model->staff->startdate = strtotime($_POST['Staff']['startdate']);
                        $model->staff->leavedate = strtotime($_POST['Staff']['leavedate']);
                        $model->staff->contract_period = strtotime($_POST['Staff']['contract_period']);
                        $model->uploadPhoto = CUploadedFile::getInstance($model, 'uploadPhoto');
                        $oldPhoto = $model->user_avatar;

                        $valid = $model->validate();
                        $valid = $model->profile->validate() && $valid;
                        $valid = $model->staff->validate() && $valid;
                        $valid = $model->staffApprover->validate() && $valid;

                        if ($valid) {
                            if ($model->uploadPhoto) {
                                $delold = ($oldPhoto == 'blank.gif') ? false : true;
                                $upResult = OA::processPicUpload($model->uploadPhoto, 'userPhoto', $oldPhoto, $delold);
                                $model->user_avatar = count($upResult) == 3 ? $upResult['filename'] : 'blank.gif';
                            }
                            $model->save(false);
                            $model->profile->uid = $model->uid;
                            $model->profile->save(false);
                            $model->staff->sid = $model->uid;
                            $model->staff->department = $auth->department_id;
                            $model->staff->save(false);
                            $model->staffApprover->staff_uid = $model->uid;
                            $model->staffApprover->updated = time();
                            $model->staffApprover->updator = Yii::app()->user->getId();
                            $model->staffApprover->save(false);
                            $this->addMessage('state', 'success');
                            $this->addMessage('message', Yii::t('message','success'));
                            $this->addMessage('refresh', true);
                            $this->addMessage('referer', $this->createUrl('/mcampus/staff/index', array('category'=>'join')));
                        } else {
                            $this->addMessage('state', 'fail');
                            $errs = current($model->getErrors());
                            $errs = !$errs ? current($model->profile->getErrors()) : $errs;
                            $errs = !$errs ? current($model->staff->getErrors()) : $errs;
                            $errs = !$errs ? current($model->staffApprover->getErrors()) : $errs;
                            $this->addMessage('message', $errs ? $errs[0] : Yii::t('message','Failed!'));
                        }
                        $this->showMessage();
                    }
                }
                break;
        }

        $this->render('index',array('dataProvider'=>$dataProvider,'category'=>$category,'model'=>$model));
    }

    public function actionCheck(){
        $id = Yii::app()->request->getParam('id', 0);
        $user = Yii::app()->user;
        Yii::import('common.models.staff.*');
        if ($id) {
            $model = User::model()->findByPk($id);
            if (empty($model->staffApprover)) {
                $model->staffApprover = new StaffApprover;
            }
            $model->setScenario('checkUser');
            $model->profile->setScenario('checkUser');
            $model->staff->setScenario('checkUser');
            $model->staffApprover->setScenario('checkUser');
            $model->staff->card_id_due = OA::formatDateTime($model->staff->card_id_due);
            $model->staff->startdate = OA::formatDateTime($model->staff->startdate);
            $model->staff->leavedate = OA::formatDateTime($model->staff->leavedate);
            $model->staff->contract_period = OA::formatDateTime($model->staff->contract_period);
            if (Yii::app()->request->isAjaxRequest) {
                if (isset($_POST['User'], $_POST['UserProfile'], $_POST['Staff'])) {
                    $model->attributes = $_POST['User'];
                    $model->profile->attributes = $_POST['UserProfile'];
                    $model->staff->attributes = $_POST['Staff'];
                    $model->staffApprover->attributes = $_POST['StaffApprover'];
                    $chechOpUser = $_POST['User']['chechOpUser'];
                    $newRecord = $model->isNewRecord;
                    if ($newRecord) {
                        $model->user_regdate = time();
                    }
                    $model->isstaff = 1;
                    if ($user->checkAccess("o_StaffApprove",null,null)){
                        $model->level = ($chechOpUser) ? -1 : -2;
                        $model->iniPassword = 'barney'; //验证规则实现麻烦,后面程序会初始化为空
                        $model->staff->emailPass = 'barney';
                    }elseif($user->checkAccess("o_StaffCreate")){
                        $model->level = ($chechOpUser) ? 1 : -1;
                    }

                    $model->profile->first_name = ucfirst(strtolower($model->profile->first_name));
                    $model->profile->last_name = ucfirst(strtolower($model->profile->last_name));
                    $model->uname = $model->profile->first_name . ' ' . substr($model->profile->last_name, 0, 1);
                    $model->timezone_offset = 8.0;
                    $model->flag = 2;
                    $model->staff->card_id_due = strtotime($_POST['Staff']['card_id_due']);
                    $model->staff->startdate = strtotime($_POST['Staff']['startdate']);
                    $model->staff->leavedate = strtotime($_POST['Staff']['leavedate']);
                    $model->staff->contract_period = strtotime($_POST['Staff']['contract_period']);
                    $model->uploadPhoto = CUploadedFile::getInstance($model, 'uploadPhoto');
                    $oldPhoto = $model->user_avatar;

                    $valid = $model->validate();
                    $valid = $model->profile->validate() && $valid;
                    $valid = $model->staff->validate() && $valid;
                    $valid = $model->staffApprover->validate() && $valid;

                    if ($valid) {
                        if ($model->uploadPhoto) {
                            $delold = ($oldPhoto == 'blank.gif') ? false : true;
                            $upResult = OA::processPicUpload($model->uploadPhoto, 'userPhoto', $oldPhoto, $delold);
                            $model->user_avatar = count($upResult) == 3 ? $upResult['filename'] : 'blank.gif';
                        }
                        if ($user->checkAccess("o_StaffApprove",null,null)){
                            $model->iniPassword = '';
                            $model->staff->emailPass = '';
                        }
                        $model->iniPassword = trim($model->iniPassword);
                        if ($model->iniPassword) {
                            $model->pass = md5($model->iniPassword);
                        }
                        $model->save(false);
                        $staffprimarygroup = 0;
                        if ($user->checkAccess("o_StaffCreate") && $chechOpUser){
                            # Xoops 权限
                            $staffgroups = array(XoGroups::STAFFGROUP);
                            $auth = DepPosLink::model()->findByPk($model->profile->occupation_en);
                            if ($auth){
                               XoGroupsUsersLink::model()->removeAllGroup($model->uid);
                                $staffgroups = array_merge($staffgroups, explode(',', $auth->authority));
                                XoGroupsUsersLink::model()->addUserToGroup(XoGroups::USERGROUP, $model->uid);
                                foreach ($staffgroups as $groupid){
                                    XoGroupsUsersLink::model()->addUserToGroup($groupid, $model->uid);
                                    if (!$staffprimarygroup && ($groupid != XoGroups::STAFFGROUP))
                                       $staffprimarygroup = $groupid;
                                }
                            }
                            # Yii 权限
                            Yii::import('srbac.models.*');
                            if ($model->uid){
                               $criteria=new CDbCriteria();
                                $criteria->compare('userid', $model->uid);
                                $criteria->compare('itemname', '<>superDude');
                                Assignments::model()->deleteAll($criteria);

                                $criteria=new CDbCriteria();
                                $criteria->compare('groupid', $staffgroups);
                                $gs = XoGroups::model()->findAll($criteria);
                                foreach ($gs as $g){
                                    $amodel = new Assignments;
                                    $amodel->itemname = $g->group_type;
                                    $amodel->userid = $model->uid;
                                    $amodel->save();
                                }
                            }
                            # 同步搜索表
                            StaffSync::model()->sync($model->uid);
                        }
                        $model->profile->uid = $model->uid;
                        $model->profile->staffprimarygroup=$staffprimarygroup;
                        $model->profile->save(false);
                        $model->staff->sid = $model->uid;
                        $model->staff->department = $auth->department_id;
                        $model->staff->save(false);
                        $model->staffApprover->staff_uid = $model->uid;
                        $model->staffApprover->updated = time();
                        $model->staffApprover->updator = Yii::app()->user->getId();
                        $model->staffApprover->save(false);
                        # 发帐号信到私人邮件
                        if ($user->checkAccess("o_StaffCreate") && $chechOpUser){
                            $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
                            $mailer->Subject = $model->profile->nationality == 36 ? '欢迎加入艾毅' : 'Welcome to Ivy';
                            $mailer->AddAddress($model->staff->pemail);
                            $mailer->iniMail( OA::isProduction() ); // 此行代码要放到AddAddress, AddCC方法下面
                            $mailer->getView('staffaccount', array('model'=>$model));
                            $mailer->Send();
                        }

                        $this->addMessage('state', 'success');
                        $this->addMessage('message', Yii::t('message','success'));
                        $this->addMessage('refresh', true);
                        $this->addMessage('referer', $this->createUrl('/mcampus/staff/index', array('category' => 'join')));
                    } else {
                        $this->addMessage('state', 'fail');
                        $errs = current($model->getErrors());
                        $errs = !$errs ? current($model->profile->getErrors()) : $errs;
                        $errs = !$errs ? current($model->staff->getErrors()) : $errs;
                        $errs = !$errs ? current($model->staffApprover->getErrors()) : $errs;
                        $this->addMessage('message', $errs ? $errs[0] : Yii::t('message','Failed!'));
                    }
                    $this->showMessage();
                }
            }
        }
        $model->staffApprover->approvers = StaffApprover::getApproverUserBySystem($model->profile->branch);
        $this->render('index',array('model'=>$model));
    }

    public function actionSelect(){
        $this->render('//layouts/common/branchSelect');
    }

    public function actionGetUser(){
        $branchid = Yii::app()->request->getPost('branchid',0);
        $data = StaffApprover::getApproverUserBySystem($branchid);
        echo CJSON::encode($data);
    }
    */
}

