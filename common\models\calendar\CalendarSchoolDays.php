<?php

/**
 * This is the model class for table "ivy_calendar_schooldays".
 *
 * The followings are the available columns in table 'ivy_calendar_schooldays':
 * @property integer $id
 * @property integer $yid
 * @property integer $semester_flag
 * @property integer $total_day
 * @property string $schoolday
 * @property string $month_label
 * @property string $schoolday_array
 * @property string $half_schoolday_array
 */
class CalendarSchoolDays extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CalendarSchoolDays the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_calendar_schooldays';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('yid, semester_flag, total_day, schoolday, month_label,month', 'required'),
			array('yid, semester_flag, total_day, month', 'numerical', 'integerOnly'=>true),
			array('schoolday', 'length', 'max'=>255),
			array('month_label', 'length', 'max'=>20),
			array('schoolday_array, half_schoolday_array', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, yid, semester_flag, total_day, schoolday, month_label, month, schoolday_array, half_schoolday_array', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'yid' => 'Yid',
			'semester_flag' => 'Semester Flag',
			'total_day' => 'Total Days',
			'schoolday' => 'Schooldays',
			'month_label' => 'Month Label',
			'month' => 'Month',
			'schoolday_array' => 'Schoolday Array',
			'half_schoolday_array' => 'Half Schoolday Array',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('semester_flag',$this->semester_flag);
		$criteria->compare('total_day',$this->total_day);
		$criteria->compare('schoolday',$this->schoolday,true);
		$criteria->compare('month_label',$this->month_label,true);
		$criteria->compare('month',$this->month_label);
		$criteria->compare('schoolday_array',$this->schoolday_array,true);
		$criteria->compare('half_schoolday_array',$this->half_schoolday_array,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
	
	/**
	 * 取得符合条件的教学天数
	 * @param int $calendarId
	 * @param int $startdate
	 * @param int $enddate
	 * @return Array
	 * <AUTHOR>
	 */
	public function getCalendarSchoolDay($calendarId,$startdate,$enddate)
	{
		$criteria = new CDbCriteria;
		$criteria->compare('yid', $calendarId);
		$criteria->addCondition('month_label>="'.date('Y-m',$startdate).'"');
		$criteria->addCondition('month_label<="'.date('Y-m',$enddate).'"');
		return $this->findAll($criteria);
	}
	

	/**
	 * 根据参数取得校历的教学天数
	 * @param array|int $calendarId
	 * @return ArrayIterator;
	 * <AUTHOR>
	 * @copyright Ivy
	 */
	public function getCalendarSchooldays($calendarIds,$calendarStartDate=0,$endDate=0)
	{
		$resutlList = array();
		$criteria = new CDbCriteria;
		$criteria->compare('yid', $calendarIds);
		if ($calendarStartDate && $endDate)
		{
			$criteria->addBetweenCondition('month', date('Ym',$calendarStartDate), date('Ym',$endDate));
		}
		$calendarList = $this->model()->findAll($criteria);
		if (!empty($calendarList))
		{
			foreach ($calendarList as $v)
			{
				$schoolday_array = array();
				$month_day = 0;
				foreach (explode(',',$v->schoolday_array) as $item) {
					if (date('Ymd', $calendarStartDate) > ($v->month . $item)) {
						continue;
					}
					if (date('Ymd', $endDate) >= ($v->month . $item)) {
						$month_day = $month_day + 1;
						$schoolday_array[] = $item;
					}
				}
				if ($calendarStartDate && $endDate && $month_day == 0) {
					continue;
				}

				$v->schoolday = count($schoolday_array);
				$v->schoolday_array = implode(',', $schoolday_array);

				if (!isset($resutlList[$v->yid]['month'][$v->month])) {
					$resutlList[$v->yid]['month'][$v->month] = $v->attributes;
				} else {
					$resutlList[$v->yid]['month'][$v->month]['schoolday'] += $v->schoolday;
					if ($resutlList[$v->yid]['month'][$v->month]['schoolday_array']) {
						$schoolday_array = $resutlList[$v->yid]['month'][$v->month]['schoolday_array'] + $schoolday_array;
					}
				}
				$resutlList[$v->yid]['month'][$v->month]['schoolday_array'] = $schoolday_array;
				$resutlList[$v->yid]['month'][$v->month]['half_schoolday_array'] = ($v->half_schoolday_array) ? explode(',',$v->half_schoolday_array) : array();
				$resutlList[$v->yid]['schoolday'] = isset($resutlList[$v->yid]['schoolday']) ? $resutlList[$v->yid]['schoolday']+$v->schoolday : $v->schoolday;
			}
		}
		// var_dump($resutlList);
		return $resutlList;
	}
	
	/**
	 * 计算两个日期内实际的教学天数
	 * @param int $calendarId
	 * @param int $startDate
	 * @param int $endDate
	 * @return ArrayIterator
	 * <AUTHOR>
	 * @copyright Ivy
	 */
	public function countCalendarSchoolday($calendarId,$startDate,$endDate,$calendarStartDate,$ceil=false)
	{
		$schooldayList = array();
		$totalActualSchoolday = 0;
		$totalSchoolday = 0;
		if ($calendarId && $startDate && $endDate)
		{
			if ($ceil && !$calendarStartDate) {
				$calendarStartDate = $startDate;
			}
			$calendarSchoolday = $this->getCalendarSchooldays($calendarId,$calendarStartDate,$endDate);
			if(isset($calendarSchoolday[$calendarId]))
			{
				$totalSchoolday = $calendarSchoolday[$calendarId]['schoolday'];
				$invoiceStartDate = $startDate;
				$startMonth = date('Ym',$startDate);
				$endMonth = date('Ym',$endDate);
				while (date('Ym',$invoiceStartDate) <= $endMonth)
				{
					$invoiceMonth = date('Ym',$invoiceStartDate);
					$_startDate = strtotime($invoiceMonth.min($calendarSchoolday[$calendarId]['month'][$invoiceMonth]['schoolday_array']));
					$_endDate = strtotime($invoiceMonth.max($calendarSchoolday[$calendarId]['month'][$invoiceMonth]['schoolday_array']));
					if ($invoiceMonth == $startMonth)
					{
						$_endDate = ($startMonth == $endMonth) ? $endDate : $_endDate;
						$schooldayList['byMonth'][$invoiceMonth] = $this->specialCountSchoolday($startDate, $_endDate, $calendarSchoolday[$calendarId]['month'][$invoiceMonth]);
						$totalActualSchoolday += $ceil?ceil($schooldayList['byMonth'][$invoiceMonth]['actualSchoolday']):$schooldayList['byMonth'][$invoiceMonth]['actualSchoolday'];
						$schooldayList['byMonth'][$invoiceMonth]['flag'] = 'first'; 
						$schooldayList['byMonth'][$invoiceMonth]['startDate'] = $startDate; 
						$schooldayList['byMonth'][$invoiceMonth]['endDate'] = $_endDate; 
					}
					elseif ($invoiceMonth == $endMonth)
					{
						$schooldayList['byMonth'][$invoiceMonth] = $this->specialCountSchoolday($_startDate, $endDate, $calendarSchoolday[$calendarId]['month'][$invoiceMonth]);
						$totalActualSchoolday += $ceil?ceil($schooldayList['byMonth'][$invoiceMonth]['actualSchoolday']):$schooldayList['byMonth'][$invoiceMonth]['actualSchoolday'];
						$schooldayList['byMonth'][$invoiceMonth]['flag'] = 'last';
						$schooldayList['byMonth'][$invoiceMonth]['startDate'] = $_startDate;
						$schooldayList['byMonth'][$invoiceMonth]['endDate'] = $endDate;
					}
					else
					{
						$schooldayList['byMonth'][$invoiceMonth]['actualSchoolday'] = $calendarSchoolday[$calendarId]['month'][$invoiceMonth]['schoolday'];
						$schooldayList['byMonth'][$invoiceMonth]['schoolday'] = $calendarSchoolday[$calendarId]['month'][$invoiceMonth]['schoolday'];
						$schooldayList['byMonth'][$invoiceMonth]['day'] = $calendarSchoolday[$calendarId]['month'][$invoiceMonth]['schoolday_array'];
						$totalActualSchoolday += $ceil?ceil($schooldayList['byMonth'][$invoiceMonth]['actualSchoolday']):$schooldayList['byMonth'][$invoiceMonth]['actualSchoolday'];
						$schooldayList['byMonth'][$invoiceMonth]['flag'] = 'middle'; 
						$schooldayList['byMonth'][$invoiceMonth]['startDate'] = $_startDate; 
						$schooldayList['byMonth'][$invoiceMonth]['endDate'] = $_endDate; 
					}
					$invoiceStartDate = mktime(0,0,0,date('m',$invoiceStartDate)+1,1,date('Y',$invoiceStartDate));
				}
			}
			$schooldayList['totalActualSchoolday'] = $totalActualSchoolday;
			$schooldayList['totalSchoolday'] = $totalSchoolday;
		}
		return $schooldayList;
	}
	

	/**
	 * 计算开始日期与结束日期所在月的实际的教学天数
	 * @param int $startDate
	 * @param int $endDate
	 * @param array $schoolday
	 * @return ArrayIterator
	 * <AUTHOR>
	 * @copyright Ivy
	 */
	public function specialCountSchoolday($startDate, $endDate, $schoolday)
	{
		$schooldayList = array();
		$tDay = date('d',$startDate);
		$eDay = date('d',$endDate);
		$schooldayList['schoolday'] = $schoolday['schoolday'];
		$days = $schoolday['schoolday_array'];
		// var_dump($days);
		$halfDays = $schoolday['half_schoolday_array'];
		$i = 0;
		$j = 0;
		foreach ($days as $v)
		{
			if ($v>=$tDay && $v<=$eDay)
			{
				$i++;
				$schooldayList['day'][] = $v;
			}
		}
		if (count($halfDays))
		{
			foreach ($halfDays as $v)
			{
				if ($v>=$tDay && $v<=$eDay)
				{
					$j = $j+0.5;
				}
			}
		}
		$schooldayList['actualSchoolday'] = $i - $j;
		return $schooldayList;
	}
    
    /*
     * 某段日期内有多少个周五
     * @param int $calendarId
     * @param int $startDate
     * @param int $endDate
     * @return array $days  周五的数组
     */
    static public function getFriOfDay($calendarId,$startDate,$endDate){
        $days = array();
        $schoolday = Yii::app()->db->createCommand()
                ->select('schoolday_array,month,month_label')
                ->from('ivy_calendar_schooldays')
                ->where('yid=:yid and month>=:startdate and month<=:enddate', array(':yid' =>$calendarId,':startdate'=>date('Ym',$startDate),':enddate'=>date('Ym',$endDate)))
                ->queryAll();
        if (is_array($schoolday) && count($schoolday)){
            foreach ($schoolday as $val){
                $value = explode(',',$val['schoolday_array']);
                $minDay = min($value);
                $maxDay = max($value);
                $firstFri = $minDay+5-date('N',strtotime($val['month'].$minDay));
                for($i=$firstFri;$i<=$maxDay;$i+=7){
                    if (in_array($i, $value)){
                        $_timestamp = strtotime($val['month_label'].'-'.$i);
                        $days[$_timestamp] = $_timestamp;
                    }
                }
                unset($value);
            }
            unset($schoolday);
        }
        return $days;
    }
}