<?php
class RegStep7Form extends CFormModel
{
	public $agree;
	public $filedata;


	public function rules()
	{
		return array(
			array('agree', 'required'),
            array('filedata', 'sometimes'),
		);
	}

	public function attributeLabels()
	{
		return array(
			'agree' => Yii::t("reg", "Agree"),
		);
	}

    public function sometimes($attribute, $params)
    {
        if ($this->agree == 1) {
            if (empty($this->$attribute)) {
                $this->addError($attribute, '签名不能空');
            }
        }
    }

    public function uploadFile()
    {
        // 判断是否为 base64 图片
        if (strpos($this->filedata, 'base64') === false) {
            return true;
        }
        $data = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $this->filedata));

        $normalDir = rtrim(Yii::app()->params['xoopsVarPath'], '/') .'/';

        $fileName = uniqid() . '.png';

        $filePath = $normalDir . $fileName;
        if (file_put_contents($filePath, $data)) {
            $this->filedata = $fileName;
            if ($this->filedata) {
                // 上传到 OSS
                $objectPath = 'regextrainfo/';
                $oss = CommonUtils::initOSS('private');
                if ($oss->uploadFile($objectPath . $this->filedata, $filePath)) {
                    unlink($filePath);
                    return true;
                }
            }
        }
        return false;
    }


}