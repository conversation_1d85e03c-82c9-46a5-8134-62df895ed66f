<style>
    [v-cloak] {
        display: none;
    }
    .loading{
        width:98%;
        height:100%;
        min-height:400px;
        background:#fff;
        position: absolute;
        opacity: 0.5;
        z-index: 99
    }
    .loading span{
        width:100%;
        height:100%;
        display:block;
        background: url("<?php echo Yii::app()->theme->baseUrl?>/images/loading.gif")no-repeat center center ;
    }
    .blue{
        color:#4D88D2
    }
    .image{
        width: 48px;
        height:48px;
        object-fit:cover;
    }
    .attendance tr th ,.attendance tr td{
        text-align:center;
        vertical-align: middle !important;
    }
    .table tr th{
        text-align:center;
        vertical-align: middle !important;
    }
    .middle{
        vertical-align: middle !important;
    }
    .searchImage{
        width: 72px;
        height:72px;
        object-fit:cover;
        margin: 0 auto
    }
    .font16{
        font-size:16px
    }
    .borderTop{
        border-top:1px solid #ddd;
    }
    .borderBto{
        border-bottom:1px solid #ddd;
    }
    .name-2 {
        font-size: 14px;
        font-weight: 700;
        color: #333333;
        margin-bottom: 8px;
    }
    .content-2 {
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        margin-bottom: 10px;
    }
    .piont{
        width: 6px;
        height: 6px;
        background: #666666;
        border-radius: 50%;
        display:inline-block
    }
    .searchChild{
        background: #f7f7f8;
        padding: 16px 0;
        border-radius: 5px;
    }
    .textOVerThree {
        display: -webkit-box;
        overflow: hidden;
        white-space: normal !important;
        text-overflow: ellipsis;
        word-wrap: break-word;
        -webkit-line-clamp: 4;
        -webkit-box-orient: vertical;
        max-height:80px;
        position: relative;
    }
    .showText{
        position: absolute;
        top: 51px;
        right: 0;
        width: 56px;
        background-color: #Fff;
        text-align: center;
        height: 20px;
    }
    .borderRight{
        border-right:1px solid #E4E7EDFF
    }
    .borderLeft{
        border-left:1px solid #E4E7EDFF
    }
    .mb0{
        margin-bottom:0
    }
    .font16{
        font-size:16px
    }
    .content img{
        max-width:100%
    }
    .maxHeight{
        max-height:800px;overflow-y:auto
    }
    .reports{
        padding: 9px;
        background: #F7F7F8;
        border-radius: 4px;
        display:inline-block
    }
    .stuClose{
        position: absolute;
        right: 14px;
        top: 14px;
    }
    .p0{
        padding: 0px ;
    }
    .bgPink{
        background: #FFF2F2;
    }
    .notOnline{
        display: inline-block;
        background: rgb(220, 56, 56);
        width: 100px;
        height: 24px;
        margin-right: -15px;
        border-radius: 81px 0px 0px 81px;
        color: rgb(255, 255, 255);
        line-height:24px
    }
    .padding15{
        padding:15px 15px 0
    }
    .pb15{
        padding-bottom:15px
    }
    .childImg{
        width:100px;
        height:100px
    }
    .score{
        margin:0
    }
    .score tr th{
        text-align:left !important;
        border-top:none !important;
    }
    .heading .panel-heading{
        background:#fff
    }
    .white-space{
        white-space: pre-line;
    }
    .flag_div{
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        align-content: center;
        justify-content: center;
    }
    .flag_span{
        font-size: 12px;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        line-height:20px
    }
    .iconfont{
        font-size:14px
    }
    .identifiedTab{
        display: inline-flex;
        padding: 16px 20px;
        align-items: center;
        border: 1px solid transparent;
        border-radius: 8px;
        line-height:1;
        cursor: pointer;
    }
    .identifiedTab img{
        width:32px;
        height:32px
    }
    .badBehavior{
        background: #FDF8F1;
    }
    .badBehaviorBorder{
        border: 1px solid #F0AD4E;
    }
    .goodBehavior{
        background: #F0F5FB;
    }
    .goodBehaviorBorder{
        border: 1px solid #4D88D2;
    }
    .presenter{
        border-radius: 4px;
        border:1px solid #E8EAED;
    }
    .avatar32 {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        object-fit: cover;
    }
    .circle{
        width: 6px;
        height: 6px;
        background: #969799;
        display:inline-block;
        border-radius:50%;
        margin-right: 8px;
    }
    .circleFlex{
        padding:4px 8px ;
        align-items:center;
        color:#333333;
    }
    .circleFlex:hover{
        background: #F2F3F5;
        cursor: pointer;
    }
    .badgeNum {
        min-width: 18px;
        height: 18px;
        background: #666666;
        border-radius: 9px;
        border: 1px solid #FFFFFF;
        color: #fff;
        font-size: 12px;
        text-align: center;
        line-height: 18px;
        display: inline-block;
        padding: 0 4px;
    }
    .text_overflow {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        line-height: 14px;
    }
    .p8{
        padding:8px 8px 12px !important
    }
    .imgList {
        width: 72px;
        height: 72px;
        border-radius: 4px;
        object-fit: cover;
    }
    .imgLi {
        list-style: none;
        padding-left: 0;
    }
    .labelWidth{
        width:75px;
    }
    .widthBad{
        width:120px;
    }
    .bgGrey {
        background: #F7F7F8;
        padding: 20px;
        border-radius: 4px;
    }
    .behavior-title {
        color: #333;
        padding:4px 8px 4px 20px;
        text-align: center;
        font-size: 12px;
        position: relative;
        line-height: 1;
        margin-right: -20px;
    }

    .behavior-title::before {
        content: "";
        position: absolute;
        top: 3px;
        left: -7px;
        width: 14px;
        height: 14px;
        background-color: #F7F7F8;
        transform: rotate(45deg);
        border-radius: 1px;
    }
    .bgRecord{
        background: #E5E6EB;
    }
    .colorRed{
        color:#D9534F;
        background:rgba(217, 83, 79, 0.15)
    }
    .colorGreen{
        background:rgba(92, 184, 92, 0.15);
        color:#5CB85C
    }
    .colorBlue{
        color:#4D88D2;
        background:rgba(77, 136, 210, 0.15)
    }
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Teaching Tasks'), array('//mteaching/default/index')) ?></li>
        <li><?php echo Yii::t('newDS', 'Academic Center') ?></li>
    </ol>
    <div class="row" id='container' v-cloak>
        <div>
            <div class="col-md-2 col-sm-12">
                <ul role="tablist" class="nav nav-pills mb20">
                    <li role="presentation" :class='searchType=="classList"?"active":""' @click='typeTab("classList")' ><a href="javascript:;"><?php echo Yii::t('newDS', 'By Class') ?></a></li>
                    <li role="presentation" :class='searchType=="search"?"active":""'  @click='typeTab("search")'><a href="javascript:;"><?php echo Yii::t('newDS', 'Search') ?></a></li>
                </ul>
            </div>
            <div class="col-md-10 col-sm-12">
                <ul class="nav nav-tabs font14" v-if='childId!=""'>
                    <li role="presentation"><a  aria-controls="Journals" role="tab" data-toggle="tab" href="#Journals" @click='typeData("Journals")'><?php echo Yii::t("teaching", "All Journals");?></a></li>
                    <li role="presentation"><a aria-controls="Assessment" role="tab" data-toggle="tab" href="#Assessment"  @click='typeData("Assessment")'>{{branchId=="BJ_DS"||branchId=="BJ_SLT"?"<?php echo Yii::t("teaching", "Assessments");?>":"<?php echo Yii::t("teaching", "Semester Report");?>"}}</a></li>
                    <li role="presentation"><a aria-controls="Attendance" role="tab" data-toggle="tab" href="#Attendance"  @click='typeData("Attendance")'><?php echo Yii::t("teaching", "Attendance Report");?></a></li>
                    <!-- <li role="presentation"><a  aria-controls="College" role="tab" data-toggle="tab"href="#College" @click='typeData("College")'><?php echo Yii::t("teaching", "College Counseling");?> </a></li> -->
                    <li role="presentation"><a  aria-controls="allBehavior" role="tab" data-toggle="tab" href="#allBehavior"  @click='typeData("allBehavior")' ><?php echo Yii::t("teaching", "Behavior Logs"); ?></a></li>
                    <li role="presentation"><a  aria-controls="College" role="tab" data-toggle="tab" href="#Application"  @click='typeData("Application")' ><?php echo Yii::t("teaching", "Daystar Enrollment Profile");?> </a></li>
                </ul>
            </div>
        </div>
        <div class='clearfix'></div>
        <div>
            <div class="col-md-2 col-sm-12">
                <template v-if='searchType=="classList"'>
                    <select class="form-control mb20" v-model='classId' @change='getStu()'>
                        <option  value=''><?php echo Yii::t('global', 'Please Select') ?></option>
                        <option v-for='(list,index) in classList' :value='list.id'>{{list.title}}</option>
                    </select>
                    <ul class="list-group">
                        <li class="list-group-item" v-for='(item,key,index) in childList' :class='key==childId?"active":""' @click='getList(key)'>{{item.name}}</li>
                    </ul>
                </template>
                <template v-if='searchType=="search"'>
                    <input type="text" class="form-control" v-model='stuSearchText' placeholder='<?php echo Yii::t("message", "Search by child ID, name, DOB, parent name, email, Phone NO.");?>'  @keyup.enter='searchStu()' v-on:input ="searchStu()">
                    <ul class="ui-autocomplete ui-menu ui-widget ui-widget-content ui-corner-all" v-if='searchList.length!=0' id="ui-id-1" tabindex="0" style="position: absolute;top: 30px;left: 15px; width: 500px;z-index:99">
                        <li @click="closePop()" class="close stuClose">×</li>
                        <li class="panel panel-default ui-menu-item" role="presentation">
                            <div class="panel-heading"><?php echo Yii::t("message", "Result");?></div>
                            <div class="panel-body">
                                <div id="res-list" style='max-height: 500px;overflow-y: auto;' class="list-group">
                                    <template v-for='(list,index) in searchList'>
                                        <div class="list-group-item"  v-if='list.value!=-1 && list.value!=0 && list.value!=-2'>
                                            <div class="col-sm-9" v-html='list.label'></div>
                                            <div class="col-sm-3 text-right">
                                                <a class="btn btn-default btn-xs mb5" :href="'<?php echo $this->createUrl('index'); ?>&childid='+list.value+'#'+currentType"><?php echo Yii::t("message", "OK");?></a>
                                            </div
                                            ><div class="clearfix"></div>
                                        </div>
                                        <div class="list-group-item"  v-if='list.value==0'>
                                            <div v-html='list.label'></div>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </li>
                        <li class="full" v-if='searchList[searchList.length-1].value==-2'><div>{{searchList[searchList.length-1].label}}</div></li>
                    </ul>
                    <div v-if='Object.keys(childInfo).length!=0'>
                        <!-- <p><?php echo Yii::t('newDS', 'Current Student') ?></p> -->
                        <div class='mt20 text-center searchChild' >
                            <img :src="childInfo.avatar" data-holder-rendered="true" class="media-object img-circle searchImage">
                            <div class='mt10 font14'>{{childInfo.name}}</div>
                            <p class='mt5'>{{childInfo.class_name}}</p>
                            <div class="flag_div" v-if="childInfo.label.length!=0">
                                <div v-for="(item,index) in childInfo.label" :key="index" :title="item['desc']" class="ml5 flag_span">
                                    <span v-html="item['name']" :style="'color:rgb('+item['color']+')'"></span>
                                    <span v-html="item['flag_text']"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </div>
            <div class="col-md-10 col-sm-12 tab-content">
                <div class='loading'  v-if='showLoading'>
                    <span></span>
                </div>
                <div id='Journals' role="tabpanel" class="tab-pane" >
                    <div class='mb20' v-if='Object.keys(journalData).length!=0 && !showLoading'>
                        <div v-if='journalData.schoolYear.length!=0'>
                            <select class="form-control mb20 select_3 pull-left" v-model='journalYear' @change='yearData("journalYear")'>
                                <option v-for='(list,index) in journalData.schoolYear' :value='list.value'>{{list.label}}</option>
                            </select>
                            <?php if ($this->isCd()): ?>
                            <div class="checkbox-inline ml20 mt5">
                                <label>
                                    <input type="checkbox" v-model='DM' @change='showData()'><?php echo Yii::t('newDS', 'DM Only') ?>
                                </label>
                            </div>
                            <?php endif; ?>
                            <div class='clearfix'></div>
                            <div v-if='journalData.list.length!=0'>
                                <div class='font14' v-for='(list,index) in journalData.list'>
                                    <p class='blue cur-p' @click='showContent(list)'>{{list.title}}</p>
                                    <div class='color6 font12'>{{list.start_year}}-{{list.start_year+1}} Week#{{list.week_num}} <span class="label label-default ml10" v-if='list.category==2'><?php echo Yii::t('newDS', 'Direct Message') ?></span> <span class="label label-warning ml10" v-if='list.comment_count_num>0'><?php echo Yii::t('newDS', 'Communication') ?></span></div>
                                    <div class="media ">
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)"><img :src="teacherUser(list.sign_as_uid,'photo')" data-holder-rendered="true" class="media-object img-circle image">
                                            </a>
                                        </div>
                                        <div class="media-body pt10 media-middle">
                                            <h4 class="media-heading ">{{teacherUser(list.sign_as_uid,'name')}}</h4>
                                            <div class="text-muted font12">{{teacherUser(list.sign_as_uid,'title')}}</div>
                                        </div>
                                    </div>
                                    <hr  v-if='index!=journalData.list.length-1'>
                                </div>
                                <nav aria-label="Page navigation" v-if='journalData.pagination.pages>1'  class="text-left ml10">
                                    <ul class="pagination">
                                        <li v-if='journalData.pagination.pageNum >1'>
                                            <a href="javascript:void(0)" @click="prev(journalData.pagination.pageNum,'list')" aria-label="Previous">
                                                <span aria-hidden="true">&laquo;</span>
                                            </a>
                                        </li>
                                        <li v-else class="disabled">
                                            <a aria-label="Previous">
                                                <span aria-hidden="true">&laquo;</span>
                                            </a>
                                        </li>
                                        <li v-for='(data,index) in journalData.pagination.pages' :class="{ active:index+1==journalData.pagination.pageNum }">
                                            <a href="javascript:void(0)" @click="plus(index,'list')">{{index+1}}</a>
                                        </li>
                                        <li v-if='journalData.pagination.pageNum <journalData.pagination.pages'>
                                            <a href="javascript:void(0)" @click="plus(journalData.pagination.pageNum,'list')" aria-label="Next">
                                                <span aria-hidden="true">&raquo;</span>
                                            </a>
                                        </li>
                                        <li v-else class="disabled">
                                            <a aria-label="Next">
                                                <span aria-hidden="true">&raquo;</span>
                                            </a>
                                        </li>
                                    </ul>
                                </nav>
                            </div>
                            <div class="alert alert-danger" v-else   role="alert"><?php echo Yii::t('asa','No data found.') ?></div>

                        </div>
                        <div class="alert alert-danger" v-else   role="alert"><?php echo Yii::t('asa','No data found.') ?></div>
                    </div>
                </div>
                <div id='Assessment' role="tabpanel" class="tab-pane" >
                    <div  v-if='Object.keys(assessmentData).length!=0  && !showLoading'>
                        <div v-if='assessmentData.schoolYear.length!=0'>
                            <select class="form-control mb20 select_3" v-model='assessmentYear' @change='yearData("assessmentYear")'>
                                <option v-for='(list,index) in assessmentData.schoolYear' :value='list.value'>{{list.label}}</option>
                            </select>
                            <div v-if='assessmentData.list.length!=0'>
                                <div  v-for='(item,index) in assessmentData.list' class='mb20'>
                                    <div class="panel panel-default font14" >
                                        <div class="panel-heading flex">
                                            <span class='flex1 font16'>{{item.title}}</span> <a target="_blank" class='text-right' style='width:120px' :href="item.guide" v-if='item.guide!=""'><span class='glyphicon glyphicon-question-sign mr10'></span><?php echo Yii::t('newDS','Parent Guide') ?></a></div>
                                        <div class="panel-body p0" >
                                            <div v-for='(list,idx) in item.items' class='padding15' :class='list.is_stat==0?"bgPink":""'>
                                                <div class=''>
                                                    <span class='pull-right notOnline' v-if='list.is_stat==0'><span class='glyphicon glyphicon-exclamation-sign ml10'></span>    <?php echo Yii::t('newDS','Not Online') ?></span>
                                                    <a :href="_item.url" target="_blank" v-for='(_item,i) in list.attachments' class='mr20 mb10 reports'><span class='glyphicon glyphicon-paperclip mr10'></span> {{_item.title}} </a>
                                                </div>
                                                <div class='pb15' :class='list.comment_to_parent!="" && list.comment_to_staff!=""?"mt20":idx!=item.items.length-1?"borderBto":""' >
                                                    <p v-if='list.comment_to_parent!=""'><strong>To Parent：</strong> </p>
                                                    <div v-if='list.comment_to_parent!=""' class='' v-html='list.comment_to_parent'></div>
                                                    <p v-if='list.comment_to_staff!=""' class='mt20'><strong>Private：</strong> </p>
                                                    <div   v-if='list.comment_to_staff!=""'class='' v-html='list.comment_to_staff'></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="alert alert-danger" v-else   role="alert"><?php echo Yii::t('asa','No data found.') ?></div>
                        </div>
                        <div class="alert alert-danger" v-else   role="alert"><?php echo Yii::t('asa','No data found.') ?></div>
                    </div>
                </div>
                <div id='Attendance' role="tabpanel" class="tab-pane" >
                    <div v-if='Object.keys(attendanceData).length!=0 && !showLoading'>
                        <div class='mb20'>
                            <select class="form-control mb20 select_3" v-model='attendanceYear' @change='yearData("attendanceYear")'>
                                <option v-for='(list,index) in attendanceData.schoolYear' :value='list.yid'>{{list.label}}</option>
                            </select>
                        </div>
                        <p class='color6'><strong class='font16'><?php echo Yii::t('newDS','By Day') ?></strong><span class='font14 ml10'><?php echo Yii::t('attends','Daily Attendance is based on 2nd period attendence.') ?></span></p>
                        <table class="table table-bordered attendance mt10">
                            <thead>
                            <tr>
                                <th ><?php echo Yii::t('attends','Present') ?> / <?php echo Yii::t('site','Days') ?></th>
                                <th ><?php echo Yii::t('attends','Online Present') ?> / <?php echo Yii::t('site','Days') ?></th>
                                <th ><?php echo Yii::t('attends','Tardy') ?>/<?php echo Yii::t('site','Days') ?></th>
                                <th ><?php echo Yii::t('attends','Personal Leave') ?>/<?php echo Yii::t('site','Days') ?></th>
                                <th ><?php echo Yii::t('attends','Sick Leave') ?>/<?php echo Yii::t('site','Days') ?></th>
                                <th ><?php echo Yii::t('attends','Absent') ?>/<?php echo Yii::t('site','Days') ?></th>
                                <th ><?php echo Yii::t('attends','Internal Suspension') ?>/<?php echo Yii::t('site','Days') ?></th>
                                <th ><?php echo Yii::t('attends','External Suspension') ?>/<?php echo Yii::t('site','Days') ?></th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td class='text-center' v-for='(list,key,i) in attendanceData.byDay'>
                                    <a href="javascript:;" v-if='list!=0 && key!=10 && key!=11' @click='showStu("days",key,"2")'>{{list}}</a>
                                    <span v-else-if='list!=0'>{{list}}</span>
                                </td>

                            </tr>
                            </tbody>
                        </table>
                        <div v-if='attendanceData.timetableTimes.length!=0'>
                            <p class='color6'><strong class='font16'><?php echo Yii::t('newDS','By Period') ?></strong></p>
                            <table class="table table-bordered attendance mt10">
                                <thead>
                                <tr>
                                    <th width='150'>#</th>
                                    <th ><?php echo Yii::t('attends','Present') ?> / <?php echo Yii::t('site','Days') ?></th>
                                    <th ><?php echo Yii::t('attends','Online Present') ?> / <?php echo Yii::t('site','Days') ?></th>
                                    <th ><?php echo Yii::t('attends','Tardy') ?>/<?php echo Yii::t('site','Days') ?></th>
                                    <th ><?php echo Yii::t('attends','Personal Leave') ?>/<?php echo Yii::t('site','Days') ?></th>
                                    <th ><?php echo Yii::t('attends','Sick Leave') ?>/<?php echo Yii::t('site','Days') ?></th>
                                    <th ><?php echo Yii::t('attends','Absent') ?>/<?php echo Yii::t('site','Days') ?></th>
                                    <th ><?php echo Yii::t('attends','Internal Suspension') ?>/<?php echo Yii::t('site','Days') ?></th>
                                    <th ><?php echo Yii::t('attends','External Suspension') ?>/<?php echo Yii::t('site','Days') ?></th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr v-for='(list,index) in attendanceData.timetableTimes'>
                                    <th>
                                        <p>{{list.label}}</p>
                                        <div>{{list.timeslot}}</div>
                                    </th>
                                    <template v-for='(_item,keys,i) in attendanceData.bySection[index]'>
                                        <td >
                                            <a href="javascript:;" v-if='_item!=0 && keys!=10 && keys!=11' @click='showStu("Period",keys,list.period)'>{{_item}}</a>
                                            <span v-else-if='_item!=0'>{{_item}}</span>
                                        </td>
                                    </template>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div v-else-if='!showLoading'>
                        <div class="alert alert-danger"   role="alert"><?php echo Yii::t('asa','No data found.') ?></div>
                    </div>
                </div>
                <div id='College' role="tabpanel" class="tab-pane" >
                    <template v-if='Object.keys(collegeData).length!=0 && !showLoading'>
                        <div class="panel panel-default font14" v-for='(list,key,index) in collegeData.list'>
                            <div class="panel-heading"><strong>{{collegeData.schoolYear[key].label}}</strong></div>
                            <div class="panel-body p0">
                                <div class='p15' :class='i<list.length-1?"mb20 pb10":item.is_stat=="0"?"bgPink":""' v-for='(item,i) in list'>
                                    <p><strong class='font16'>{{item.semester}}</strong>
                                        <span class='pull-right notOnline' v-if='item.is_stat=="0"'><span class='glyphicon glyphicon-exclamation-sign ml10'></span> <?php echo Yii::t('newDS','Not Online') ?></span></p>
                                    <p   v-html='html(item.college_counseling)'></p>
                                    <div class="media ">
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)"><img :src="collegeData.staffListInfo[item.uid].photoUrl" data-holder-rendered="true" class="media-object img-circle image"></a>
                                        </div>
                                        <div class='media-right pull-right'>
                                            <span class='pull-right pt10'>{{item.create_time}}</span>
                                        </div>
                                        <div class="media-body mt5 media-middle">
                                            <h4 class="media-heading font14">{{collegeData.staffListInfo[item.uid].name}}</h4>
                                            <div class="text-muted font12">{{collegeData.staffListInfo[item.uid].position}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                    <div v-else-if='!showLoading'>
                        <div class="alert alert-danger"   role="alert"><?php echo Yii::t('asa','No data found.') ?></div>
                    </div>
                </div>
                <div id='Application' role="tabpanel" class="tab-pane font14" >
                    <template v-if='Object.keys(applyData).length!=0 && !showLoading'>
                        <div class="panel panel-default" v-if='rankTableData.length!=0'>
                            <div class="panel-heading"><strong><?php echo Yii::t("application", "Assessment Ranking");?></strong></div>
                            <div class="panel-body p0">
                               <table class="table score">
                                    <tr>
                                        <th ><span class='ml10'><?php echo Yii::t("application", "Subject");?></span>   </th>
                                        <th><?php echo Yii::t("application", "Ranking");?></th>
                                        <th><?php echo Yii::t("application", "Score");?></th>
                                        <th><?php echo Yii::t("application", "Lowest Score");?></th>
                                        <th><?php echo Yii::t("application", "Highest Score");?></th>
                                        <th><?php echo Yii::t("application", "Average Score");?></th>
                                    </tr>
                                    <tr v-for='(list,index) in rankTableData' class='color6'>
                                        <td ><span class='ml10'>{{list.title}}</span></td>
                                        <td><span v-if='list.rank!="无"'>{{list.rank}}/{{list.count}}</span><span v-else>{{list.rank}}</span></td>
                                        <td>{{list.score}}</td>
                                        <td>{{list.min}}</td>
                                        <td>{{list.max}}</td>
                                        <td>{{list.avg}}</td>
                                    </tr>
                               </table>
                            </div>
                        </div>
                        <div class="panel panel-default" v-if='templateId=="f052d4c804cf44e9803d05b58e1387d1"'>
                            <div class="panel-heading"><strong><?php echo Yii::t("application", "Assessment&Interview-ES");?></strong></div>
                            <div class="panel-body heading">
                                <div class='mb10'> <button type="button" class="btn mr20"  v-for='(list,index) in scoreDataList' :class='showInterviewIndex==index?"btn-primary":"btn-default"' @click='tabInterview(index)'>第{{index+1}}次面试</button></div>
                                <div>
                                    <p>
                                        <span class='color6'>Type of previous school：</span>
                                        <label class="radio-inline color3" v-for='(list,index) in schoolType' >
                                            <input type="radio"  v-model='scoreData.school_type' :value="list.value" disabled> {{list.label}}
                                        </label>
                                    </p>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>Records indicate student has</strong></div>
                                        <div class="panel-body">
                                            <p><span class='color6'>Physical disabilities：</span><span class='color3'>{{scoreData.txt_phy_disab}} </span></p>
                                            <p><span class='color6'>Behavioral concerns： </span><span class='color3'>{{scoreData.txt_behave_concern}}</span></p>
                                            <p><span class='color6'>Learning difficulties：</span><span class='color3'> {{scoreData.txt_learning_diff}}</span></p>
                                        </div>
                                    </div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>MAP Reading</strong></div>
                                        <div class="panel-body">
                                            <p><span class='color6'>RIT： </span><span class='color3'>{{scoreData.rit_map_reading}}</span></p>
                                            <p><span class='color6'>Grade Level/Score：</span><span class='color3'>{{scoreData.grade_map_reading}}</span></p>
                                        </div>
                                    </div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>Chinese Language</strong></div>
                                        <div class="panel-body">
                                            <p><span class='color6'>RIT：</span><span class='color3'> {{scoreData.rit_cn_lang}}</span></p>
                                            <p><span class='color6'>Grade Level/Score：</span><span class='color3'>{{scoreData.grade_cn_lang}}</span></p>
                                        </div>
                                    </div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>Chinese Math</strong></div>
                                        <div class="panel-body">
                                            <p><span class='color6'>RIT： </span><span class='color3'>{{scoreData.rit_cn_math}}</span></p>
                                            <p><span class='color6'>Grade Level/Score：</span> <span class='color3'>{{scoreData.grade_cn_math}}</span></p>
                                        </div>
                                    </div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>Suggested Placement</strong></div>
                                        <div class="panel-body">
                                        <span class='color3'>{{scoreData.suggested_placement}}</span>
                                        </div>
                                    </div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>Student Interview</strong></div>
                                        <div class="panel-body">
                                            <p><span class='color6'>Name of Inteviewer(s)：</span><span class='color3'>{{scoreData.interviewer_child}}</span> </p>
                                            <p></p> 
                                            <p>
                                                <span class='color6'>Verbal English assessment：</span>
                                                <label class="radio-inline color3" v-for='(list,index) in assessment'>
                                                    <input type="radio"  :value="list.value"  v-model='scoreData.verbal_en' disabled> {{list.label}}
                                                </label>
                                            </p>
                                            <p>
                                                <span class='color6'>Verbal Chinese assessment：</span>
                                                <label class="radio-inline color3" v-for='(list,index) in assessment'>
                                                    <input type="radio"  :value="list.value" v-model='scoreData.verbal_cn' disabled> {{list.label}}
                                                </label>
                                            </p> 
                                            <p>
                                                <span class='color6'>Feedback：</span>
                                                <label class="radio-inline color3" v-for='(list,index) in Feedback'>
                                                    <input type="radio"  :value="list.value" v-model='scoreData.feedback_child' disabled> {{list.label}}
                                                </label>
                                            </p>
                                            <p class='flex'><span class='color6'>Note：</span><span class='color3 flex1 white-space' v-html='html(scoreData.note_child)'></span> </p>
                                        </div>
                                    </div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>Parent Interview</strong></div>
                                        <div class="panel-body">
                                            <p><span class='color6'>Name of Inteviewer(s)：</span><span class='color3'> {{scoreData.interviewer_parent}}</span></p>
                                            <p>
                                                <span class='color6'>Feedback：</span>
                                                <label class="radio-inline color3" v-for='(list,index) in Feedback'>
                                                    <input type="radio"  :value="list.value" v-model='scoreData.feedback_parent' disabled> {{list.label}}
                                                </label>
                                            </p>
                                            <p class='flex'><span class='color6'>Note：</span><span class='color3 flex1 white-space' v-html='html(scoreData.note_parent)'></span> </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="panel panel-default" v-if='templateId=="4cbfd8311b3d4c2a8bf25b8f589614cc"'>
                            <div class="panel-heading"><strong><?php echo Yii::t("application", "Assessment &interview-SS");?></strong></div>
                            <div class="panel-body heading">
                                <div class='mb10'> <button type="button" class="btn mr20"  v-for='(list,index) in scoreDataList' :class='showInterviewIndex==index?"btn-primary":"btn-default"' @click='tabInterview(index)'>第{{index+1}}次面试</button></div>
                                <div>
                                    <p>
                                        <span class='color6'>Type of previous school：</span>
                                        <label class="radio-inline color3" v-for='(list,index) in schoolType' >
                                            <input type="radio"  v-model='scoreData.school_type' :value="list.value" disabled='true'> {{list.label}}
                                        </label>
                                    </p>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>Records indicate student has</strong></div>
                                        <div class="panel-body">
                                            <p><span class='color6'>Physical disabilities：</span><span class='color3'>{{scoreData.txt_phy_disab}}</span> </p>
                                            <p><span class='color6'>Behavioral concerns： </span><span class='color3'>{{scoreData.txt_behave_concern}}</span></p>
                                            <p><span class='color6'>Learning difficulties：</span> <span class='color3'>{{scoreData.txt_learning_diff}}</span></p>
                                        </div>
                                    </div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>MAP Reading</strong></div>
                                        <div class="panel-body">
                                            <p><span class='color6'>RIT： </span><span class='color3'>{{scoreData.rit_map_reading}}</span></p>
                                            <p><span class='color6'>Grade Level/Score：</span><span class='color3'>{{scoreData.grade_map_reading}}</span></p>
                                        </div>
                                    </div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>Language in Use</strong></div>
                                        <div class="panel-body">
                                            <p><span class='color6'>RIT：</span> <span class='color3'>{{scoreData.rit_lang_inuse}}</span></p>
                                            <p><span class='color6'>Grade Level/Score：</span><span class='color3'>{{scoreData.grade_lang_inuse}}</span></p>
                                        </div>
                                    </div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>Map Math</strong></div>
                                        <div class="panel-body">
                                            <p><span class='color6'>RIT： </span> <span class='color3'>{{scoreData.rit_map_math}}</span></p>
                                            <p><span class='color6'>Grade Level/Score：</span>  <span class='color3'>{{scoreData.grade_map_math}}</span></p>
                                        </div>
                                    </div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>Chinese (if need)</strong></div>
                                        <div class="panel-body">
                                            <p><span class='color6'>RIT：</span><span class='color3'>{{scoreData.rit_cn_math}}</span></p>
                                            <p><span class='color6'>Grade Level/Score：</span><span class='color3'>{{scoreData.grade_cn_math}}</span></p>
                                        </div>
                                    </div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>Suggested Placement</strong></div>
                                        <div class="panel-body">
                                            <span class='color3'>{{scoreData.suggested_placement}}</span>
                                        </div>
                                    </div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>Student Interview</strong></div>
                                        <div class="panel-body">
                                            <p><span class='color6'>Name of Inteviewer(s)：</span><span class='color3'>{{scoreData.interviewer_child}}</span> </p>
                                            <p></p> 
                                            <p>
                                                <span class='color6'>Verbal English assessment：</span>
                                                <label class="radio-inline color3" v-for='(list,index) in assessment'>
                                                    <input type="radio"  :value="list.value"  v-model='scoreData.verbal_en' disabled> {{list.label}}
                                                </label>
                                            </p>
                                            <p>
                                                <span class='color6'>Verbal Chinese assessment：</span>
                                                <label class="radio-inline color3" v-for='(list,index) in assessment'>
                                                    <input type="radio"  :value="list.value" v-model='scoreData.verbal_cn' disabled> {{list.label}}
                                                </label>
                                            </p> 
                                            <p>
                                                <span class='color6'>Feedback：</span>
                                                <label class="radio-inline color3" v-for='(list,index) in Feedback'>
                                                    <input type="radio"  :value="list.value" v-model='scoreData.feedback_child' disabled> {{list.label}}
                                                </label>
                                            </p>
                                            <p class='flex'><span class='color6'>Note：</span><span class='color3 flex1 white-space' v-html='html(scoreData.note_child)'></span></p>
                                        </div>
                                    </div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>Parent Interview</strong></div>
                                        <div class="panel-body">
                                            <p><span class='color6'>Name of Inteviewer(s)：</span><span class='color3'> {{scoreData.interviewer_parent}}</span></p>
                                            <p>
                                                <span class='color6'>Feedback：</span>
                                                <label class="radio-inline color3" v-for='(list,index) in Feedback'>
                                                    <input type="radio"  :value="list.value" v-model='scoreData.feedback_parent' disabled> {{list.label}}
                                                </label>
                                            </p>
                                            <p class='flex'><span class='color6'>Note：</span><span class='color3 flex1 white-space' v-html='html(scoreData.note_parent)'></span></p>
                                        </div>
                                    </div>
                               </div>
                            </div>
                        </div>
                        <div class="panel panel-default" v-if='templateId=="0a78f5786d80448cb1b4c88b8ab1bf39"'>
                            <div class="panel-heading"><strong>泉发考试结果</strong></div>
                            <div class="panel-body heading">
                                <div class='mb10'> <button type="button" class="btn mr20"  v-for='(list,index) in scoreDataList' :class='showInterviewIndex==index?"btn-primary":"btn-default"' @click='tabInterview(index)'>第{{index+1}}次面试</button></div>
                                <div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>Basic</strong></div>
                                        <div class="panel-body">
                                            <p><span class='color6'>sibling_at：</span><span class='color3'>{{scoreData.sibling_at}}</span> </p>
                                            <p><span class='color6'>Staff name (for staff child)： </span><span class='color3'>{{scoreData.staff_name}}</span></p>
                                        </div>
                                    </div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>Type of previous school</strong></div>
                                        <div class="panel-body">
                                            <p>
                                                <span class='color6'>Type of previous school：</span>
                                                <label class="radio-inline color3" v-for='(list,index) in schoolType' >
                                                    <input type="radio"  v-model='scoreData.school_type' :value="list.value" disabled='true'> {{list.label}}
                                                </label>
                                            </p>
                                        </div>
                                    </div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>Records indicate student has</strong></div>
                                        <div class="panel-body">
                                            <p><span class='color6'>Physical disabilities：</span> <span class='color3'>{{scoreData.txt_phy_disab}}</span></p>
                                            <p><span class='color6'>Behavioral concerns：</span><span class='color3'>{{scoreData.txt_behave_concern}}</span></p>
                                            <p><span class='color6'>Learning difficulties：</span><span class='color3'>{{scoreData.txt_learning_diff}}</span></p>
                                        </div>
                                    </div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>Suggested Placement</strong></div>
                                        <div class="panel-body">
                                            <p><span class='color6'>Suggested Placement： </span> <span class='color3'>{{scoreData.group_placement}}</span></p>
                                        </div>
                                    </div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>Student Interview</strong></div>
                                        <div class="panel-body">
                                            <p><span class='color6'>Name of Inteviewer(s)：</span><span class='color3'>{{scoreData.interviewer_child}}</span> </p>
                                            <p></p> 
                                            <p>
                                                <span class='color6'>Verbal English assessment：</span>
                                                <label class="radio-inline color3" v-for='(list,index) in assessment'>
                                                    <input type="radio"  :value="list.value"  v-model='scoreData.verbal_en' disabled> {{list.label}}
                                                </label>
                                            </p>
                                            <p>
                                                <span class='color6'>Verbal Chinese assessment：</span>
                                                <label class="radio-inline color3" v-for='(list,index) in assessment'>
                                                    <input type="radio"  :value="list.value" v-model='scoreData.verbal_cn' disabled> {{list.label}}
                                                </label>
                                            </p> 
                                            <p>
                                                <span class='color6'>Feedback：</span>
                                                <label class="radio-inline color3" v-for='(list,index) in Feedback'>
                                                    <input type="radio"  :value="list.value" v-model='scoreData.feedback_child' disabled> {{list.label}}
                                                </label>
                                            </p>
                                            <p class='flex'><span class='color6'>Note：</span><span class='color3 flex1 white-space' v-html='html(scoreData.note_child)'></span></p>
                                        </div>
                                    </div>
                                    <div class="panel panel-default">
                                        <div class="panel-heading"><strong>Parent Interview</strong></div>
                                        <div class="panel-body">
                                            <p><span class='color6'>Name of Inteviewer(s)：</span><span class='color3'> {{scoreData.interviewer_parent}}</span></p>
                                            <p>
                                                <span class='color6'>Feedback：</span>
                                                <label class="radio-inline color3" v-for='(list,index) in Feedback'>
                                                    <input type="radio"  :value="list.value" v-model='scoreData.feedback_parent' disabled> {{list.label}}
                                                </label>
                                            </p>
                                            <p class='flex'><span class='color6'>Note：</span><span class='color3 flex1 white-space' v-html='html(scoreData.note_parent)'></span></p>
                                        </div>
                                    </div>
                               </div>
                            </div>
                        </div>
                        <div class="panel panel-default">
                            <div class="panel-heading"><strong><?php echo Yii::t("application", "Student Basic Information");?></strong></div>
                            <div class="panel-body">
                                <p class='col-md-4 col-sm-12 flex'>
                                <span class='color6'><?php echo Yii::t("application", "Chinese Name");?>：</span> <span class='color3 flex1'>{{applyData.application.name_cn}}</span> 
                                </p>
                                <p class='col-md-4 col-sm-12 flex'>
                                    <span class='color6'><?php echo Yii::t("application", "Legal First Name");?>：</span> <span class='color3 flex1'>{{applyData.application.first_name}}</span> 
                                </p>
                                <p class='col-md-4 col-sm-12 flex'>
                                    <span class='color6'><?php echo Yii::t("application", "Legal Middle Name");?>：</span> <span class='color3 flex1'>{{applyData.application.middle_name}}</span> 
                                </p>
                                <p class='col-md-4 col-sm-12 flex'>
                                    <span class='color6'><?php echo Yii::t("application", "Legal Last Name");?>：</span> <span class='color3 flex1'>{{applyData.application.last_name}}</span> 
                                </p>
                                <p class='col-md-4 col-sm-12 flex'>
                                    <span class='color6'><?php echo Yii::t("application", "Gender");?>： </span> <span class='color3 flex1'>{{applyData.application.gender=='1'?'<?php echo Yii::t("application", "Male");?>':applyData.application.gender=='2'?'<?php echo Yii::t("application", "Female");?>':''}}</span>
                                </p>
                                <p class='col-md-4 col-sm-12 flex'>
                                    <span class='color6'><?php echo Yii::t("application", "Nationality");?>：</span> <span class='color3 flex1'>{{countryFilter(applyData.application.country) }}</span>
                                </p>
                                <p class='col-md-4 col-sm-12 flex'>
                                    <span class='color6'><?php echo Yii::t("application", "Date of Birth");?>：</span> <span class='color3 flex1'>{{applyData.application.birthday}}</span>
                                </p>
                                <p class='col-md-4 col-sm-12 flex'>
                                    <span class='color6'><?php echo Yii::t("application", "Passport ID");?>：</span> <span class='color3 flex1'>{{applyData.application.identity}}</span>
                                </p>
                                <p class='col-md-4 col-sm-12 flex'>
                                    <span class='color6'><?php echo Yii::t("application", "Native Language");?>：</span> <span class='color3 flex1'>{{languageFilter(applyData.application.native_lang)}}</span>
                                </p>
                            </div>
                        </div>
                        <div class="panel panel-default">
                            <div class="panel-heading"><strong><?php echo Yii::t("application", "Current School");?></strong></div>
                            <div class="panel-body">
                                <div  class='col-md-12 col-sm-12' v-for='(list,index) in applyData.application.school_history'>
                                    <p class='col-md-4 col-sm-12 flex'>
                                    <span class='color6'><?php echo Yii::t("application", "Current School");?>：</span> <span class='color3 flex1'>{{list.name}}</span> 
                                    </p>
                                    <p class='col-md-4 col-sm-12 flex'>
                                        <span class='color6'><?php echo Yii::t("application", "City Country");?>：</span> <span class='color3 flex1'>{{list.city}}</span> 
                                    </p>
                                    <p class='col-md-4 col-sm-12 flex'>
                                        <span class='color6'><?php echo Yii::t("application", "Dates Attended");?>：</span> <span class='color3 flex1'>{{list.dates_attended}}</span>
                                    </p>
                                    <p class='col-md-4 col-sm-12 flex'>
                                        <span class='color6'><?php echo Yii::t("application", "Grade");?>：</span> <span class='color3 flex1'>{{list.grade}}</span>
                                    </p>
                                    <p class='col-md-4 col-sm-12 flex'>
                                        <span class='color6'><?php echo Yii::t("application", "Curriculum");?></span> <span class='color3 flex1'>{{list.curriculum}}</span>
                                    </p>
                                    <p class='col-md-4 col-sm-12 flex'>
                                        <span class='color6'><?php echo Yii::t("application", "Instruction Language");?>：</span> <span class='color3 flex1'>{{list.instruction_language}}</span>
                                    </p>
                                    <div class='clearfix'></div>
                                    <hr v-if='index!=applyData.application.school_history.length-1'>
                                </div>
                            </div>
                        </div>
                        <div class="panel panel-default">
                            <div class="panel-heading"><strong><?php echo Yii::t("application", "Entry Information");?> </strong></div>
                            <div class="panel-body">
                                <p class='col-md-4 col-sm-12 flex'>
                                <span class='color6'><?php echo Yii::t("application", "Campus");?>：</span> <span class='color3 flex1'>{{applyData.application.campusText}}</span> 
                                </p>
                                <p class='col-md-4 col-sm-12 flex'>
                                    <span class='color6'><?php echo Yii::t("application", "Entry Year");?>：</span> <span class='color3 flex1'>{{applyData.application.yearText}}</span> 
                                </p>
                                <p class='col-md-4 col-sm-12 flex'>
                                    <span class='color6'><?php echo Yii::t("application", "Grade");?>：</span> <span class='color3 flex1'>{{applyData.application.gradeText}}</span>
                                </p>
                                <p class='col-md-4 col-sm-12 flex'>
                                    <span class='color6'><?php echo Yii::t("application", "Staff Child?");?>：</span> <span class='color3 flex1'>{{applyData.application.is_staff=='1'?'<?php echo Yii::t("reg", "Yes");?>':'<?php echo Yii::t("reg", "No");?>'}}</span>
                                </p>
                                <p class='col-md-4 col-sm-12 flex'>
                                    <span class='color6'><?php echo Yii::t("application", "Siblings at School?");?>：</span> <span class='color3 flex1'>{{applyData.application.has_siblings=='1'?'<?php echo Yii::t("reg", "Yes");?>':'<?php echo Yii::t("reg", "No");?>'}}</span>
                                </p>
                            </div>
                        </div>
                        <div class="panel panel-default">
                            <div class="panel-heading"> <strong><?php echo Yii::t("application", "Guardians");?> </strong></div>
                            <div class="panel-body">
                                <div  class='col-md-12 col-sm-12'  v-for='(list,index) in applyData.application.guardian'>
                                    <p class='col-md-4 col-sm-12 flex'>
                                    <span class='color6'><?php echo Yii::t("application", "Name");?>：</span> <span class='color3 flex1'>{{list.name}}</span> 
                                    </p>
                                    <p class='col-md-4 col-sm-12 flex'>
                                        <span class='color6'><?php echo Yii::t("application", "Gender");?>：</span> <span class='color3 flex1'>{{list.gender=='2'?'<?php echo Yii::t("application", "Female");?>':list.gender=='1'?'<?php echo Yii::t("application", "Male");?>':''}}</span> 
                                    </p>
                                    <p class='col-md-4 col-sm-12 flex'>
                                        <span class='color6'><?php echo Yii::t("application", "Phone");?>：</span> <span class='color3 flex1'>{{list.mobile}}</span>
                                    </p>
                                    <p class='col-md-4 col-sm-12 flex'>
                                        <span class='color6'><?php echo Yii::t("application", "Email");?>：</span> <span class='color3 flex1'>{{list.email}}</span>
                                    </p>
                                    <p class='col-md-4 col-sm-12 flex'>
                                        <span class='color6'><?php echo Yii::t("application", "Nationality");?>：</span> <span class='color3 flex1'>{{list.nationality}}</span>
                                    </p>
                                    <p class='col-md-4 col-sm-12 flex'>
                                        <span class='color6'><?php echo Yii::t("application", "Employer");?>：</span> <span class='color3 flex1'>{{list.employer}}</span>
                                    </p>
                                    <p class='col-md-4 col-sm-12 flex'>
                                        <span class='color6'><?php echo Yii::t("application", "Position");?>： </span> <span class='color3 flex1'>{{list.position}}</span>
                                    </p>
                                    <div class='clearfix'></div>
                                    <hr v-if='index!=applyData.application.guardian.length-1'>
                                </div>
                            </div>
                        </div>
                        <div class="panel panel-default">
                            <div class="panel-heading"><strong><?php echo Yii::t("application", "Address");?></strong></div>
                            <div class="panel-body">
                                <p class='col-md-4 col-sm-12 flex'>
                                    <span class='color6'><?php echo Yii::t("application", "City");?>：</span> <span class='color3 flex1'>{{applyData.application.home_addr_cidy}}</span> 
                                </p>
                                <p class='col-md-4 col-sm-12 flex'>
                                    <span class='color6'><?php echo Yii::t("application", "District");?>：</span> <span class='color3 flex1'>{{applyData.application['1626853720827_8003']}}</span> 
                                </p>
                                <p class='col-md-4 col-sm-12 flex'>
                                    <span class='color6'><?php echo Yii::t("application", "Street");?>：</span> <span class='color3 flex1'>{{applyData.application['1626853728705_96838']}}</span> 
                                </p>
                                <p class='col-md-4 col-sm-12 flex'>
                                    <span class='color6'><?php echo Yii::t("application", "Room NO.");?>：</span> <span class='color3 flex1'>{{applyData.application['1626853759385_8289']}}</span> 
                                </p>
                            </div>
                        </div>
                        <div class="panel panel-default">
                            <div class="panel-heading"><strong>	<?php echo Yii::t("application", "Photo of Student");?></strong></div>
                            <div class="panel-body">
                                <img :src="applyData.application.photo" alt="" class='childImg mr20' >
                            </div>
                        </div>
                        <div class="panel panel-default">
                            <div class="panel-heading"><strong><?php echo Yii::t("application", "Progress reports");?></strong></div>
                            <div class="panel-body">
                                <p v-for='(list,index) in applyData.application.report_files'><a :href="list.value" target="_blank"><span class='glyphicon glyphicon-paperclip font12'></span> {{list.label}}</a> </p>
                            </div>
                        </div>
                        <div class="panel panel-default">
                            <div class="panel-heading"><strong><?php echo Yii::t("application", "ID Information");?></strong></div>
                            <div class="panel-body">
                                <!-- 共有{{applyData.application.identity_files.length}}个身份信息文件，已隐藏。 -->
                                <?php echo Yii::t("application", "Please contact Admissions for student and parents ID/Passport copies");?>
                            </div>
                        </div>
                        <div class="panel panel-default">
                            <div class="panel-heading"><strong><?php echo Yii::t("application", "Interests or Talents");?> </strong></div>
                            <div class="panel-body">
                                <p v-html='html(applyData.application.interests)'></p>
                            </div>
                        </div>
                        <div class="panel panel-default">
                            <div class="panel-heading"><strong><?php echo Yii::t("application", "Learning Disorders");?></strong></div>
                            <div class="panel-body">
                                <p v-html='html(applyData.application.disorders)'></p>
                            </div>
                        </div>
                        <div class="panel panel-default">
                            <div class="panel-heading"><strong><?php echo Yii::t("application", "Physical Problems");?></strong></div>
                            <div class="panel-body">
                                <p v-html='html(applyData.application.physical)'></p>
                            </div>
                        </div>
                        <div class="panel panel-default">
                            <div class="panel-heading"><strong><?php echo Yii::t("application", "Is the candidate neurodiverse (sometimes referred to as having a special educational need)?Examples may include ADHD, dyslexia, dyspraxia, autistic spectrum condition");?></strong></div>
                            <div class="panel-body">
                                <p>{{applyData.application.special_educational=='1'?'<?php echo Yii::t("reg", "Yes");?>':'<?php echo Yii::t("reg", "No");?>'}}</p>
                            </div>
                        </div>
                        <div class="panel panel-default">
                            <div class="panel-heading"><strong><?php echo Yii::t("application", "Please provide details about the neurodiversity");?></strong></div>
                            <div class="panel-body">
                                <p v-html='html(applyData.application.neurodiversity)' ></p>
                            </div>
                        </div>
                        <div class="panel panel-default">
                            <div class="panel-heading"><strong><?php echo Yii::t("application", "Does the candidate receive additional learning support from their current school in connection with this neurodiversity?");?></strong></div>
                            <div class="panel-body">
                                <p>{{applyData.application.learning_support=='1'?'<?php echo Yii::t("reg", "Yes");?>':'<?php echo Yii::t("reg", "No");?>'}}</p>
                            </div>
                        </div>
                        <div class="panel panel-default">
                            <div class="panel-heading"><strong><?php echo Yii::t("application", "Do you have a specialist's report (e.g. from a doctor, educational psychologist or psychiatrist) confirming this neurodiversity?");?></strong></div>
                            <div class="panel-body">
                                <p>{{applyData.application.specialist_report=='1'?'<?php echo Yii::t("reg", "Yes");?>':'<?php echo Yii::t("reg", "No");?>'}}</p>
                            </div>
                        </div>
                        <div class="panel panel-default">
                            <div class="panel-heading"><strong><?php echo Yii::t("application", "If yes, please submit the report with this form");?></strong></div>
                            <div class="panel-body">
                                <p v-for='(list,index) in applyData.application.specialist_submit'><a :href="list.value" target="_blank"><span class='glyphicon glyphicon-paperclip font12'></span> {{list.label}}</a> </p>
                            </div>
                        </div>
                        <div class="panel panel-default">
                            <div class="panel-heading"><strong><?php echo Yii::t("application", "Does the candidate have any serious allergies or medical conditions of which we should be aware?");?></strong></div>
                            <div class="panel-body">
                                <p>{{applyData.application.allergies=='1'?'<?php echo Yii::t("reg", "Yes");?>':'<?php echo Yii::t("reg", "No");?>'}}</p>
                            </div>
                        </div>
                        <div class="panel panel-default">
                            <div class="panel-heading"><strong><?php echo Yii::t("application", "If yes, please provide details");?></strong></div>
                            <div class="panel-body">
                                <p v-html='html(applyData.application.allergies_details)' ></p>
                            </div>
                        </div>
                        <div class="panel panel-default">
                            <div class="panel-heading"><strong><?php echo Yii::t("application", "Does the candidate suffer from any physical disability?");?></strong></div>
                            <div class="panel-body">
                                <p>{{applyData.application.physical_disability=='1'?'<?php echo Yii::t("reg", "Yes");?>':'<?php echo Yii::t("reg", "No");?>'}}</p>
                            </div>
                        </div>
                        <div class="panel panel-default">
                            <div class="panel-heading"><strong><?php echo Yii::t("application", "If yes, please provide details");?></strong></div>
                            <div class="panel-body">
                                <p v-html='html(applyData.application.physical_disability_details)' ></p>
                            </div>
                        </div>
                        <div class="panel panel-default">
                            <div class="panel-heading"><strong><?php echo Yii::t("application", "I understand and agree that failure to provide complete and accurate information may nullify a student’s enrollment at Daystar Academy");?></strong></div>
                            <div class="panel-body">
                                <p>{{applyData.application.understand=='1'?'<?php echo Yii::t("application", "Yes");?>':''}}</p>
                            </div>
                        </div>
                        <div class="panel panel-default">
                            <div class="panel-heading"><strong><?php echo Yii::t("application", "Additional Materials");?></strong></div>
                            <div class="panel-body">
                                <p class='flex'><span  class='color6 '><?php echo Yii::t("application", "Notes");?>：</span> <span class='color3 flex1' v-html='html(applyData.appMaterialInfo.admissionsComment)'></span> </p>
                                <p v-for='(list,index) in applyData.appMaterialInfo.applyMaterial'><a href="javascript:;" @click='showImg2(list)'><span class='glyphicon glyphicon-paperclip font12'></span> {{list.title}}</a> </p>

                            </div>
                        </div>
                    </template>
                    <div v-else-if='!showLoading'>
                        <div class="alert alert-danger"   role="alert"><?php echo Yii::t('asa','No data found.') ?></div>
                    </div>
                </div>
                <div id='allBehavior' role="tabpanel" class="tab-pane" >
                    <div v-if='affirmRecordList.list && affirmRecordList.child_info[childId]'>
                        <div class='mb20 flex' v-if='affirmRecordList.child_info[childId].grade=="HS" || affirmRecordList.child_info[childId].grade=="MS"' >
                            <div class='flex1'>
                                <div class='identifiedTab badBehavior'  @click='behaviorTabDetail="bad"' :class='behaviorTabDetail=="bad"?"badBehaviorBorder":""'>
                                    <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/praise3.png' ?>" alt="">
                                    <div class='ml12'>
                                        <div class='font12 color3 mb4'><?php echo Yii::t("referral", "Negative Behavior"); ?></div>
                                        <div class='font16 color3'>{{affirmRecordList.list.length}}</div>
                                    </div>
                                </div>
                                <div class='identifiedTab ml20 goodBehavior' @click='behaviorTabDetail="good"' :class='behaviorTabDetail=="good"?"goodBehaviorBorder":""'>
                                    <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/praise2.png' ?>" alt="">
                                    <div class='ml12'>
                                        <div class='font12 color3 mb4'><?php echo Yii::t("behavior", "Positive Behavior"); ?></div>
                                        <div class='font16 color3'>{{affirmRecordList.positive.length}}</div>
                                    </div>
                                </div>
                            </div>
                            <div v-if='affirmRecordList.schoolYear.length!=0' >
                                <select class="form-control select_3" v-model='start_year' @change='showData()'>
                                    <option v-for='(list,index) in affirmRecordList.schoolYear' :value='list.value'>{{list.label}}</option>
                                </select>
                            </div>
                        </div>
                        <div v-else-if='affirmRecordList.schoolYear.length!=0' class='flex'>
                            <div class='flex1'></div>
                            <select class="form-control mb16 select_3 " v-model='start_year' @change='showData()'>
                                <option v-for='(list,index) in affirmRecordList.schoolYear' :value='list.value'>{{list.label}}</option>
                            </select>
                        </div>
                        <div v-if='behaviorTabDetail=="bad"'>
                            <div v-if='affirmRecordList.list.length!=0'>
                                <div class='col-md-12 p16 presenter pt15 pb15 m0'>
                                    <div class='col-md-7 '>
                                        <div class='font12 color6 mb12'><?php echo Yii::t("referral", "A behavior issue"); ?>：{{affirmRecordList.stat.behaviorNum}}</div>
                                        <div class='flex mt12'>
                                            <div class='flex1 text-center'>
                                                <div class='font14 color3 fontBold'>{{affirmRecordList.stat.inSchoolSuspensionNum}}</div>
                                                <div class='mt4 font12 color3'><?php echo Yii::t("referral", "In school suspension"); ?></div>
                                            </div>
                                            <div class='flex1 text-center'>
                                                <div class='font14 color3 fontBold'>{{affirmRecordList.stat.outSchoolSuspensionNum}}</div>
                                                <div class='mt4 font12 color3'><?php echo Yii::t("referral", "Out of school suspension"); ?></div>
                                            </div>
                                            <div class='flex1 text-center'>
                                                <div class='font14 color3 fontBold'>{{affirmRecordList.stat.serviceNum}}</div>
                                                <div class='mt4 font12 color3'><?php echo Yii::t("referral", "Community Service"); ?></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class='col-md-5 borderLeft'>
                                        <div class='font12 color6 mb12 pl16'><?php echo Yii::t("report", "Other"); ?>：{{affirmRecordList.stat.otherNum}}</div>
                                        <div class='flex mt12'>
                                            <div class='flex1 text-center'>
                                                <div class='font14 color3 fontBold'>{{affirmRecordList.stat.undeterminedNum}}</div>
                                                <div class='mt4 font12 color3'><?php echo Yii::t("referral", "NOT a behavior issue"); ?></div>
                                            </div>
                                            <div class='flex1 text-center'>
                                                <div class='font14 color3 fontBold'>{{affirmRecordList.stat.recordOnlyNum}}</div>
                                                <div class='mt4 font12 color3'><?php echo Yii::t("referral", "Record only"); ?></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class='clearfix'></div>
                                <div v-if='affirmRecordList.stat.problemBehavior.length!=0' class='presenter p8 mt16 mb16'>
                                    <div class='p8 color6'><?php echo Yii::t("referral", "Problem Behavior"); ?></div>
                                    <div class='row'>
                                        <div class='col-md-6' v-for='(list,key,index) in affirmRecordList.stat.problemBehavior'>
                                            <div class='flex circleFlex'  onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" :title="showTitle(key,'problem_behavior')"  data-placement="top">
                                                <span class="circle"></span>
                                                <div class=' text_overflow mr20'>{{showTitle(key,'problem_behavior')}} </div>
                                                <span class="badgeNum">{{list}}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div v-for='(item,idx) in affirmRecordList.list' class='bgGrey mt12 relative '>
                                    <div>
                                        <div class='flex font14'>
                                            <div class='flex1 color3 font14'>No.{{item.id}}</div> 
                                            <span class='behavior-title color6 bgRecord' v-if='item.result.identified==3'><?php echo Yii::t("referral", "Record only, no followup needed"); ?></span>
                                            <span class='behavior-title'v-else :class='item.result.identified==1?"colorRed":"colorGreen"'>{{showTitle(item.result.identified,'fact')}}</span>
                                        </div>
                                        <div class='color6 font12 mt16'>{{item.desc}}</div>
                                        <div class='flex align-items mt12'>
                                            <img :src="affirmRecordList.staff_info[item.result.created_by].photoUrl" data-holder-rendered="true" class="avatar32">
                                            <span class='ml10 color3 font12'>{{affirmRecordList.staff_info[item.result.created_by].name}}</span>
                                            <span class='color9 font12'>｜</span>
                                            <div class='flex1 color3 font12'>{{item.result.created_at}}</div>
                                        </div>
                                        <div class='flex mt12 font12'  v-if='item.result.identified!=3'>
                                            <span class='color9  '><?php echo Yii::t("referral", "Final Result"); ?>：</span>
                                            <div class='flex1 color3'>
                                                <span class='mr10' v-for='(list,index) in item.result.actions'>{{showTitle(list,'actions')}}</span>
                                                <span v-if='item.followup.date'>{{item.followup.date[0]}} ~ {{item.followup.date[1]}}</span>
                                            </div>
                                        </div>
                                        <div class='flex mt12 font12' v-if='item.result.identified!=3'>
                                            <span class='color9  '><?php echo Yii::t("referral", "Comment"); ?>：</span>
                                            <div class='flex1 color3' v-html='htmlBr(item.result.remark)'></div>
                                        </div>
                                        <div class='flex mt12 font12' v-if='item.result.attachments && (item.result.attachments.img || item.result.attachments.other)'>
                                            <span class='color9  '><?php echo Yii::t("curriculum", "Attachments"); ?>：</span>
                                            <div class='flex1 color3'>
                                                <div class=''>
                                                    <ul class='mb12 imgLi'  id='record'  v-if='item.result.attachments.img'>
                                                        <li v-for='(list,i) in item.result.attachments.img'>
                                                            <img :src="affirmRecordList.attachmentList[list].file_key" class='imgList mb8 mr8' @click='showImg("record",item.result.attachments.img)' alt=""  >
                                                        </li>
                                                    </ul>
                                                </div>
                                                <div class='mt12 color3' v-if='item.result.attachments.other'>
                                                    <div v-for='(list,j) in item.result.attachments.other'>
                                                        <div class='flex fileLink' >
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='affirmRecordList.attachmentList[list].mime_type=="application/pdf"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='affirmRecordList.attachmentList[list].mime_type=="application/vnd.ms-excel" || affirmRecordList.attachmentList[list].mime_type=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='affirmRecordList.attachmentList[list].mime_type=="application/msword" || affirmRecordList.attachmentList[list].mime_type=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='affirmRecordList.attachmentList[list].mime_type=="application/x-zip-compressed"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                                            <a class='flex1 ml5' target= "_blank" :href="list.url">{{affirmRecordList.attachmentList[list].title}}
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else-if='!showLoading'>
                                <div class="alert alert-danger"   role="alert"><?php echo Yii::t('asa','No data found.') ?></div>
                            </div>
                        </div>
                        <div v-if='behaviorTabDetail=="good"'>
                            <div v-if='affirmRecordList.positive.length!=0'>
                                <div v-for='(item,idx) in affirmRecordList.positive' class='bgGrey mt12 relative '>
                                    <div class='flex font14'>
                                        <div class='flex1 color3 font14'>No.{{item.id}}</div> 
                                        <span class='behavior-title font12 colorBlue'><?php echo Yii::t("behavior", "Positive Behavior"); ?></span>
                                    </div>
                                    <div class='color6 font12 mt16'>{{item.desc}}</div>
                                    <div class='flex align-items mt12'>
                                        <img :src="affirmRecordList.staff_info[item.created_by].photoUrl" data-holder-rendered="true" class="avatar32">
                                        <span class='ml10 color3 font12'>{{affirmRecordList.staff_info[item.created_by].name}}</span>
                                        <span class='color9 font12'>｜</span>
                                        <div class='flex1 color3 font12'>{{item.created_at}}</div>
                                    </div>
                                </div>
                            </div>
                            <div v-else-if='!showLoading'>
                                <div class="alert alert-danger"   role="alert"><?php echo Yii::t('asa','No data found.') ?></div>
                            </div>
                        </div>
                    </div>
                    <div v-else-if='!showLoading'>
                        <div class="alert alert-danger"   role="alert"><?php echo Yii::t('asa','No data found.') ?></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal fade" id="childModel" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" ><?php echo Yii::t('attends','Student List') ?> <span id="remarks_t"></span></h4>
                    </div>
                    <div class="form-horizontal">
                        <div class="modal-body">
                            <div class='p10'>
                                <ul class="nav nav-pills mb20" role="tablist">
                                    <li role="presentation"  :class='key==type?"active":""'  v-for='(list,key,index) in childData.countList' @click='showStu(currentModal,key,period)'><a href="javascript:;">{{types[key]}} <span class="badge">{{list}}</span></a></li>
                                </ul>
                                <div class="alert alert-danger mt20"  v-if='childData.studentInfo && childData.studentInfo.length==0' role="alert"><?php echo Yii::t('asa','No data found.') ?></div>
                                <div style='max-height:400px;overflow-y:auto' v-else class='scroll-box'>
                                    <div class='col-md-3' v-for='(list,key,index) in childData.studentInfo' >
                                        <div class='flex'>
                                            <div style='width:16px'>
                                                <span class='piont'></span>
                                            </div>
                                            <div class='flex1'>
                                                <div class='color3 font14 mb5'>{{list.target_date}}</div>
                                                <div class='color6 font14' v-if='currentModal=="Period"'>#{{list.period}} <span class='ml5'>({{childData.timetableTimes[list.period].timeslot}})</span> </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class='clearfix'></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 查看内容 -->
        <div class="modal fade" id="contentModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "Content");?></h4>
                    </div>
                    <div class="modal-body">
                        <form class="form-horizontal " :class='journalsContent.comment_count_num==0?"col-md-12 col-sm-12":"col-md-6 col-sm-6 scroll-box maxHeight"'  >
                            <p class='font14'><strong><?php echo Yii::t('newDS', 'Detail') ?></strong></p>
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <h3 class='mb0'><strong>{{contentData.title}}</strong></h3>
                                </div>
                            </div>
                            <div class="form-group" v-if='contentData.type=="leader"'>
                                <div class="col-sm-12">
                                    <h3 class='mb0'><strong>{{contentData.title_en}}</strong></h3>
                                </div>
                            </div>
                            <div class="form-group" >
                                <div class="col-sm-12">
                                    <span class="label label-default defaultBg defaultBg mr5 mb5 " > {{contentData.category==1?"<?php echo Yii::t('newDS', 'Normal Journal') ?>":"<?php echo Yii::t('newDS', 'Direct Message') ?>"}}</span>
                                </div>
                            </div>
                            <div class="form-group"  v-if='contentData.type=="leader"'>
                                <div class="col-sm-12">
                                    <span class="label label-default defaultBg defaultBg mr5" v-for='(item,id) in contentData.grade_group_id'>{{journalData.groupMap[item]}}</span>
                                </div>
                            </div>
                            <div class="form-group mt20 pt10 content">
                                <div class="col-sm-12 pt7" style="padding: 0 30px;">
                                    <div class='content' v-html='contentData.content'></div>
                                </div>
                            </div>
                            <div class="form-group content" v-if='contentData.type=="leader"'>
                                <div class="col-sm-12 pt7" style="padding: 0 30px;">
                                    <div class='content' v-html='contentData.content_en'></div>
                                </div>
                            </div>
                            <div class="form-group"  v-if='contentData.type=="teacher"'>
                                <div class="col-sm-12 pt7" >
                                    <div class="media ">
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img class="media-object img-circle image" :src="teacherUser(contentData.sign_as_uid,'photo')" data-holder-rendered="true" >
                                            </a>
                                        </div>
                                        <div class="media-body pt10 media-middle">
                                            <h4  class="media-heading font12">{{teacherUser(contentData.sign_as_uid,'name')}}</h4>
                                            <div class='text-muted'>
                                                {{teacherUser(contentData.sign_as_uid,'title')}}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group"  v-if='contentData.type=="leader"'>
                                <div class="col-sm-12 pt7" >
                                    <div class="media ">
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img class="media-object img-circle image" :src="contentData.user_info.photo" data-holder-rendered="true" >
                                            </a>
                                        </div>
                                        <div class="media-body pt10 media-middle">
                                            <h4  class="media-heading font12">{{contentData.user_info.name}}</h4>
                                            <div class='text-muted'>
                                                {{contentData.user_info.title}}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-12 pt7" >
                                    <p v-for='(list,index) in attachments'>
                                        <a target="_blank" style='line-height:26px' :href='list.file_key' >{{list.title}}</a>
                                    </p>
                                </div>
                            </div>
                        </form>
                        <div class='col-md-6 col-sm-6 scroll-box borderLeft' v-if='commentData.item' >
                            <p class='font14'><strong><?php echo Yii::t('newDS', 'View Comments') ?></strong> </p>
                            <div class='mt20' style="margin-bottom: 15px;" v-for="(fItem, i) in commentData.item" :key="i">
                                <div v-if='fItem.mark_as_staff==0'>
                                    <div class="row" v-if="fItem.creator_type == 'parent'">
                                        <div class="col-md-2 text-center">
                                            <img :src="fItem.photo" class="img-circle image" width="50">
                                        </div>
                                        <div class="col-md-10">
                                            <div class="name-2">{{fItem.name}}</div>
                                            <div class="content-2"  v-html='html(fItem.content)'></div>
                                            <div class="text-muted" v-if="fItem.created_by_identity == 'f'"><?php echo Yii::t("newDS", "From student's Dad");?> {{ fItem.updated_at }}</div>
                                            <div class="text-muted" v-if="fItem.created_by_identity == 'm'"><?php echo Yii::t("newDS", "From student's Mom");?> {{ fItem.updated_at }}</div>
                                        </div>
                                    </div>
                                    <div class="row" v-if="fItem.creator_type == 'staff'">
                                        <div class="col-md-2 text-center">
                                            <img :src="commentData.staffListInfo[fItem.created_by].photoUrl" class="img-circle image" width="50">
                                        </div>
                                        <div class="col-md-10">
                                            <div class="name-2">{{commentData.staffListInfo[fItem.created_by].name}}</div>
                                            <div class="content-2" v-html='html(fItem.content)'></div>
                                            <div class="text-muted">{{ fItem.updated_at }}</div>
                                        </div>
                                    </div>
                                </div>
                                <div v-else class='p20 mt20 text-center borderTop ' :class='i!=commentData.item.length-1?"borderBto":""'>
                                    <p class='font14 color3'>
                                        <span class='glyphicon glyphicon-pushpin mr10' style='color:#EC971FFF'></span>{{commentData.staffListInfo[fItem.created_by].name}} <?php echo Yii::t("newDS", "marked No Reply Needed");?> <span class='font14 color6 ml10'>{{fItem.updated_at}}</span>
                                    </p>
                                    <div  class='font12 color9'>
                                        <?php echo Yii::t("newDS", "(This message is invisible to parents)");?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class='clearfix'></div>
                </div>
            </div>
        </div>
    </div>

</div>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<!-- 加载底部导航栏 -->
<?php
//$this->branchSelectParams['extraUrlArray'] = array('//mgrades/attendance/index');
//$this->renderPartial('//layouts/common/branchSelectBottom');
$types = array(
    0 => Yii::t('attends', 'Action'),
    9 => Yii::t('attends', 'Reset'),
    10 => Yii::t('attends', 'Present'),
    11 => Yii::t('attends', 'Online Present'),
    20 => Yii::t('attends', 'Tardy'),
    30 => Yii::t('attends', 'Personal Leave'),
    31 => Yii::t('attends', 'Sick Leave'),
    40 => Yii::t('attends', 'Absent'),
    41 => Yii::t('attends', 'Internal Suspension'),
    42 => Yii::t('attends', 'External Suspension'),
    205 => sprintf(Yii::t('attends', 'Tardy %s Minutes'), 5),
    2010 => sprintf(Yii::t('attends', 'Tardy %s Minutes'), 10),
    2015 => sprintf(Yii::t('attends', 'Tardy %s Minutes'), 15),
    2020 => sprintf(Yii::t('attends', 'Tardy %s Minutes'), 20),
);

?>
<script>
    $(function () {
        $('[data-toggle="tooltip"]').tooltip()
    }) 
    var lang='<?php echo Yii::app()->language;?>'
    var types = <?php unset($types[0]);echo CJSON::encode($types); ?>;
    var childId = '<?php echo Yii::app()->request->getParam('childid', '')?>';
    var branchId = '<?php echo Yii::app()->request->getParam('branchId', '')?>';
    var container = new Vue({
        el: "#container",
        data: {
            lang:lang,
            types:types,
            branchId:branchId,
            contentData:{},
            attachments:[],
            searchType:'classList',
            classList:[],
            classId:'',
            childList:{},
            childId:'',
            currentType:'',
            stuSearchText:'',
            DM:false,
            journalData:{},
            journalYear:'',
            journalsContent:{},
            commentData:{},
            assessmentData:{},
            assessmentYear:'',
            attendanceData:{},
            attendanceYear:'',
            childData:{},
            pageNum:1,
            showLoading:false,
            childInfo:{},
            collegeData:{},
            searchList:[],
            currentModal:'',
            applyData:{},
            countryData:[],
            languageData:[],
            rankTableData:[],
            scoreData:{},
            templateId:'',
            schoolType:[
                {
                    "label": "Public School or Privite Public School",
                    "value": "1"
                },
                {
                    "label": "Home-school",
                    "value": "2"
                },
                {
                    "label": "International School/Abroad",
                    "value": "3"
                },
                {
                    "label": "Bilingual school ",
                    "value": "4"
                }
            ],
            assessment:[
                {
                    "label": "Native",
                    "value": "0"
                },
                {
                    "label": "P",
                    "value": "1"
                },
                {
                    "label": "D",
                    "value": "2"
                },
                {
                    "label": "B",
                    "value": "3"
                },
                {
                    "label": "NR",
                    "value": "4"
                }
            ],
            Feedback:[
                {
                    "label": "Best fit",
                    "value": "1"
                },
                {
                    "label": "Fit",
                    "value": "2"
                },
                {
                    "label": "Wait-list",
                    "value": "3"
                },
                {
                    "label": "Not acceptable",
                    "value": "4"
                }
            ],
            Anchor:{

            },
            showInterviewIndex:0,
            scoreDataList:[],
            behaviorTabDetail:"bad",
            affirmRecordList:{},
            start_year:'0'
        },
        watch:{},
        created:function(){
            let that=this
            var hash = window.location.hash;
            if (hash) {
                this.currentType=hash.substr(1,hash.length)
                $('.nav-tabs a[href="' + hash + '"]').tab('show');
                this.showLoading=true
                this.showData()
            }else{
                this.currentType='Application'
                if(branchId=='BJ_QFF'){
                    $('.nav-tabs a[href="#Assessment"]').tab('show');
                    history.pushState(null,null, '#Assessment');
                }else{
                    $('.nav-tabs a[href="#Application"]').tab('show');
                    history.pushState(null,null, '#Application');
                }

            }
            if(childId!=''){
                this.childId=childId
                this.searchType='search'
                this.getChildInfo()
            }
            this.init()
            $.ajax({
                url: 'https://daystar.isapply.com/dict/country',
                type: "get",
                dataType: 'json',
                data: {
                },
                success: function(data) {
                    if(data.code==0){
                       that.countryData=data.data
                    }else{
                    }
                }
            })
            $.ajax({
                url: 'https://daystar.isapply.com/dict/language',
                type: "get",
                dataType: 'json',
                data: {
                },
                success: function(data) {
                    if(data.code==0){
                       that.languageData=data.data
                    }else{
                    }
                }
            })
            
        },
        methods: {
            countryFilter(id){
                let str=this.countryData.find(item => item.value == id).label
                let label= str.split('[[@]]')
                return label[0]
            },
            languageFilter(id){
                let str=this.languageData.find(item => item.value == id).label
                let label= str.split('[[@]]')
                return label[0]
            },
            html(data){
                if(data!=null){
                    return  data.replace(/\n/g, '<br/>')
                }
            },
            school(type,year){
                for(var i=0;i<this[type].schoolYear;i++){
                    if(this[type].schoolYear[i].value==this[year]){
                        return this[type].schoolYear[i].label
                    }
                }
            },
            init(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("yearClass") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.classList=data.data.classList
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            typeTab(type){
                this.searchType=type
                if(type=='search'){
                    if(childId!='' && this.childId!=childId){
                        this.childId=childId
                        this.getChildInfo()
                    }
                }else{
                    if(this.classId!=''){
                        this.getStu()
                    }
                }
            },
            getChildInfo(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getChildInfo") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        child_id:this.childId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.childInfo=data.data
                            that.classId=data.data.class_id
                            that.showData()
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            getStu(){
                let that=this
                if(this.classId==null || this.classId===''){
                    this.classId=''
                    return
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("GetStudentByClass") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        classId:this.classId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.childList=data.data
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            closePop(){
                this.searchList=[]
            },
            searchStu(){
                let that=this
                if(this.stuSearchText.trim()==''){
                    that.searchList=[]
                    return
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("/backend/childSearchByCampus/childoutput") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        term:this.stuSearchText
                    },
                    success: function(data) {
                        that.searchList=data
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            typeData(type){
                this.currentType=type
                $('.nav-tabs a').on('shown.bs.tab', function (e) {
                    history.pushState(null,null, e.target.hash);
                });
                this.showData()
            },
            getList(id){
                this.childId=id
                this.pageNum=1
                this.assessmentYear=''
                this.journalYear=''
                this.attendanceYear=''
                this.showData('init')
            },
            yearData(type){
                if(this[type]!=''){
                    this.showData()
                }
            },
            plus(index) {
                this.pageNum = Number(index) + 1
                this.showData()
            },
            prev(index) {
                this.pageNum = Number(index) - 1
                this.showData()
            },
            showData(type){
                let that=this
                if(this.childId==''){
                    return
                }
                this.showLoading=true
                if(this.currentType=='Journals'){
                    $.ajax({
                        url: '<?php echo $this->createUrl("getListByChildId") ?>',
                        type: "get",
                        dataType: 'json',
                        data: {
                            "child_id":this.childId,
                            "start_year":this.journalYear,
                            "class_id":this.classId,
                            "messages":this.DM?1:2,
                            "pageNum":this.pageNum,
                            "pageSize":20
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.journalData=data.data
                                that.journalYear=data.data.checkYear
                            } else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                            that.showLoading=false
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.showLoading=false
                        },
                    })
                }else if(this.currentType=='Reports' || this.currentType=='Assessment'){
                    var sendData={}
                    if(this.assessmentYear==''){
                        sendData={
                            "child_id":this.childId,
                            "class_id":this.classId,
                        }
                    }else{
                        sendData={
                            "child_id":this.childId,
                            "class_id":this.classId,
                            "start_year":this.assessmentYear
                        }
                    }
                    $.ajax({
                        url: '<?php echo $this->createUrl("getAssessmentReportsByChild") ?>',
                        type: "get",
                        dataType: 'json',
                        data: sendData,
                        success: function(data) {
                            if (data.state == 'success') {
                                that.assessmentData=data.data
                                that.assessmentYear=data.data.startYear
                            } else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                            that.showLoading=false
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.showLoading=false
                        },
                    })
                }else if(this.currentType=='Attendance'){
                    $.ajax({
                        url: '<?php echo $this->createUrl("getChildAttenanceProfile") ?>',
                        type: "get",
                        dataType: 'json',
                        data: {
                            "childid":this.childId,
                            "yid":this.attendanceYear,
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.attendanceData=data.data
                                that.attendanceYear=data.data.yid
                            } else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                            that.showLoading=false
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.showLoading=false
                        },
                    })
                }else if(this.currentType=='College'){
                    $.ajax({
                        url: '<?php echo $this->createUrl("getChildCollegeCounseling") ?>',
                        type: "get",
                        dataType: 'json',
                        data: {
                            "child_id":this.childId,
                            "class_id":this.classId,
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.collegeData=data.data
                            } else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                            that.showLoading=false
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.showLoading=false
                        },
                    })
                }else if(this.currentType=='Application'){
                    $.ajax({
                        url: '<?php echo $this->createUrl("//mcampus/admissions/applyInfo") ?>',
                        type: "get",
                        dataType: 'json',
                        data: {
                            // "childId":20775,
                            childId:that.childId
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                if(data.data.length!=0){
                                    that.applyData=data.data
                                    if(data.data.scoreData!==null){
                                        that.scoreDataList=data.data.scoreData
                                        that.showInterviewIndex=that.scoreDataList.length-1
                                        that.scoreData=that.scoreDataList[that.showInterviewIndex].mapData
                                        that.templateId=that.scoreDataList[that.showInterviewIndex].templateId
                                    }else{
                                        that.scoreDataList=[]
                                        that.scoreData=[]
                                        that.showInterviewIndex=0
                                        that.templateId=''
                                    }
                                    var rankData = [];
                                    if(data.data.rankData){
                                        data.data.rankData.rankCfg.rankItems.forEach((item, index) => {
                                        if (data.data.rankData.rank[item]) {
                                            var rankObj = {
                                            title: data.data.rankData.rankCfg.propNames[item],
                                            score:
                                                data.data.rankData.score[index] == null
                                                ? "无"
                                                : data.data.rankData.score[index],
                                            rank: data.data.rankData.rank[item].rank
                                                ? data.data.rankData.rank[item].rank
                                                : "无",
                                            count: data.data.rankData.rank[item].count,
                                            min: data.data.rankData.rank[item].min,
                                            max: data.data.rankData.rank[item].max,
                                            avg: data.data.rankData.rank[item].avg,
                                            };
                                        } else {
                                            var rankObj = {
                                            title: data.data.rankData.rankCfg.propNames[item],
                                            score: "无",
                                            rank: "无",
                                            count: "无",
                                            min: "无",
                                            max: "无",
                                            avg: "无",
                                            };
                                        }

                                        rankData.push(rankObj);
                                        });
                                        that.rankTableData = rankData;
                                    }
                                }else {
                                    that.applyData = {}
                                }
                            } else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                            that.showLoading=false
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.showLoading=false
                        },
                    })
                }else if(this.currentType=='allBehavior'){
                    this.affirmRecordList={}
                    this.behaviorTabDetail="bad"
                    $.ajax({
                        url: '<?php echo $this->createUrl("/mteaching/student/allBehaviorAffirmRecordList") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            child_id:that.childId,
                            start_year: type?0:this.start_year,
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.affirmRecordList=data.data
                                that.start_year=data.data.start_year
                            } else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                            that.showLoading=false
                        },
                        error: function(data) {
                            that.showLoading=false
                        },
                    })
                }
            },
            // showTotalIntro (list,type) {
            //     list[type+'showTotal'] = !list[type+'showTotal'];
            //     list[type+'exchangeButton']= !list[type+'exchangeButton'];
            // },
            // showAssessment(){
            //     this.$nextTick(function () {
            //         for(var j=0;j<this.assessmentData.list.length;j++){

            //             let refParent=this.assessmentData.list[j].refParent
            //             let refStaff=this.assessmentData.list[j].refStaff
            //             if (this.$refs[refStaff]!=undefined && this.$refs[refStaff].length!=0) {
            //                 let descHeight = window.getComputedStyle(this.$refs[refStaff][0]).height.replace('px', '');
            //                 if (descHeight >80) {
            //                     Vue.set( this.assessmentData.list[j], 'staffshowExchangeButton', true);
            //                     Vue.set( this.assessmentData.list[j], 'staffexchangeButton', true);
            //                     Vue.set( this.assessmentData.list[j], 'staffshowTotal', false);
            //                 } else {
            //                     Vue.set( this.assessmentData.list[j], 'showTotal', true);
            //                     Vue.set( this.assessmentData.list[j], 'showExchangeButton', false);
            //                 }
            //             }
            //             if (this.$refs[refParent]!=undefined && this.$refs[refParent].length!=0) {
            //                 let descHeight = window.getComputedStyle(this.$refs[refParent][0]).height.replace('px', '');
            //                 if (descHeight >80) {
            //                     Vue.set( this.assessmentData.list[j], 'parentshowExchangeButton', true);
            //                     Vue.set( this.assessmentData.list[j], 'parentexchangeButton', true);
            //                     Vue.set( this.assessmentData.list[j], 'parentshowTotal', false);
            //                 } else {
            //                     Vue.set( this.assessmentData.list[j], 'parentshowTotal', true);
            //                     Vue.set( this.assessmentData.list[j], 'parentshowExchangeButton', false);
            //                 }
            //             }
            //         }
            //     })
            // },
            teacherUser(id, type) {
                var data
                for(var i=0;i<this.journalData.userList.length;i++){
                    if (this.journalData.userList[i].uid == id) {
                        data = this.journalData.userList[i][type]
                    }
                }
                return data
            },
            leaderUser(id, type) {
                var data
                this.leaderuserList.forEach(item => {
                    if (item.uid == id) {
                        data = item[type]
                    }
                })
                return data
            },
            showContent(list){
                this.journalsContent=list
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/getOne") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id: list._id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.contentData=data.data
                            $('#contentModal').modal('show')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/getAttachments") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        linkId: list._id,
                        linkType:'journal'
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.attachments=data.data.data
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
                if(list.comment_count_num==0){
                    that.commentData={}
                    return
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("commentItem") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        "child_id":this.childId,
                        id:list._id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.commentData=data.data
                            $('#contentModal').modal('show')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            showStu(currentModal,type,period){
                let that=this
                this.period=period
                this.type=type
                this.currentModal=currentModal
                $.ajax({
                    url: '<?php echo $this->createUrl("showStudentAttendByYid") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        yid:this.attendanceYear,
                        childid:this.childId,
                        type:type,
                        period:period
                    },
                    success: function(data) {
                        if(data.state=='success'){
                            that.childData=data.data
                            $('#childModel').modal('show');
                        }else{
                        }
                    }
                })
            },
            showImg2(list){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("/mcampus/admissions/privateFileLink") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        "filename": list.url,
                        "style": ""
                    },
                    success: function(data) {
                        if(data.state=='success'){
                            window.open(data.data)
                        }else{
                        }
                    }
                })
            },
            tabInterview(index){
                this.showInterviewIndex=index
                this.scoreData=this.scoreDataList[this.showInterviewIndex].mapData
                this.templateId=this.scoreDataList[this.showInterviewIndex].templateId
            },
            showTitle(data,type){
                for(var i=0;i<this.affirmRecordList.behaviorConfig[type].length;i++){
                    if(this.affirmRecordList.behaviorConfig[type][i].value==data){
                        return this.affirmRecordList.behaviorConfig[type][i].title
                    }
                }
            },
            htmlBr(content){
                if(content!=null){
                    var reg=new RegExp("\n","g");
                    content= content.replace(reg,"<br/>");
                }
                return content
            },
            showImg(id,list){
                var viewer = new Viewer(document.getElementById(id),{
                    fullscreen: false,
                    title:false,
                    scalable:false,
                    show:function(){ 
                        if(list.length==1){
                            $('.viewer-prev').hide()
                            $('.viewer-next').hide()
                        }
                    },
                    hide:function(){ 
                        viewer.destroy()
                    }
                });
                $("#"+id).click();
            },
        }
    })
</script>