<?php
$this->breadcrumbs = array(
    Yii::t("navigations", "Resources") => array('/child/resource/index'),
    Yii::t("navigations", "Library"),
);
?>

<div class="mb-1em">

    <div id="card-info">
    <?php if (!empty($card_info)): ?>
        
            <h3><?php echo Yii::t("library", "My Library Card"); ?></h3>
            <div class="info">
                <dl>
                    <dt><?php echo Yii::t("userinfo", "Name"); ?></dt>
                    <dd class="number"><?php echo $this->myChildObjs[$this->childid]->getChildName(); ?></dd>
                </dl>
                <dl>
                    <dt><?php echo Yii::t("library", "Card NO."); ?></dt>
                    <dd class="number"><?php echo $card_info->cardid; ?></dd>
                </dl>
                <dl>
                    <dt><?php echo Yii::t("library", "Expires"); ?></dt>
                    <dd class="number"><?php echo Mims::formatDateTime($card_info->end_time, 'medium'); ?></dd>
                </dl>
            </div>
            <div class="status" title="<?php echo Yii::t("library", "Borrowed / Maximum"); ?>"><span><?php echo $card_info->borrower_num; ?></span>/ <?php echo $card_info->cardType->amount; ?></div>
    <?php else:?>
            <h3><?php echo Yii::t("library", "My Library Card"); ?></h3>
            <div class="no-card">
                <?php echo Yii::t("library", "You have no library card."); ?>
            </div>
        
    
    <?php endif; ?>
    </div>

    <div class="desc">

        <h1>
            <label><?php echo Yii::t("library", 'Ivy Library Introduction'); ?></label>
        </h1>
        <?php echo Yii::t("library", "Our campus provides an extensive and open children’s library where every book has been carefully selected by our team of educational experts.  To get a library card or to find out more about the library, please contact our office staff."); ?>
    </div>

</div>

<div class="clear"></div>
<h1>
    <label><?php echo Yii::t("library", 'Books Outstanding'); ?></label>
</h1>

<?php
$model = new InoutRecords();
$this->widget('zii.widgets.grid.CGridView', array(
    'id' => 'borrowing-grid',
    'cssFile' => Yii::app()->theme->baseUrl . '/widgets/gridview/styles.css',
    'dataProvider' => $borrowingDataProvider,
    'columns' => array(
        'bookid',
        array(
            'header' => Yii::t("library", 'Book Title'),
            'value' => array($model, 'renderBookTitle'),
        ),
        array(
            'name' => 'borrowtime',
            'value' => 'Mims::formatDateTime($data->borrowtime,"medium","short")',
        ),
        array(
            'name' => 'backtime',
            'value' => 'Mims::formatDateTime($data->backtime,"medium","short")',
        ),
    ),
));
?>


<h1>
    <label><?php echo Yii::t("library", 'History of Books Borrowed'); ?></label>
</h1>


<?php
$model = new InoutRecords();
$this->widget('zii.widgets.grid.CGridView', array(
    'id' => 'borrow-history-grid',
    'cssFile' => Yii::app()->theme->baseUrl . '/widgets/gridview/styles.css',
    'dataProvider' => $borrowHistoryDataProvider,
    'ajaxUpdate' => true,
    'pager' => array(
        'class' => 'CLinkPager',
        'cssFile' => Yii::app()->theme->baseUrl . '/css/pager.css',
    ),
    'columns' => array(
        'bookid',
        array(
            'header' => Yii::t("library", 'Book Title'),
            'value' => array($model, 'renderBookTitle'),
        ),
        array(
            'name' => 'borrowtime',
            'value' => 'Mims::formatDateTime($data->borrowtime,"medium","short")',
        ),
        array(
            'header' => Yii::t("library", 'Date Returned'),
            'value' => 'Mims::formatDateTime($data->updatetime,"medium","short")',
        ),
    ),
));
?>

