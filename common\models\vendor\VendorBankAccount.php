<?php

/**
 * This is the model class for table "ivy_vendor_bank_account".
 *
 * The followings are the available columns in table 'ivy_vendor_bank_account':
 * @property integer $id
 * @property integer $vendor_id
 * @property string $account_bank
 * @property string $account_title
 * @property string $account
 * @property string $memo
 * @property integer $status
 * @property integer $update_timestamp
 * @property integer $update_user
 */
class VendorBankAccount extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_vendor_bank_account';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('vendor_id', 'required'),
			array('vendor_id, status, update_timestamp, update_user', 'numerical', 'integerOnly'=>true),
			array('account_bank, account_title', 'length', 'max'=>225),
			array('account', 'length', 'max'=>25),
			array('memo', 'length', 'max'=>1024),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, vendor_id, account_bank, account_title, account, memo, status, update_timestamp, update_user', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'vendor_id' => 'Vendor',
			'account_bank' => 'Account Bank',
			'account_title' => 'Account Title',
			'account' => 'Account',
			'memo' => 'Memo',
			'status' => 'Status',
			'update_timestamp' => 'Update Timestamp',
			'update_user' => 'Update User',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('vendor_id',$this->vendor_id);
		$criteria->compare('account_bank',$this->account_bank,true);
		$criteria->compare('account_title',$this->account_title,true);
		$criteria->compare('account',$this->account,true);
		$criteria->compare('memo',$this->memo,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('update_timestamp',$this->update_timestamp);
		$criteria->compare('update_user',$this->update_user);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return VendorBankAccount the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
