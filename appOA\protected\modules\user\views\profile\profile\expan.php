<?php
$staffLabels = Staff::model()->attributeLabels();
$this->staff->staff->card_id_due = ($this->staff->staff->card_id_due) ? date('Y-m-d',$this->staff->staff->card_id_due) : '';
$this->staff->staff->startdate = ($this->staff->staff->startdate) ? date('Y-m-d',$this->staff->staff->startdate) : '';
$this->staff->staff->contract_period = ($this->staff->staff->contract_period) ? date('Y-m-d',$this->staff->staff->contract_period) : '';
$this->staff->staff->birthday_search = ($this->staff->staff->birthday_search) ? $this->staff->staff->birthday_search : '';
$this->staff->staff->visa_expiry = ($this->staff->staff->visa_expiry) ? date('Y-m-d',$this->staff->staff->visa_expiry) : '';

$relationship_opt = Staff::relationshipOpt();
$highest_type_opt = Staff::highestTypeOpt();
$license_type_opt = Staff::licenseTypeOpt();
?>
<?php echo CHtml::form($this->createUrl('//user/profile/saveExpan'), 'post', array('class'=>'form-horizontal J_ajaxForm','role'=>'form'))?>
<div class="panel panel-default">
    <div class="panel-heading">
        <h3 class="panel-title">
            <!--扩展资料-->
            <span class="edit-title"></span>
            <small style="font-size: 80%;">- <?php echo Yii::t('userinfo', 'All items on this page are viewable by HR only')?></small>
        </h3>
    </div>
    <div class="panel-body">
        <div class="form-group" model-attribute="pemail">
            <?php echo CHtml::label($staffLabels['pemail'], CHtml::getIdByName('Staff[pemail]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-4 col-sm-9">
                <?php echo CHtml::textField('Staff[pemail]', $this->staff->staff->pemail,array('class'=>'form-control','placeholder'=>$staffLabels['pemail']));?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($staffLabels['mobile_telephone'], CHtml::getIdByName('Staff[mobile_telephone]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-4 col-sm-9">
                <?php echo CHtml::textField('Staff[mobile_telephone]', $this->staff->staff->mobile_telephone,array('class'=>'form-control','placeholder'=>$staffLabels['mobile_telephone']));?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($staffLabels['birthday_search'], CHtml::getIdByName('Staff[birthday_search]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-4 col-sm-9">
                <?php
                $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                    "model"=>$this->staff->staff,
                    "attribute"=>"birthday_search",
                    "options"=>array(
                        'changeMonth'=>true,
                        'changeYear'=>true,
                        'dateFormat'=>'yy-mm-dd',
                        'maxDate'=>date('Y-m-d', time()-567648000),
                        'yearRange' => "-80:+0",
                    ),
                    "htmlOptions"=>array(
                        'class' => 'form-control',
                        'onchange' => 'autoMake()'
                    )
                ));
                ?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label(Yii::t('userinfo', 'Age'), 'age', array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-4 col-sm-9">
                <?php echo CHtml::textField('age', '',array('class'=>'form-control', 'disabled' => 'disabled'));?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($staffLabels['passport_name'], CHtml::getIdByName('Staff[passport_name]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-4 col-sm-9">
                <?php echo CHtml::textField('Staff[passport_name]', $this->staff->staff->passport_name, array('oninput' => 'this.value = this.value.toUpperCase();', 'class'=>'form-control','placeholder'=>$staffLabels['passport_name']));?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($staffLabels['card_id'], CHtml::getIdByName('Staff[card_id]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-4 col-sm-9">
                <?php echo CHtml::textField('Staff[card_id]', $this->staff->staff->card_id,array('class'=>'form-control','placeholder'=>$staffLabels['card_id']));?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($staffLabels['card_id_due'], CHtml::getIdByName('Staff[card_id_due]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-4 col-sm-9">
                <?php
                    $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                        "model"=>$this->staff->staff,
                        "attribute"=>"card_id_due",
                        "options"=>array(
                            'changeMonth'=>true,
                            'changeYear'=>true,
                            'dateFormat'=>'yy-mm-dd',
                        ),
                        "htmlOptions"=>array(
                            'class'=>'form-control'
                        )
                    ));
                    ?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($staffLabels['visa_expiry'], CHtml::getIdByName('Staff[visa_expiry]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-4 col-sm-9">
                <?php
                $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                    "model"=>$this->staff->staff,
                    "attribute"=>"visa_expiry",
                    "options"=>array(
                        'changeMonth'=>true,
                        'changeYear'=>true,
                        'dateFormat'=>'yy-mm-dd',
                    ),
                    "htmlOptions"=>array(
                        'class'=>'form-control'
                    )
                ));
                ?>
            </div>
        </div>
        <div class="form-group" model-attribute="startdate">
            <?php echo CHtml::label($staffLabels['startdate'], CHtml::getIdByName('Staff[startdate]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-4 col-sm-9">
                <?php echo CHtml::textField('Staff[startdate]', $this->staff->staff->startdate,array('class'=>'form-control','disabled'=>'disabled'));?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label(Yii::t('userinfo', 'Years at Ivy/Daystar'), 'at_ivy', array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-4 col-sm-9">
                <?php echo CHtml::textField('at_ivy', '',array('class'=>'form-control', 'disabled' => 'disabled'));?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($staffLabels['contract_type'], CHtml::getIdByName('Staff[contract_type]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-4 col-sm-9">
                <?php echo CHtml::textField('Staff[contract_type]', $this->staff->staff->contract_type,array('class'=>'form-control','disabled'=>'disabled'));?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($staffLabels['contract_period'], CHtml::getIdByName('Staff[contract_period]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-4 col-sm-9">
                <?php echo CHtml::textField('Staff[contract_period]', $this->staff->staff->contract_period,array('class'=>'form-control','disabled'=>'disabled'));?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($staffLabels['dwelling_place'], CHtml::getIdByName('Staff[dwelling_place]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-4 col-sm-9">
                <?php echo CHtml::textField('Staff[dwelling_place]', $this->staff->staff->dwelling_place,array('class'=>'form-control', 'onkeyup' => 'py(this)', 'placeholder'=>$staffLabels['dwelling_place']));?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($staffLabels['dwelling_place_pinyin'], CHtml::getIdByName('Staff[dwelling_place_pinyin]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-4 col-sm-9">
                <?php echo CHtml::textField('Staff[dwelling_place_pinyin]', $this->staff->staff->dwelling_place_pinyin,array('class'=>'form-control', 'readonly' => 'readonly', 'placeholder'=>$staffLabels['dwelling_place_pinyin']));?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($staffLabels['home_of_record'], CHtml::getIdByName('Staff[home_of_record]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-4 col-sm-9">
                <?php echo CHtml::textField('Staff[home_of_record]', $this->staff->staff->home_of_record,array('class'=>'form-control','placeholder'=>$staffLabels['home_of_record']));?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($staffLabels['emergency_person'], CHtml::getIdByName('Staff[emergency_person]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-4 col-sm-9">
                <?php echo CHtml::textField('Staff[emergency_person]', $this->staff->staff->emergency_person,array('class'=>'form-control','placeholder'=>$staffLabels['emergency_person']));?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($staffLabels['emergency_contact'], CHtml::getIdByName('Staff[emergency_contact]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-4 col-sm-9">
                <?php echo CHtml::textField('Staff[emergency_contact]', $this->staff->staff->emergency_contact,array('class'=>'form-control','placeholder'=>$staffLabels['emergency_contact']));?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($staffLabels['relationship'], CHtml::getIdByName('Staff[relationship]'), array('class' => 'control-label col-sm-3')); ?>
            <div class="col-lg-4 col-sm-9">
                <?php echo CHtml::dropDownList('Staff[relationship]', $this->staff->staff->relationship, $relationship_opt, array('class' => 'form-control', 'onchange' => 'relationshipOther(this, "Staff_relationship_other")'))?>
            </div>
        </div>
        <div class="form-group" style="display: <?php echo $this->staff->staff->relationship == 'other' ? 'block' : 'none';?>;">
            <?php echo CHtml::label('', CHtml::getIdByName('Staff[relationship_other]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-4 col-sm-10">
                <?php echo CHtml::textField('Staff[relationship_other]', $this->staff->staff->relationship_other, array('class'=>'form-control', 'placeholder'=>$staffLabels['relationship_other']));?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($staffLabels['contact_person2'], CHtml::getIdByName('Staff[contact_person2]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-4 col-sm-9">
                <?php echo CHtml::textField('Staff[contact_person2]', $this->staff->staff->contact_person2,array('class'=>'form-control','placeholder'=>$staffLabels['contact_person2']));?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($staffLabels['conact_number2'], CHtml::getIdByName('Staff[conact_number2]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-4 col-sm-9">
                <?php echo CHtml::textField('Staff[conact_number2]', $this->staff->staff->conact_number2,array('class'=>'form-control','placeholder'=>$staffLabels['conact_number2']));?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($staffLabels['relationship2'], CHtml::getIdByName('Staff[relationship2]'), array('class' => 'control-label col-sm-3')); ?>
            <div class="col-lg-4 col-sm-9">
                <?php echo CHtml::dropDownList('Staff[relationship2]', $this->staff->staff->relationship2, $relationship_opt, array('class' => 'form-control', 'onchange' => 'relationshipOther(this, "Staff_relationship2_other")'))?>
            </div>
        </div>
        <div class="form-group" style="display: <?php echo $this->staff->staff->relationship2 == 'other' ? 'block' : 'none';?>;">
            <?php echo CHtml::label('', CHtml::getIdByName('Staff[relationship2_other]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-4 col-sm-9">
                <?php echo CHtml::textField('Staff[relationship2_other]', $this->staff->staff->relationship2_other, array('class'=>'form-control', 'placeholder'=>$staffLabels['relationship2_other']));?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($staffLabels['residence_card'], CHtml::getIdByName('Staff[residence_card]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-4 col-sm-9">
                <label class="radio-inline">
                    <input type="radio" name="Staff[residence_card]" value="1" <?php echo $this->staff->staff->residence_card == '1' ? 'checked' : '';?>> Yes
                </label>
                <label class="radio-inline">
                    <input type="radio" name="Staff[residence_card]" value="2" <?php echo $this->staff->staff->residence_card == '2' ? 'checked' : '';?>> No
                </label>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($staffLabels['highest_duniversity'], CHtml::getIdByName('Staff[highest_duniversity]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-4 col-sm-9">
                <?php echo CHtml::textField('Staff[highest_duniversity]', $this->staff->staff->highest_duniversity,array('class'=>'form-control','placeholder'=>$staffLabels['highest_duniversity']));?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($staffLabels['highest_type'], CHtml::getIdByName('Staff[highest_type]'), array('class' => 'control-label col-sm-3')); ?>
            <div class="col-lg-4 col-sm-9">
                <?php echo CHtml::dropDownList('Staff[highest_type]', $this->staff->staff->highest_type, $highest_type_opt, array('class' => 'form-control', 'onchange' => 'relationshipOther(this, "Staff_highest_type_other")'))?>
            </div>
        </div>
        <div class="form-group" style="display: <?php echo $this->staff->staff->highest_type == 'other' ? 'block' : 'none';?>;">
            <?php echo CHtml::label('', CHtml::getIdByName('Staff[highest_type_other]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-4 col-sm-9">
                <?php echo CHtml::textField('Staff[highest_type_other]', $this->staff->staff->highest_type_other, array('class'=>'form-control', 'placeholder'=>$staffLabels['highest_type_other']));?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($staffLabels['highest_major'], CHtml::getIdByName('Staff[highest_major]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-4 col-sm-9">
                <?php echo CHtml::textField('Staff[highest_major]', $this->staff->staff->highest_major,array('class'=>'form-control','placeholder'=>$staffLabels['highest_major']));?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($staffLabels['work_number'], CHtml::getIdByName('Staff[work_number]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-4 col-sm-9">
                <?php echo CHtml::textField('Staff[work_number]', $this->staff->staff->work_number,array('class'=>'form-control','placeholder'=>$staffLabels['work_number'], 'disabled'=>'disabled'));?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($staffLabels['position'], CHtml::getIdByName('Staff[position]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-4 col-sm-9">
                <?php echo CHtml::textField('Staff[position]', $this->staff->staff->position,array('class'=>'form-control','placeholder'=>$staffLabels['position'], 'disabled'=>'disabled'));?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($staffLabels['teacher_license'], CHtml::getIdByName('Staff[teacher_license]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-4 col-sm-9">
                <?php echo CHtml::textField('Staff[teacher_license]', $this->staff->staff->teacher_license,array('class'=>'form-control','placeholder'=>$staffLabels['teacher_license'], 'disabled'=>'disabled'));?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($staffLabels['license_type'], CHtml::getIdByName('Staff[license_type]'), array('class' => 'control-label col-sm-3')); ?>
            <div class="col-lg-4 col-sm-9">
                <?php echo CHtml::dropDownList('Staff[license_type]', $this->staff->staff->license_type, $license_type_opt, array('class' => 'form-control', 'onchange' => 'relationshipOther(this, "Staff_highest_type_other")', 'disabled'=>'disabled'))?>
            </div>
        </div>
        <div class="form-group" style="display: <?php echo $this->staff->staff->license_type == 'other' ? 'block' : 'none';?>;">
            <?php echo CHtml::label('', CHtml::getIdByName('Staff[license_type_other]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-4 col-sm-9">
                <?php echo CHtml::textField('Staff[license_type_other]', $this->staff->staff->license_type_other, array('class'=>'form-control', 'placeholder'=>$staffLabels['license_type_other'], 'disabled'=>'disabled'));?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($staffLabels['license_subject'], CHtml::getIdByName('Staff[license_subject]'), array('class'=>'control-label col-sm-3')); ?>
            <div class="col-lg-4 col-sm-9">
                <?php echo CHtml::textField('Staff[license_subject]', $this->staff->staff->license_subject,array('class'=>'form-control','placeholder'=>$staffLabels['license_subject'], 'disabled'=>'disabled'));?>
            </div>
        </div>
    </div>
    <div class="panel-footer">
        <button type="button" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Save');?></button>
    </div>
</div>
<?php echo CHtml::endForm()?>

<script>
    function relationshipOther(_, ref) {
        if ($(_).val() === 'other') {
            $('#'+ref).parents('.form-group').show()
        } else {
            $('#'+ref).parents('.form-group').hide()
        }
    }

    function calculateAgeWithMonths(birthDate, currentDate = new Date()) {
        // 提取出生日期和当前日期的年、月、日
        const birthYear = birthDate.getFullYear();
        const birthMonth = birthDate.getMonth(); // 月份从 0 开始计数
        const birthDay = birthDate.getDate();

        const currentYear = currentDate.getFullYear();
        const currentMonth = currentDate.getMonth();
        const currentDay = currentDate.getDate();

        // 计算年龄年份部分
        let years = currentYear - birthYear;
        let months = currentMonth - birthMonth;

        // 如果当前月份小于出生月份，说明年份还没满
        if (months < 0) {
            years--;
            months += 12;
        }

        // 如果当前日小于出生日，调整月份
        if (currentDay < birthDay) {
            months--;
            if (months < 0) {
                years--;
                months += 12;
            }
        }

        return { years, months };
    }

    function autoMake() {
        const birthday_search = $('#Staff_birthday_search').val()
        if ( birthday_search && parseInt(birthday_search) > 0) {
            const birthday_array = birthday_search.split('-')
            const birthDate = new Date(birthday_array[0], birthday_array[1]-1, birthday_array[2]);
            const result = calculateAgeWithMonths(birthDate);

            if ( result ) {
                $('#age').val( `${result.years} years ${result.months} months` )
            }
        }
    }

    function autoMake2() {
        const birthday_search = $('#Staff_startdate').val()
        if ( birthday_search ) {
            const birthday_array = birthday_search.split('-')
            const birthDate = new Date(birthday_array[0], birthday_array[1]-1, birthday_array[2]);
            const result = calculateAgeWithMonths(birthDate);

            if ( result ) {
                $('#at_ivy').val( `${result.years} years ${result.months} months` )
            }
        }
    }

    autoMake()
    autoMake2()

    function py(_) {
        const { pinyin } = pinyinPro;
        const str = pinyin($(_).val(), {toneType: 'none'});
        $('#Staff_dwelling_place_pinyin').val( str.toUpperCase() )
    }
</script>
