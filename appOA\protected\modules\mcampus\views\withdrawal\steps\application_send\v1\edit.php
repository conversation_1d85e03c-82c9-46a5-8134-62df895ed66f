<div>
    <div class='flex relative'>
        <div class='dot'></div>
        <span class='stepBorderLeft'></span>
        <div class='ml8 pb24 flex1 width90'>
            <div class='font14 color3 fontBold'><?php echo Yii::t('withdrawal','Send WeChat request form');?></div>
            <div class='mt16 mb16 flex align-items'>
                <span class='bluebg flex1 font14 ' ><span onclick='allChecked()' class='cur-p'>全选</span> </span>
                <button type="button" class="btn btn-primary btn-sm" onclick='sendWechat()'>发送</button>
            </div>
            <div class='overscroll'>
                <div id='wechatBox'>
                </div>
            </div>
        </div>
    </div>
   
</div>
<script>
    init()
    function init(){    
        let send_list=childDetails.forms[0].send_list
        var send_log={}
        var latest_send={}
        if(childDetails.forms[0].data.send_log){
            send_log=childDetails.forms[0].data.send_log
        }
        if(childDetails.forms[0].data.latest_send){
            latest_send=childDetails.forms[0].data.latest_send
        }
        var list=''
        for(let i=0;i<send_list.length;i++){
           let parentType=send_list[i].parentType=='father'?"父亲":"母亲"
           if(latest_send[send_list[i].openid]){
                list="<div class='wechatList'>"
                if(latest_send[send_list[i].openid].can_send){
                    list+="<input type='checkbox'  value='"+send_list[i].openid+"' name='sendList'><div class='flex1 ml15'><div class='flex align-items'><img src="+send_list[i].headimgurl+" alt='' class='img28'><div class='font14 color3 ml8'>"+send_list[i].nickname+"</div><div class='font14 color6'>｜"+parentType+"</div></div><div class='font12 color6 mt10 cur-p' onclick='sendLog(this)' data-id="+send_list[i].openid+">最近一次发送："+latest_send[send_list[i].openid].date+" <span class='el-icon-arrow-right'></span></div>"
                }else{
                    list+="<input type='checkbox'disabled  value='"+send_list[i].openid+"' name='sendList'><div class='flex1 ml10'><div class='flex align-items'><img src="+send_list[i].headimgurl+" alt='' class='img28'><div class='font14 color3 ml8'>"+send_list[i].nickname+"</div><div class='font14 color6'>｜"+parentType+"</div></div><div class='font12 color6 mt10 cur-p' onclick='sendLog(this)' data-id="+send_list[i].openid+">最近一次发送："+latest_send[send_list[i].openid].date+" <span class='el-icon-arrow-right'></span></div>"
                }
               
                if(!latest_send[send_list[i].openid].can_send){
                    list+="<div class='mt5 yellow'>"+latest_send[send_list[i].openid].interval_date+"可再次发送</div></div></div>"
                }
            }else{
                list="<div class='wechatList'><input type='checkbox' value='"+send_list[i].openid+"' name='sendList'><div class='flex1 ml15'><div class='flex align-items'><img src="+send_list[i].headimgurl+" alt='' class='img28'><div class='font14 color3 ml8'>"+send_list[i].nickname+"</div><div class='font14 color6'>｜"+parentType+"</div></div><div class='font12 color9 mt10'>无发送记录</div></div></div>"
            }
            $('#wechatBox').append(list)
        }
    }
    function allChecked(){
        var objs = window.document.getElementsByName("sendList");
        for(var   i=0;i<objs.length;i++){
            if (objs[i].type == "checkbox" && objs[i].disabled==false){
                objs[i].checked = true;     
            }          
        }
    }
    function sendWechat(){
        const checkboxes = document.querySelectorAll('input[type="checkbox"][name="sendList"]:checked');
        const values = Array.from(checkboxes).map(checkbox => checkbox.value); // 将选中复选框的值放入数组
        $.ajax({
            url: '<?php echo $this->createUrl("sendAppForm") ?>',
            type: "post",
            dataType: 'json',
            data: {
                applicationId: container.applicationId,
                node:'application',
                task:'application_send',
                openids:values
            },
            success: function(data) {
                if (data.state == 'success') {
                    container.openModal(container.applicationId,'')
                    resultTip({
                        msg: data.state
                    });
                } else {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                }
            },
            error: function(data) {},
        })
    }
    function sendLog(that){
        let list=$(that).attr('data-id')
        let log=childDetails.forms[0].data.send_log
        var html=''
        for(let i=0;i<log[list].length;i++){
            html+='<div>'+log[list][i].date+'</div>'
        }
        $('#dateList').html(html) 
        $('#dateListModal').modal('show')

    }
</script>
<style>
    .wechatList{
        height: 112px;
        background: #FAFAFA;
        border-radius: 4px;
        display:inline-flex;
        padding:16px;
        margin-right:16px;
        align-items: flex-start;
    }
    .overscroll{
    }
    #wechatBox{
        overflow-x: auto;
        white-space: nowrap;
        padding-bottom:10px
    }
    #wechatBox::-webkit-scrollbar {
        height:8px; /* 设置滚动条的高度 */
    }
    #wechatBox::-webkit-scrollbar-track {
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0);
        background   : #fff;
        border-radius: 10px;
        border:none
    }
    #wechatBox::-webkit-scrollbar-thumb {
        border-radius   : 10px;
        background-color: #ccc;
        background-image: none
    }
    .width90{
        width:90%
    }
</style>