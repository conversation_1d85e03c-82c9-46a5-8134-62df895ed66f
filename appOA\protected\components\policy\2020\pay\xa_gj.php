<?php
if(!defined("IVY_POLICY"))
throw new CException('DO NOT USE THIS FILE DIRECTLY');

return array( 'policy'=>array(

	//是否老生老办法
	ENABLE_CONSTANT_TUITION => true,

	//年保育费
	//ANNUAL_CHILDCARE_AMOUNT => 8000,

	//是否开放课外活动账单
	ENABLE_AFTERSCHOOLS => true,

	//图书馆工本费
	FEETYPE_LIBCARD => 10,

	//学期四个时间点；取前后两端
	TIME_POINTS   => array(202009,202102,202103,202108),

	HOLIDAY_MONTH => array(
		MONTH=>array(202102=>20, 202108=>20),
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
			FEETYPE_SCHOOLBUS,
		),
		TUITION_CALC_BASE => BY_MONTH,
	),

	//学期付开通哪些缴费类型
	PAYGROUP_SEMESTER => array(
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
			FEETYPE_SCHOOLBUS,
		),
		TUITION_CALC_BASE => BY_MONTH,
	),

	PAYGROUP_MONTH => array(
		ITEMS => array(
//			FEETYPE_TUITION,
			FEETYPE_LUNCH,
//			FEETYPE_SCHOOLBUS,
		),
		TUITION_CALC_BASE => BY_MONTH,
	),

	//计算基准：BY_MONTH, 或者 BY_SETTING, 前者表示所有的学费都基于月费用计算，后者表示所有的学费都基于学期或学年
	TUITION_CALCULATION_BASE => array(

		BY_MONTH => array(

			//月收费金额
			AMOUNT => array(
				FULL5D => 1700,
			),

			//收费月份，及其权重
			MONTH_FACTOR => array(
				202009=>1,
				202010=>1,
				202011=>1,
				202012=>1,
				202101=>1,
				202102=>0,
				202103=>1,
				202104=>1,
				202105=>1,
				202106=>1,
				202107=>1,
				202108=>0,
			),

		),

		BY_SEMESTER1 => array(
			ENDDATE => 1611849600 //2021-1-29
		),

		BY_SEMESTER2 => array(
			ENDDATE => 1627574400 //2021-7-30
		),

	),

	//每餐费用
	LUNCH_PERDAY => 25,

	//账单到期日和账单开始日之差
	DUEDAYS_OFFSET => 1,

    //代金卷金额
//    VOUCHER_AMOUNT => 250,
    VOUCHER_AMOUNT => 350,

));