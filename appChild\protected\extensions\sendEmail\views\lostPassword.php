<?php
if (true === $this->lang || 'cn' == $this->lang) {
    $df_cn = new CDateFormatter('zh-cn');
    $this->render('mailHeader_lang', array('lang' => 'cn'));
    ?>

    <div style="font-family: 'Microsoft Yahei'">

        <p>亲爱的家长，</p>

        <p>您在<?php echo Mims::unIvy()?'幼儿园管理系统':'艾毅在线'; ?>上进行了“找回密码”操作，请点击以下链接设置新密码:</p>

        <p>
            <?php echo CHtml::link("重设密码",$activation_url); ?>
        </p>

        <p>如果链接不能点击，请复制地址到浏览器打开。<br /><?php echo $activation_url; ?></p>

        <p>如遇到问题，请联系校园 <?php echo CHtml::link($support_email, 'mailto:' . $support_email) ?>。</p>

        <p>
            <?php echo $df_cn->formatDateTime($date_time, 'medium', 'short'); ?>
        </p>

        <p>该邮件为系统自动发送。</p>

    </div>

    <?php
    $this->render('mailFooter_lang', array('lang' => 'cn'));
}


?>


<?php
if (true === $this->lang || 'en' == $this->lang) {
    $df_en = new CDateFormatter('en-us');
    $this->render('mailHeader_lang', array('lang' => 'en'));
    ?>

    <div style="font-family: 'Trebuchet MS','Microsoft Yahei',Arial,Helvetica,sans-serif">

        <p>Dear Parents,</p>

        <p>You had tried to recover your password on <?php echo Mims::unIvy()?'the system':'IvyOnline';?>.</p>
        <p>Please click on the link below to set a new password for your account:</p>

        <p>
            <?php echo CHtml::link('Reset Password', $activation_url); ?>
        </p>

        <p>If the link is not clickable, please copy the address and open it in your browser.<br><?php echo $activation_url; ?></p>

        <p>If you have any questions, please feel free to contact the campus <?php echo CHtml::link($support_email, 'mailto:' . $support_email) ?>.</p>

        <p>
            <?php echo $df_en->formatDateTime($date_time, 'medium', 'short'); ?>
        </p>

        <p>This is an automatic email.</p>

    </div>
    <?php
    $this->render('mailFooter_lang', array('lang' => 'en'));
}
?>


