<style>
    .itemList{
        margin-bottom:16px;
        border-radius: 4px;
        border: 1px solid #E8EAED;
        padding:16px;
        position: relative;
    }
    .bgItemList{
        background: rgba(77,136,210,0.1);
        border: 1px solid #4D88D2;
    }
    .code{
        height: 18px;
        background: #F7F7F8;
        border-radius: 2px;
        border: 1px solid #D9D9D9;
        display:inline-block;
        text-align:center;
        line-height:18px;
        margin-right:9px;
        padding: 0 5px;
        margin-bottom:5px
    }
    .standard{
        border: 1px solid #E8EAED;
        padding:16px;
        border-radius: 4px;
    }
    .standardCode{
        width:40px;
    }
    .standardCode span{
        height: 16px;
        background: #5BC0DE;
        border-radius: 2px;
        text-align:center;
        line-height:16px;
        color:#fff;
        display:inline-block;
        padding:0 5px;
        font-size:12px
    }
    .standardTitle{
        width:120px
    }
    .standardEdit{
        width:80px;
        text-align:right
    }
    .standardList{
        padding:16px;
        background: #FAFAFA;
    }
    .sortableList{
        background: rgba(77,136,210,0.1);
        border: 1px solid #4D88D2;
    }
    .tag{
        background: #5CB85C;
        border-radius: 2px;
        position: absolute;
        right: 0;
        top: 0;
        font-size: 12px;
        font-weight: 400;
        color: #FFFFFF;
        padding: 0px 5px;
    }
    .tagBg{
        background:#EBEDF0;
        color:#333333
    }
    .focusedInput{
        border-color: rgba(82,168,236,.8);
        outline: 0;
        outline: thin dotted\9;
        -webkit-box-shadow: 0 0 8px rgba(82,168,236,.6);
        box-shadow: 0 0 8px rgba(82,168,236,.6);
    }
    .p0{
        padding:0
    }
    .maxWidth{
        max-width:270px
    }
    .form-control{
        box-shadow: none;
        border: 1px solid #dcdfe6;
        box-sizing: border-box;
    }
    .form-control::-webkit-input-placeholder { /* Chrome/Opera/Safari */
        color: #ccc;
    }
</style>
<div class="container-fluid" id='container'>
    <ol class="breadcrumb">
        <li><?php echo Yii::t('site','Campus Workspace');?></li>
        <li><?php echo Yii::t('site','Reports Templates');?></li>
        <li class="active">评定标准管理</li>
    </ol>

    <div class="row">
        <div class="col-md-2 col-sm-12">
        <div class="list-group" id="classroom-status-list">
                <a href="<?php echo $this->createUrl('index'); ?>" class="list-group-item status-filter"><?php echo Yii::t("newDS", "标准库管理");?></a>
                <a href="<?php echo $this->createUrl('option'); ?>" class="list-group-item status-filter active"><?php echo Yii::t("newDS", "评定标准管理");?></a>
                <a href="<?php echo $this->createUrl('template'); ?>" class="list-group-item status-filter"><?php echo Yii::t("newDS", "评估报告模板");?></a>
            </div>
        </div>
        <div class="col-md-10 col-sm-12">
            <div class='mb24'>
                <button type="button" class="btn btn-primary" @click='addList()'>新建评定标准组</button>
            </div>
            <div class='flex row '>
                <div class="maxWidth col-md-4 col-sm-4" >
                    <div class='itemList' v-for='(list,key,index) in optionList' @click='showList(key)' :class='listActive==list.id?"bgItemList":""'>
                        <span class='tag' v-if='list.active==1'>有效</span>
                        <span class='tag tagBg' v-else>无效</span>
                        <div class='color3 font14 fontBold mb10'>{{list.title}}</div>
                        <div><span class='code' v-for='(item,idx) in list.labels'>{{item}}</span></div>
                        <div class='mt10 font14'>
                            <button type="button" class="p0 btn-link font14" @click.stop='editOption(index)'>编辑</button>
                            <button type="button" class="ml10 btn-link font14" @click.stop='delList(list,index)'>删除</button>
                        </div>
                    </div>
                </div>
                <div  class="col-md-8 col-sm-8 flex1">
                    <div class='standard' v-if='listActive!=""'>
                        <div v-if='optionItems.length!=0'>
                            <div  id='optionSortData'>
                                <div class=' standardList font14 mb16' v-for='(list,index) in  optionItems'>
                                    <div class='' v-if='list.editList'>
                                        <div class='row mb16'>
                                            <div class='col-md-6'>
                                                <div class='font14 mb8'>评定标准代码</div>
                                                <div><input type="text" class="form-control" placeholder="请输入" v-model='copyOptionItems[index].label'></div>
                                            </div>
                                        </div>
                                        <div class='row mb16'>
                                            <div class='col-md-6'>
                                                <div class='font14 mb8'>标题（中文）</div>
                                                <div><input type="text" class="form-control" placeholder="请输入" v-model='copyOptionItems[index].title_cn'></div>
                                            </div>
                                            <div class='col-md-6'>
                                                <div class='font14 mb8'>标题（英文）</div>
                                                <div><input type="text" class="form-control" placeholder="请输入" v-model='copyOptionItems[index].title_en'></div>
                                            </div>
                                        </div>
                                        <div class='row mb16'>
                                            <div class='col-md-6'>
                                                <div class='font14 mb8'>描述（中文）</div>
                                                <div><textarea class="form-control" rows="3" v-model='copyOptionItems[index].desc_cn'></textarea></div>
                                            </div>
                                            <div class='col-md-6'>
                                                <div class='font14 mb8'>描述（英文）</div>
                                                <div><textarea class="form-control" rows="3"  v-model='copyOptionItems[index].desc_en'></textarea></div>
                                            </div>
                                        </div>
                                        <div class='flex '>
                                            <div class='flex1'>
                                                <button type="button" class="btn-link font14" @click='delOption(list,index)'>删除</button>
                                            </div>
                                            <div class=''>
                                                <button type="button" class="btn btn-link font14" @click='list.editList=false'>取消</button>
                                                <button type="button" class="btn btn-primary font14" @click='saveEdit(list,index)'>保存</button>
                                            </div>
                                        </div>
                                        <div class='clearfix'></div>
                                    </div>
                                    <div v-else class='flex align-items '>
                                        <div class='standardCode'><span>{{list.label}}</span></div>
                                        <div class='standardTitle'><strong>{{list.title}}</strong> </div>
                                        <div class='flex1 color3'>{{list.desc}}</div>
                                        <div class='standardEdit ml24'>
                                            <el-tooltip class="item" effect="dark" content="编辑" placement="top">
                                                <span class='glyphicon glyphicon-edit color9 cur-p' @click='editList(list,index)'></span>
                                            </el-tooltip>
                                            <el-tooltip class="item" effect="dark" content="拖动" placement="top">
                                                <span class='glyphicon glyphicon-move color9 cur-p ml16 optionMove'></span>
                                            </el-tooltip>
                                        </div>
                                    </div>
                                </div>
                            </div>  
                        </div>
                        <div v-else-if='!showAddItem'>
                            <el-empty>
                                <button type="button" class="btn btn-primary font14" @click='addItem()'><span class='glyphicon glyphicon-plus'></span> 添加</button>
                            </el-empty>
                        </div>
                        <div class='standardList font14' v-if='showAddItem'>
                            <div class='row mb16'>
                                <div class='col-md-6'>
                                    <div class='font14 mb8'>评定标准代码</div>
                                    <div><input type="text" class="form-control" placeholder="请输入" v-model='pushItem.label'></div>
                                </div>
                            </div>
                            <div class='row mb16'>
                                <div class='col-md-6'>
                                    <div class='font14 mb8'>标题（中文）</div>
                                    <div><input type="text" class="form-control" placeholder="请输入" v-model='pushItem.title_cn'></div>
                                </div>
                                <div class='col-md-6'>
                                    <div class='font14 mb8'>标题（英文）</div>
                                    <div><input type="text" class="form-control" placeholder="请输入" v-model='pushItem.title_en'></div>
                                </div>
                            </div>
                            <div class='row mb16'>
                                <div class='col-md-6'>
                                    <div class='font14 mb8'>描述（中文）</div>
                                    <div><textarea class="form-control" rows="3" v-model='pushItem.desc_cn'></textarea></div>
                                </div>
                                <div class='col-md-6'>
                                    <div class='font14 mb8'>描述（英文）</div>
                                    <div><textarea class="form-control" rows="3" v-model='pushItem.desc_en'></textarea></div>
                                </div>
                            </div>
                            <div class='pull-right'>
                                <button type="button" class="btn btn-link" @click='showAddItem=false'>取消</button>
                                <button type="button" class="btn btn-primary" @click='savepushList()'>保存</button>
                            </div>
                            <div class='clearfix'></div>
                        </div>
                        <div v-if='!showAddItem && optionItems.length!=0'><button type="button" class="btn-link font14" @click='addItem()'><span class='el-icon-circle-plus-outline'></span> 新增项</button></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="addListModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("directMessage", "新建评定标准组");?></h4>
            </div>
            <div class="modal-body p24" >
                <div>
                    <div class='row'>
                        <div class='col-md-5'>
                            <div class='font14 mb8'>标准组名称（中文）</div>
                        </div>
                        <div class='col-md-5'>
                            <div class='font14 mb8'>标准组名称（英文）</div>
                        </div>
                    </div>   
                    <div id="sortable">                 
                        <div class='row mb24'  v-for='(list,index) in addListData'>
                            <div class='col-md-5'>
                                <div><input type="text" :class='editOptionIndex==index?"focusedInput":""' class="form-control" placeholder="请输入" v-model='list.title_cn'></div>
                            </div>
                            <div class='col-md-5'>
                                <div><input type="text" class="form-control" :class='editOptionIndex==index?"focusedInput":""'  placeholder="请输入" v-model='list.title_en'></div>
                            </div>
                            <div class='col-md-2 mt5'>
                                <label class="checkbox-inline">
                                    <input type="checkbox"  v-model='list.active'> 有效
                                </label>
                                <span class='glyphicon glyphicon-trash color9 cur-p ml24' @click='delAddList(index)'></span>
                            </div>
                            <!-- <div class='col-md-1 pt8'>
                                <span class='glyphicon glyphicon-trash color9 cur-p' @click='delAddList(index)'></span>
                                <span class='glyphicon glyphicon-move color9 cur-p ml16 listItem'></span>
                            </div> -->
                        </div>
                    </div>
                    <div><button type="button" class="btn-link font14" @click='pushList()'><span class='el-icon-circle-plus-outline'></span> 新增项</button></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "取消");?></button>
                <button type="button" class="btn btn-primary" @click="saveAddList()"><?php echo Yii::t("newDS", "保存");?></button>
            </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="delModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "删除"); ?></h4>
                </div>
                <div class="modal-body p24" >
                    确定删除吗
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" @click='delList("del")' v-if='delType=="list"'>确定</button>
                    <button type="button" class="btn btn-primary" @click='delOption("del")'  v-if='delType=="option"'>确定</button>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var container = new Vue({
        el: "#container",
        data: {
           optionList:{},
           addListData:[],
           listActive:'',
           optionItems:{},
           pushItem:{},
           showAddItem:false,
           delData:{},
           delIndex:'',
           editOptionIndex:null,
           delType:'',
           currentKey:''
        },
        watch:{
        },
        created: function() {
            this.getList()
        },
        computed: {},
        methods: {
            getList(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("optionList") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.optionList=data.data
                            if(that.currentKey!=''){
                                that.showList(that.currentKey)
                            }else{
                                that.showList(Object.keys(data.data)[0]) 
                            }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            sortList(){
                var that=this
                Sortable.create(document.getElementById('sortable'), {
                    handle: ".listItem",
                    ghostClass: "sortableList", 
                    animation: 150, //动画参数
                    onEnd: function (evt) { //拖拽完毕之后发生该事件
                        var itemEl = evt.item;  // 拖拽的元素
                        that.addListData.splice(evt.newIndex, 0, that.addListData.splice(evt.oldIndex, 1)[0])
                        var newArray = that.addListData.slice(0)
                        that.addListData = []
                        that.$nextTick(function () {
                            that.addListData = newArray
                        })
                    }
                });
            },
            addList(){
                this.addListData=Object.values(JSON.parse( JSON.stringify (this.optionList)))
                this.addListData.forEach(item => {
                   if(item.active==1){
                    item.active=true
                   }else{
                    item.active=false
                   }
                });
                $('#addListModal').modal('show')
                this.sortList()
            },
            editOption(index){
                this.addListData=Object.values(JSON.parse( JSON.stringify (this.optionList)))
                this.addListData.forEach(item => {
                   if(item.active==1){
                    item.active=true
                   }else{
                    item.active=false
                   }
                });
                this.editOptionIndex=index
                $('#addListModal').modal('show')
                this.sortList()
            },
            pushList(){
                this.addListData.push({
                    id:'',
                    title_cn:'',
                    title_en:'',
                    active:true,
                })
                this.sortList()
            },
            delList(list,index){
                this.delType='list'

                if(list!='del'){
                    this.delData=list
                    this.delIndex=index
                    $('#delModal').modal('show')
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("optionGroupDel") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:this.delData.id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                          that.getList()
                        //   delete that.optionList[that.delData.id]
                        //   that.optionList.splice(that.delIndex,1)
                          resultTip({
                                msg: data.message
                            });
                          $('#delModal').modal('hide')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            delAddList(index){
                this.addListData.splice(index,1)
            },
            saveAddList(){
                var list=[]
                this.addListData.forEach((item,index) => {
                    list.push({
                        active:item.active?1:"",
                        title_cn:item.title_cn,
                        title_en:item.title_en,
                        id:item.id,
                        weight:index+1
                    })
                });
                // return
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("optionGroupUpdate") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        list:list
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getList()
                            $('#addListModal').modal('hide')
                            resultTip({
                                msg: data.message
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            showList(key){
                let list=this.optionList[key]
                this.currentKey=key
                this.listActive=list.id
                list.items.forEach(item => {
                    Vue.set(item, 'editList',false);

                });
                this.copyOptionItems=JSON.parse( JSON.stringify (list.items))
                this.optionItems=list.items
                
                this.showAddItem=false 
                this.$nextTick(function () {
                    if(this.optionItems.length!=0){
                        this.sortOption()
                    }
                })
               
            },
            sortOption(){
                var that=this
                Sortable.create(document.getElementById('optionSortData'), {
                    handle: ".optionMove",
                    ghostClass: "sortableList", 
                    animation: 150, //动画参数
                    onEnd: function (evt) { //拖拽完毕之后发生该事
                        that.optionItems.splice(evt.newIndex, 0, that.optionItems.splice(evt.oldIndex, 1)[0])
                        var newArray = that.optionItems.slice(0)
                        that.optionItems = []
                        that.$nextTick(function () {
                            that.optionItems = newArray
                            that.saveSortOption()
                        })
                    }
                });
            },
            saveSortOption(){
                let that=this
                var list=[]
                this.optionItems.forEach(item => {
                    list.push(item.id)
                });
                $.ajax({
                    url: '<?php echo $this->createUrl("optionSort") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        optionIdList:list
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.sortOption()
                            resultTip({
                                msg: data.message
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            editList(list,index){
                this.optionItems[index].editList=true
                this.copyOptionItems=JSON.parse( JSON.stringify (this.optionItems))
            },
            saveEdit(list,index){
                let that=this
                if( this.copyOptionItems[index].label.trim()==''){
                    resultTip({
                        error: 'warning',
                        msg:'请输入评定标准代码'
                    });
                    return
                }
                if(this.copyOptionItems[index].title_cn.trim()==''){
                    resultTip({
                        error: 'warning',
                        msg:'请输入分类名称中文'
                    });
                    return
                }
                if(this.copyOptionItems[index].title_en.trim()==''){
                    resultTip({
                        error: 'warning',
                        msg:'请输入分类名称英文'
                    });
                    return
                }
                // if(this.copyOptionItems[index].desc_cn.trim()==''){
                //     resultTip({
                //         error: 'warning',
                //         msg:'请输入描述（中文）'
                //     });
                //     return
                // }
                // if(this.copyOptionItems[index].desc_en.trim()==''){
                //     resultTip({
                //         error: 'warning',
                //         msg:'请输入描述（英文）'
                //     });
                //     return
                // }
                $.ajax({
                    url: '<?php echo $this->createUrl("optionUpdate") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        group_id:this.listActive,
                        desc_cn:this.copyOptionItems[index].desc_cn,
                        desc_en:this.copyOptionItems[index].desc_en,
                        id: this.copyOptionItems[index].id,
                        label: this.copyOptionItems[index].label,
                        title_cn: this.copyOptionItems[index].title_cn,
                        title_en:this.copyOptionItems[index].title_en,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                          that.optionItems=that.copyOptionItems
                          that.optionItems[index].editList=false
                          resultTip({
                                msg: data.message
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            addItem(){
                this.pushItem={
                    desc_cn: "",
                    desc_en: "",
                    id: '',
                    label: "",
                    title_cn: "",
                    title_en: "",
                    weight:1,
                }
                this.showAddItem=true
            },
            savepushList(){
                let that=this
                if( this.pushItem.label.trim()==''){
                    resultTip({
                        error: 'warning',
                        msg:'请输入评定标准代码'
                    });
                    return
                }
                if(this.pushItem.title_cn.trim()==''){
                    resultTip({
                        error: 'warning',
                        msg:'请输入分类名称中文'
                    });
                    return
                }
                if(this.pushItem.title_en.trim()==''){
                    resultTip({
                        error: 'warning',
                        msg:'请输入分类名称英文'
                    });
                    return
                }
                if(this.pushItem.desc_cn.trim()==''){
                    resultTip({
                        error: 'warning',
                        msg:'请输入描述（中文）'
                    });
                    return
                }
                if(this.pushItem.desc_en.trim()==''){
                    resultTip({
                        error: 'warning',
                        msg:'请输入描述（英文）'
                    });
                    return
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("optionUpdate") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        group_id:this.listActive,
                        desc_cn:this.pushItem.desc_cn==''?"":this.pushItem.desc_cn,
                        desc_en:this.pushItem.desc_en==''?"":this.pushItem.desc_en,
                        id: this.pushItem.id,
                        label: this.pushItem.label,
                        title_cn: this.pushItem.title_cn,
                        title_en:this.pushItem.title_en,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                          that.getList()
                          resultTip({
                                msg: data.message
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            delOption(list,index){
                this.delType='option'
                if(list!='del'){
                    this.delData=list
                    this.delIndex=index
                    $('#delModal').modal('show')
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("optionDel") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:this.delData.id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                        //   delete that.optionList[that.delData.id]
                          that.optionItems.splice(that.delIndex,1)
                          resultTip({
                                msg: data.message
                            });
                          $('#delModal').modal('hide')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            }
        }
    })
</script>
