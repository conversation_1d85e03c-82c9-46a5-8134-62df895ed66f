<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <link rel="icon" href="/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover"/>
    <title>OA</title>
  </head>
  <body>
    
  <style>
     html,body{
        height: 100%;
        margin:0;
    }
    #bg{
        max-width: 960px;
        height: 100%;
        background: linear-gradient(180deg, #FFFFFF 0%, #EDF6FF 100%);
        box-shadow: 0px 2px 16px 0px rgba(0,0,0,0.06);
        border-radius: 16px;
        margin:0 auto;
        font-family: "Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif;
    }
    .logo_lang{
        display:flex;
        padding-top:32px;
        align-items:center
    }
    .flex1{
        flex:1
    }
    .lang{
        padding:6px 12px;
        background: #E8F3FF;
        border-radius: 16px;
        color:#1989FA;
        cursor: pointer;
    }
    .maxWidth{
        width: 70%; 
        margin:0 auto
    }
    .logo{
        width:143px;  
    }
    .langImg{
        width:13px
    }
    .text-center{
        text-align:center
    }
    .marginimg{
        width:218px;
        margin-top:98px;
    }
    .title{
        font-size: 24px;
        font-weight: 600;
        color: #323233;
        text-align:center;
        margin-top:24px;
    }
    .notes{
        text-align:center;
        font-size: 22px;
        font-weight: 400;
        color: #323233;
        margin-bottom:52px;
        margin-top:16px;
    }
    .input{
        width: 340px;
        margin:0 auto; 
    }
    .inputCSs{
        padding: 12px 20px;
        background: #FFFFFF;
        box-shadow: 0px 2px 6px 0px rgba(0,0,0,0.04);
        border-radius: 22px;
        display:flex;
        align-items:center
    }
    .codeText{
        font-size: 14px;
        font-weight: 400;
        color: #323233;
    }
    .text{
        margin-left:20px;
        border:none;
        height:20px
    }
    .text:focus {
        outline: none; /* 去除默认的焦点边框 */
        border-style: none; /* 设置为实线边框 */
    }
    .btnMargin{
        width:520px;
        text-align:center;
        margin:0 auto
    }
    .btn{
        width: 222px;
        height: 44px;
        background: #1989FA;
        border-radius: 22px;
        border: none;
        font-size: 14px;
        font-weight: 500;
        color: #FFFFFF;
        margin-top:40px;
        text-align:center
    }
    .bvalidator_errmsg:before {
        position: absolute;
        content: "";
        width: 0;
        height: 0;
        top: -5px;
        left: 60px;
        border-right: solid 5px transparent;
        border-left: solid 5px transparent;
        border-bottom: solid 5px #333;
    }
    .bvalidator_errmsg{
        left: 0px !important;
        position: relative;
        top: 20px !important;
        white-space: nowrap;
        font-size: 12px;
        background-color: #333;
        border: 1px solid #999;
        color: #FFF;
        padding: 4px 12px !important;
        border-radius: 4px !important;
    }
    .bvalidator_invalid{
        background-color:#fff !important
    }
    @media (max-width: 767px) {
        #bg{
            width: 100%;
            height: 100%;
            background: linear-gradient(180deg, #FFFFFF 0%, #EDF6FF 100%);
            box-shadow: 0px 2px 16px 0px rgba(0,0,0,0.06);
            border-radius: 16px;
            margin:0 auto;
        }
        .maxWidth{
            width: 100%; 
        }
        .logo_lang{
            padding:6px 16px;
        }
        .logo{
            width:110px;  
        }
        .langImg{
            width:13px
        }
        .marginimg{
            width:218px;
        }
        .marginimg{
            margin-top:16px;
        }
        .mobile{
            padding:0 32px;
        }
        .title{
            font-size: 24px;
            font-weight: 600;
            color: #323233;
            text-align:left;
            margin-top:0;
        }
        .notes{
            text-align:left;
            font-size: 16px;
            font-weight: 400;
            color: #323233;
            margin-bottom:40px;
            margin-top:16px;
        }
        .btnMargin{
            width:100%
        }
        .input{
            width:100%
        }
        .inputCSs{
            padding:12px
        }
        .btn{
            margin-top:76px
        }
    }
</style>
<div id='bg'>
    <div class='maxWidth'>
        <div class='logo_lang'>
            <div class='flex1'>
                <img src="https://m2.files.ivykids.cn/cloud01-file-8025768FvmXRmPSuQ5fWHXzE6tCHwMjYuLD.png" alt="" class='logo'>
            </div>
            <span class='lang'>
                <img src="https://m2.files.ivykids.cn/cloud01-file-8025768FhX4IWfE90q4uARGZiaerEwik58K.png" alt="" class='langImg'>
                <span id='lang' onclick='switch_lang(this)'></span> 
            </span>
        </div>
        <div class='text-center'>
            <img src="https://m2.files.ivykids.cn/cloud01-file-8025768FojJWjhHd1w5OtmgFHg9z0LiVtlV.png" alt="" class='marginimg'>
        </div>
        <form method="post" action="" id="frm_start_surveyed" name="frm_manage">
            <div class='mobile'>
                <div class='title' id='title'>
                </div>
                <div class='btnMargin'>
                <div class='notes' id='notes'></div>
                <div class='input'>
                    <div class='inputCSs flex'>
                        <span class='codeText' id='codeText'></span>
                        <div class='flex1'>
                            <input type="text" class='text' name="invite_code" id="invite_code" placeholder='' data-bvalidator-msg=""  data-bvalidator="required,checkInviteCode">
                        </div>
                    </div>
                </div>
                    <button class='btn' onclick="start_surveyed();return false;" id='submit'></button>
                </div>
            </div>
            <input value="85" name="survey_id" id="survey_id" type="hidden">
            <input value="1" name="is_anonymous" id="is_anonymous" type="hidden">
            <input value="startSurveyed" name="action" id="action" type="hidden">
        </form>
    </div>
</div>
<?php
$yii=dirname(__FILE__).'/../../framework/yii.php';
require_once($yii);
include ('../common/models/CommonUtils.php');
?>
<script src="https://oa.ivyonline.cn/include/jquery/jquery-1.6.1.min.js" type="text/javascript"></script>
<script src="https://oa.ivyonline.cn/include/jquery/plugins/bValidator/jquery.bvalidator-yc.js" type="text/javascript"></script>
<script src="https://oa.ivyonline.cn/include/jquery/plugins/form/jquery.form.js" type="text/javascript"></script>
<link rel="stylesheet" href="https://oa.ivyonline.cn/include/jquery/plugins/bValidator/bvalidator.css" type="text/css" />

<script>
    var api ="<?php echo CommonUtils::isProduction() ? "https://api.ivyonline.cn/api/" : "http://dsonline.cn:8000/api/"?>" ;
    var id = "<?php echo  $_GET['id'];?>"
    var cl='schinese_utf8'
    init()
    var surveyData={}
    function init(){
        if (getCookie('survey_lang') == null) {
            cl = 'schinese_utf8';
        } else {
            cl = getCookie('survey_lang');
        }
        const url =api+'survey/home?id='+id;
        fetch(url, {
            method: 'get',
            data:{
                id:id
            }
            })
        .then((response) => response.json()) // 将响应转换为JSON格式
        .then((data) => {
            if (data.code === 0) {
                surveyData=data.data
                langTab()
            } else {
            }
        })
        .catch((error) => {
            console.error('获取数据失败', error);
            // 错误处理逻辑
        });
    }
    function langTab(){
        if(cl=='english'){
            document.getElementById("lang").innerHTML ='English'
            document.getElementById("title").innerHTML =surveyData.title_en
            document.getElementById("notes").innerHTML ='The survey is completely anonymous and no personal information are identified or kept.'
            document.getElementById("codeText").innerHTML ='Invitation Code'
            document.getElementById("submit").innerHTML ='Start'
            var element = document.getElementById('invite_code');
           element.setAttribute('data-bvalidator-msg','Invalid invitation code');
           document.getElementById("invite_code").placeholder = "Please Input"; 
        }else{
            document.getElementById("lang").innerHTML ='中文'
            document.getElementById("title").innerHTML =surveyData.title_cn
            document.getElementById("notes").innerHTML ='调查问卷完全以匿名形式进行，系统不会记录任何个人身份信息。'
            document.getElementById("codeText").innerHTML ='邀请码'
            document.getElementById("submit").innerHTML ='开始'
            var element = document.getElementById('invite_code');
            element.setAttribute('data-bvalidator-msg','该项不能为空或此邀请码无效');
           document.getElementById("invite_code").placeholder = "请输入"; 

        }
    }
    function start_surveyed() {
        if (validator.validate()) {
            //$("#invite_code").rules("remove");
            validator.destroy();
            $('#frm_start_surveyed').submit();
        }
    }
    var options = {
        offset: {
            x: 0,
            y: 0
        },
        position: {
            x: 'bottom',
            y: 'left'
        },
        template: '<div class="{errMsgClass}">{message}</div>',
        showCloseIcon: false,
        validateOn: null,
        errorValidateOn: 'change',
        validateOnSubmit: false
    };
    validator = $('#frm_start_surveyed').bValidator(options);
    function checkInviteCode(invite_code) {
        var ret = false;
        var ic_length = $.trim(invite_code).length;
        if (ic_length == 6) {
            $.ajax({
                type: 'POST',
                url: '/modules/survey/ajax/staff_survey_ajax.php',
                async: false,
                data: {
                    'action': "checkInviteCode",
                    'invite_code': invite_code
                },
                dataType: "JSON",
                success: function (rt) {
                    if (rt.flag == 'success')
                        ret = true
                }
            });
        }
        return ret;
        }

    function switch_lang(_this) {
        console.log(document.getElementById("lang").innerText)
        console.log(document.getElementById("lang").innerText)
        let text=document.getElementById("lang").innerText
        if(text=='English'){
            setCookie("survey_lang",'schinese_utf8');
            cl = 'schinese_utf8';
        }else{
            setCookie("survey_lang",'english');
            cl = 'english';
        }
        langTab()
    }
        //  设置cookies
    function setCookie(name, value) {
        var exp = new Date();
        exp.setTime(exp.getTime() + 60 * 60 * 1000);
        document.cookie = name + "=" + escape(value) + ";expires=" + exp.toGMTString() + ";path=/";
    }
    //读取cookies
    function getCookie(name) {
        var arr, reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
        if (arr = document.cookie.match(reg))
            return unescape(arr[2]);
        else
            return null;
    }
</script>
  </body>
</html>
