<?php

class BackendModule extends CWebModule
{
	public function init()
	{
		// this method is called when the module is being created
		// you may place code here to customize the module or the application

		// import the module-level models and components
		$this->setImport(array(
			'srbac.models.ItemChildren',
			'backend.models.*',
			'backend.components.*',
		));
	}

	public function beforeControllerAction($controller, $action)
	{
		if(parent::beforeControllerAction($controller, $action))
		{
			if(!Yii::app()->user->getId()) {

                if(isset(Yii::app()->params['standardLogin']) && Yii::app()->params['standardLogin'] ){
                    Yii::app()->user->returnUrl = Yii::app()->getRequest()->getRequestUri();
                }else{
                    $absUrl = Yii::app()->createAbsoluteUrl(Yii::app()->getRequest()->getUrl());
                    Yii::app()->user->loginUrl = Yii::app()->user->loginUrl . "?xoops_requesturi=" . urlencode($absUrl);
                }

				Yii::app()->user->loginRequired();
			}
			return true;
		}
		else
			return false;
	}
}
