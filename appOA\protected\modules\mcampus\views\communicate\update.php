<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Routines'), array('//mcampus/default/index')) ?></li>
        <li><?php echo Yii::t('site', 'Daily Bulletin') ?></li>
        <li class="active"><?php echo Yii::t('site', '编辑公告') ?></li>
    </ol>
    <div class="row">
        <div class="col-md-2">
            <div class="list-group" id="classroom-status-list">
                <a href="<?php echo $this->createUrl('//mcampus/communicate/internal'); ?>" class="list-group-item status-filter <?php if ($filter != 'history') : ?>active<?php endif; ?>"><?php echo Yii::t('global', 'Active'); ?> </a></li>
                <a href="<?php echo $this->createUrl('//mcampus/communicate/internal', array('filter' => 'history')); ?>" class="list-group-item status-filter <?php if ($filter == 'history') : ?>active<?php endif; ?>"><?php echo Yii::t('global', 'History'); ?> </a></li>
            </div>
        </div>
        <div class="col-md-10">
            <?php $form = $this->beginWidget('CActiveForm', array(
                'id' => 'bulletin-edit-form',
                'enableAjaxValidation' => false,
                'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form'),
            )); ?>
            <?php if (Dailymail::getCate($this->branchId)) : ?>
                <div class="form-group">
                    <label class="col-xs-1 control-label"><?php echo $form->labelEx($model, 'cate'); ?></label>
                    <div class="col-xs-8">
                        <?php
                        echo $form->radioButtonList($model, 'cate', Dailymail::getCate($this->branchId), array('separator' => '', 'template' => '<div class="col-xs-3">{input} {label}</div>'));
                        ?>
                    </div>
                </div>
            <?php endif; ?>
            <div class="form-group">
                <label class="col-xs-1 control-label"><?php echo $form->labelEx($model, 'priority'); ?></label>
                <div class="col-xs-8">
                    <?php
                    echo $form->radioButtonList($model, 'priority', Dailymail::getPrioritys(), array('separator' => '', 'template' => '<div class="col-xs-3">{input} {label}</div>'));
                    ?>
                </div>
            </div>
            <div class="form-group">
                <label class="col-xs-1 control-label"><?php echo $form->labelEx($model, 'startdate'); ?></label>
                <div class="col-xs-8">
                    <?php
                    $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                        "model" => $model,
                        "attribute" => "startdate",
                        "options" => array(
                            'changeMonth' => true,
                            'changeYear' => true,
                            'dateFormat' => 'yy-mm-dd',
                        ),
                        'htmlOptions' => array(
                            'class' => 'form-control length_3'
                        ),
                    ));
                    ?>
                </div>
            </div>
            <div class="form-group">
                <label class="col-xs-1 control-label"><?php echo $form->labelEx($model, 'dueDay'); ?></label>
                <div class="col-xs-8">
                    <?php
                    echo $form->dropDownList($model, 'dueDay', Dailymail::getDueDay(), array('class' => 'form-control length_3', 'onchange' => 'cDue(this)'));
                    ?>
                </div>
            </div>
            <div class="form-group" id="duedatewarp" style="<?php if ($model->dueDay != 99) : ?>display: none;<?php endif; ?>">
                <label class="col-xs-1 control-label"><?php echo $form->labelEx($model, 'duedate'); ?></label>
                <div class="col-xs-8" style="z-index: 9999;">
                    <?php
                    $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                        "model" => $model,
                        "attribute" => "duedate",
                        "options" => array(
                            'changeMonth' => true,
                            'changeYear' => true,
                            'dateFormat' => 'yy-mm-dd',
                        ),
                        'htmlOptions' => array(
                            'class' => 'form-control length_3'
                        ),
                    ));
                    ?>
                </div>
            </div>
            <div class="form-group">
                <label class="col-xs-1 control-label"><?php echo $form->labelEx($model, 'stat'); ?></label>
                <div class="col-xs-8">
                    <?php echo $form->checkBox($model, 'stat', array('value' => 10)); ?>
                </div>
            </div>
            <div class="form-group">
                <label class="col-xs-1 control-label"><?php echo $form->labelEx($model, 'en_content'); ?></label>
                <div class="col-xs-8">
                    <?php echo $form->textArea($model, 'en_content', array('maxlength' => 255, 'class' => 'form-control tinymce')); ?>
                </div>
            </div>
            <div class="form-group">
                <div class="col-xs-9">
                    <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global', 'Submit'); ?></button>
                </div>
            </div>
            <!-- 附件 -->
            <input type="hidden" name="Dailymail[id]" value="<%= id%>" id="Dailymail_id">
            <div class="col-xs-12">
                <!-- 显示附件 -->
                <div id="fileList">
                    <table class="table">
                        <thead>
                            <tr>
                                <th><?php echo Yii::t('workflow', 'Uploaded Files'); ?></th>
                                <th><?php echo Yii::t('workflow', 'Display Name'); ?></th>
                                <th>#</th>
                            </tr>
                        </thead>
                        <tbody>
                            <%= files%>
                        </tbody>
                    </table>
                </div>
                <!-- 上传 -->
                <div class="btn-group btn-group-justified" role="group">
                    <input id="file" type="file" name="file" onchange="fileUpload(this)" style="display:none">
                    <div class="btn-group" role="group">
                        <a id="filebtn" class="btn btn-default" onclick="$('#file').click();"><?php echo Yii::t('workflow', 'Upload New File'); ?></a>
                    </div>
                </div>
                <br>
                <span class="text-warning" id="spaninfo"></span>
            </div>
            <?php $this->endWidget(); ?>
        </div>
    </div>
</div>
</div>


<script>
    tinymce.init({
        selector: '#Dailymail_en_content',
        language: '<?php echo CommonUtils::autoLang("zh_CN", "en"); ?>',
        height: 600,
        plugins: 'fullscreen image link media imagetools preview table code wordcount',
        imagetools_cors_hosts: ['picsum.photos'],
        menubar: 'file edit view insert format help',
        toolbar: 'undo redo bold italic underline strikethrough fontselect fontsizeselect formatselect | image fullscreen preview ',
        toolbar_sticky: true,
        image_uploadtab: false,
        image_dimensions: false,
        automatic_uploads: true,
        paste_data_images: true,
        // 上传文件，成功回调，失败回调，进度
        images_upload_handler: function (blobInfo, success, failure, progress) {
            handleUploadImage(blobInfo, success, failure, progress);
        },
        setup: function(editor) {
            // 实时同步编辑器内容到 selector
            editor.on('change', function() {
                tinymce.triggerSave();
            });
        },
    });
    tinymce.PluginManager.add('watermark-plugin', function (editor) {
        editor.ui.registry.addMenuItem('editimage', {
            icon: 'image',
            text: '<?php echo Yii::t("newDS", "Add/Remove Watermark");?>',
            onAction: function () {
                if(container.Watermark.indexOf(container.WatermarkImg) == -1){
                    container.Watermark=container.Watermark+container.WatermarkImg
                }else{
                    container.Watermark=container.Watermark.replace(container.WatermarkImg,"")
                }
                editor.insertContent('<div><img  style="max-width:100%"  src='+container.Watermark+'></div>')
            }
        });
        editor.ui.registry.addContextMenu('image', {
            update: function (element) {
                container.Watermark=element.src
                return !container.Watermark ? '' : 'image';
            }
        });
    });

    function cDue(_this) {
        if ($(_this).val() == 99) {
            $('#duedatewarp').show();
        } else {
            $('#duedatewarp').hide();
        }
    }

    //上传文件
    function fileUpload(_this) {
        var url = '<?php echo $this->createUrl('saveUploadFile'); ?>';
        var formData = new FormData();
        formData.append('file', _this.files[0]);
        formData.append('id', $('#Dailymail_id').val());
        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(data) {
                var msg = eval('(' + data + ')');
                if (msg.state == 'success') {
                    var html = '<tr><td><a target="_blank" href="' + msg.data.url + '">' + msg.data.fileName + '</a></td>';
                    html += '<td><input type="text" onchange=changFileName(this,' + msg.data.fileId + ') class="form-control" value="' + msg.data.fileName + '"></td>';
                    html += '<td><a class="btn btn-danger btn-xs" onclick="deleteFile(this,' + msg.data.fileId + ')"><span class="glyphicon glyphicon-trash"> </span></a></td></tr>';
                    html += '<input type="hidden" name="files[]" value=' + msg.data.fileId + ' />';
                    $('#fileList > table').append(html);
                }
                $('#spaninfo').text(msg.message);
            }
        });
    }
    //删除文件
    function deleteFile(btn, id) {
        if (confirm('<?php echo Yii::t('workflow', 'Confirm delete this file?'); ?>')) {
            var url = '<?php echo $this->createUrl('deleteUploadFile'); ?>';

            $.ajax({
                url: url,
                type: 'POST',
                data: {
                    id: id
                },
                success: function(data) {
                    var msg = eval('(' + data + ')');
                    if (msg.state == 'success') {
                        $(btn).parent().parent().remove();
                    }
                    $('#spaninfo').text(msg.message);
                }
            });
        }
    }
    //修改文件名
    function changFileName(btn, id) {
        var notes = $(btn).val();
        if (notes == "") {
            return false
        }
        var url = '<?php echo $this->createUrl('changeFileName'); ?>';

        $.ajax({
            url: url,
            type: 'POST',
            data: {
                id: id,
                notes: notes
            },
            success: function(data) {
                var msg = eval('(' + data + ')');
                if (msg.state == 'success') {
                    var con = $(btn).parent().prev().children().text(msg.data);
                }
                $('#spaninfo').text(msg.message);
            }
        });
    }

    function handleUploadImage (blobInfo, success, failure, progress) {
        $.ajax({
            url: '<?php echo $this->createUrl("getQiniuTokenSimple") ?>',
            type: "post",
            dataType: 'json',
            data: {
                isPrivate: 0,
                prefix: 'communicate',
            },
            success: function(data) {
                if (data.state == 'success') {
                    var token = data.data.token;
                    var domain = data.data.domain;
                    // 上传文件

                    var xhr = new XMLHttpRequest();
                    xhr.withCredentials = false;
                    xhr.open("post", "http://up-z0.qiniup.com");
                    xhr.upload.onprogress = function (e) {
                        progress(Math.round(e.loaded / e.total * 100) | 0);
                    };
                    xhr.onerror = function () {
                        failure('Image upload failed due to a XHR Transport error. Code: ' + xhr.status);
                    };
                    xhr.onload = function() {
                        var json;

                        if (xhr.status === 403) {
                            failure('HTTP Error: ' + xhr.status, { remove: true });
                            return;
                        }

                        if (xhr.status < 200 || xhr.status >= 300) {
                            failure('HTTP Error: ' + xhr.status);
                            return;
                        }

                        json = JSON.parse(xhr.responseText);

                        if (!json || typeof json.name != 'string') {
                            failure('Invalid JSON: ' + xhr.responseText);
                            return;
                        }
                        success( domain + "/" + json.name+container.WatermarkImg);
                    };
                    var formData = new FormData();
                    var file = blobInfo.blob();
                    formData.append('file', file, file.name);
                    formData.append('token', token);
                    xhr.send(formData);
                } else {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                }
            },
            error: function(data) {
                resultTip({
                    error: 'warning',
                    msg: "上传失败"
                });
            },
        })
    }
</script>