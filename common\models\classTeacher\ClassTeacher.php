<?php

/**
 * This is the model class for table "ivy_class_teacher".
 *
 * The followings are the available columns in table 'ivy_class_teacher':
 * @property integer $id
 * @property integer $classid
 * @property string $schoolid
 * @property integer $yid
 * @property integer $teacherid
 * @property integer $isheadteacher
 * @property integer $weight
 * @property integer $updated_timestamp
 * @property integer $userid
 */
class ClassTeacher extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ClassTeacher the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_class_teacher';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('classid, schoolid, teacherid, updated_timestamp, userid', 'required'),
			array('classid, yid, teacherid, isheadteacher, ishelpteacher, weight, updated_timestamp, userid', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, classid, schoolid, yid, teacherid, isheadteacher, ishelpteacher, weight, updated_timestamp, userid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'staffInfo'=>array(self::BELONGS_TO, 'InfopubStaffExtend', 'teacherid'),
            'userWithProfile'=>array(self::BELONGS_TO, 'User', 'teacherid', 'together'=>false, 'with'=>'profile'),
			'classInfo'=>array(self::BELONGS_TO, 'IvyClass', array('classid'=>'classid'),'select'=>'classid,schoolid,yid,title,stat'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'classid' => 'Classid',
			'schoolid' => 'Schoolid',
			'yid' => 'Yid',
			'teacherid' => 'Teacherid',
			'isheadteacher' => 'Isheadteacher',
			'ishelpteacher' => 'Ishelpteacher',
			'weight' => 'Weight',
			'updated_timestamp' => 'Updated Timestamp',
			'userid' => 'Userid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('teacherid',$this->teacherid);
		$criteria->compare('isheadteacher',$this->isheadteacher);
		$criteria->compare('ishelpteacher',$this->ishelpteacher);
		$criteria->compare('weight',$this->weight);
		$criteria->compare('updated_timestamp',$this->updated_timestamp);
		$criteria->compare('userid',$this->userid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

    public function getCampusAllClassTeacher($branchId, $yid, $withClassTitle=false, $revertClassTeacherKey=false, $primaryClass=false){
        $data = array();
        $models = ClassTeacher::model()->findAllByAttributes(
            array(
                'schoolid' => $branchId,
                'yid' => $yid
            )
        );

        if($revertClassTeacherKey){
            foreach($models as $model){
                $data['teachers'][$model->teacherid][] = $model->classid;
            }

        }else{
            foreach($models as $model){
                $data['teachers'][$model->classid][] = $model->teacherid;
            }
        }

        if($withClassTitle){
            $classModels = IvyClass::model()->getClassList($branchId, $yid, $primaryClass);

            foreach($classModels as $cm){
                if($cm->child_age > 6)
                    $data['classes'][$cm->classid] = $cm->title;
                    $data['classArr'][] = array(
                        'id' => $cm->classid,
                        'title' => $cm->title
                    );
            }

        }
        return $data;
    }
}