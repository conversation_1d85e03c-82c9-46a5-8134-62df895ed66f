<div id="stepDefault">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title"><?php echo Yii::t('attends', '编辑注册配置') ?> <span id="remarks_t"></span></h4>
            </div>
            <div class="modal-body">
                <ul class="nav nav-wizard pb20 mt10">
                    <li class=" mb10" :class='currentStepConfig==list.flag?"active":""'
                        v-for='(list,index) in showList.step'><a href="javascript:;" @click='stepConfig(list.flag)'><span
                                    class="number">{{index+1}}</span>{{list.title}}</a></li>
                </ul>
                <div class="form-horizontal">
<!--                    <div class="form-group" style="display: flex" v-if="coll.type==2">-->
<!--                        <label for="notify_email" class="col-sm-2 control-label">提醒邮件 </label>-->
<!--                        <div class="col-sm-10">-->
<!--                            <input id="notify_email" class="form-control" type="email" v-model='notify_email'>-->
<!--                        </div>-->
<!--                    </div>-->
                    <div class="form-group">
                        <label for="inputEmail3" class="col-sm-2 control-label">中文内容 </label>
                        <div class="col-sm-10" v-if="isTinymce">
                            <input id="tinymce" type="textarea">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="inputPassword3" class="col-sm-2 control-label">英文内容</label>
                        <div class="col-sm-10" v-if="isTinymce">
                            <input id="tinymceEn" type="textarea">
                        </div>
                    </div>
                    <div class="form-group" style="display: flex">
                        <label for="inputPassword3" class="col-sm-2 control-label" id="cnP">中文协议</label>
                            <el-upload action="https://up-z0.qiniup.com"
                                    list-type="picture-card"
                                    accept="image/*"
                                    :multiple="isMultiple"
                                    :on-remove="(file, fileList)=>{return handleRemove(file, fileList, 'cn')}"
                                    :on-success="(response, file, fileList)=>{return handleAvatarSuccess(response, file, fileList, 'cn')}"
                                    :before-upload="beforeAvatarUpload"
                                    :on-error="imgUploadError"
                                    :data="uploadToken"
                                    :on-preview="handlePreview"
                                    :file-list="fileListCn" style="flex: 1">
                            <i class="el-icon-plus"></i>
                            </el-upload>
                    </div>

                    <div class="form-group" style="display: flex">
                        <label for="inputPassword3" class="col-sm-2 control-label">英文协议</label>
                        <el-upload action="https://up-z0.qiniup.com"
                                   list-type="picture-card"
                                   accept="image/*"
                                   :multiple="isMultiple"
                                   :on-remove="(file, fileList)=>{return handleRemove(file, fileList, 'en')}"
                                   :on-success="(response, file, fileList)=>{return handleAvatarSuccess(response, file, fileList, 'en')}"
                                   :before-upload="beforeAvatarUpload"
                                   :on-error="imgUploadError"
                                   :data="uploadToken"
                                   :on-preview="handlePreview"
                                    :file-list="fileListEn" style="flex: 1">
                            <i class="el-icon-plus"></i>
                        </el-upload>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" :disabled='addStuBtn'  @click='saveStepConfig()'>保存</button>
            </div>
            <el-dialog :visible.sync="dialogVisible"
                       :before-close="handleClose"
                       :modal-append-to-body="modalappendtobody"
                       width="80%"
                       top="100vh"
                       :close-on-press-escape="escape"
                       :lock-scroll="lock_scroll">
                <img width="100%" :src="dialogImageUrl" alt="">
            </el-dialog>
        </div>
    </div>
</div>
<script>
    var coll_id = '<?php echo $coll_id?>';
    var step_flag = '<?php echo $step_flag?>';
    var container = new Vue({
        "el": '#stepDefault',
        data: {
            lock_scroll:true,
            modalappendtobody:false,
            escape:false,
            coll_id: coll_id,
            showList: {},
            childData: {},
            addStuList: {},
            yearData: {},
            yid: '',
            checkData: [],
            coll: {
                startdate: '',
                enddate: '',
                title_cn: '',
                title_en: ''
            },
            config_data: {},
            currentStepConfig: step_flag,
            isTinymce: true,
            parentEmail: '',
            copyParentEmail: '',
            step_html: '',
            type: 'unfilled',
            stepName: '',
            Watermark:'',
            WatermarkImg: '!v1000',
            dialogImageUrl: '',
            dialogVisible: false,
            productImgs: [],
            isMultiple: true,
            uploadToken:{},
            fileListCn:[],
            fileListEn:[],
            cnProtocolUrlList:[],
            enProtocolUrlList:[],
            notify_email: '',
            addStuBtn:false
        },
        created: function () {
            this.showData()
            this.stepConfig(step_flag)
            this.handleUploadImage()
        },
        mounted: function () {
            intTinymce()
        },
        methods: {
            showData() {
                let that = this
                $.ajax({
                    url: '<?php echo $this->createUrlReg('detailColl'); ?>',
                    type: 'post',
                    dataType: 'json',
                    data: {
                        coll_id: this.coll_id
                    },
                    success: function (data) {
                        if (data.state == 'success') {
                            that.showList = data.data
                            that.yid = data.data.master.yid
                            that.coll = data.data.master
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function (data) {
                        resultTip({
                            error: 'warning',
                            msg: '请求错误'
                        });
                    }
                })
            },
            stepConfig(step_id) {
                this.currentStepConfig = step_id
                let that = this
                $.ajax({
                    url: '<?php echo $this->createUrlReg('editCollConfigShowInfo'); ?>',
                    type: 'post',
                    dataType: 'json',
                    data: {
                        step_flag: this.currentStepConfig,
                        coll_id: this.coll_id,
                    },
                    success: function (data) {
                        if (data.state == 'success') {
                            that.config_data = data.data.config_data
                            that.notify_email = '';

                            if (data.data.config_data.content_cn != null && data.data.config_data.content_cn.notify_email) {
                                that.notify_email = data.data.config_data.content_cn.notify_email 
                            }
                            // that.isTinymce = false
                            let content_cn = that.config_data.content_cn
                            let content_en = that.config_data.content_en
                            that.fileListCn = [];
                            that.cnProtocolUrlList = [];

                            that.fileListEn = [];
                            that.enProtocolUrlList = [];

                            tinymce.get('tinymceEn').setContent('')
                            tinymce.get('tinymceEn').setContent('')

                            if(content_cn != null && content_cn.protocol != null){
                                content_cn.protocol.forEach((value,key)=>{
                                    console.log(value)
                                    console.log(key)
                                    let listData = {"name":key,"url":value};
                                    that.fileListCn.push(listData);
                                    that.cnProtocolUrlList.push(value)
                                })
                            }
                            if(content_en != null && content_en.protocol != null){
                                content_en.protocol.forEach((value,key)=>{
                                    // let name = value+'.jpg';
                                    console.log(value)
                                    console.log(key)
                                    let listData = {"name":key,"url":value};
                                    that.fileListEn.push(listData);
                                    that.enProtocolUrlList.push(value)
                                })
                            }

                            that.$nextTick(() => {
                                try {
                                    if (data.data.config_data.content_en.desc != null) {
                                        tinymce.get('tinymceEn').setContent(data.data.config_data.content_en.desc)
                                    } else {
                                        tinymce.get('tinymceEn').setContent('')
                                    }
                                }catch (e) {
                                    tinymce.get('tinymceEn').setContent('')
                                }

                                try {
                                    if (data.data.config_data.content_cn.desc != null) {
                                        tinymce.get('tinymce').setContent(data.data.config_data.content_cn.desc)
                                    } else {
                                        tinymce.get('tinymce').setContent('')
                                    }
                                }catch (e){
                                    tinymce.get('tinymce').setContent('')
                                }
                            })
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function (data) {
                        resultTip({
                            error: 'warning',
                            msg: '请求错误'
                        });
                    }
                })
            },
            saveStepConfig() {
                let that=this
                that.addStuBtn=true

                $.ajax({
                    url: '<?php echo $this->createUrlReg('editCollConfigOpe'); ?>',
                    type: 'post',
                    dataType: 'json',
                    data: {
                        flag: this.currentStepConfig,
                        coll_id: this.coll_id,
                        'data[content_cn][notify_email]': this.notify_email,
                        'data[content_cn][desc]': tinymce.get('tinymce').getContent(),
                        'data[content_en][desc]': tinymce.get('tinymceEn').getContent(),
                        'data[content_cn][protocol]':this.cnProtocolUrlList,
                        'data[content_en][protocol]':this.enProtocolUrlList,
                    },
                    success: function (data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg: data.state
                            });
                            that.addStuBtn=false

                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.addStuBtn=false

                        }
                    },
                    error: function (data) {
                        that.addStuBtn=false
                        
                        resultTip({
                            error: 'warning',
                            msg: '请求错误'
                        });
                    }
                })
            },
            handleRemove(file, fileList, flag) {//移除图片
                this.upImgUrlList(file, fileList, flag)
            },
            handleClose(done) {
                this.dialogVisible = false
            },
            handlePreview(file) {
                let that = this
                that.dialogImageUrl = file.url;
                that.dialogVisible = true;
            },
            imgUploadError(err, file, fileList){//图片上传失败调用
                console.log(err)
                this.$message.error('上传图片失败!');
            },
            handleAvatarSuccess(response, file, fileList, flag) {//图片上传成功
                this.upImgUrlList(file,fileList,flag)
            },
            handleExceed(files, fileList) {//图片上传超过数量限制
                this.$message.error('上传图片不能超过6张!');
                console.log(file, fileList);
            },
            beforeAvatarUpload(file) {//文件上传之前调用做一些拦截限制
                console.log(file);
                const isJPG = true;
                // const isJPG = file.type === 'image/jpeg';
                const isLt2M = file.size / 1024 / 1024 < 2;

                // if (!isJPG) {
                // this.$message.error('上传头像图片只能是 JPG 格式!');
                // }
                if (!isLt2M) {
                    this.$message.error('上传图片大小不能超过 2MB!');
                }
                return isJPG && isLt2M;
            },
            upImgUrlList(file,fileList,flag){
                console.log("调取更新操作")
                console.log(flag)
                console.log(fileList)
                console.log("调取更新操作结束")

                let ImgUrlList = [];
                console.log('开始更新list操作')
                for (let value of fileList) {
                    if(value.response != null ){
                        url = 'https://m3.media.ivykids.cn/'+ value.response.name
                    }else{
                        url = value.url
                    }
                    ImgUrlList.push(url)
                }
                console.log(ImgUrlList)
                console.log('结束更新list操作')
                if(flag === 'cn'){
                    console.log(flag,ImgUrlList)
                    this.cnProtocolUrlList = ImgUrlList
                }
                if(flag === 'en'){
                    console.log(flag,ImgUrlList)
                    this.enProtocolUrlList = ImgUrlList
                }
            },
            handleUploadImage() {
                let that = this;
                $.ajax({
                    url: '<?php echo $this->createUrlReg("//mteaching/journals/getQiniuTokenSimple") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        isPrivate: 0,
                        prefix: 'pact',
                    },
                    success: function (data) {
                        if (data.state == 'success') {
                            var token = data.data.token;
                            var domain = data.data.domain;
                            that.uploadToken = {'token':token};
                        }
                    }
                })
            },
    }
    })
    function intTinymce(){
        tinymce.init({
            selector: '#tinymce',
            content_style: "img{max-width:100%}",
            language: '<?php echo CommonUtils::autoLang("zh_CN", "en"); ?>',
            height: 600,
            plugins: 'fullscreen image link media imagetools preview table code wordcount paste my-example-plugin',
            imagetools_cors_hosts: ['picsum.photos'],
            menubar: 'file edit view insert format help',
            contextmenu: 'link image imagetools table spellchecker lists editimage',
            toolbar: 'undo redo bold italic underline strikethrough fontselect fontsizeselect formatselect | image fullscreen preview | editimage',
            toolbar_sticky: true,
            image_uploadtab: false,
            image_dimensions: false,
            automatic_uploads: true,
            paste_data_images: true,
            menu: {
                insert: {
                    title: 'Insert',
                    items: 'image link media template codesample inserttable | charmap emoticons hr | pagebreak nonbreaking anchor toc | insertdatetime | editimage'
                },
            },
            content_style: "img{max-width:100%}",
            automatic_uploads: true,
            paste_data_images: true,
            // 上传文件，成功回调，失败回调，进度
            images_upload_handler: function (blobInfo, success, failure, progress) {
                handleUploadImage2(blobInfo, success, failure, progress);
            },
            toolbar: 'mediaDialog',
        });
        tinymce.init({
            selector: '#tinymceEn',
            content_style: "img{max-width:100%}",
            language: '<?php echo CommonUtils::autoLang("zh_CN", "en"); ?>',
            height: 600,
            plugins: 'fullscreen image link media imagetools preview table code wordcount paste my-example-plugin',
            imagetools_cors_hosts: ['picsum.photos'],
            menubar: 'file edit view insert format help',
            contextmenu: 'link image imagetools table spellchecker lists editimage',
            toolbar: 'undo redo bold italic underline strikethrough fontselect fontsizeselect formatselect | image fullscreen preview | editimage',
            toolbar_sticky: true,
            image_uploadtab: false,
            image_dimensions: false,
            automatic_uploads: true,
            paste_data_images: true,
            menu: {
                insert: {
                    title: 'Insert',
                    items: 'image link media template codesample inserttable | charmap emoticons hr | pagebreak nonbreaking anchor toc | insertdatetime | editimage'
                },
            },
            // 上传文件，成功回调，失败回调，进度
            images_upload_handler: function (blobInfo, success, failure, progress) {
                handleUploadImage2(blobInfo, success, failure, progress);
            },
            toolbar: 'mediaDialog ',
        });
        tinymce.PluginManager.add('my-example-plugin', function (editor) {
            editor.ui.registry.addMenuItem('editimage', {
                icon: 'image',
                text: '<?php echo Yii::t("newDS", "Add/Remove Watermark");?>',
                onAction: function () {
                    if(container.Watermark.indexOf(container.WatermarkImg) == -1){
                        container.Watermark=container.Watermark+container.WatermarkImg
                    }else{
                        container.Watermark=container.Watermark.replace(container.WatermarkImg,"")
                    }
                    editor.insertContent('<div><img  style="max-width:100%"  src='+container.Watermark+'></div>')
                }
            });
            editor.ui.registry.addContextMenu('image', {
                update: function (element) {
                    container.Watermark = element.src
                    return !container.Watermark ? '' : 'image';
                }
            });
        });
    }
    function handleUploadImage2(blobInfo, success, failure, progress) {
        $.ajax({
            url: '<?php echo $this->createUrlReg("//mteaching/journals/getQiniuTokenSimple") ?>',
            type: "post",
            dataType: 'json',
            data: {
                isPrivate: 0,
                prefix: 'pact',
            },
            success: function (data) {
                if (data.state == 'success') {
                    var token = data.data.token;
                    var domain = data.data.domain;
                    // 上传文件

                    var xhr = new XMLHttpRequest();
                    xhr.withCredentials = false;
                    xhr.open("post", "https://up-z0.qiniup.com");
                    xhr.upload.onprogress = function (e) {
                        progress(Math.round(e.loaded / e.total * 100) | 0);
                    };
                    xhr.onerror = function () {
                        failure('Image upload failed due to a XHR Transport error. Code: ' + xhr.status);
                    };
                    xhr.onload = function () {
                        var json;

                        if (xhr.status === 403) {
                            failure('HTTP Error: ' + xhr.status, {remove: true});
                            return;
                        }

                        if (xhr.status < 200 || xhr.status >= 300) {
                            failure('HTTP Error: ' + xhr.status);
                            return;
                        }

                        json = JSON.parse(xhr.responseText);

                        if (!json || typeof json.name != 'string') {
                            failure('Invalid JSON: ' + xhr.responseText);
                            return;
                        }
                        success(domain + "/" + json.name + container.WatermarkImg);
                    };
                    var formData = new FormData();
                    var file = blobInfo.blob();
                    formData.append('file', file, file.name);
                    formData.append('token', token);
                    xhr.send(formData);
                } else {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                }
            },
            error: function (data) {
                resultTip({
                    error: 'warning',
                    msg: "上传失败"
                });
            },
        })
    }

</script>
<style>
    input[type="file"]{
        display: none;!important;
    }
</style>
