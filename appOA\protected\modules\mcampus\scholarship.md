# easyapply 奖学金接口
```


```

[TOC] 

## 一、奖学金

### 1、奖学金主页

**请求URI**
```
/scholarship/index
```

**请求方式**
```
POST
```

**请求参数**

| 参数     | 是否必选 | 类型   | 示例 | 说明     |


**返回说明**

| 参数  | 说明      |
| :---- | :-------- |
| $calendarInfo | 学年数组 |
| $nowYear | 当前学年 |

**返回示例**

```javascript
(

)
```

### 2-1、奖学金数据根据前端写得方法  （以孩子为一条数据返回得）

**请求URI**
```
/scholarship/findScholarShipT
```

**请求方式**
```
POST
```

**请求参数**

| 参数     | 是否必选 | 类型   | 示例  | 说明     |
| schoolYear | Y | INT | 2019 | 学年 |
| yid | Y | INT | 99 | 校历ID |
| page | Y | INT | 1 | 页数 |
| pageNum |N | INT | 20 | 每页显示条数 默认为20 |

**返回说明**

| 参数  | 说明      |
| :---- | :-------- |
| total | 总页数 |
| currentPage | 当前页 |
| pageNum | 显示条数 |
| info | 数据 |

**返回示例**

```javascript
Array
(
    [currentPage] => 1
    [pageNum] => 20
    [total] => 1
    [info] => Array
        (
            [0] => Array
                (
                    [childid] => 15447
                    [name] => 黄策
                    [birthdate] => 2013-04-26
                    [grade] => 2019-2020  Grade 1 C
                    [fName] => 黄锐
                    [fPhone] => 13800000001
                    [fEmail] => <EMAIL>
                    [mName] => 郁梦
                    [mPhone] => 13800000002
                    [mEmail] => <EMAIL>
                    [Reason] => 请陈述您申请奖学金的原因。请陈述您的家庭收入，支出及存款情况。并提及任何可能给您家庭带来经济负担的状况。
                    [attach] => Array
                        (
                            [0] => scholarship/202004/5e8431d9b76f6.png
                        )

                )

        )

)

```
### 2-2、奖学金数据修改后得（一条数据整体返回） 

**请求URI**
```
/scholarship/findScholarShip
```

**请求方式**
```
GET
```

**请求参数**

| 参数     | 是否必选 | 类型   | 示例  | 说明     |
| schoolYear | Y | INT | 2019 | 学年 |
| yid | Y | INT | 99 | 校历ID |
| page | Y | INT | 1 | 页数 |
| pageNum |N | INT | 20 | 每页显示条数 默认为20 |

**返回说明**

| 参数  | 说明      |
| total | 总页数 |
| currentPage | 当前页 |
| pageNum | 显示条数 |
| info | 数据 |

**返回示例**

```javascript
Array
(
    [total] => 1
    [pageNum] => 20
    [currentPage] => 1
    [info] => Array
        (
            [0] => Array
                (
                    [id] => 11
                    [childInfo] => Array
                        (
                            [0] => Array
                                (
                                    [childid] => 15447
                                    [name] => 黄策
                                    [birthdate] => 2013-04-26
                                    [grade] => 2019-2020  Grade 1 C
                                )

                        )

                    [fName] => 黄锐
                    [fPhone] => 13800000001
                    [fEmail] => <EMAIL>
                    [mName] => 郁梦
                    [mPhone] => 13800000002
                    [mEmail] => <EMAIL>
                    [Reason] => 请陈述您申请奖学金的原因。请陈述您的家庭收入，支出及存款情况。并提及任何可能给您家庭带来经济负担的状况。
                    [attach] => Array
                        (
                            [0] => scholarship/202004/5e8431d9b76f6.png
                        )

                )

        )

)

```
### 3、文件下载

**请求URI**
```
/scholarship/ossfileRedit
```

**请求方式**
```
GET
```

**请求参数**

| 参数     | 是否必选 | 类型   | 示例  | 说明     |
| filePath | Y | string | scholarship/202004/5e8431d9b76f6.png | 图片链接 |

**返回说明**

| 参数  | 说明      |
| :---- | :-------- |

**返回示例**

```javascript
(
)

```
### 4、删除 （只针对 2-2 数据类型） 修改状态 为99 

**请求URI**
```
/scholarship/delScholarship
```

**请求方式**
```
GET
```

**请求参数**

| 参数     | 是否必选 | 类型   | 示例  | 说明     |
| id | Y | INT | 1 | 数据主键 |

**返回说明**

| 参数  | 说明      |
| :---- | :-------- |

**返回示例**

```javascript
(
)

```


