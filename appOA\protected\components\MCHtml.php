<?php
/**
 * 跟framework 1.1.11 里的CHtml::radioButtonList函数几乎一样，唯一不一样的就是$id是通过参数传入，
 * 原函数中$id是从0开始
 **/
class MCHtml extends CHtml{
	public static function groupRadioButtonList($name,$select,$data, $startid=0,$htmlOptions=array()){
		$template=isset($htmlOptions['template'])?$htmlOptions['template']:'{input} {label}';
		$separator=isset($htmlOptions['separator'])?$htmlOptions['separator']:"<br/>\n";
		$container=isset($htmlOptions['container'])?$htmlOptions['container']:'span';
		unset($htmlOptions['template'],$htmlOptions['separator'],$htmlOptions['container']);

		$labelOptions=isset($htmlOptions['labelOptions'])?$htmlOptions['labelOptions']:array();
		unset($htmlOptions['labelOptions']);

		$items=array();
		$baseID=self::getIdByName($name);
		$id=$startid;
		foreach($data as $value=>$label)
		{
			$checked=!strcmp($value,$select);
			$htmlOptions['value']=$value;
			$htmlOptions['id']=$baseID.'_'.$id++;
			$option=self::radioButton($name,$checked,$htmlOptions);
                        $currentLabelOptions = $labelOptions;
                        if(!empty($currentLabelOptions)){
                            foreach ($currentLabelOptions as $l_k => $l_v) {
                                if('{value}' == $l_v){
                                    $currentLabelOptions[$l_k] = $value;
                                }
                            }
                        }
			$label=self::label($label,$htmlOptions['id'],$currentLabelOptions);
			$items[]=strtr($template,array('{input}'=>$option,'{label}'=>$label));
		}
		if(empty($container))
			return implode($separator,$items);
		else
			return self::tag($container,array('id'=>$baseID),implode($separator,$items));
	}
    
    public static function serviceDay($model, $attribute, $halfDay=false)
    {
        $weekday = array('mon', 'tue', 'wed', 'thu', 'fri');
        $tag = CHtml::openTag('div', array('class'=>'service-day'));
        foreach ($weekday as $day){
            $name = $attribute.'['.$day.']';
            $tag .= CHtml::openTag('i', array('class'=>'day-10', 'title'=>ucfirst($day), 'halfday'=>$halfDay, 'rel'=>self::getIdByName($name)));
            $tag .= CHtml::closeTag('i');
            $tag .= CHtml::activeHiddenField($model, $name, array('id'=>self::getIdByName($name), 'value'=>'10'));
        }
        $tag .= CHtml::openTag('div', array('class'=>'c'));
        $tag .= CHtml::closeTag('div');
        $tag .= CHtml::closeTag('div');
        return $tag;
    }

}