<?php

/**
 * 小学课程表
 * <AUTHOR>
 */
class GradeSchedule extends CWidget{
    public $classId;
    public $yId;
    public function init() {
        parent::init();
        Yii::import('common.models.grades.*');
        Yii::import('common.config.CfgCampusProgram');
        $cs = Yii::app()->clientScript;
        $cs->registerCssFile(Yii::app()->theme->baseUrl.'/widgets/schedule/schedule.css?t='.Yii::app()->params['refreshAssets']);
        $cs->registerCssFile(Yii::app()->theme->baseUrl.'/css/widgetbase.css?t='.Yii::app()->params['refreshAssets']);
    }
    public function run(){
        $linkModel = GradesScheduleLink::model()->find('class_id=:classid and yid=:yid',array(':classid'=>$this->classId,':yid'=>$this->yId));
        $programs = CommonUtils::LoadConfig('CfgCampusProgram');
        $schoolId = Yii::app()->controller->getSchoolId();
        $branchModel = Branch::model()->findByPk($schoolId);
        $program = isset( $programs[$branchModel->type] ) ? $programs[$branchModel->type] : null;
        if(empty($program)){
            echo 'Invalid Campus Type, DayStar Type use ONLY.';
            Yii::app()->end();
        }
        if (!empty($linkModel)){
            $items = GradesScheduleItem::model()->findAllByAttributes(array(
                    'schedule_id' => $linkModel->schedule_id,
                    'classid' => $this->classId,
                ));
            foreach($items as $val){
                $itemsList[$val->timeslot][$val->weekday][] = $val->getAttributes();
            }
            $this->render('grade_schedule',array('items'=>$itemsList,'program'=>$program,'linkModel'=>$linkModel));
        }
    }
}

