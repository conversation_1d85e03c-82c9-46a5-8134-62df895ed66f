<?php
class BJDSBus implements RefundInterface
{
    public $invoiceObj;
    public $config;
    public $workflow;
    public function __construct($invoiceObj,$config,$workflow) {
        $this->invoiceObj = $invoiceObj;
        $this->config = $config;
        $this->workflow = $workflow;
    }

    public function fee(){
        $ret = 0;   
        $calendarDay = $this->getCalendar();
        $refundStartDate = $this->workflow->opertationObj->exte1;
        $refundEndDate = ($this->workflow->opertationObj->exte2) ? $this->workflow->opertationObj->exte2 : $this->invoiceObj->enddate;
        //全退
        if ($refundStartDate <= $this->invoiceObj->startdate){
            $ret = $this->invoiceObj->original_amount;
            return $ret;
        }
        
        //整月退
        if (date('j',$refundStartDate) == 1){
            $ret = round($this->getAmountByMonth() * $this->getMonthNum($refundStartDate, $refundEndDate));
            return $ret;
        }
        
        $ret = round($this->getAmountByMonth() * ($this->getMonthNum($refundStartDate, $refundEndDate)-1));
        return $ret;
    }
    
    /**
     * 获取月份
     */
    public function getMonthNum($refundStartDate,$refundEndDate){
        Yii::import('common.models.calendar.CalendarSchoolDays');
        $schoolDay = CalendarSchoolDays::model()->getCalendarSchoolDay($this->invoiceObj->calendar_id, $refundStartDate,$refundEndDate);
        return count($schoolDay);
    }
    
    /**
     * 月付金额
     */
    public function getAmountByMonth(){
        Yii::import('common.models.calendar.CalendarSchoolDays');
        $schoolDay = CalendarSchoolDays::model()->getCalendarSchoolDay($this->invoiceObj->calendar_id, $this->invoiceObj->startdate, $this->invoiceObj->enddate);
        $schoolDay = (count($schoolDay) > 10) ? 10 :count($schoolDay) > 10;
        return ($this->invoiceObj->original_amount/$schoolDay);
    }


    /*
     * 当前学年的校历
     */
    public function getCalendar(){
        Yii::import('common.models.calendar.CalendarSemester');
        $calendar = CalendarSemester::model()->getSemesterTimeStamp();
        return $calendar;
    }
}
