<!DOCTYPE html>
<!--
  _______ _            _____            _____
 |__   __| |          |_   _|          / ____|
    | |  | |__   ___    | |_   ___   _| |  __ _ __ ___  _   _ _ __
    | |  | '_ \ / _ \   | \ \ / / | | | | |_ | '__/ _ \| | | | '_ \
    | |  | | | |  __/  _| |\ V /| |_| | |__| | | | (_) | |_| | |_) |
    |_|  |_| |_|\___| |_____\_/  \__, |\_____|_|  \___/ \__,_| .__/
                                  __/ |                      | |
                                 |___/                       |_|
-->
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title><?php echo CHtml::encode($this->pageTitle); ?></title>

    <!-- Bootstrap core CSS -->
    <link href="<?php echo Yii::app()->theme->baseUrl; ?>/css/bootstrap.css" rel="stylesheet">
    <link href="<?php echo Yii::app()->theme->baseUrl; ?>/css/css.css?v<?php echo Yii::app()->params['refreshAssets'];?>" rel="stylesheet">
    <script>
        var GV = {
            'themeManagerBaseUrl': '<?php echo Yii::app()->themeManager->baseUrl;?>',
            'themeBaseUrl': '<?php echo Yii::app()->theme->baseUrl;?>',
            'lang': "<?php echo Yii::app()->language;?>"
        };
    </script>
    <script src="<?php echo Yii::app()->themeManager->baseUrl; ?>/base/js/head.min.js"></script>
    <?php
        if (!isset($this->nonJquery) || !$this->nonJquery) {
            Yii::app()->clientScript->registerCoreScript('jquery');
        }
    ?>
    <script src="<?php echo Yii::app()->themeManager->baseUrl; ?>/base/js/bootstrap.min.js"></script>
    <script>var LANG="<?php echo Yii::app()->language;?>";</script>

</head>
<style>
    #shortcut_link:hover{
        background:#428bca;
        cursor: pointer;
    }
</style>
<body style="padding-top: 70px;">
<div class="navbar navbar-fixed-top navbar-inverse" role="navigation">
    <?php if(Yii::app()->params['siteFlag'] == 'daystar' && Yii::app()->user->id):?>
        <div  class='tipRelative ' style="float: left;margin-right: 12px;" data-position='top' data-id='shortcutLink' data-title='<?php echo Yii::t('admissions', 'Quick Links');?>' data-desc='<?php echo Yii::t('admissions', 'Click here to view all quick links.');?>' data-btnText='<?php echo Yii::t("admissions","Got it");?>'>
        <div id="shortcut_link" class=" text-center" title='<?php echo Yii::t('admissions', 'Quick Links');?>' style="display: inline-block;width: 50px;height: 50px;color: #fff;"  >
            <span class="glyphicon glyphicon-list" aria-hidden="true" style="font-size: 16px;margin-top: 17px;"></span>
        </div>
    </div>
    <?php endif;?>
    <div class="container-fluid">
        <div class="navbar-header">
            <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>
            <div class="navbar-brand">
                <?php echo CHtml::link(Yii::t('site',Yii::app()->name), array('/backend/apps?v2'), array('class'=>'navbar---brand'));?>
                <?php if(!empty($this->modernMenuCategoryTitle)):?>
                    <?php
                    echo ' <span class="glyphicon glyphicon-chevron-right"></span> ';
                    echo CHtml::openTag('span', array('style'=>"position: relative"));
                    echo CHtml::link($this->modernMenuCategoryTitle.'<b class="caret"></b>', 'javascript:void(0);', array('class'=>'dropdown-toggle','data-toggle'=>"dropdown", 'id'=>'dropdown-maincate'));?>
                    <ul class="dropdown-menu main-menu-toggle" role="menu" aria-labelledby="dropdown-maincate">
                        <?php
                            foreach($this->modernMenu['main'] as $_mainMenu){
                                echo CHtml::openTag('li');
                                echo CHtml::link(CHtml::openTag('dt') . $_mainMenu['label'] . CHtml::closeTag('dt'),"javascript:void(0)","");
                                echo CHtml::closeTag('li');
                                foreach($_mainMenu['items'] as $_item){
                                    echo CHtml::openTag('li');
                                    echo CHtml::link($_item['label'],$_item['url'],isset($_item['linkOptions']) ? $_item['linkOptions'] : array());
                                    echo CHtml::closeTag('li');
                                }
                            }
                        ?>
                    </ul>
                    <?php echo CHtml::closeTag('span');?>
                <?php endif;?>
            </div>
        </div>
        <div class="collapse navbar-collapse">

            <div class="nav navbar-nav navbar-right">
                <li class="dropdown">
                    <a class="dropdown-toggle" data-toggle="dropdown" href="#"><span class="glyphicon glyphicon-user"></span>
                        <?php
                            if (isset($this->staff)) {
                                echo (Yii::app()->language == 'zh_cn') ? $this->staff->name : $this->staff->profile->first_name.' '.$this->staff->profile->last_name;
                            }
                        ?> <b class="caret"></b>
                    </a>
                    <?php

                    $userItems = array(
                        array('label'=>Yii::t('site','My Profile'), 'url'=>array("//user/profile/index", "category" => "basic")),
                        array('label'=>'English', 'url'=>array("//site/lang", "lang" => "english"), 'linkOptions'=>array('class'=>'J_lang', 'lang'=>'en_us')),
                        array('label'=>'中文', 'url'=>array("//site/lang", "lang" => "schinese_utf8"), 'linkOptions'=>array('class'=>'J_lang','lang'=>'zh_cn'))
                    );

                    if(Yii::app()->user->id){
                        $userItems[] = array('label'=>Yii::t('user','Logout'), 'url'=>Yii::app()->user->logoutUrl);
                    }

                    $this->widget('zii.widgets.CMenu', array(
//                        'id'=>'lang-switch',
                        'items'=> $userItems,
                        'htmlOptions'=>array('class' => 'dropdown-menu'),
                        'encodeLabel'=>false,
                    ));
                    ?>
                </li>
            </div>
            <?php if(Yii::app()->params['siteFlag'] == 'ivygroup'):?>
            <div class="nav navbar-nav navbar-right">
                <li id="oldversion"><a target="_blank" href="<?php echo Yii::app()->params['OABaseUrl']?>"><?php echo Yii::t('user','Old Version');?></a></li>
            </div>
            <?php endif;?>
            <?php
            if(isset($this->modernMenu) && isset($this->modernMenu[$this->modernMenuFlag])):
                $this->widget('zii.widgets.CMenu', array(
                    'id'=>'main-nav',
                    'items'=>$this->modernMenu[$this->modernMenuFlag],
                    'htmlOptions'=>array('class' => 'nav navbar-nav'),
                    'encodeLabel'=>false,
                ));
            endif;
            ?>
        </div><!-- /.nav-collapse -->
    </div><!-- /.container -->
</div><!-- /.navbar -->

<?php echo $content; ?>

<button class="btn btn-primary btn-xs" type="button" onclick="feedback()" title="<?php echo Yii::t('settings', 'Feedback');?>" style="display:none;position:fixed;right:0px;bottom:200px;">
    <span class="glyphicon glyphicon-edit"></span>
</button>
<div class="modal fade" id="feedback" tabindex="-1" role="dialog" aria-labelledby="modal">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title"><?php echo Yii::t('settings', 'Feedback');?></h4>
            </div>
            <form class="J_ajaxForm form-horizontal" role="form" action="<?php echo Yii::app()->createUrl('/moperation/feedback/saveFeedback');?>" method="post">
                <div class="modal-body">
                    <div class="">
                        <div class="alert alert-info" role="alert"><?php echo Yii::t('message','Please do let us know if you have any suggestions about our online system! We will keep improving our system based on your feedbacks and provide better service for you!')?></div>
                        <textarea class="form-control" rows="3" name="Comment[content]"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit');?></button>
                    <button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global', 'Cancel');?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="modal fade" id="add_shortcut" tabindex="-1" role="dialog" aria-labelledby="modal">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title"><?php echo Yii::t('global', 'Add to customized menu');?></h4>
            </div>
            <form class="J_ajaxForm form-horizontal" role="form" action="<?php echo $this->createUrl('/backend/apps/addShortcut');?>" method="post">
                <div class="modal-body">
                    <div style="margin-bottom: 15px;">
                        <label for="input_link"><?php echo Yii::t('global', 'Link: ');?></label>
                        <div class="input-group">
                            <div class="input-group-btn">
                                <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><span id="prefix_url">https://</span> <span class="caret"></span></button>
                                <input type="hidden" name="prefix_url_hide" id="prefix_url_hide" value="https://">
                                <ul class="dropdown-menu">
                                    <li><a href="javascript:;" onclick="urlPrefix('https://')">https://</a></li>
                                    <li><a href="javascript:;" onclick="urlPrefix('http://')">http://</a></li>
                                </ul>
                            </div><!-- /btn-group -->
                            <input type="text" class="form-control" id="input_link" name="input_link" placeholder="<?php echo Yii::t('global', 'Please input link');?>" onblur="urlHandle(this.value)">
                        </div>
                    </div>
                    <div>
                        <label for="input_name"><?php echo Yii::t('global', 'Title: ');?></label>
                        <input type="text" class="form-control" id="input_name" name="input_name" placeholder="<?php echo Yii::t('global', 'Please input title');?>">
                    </div>
                </div>
                <div class="modal-footer">
                    <input type="hidden" name="id" id="shortcut_id" value="0">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel');?></button>
                    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit');?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<div id="shortcut_link_warp" class="shortcut-link" style="display: none;">
    <div class="shortcut-content" style="display: none;">
        <span aria-hidden="true" class="close">×</span>
        <div class="shortcut-sys">
            <div class="shortcut-header">
                <ul class="nav nav-pills" id="link_category"></ul>
                <div class="label-01">
                    <label><input type="checkbox" id="new_window"> <?php echo Yii::t('global', 'Open in new window');?></label>
                </div>
            </div>
            <div style="height: calc(100vh - 150px);overflow-y: auto;"><div id="link_items"></div></div>
        </div>
    </div>
</div>

<script type="text/javascript">
    /*<![CDATA[*/
    jQuery(function($) {
        jQuery('body').on('click','.J_lang',function(){jQuery.ajax({'dataType':'json','success':function(data, textStatus, jqXHR){window.location.reload()},'url':$(this).attr('href'),'cache':false});return false;});
//        $('#lang-switch a[lang|='+LANG+']').parent().addClass('active');

        <?php if(Yii::app()->params['siteFlag'] == 'daystar' && Yii::app()->user->id):?>
        const sid = document.cookie.substr(document.cookie.indexOf('XOSESSIONID2=')+13, 27);
        const linksKey = 'SHORTCUT-LINK-' + sid + '-' + GV.lang;
        let linksData = sessionStorage.getItem(linksKey);
        let editIndex;
        if (!linksData) {
            jQuery.ajax({
                url: '<?php echo $this->createUrl('/backend/apps/shortcutLink');?>',
                type: 'get',
                dataType: 'json',
            }).done(function (res) {
                if (res.state == 'success') {
                    linksData = JSON.stringify(res.data);
                    sessionStorage.setItem(linksKey, linksData);
                }
            });
        }
        else {

        }
        <?php endif;?>

        function shortcutLinkHand(linksData)
        {
            const isNewWindow = localStorage.getItem('NEW_WINDOW') == 'true' ? Boolean(localStorage.getItem('NEW_WINDOW')) : false;
            const aTarget = !isNewWindow ? '_blank' : '_self';
            $('#new_window').prop('checked', !isNewWindow);
            //const _link = '<?php //echo Yii::app()->urlManager->baseUrl;?>//';
            const _link = '';
            const link_category = $('#link_category');
            link_category.html('');
            linksData = JSON.parse(linksData);
            for (let key in linksData['system']) {
                link_category.append('<li><a href="javascript:;" data-key="'+key+'">'+ linksData['system'][key].title +'</a></li>');
            }
            link_category.on('click', 'li a', function (){
                $('#link_category li').removeClass('active');
                $(this).parent().addClass('active');

                const key = $(this).data('key');
                const _data = linksData['system'][key]['items'];
                const itemObj = $('#link_items');
                itemObj.html('');
                let linkWarp, linkItem
                for (let key in _data) {
                    linkWarp = '<div class="category-link">';
                    if(key==10){
                        <?php if(Yii::app()->user->checkAccess('ivystaff_hos_office')){?>
                        linkWarp += '<div class="category-title">'+
                            '<div class="flex1"><i class="category-icon category-'+_data[key]['icon']+'"></i>'+_data[key]['title']+'</div>'+
                            '<a href="<?php echo $this->createUrl("/mcampus/shortcut/index")?>" style="text-decoration: none" class="glyphicon glyphicon-cog font14 category-title-set" title="<?php echo Yii::t('management', 'Management');?>"></a>'
                        <?php }else{?>
                        linkWarp += '<div class="category-title">'+
                            '<div class="flex1"><i class="category-icon category-'+_data[key]['icon']+'"></i>'+_data[key]['title']+'</div>'
                        <?php }?>
                    }else{
                        linkWarp += '<div class="category-title"><div><i class="category-icon category-'+_data[key]['icon']+'"></i>'+_data[key]['title']+'</div>';
                    }
                    if (key == 99) {
                        linkWarp += '<div class="shortcut-user-header-02">';
                        linkWarp += '<a href="javascript:addShortcut();" id="add_shortcut_btn">';
                        linkWarp += '<span class="glyphicon glyphicon-plus-sign" aria-hidden="true"></span> '
                        linkWarp += '<span><?php echo Yii::t('global', 'Add');?></span>'
                        linkWarp += '</a>'
                        if (_data[key]['items'].length > 0) {
                            linkWarp += '<a href="javascript:mgtShortcut();" id="mgt_shortcut_btn" style="margin-left: 26px;">'
                            linkWarp += '<span class="glyphicon glyphicon-cog" aria-hidden="true"></span> '
                            linkWarp += '<span><?php echo Yii::t('management', 'Management');?></span>'
                            linkWarp += '</a>'
                        }
                        linkWarp += '<a href="javascript:exitmgtShortcut();" id="exitmgt_shortcut_btn" style="display: none;">'
                        linkWarp += '<span class="glyphicon glyphicon-log-out" aria-hidden="true"></span> '
                        linkWarp += '<span><?php echo Yii::t('global', 'Exit Management');?></span>'
                        linkWarp += '</a>'
                        linkWarp += '</div>';
                    }
                    linkWarp += '</div>';
                    let emptyClass = _data[key]['items'].length > 0 ? '' : ' empty';
                    if(_data[key]['items'].length > 0){
                        linkWarp += '<div class="category-item'+emptyClass+'" id="category_'+key+'">';
                        for (let _key in _data[key]['items']) {
                            let __data = _data[key]['items'][_key];
                            let link = __data['link'];
                            link = atob(link.substr(0, link.length-26));
                            linkItem = ''
                            linkItem += '<a href="'+_link+link+'" class="link-item" target="'+aTarget+'" data-id="'+__data['id']+'" data-key="'+_key+'">';
                            linkItem += '<div class="link-item-title">'+__data['title']+'</div>';
                            if (__data['intro']) {
                                // linkItem += '< class="link-item-intro">';
                                linkItem += '<span class="glyphicon glyphicon-question-sign font16 link-item-intro" aria-hidden="true" data-toggle="tooltip" data-placement="left" title="'+__data['intro']+'"></span>';
                            }
                            linkItem += '</a>'
                            linkWarp += linkItem;
                        }
                    }else{
                        linkWarp += '<div class="category-item-not font12 color9">暂无数据';
                    }
                    itemObj.append(linkWarp + '</div></div>');
                }

                $('[data-toggle="tooltip"]').tooltip()
                localStorage.setItem('LINK_CATEGORY', key)
            })

            let activeCategory;
            const linkCategory = localStorage.getItem('LINK_CATEGORY')
            if (linkCategory && Object.keys(linksData['system']).indexOf(linkCategory) > -1) {
                activeCategory =  localStorage.getItem('LINK_CATEGORY')
            }
            else {
                activeCategory =  linksData['active']
            }
            link_category.find('a[data-key="'+activeCategory+'"]').click()
        }
        function shortcutToggle()
        {
            const warpObj = $('#shortcut_link_warp');
            const warp2Obj = $('#shortcut_link_warp .shortcut-content');
            const btnObj = $('#shortcut_link');
            if (warpObj.is(':visible')) {
                warpObj.hide();
                warp2Obj.hide(200);
                btnObj.css('background-color', '');
                $('body').css('overflow', 'auto');
            }
            else {
                let linksData = sessionStorage.getItem(linksKey);
                shortcutLinkHand(linksData)
                warpObj.show();
                warp2Obj.show(200);
                btnObj.css('background-color', '#4D88D2');
                $('body').css('overflow', 'hidden');
            }
        }

        jQuery('#shortcut_link').click(function () {
            shortcutToggle();
        })
        jQuery('#shortcut_link_warp').click(function () {
            shortcutToggle();
        })
        jQuery('#shortcut_link_warp .close').click(function () {
            shortcutToggle();
        })
        jQuery('#shortcut_link_warp .shortcut-content').click(function (event) {
            event.stopPropagation();
        })
        jQuery('#new_window').click(function () {
            localStorage.setItem('NEW_WINDOW', !$(this).is(':checked'));
            shortcutLinkHand(linksData);
        })

        window.interflow = function (data) {
            linksData = JSON.parse(linksData);
            linksData['system'][99]['items'][99]['items'].unshift({
                'id': data.id,
                'link': data.link,
                'title': data.name,
                'intro': ''
            });
            linksData = JSON.stringify(linksData);
            shortcutLinkHand(linksData);
            removeLinkStorage();
        }
        window.interflow2 = function (index) {
            linksData = JSON.parse(linksData);
            linksData['system'][99]['items'][99]['items'].splice(index, 1);
            linksData = JSON.stringify(linksData);
            shortcutLinkHand(linksData);
        }
        window.interflow3 = function (index) {
            linksData = JSON.parse(linksData);
            const id = linksData['system'][99]['items'][99]['items'][index]['id'];
            const title = linksData['system'][99]['items'][99]['items'][index]['title'];
            const link = linksData['system'][99]['items'][99]['items'][index]['link'];
            linksData = JSON.stringify(linksData);
            editIndex = index

            return [id, title, link];
        }
        window.interflow4 = function (data) {
            linksData = JSON.parse(linksData);
            linksData['system'][99]['items'][99]['items'][editIndex] = {
                'id': data.id,
                'link': data.link,
                'title': data.name,
                'intro': ''
            };
            linksData = JSON.stringify(linksData);
            shortcutLinkHand(linksData);
            removeLinkStorage();
        }
        window.removeLinkStorage = function () {
            sessionStorage.removeItem(linksKey);
        }
    });
    /*]]>*/

    function feedback() {
        $('#feedback').modal({
            show: true,
            backdrop: 'static'
        });
    }

    $('#feedback').on('shown.bs.modal', function (e) {
        $('#feedback textarea').focus();
    })

    // 回调：反馈成功
    function cbFeedback() {
        $('#feedback').modal('hide');
    }

    function addShortcut() {
        $('#add_shortcut #shortcut_id').val(0);
        $('#add_shortcut #input_link').val('');
        $('#add_shortcut #input_name').val('');
        $('#add_shortcut').modal({
            show: true,
            backdrop: 'static'
        });
    }
    $('#add_shortcut').on('shown.bs.modal', function (e) {
        $('#add_shortcut #input_link').focus();
    })
    function cbShortcut(data) {
        $('#add_shortcut').modal('hide');
        $('#add_shortcut #input_link').val('');
        $('#add_shortcut #prefix_url_hide').val('https://');
        $('#add_shortcut #input_name').val('');
        window.interflow(data);
    }
    function cbShortcut2(data) {
        $('#add_shortcut').modal('hide');
        $('#add_shortcut #shortcut_id').val(0);
        $('#add_shortcut #input_link').val('');
        $('#add_shortcut #input_name').val('');
        window.interflow4(data);
        mgtShortcut()
    }
    function mgtShortcut() {
        $('#add_shortcut_btn').hide();
        $('#mgt_shortcut_btn').hide();
        $('#exitmgt_shortcut_btn').show();
        $('#category_99 a').each(function () {
            $(this).addClass('link-item-active');
            $(this).append('<span class="glyphicon glyphicon-edit" onclick="editShortcut(this)" style="margin-right: 15px;"></span><span class="glyphicon glyphicon-trash" onclick="reShortcut(this)"></span>');
        });
    }
    function exitmgtShortcut() {
        $('#add_shortcut_btn').show();
        $('#mgt_shortcut_btn').show();
        $('#exitmgt_shortcut_btn').hide();
        $('#category_99 a').each(function () {
            $(this).removeClass('link-item-active');
            $(this).find('span').remove();
        });
    }
    function reShortcut(_this) {
        const e = window.event || arguments.callee.caller.arguments[0];
        e.preventDefault();
        e.stopPropagation();
        const id = $(_this).parent().data('id');
        const index = $(_this).parent().data('key');
        $.ajax({
            url: '<?php echo $this->createUrl('/backend/apps/removeShortcut');?>',
            type: 'post',
            dataType: 'json',
            data: {id: id}
        }).done(function (res) {
            if (res.state == 'success') {
                $(_this).parent().remove();
                $('#category_99 a').each(function (index) {
                    $(this).data('key', index);
                });
                window.interflow2(index);
                window.removeLinkStorage();
            }
        });
    }
    function editShortcut(_this) {
        const e = window.event || arguments.callee.caller.arguments[0];
        e.preventDefault();
        e.stopPropagation();
        const index = $(_this).parent().data('key');
        const ret = window.interflow3(index);
        $('#add_shortcut').modal({
            show: true,
            backdrop: 'static'
        });
        let link = ret[2]
        link = atob(link.substr(0, link.length-26));
        $('#add_shortcut #shortcut_id').val(ret[0]);
        $('#add_shortcut #input_link').val(link);
        $('#add_shortcut #input_name').val(ret[1]);
        urlHandle(link)
    }
    function urlPrefix(prefix)
    {
        $('#prefix_url_hide').val(prefix)
        $('#prefix_url').html(prefix)
    }
    function urlHandle(url)
    {
        const isHttp = url.substr(0, 7) === 'http://';
        const isHttps = url.substr(0, 8) === 'https://';
        if (isHttps) {
            urlPrefix('https://')
            url = url.substr(8)
        }
        if (isHttp) {
            urlPrefix('http://')
            url = url.substr(7)
        }
        $('#input_link').val(url)
    }
    function setCategory(){
        window.open('<?php echo $this->createUrl("/mcampus/shortcut/index")?>');
    }
</script>
<script type="text/javascript" src="<?php echo Yii::app()->themeManager->baseUrl; ?>/base/js/common.js?v2025"></script>
</body>
</html>