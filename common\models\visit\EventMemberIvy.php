<?php

/**
 * This is the model class for table "ivy_event_member_ivy".
 *
 * The followings are the available columns in table 'ivy_event_member_ivy':
 * @property integer $id
 * @property integer $event_id
 * @property string $parent_name
 * @property string $telphone
 * @property string $email
 * @property string $schoolid
 * @property string $ivyclass
 * @property string $child_name
 * @property integer $adult_number
 * @property integer $kid_number
 * @property integer $timestamp
 * @property string $ip
 */
class EventMemberIvy extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return EventMemberIvy the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_event_member_ivy';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('event_id, parent_name, telphone, email,schoolid, ivyclass, child_name, adult_number, kid_number, timestamp', 'required'),
			array('event_id, adult_number, kid_number, timestamp', 'numerical', 'integerOnly'=>true),
			array('parent_name, telphone, email,ivyclass, child_name', 'length', 'max'=>255),
			array('schoolid', 'length', 'max'=>30),
			array('ip', 'length', 'max'=>64),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, event_id, parent_name, telphone, email,schoolid, ivyclass, child_name, adult_number, kid_number, timestamp, ip', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'event_id' => 'Event',
			'parent_name' => Yii::t('event', 'Parent Name'),
			'telphone' => Yii::t('event', 'Telphone'),
			'schoolid' => Yii::t('event', 'Schoolid'),
			'ivyclass' => Yii::t('event', 'Ivyclass'),
			'child_name' => Yii::t('event', 'Child Name'),
			'adult_number' => Yii::t('event', 'Adult Number'),
			'kid_number' => Yii::t('event', 'Kid Number'),
			'timestamp' => Yii::t('event', 'Timestamp'),
			'email' => Yii::t('event', 'Email'),
			'ip' => 'Ip',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('event_id',$this->event_id);
		$criteria->compare('parent_name',$this->parent_name,true);
		$criteria->compare('telphone',$this->telphone,true);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('ivyclass',$this->ivyclass,true);
		$criteria->compare('child_name',$this->child_name,true);
		$criteria->compare('adult_number',$this->adult_number);
		$criteria->compare('kid_number',$this->kid_number);
		$criteria->compare('timestamp',$this->timestamp);
		$criteria->compare('ip',$this->ip,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}