<?php

/**
 * This is the model class for table "ivy_calendar_yearly".
 *
 * The followings are the available columns in table 'ivy_calendar_yearly':
 * @property integer $yid
 * @property string $title
 * @property string $startyear
 * @property integer $created_timestamp
 * @property integer $userid
 * @property integer $stat
 * @property string $semester
 * @property integer $updated_timestamp
 */
class Calendar extends CActiveRecord
{
    public $firstSemester;
    public $secondSemester;
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return Calendar the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_calendar_yearly';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('startyear, title, userid', 'required'),
			array('created_timestamp, userid, stat, updated_timestamp', 'numerical', 'integerOnly'=>true),
			array('title, semester', 'length', 'max'=>255),
			array('timepoints', 'length', 'max'=>64),
			array('startyear', 'length', 'max'=>4),
            array('firstSemester, secondSemester', 'checkSemesterTime', 'on'=>'nCalendar'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('yid, title, startyear, created_timestamp, userid, stat, semester, updated_timestamp, timepoints', 'safe', 'on'=>'search'),
		);
	}

    public function checkSemesterTime($attribute, $params)
    {
        $beginTime =  $this->{$attribute}[0];
        $endTime =  $this->{$attribute}[1];

        $date_ym = '/\d{4}\-\d{1,2}\-\d{1,2}$/';
        if(!preg_match($date_ym, $beginTime) || !preg_match($date_ym, $endTime)){
            $this->addError($attribute, Yii::t("labels", "Invalid date (y-m-d)"));
        }

        $btimestamp = strtotime($beginTime);
        $etimestamp = strtotime($endTime);
        if($btimestamp >= $etimestamp){
            $this->addError($attribute, Yii::t("labels", "Begin Date too big."));
        }
        if($etimestamp-$btimestamp >= 31536000){
            $this->addError($attribute, Yii::t("labels", "End Date too big."));
        }
    }

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'semesters'=>array(self::HAS_MANY, 'CalendarSemester', 'yid','order'=>'semester_flag ASC'),
			'currentClasses'=>array(self::HAS_MANY, 'IvyClass', array('yid'=>'yid'), 'condition'=>'currentClasses.schoolid=:schoolid', 'with'=>'children','order'=>'currentClasses.child_age ASC,currentClasses.title ASC'),
			'nextClasses'=>array(self::HAS_MANY, 'IvyClass', array('yid'=>'yid'), 'condition'=>'nextClasses.schoolid=:schoolid', 'with'=>'reserveChildren', 'together'=>false,'order'=>'nextClasses.child_age ASC,nextClasses.title ASC'),
            'campuses'=>array(self::HAS_MANY, 'CalendarSchool', array('yid'=>'yid'))
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'yid' => 'Yid',
			'title' => Yii::t('labels', 'Calendar Title'),
			'startyear' => Yii::t('labels', 'School Year'),
			'created_timestamp' => 'Created Timestamp',
			'userid' => 'Userid',
			'stat' => 'Stat',
			'semester' => 'Semester',
			'updated_timestamp' => 'Updated Timestamp',
            'firstSemester' => Yii::t('labels', '1st Semster'),
            'secondSemester' => Yii::t('labels', '2nd Semster'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('yid',$this->yid);
		$criteria->compare('title',$this->title,true);
		$criteria->compare('startyear',$this->startyear,true);
		$criteria->compare('created_timestamp',$this->created_timestamp);
		$criteria->compare('userid',$this->userid);
		$criteria->compare('stat',$this->stat);
		$criteria->compare('semester',$this->semester,true);
		$criteria->compare('updated_timestamp',$this->updated_timestamp);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * 取校历信息
	 * @param string $calendarId
	 * @param array|string $param
	 * @return array|string|obj
	 * <AUTHOR>
	 */
	public function getCalendarInfo($calendarId,$param = array())
	{
		if ($calendarId)
		{
			$obj = $this->findByPk($calendarId);
			if (is_array($param) && count($param))
			{
				return $obj->getAttributes($param);
			}
			elseif (is_string($param) && strlen($param))
			{
				return $obj->getAttribute($param);
			}
			else
			{
				return $obj;
			}
		}
		return null;
	}

    public function getUpdownYear($yid)
    {
        if ($yid){
            $model = $this->findByPk($yid);
            if ($model !== null){
                $downYear = $model->startyear-1;
                $upYear = $model->startyear+1;
                return array(
                    $downYear => $downYear . ' ~ ' . ($downYear+1),
                    $model->startyear => $model->startyear . ' ~ ' . ($model->startyear+1),
                    $upYear => $upYear . ' ~ ' . ($upYear+1),
                );
            }
        }
        return array();
    }
}