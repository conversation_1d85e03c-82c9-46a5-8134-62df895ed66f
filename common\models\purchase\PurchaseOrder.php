<?php

/**
 * This is the model class for table "ivy_purchase_order".
 *
 * The followings are the available columns in table 'ivy_purchase_order':
 * @property integer $id
 * @property string $title
 * @property integer $status
 * @property string $schoolid
 * @property integer $vendorid
 * @property integer $delivery_timestamp
 * @property integer $delivery_place
 * @property string $pay_type
 * @property string $billing_type
 * @property string $invoice_title
 * @property string $quality_aftersale
 * @property string $content
 * @property integer $add_user
 * @property integer $add_timestamp
 * @property integer $total_price
 */
class PurchaseOrder extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_purchase_order';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('title, status, schoolid, vendorid, delivery_timestamp, delivery_place, pay_type, billing_type, invoice_title, quality_aftersale, content, add_user, add_timestamp, total_price, school_name, aid, pid', 'required'),
			array('status, vendorid, delivery_timestamp, add_user, add_timestamp, aid, is_diff', 'numerical', 'integerOnly'=>true),
			array('title, schoolid, pay_type, invoice_title, quality_aftersale, content, delivery_place, pid, school_name, different', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, title, status, schoolid, vendorid, delivery_timestamp, delivery_place, pay_type, billing_type, invoice_title, quality_aftersale, content, add_user, add_timestamp, total_price, school_name, aid, pid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'item'=>array(self::HAS_MANY ,'PurchaseOrderItem', 'oid'),
			'application'=>array(self::HAS_ONE ,'PurchaseApplication', array('id'=>'aid')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'title' => '订单标题',
			'status' => '订单状态',
			'schoolid' => '学校ID',
			'vendorid' => '供应方',
			'delivery_timestamp' => '交货日期',
			'delivery_place' => '交货地点',
			'pay_type' => '付款方式',
			'billing_type' => '结算方式',
			'invoice_title' => '发票抬头',
			'quality_aftersale' => '质量及售后',
			'content' => '联络信息',
			'add_user' => '添加人',
			'add_timestamp' => '添加时间',
			'total_price' => '订单总价',
			'school_name' => '订购校园',
			'aid' => '采购申请ID',
			'pid' => '采购单ID',
			'is_diff' => '到货时是否存在差异',
			'different' => '差异详情',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('title',$this->title,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('vendorid',$this->vendorid);
		$criteria->compare('delivery_timestamp',$this->delivery_timestamp);
		$criteria->compare('delivery_place',$this->delivery_place);
		$criteria->compare('pay_type',$this->pay_type,true);
		$criteria->compare('billing_type',$this->billing_type,true);
		$criteria->compare('invoice_title',$this->invoice_title,true);
		$criteria->compare('quality_aftersale',$this->quality_aftersale,true);
		$criteria->compare('content',$this->content,true);
		$criteria->compare('add_user',$this->add_user);
		$criteria->compare('add_timestamp',$this->add_timestamp);
		$criteria->compare('total_price',$this->total_price);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return PurchaseOrder the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	protected function afterDelete()
	{
		//更新标配表，删除purchaseOrderItem相关的记录
		foreach ($this->item as $orderItem) {
			$orderItem->delete();
		}

		parent::afterDelete();
	}
}
