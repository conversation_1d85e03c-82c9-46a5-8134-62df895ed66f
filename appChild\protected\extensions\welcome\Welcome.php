<?php

class Welcome extends CWidget {

    public $schoolId;
    public $yId;
    public $weekNum;
    public $cssFile;
    public $assetsUrl;

    public function init() {
        Yii::import('ext.schoolnews.models.*');

//        if($this->assetsUrl===null)
//			$this->assetsUrl=Yii::app()->getAssetManager()->publish(dirname(__FILE__) . '/assets', false, -1, YII_DEBUG);

        $cs = Yii::app()->clientScript;
        if (false !== $this->cssFile) {
            if (null === $this->cssFile)
            //$this->cssFile = $this->assetsUrl . '/css.css';
                $this->cssFile = Yii::app()->theme->baseUrl . '/css/portfolio.css';
            $cs->registerCssFile($this->cssFile);
        }
    }

    public function run() {
        $SNotesModel = array();
        if ($this->schoolId) {
            Mims::LoadHelper('HtoolKits');
            Yii::import('common.models.attendance.*');
            $criteria = new CDbCriteria();
            $criteria->condition = "t.en_content <> ''";
            $criteria1 = new CDbCriteria();
            $criteria1->condition = "t.en_important <> ''";
            $criteria->mergeWith($criteria1, false);
            $criteria->compare('schoolid', $this->schoolId);
            if ($this->weekNum && $this->yId) {
                $criteria->compare('t.yid', $this->yId);
                $criteria->compare('t.weeknumber', $this->weekNum);
            } else {
                $criteria->order = 't.updated_timestamp desc';
                $criteria->limit = 1;
            }
            $criteria->compare('t.stat', 20);
            $criteria->compare('t.type', 10);
            if ($this->weekNum && $this->yId) {
                $SNotesModel = NotesSchCla::model()->find($criteria);
            } else {
                $SNotesModel = NotesSchCla::model()->find($criteria);
                if ($SNotesModel) {
                    $weekInfoModel = CalendarWeek::model()->findByAttributes(array('weeknumber' => $SNotesModel->weeknumber, 'yid' => $SNotesModel->yid));
                    $SNotesModel->weekInfo = $weekInfoModel;
                }
            }
            
            $criteria=new CDbCriteria;
            $criteria->compare('t.childid', $this->getController()->getChildId());
            $criteria->compare('t.yid', $this->yId);
            $criteria->compare('t.stat', 20);
            $criteria->order='t.weeknumber desc';
            $criteria->limit=5;
            $recentJ=NotesChild::model()->with('weekInfo', 'yInfo')->findAll($criteria);

            $criteria=new CDbCriteria;
            $criteria->compare('t.schoolid', $this->schoolId);
            $criteria->compare('t.is_home', 1);
            $criteria->compare('t.published', 1);
            $criteria->order='t.times desc';
            $criteria->limit=8;
            $informationJ = InformationDs::model()->findAll($criteria);
        }

        $this->render('welcome', array('SNotesModel' => $SNotesModel, 'weeknum' => $this->weekNum, 'yid' => $this->yId, 'recentj'=>$recentJ, 'informationJ' => $informationJ));
    }

}