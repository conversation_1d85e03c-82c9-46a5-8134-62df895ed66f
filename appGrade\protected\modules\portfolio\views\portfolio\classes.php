<?php
$this->breadcrumbs=array(
	'Portfolio'=>array('/portfolio/portfolio/classes'),
	Yii::t("navigations",'Classes'),
);

$is_iPad = (bool) strpos($_SERVER['HTTP_USER_AGENT'],'iPad');

$colorbox = $this->widget('application.extensions.colorbox.JColorBox');
$colorbox->addInstance('.colorbox', array('iframe'=>false, 'width'=>'800', 'height'=>'80%'));
$colorbox->addInstance('.colorboxpc', array('iframe'=>false, 'width'=>'930', 'height'=>'80%'));// parent contact

if (!$is_iPad){
    $colorbox->addInstance('.colorbox_s', array('iframe'=>true, 'width'=>'1030', 'height'=>'80%'));
    $colorbox->addInstance('.colorbox_l', array('iframe'=>true, 'width'=>'930', 'height'=>'60%'));
}
?>


<div id="left-col" class="no-bg span-6">
	&nbsp;
</div>

<div id="main-col" class="span-18 last">
	
	<?php $i=0;?>
		<?php foreach($classList->getData() as $data):?>

			<?php
				if ($classShow && $classShow['status'] == 0) {
					continue;
				}
				// $lastStr = substr(trim($data->title), -1);
				// if (!preg_match("/[A-Z]/", $lastStr)) {
				// 	continue;
				// }
			?>
		
		<div class="class-box">
			<h1><label>
				<ul class="sub-nav">
					<li class="active">
					<a href="#anchor_<?php echo $data->classid;?>" name="anchor_<?php echo $data->classid;?>"><?php echo $data->title?></a>
					</li>
				</ul>
				</label>
				
				<span><a href="#"><?php echo $data->schoolInfo->title?></a></span>
			</h1>
			<div class="span-8">
				<ul class="pfolio-list-i">
					<li class="calendar">
                        <a href="<?php echo Yii::app()->getController()->createUrl("//portfolio/portfolio/calendarview", array('yid'=>$data->yid,'schoolid'=>$data->schoolid))?>" class="colorbox">
                            <em></em><div><p><?php echo Yii::t("portfolio",'School Calendar');?></p></div>
                        </a>
					</li>
                    <?php if(Yii::app()->params['ownerConfig']['key'] != $data->schoolid):?>
					<li class="schedule">
                        <a href="<?php echo Yii::app()->getController()->createUrl("//portfolio/portfolio/schedule", array('classid'=>$data->classid))?>" class="colorbox_s" <?php if ($is_iPad):?>target="_blank"<?php endif;?>>
                            <em></em><div><p><?php echo Yii::t("portfolio",'Weekly Schedule');?></p></div>
                        </a>
					</li>
                    <?php endif;?>
					<li class="lunch">
						<a href="<?php echo Yii::app()->getController()->createUrl("//portfolio/portfolio/lunch", array('schoolid'=>$data->schoolid,'yid'=>$data->yid))?>" class="colorbox_l" <?php if ($is_iPad):?>target="_blank"<?php endif;?>>
                            <em></em><div><p><?php echo Yii::t("portfolio",'Lunch Menu');?></p></div>
                        </a>
					</li>
					<li class="teacher">
						<a href="<?php echo Yii::app()->getController()->createUrl("//portfolio/portfolio/teachers", array('classid'=>$data->classid))?>" class="colorbox">
                            <em></em><div><p><?php echo Yii::t("portfolio",'Class Teachers');?></p></div>
                        </a>
					</li>
					<li class="contact">
						<a href="<?php echo Yii::app()->getController()->createUrl("//portfolio/portfolio/contact", array('classid'=>$data->classid))?>" class="colorboxpc" id="contact_class_<?php echo $data->classid?>">
                            <em></em><div><p><?php echo Yii::t("portfolio",'Family Directory');?></p></div>
                        </a>
					</li>
                    <?php if(CommonUtils::isGradeSchool($data->classid)):?>
					<li class="schedule">
                        <a href="<?php echo Yii::app()->getController()->createUrl("//portfolio/portfolio/getSchedule", array('classid'=>$data->classid,'yid'=>$data->yid))?>" class="colorbox_s" <?php if ($is_iPad):?>target="_blank"<?php endif;?>>
                            <em></em><div><p><?php echo Yii::t("portfolio",'Class Schedule');?></p></div>
                        </a>
					</li>
                    <?php endif;?>
				</ul>
			</div>
			<div class="span-10 last">
				<ul class="class-attr">
					<li>
						<label><?php echo Yii::t("portfolio","School Year");?></label><span><?php echo IvyClass::formatSchoolYear($data->calendarInfo->startyear)?></span>
					</li>
					<li>
                        <label><?php echo Yii::t("portfolio","Boys vs Girls");?></label><span><?php echo $data->child_boys;?>:<?php echo $data->child_girls;?></span>
					</li>
					<li>
						<label><?php echo Yii::t("portfolio","Schools Hours");?></label><span><?php echo IvyClass::formatPeriodtime($data->periodtime)?></span>
					</li>
				</ul>
				<div class="clear"></div>
				<ul class="pfolio-list-b">
					<li class="journal">
						<a href="<?php echo Yii::app()->getController()->createUrl("//portfolio/portfolio/journal", array('startyear'=>$data->calendarInfo->startyear))?>">
						<em></em><p><?php echo Yii::t("navigations",'Weekly Journal');?></p>
						</a>
					</li>
					<li class="semester">
						<a href="<?php echo Yii::app()->getController()->createUrl("//portfolio/portfolio/semester", array('classid'=>$data->classid))?>">
						<em></em><p><?php echo Yii::t("navigations",'Semester Report');?></p></a></li>
				</ul>					
			</div>
			<div class="clear"></div>

			
		
		</div>
		<?php $i++;?>
		<?php endforeach; ?>
	<?php if($i==0):?>
	<p>
		<?php echo Yii::t("portfolio","No class assigned.");?>
	</p>
	<?php endif;?>
	
</div>

<?php
$this->widget('ext.guide.Guide', array(
    'options'=>array(
        'guides'=>array(
			//欢迎页
            0=>array(
                'selector'=>'#pfolio-sub-nav',
                'caption'=>'portfolio_classes',
				'style'=>array(
					'arrow'=>array(
						'direction'=>'up',
						'top'=>0,
						'left'=>0,
					),
					'mainbox'=>array(
						'leftOffset'=>100, //guide窗口与主窗口(.page) 左侧的距离
						'top'=>50,
					),				
				),
				'next'=>10,
            ),
			//导航
            10=>array(
                'selector'=>'#navigation',
                'caption'=>'navigations',
				'style'=>array(
					'arrow'=>array(
						'direction'=>'up',
						'top'=>0,
						'left'=>70,
					),
					'mainbox'=>array(
						'leftOffset'=>100, 
						'top'=>50,
					),				
				),
				//'next'=>20,
            ),			
			/*
            10=>array(
                'selector'=>'#pfolio-sub-nav ul li:eq(2)',
                'caption'=>'portfolio_classes',
				'style'=>array(
					'arrow'=>array(
						'direction'=>'up',
						'top'=>0,
						'left'=>0,
					),
					'mainbox'=>array(
						'leftOffset'=>100, //guide窗口与主窗口(.page) 左侧的距离
						'top'=>50,
					),				
				),
				'next'=>20,
            ),
            20=>array(
                'selector'=>'#pfolio-sub-nav ul li:eq(3)',
                'caption'=>'portfolio_classes',
				'style'=>array(
					'arrow'=>array(
						'direction'=>'up',
						'top'=>0,
						'left'=>0,
					),
					'mainbox'=>array(
						'leftOffset'=>100, //guide窗口与主窗口(.page) 左侧的距离
						'top'=>50,
					),				
				),
				'next'=>30,
            ),
            30=>array(
                'selector'=>'#pfolio-sub-nav ul li:eq(4)',
                'caption'=>'portfolio_classes',
				'style'=>array(
					'arrow'=>array(
						'direction'=>'up',
						'top'=>0,
						'left'=>0,
					),
					'mainbox'=>array(
						'leftOffset'=>100, //guide窗口与主窗口(.page) 左侧的距离
						'top'=>50,
					),				
				),
				'next'=>50,
            ),
			//导航
            50=>array(
                'selector'=>'#maintop',
                'caption'=>'navigations',
				'style'=>array(
					'arrow'=>array(
						'direction'=>'up',
						'top'=>0,
						'left'=>70,
					),
					'mainbox'=>array(
						'leftOffset'=>100, 
						'top'=>50,
					),				
				),
				'next'=>60,
            ),
            60=>array(
                'selector'=>'#main-col div.class-box:eq(0)',
                'caption'=>'portfolio_class_item',
				'style'=>array(
					'arrow'=>array(
						'direction'=>'up',
						'top'=>0,
						'left'=>0,
					),
					'mainbox'=>array(
						'leftOffset'=>100, //guide窗口与主窗口(.page) 左侧的距离
						'top'=>50,
					),				
				),
				'next'=>50,
            ),
			*/
			
        ),
    )
));
?>
<script type="text/javascript">
    function changeContactClass(_this){
        $('#contact_class_'+$(_this).val()).click();
    }
</script>