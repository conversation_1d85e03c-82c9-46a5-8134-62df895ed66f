<script>
$('table span.charts').bind('dblclick',function(){
	var subcode=$(this).parent().attr('subcode');
	var month=$(this).parent().parent().attr('month');
	var code=$(this).parent().parent().parent().parent().attr('code');
	var id='charts-'+month+'-'+subcode;
	var oldData =$(this).html();
    if (oldData.indexOf("input")>=0){
        return;
    }
	$(this).html("<input class='input' id='"+id+"' type='text' size='14' value="+$(this).html()+">");
	$("#"+id).focus().blur(function(){
		if(parseInt(oldData) == parseInt($("#"+id).val())){
			$("#"+id).parent().html(oldData);
		}else{
			$.ajax({
				type: "POST",   
				url: "<?php echo $this->createUrl('//operations/charts/update');?>",
				data: "code="+code+"&period="+month+"&subcode="+subcode+"&data="+$("#"+id).val(),
				dataType: "json",
				success: function(data){
					if(data.state == 'success'){
                        var tddom = $("#"+id).parents('td');
						$("#"+id).parent().html($("#"+id).val());
                        if (data.data.code == 11 && !tddom.find('div.itemOp').length)
                            tddom.append('<div class="itemOp"></div>').addClass('memo').attr('bid', data.data.id);
                        tdhover();
					}else{
						alert(data.message);
						$("#"+id).parent().html(oldData);
					}
				}   
			});		
		}
	});
	$("#"+id).keydown(function(e){
		if(e.keyCode==13){
		   $(this).blur();
		}
	});
});

function tdhover()
{
    $('table td.memo').hover(function(){
        if (!$(this).find('div.itemOp a').length)
            $(this).find('div.itemOp').append( $('#itemOp-template').html() );
    },function(){
        $(this).find('div.itemOp a').remove();
    });
}
tdhover();
head.use('dialog', 'draggable', function() {
    region_pop = $('#J_dialog_memo');
    region_pop.draggable( { handle : '.pop_top'} );
    $('#J_dialog_memo_pop_x,#J_dialog_close').on('click', function(e){
		e.preventDefault();
		region_pop.hide();
	});
});

var jsMemo = <?php echo $jsMemo;?>;
function openWind(e){
    var dom = $(e).parents('table td.memo');
    var bid = $(dom).attr('bid');
    $('#id').val(bid);
    $('#memo').val(jsMemo[bid]);
    openQuickAssignWindow();
}

function openQuickAssignWindow(){
	$('#J_dialog_memo').show().css({
		left : ($(window).width() - region_pop.outerWidth())/2,
		top : ($(window).height() - region_pop.outerHeight())/2// + $(document).scrollTop()
	});
}

$('td.memo').each(function(){
    var ctmp = jsMemo[$(this).attr('bid')] ? jsMemo[$(this).attr('bid')] : '';
    $(this).append('<div class="itemOp">'+ctmp+'</div>');
});
    function cb(data)
    {
        $('td.memo[bid='+data.id+'] div.itemOp').html(data.memo);
        jsMemo[data.id]=data.memo;
        $('#J_dialog_close').click();
    }
</script>