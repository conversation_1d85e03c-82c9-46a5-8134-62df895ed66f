<?php

/**
 * This is the model class for table "ivy_as_leave_extension_history".
 *
 * The followings are the available columns in table 'ivy_as_leave_extension_history':
 * @property integer $id
 * @property integer $leave_id
 * @property integer $extension_date
 * @property string $desc
 * @property integer $creator
 * @property integer $created
 */
class AsLeaveExtensionHistory extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return AsLeaveExtensionHistory the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_as_leave_extension_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('leave_id, extension_date, creator, created', 'required'),
			array('leave_id, extension_date, creator, created', 'numerical', 'integerOnly'=>true),
			array('desc', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, leave_id, extension_date, desc, creator, created', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'leave_id' => 'Leave',
			'extension_date' => 'Extension Date',
			'desc' => 'Desc',
			'creator' => 'Creator',
			'created' => 'Created',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('leave_id',$this->leave_id);
		$criteria->compare('extension_date',$this->extension_date);
		$criteria->compare('desc',$this->desc,true);
		$criteria->compare('creator',$this->creator);
		$criteria->compare('created',$this->created);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}