<?php
$ages = array('全部年龄') + $cfgs['ages'];
switch ($this->branchId) {
    case 'BJ_SLT':
        $grade = array('全部班级') + $cfg['grade_slt']; 
        break;
    case 'BJ_DS':
        $grade = array('全部班级') + $cfg['grade'];
        break;
    default:
        $grade = array('全部班级') + $cfg['grade_ayalt'];
        break;
}
$classlist = AdmissionsDs::getConfig();

if($this->branchId == "BJ_IASLT"){
    $class = $classlist['grade_ayalt'];
}else{
    $class = $classlist['grade'];
}

?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Basic'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Admissions management'), array('//mcampus/admissions/index')) ?></li>
        <li class="active">入学申请</li>
    </ol>

    <div class="row">
        <!-- 左侧菜单 -->
        <div class="col-md-2 col-sm-2 mb10">
            <?php
            $this->widget('zii.widgets.CMenu',array(
                'items'=> $this->getMenu(),
                'id'=>'pageCategory',
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked background-gray'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>

        <div class="col-md-10 col-sm-12">
            <div class="mb10 row">
                <!-- 搜索框 -->
                <form  class="" style="float: left;width: 100%" action="<?php echo $this->createUrl('admissions'); ?>" method="get">
                    <?php echo Chtml::hiddenField('branchId',$this->branchId); ?>
                        <!-- 状态 -->
                        <div class="col-sm-2 form-group">
                            <input type="text" class="form-control" name="username" placeholder="孩子姓名" value="<?php echo Yii::app()->request->getParam('username','')?Yii::app()->request->getParam('username',''):''; ?>">
                        </div>
                        <!-- 模式 -->
                        <div class="col-sm-2 form-group">
                            <input type="text" class="form-control" name="phone" placeholder="家长联系方式" value="<?php echo Yii::app()->request->getParam('phone','')?Yii::app()->request->getParam('phone',''):''; ?>">
                        </div>
                        <!-- 班级 -->
                        <div class="col-sm-2 form-group">
                            <?php echo Chtml::dropDownList('grade',$_GET['grade'],$grade,array('class'=>'form-control')); ?>
                        </div>
                        <!-- 状态 -->
                        <div class="col-sm-2 form-group">
                            <?php echo Chtml::dropDownList('status',$_GET['status'],$cfg['states'],array('class'=>'form-control',  'empty' => Yii::t('teaching', '全部状态'))); ?>
                        </div>
                        <!-- 入学时间 -->
                        <div class="col-sm-2" style="float: left;">
                            <?php echo Chtml::dropDownList('startYear',$_GET['startYear'], $calender, array('class'=>'form-control', 'empty' => Yii::t('teaching', '入学学年'))); ?>
                        </div>
                        <!-- 内容匹配 -->
                        <div class="">
                            <button class="btn btn-default ml5" type="submit"><span class="glyphicon glyphicon-search"> </span> </button>
                            <?php if($_GET['grade'] || $_GET['status'] || $_GET['startYear'] || $_GET['username'] || $_GET['phone'] ): ?>
                                <a href="javascript:void(0);" onclick="exportParent()" class="btn btn-info" target="_blank"><?php echo Yii::t('user', 'Export');?></a>
                            <?php endif; ?>
                        </div>
                </form>
            </div>

            <div class="col-md-12"></div>
            <div class="panel panel-default">
                <div class="panel-body">
                    <?php
                    $this->widget('ext.ivyCGridView.BsCGridView', array(
                        'id'=>'confirmed-visit-grid',
                        'afterAjaxUpdate'=>'js:head.Util.modal',
                        'dataProvider'=>$dataProvider,
                        'template'=>"{items}{pager}{summary}",
                        'colgroups'=>array(
                            array(
                                "colwidth"=>array(100,100,100,100,100,100,100,100,200),
                            )
                        ),
                        'columns'=>array(
                            array(
                                'name'=> Yii::t('user','中文姓名'),
                                'type'=>'raw',
                                'value'=>'$data->cn_name . " " . $data->cn_name_last',
                            ),
                            array(
                                'name'=> Yii::t('user','英文姓名'),
                                'type'=>'raw',
                                'value'=>'$data->en_name . " " . $data->en_name_middle . " " . $data->en_name_last',
                            ),
                            array(
                                'name'=>'birthday',
                                'type'=>'raw',
                                //'value'=>'CommonUtils::getAge($data->birthday)',
                                'value'=> array($this, 'getBirthday'),
                            ),
                            array(
                                'name'=>'status',
                                'type'=>'raw',
                                'value'=>'$data->getCatestates("states")',
                            ),
                            'remark',
                            array(
                                'name'=>'start_year',
                                'type'=>'raw',
                                'value' => array($this, "getStartDate"),
                                //'value'=>'date("Y-m-d", $data->start_date)',
                            ),
                            array(
                                'name'=>'start_grade',
                                'type'=>'raw',
                                'value'=> in_array($this->branchId, array("BJ_SLT", "BJ_DS")) ? '$data->getCatename("grade")' : '$data->getCatename("grade_ayalt")',
                            ),
                            array(
                                'name'=>'add_timestamp',
                                'type'=>'raw',
                                'value'=>'date("Y-m-d H:i", $data->add_timestamp)',
                            ),

                            array(
                                'name'=>'操作',
                                'value'=> array($this, 'getButton'),
                            ),
                        ),
                    ));
                    ?>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    $('#datepicker').datepicker({
        changeMonth: true,
        changeYear: true,
        dateFormat:'yy-mm-dd'
    });
    var modal = '<div class="modal fade" id="modal" class="1123" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog  modal-lg" role="document"><div class="modal-content"></div></div></div>';
    $('body').append(modal);
    
    function cbVisitsasdasd() {
        location=location;
        /*$.fn.yiiGridView.update('confirmed-visit-grid');
        head.Util.ajaxDel();*/
    }
    
    function exportParent() {
        var childName = '<?php echo Yii::app()->request->getParam('username','') ?>';
        var phone = '<?php echo Yii::app()->request->getParam('phone','') ?>';
        var grade = '<?php echo Yii::app()->request->getParam('grade','') ?>';
        var status = '<?php echo Yii::app()->request->getParam('status','') ?>';
        var startYear = '<?php echo Yii::app()->request->getParam('startYear','') ?>';
        var url = '<?php echo $this->createUrl('exportAdmissions') ?>';
        $.ajax({
            url: url,
            type: 'POST',
            data: {childName:childName,phone:phone,grade:grade,status:status,startYear:startYear},
            success: function (res) {
                if (res.state == 'success') {
                    var data = res.data.items;
                    const filename = res.data.title;
                    const ws_name = "SheetJS";

                    const worksheet = XLSX.utils.aoa_to_sheet(data);
                    const workbook = XLSX.utils.book_new();
                    XLSX.utils.book_append_sheet(workbook, worksheet, ws_name);
                    // generate Blob
                    const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                    const blob = new Blob([wbout], {type: 'application/octet-stream'});
                    // save file
                    let link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = filename;
                    link.click();
                    setTimeout(function() {
                        // 延时释放掉obj
                        URL.revokeObjectURL(link.href);
                        link.remove();
                    }, 500);
                }
            },
            dataType: 'json'
        });
    }
</script>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>