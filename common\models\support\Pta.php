<?php

/**
 * This is the model class for table "ivy_pta".
 *
 * The followings are the available columns in table 'ivy_pta':
 * @property integer $id
 * @property string $schoolid
 * @property integer $childid
 * @property integer $parent_id
 * @property string $desc
 * @property integer $status
 * @property integer $update_user
 * @property integer $create_timestamp
 * @property integer $update_timestamp
 */
class Pta extends CActiveRecord {

    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return Pta the static model class
     */
    public static function model($className = __CLASS__) {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName() {
        return 'ivy_pta';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules() {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('schoolid', 'required'),
            array('childid, parent_id, status, update_user, create_timestamp, update_timestamp', 'numerical', 'integerOnly' => true),
            array('schoolid', 'length', 'max' => 25),
            array('desc', 'safe'),
            // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array('id, schoolid, childid, parent_id, desc, status, update_user, create_timestamp, update_timestamp', 'safe', 'on' => 'search'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations() {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
            'classLink' => array(self::BELONGS_TO, 'ChildClassLink', 'childid','alias'=>'classLink')
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels() {
        return array(
            'id' => 'ID',
            'schoolid' => 'Schoolid',
            'childid' => 'Childid',
            'parent_id' => 'Parent',
            'desc' => 'Desc',
            'status' => 'Status',
            'update_user' => 'Update User',
            'create_timestamp' => 'Create Timestamp',
            'update_timestamp' => 'Update Timestamp',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search() {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('id', $this->id);
        $criteria->compare('schoolid', $this->schoolid, true);
        $criteria->compare('childid', $this->childid);
        $criteria->compare('parent_id', $this->parent_id);
        $criteria->compare('desc', $this->desc, true);
        $criteria->compare('status', $this->status);
        $criteria->compare('update_user', $this->update_user);
        $criteria->compare('create_timestamp', $this->create_timestamp);
        $criteria->compare('update_timestamp', $this->update_timestamp);

        return new CActiveDataProvider($this, array(
                    'criteria' => $criteria,
                ));
    }

}