const animateCSS = (element, animation, prefix = 'animate__') =>
    // We create a Promise and return it
    new Promise((resolve, reject) => {
        const animationName = `${prefix}${animation}`;
        const node = document.querySelector(element);

        node.classList.add(`${prefix}animated`, animationName);

        // When the animation ends, we clean the classes and resolve the Promise
        function handleAnimationEnd(event) {
            event.stopPropagation();
            node.classList.remove(`${prefix}animated`, animationName);
            resolve('Animation ended');
        }

        node.addEventListener('animationend', handleAnimationEnd, {once: true});
    });

const addCSS = (element, css) =>
    new Promise((resolve, reject) => {
        $(element).addClass(css)
        resolve()
    });

const removeCSS = (element, css) =>
    new Promise((resolve, reject) => {
        $(element).removeClass(css)
        resolve()
    });

$(document).ready(function(){
    $('.si-logo span').click(() => {
        si_show()
    })

    let width=$(".si-logo").width()
    let out=width-20
    let backIn=width-40
    var tt=document.styleSheets[0];
    tt.insertRule("@keyframes backOutRights { 0% {-webkit-transform: scale(1);transform: scale(1);opacity:1}20% {-webkit-transform: translateX(0) scale(.7);transform: translateX(0) scale(.7); opacity: .7 }to {-webkit-transform: translateX("+out+"px) scale(1); transform: translateX("+out+"px) scale(1);opacity:1}",2);//写入样式\
    
    tt.insertRule("@-webkit-keyframes backOutRights { 0% {-webkit-transform: scale(1);transform: scale(1);opacity:1}20% {-webkit-transform: translateX(0) scale(.7);transform: translateX(0) scale(.7); opacity: .7 }to {-webkit-transform: translateX("+out+"px) scale(1); transform: translateX("+out+"px) scale(1);opacity:1}",3);//写入样式\
    
    tt.insertRule("@keyframes backInRights {  0% {-webkit-transform: translateX("+backIn+"px) scale(.7);transform: translateX("+backIn+"px) scale(.7);opacity: .7} 80% {-webkit-transform: translateX(0) scale(.7);transform: translateX(0) scale(.7);; opacity: .7}to {-webkit-transform: scale(1);transform: scale(1);opacity: 1} }",0);//写入样式
    
    tt.insertRule("@-webkit-keyframes backInRights {  0% {-webkit-transform: translateX("+backIn+"px) scale(.7);transform: translateX("+backIn+"px) scale(.7);opacity: .7} 80% {-webkit-transform: translateX(0) scale(.7);transform: translateX(0) scale(.7);; opacity: .7}to {-webkit-transform: scale(1);transform: scale(1);opacity: 1} }",1);//写入样式
});
    
    var openSI = 0
    function si_show() {
        if (openSI == 0) {
            animateCSS('.animate', 'backInRights').then(() => {
                openSI = 1
                $('.si-tips').removeClass('hide')
                $(".si-logo").css("right","calc(50% - 20px)");
                animateCSS('.si-tips', 'fadeIn').then(() => {
                    $(".animate").css("right","calc(50% - 20px)");
                    // $('.si-logo').addClass('si-logo-off');
                })
            })
        }
        else {
            animateCSS('.si-tips', 'fadeOut').then(() => {
                $('.si-tips').addClass('hide')
                animateCSS('.animate', 'backOutRights').then(() => {
                    openSI = 0
                    $(".animate").css("right","calc(50% - 20px)");
                    $(".si-logo").css("right","0px");
                })
            })
        }
    }
document.addEventListener('keydown', function(event) {
    var keyCode = event.which || event.keyCode;
    if (keyCode == 27 && openSI == 1) {
        si_show()
    }
})