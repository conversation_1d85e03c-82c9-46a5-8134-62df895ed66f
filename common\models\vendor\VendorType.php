<?php

/**
 * This is the model class for table "ivy_vendor_type".
 *
 * The followings are the available columns in table 'ivy_vendor_type':
 * @property integer $vendor_type_id
 * @property string $sign
 * @property string $cn_title
 * @property string $en_title
 * @property string $typefield_arr
 */
class VendorType extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_vendor_type';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('sign', 'required'),
			array('sign, cn_title, en_title', 'length', 'max'=>150),
			array('typefield_arr', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('vendor_type_id, sign, cn_title, en_title, typefield_arr', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'vendor_type_id' => 'Vendor Type',
			'sign' => 'Sign',
			'cn_title' => 'Cn Title',
			'en_title' => 'En Title',
			'typefield_arr' => 'Typefield Arr',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('vendor_type_id',$this->vendor_type_id);
		$criteria->compare('sign',$this->sign,true);
		$criteria->compare('cn_title',$this->cn_title,true);
		$criteria->compare('en_title',$this->en_title,true);
		$criteria->compare('typefield_arr',$this->typefield_arr,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return VendorType the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public function getTitle(){
        switch (Yii::app()->language){
            case "zh_cn":
                return ($this->cn_title) ? $this->cn_title : $this->en_title ;
                break;
            case "en_us":
                return ($this->en_title) ? $this->en_title : $this->cn_title ;
                break;
        }
    }
}
