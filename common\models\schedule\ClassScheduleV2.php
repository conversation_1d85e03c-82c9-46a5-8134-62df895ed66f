<?php

/**
 * This is the model class for table "ivy_class_schedule_v2".
 *
 * The followings are the available columns in table 'ivy_class_schedule_v2':
 * @property integer $id
 * @property integer $classid
 * @property integer $yid
 * @property integer $weeknumber
 * @property string $activitys
 * @property integer $timestamp
 * @property integer $uid
 */
class ClassScheduleV2 extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ClassScheduleV2 the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_class_schedule_v2';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('classid, yid, weeknumber, timestamp, uid', 'required'),
			array('classid, yid, weeknumber, timestamp, uid', 'numerical', 'integerOnly'=>true),
			array('activitys', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, classid, yid, weeknumber, activitys, timestamp, uid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'data' => array(self::HAS_ONE, 'ClassScheduleData', 'id'),
            'classTitle'=>array(self::BELONGS_TO, 'IvyClass', 'classid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'classid' => 'Classid',
			'yid' => 'Yid',
			'weeknumber' => 'Weeknumber',
			'activitys' => 'Activitys',
			'timestamp' => 'Timestamp',
			'uid' => 'Uid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('weeknumber',$this->weeknumber);
		$criteria->compare('activitys',$this->activitys,true);
		$criteria->compare('timestamp',$this->timestamp);
		$criteria->compare('uid',$this->uid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}