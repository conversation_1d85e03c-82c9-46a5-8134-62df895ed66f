<style>
    [v-cloak] {
        display: none;
    }
    .panel-body .badge {
        position: absolute;
        right: 30px;
    }
    .loading{
        width:98%;
        height:100%;
        background:#fff;
        position: absolute;
        opacity: 0.5;
        z-index: 99
    }
    .loading span{
        width:100%;
        height:100%;
        display:block;
        background: url("<?php echo Yii::app()->theme->baseUrl?>/images/loading.gif")no-repeat center center ;
    }
    .badge {
        background-color: #F56B6C;
        color: #fff;
    }
    .comment-box {
        background-color: #fff;
        padding: 15px;
        border: 1px solid #dddddd;
        margin-bottom: 17px;
        margin-top: -17px;
    }
    .bg-info {
        background-color: #F7F7F8;
    }
    .active-1 {
        background-color: #FCF8E4 !important;
        border: 1px solid #ddd;
    }
    .name-2 {
        font-size: 14px;
        font-weight: 700;
        color: #333333;
        margin-bottom: 8px;
        line-height: 50px;
    }
    .content-2 {
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        margin-bottom: 10px;
    }
    .bg-hover:hover {
        background-color: #F7F7F8;
    }
    .badge.dot {
        width: 8px;
        height: 8px;
        border-radius: 4px;
        min-width: unset;
        padding: 0;
    }
    .allImage{
        width: 48px;
        height: 48px;
        object-fit: cover;
    }
    .cover{
        object-fit: cover;
    }
    .displayFlex{
        display:flex
    }
    .flexWidth{
        width:162px
    }
    .ellipsis{
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .badgeNum{
        display: inline-block;
        width: 6px;
        height: 6px;
        background: #d9534f;
        border-radius: 50%;
        margin-top: 6px;
    }
    .nav-tabs > li > a{
        border:none
    }
    .image{
        width: 48px; 
        height: 48px;
        object-fit:cover;
    }
    .content img{
        max-width:100%;
        margin-bottom: 1em; 
    }
    .gray{
        background:#777
    }
    .Unreplied{
        right:74px !important;
    }
    .borderTop{
        border-top:1px solid #ddd;
    }
    .borderBto{
        border-bottom:1px solid #ddd;
    }
    .maxHeight{
        max-height:500px;
        overflow-y:auto
    }
    .textareaCss{
        border: none;
        border-radius: 0;
        box-shadow: none;
    }
    .uploadIcon{
        background: #fff;
        font-size: 16px;
        padding: 10px 12px;
        color: #555555;
        margin-top:10px
    }
    .uploadImg{
        overflow-x: auto;
        white-space: nowrap;
        margin-top:10px
    }
    .fileImgs{
        width:72px;
        height:72px;
        object-fit: cover;
        display: inline-block;
    }
    .fileImg{
        width:72px;
        height:72px;
        object-fit: cover;
        display: inline-block;
        margin-bottom:10px;
        cursor: pointer;
    }
    .imgData{
        position: relative;
        display: inline-block;
    }
    .imgData span{    
        position: absolute;
        right: 3px;
        top: 3px;
        background: #333333;
        opacity: 1;
        color: #fff;
        width: 17px;
        height: 17px;
        border-radius: 50%;
        font-weight: 200;
        text-align: center;
        line-height: 15px;
        font-size: 19px;
    }
    .uploadFile {
        font-size:14px;
        padding:6px 12px;
        align-items: center;
    }
    .uploadFile .icon{
        display:none
    }
    .uploadFile:hover{
        background: #F7F7F8;

    }
    .uploadFile:hover .icon{
        display:block
    }
    .uploadFile a{
        color:#4D88D2
    }
    .fileLink{
        background: #F7F7F8;
        display: inline-block;
        border-radius: 4px;
        border: 1px solid #EBEDF0;
        padding: 6px 12px;
        font-size: 14px;
        margin-right: 16px;
        margin-bottom:8px;
        line-height:20px
    }
    .fileLink img{
        width:20px
    }
    .uploadLoading{
        width: 72px;
        height: 72px;
        background: #D9D9D9;
        border-radius: 4px;
        line-height: 72px;
        text-align: center;
    }
    .inputStyle{
        border: none;
        border-bottom: 1px solid #ccc;
        padding: 0;
        height: 25px;
        line-height: 25px;
        width: 100%;
    }
    .imgLi{
        list-style: none;
        padding-left: 0;
    }
    .imgLi li{
        display:inline-block
    }
    .img24{
        width: 24px;
        height: 24px;
        border-radius: 12px;
        border: 1px solid #FFFFFF;
        object-fit: cover;
    }
    .jointImg{
        margin-left:-5px
    }
    .absoluteFlex{
        position: absolute;
        top: 5px;
    }
    .contentAvatar{
        width: 44px;
        height: 44px;
        border-radius: 50%;
        object-fit:cover;
    }
    .bg7{
        background-color: #777777;
    }
    .wechatQrcode{
        display: inline-block;
        padding: 6px 10px;
        color: #50B674;
        background: #FFFFFF;
        border-radius: 4px;
        border: 1px solid #50B674;
        cursor: pointer;
    }
    .wechatQrcode img{
        width:16px;
        float:left;
        border-radius:50%
    }
    .qrcodeBox{
        position: absolute;
        left: 0px;
        top: 45px;
        width: 290px;
        padding:20px 10px;
        border: 1px solid #ddd;
        background: #fff;
        border-radius: 4px;
        text-align:center;
        font-size:14px;
        color:#333;
        box-shadow: 0px 2px 8px 0px rgb(0 0 0 / 20%);
        z-index:9
    }
    .qrcodeBox .qrcodeWechat{
        width:124px;
        height:124px;
        margin:0 auto
    }
    .wechatIcon{
        height:16px;
        position: absolute;
        bottom: 0;
        right: 0;
        width: 16px;
    }
    .avatarWechat{
        width:60px !important;
        height:60px  !important;
        border-radius:50%
    }
    .viewer-prev::before, .viewer-next::before{
        background-image: none !important;
    }
    video{
        max-height: 300px;
        max-width: 100%;
        width: auto !important;
        margin-bottom:8px
    }
</style>

<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Teaching Tasks'), array('//mteaching/default/index')) ?></li>
        <li><?php echo Yii::t('newDS', 'Notice') ?></li>
        <li><?php echo Yii::t("newDS", "Parent Feedback");?></li>
    </ol>
    <div class="row" id='container' v-cloak >
        <div class="col-md-2 col-sm-12">
            <div class="list-group">
                <a href="<?php echo $this->createUrl('index'); ?>" class="list-group-item status-filter"><?php echo Yii::t("newDS", "Notice List");?></a>
                <a href="<?php echo $this->createUrl('feedback'); ?>" class="list-group-item status-filter active"><span  v-if='toReplyNum>0' class='badgeNum pull-right'></span><?php echo Yii::t("newDS", "Parent Feedback");?></a></li>
            </div>
            <div class='wechatQrcode mr20 relative' @click.stop='showUnWechatQrcode' v-if='wechatData.state==0'>
                <div>
                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/wechatDM.png' ?>" alt="">
                    <span class='ml4'><?php echo Yii::t("directMessage", "Wechat Notification"); ?></span> 
                </div>
                <div class="qrcodeBox"  v-if='scanQrcodeBox'>
                    <div class='font14 color3'><strong><?php echo Yii::t("directMessage", "Wechat Notification"); ?></strong></div>
                    <div class='mt8 font12 color6'><?php echo Yii::t("directMessage", "Response parent feedback on wechat!"); ?></div>
                    <div>
                        <div id='wechatQrcodeBind' alt="" class='qrcodeWechat mt24'></div>
                        <div class='font12 mt16 color6 '><?php echo Yii::t("directMessage", "Wechat scan to bind your account."); ?></div>
                    </div>
                </div>
            </div>
            <div class='wechatQrcode mr20 relative' @click.stop='qrcodeBox=true'  v-if='wechatData.state==1'>
                <div>
                    <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/wechatDM.png' ?>" alt="" v-if='wechatData.headimgurl=="" || wechatData.headimgurl==null'>
                    <img :src="wechatData.headimgurl" alt="" v-if='wechatData.headimgurl!=""'>
                    <span class='ml4'><?php echo Yii::t("directMessage", "Wechat Enabled"); ?></span>  
                </div>
                <div  class="qrcodeBox"  v-if='qrcodeBox'>
                    <div class='font14 color3'><strong><?php echo Yii::t("directMessage", "Wechat Enabled"); ?></strong></div>
                    <div class='mt8 font12 color6'><?php echo Yii::t("directMessage", "Response parent feedback on wechat!"); ?></div>
                    <div class='relative inline-block' v-if='wechatData.headimgurl!=""'>
                        <img :src="wechatData.headimgurl" alt="" class='avatarWechat mt16'>
                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/wechatDM.png' ?>" alt="" class='wechatIcon'>
                    </div>
                    <div class='font12 mt16 color6 '  v-if='wechatData.nickname!=""'>{{wechatData.nickname}}</div>
                    <div class='font12 mt24 mt16 color6 '><button class="btn btn-default" type="button" @click.stop='unbind()'><?php echo Yii::t("directMessage", "Unbind your account"); ?></button></div>
                </div>
            </div>
        </div>
        <div class="col-md-10 col-sm-12">
            <div >
                <!-- Nav tabs -->
                <ul class="nav nav-tabs" role="tablist">
                    <li role="presentation" class="active"><a href="#unreply" aria-controls="unreply" role="tab" data-toggle="tab" @click="change('unreply')"><?php echo Yii::t("newDS", "My unreplied");?> <span class="badge" v-if='tabType=="unreply" &&  feedback.count &&  feedback.count!=0'>{{ replied }}</span></a></li>
                    <li role="presentation"><a href="#replied" aria-controls="replied" role="tab" data-toggle="tab" @click="change('replied')"><?php echo Yii::t("newDS", "My replied");?>  </a></li>
                    <!-- <li role="presentation"><a href="#all" aria-controls="all" role="tab" data-toggle="tab" @click="change('all')"><?php echo Yii::t("newDS", "All (Admin Only)");?></a></li>
                    <li role="presentation"><a href="#allUnreplied" aria-controls="allUnreplied" role="tab" data-toggle="tab" @click="change('allUnreplied')"><?php echo Yii::t("newDS", "Unreplied (Admin Only)");?></a></li> -->
                
                </ul>
                <!-- Tab panes -->
                <div class="tab-content mt20">
                    <div class='loading' v-if='loading'>
                        <span></span>
                    </div>
                    <div role="tabpanel" class="tab-pane active" id="unreply">
                        <?php $this->renderPartial("_reply", array('type' => 'unreply'));?>
                    </div>
                    <div role="tabpanel" class="tab-pane" id="replied">
                        <?php $this->renderPartial("_reply", array('type' => 'replied'));?>
                        <nav v-if="feedback.pages && feedback.pages.count>1">
                            <ul class="pager">
                                <li class="previous" :class='pageNum==1?"disabled":""'><a href="javascript:;" @click="prev('replied')"><span aria-hidden="true">&larr;</span> 上一页</a></li>
                                <li class="next" :class='pageNum==feedback.pages.count?"disabled":""'><a href="javascript:;" @click="plus('replied')">下一页 <span aria-hidden="true">&rarr;</span></a></li>
                            </ul>
                        </nav>
                    </div>
                    <!-- <div role="tabpanel" class="tab-pane" id="allUnreplied">
                        <?php $this->renderPartial("_reply", array('type' => 'allUnreplied'));?>
                    </div>
                    <div role="tabpanel" class="tab-pane" id="all">
                        <div class="panel panel-default" v-for='(item,index) in allItems.items'>
                            <div class="panel-body">
                                <div class="media " v-if='item.creator_type == "parent"'>
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                            <img class="media-object img-circle allImage" :src='allItems.childs[item.child_id].avatar' data-holder-rendered="true" >
                                        </a>
                                    </div>
                                    <div class="media-right pull-right">
                                        <a href='javascript:void(0)'style='line-height:48px' @click='linked(item)'><?php echo Yii::t("newDS", "Read all conversations");?></a>
                                    </div>
                                    <div class="media-body pt10 media-middle">
                                        <h4  class="media-heading font14">{{allItems.childs[item.child_id].name}}</h4>
                                        <div class='text-muted font12'>{{allItems.childs[item.child_id].class_name}}</div>
                                    </div>
                                </div>
                                <div class="media " v-if='item.creator_type == "staff"'>
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                            <img class="media-object img-circle allImage" :src='allItems.staff[item.created_by].avatar' data-holder-rendered="true" >
                                        </a>
                                    </div>
                                    <div class="media-right pull-right">
                                        <a href='javascript:void(0)'style='line-height:48px' @click='linked(item)'><?php echo Yii::t("newDS", "Read all conversations");?></a>
                                    </div>
                                    <div class="media-body pt10 media-middle">
                                        <h4  class="media-heading font14 pt10">{{allItems.staff[item.created_by].name}}</h4>
                                    </div>
                                </div>
                                <div class='pt10' style='padding-left:58px'>
                                    <p class='font14 color3' v-html='html(item.content)'> </p>
                                    <p class='font12 color6'>{{item.updated_at}}</p>
                                </div>
                            </div>    
                            <div class="panel-footer displayFlex">
                                <div class="flex1 ellipsis">
                                    <?php echo Yii::t("newDS", "Source:");?>  <a href='javascript:void(0)' @click='viewContent(item.journal_id)'>{{item.journal_title}}</a>
                                </div>
                                <div class="flexWidth color6 text-right"> {{newDate(item.journal_updated_at)}} </div>
                            </div>
                        </div>
                        <nav v-if="allItems.pages && allItems.pages.count>1">
                            <ul class="pager">
                                <li class="previous" :class='pageNum==1?"disabled":""'><a href="javascript:;" @click="prev('all')"><span aria-hidden="true">&larr;</span> 上一页</a></li>
                                <li class="next" :class='pageNum==allItems.pages.count?"disabled":""'><a href="javascript:;" @click="plus('all')">下一页 <span aria-hidden="true">&rarr;</span></a></li>
                            </ul>
                        </nav>
                    </div> -->
                </div>
                <div class="modal fade" id="linkedModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title" id="parentReplyLabel">{{journal_title}}
                                <div class='font12 color6 pt10'>{{newDate(journal_updated_at)}}</div></h4>
                        </div>
                        <div class="modal-body">
                            
                            <div  class="comment-box mt20" v-if='Object.keys(linkData).length!=0'>
                                <div class="row" style="margin-right: 0;">
                                <div class="col-md-4">
                                    <div class="list-group scroll-box"  style="max-height: 500px;overflow-y: auto; overflow-x: hidden; margin-bottom: 15px;">
                                        <a href="javascript:;" class="list-group-item" :class="{'active-1': childid==_childid}" v-for="(list,_childid,index) in linkData.childs" :key="_childid" @click.stop="itemChild(_childid,'all')">
                                            <div class="row">
                                                <div class="col-md-3">
                                                    <img :src="linkData.childs[_childid].avatar" class="img-circle allImage" width="50">
                                                </div>
                                                <div class="col-md-9" style="margin-top: 5px;">
                                                    <h4 class="list-group-item-heading">{{linkData.childs[_childid].name}}</h4>
                                                    <p class="list-group-item-text">{{linkData.childs[_childid].class_name}}</p>
                                                </div>
                                            </div>
                                        </a>
                                    </div>
                                </div>
                                    <div  class="col-md-8 active-1 scroll-box" style="padding: 15px; border-radius: 4px;max-height: 500px;overflow-y: auto; overflow-x: hidden; margin-bottom: 15px;">
                                        <div class='scroll-box' v-if='Object.keys(feedbackItem).length!=0'>
                                            <div style="margin-bottom: 15px;" v-for="(fItem, i) in feedbackItem.items" :key="i">
                                                <div v-if='fItem.mark_as_staff==0'>
                                                    <div class="row" v-if="fItem.creator_type == 'parent'">
                                                        <div class="col-md-2 text-center">
                                                            <img :src="linkData.childs[childid].avatar" class="img-circle allImage" width="50">
                                                        </div>
                                                        <div class="col-md-10">
                                                            <div class="name-2">{{linkData.childs[childid].name}}</div>
                                                            <div class="content-2"  v-html='html(fItem.content)'></div>
                                                            <div class="text-muted" v-if="linkData.childs[childid][`p_${fItem.created_by}`] == 'f'"><?php echo Yii::t("newDS", "From student's Dad");?> {{ fItem.updated_at }}</div>
                                                            <div class="text-muted" v-if="linkData.childs[childid][`p_${fItem.created_by}`] == 'm'"><?php echo Yii::t("newDS", "From student's Mom");?> {{ fItem.updated_at }}</div>
                                                        </div>
                                                    </div>
                                                    <div class="row" v-if="fItem.creator_type == 'staff'">
                                                        <div class="col-md-2 text-center">
                                                            <img :src="feedbackItem.staff[fItem.created_by].avatar" class="img-circle allImage" width="50">
                                                        </div>
                                                        <div class="col-md-10">
                                                            <div class="name-2">{{feedbackItem.staff[fItem.created_by].name}}</div>
                                                            <div class="content-2" v-html='html(fItem.content)'></div>
                                                            <div class="text-muted">{{ fItem.updated_at }}</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div v-else class='p20 mt20 text-center borderTop ' :class='i!=feedbackItem.items.length-1?"borderBto":""'>
                                                    <p class='font14 color3'>
                                                        <span class='glyphicon glyphicon-pushpin mr10' style='color:#EC971FFF'></span>{{feedbackItem.staff[fItem.created_by].name}} <?php echo Yii::t("newDS", "marked No Reply Needed");?> <span class='font14 color6 ml10'>{{fItem.updated_at}}</span>
                                                    </p>
                                                    <div  class='font12 color9'>
                                                        <?php echo Yii::t("newDS", "(This message is invisible to parents)");?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                 
            </div>
        </div>
        <div style='position: fixed; z-index: 10000;display:none' class='nextImg'>
            <div id='prev' class='viewer-prev'></div>
            <div id='next' class='viewer-next'></div>
        </div>
        <!-- 查看内容 -->
        <div class="modal fade" id="contentModal" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "Content");?></h4>
                </div>
                <div class="modal-body" v-if='Object.keys(contentData).length!=0'>
                    <form class="form-horizontal">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <h3 class='titleCn'>{{contentData.q_cn}}</h3>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="col-sm-12">
                                <h3 class='titleEn'>{{contentData.q_en}}</h3>
                            </div>
                        </div>
                        <div class="form-group" >
                            <div class="col-sm-12 pt7 summary_cn" >
                                {{contentData.summary_cn}}
                            </div>
                        </div>   
                        <div class="form-group" >
                            <div class="col-sm-12 pt7 summary_en">
                                {{contentData.summary_en}}
                            </div>
                        </div>   
                        <div class="form-group" >
                            <div class="col-sm-12 pt7">
                            <span class="label label-default defaultBg mr5 fontWeightNormal" v-for='(list,index) in contentData.grade_text'>{{list}}</span>
                            </div>
                        </div>   
                        <div class="form-group">
                            <div class="col-sm-12 pt7" style="padding: 0 30px;">
                                <div class='content contentCn' v-html='contentData.content'></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="col-sm-12 pt7" style="padding: 0 30px;">
                                <div class='content contentEn' v-html='contentData.content_en'></div>
                            </div>
                        </div>
                        <div class='mt24' v-if='contentData.joint_admins.length!=0'>
                            <p class='color3 font14'><strong><?php echo Yii::t("directMessage", "Collaboration"); ?></strong> <span class="badge bg7 ml5">{{contentData.joint_admins.length}}</span></p>
                            <div v-for='(list,index) in contentData.joint_admins' class='mt8 mb8' >
                                <div class="media">
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                            <img class="contentAvatar" :src="contentData.joint_admins_info[list].photoUrl" data-holder-rendered="true" >
                                        </a>
                                    </div>
                                    <div class="media-body pt8 media-middle">
                                        <h4  class="media-heading font12">{{contentData.joint_admins_info[list].name}}</h4>
                                        <div class='text-muted'>{{contentData.joint_admins_info[list].hrPosition}}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="col-sm-12 pt7" >
                                <p v-for='(list,index) in attachments'>
                                    <a target="_blank" style='line-height:26px' :href='list.file_key' >{{list.title}}</a>
                                </p>
                            </div>
                        </div>
                    </form>
                </div>
                </div>
            </div>
        </div>
        <!-- 标记为未回复 -->
        <div class="modal fade" id="noReplyModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
            <div class="modal-dialog modal-sm" role="document">
                <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "Warning");?></h4>
                </div>
                <div class="modal-body">
                    <div v-if='joint_admins_count!=0'>
                        <div><?php echo Yii::t("directMessage", 'This DM involves multi collaborators. This operation will cause everyone to no longer receive unreplied reminders about this feedback. Are you sure to continue?');?> </div>
                        <div class="checkbox">
                            <label>
                                <input type="checkbox" v-model='noReplySure'><?php echo Yii::t("directMessage", 'Yes, pretty sure');?>
                            </label>
                        </div>
                    </div>
                    <div v-else><?php echo Yii::t("newDS", 'Proceed to mark it as "No Reply Needed"?');?></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel");?></button>
                    <button type="button" class="btn btn-primary" @click='reply("noReply")'><?php echo Yii::t("message", "OK");?></button>
                </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    var toReplyNum = '<?php echo $this->toReplyNum; ?>';
    var data = <?php echo json_encode($data); ?>;
    var staff = <?php echo CHtml::encode(Yii::app()->request->getParam('staff', 0)); ?>;
    var container=new Vue({
        el: "#container",
        data: {
            loading: false,
            toReplyNum:toReplyNum,
            opened: '',
            childid: 0,
            content: '',
            feedback: {},
            feedbackNonAlert: [],
            feedbackItem: {},
            allItems:{},
            linkData:{},
            replied:null,
            pageNum:'1',
            journal_title:'',
            journal_updated_at:'',
            contentData:{},
            attachments:[],
            journalCategory:data.journalCategory,
            subjectList:data.subjectList,
            leaderuserList: data.leaderList,
            teacheruserList: {},
            gradeGroupList:data.gradeGroupList,
            tabType:'',
            token:'',
            videoToken:'',
            uploadImgList:[],
            uploadLinkList:[],
            attachmentName:'',
            uploadShow:false,
            loadingList:[],
            loadingType:0,
            joint_admins_count:null,
            noReplySure:false,
            qrcodeBox:false,
            wechatData:{},
            scanQrcodeBox:false,
        },
        created: function() {
            this.change('unreply')
            this.showWechatQrcode()
        },
        methods: {
            showUnWechatQrcode(){
                let that=this
                if(this.scanQrcodeBox){
                    clearInterval(this.timer);
                    that.scanQrcodeBox=!that.scanQrcodeBox
                    return
                }else{
                    this.timer =setInterval(this.showWechatQrcode, 5000);   
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("directMessage/teacherBindQrcode") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {},
                    success: function(data) {
                        if (data.state == 'success') {
                            that.scanQrcodeBox=!that.scanQrcodeBox
                            that.$nextTick(() => {
                                $('#wechatQrcodeBind').qrcode({width: 124,height: 124,text:data.data});
                            })                            
                        }
                    },
                    error: function(data) {
                        
                    },
                })
            },
            showWechatQrcode(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("directMessage/teacherBindInfo") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {},
                    success: function(data) {
                        if (data.state == 'success') {
                            that.wechatData=data.data
                            if(data.data.state==1){
                                if(that.scanQrcodeBox){
                                    that.scanQrcodeBox=false
                                    that.qrcodeBox=true
                                }
                                clearInterval(that.timer);
                            }
                        }
                    },
                    error: function(data) {
                        
                    },
                })
            },
            unbind(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("directMessage/teacherUnbind") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {},
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg:data.message
                            })
                            that.qrcodeBox=false
                            that.showWechatQrcode()
                        }else{
                            resultTip({
                                    error: 'warning',
                                    msg:data.message
                                })
                        }
                    },
                    error: function(data) {
                        
                    },
                })
            },
            html(data){
              return  data.replace(/\n/g, '<br/>')
            },
            newDate(dateTime){
                var dateString=''
                if(dateTime!=''){
                    let time = new Date(dateTime).getTime();
                    const date = new Date(time);
                    const Y = date.getFullYear() + '-';
                    const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
                    const D = (date.getDate() < 10 ? '0'+date.getDate() : date.getDate()) + '  ';
                    const h = (date.getHours() < 10 ? '0'+date.getHours() : date.getHours()) + ':';
                    const m = (date.getMinutes() <10 ? '0'+date.getMinutes() : date.getMinutes()) + ':';
                    const s = date.getSeconds(); // 秒
                    dateString = Y + M + D + h + m + s;
                }
                return dateString;
            },
            open(id) {
                if (this.opened == id) {
                    this.opened = ''
                }
                else {
                    if (this.content != '') {
                        if (!confirm('您填写的内容还未提交，确定切换到其他学生吗？')) {
                            return;
                        }
                    }
                    if (this.opened != id) {
                        this.opened = id;
                        this.childid = 0;
                        this.content = '';
                    }
                }
            },
            itemChild(childid,type) {
                if (this.content != '') {
                    if (!confirm('您填写的内容还未提交，确定切换到其他学生吗？')) {
                        return;
                    }
                }
                var _this = this;
                this.childid = childid;
                this.feedbackItem={}
                this.content = '';
                if(!type){
                    this.loading = true
                }
                this.uploadImgList=[]
                this.uploadLinkList=[]
                this.loadingList=[]
                $.ajax({
                    url: '<?php echo $this->createUrl("item") ?>',
                    type: 'get',
                    dataType: 'json',
                    data: {
                        id: _this.opened,
                        child_id: childid
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            if(!type){
                                _this.loading = false
                            }
                            _this.getQiniu()
                            data.data.items.forEach((list) => {
                                list.imgUrl=[]
                                list.pdfUrl=[]
                                list.videoUrl=[]
                                if(list.attachments.length!=0){
                                    list.attachments.forEach((item) => {
                                        if(data.data.attachmentList[item]){
                                            let type=data.data.attachmentList[item].mimetype.split('/')[0]
                                            if(type=="image"){
                                            list.imgUrl.push(data.data.attachmentList[item])
                                            }else if(type=="video"){
                                            list.videoUrl.push(data.data.attachmentList[item])
                                            }else{
                                            list.pdfUrl.push(data.data.attachmentList[item])
                                            }
                                        }
                                    })
                                }
                            })
                            _this.feedbackItem = data.data 
                            _this.$nextTick(function () {
                                setTimeout(() => {
                                    if(_this.tabType=='unreply'){
                                        _this.$refs.scrollHeight[1].scrollTop = _this.$refs.scrollHeight[1].scrollHeight
                                    }
                                    if(_this.tabType=='replied'){
                                        _this.$refs.scrollHeight[0].scrollTop = _this.$refs.scrollHeight[0].scrollHeight
                                    }
                                }, 500);
                            });
                        }
                        else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            _this.loading = false
                        }
                    }
                });
            },
            reply(typeData) {
                var _this = this;
                if(this.loadingList.length!=0){
                    resultTip({
                        error: 'warning',
                        msg: '<?php echo Yii::t("directMessage", "Uploading");?>'
                    });
                    return
                }
                var imgIds=[]
                var pdfIds=[]
                var videoIds=[]
                var ids=[]
                this.uploadImgList.forEach((item) => {
                    ids.push(item._id)
                    imgIds.push({
                        url:item.file_key,
                        id:item._id,
                        mimetype:item.mimetype,
                        title:item.title
                    })
                })
                this.uploadLinkList.forEach((item) => {
                    ids.push(item._id)
                    let type=item.mimetype.split('/')[0]
                    if(type=='video'){
                        videoIds.push({
                            url:item.file_key,
                            id:item._id,
                            mimetype:item.mimetype,
                            title:item.title,
                            video_convert_status: item.video_convert_status,
                            video_cover: item.video_cover,
                        })
                    }else{
                        pdfIds.push({
                            url:item.file_key,
                            id:item._id,
                            mimetype:item.mimetype,
                            title:item.title
                        })
                    }
                })
                if(typeData=='noReply' && this.joint_admins_count!=0){
                    if(!this.noReplySure){
                        resultTip({
                            error: 'warning',
                            msg: '<?php echo Yii::t("newDS", "Please confirm");?>'
                        });
                        return
                    }
                }
                if(typeData!='noReply'){
                    if(ids.length==0 && _this.content==''){
                        resultTip({
                            error: 'warning',
                            msg: '<?php echo Yii::t("directMessage", "Content cannot be empty.");?>'
                        });
                        return
                    }
                }
                
                this.loading = true
                $.ajax({
                    url: '<?php echo $this->createUrl("saveComment") ?>',
                    type: 'post',
                    dataType: 'json',
                    data: {
                        id: _this.opened,
                        child_id: _this.childid,
                        content: _this.content,
                        mark_as_staff:typeData=='noReply'?1:0,
                        attachments:ids
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            _this.feedbackItem.items.push({
                                id: data.data._id,
                                content: data.data.content,
                                created_by: data.data.created_by,
                                creator_type: data.data.creator_type,
                                updated_at: data.data.created_at_format,
                                mark_as_staff:data.data.mark_as_staff,
                                imgUrl:imgIds,
                                pdfUrl:pdfIds,
                                videoUrl:videoIds
                            })
                            _this.loading = false
                            _this.content = '';
                            _this.feedbackNonAlert.push(_this.childid)
                            if(typeData=='noReply'){
                                $('#noReplyModal').modal('hide')
                            }
                             _this.count()
                             _this.uploadImgList=[]
                             _this.uploadLinkList=[]
                        }
                        else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            _this.loading = false
                        }
                    }
                });
            },
            count(){
                if(Number(this.replied)!=0){
                    this.replied=Number(this.replied) - 1
                }
                if(this.replied==0){
                    this.toReplyNum=0
                }
                for(var i=0;i<this.feedback.items.length;i++){
                    if(this.feedback.items[i].id==this.opened){
                        if(Number(this.feedback.items[i].count)!=0){
                            this.feedback.items[i].count=Number(this.feedback.items[i].count) - 1
                        }
                    }
                }
            },
            change(type,pege) {
                this.feedback={}
                this.tabType=type
                this.loading = true
                this.opened = '';
                this.childid = 0;
                this.content = '';
                var _this = this;
                this.linkData={}
                if(!pege){
                    _this.pageNum='1'
                }
                if (type == 'unreply') {
                    $.ajax({
                        url: '<?php echo $this->createUrl("unreply") ?>',
                        type: 'get',
                        dataType: 'json',
                        data:{
                            staff:staff
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                _this.replied=data.data.count
                                _this.feedback = data.data;
                                _this.loading = false;
                            }
                            else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                                _this.loading = false

                            }
                        }
                    });
                }
                else if (type == 'allUnreplied') {
                    $.ajax({
                        url: '<?php echo $this->createUrl("AllUnreply") ?>',
                        type: 'get',
                        dataType: 'json',
                        success: function(data) {
                            if (data.state == 'success') {
                                _this.feedback = data.data
                                _this.loading = false
                            }
                            else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                                _this.loading = false
                            }
                        }
                    });
                }
                else if (type == 'replied') {
                    $.ajax({
                        url: '<?php echo $this->createUrl("replied") ?>',
                        type: 'get',
                        dataType: 'json',
                        data:{
                            page:_this.pageNum,
                            staff:staff
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                _this.pageNum=data.data.pages.page
                                _this.feedback = data.data
                                _this.loading = false
                            }
                            else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                               _this.loading = false

                            }
                        }
                    });
                }
                else if (type == 'all') {                    
                    $.ajax({
                        url: '<?php echo $this->createUrl("all") ?>',
                        type: 'get',
                        dataType: 'json',
                        data:{
                            page:_this.pageNum
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                               _this.pageNum=data.data.pages.page
                               _this.allItems=data.data
                               _this.loading = false

                            }
                            else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                                _this.loading = false
                            }
                        }
                    });
                }
            },
            plus(type) {
                if(this.pageNum==this.feedback.pages.count){
                    return
                }
                this.pageNum = Number(this.pageNum) + 1
                this.change(type, 'page')
            },
            prev(type) {
                if(this.pageNum==1){
                    return
                }
                this.pageNum = Number(this.pageNum) - 1
                this.change(type, 'page')     
            },
            linked(list){
                var _this = this;
                this.opened=list.journal_id
                $.ajax({
                    url: '<?php echo $this->createUrl("viewComment") ?>',
                    type: 'get',
                    dataType: 'json',
                    data: {
                        id: list.id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            _this.linkData=data.data
                            _this.feedbackItem.items = data.data.items
                            _this.feedbackItem.staff = data.data.staff
                            _this.childid = data.data.current_child_id;
                            _this.journal_title=list.journal_title
                            _this.journal_updated_at=list.journal_updated_at
                            $('#linkedModal').modal('show')
                        }
                        else {
                            alert(data.message)
                        }
                    }
                });
            },
            viewContent(journal_id){
                var that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getOne") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id: journal_id,
                        linkType:'notice'

                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.contentData=data.data
                           $('#contentModal').modal('show')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/getAttachments") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        linkId: journal_id,
                        linkType:'notice'
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.attachments=data.data.data
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
                
            },
            markNoReply(item){
                this.noReplySure=false
                this.joint_admins_count=item.joint_admins_count
                $('#noReplyModal').modal()
            },
            getQiniu(){
                this.initUploader(1);
                this.initUploader(0);
            },
            initUploader(isVideo) {
                let that = this;
                let url = '<?php echo $this->createUrl("journals/getCommentQiniuToken") ?>';
                let data = {
                    linkId: that.opened,
                    linkType: 'journal'
                };
                if (isVideo) data.isVideo = 1;
                $.ajax({
                    url: url,
                    type: "POST",
                    dataType: 'json',
                    data: data,
                    success: function(res) {
                        if (res.state === 'success') {
                            if (isVideo) {
                                that.videoToken = res.data.data;
                            } else {
                                that.token = res.data.data;
                            }
                            that.uploadShow = true;
                            that.$nextTick(() => {
                                let btns = [];
                                if (that.tabType === 'unreply') {
                                    btns = isVideo ? [{id: 'pickfilesVideo', type: 'video'}] :
                                        [{id: 'pickfilesPhoto', type: 'photo'}, {id: 'pickfilesPDF', type: 'pdf'}];
                                } else if (that.tabType === 'replied') {
                                    btns = isVideo ? [{id: 'pickfilesVideo1', type: 'video'}] :
                                        [{id: 'pickfilesPhoto1', type: 'photo'}, {id: 'pickfilesPDF1', type: 'pdf'}];
                                } else if (that.tabType === 'allUnreplied') {
                                    btns = isVideo ? [{id: 'pickfilesVideo2', type: 'video'}] :
                                        [{id: 'pickfilesPhoto2', type: 'photo'}, {id: 'pickfilesPDF2', type: 'pdf'}];
                                }
                                btns.forEach(btn => {
                                    const mimeTypes = that.getMimeTypes(btn.type);
                                    let uploaderConfig = Object.assign({}, config, {
                                        token: isVideo ? that.videoToken : that.token,
                                        browse_button: btn.id,
                                        filters: mimeTypes
                                    });
                                    let uploader = new plupload.Uploader(uploaderConfig);
                                    uploader.init();
                                });
                            });
                        } else {
                            resultTip({ error: 'warning', msg: res.message });
                        }
                    },
                    error: function() {
                        resultTip({ error: 'warning', msg: '请求 Token 失败' });
                    }
                });
            },
            getMimeTypes(type) {
                let mimeTypes;
                if (type === 'photo') {
                    mimeTypes = [{
                        title: "<?php echo Yii::t('teaching', 'Image files'); ?>",
                        extensions: "jpg,gif,png,jpeg"
                    }];
                } else if (type === 'video') {
                    mimeTypes = [{
                        title: "<?php echo Yii::t('teaching', 'Video files'); ?>",
                        extensions: "mp4,avi,mov,wmv"
                    }];
                } else {
                    mimeTypes = [{
                        title: "<?php echo Yii::t('teaching', 'PDF and document files'); ?>",
                        extensions: "pdf,doc,xls,zip,xlsx,docx"
                    }];
                }
                return mimeTypes;
            },
            delImg(type,list,index){
                var that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/deleteAttachment") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        attachmentId:list._id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            if(type=='img'){
                                that.uploadImgList.forEach((item,index) => {
                                    if (item._id==list._id) {
                                        that.uploadImgList.splice(index, 1)
                                    } 
                                })
                            }else{
                                that.uploadLinkList.forEach((item,index) => {
                                    if (item._id==list._id) {
                                        that.uploadLinkList.splice(index, 1)
                                    } 
                                })
                            }
                            resultTip({
                                msg:data.state
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            saveFile(list,index) {
                var that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/updateAttachmentName") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        attachmentId:list._id,
                        attachmentName:this.attachmentName

                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.uploadLinkList.forEach((item,index) => {
                                if (item._id==list._id) {
                                    Vue.set(that.uploadLinkList[index], 'title',data.data.title);
                                    Vue.set(that.uploadLinkList[index], 'file_key', data.data.url);
                                    Vue.set(that.uploadLinkList[index], 'isEdit', false);
                                } 
                            })
                            resultTip({
                                msg:'<?php echo Yii::t("newDS", "Data Saved!");?>'
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            showImg(list){
                var id=this.tabType+"_"+list.id 
                var viewer = new Viewer(document.getElementById(id),{
                    fullscreen: false,
                    title:false,
                    show:function(){ 
                        $('.nextImg').show()
                        if(list.imgUrl.length==1){
                            $('.viewer-prev').hide()
                            $('.viewer-next').hide()
                        }
                    },
                    hide:function(){ 
                        $('.nextImg').hide()
                        viewer.destroy()
                    }
                });
                document.getElementById('next').onclick = function() {
                    viewer.next();
                }
                document.getElementById('prev').onclick = function() {
                    viewer.prev();
                }
                $("#"+id).click();
            },
        }
    })
    var config = {
        runtimes: 'html5,flash,html4', //上传模式,依次退化
        container: 'container', //上传区域DOM ID，默认是browser_button的父元素，
        browse_button: 'pickfilesPhoto', //上传选择的点选按钮，**必需**
        token: '',
        flash_swf_url: '/js/plupload/Moxie.swf', // flash的相对地址
        auto_start: true, //选择文件后自动上传，若关闭需要自己绑定事件触发上传
        // multi_selection: false, //不允许多选
        url:'https://upload-z1.qiniup.com',
        init: {
            'FilesAdded': function(up, files) {
                container.disabledUpload=true
                let fileType=files[0].type.split('/')
                if(fileType[0]=="image"){
                  container.loadingType=1
                }else{
                  container.loadingType=2
                }
                container.loadingList=files
                up.start();
            },
            BeforeUpload: function(up, file) {
                let fileType=file.type.split('/')
                let token=fileType[0]=="video"?container.videoToken:container.token
                up.setOption({
                    multipart_params:{token:token}
                })
                if(fileType[0]=="image"){
                    container.uploadImgList.push({types:'1'})
                }else{
                    container.uploadLinkList.push({title:' <?php echo Yii::t("directMessage", "Uploading");?>'})  
                }
                container.loadingList.splice(0,1)
            },
            UploadProgress: function(up, file) {
                $('#progress').css('width', file.percent+'%').html(file.percent+'%');
            },
            'FileUploaded': function(up, file, info) {
                var response = eval('('+info.response+')');
                if(info.status == 200){
                    let fileType=response.data.mimetype.split('/')
                    if(fileType[0]=="image"){
                        container.uploadImgList.splice(container.uploadImgList.length-1,1)
                        container.uploadImgList.push(response.data)
                    }else{
                        response.data.isEdit=false
                        container.uploadLinkList.splice(container.uploadLinkList.length-1,1)
                        container.uploadLinkList.push(response.data)
                    }
                }
            },
            'Error': function(up, err, errTip) {
                if(err.response){
                    var response = eval('('+err.response+')');
                }
                else{
                    var response = {message: err.message};
                }
                if(response.error == 'token not specified'){
                    up.stop();
                    resultTip({
                        error: 'warning',
                        msg: err.message
                    });
                }
            },
            'UploadComplete': function(up, file) {
                //队列文件处理完毕后,处理相关的事情
            }
        }
    };
    $(document).click(function(event) {
        container.qrcodeBox=false;
        container.scanQrcodeBox=false;
        clearInterval(container.timer);
    });
</script>
