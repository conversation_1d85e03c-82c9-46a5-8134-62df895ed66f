<?php

/**
 * This is the model class for table "ivy_child_label".
 *
 * The followings are the available columns in table 'ivy_child_label':
 * @property integer $childid
 * @property integer label1
 * @property integer label2
 * @property integer label3
 * @property integer label4
 * @property integer label5
 * @property integer label6
 * @property integer label7
 * @property integer label8
 * @property integer label9
 * @property integer label10
 */
class ChildLabel extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ChildClassLink the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_child_label';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'ChildProfileBasic' => array(self::BELONGS_TO, 'ChildProfileBasic', 'childid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'childid' => 'Childid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('childid',$this->childid);
		$criteria->compare('label1',$this->label1);
		$criteria->compare('label2',$this->label2);
		$criteria->compare('label3',$this->label3);
		$criteria->compare('label4',$this->label4);
		$criteria->compare('label5',$this->label5);
		$criteria->compare('label6',$this->label6);
		$criteria->compare('label7',$this->label7);
		$criteria->compare('label8',$this->label8);
		$criteria->compare('label9',$this->label9);
		$criteria->compare('label10',$this->label10);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}