<?php
$this->breadcrumbs=array(
	Yii::t("navigations","Payment")=>array('/child/payment/summary'),
	Yii::t("navigations","Tuition Deposit"),
);?>

<h1><label><?php echo Yii::t("payment", 'Tuition Deposit History')?></label></h1>
<?php
		$this->widget('zii.widgets.grid.CGridView', array(
			'cssFile'=>Yii::app()->theme->baseUrl.'/widgets/gridview/styles.css',
			'dataProvider'=> $depositList,
			'enableSorting'=>false,
			'pager'=>array(
				'class'=>'CLinkPager',
				'cssFile'=>Yii::app()->theme->baseUrl.'/css/pager.css',
			),
			'columns'=>array(
				array(
					'headerHtmlOptions'=>array('class'=>'icon-24'),
					'htmlOptions' => array('class'=>'icon-column'),
					'name' => 'inout',
					'header' => '',
					'type'=>'raw',
					'value' => '$data->renderInout()'
				),
				array(
					'name' => 'yid',
					'value'  => '$data->renderSchoolYear($data->calendarInfo->startyear)',
				),
				array(
        			'name'   =>'amount',
					'value' => 'Invoice::formatAmount($data->inout,$data->amount)'
				),
				array(
        			'name'=>'timestamp',
					'value' => 'Mims::formatDateTime($data->timestamp,"medium","short")'
				),
			),
			'ajaxUpdate'=>false,
			'template'=>"{summary}{pager}{items}{summary}{pager}"
		));
?>