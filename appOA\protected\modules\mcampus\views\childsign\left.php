<?php
    $url_exp = explode('?',$_SERVER['REQUEST_URI']);
    $url_exp2 = explode('/',$url_exp[0]);
    $func = end($url_exp2)
?>
<div class="col-md-2">
    <div class="list-group" id="classroom-status-list">
        <a href="<?php echo Yii::app()->params['siteFlag'] == 'daystar' ?  $this->createUrl('index2') :  $this->createUrl('index')?>"
           class="list-group-item status-filter <?php if(in_array($func,array('index','index2'))){ echo "active";}?>"><?php echo Yii::t('campus', 'Student Attendance'); ?></a>
        <a href="<?php echo $this->createUrl('vacation'); ?>"
           class="list-group-item status-filter <?php if($func=='vacation'){ echo "active";}?>"><?php echo Yii::t('campus', 'Student leave list'); ?></a>
        <a href="<?php echo $this->createUrl('comeLate'); ?>"
           class="list-group-item status-filter <?php if($func=='comeLate'){ echo "active";}?>"><?php echo Yii::t('campus', 'Late arrival/early release'); ?></a>
        <a href="<?php echo $this->createUrl('track'); ?>"
           class="list-group-item status-filter <?php if($func=='track'){ echo "active";}?>"><?php echo Yii::t('campus', 'Sick leave follow-up'); ?></a>
        <a href="<?php echo $this->createUrl('signlog'); ?>"
           class="list-group-item status-filter <?php if($func=='signlog'){ echo "active";}?>"><?php echo Yii::t('campus', 'Operations log'); ?></a>
        <a href="<?php echo $this->createUrl('report'); ?>"
           class="list-group-item status-filter <?php if($func=='report'){ echo "active";}?>"><?php echo Yii::t('campus', 'Monthly report'); ?></a>
    </div>
</div>