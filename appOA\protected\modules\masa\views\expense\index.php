<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Routines'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', '课后课管理') ?></li>
        <li class="active"><?php echo Yii::t('site', '报销列表') ?></li>
    </ol>

    <div class="row">
        <div class="col-md-2 col-sm-2 mb10">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->module->getMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
            ));
            ?>
        </div>

        <div class="col-md-10 col-sm-12">
            <ul class="nav nav-tabs" role="tablist">
               <li role="presentation" class="active"><a href="#home" aria-controls="home" role="tab" data-toggle="tab">报销列表</a></li>
               <li role="presentation"><a href="#profile" aria-controls="profile" role="tab" data-toggle="tab">付款列表</a></li>
             </ul>
             <div class="tab-content">
                 <div role="tabpanel" class="tab-pane active" id="home">
                                 <div class="page-header">
                                     <h3>报销列表
                                         <a href="<?php echo $this->createUrl('expenesAdd') ?>" class="J_modal btn btn-primary pull-right"
                                            target="_blank"><span class="glyphicon glyphicon-plus"
                                                                  aria-hidden="true"></span> <?php echo Yii::t('user', '增加报销'); ?></a>
                                     </h3>
                                 </div>
                                 <?php echo Yii::t('asa', '凡与萌犸象有关的报销，请参见以下流程：<br><br>1. 经园长同意之后开始采购，需开具萌犸象发票，发票信息如下：'); ?>
                                 <br>
                                 <div class="alert alert-info" role="alert">
                                     <?php echo Yii::t('asa', '<strong>萌犸象（北京）教育咨询有限公司</strong><br>
                       统一信用代码证（税号）：<span class="text-warning">91110105MA00ARBB5A</span><br>
                       地址：北京市朝阳区东三环北路霞光里18号1号楼A座10层E2-1单元 工商银行北京燕莎支行 <span class="text-warning">0200012709201644656</span><br>
                       电话：010-84464189<br>'); ?>
                                 </div>
                                 <?php echo Yii::t('asa', '2. 拿到发票之后在系统中录入报销信息及明细，如果仅限于某课程的报销，请务必选择对应的课程<br>
                     3. 等待报销审核状态为“通过”，从系统打印报销单，与原始票据一起邮寄回总部<br>
                     4. 等待报销'); ?>
                                 <hr/>
                                 <div class="col-md-12"></div>
                                 <?php
                                 $this->widget('ext.ivyCGridView.BsCGridView', array(
                                     'id' => 'expenseList',
                                     'afterAjaxUpdate' => 'js:head.Util.modal',
                                     'dataProvider' => $model,
                                     'template' => "{items}{pager}",
                                     //状态为无效时标红
                                     //'rowCssClassExpression' => '( $data->status == 0 ? "active" : "" )',
                                     'colgroups' => array(
                                         array(
                                             "colwidth" => array(100, 100, 100, 100, 100, 100, 80, 100, 120),
                                         )
                                     ),
                                     'columns' => array(
                                         array(
                                             'name' => 'voucherid',
                                             'value' => 'sprintf("%08d",$data->id)',
                                         ),
                                         array(
                                             'name' => 'title',
                                             'value' => '$data->title',
                                         ),
                                         array(
                                             'name' => 'expense_uid',
                                             'value' => array($this, "getUsersName"),
                                         ),
                                         array(
                                             'name' => 'amount',
                                             'value' => '$data->amount',
                                         ),
                                         array(
                                             'name' => 'payee_user',
                                             'value' => '$data->payee_user',
                                         ),
                                         array(
                                             'name' => 'payee_account',
                                             'value' => '$data->payee_account',
                                         ),
                                         array(
                                             'name' => 'status',
                                             'value' => array($this, "getStatus"),
                                         ),
                                         array(
                                             'name' => 'created',
                                             'value' => 'date("Y-m-d", $data->created)',
                                         ),
                                         array(
                                             'name' => Yii::t('asa', '操作'),
                                             'value' => array($this, "getButton"),
                                         ),
                                     ),
                                 ));
                                 ?>
                 </div>
                 <div role="tabpanel" class="tab-pane" id="profile">
                                 <div class="page-header">
                                     <h3>付款列表
                                         <button  class="btn btn-primary pull-right"
                                            data-target="#delcfmModel" data-toggle="modal" ><span class="glyphicon glyphicon-plus"
                                                                  aria-hidden="true"></span> <?php echo Yii::t('user', '增加付款'); ?></button>
                                     </h3>
                                 </div>
                                 <?php echo Yii::t('asa', '凡与萌犸象有关的付款流程，请参见以下流程：<br><br>1. 所有的付款需填写付款申请单，园长签字同意。'); ?>
                                 <br>
                                 <?php echo Yii::t('asa', '2. 先行获取收款方开具的萌犸象抬头的发票信息，发票信息如下：') ?>
                                 <div class="alert alert-info" role="alert">
                                     <?php echo Yii::t('asa', '<strong>萌犸象（北京）教育咨询有限公司</strong><br>
                       统一信用代码证（税号）：<span class="text-warning">91110105MA00ARBB5A</span><br>
                       地址：北京市朝阳区东三环北路霞光里18号1号楼A座10层E2-1单元 工商银行北京燕莎支行 <span class="text-warning">0200012709201644656</span><br>
                       电话：010-84464189<br>'); ?>
                                 </div>
                                 <?php echo Yii::t('asa', '3. 在系统中录入付款信息及明细，如果仅限于某课程的付款，请务必选择对应的课程<br>4. 等待审核状态为“通过”，从系统打印付款申请单，与原始票据一起邮寄回总部<br>5. 等待财务付款'); ?>
                                 <hr/>
                                 <div class="col-md-12"></div>
                                 <?php
                                 $this->widget('ext.ivyCGridView.BsCGridView', array(
                                     'id' => 'expenseList2',
                                     'afterAjaxUpdate' => 'js:head.Util.modal',
                                     'dataProvider' => $model2,
                                     'template' => "{items}{pager}",
                                     //状态为无效时标红
                                     //'rowCssClassExpression' => '( $data->status == 0 ? "active" : "" )',
                                     'colgroups' => array(
                                         array(
                                             "colwidth" => array(100, 100, 100, 100, 100, 100, 80, 100, 120),
                                         )
                                     ),
                                     'columns' => array(
                                         array(
                                             'name' => 'voucherid',
                                             'value' => 'sprintf("%08d",$data->id)',
                                         ),
                                         array(
                                             'name' => 'title',
                                             'value' => '$data->title',
                                         ),
                                         array(
                                             'name' => 'expense_uid',
                                             'value' => array($this, "getUsersName"),
                                         ),
                                         array(
                                             'name' => 'amount',
                                             'value' => '$data->amount',
                                         ),
                                         array(
                                             'name' => 'payee_user',
                                             'value' => '$data->payee_user',
                                         ),
                                         array(
                                             'name' => 'payee_account',
                                             'value' => '$data->payee_account',
                                         ),
                                         array(
                                             'name' => 'status',
                                             'type' => 'raw',
                                             'value' => array($this, "getStatus"),
                                         ),
                                         array(
                                             'name' => 'created',
                                             'value' => 'date("Y-m-d", $data->created)',
                                         ),
                                         array(
                                             'name' => Yii::t('asa', '操作'),
                                             'value' => array($this, "getButton"),
                                         ),
                                     ),
                                 ));
                                 ?>
                 </div>
            </div>
        </div>
    </div>
     <div class="modal fade" id="delcfmModel">  
      <div class="modal-dialog">  
        <div class="modal-content message_align">  
          <div class="modal-header">  
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>  
            <h4 class="modal-title">提示信息</h4>  
          </div>  
          <div class="modal-body">  
            <p>如需为第三方公司申请课程分成费用，请进入费用申请页面。</p>  
          </div>  
          <div class="modal-footer">  
             <input type="hidden" id="url"/>  
              <a href="<?php echo $this->createUrl('expenesAdd', array('type' => 2)) ?>" class="J_modal btn btn-primary pull-right ml15"
                                            target="_blank" data-dismiss="modal">否</a> 

              <a href="<?php echo $this->createUrl('pay/fee'); ?>" class="btn btn-primary pull-right">是</a> 
          </div>  
        </div><!-- /.modal-content -->  
      </div><!-- /.modal-dialog -->  
    </div><!-- /.modal -->  
    <script>
        var modal = '<div class="modal fade" id="modal" class="1123" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog modal-lg" role="document"><div class="modal-content"></div></div></div>';
        $('body').append(modal);

        function cbExpenesList() {
            $.fn.yiiGridView.update('expenseList');
            $.fn.yiiGridView.update('expenseList2');
        }

        function addExpense(id, status) {
            $.ajax({
                type: "POST",
                url: '<?php echo $this->createUrl("expenesStatusUpdate", array("expenesId" => $data->id)); ?>',
                data: {id: id, status: status},
                dataType: "json",
                success: function (data) {
                    if (data.state == 'success') {
                        $.fn.yiiGridView.update('expenseList');
                        $.fn.yiiGridView.update('expenseList2');
                    }
                    if (data.state == 'fail') {
                        resultTip({error: 'warning', msg: data.message});
                    }
                }
            });
        }

        function cbUpdateExpense(){
            $('#modal').modal('hide');
            $.fn.yiiGridView.update('expenseList');
        }
    </script>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>