<?php
    $command = Yii::app()->db->createCommand();
    $command->from(EClassroom::model()->tableName());
    $command->where('schoolid=:schoolid', array(':schoolid' => $this->branchId));
    $command->order('code asc');
    $classroomList = $command->queryAll();

    $statsList = EClassroom::getStatus();
    $labels = EClassroom::model()->attributeLabels();
?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array(''))?></li>
        <li class="active"><?php echo Yii::t('site','Routines');?></li>
    </ol>
    <div class="row">
        <div class="col-md-2">
            <div class="btn-group mb10">
                <button type="button" class="btn btn-primary" onclick="createClassroom(0);">
                    <?php echo Yii::t('campus', 'Create Classroom');?>
                </button>
            </div>

            <div class="list-group" id="classroom-status-list">
                <a data-vfilter="-1" href="javascript:void(0)" class="list-group-item status-filter" onclick="filterStatus(-1);"><?php echo Yii::t('global', 'All');?> <span class="badge">19</span></a></li>
                <?php foreach ($statsList as $key => $val):?>
                    <a data-vfilter="<?php echo $key; ?>" href="javascript:void(0)" class="list-group-item status-filter" onclick="filterStatus(<?php echo $key; ?>);"><?php echo $val;?> <span class="badge">19</span></a></li>
                <?php endforeach;?>
            </div>
        </div>

        <div class="col-md-10">
            <table class="table table-hover">
                <thead>
                <tr class="active">
                    <th width="120"><?php echo $labels['code'];?></th>
                    <th width="80"><?php echo $labels['capacity'];?></th>
                    <th width="140" class="text-center"><?php echo $labels['is_multifunc'];?></th>
                    <th width="140" class="text-center"><?php echo $labels['is_hall'];?></th>
                    <th width="140" class="text-center"><?php echo $labels['is_lib'];?></th>
                    <th width="150" class=""><?php echo $labels['status'];?></th>
                    <th><?php echo $labels['desc'];?></th>
                    <th width="100"><?php echo Yii::t('global','Action');?></th>
                </tr>
                </thead>
                <tbody id="classroom_list">

                </tbody>
            </table>
        </div>
    </div>
    <div class="row">

    </div>
</div>
<!-- Modal -->
<div class="modal" id="classroomEditModal" tabindex="-1" role="dialog" aria-labelledby="schoolbusEditModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel">教室信息 <small></small></h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="well">
                            说明
                        </div>
                        <form class="form-horizontal J_ajaxForm" action="<?php echo $this->createUrl('//mcampus/classroom/saveClassroom');?>" method="POST">
                            <div id="form-data">
                                <!--place holder-->
                            </div>

                            <div class="pop_bottom">
                                <button onclick="$('#classroomEditModal').modal('hide')" type="button" class="btn btn-default pull-right"><?php echo Yii::t('global','Cancel');?></button>
                                <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php
    $this->branchSelectParams['extraUrlArray'] = array('//mcampus/classroom/index');
    $this->renderPartial('//layouts/common/branchSelectBottom');
    $this->renderPartial('_classroom_template',array('statsList'=>$statsList));
?>
<script>
var classroomListTemplate = _.template($('#classroom-list-template').html());
var classroomAddTemplate = _.template($('#classroom-add-template').html());
var classroomList = <?php echo CJSON::encode($classroomList);?>;
var statsList = <?php echo CJSON::encode($statsList);?>;
var oStat = -1; //当前过滤状态
var statusGroupedClassroom = null;
$(function(){
    //初始化教室显示
    var _container = $('#classroom_list');
    displayClassroomList = function(){
        _container.empty();
        if (!_.isEmpty(classroomList)){
            $.each(classroomList, function(m, item){
                _container.append(classroomListTemplate(item));
            })
        }
        _container.find('tr[data-status|="0"]').addClass('warning');
        renderStatsBadge();
        filterStatus(oStat)
    }

    renderStatsBadge = function(){
        statusGroupedClassroom = _.groupBy(classroomList, 'status');
        $('#classroom-status-list').find('a[data-vfilter|="-1"]').find('span.badge').html(classroomList.length);
        _.each(statsList, function(item,index){
            $('#classroom-status-list').find('a[data-vfilter|="'+index+'"]').find('span.badge').html(_.isUndefined(statusGroupedClassroom[index])?0:statusGroupedClassroom[index].length);
        })
    }

    //根据教室状态过滤
    filterStatus = function(stat){
        oStat = stat;
        $('#classroom-status-list a').removeClass('active');
        $('#classroom-status-list a[data-vfilter|="'+oStat+'"]').addClass('active');
        if(oStat == -1){
            $('#classroom_list').find('tr').show();
        }else{
            $('#classroom_list').find('tr').hide();
            $('#classroom_list').find('tr[data-status|="'+oStat+'"]').show();
        }
    }

    //删除教室信息回调函数
    deleteCallback = function(id){
        classroomList = _.indexBy(classroomList,"id");
        delete classroomList[id];
        classroomList = _.sortBy(classroomList,"code");
        displayClassroomList();
        head.Util.ajaxDel();
    }

    //更新教室信息回调函数
    updateCallback = function(data){
        $('#classroomEditModal').modal('hide');
        classroomList = _.indexBy(classroomList,"id");
        if (!_.isEmpty(classroomList)){
            classroomList[data.id] = data;
        }else{
            classroomList[data.id] = data;
        }
        classroomList = _.sortBy(classroomList,"code");
        displayClassroomList();
        head.Util.ajaxDel();
    }

    displayClassroomList();
    head.Util.ajaxDel();

    //初始化教室添加页面
    createClassroom = function(id){
        var classroomInfo = {};
        if (id == 0){
            classroomInfo={id:0,code:'',capacity:'',is_multifunc:'',is_hall:'',is_lib:'',status:'',desc:''};
        }else{
            classroomList = _.indexBy(classroomList,"id");
            classroomInfo = classroomList[id];
            classroomList = _.sortBy(classroomList,"code");
        }
        openSchoolbusEditModal(classroomInfo);
    }
    //显示添加教室页面
    openSchoolbusEditModal = function(classroomInfo){
        $('#J_fail_info').remove();
        $('#classroomEditModal').modal();
        var _formData = classroomAddTemplate(classroomInfo);
        $('#form-data').html(_formData);
        if (classroomInfo.id > 0){
            if (classroomInfo.status == 1){
                $('#form-data #EClassroom_status_0').attr("checked",true);
            }else{
                $('#form-data #EClassroom_status_1').attr("checked",true);
            }
            if (classroomInfo.is_multifunc == 1){
                $('#form-data #EClassroom_type_0').attr("checked",true);
            }
            if (classroomInfo.is_hall == 1){
                $('#form-data #EClassroom_type_1').attr("checked",true);
            }
            if (classroomInfo.is_lib == 1){
                $('#form-data #EClassroom_type_2').attr("checked",true);
            }
        }
    };

})
</script>