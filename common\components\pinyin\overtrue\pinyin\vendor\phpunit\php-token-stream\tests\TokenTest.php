<?php
/*
 * This file is part of the PHP_TokenStream package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

/**
 * Tests for the PHP_Token class.
 *
 * @package    PHP_TokenStream
 * @subpackage Tests
 * <AUTHOR> <<EMAIL>>
 * @copyright  <PERSON> <<EMAIL>>
 * @license    http://www.opensource.org/licenses/BSD-3-Clause  The BSD 3-Clause License
 * @version    Release: @package_version@
 * @link       http://github.com/sebastian<PERSON>mann/php-token-stream/
 * @since      Class available since Release 1.0.0
 */
class PHP_TokenTest extends PHPUnit_Framework_TestCase
{
    /**
     * @covers PHP_Token::__construct
     * @covers PHP_Token::__toString
     */
    public function testToString()
    {
        $this->markTestIncomplete();
    }

    /**
     * @covers PHP_Token::__construct
     * @covers PHP_Token::getLine
     */
    public function testGetLine()
    {
        $this->markTestIncomplete();
    }
}
