<!-- 加载底部导航栏 -->
<?php
$this->branchSelectParams['extraUrlArray'] = array('//mgrades/attendance/index');
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<style>
    .cflex1 {
        flex: 1;
        overflow: hidden;
    }
    
    .clear:after{/*清除浮动*/
        content: '';
        display:block;
        clear: both;
    }
    .ml-3{
        margin-left:.3rem
    }
    .selectTeacher{
        width: 745px;
        max-height: 300px;
        position: absolute;
        background: #fff;
        border: 1px solid #E8EAED;
        border-radius: 3px;
        top:35px;
        overflow-y: auto;
        z-index:1;
        left:0px;
        padding:0 8px 16px 8px ;
        box-shadow: 0px 0px 6px 0px rgba(0,0,0,0.04), 0px 2px 4px 0px rgba(0,0,0,0.12);
    }
    .triangle{
        border-top-width: 0;
        border-bottom-color: #ebeef5;
        position: absolute;
        display: block;
        width: 0;
        height: 0;
        border-color: transparent;
        border-style: solid;
        top: -6px;
        left: 50%;
        margin-right: 3px;
        border-top-width: 0;
        border-bottom-color: #ebeef5;

    }
    .triangle:after{
        position: absolute;
        display: block;
        width: 0;
        height: 0;
        border-color: transparent;
        border-style: solid;
        content: " ";
        border-width: 6px;
        top: 1px;
        margin-left: -6px;
        border-top-width: 0;
        border-bottom-color: #fff;
    }
    .block{
        display: block !important
    }
    .align-items{
        align-items:center;
        background: #FAFAFA;
        padding:8px 0 8px 8px
    }
    .font16{
        font-size:16px
    }
    .selected{
        opacity: 0;

    }
    .image{
        width: 44px;
        height: 44px;
        object-fit: cover;
    }
    .selected:after{
        position: absolute;
        right:10px;
        font-family: element-icons;
        content: "\e79c";
        font-size: 15px;
        font-weight: 700;
        -webkit-font-smoothing: antialiased;
        top:10px
    }
    .el-select-dropdown__item {
        height:auto;
        padding:5px 0
    }
    .el-select-dropdown__item input[type="checkbox"]{
        opacity: 0;
    }
    .el-select-dropdown__item input[type="checkbox"]:checked + span {
        opacity: 1;
        color:#5CB85C
    }
    .bgSelect {
        background:#DFF0D8;
        border-radius: 3px;
    }
    .text_overflow{
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        line-height:14px;
    }
    .selectNum{
        position: absolute;
        right: -5px;
        top: -10px;
        width: 18px;
        height: 18px;
        background: #D9534F;
        color: #fff;
        border-radius: 50%;
        text-align: center;
        line-height: 18px;
        font-size: 12px;
    }
    .searchPosition{
        position: absolute;
        top: 9px;
        left: 10px;
        color: #ccc;
    }
    .selectPosition{
        position: absolute;
        right: 10px;
        top: 8px;
        font-size: 14px;
    }
    .pl30{
        padding-left:30px
    }
    .modal-body{
        padding:24px
    }
    .modal{
        top:40px
    }
    .childList {
        max-height: 250px;
        min-height: 40px;
        padding: 14px;
        overflow-y: auto;
    }
    .border {
        border: 1px solid #E8EAED;
        border-radius: 4px;
    }
    .image42 {
        width: 42px;
        height: 42px;
        object-fit: cover;
    }
    .addChild {
        font-size: 17px;
        color: #409EFF;
        width: 16px;
        height: 16px;
        display: inline-block;
        border: 1px solid #409EFF;
        text-align: center;
        line-height: 15px;
        border-radius: 50%;
    }
    .allCheck {
        position: absolute;
        right: 10px;
        top: 4px;
    }
    .listMedia {
        padding: 8px;
        border: 1px solid #fff;
        max-height: 62px;
        margin: 0;
    }
    .listMedia:hover{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        border: 1px solid #4D88D2;
        cursor: pointer;
    }
</style>
<script>
    var teachers = <?php echo CJSON::encode($teachers); ?>;
    var projects = [];
</script>
<div class="container-fluid">
    <!-- 面包屑导航 -->
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', 'G6-12 Schedule Management'); ?></li>
    </ol>
    <div class="row">
        <div class="col-md-2">
            <?php
                $this->widget('zii.widgets.CMenu',array(
                    'items'=> $this->siderMenu,
                    'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-right background-gray mb10'),
                    'activeCssClass'=>'active',
                    'itemCssClass'=>''
                ));
            ?>
        </div>
        <div class="col-md-10">
            <h3><?php echo Yii::t('attends','Course-Teacher Assignment');?></h3>
            <h4 class="clear">
                <?php $this->renderPartial('_select'); ?>
                <div class="col-md-11 col-sm-11">
                </div>
                <button type="button" class="btn btn-default btn-sm" onclick="showAddCourse()" style="float: right;margin-bottom: 15px">
                    <span class="glyphicon glyphicon-plus" aria-hidden="true"></span>
                    <?php echo Yii::t('asa', 'Create a course'); ?>
                </button>
            </h4>

        </div>
        <div class="col-md-1">
            <div class="list-group">
                <?php foreach ($courseType as $id => $item) : ?>
                    <span class="list-group-item <?php echo $id == '0' ? 'active' : '' ?>" onClick="switchType(this, '<?php echo $id ?>')"><?php echo $item; ?></span>
                <?php endforeach; ?>
            </div>
        </div>
        <div class="col-md-9">
            <table class="table table-bordered" id="course-table">
                <colgroup>
                  <col width="100">
                  <col width="">
                  <col width="250">
                  <col width="150">
                  <col width="100">
                </colgroup>
                <thead>
                    <tr id='theadTr'>
                        <th><?php echo Yii::t('attends', 'Course Code'); ?></th>
                        <th><?php echo Yii::t('attends', 'Course Title'); ?></th>
                        <th><?php echo Yii::t('attends', 'Assign Teacher'); ?></th>
                        <th><?php echo Yii::t('attends', 'Assign Students'); ?></th>
                        <th><?php echo Yii::t('attends', 'Action'); ?></th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
    </div>
</div>


<div id="teaceher-modal" class="modal fade bs-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
          <h4 class="modal-title" id="myModalLabel"><?php echo Yii::t('attends', 'Assign Course Primary Teacher'); ?></h4>
        </div>
        <div class="modal-body">
            <div class="form-group" >
                <p id="assigned"></p>
            </div>
            <div class="form-inline">
                <div class="form-group">
                    <input type="hidden" id="teacherId" name="teacherId" value="">
                    <input type="text" id="project" name="project" class="form-control"
                           placeholder="<?php echo Yii::t("attends", "Input name to filter"); ?>"
                           value="">
                </div>
               <button onclick="update2()" type="submit" class="btn btn-primary"><?php echo Yii::t('attends', 'Assign'); ?></button>
            </div>
            <hr>
            <?php foreach ($teachers as $teacherid => $teacher) :?>
                <script>
                    teacherid = <?php echo $teacherid; ?>;
                    teachername = '<?php echo $teacher->getName(); ?>';
                    teachers[teacherid].name = teachername;
                    projects.push({teacherid:teacherid, value:teachername});
                </script>
                <button onClick="update(<?php echo $teacherid; ?>)" class="mb5 btn btn-default"><?php echo $teacher->getName(); ?></button>
            <?php endforeach; ?>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
        </div>
    </div>
  </div>
</div>

<div id="student-modal" class="modal fade bs-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="student-model-title"><?php echo Yii::t('attends', '分配课程学生'); ?></h4>
            </div>
            <?php $form=$this->beginWidget('CActiveForm', array(
                'id'=>'form-1',
                'enableAjaxValidation'=>false,
                'htmlOptions'=>array('class'=>'J_ajaxForm', 'role'=>'form'),
                'action'=>$this->createUrl('saveStudent')
            )); ?>
            <div class="modal-body">
                <h5 style="font-size: 14px;"><?php echo Yii::t('attends', 'Student Search'); ?></h5>
                <div class="mb10">
                    <?php $this->widget('ext.search.ChildSearchBox', array(
                        'acInputCSS' => 'form-control',
                        'allowMultiple' => false,
                        'simpleDisplay' => false,
                        'extendCss' => false,
                        'useModel' => false,
                        'withAlumni' => true,
                        'name' => 'sid',
                        'htmlOptions' => array('class'=>'form-control'),
                    )) ?>
                    <input type="hidden" name='id' id="cc_id" value=''>
                    <input type="hidden" name='code' id="cc_code" value=''>
                </div>
                <div class="text-right">
                    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('attends', 'Add To Course'); ?></button>
                </div>
                <h5 style="font-size: 14px;"><?php echo Yii::t('attends', 'Student List'); ?></h5>
                <div class="row" id="student_items"></div>
            </div>
            <?php $this->endWidget(); ?>
        </div>
    </div>
</div>

<div id="course-modal" class="modal fade bs-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
          <h4 class="modal-title" id="myModalLabel"><?php echo Yii::t('attends', 'Edit Course Title'); ?></h4>
        </div>
        <div class="modal-body">
            <div class="form-group form-inline">
                <label for="title_en"><?php echo Yii::t('attends', 'Course Code:'); ?></label>
                <input style="width: 90%;" type="text" class="form-control" id="course_code" placeholder="" disabled>
            </div>

           <div class="form-group form-inline">
             <label for="title_cn"><?php echo Yii::t('attends', 'Chinese Title:'); ?></label>
             <input style="width: 90%;" type="text" class="form-control" id="title_cn" placeholder="">
           </div>
           <div class="form-group form-inline">
             <label for="title_en"><?php echo Yii::t('attends', 'English Title:'); ?></label>
             <input style="width: 90%;" type="text" class="form-control" id="title_en" placeholder="">
           </div>
<!--           <div class="checkbox hide" id="isAlldiv">-->
<!--             <label>-->
<!--               <input type="checkbox" id="isAll"> --><?php //echo Yii::t('attends', 'Synchronous modification'); ?>
<!--             </label>-->
<!--           </div>-->
<!--           <div>-->
<!--               <ul class="list-group" id="course_item">-->
<!--               </ul>-->
<!--           </div>           -->
        </div>
        <div class="modal-footer">
           <button onclick="updateTitleAction()" class="btn btn-primary"><?php echo Yii::t('global', 'Save'); ?></button>
          <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
        </div>
    </div>
  </div>
</div>

<div id="course-add-modal" class="modal fade bs-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel"><?php echo Yii::t('attends', 'Add a Course'); ?></h4>
            </div>
            <div class="modal-body">
                <div class="form-group form-inline">
                    <label for="title_en"><?php echo Yii::t('attends', 'Course Code:'); ?></label>
                    <input style="width: 90%;" type="text" class="form-control" id="add_course_code" placeholder="">
                </div>
                <div class="form-group form-inline">
                    <label for="title_cn"><?php echo Yii::t('attends', 'Chinese Title:'); ?></label>
                    <input style="width: 90%;" type="text" class="form-control" id="add_title_cn" placeholder="">
                </div>
                <div class="form-group form-inline">
                    <label for="title_en"><?php echo Yii::t('attends', 'English Title:'); ?></label>
                    <input style="width: 90%;" type="text" class="form-control" id="add_title_en" placeholder="">
                </div>
            </div>
            <div class="modal-footer">
                <button onclick="addCourse()" class="btn btn-primary"><?php echo Yii::t('global', 'Save'); ?></button>
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
            </div>
        </div>
    </div>
</div>
<!--  aria-labelledby="wantSayModalLabel" data-backdrop="static" data-keyboard="false" -->
<div  id="container">
    <div  id="showMembers" class="modal fade bs-example-modal-lg" tabindex="-1" role="dialog"  >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content" >
                <div class="modal-header"  @click='hideFocus'>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel"><?php echo Yii::t('attends', 'Meet Members'); ?></h4>
                </div>
                <div class="modal-body">
                    <div class='font14'  @click='hideFocus'><label> <?php echo Yii::t('global', 'Search'); ?></label><span class='color6 ml20' v-if='checkList.length>0'><?php echo Yii::t('newDS', 'Subscribed'); ?>{{checkList.length}}人</span></div>
                    <div class='flex'>
                        <div class=" flex1 relative">
                            <div class="form-group">
                                <span class='glyphicon glyphicon-search searchPosition'></span>
                                <input type="text" class="form-control pl30" id="exampleInputAmount" placeholder="<?php echo  Yii::t('attends', 'Filter by name');  ?>" v-model="searchStu" @focus.stop="showFocus">
                                <!-- <i class="el-icon-arrow-down selectPosition" v-if='!showSelect'></i> -->
                                <i class="el-icon-circle-close selectPosition color9" v-if='searchStu!=""' @click='searchStu=""' ></i>
                            </div>
                            <div class='selectTeacher' v-if='showSelect'>
                                <span class='triangle'></span>
                                <div class="checkbox" v-if='searchStu==""'>
                                    <label class='font14'>
                                    <input type="checkbox" @click.stop='checkAll' v-model='checked'> <?php echo Yii::t('global', 'Select All'); ?>
                                    </label>
                                </div>
                                <div v-if='searchData.length==0'>
                                <p class='font14 color3 text-center mt20 color9'><?php echo Yii::t('ptc', 'No Data'); ?></p> 
                                </div>
                                <div v-else>
                                    <div class="checkbox block mb10" v-for='(list,index) in searchData'>
                                        <label class='flex el-select-dropdown__item pb5 pt5'  :class='isCheck(list.uid)?"bgSelect":""'>
                                            <div class='font14 color3 flex1 '>
                                                <div class="media">
                                                    <div class="media-left pull-left media-middle">
                                                        <a href="javascript:void(0)"><img :src="list.user_avatar" data-holder-rendered="true" class="media-object img-circle image"></a>
                                                    </div>
                                                    <div class="media-body mt5 media-middle">
                                                        <h4 class="media-heading font14 color3 text_overflow">{{list.name}}</h4> 
                                                        <div class="text-muted text_overflow font12">{{list.teacher_occupation}}</div>
                                                    </div>
                                                </div>
                                            </div> 
                                            <span class='teacherInput'>
                                                <input type="checkbox" :value="list.uid" v-model='checkList' @change='checkedData($event)'> 
                                                <span class='selected'></span> 
                                            </span> 
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class='relative'>
                            <button type="submit" class="btn btn-primary ml20" @click='joinMeeting' :disabled='checkList.length==0?true:false'><?php echo Yii::t('attends', 'Add to Meet'); ?></button>
                            <span class='selectNum' v-if='checkList.length>0'>{{checkList.length}}</span>
                        </div>
                    </div>
                    <div class='mt20' @click='hideFocus' v-if='Object.keys(checkTeacherList).length!=0'>
                        <p><label class='font14'><?php echo Yii::t('attends', 'Members');?></label> <span class="badge">{{Object.keys(checkTeacherList).length}}</span></p>
                        <div class='row'  :style="'max-height:'+ height +'px;overflow-y:auto'">
                            <div class='col-md-4 mb15' v-for='(list,key,index) in checkTeacherList'>
                                <div class='flex align-items'>
                                    <div class='cflex1'>
                                        <div class="media">
                                            <div class="media-left pull-left media-middle">
                                                <a href="javascript:void(0)"><img :src="list.photoUrl" data-holder-rendered="true" class="media-object img-circle image"></a>
                                            </div>
                                            <div class="media-body mt5 media-middle">
                                                <h4 class="media-heading font14 color3 text_overflow" :title='list.name'>{{list.name}}</h4> 
                                                <div class="text-muted text_overflow"  :title='list.hrPosition'>{{list.hrPosition}}</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class='font16 color9' style='width:25px;'><span class='el-icon-circle-close cur-p' @click='delTeacher(list)'></span></div>
                                </div>
                            </div>
                            <div class='clearfix'></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="addClassModal" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("newDS", "分配课程学生");?></h4>
                </div>
                <div class="modal-body">
                    <div class='row'>
                        <div class='col-md-6 col-sm-6 '>
                                <div class='mb10'>
                                    <input type="text" class="form-control" placeholder="<?php echo Yii::t("attends", "Student Search");?>" v-model="searchChild">
                                </div>
                                <div  class="scroll-box" id="class"  style='max-height:600px;overflow-y:auto'>
                                    <div v-if='searchChildList.length==0'>
                                        <div v-for='(list,index) in classList' class='relative mb16'>
                                            <p  @click='getChild(list)'>
                                                <span  class='font14 color606 cur-p'>{{list.title}} </span>
                                                <span class='glyphicon glyphicon-chevron-down ml5' v-if='classId!=list.classid'></span>
                                                <span class='glyphicon glyphicon-chevron-up ml5' v-else></span>
                                            </p>
                                            <p  class='allCheck' v-if='classId==list.classid'><button class="btn btn-primary pull-right btn-xs" type="button" @click='selectAll(list)'  v-if='list.childData && list.childData.length!=0'><?php echo Yii::t("global", "Select All");?></button></p>
                                            <div  class='border scroll-box mr10 childList' v-if='classId==list.classid'>
                                                <div v-if='!childLoading'>
                                                    <div class='' v-if='list.childData && list.childData.length!=0'>
                                                        <div class="media listMedia" v-for='(item,index) in list.childData'>
                                                            <div class="media-left pull-left media-middle">
                                                                <a href="javascript:void(0)">
                                                                    <img :src="item.photo" data-holder-rendered="true" class="media-object img-circle image42">
                                                                </a>
                                                            </div>
                                                            <div v-if='item.disabled' class="media-right pull-right text-muted mt15">
                                                                <span class='cur-p'><?php echo Yii::t("newDS", "Subscribed");?></span>
                                                            </div>
                                                            <div v-else class="media-right pull-right text-muted" @click='assignChildren(item)'>
                                                                <span class='cur-p addChild mt15'>+</span>
                                                            </div>
                                                            <div class="media-body media-middle">
                                                                <h4 class="media-heading font12 mt8">{{item.name}}</h4>
                                                                <div class="text-muted">{{item.className}}</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div v-else>
                                                        <div class='font12 text-muted text-center'><?php echo Yii::t("newDS", "no student in this class");?></div>
                                                    </div>
                                                </div>
                                                <div class='loading' v-else>
                                                    <span></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div v-else-if='searchChildList.length!=0'>
                                        <div class="media listMedia" v-for='(item,index) in searchChildList'>
                                            <div class="media-left pull-left media-middle">
                                                <a href="javascript:void(0)">
                                                    <img :src="item.avatar" data-holder-rendered="true" class="media-object img-circle image42">
                                                </a>
                                            </div>
                                            <div v-if='item.disabled' class="media-right pull-right text-muted mt15">
                                                <span class='cur-p '><?php echo Yii::t("newDS", "Subscribed");?></span>
                                            </div>
                                            <div v-else class="media-right pull-right text-muted" @click='assignChildren(item)'>
                                                <span class='cur-p addChild mt15'>+</span>
                                            </div>
                                            <div class="media-body media-middle">
                                                <h4 class="media-heading font12 mt8">{{item.name}}</h4>
                                                <div class="text-muted">{{item.className}}</div>
                                            </div>
                                        </div>
                                    </div>
                            </div>
                        </div>
                        <div class='col-md-6 col-sm-6 borderLeft'>
                            <p class='font14 color606'><?php echo Yii::t("newDS", " ");?>{{selected.length}}<?php echo Yii::t("newDS", " student(s) selected");?>
                                <!-- <button class="btn btn-primary pull-right btn-xs" v-if='selected.length!=0' type="button" @click='batchDel()'><?php echo Yii::t("newDS", "Remove All");?></button> -->
                            </p>
                            <div class='border scroll-box p10 overflow-y' style='height:600px'>
                                <div class="media listMedia" v-for='(list,index) in selected'>
                                    <div class="media-left pull-left media-middle">
                                        <!-- <a href="javascript:void(0)">
                                            <img :src="list.avatar" data-holder-rendered="true" class="media-object img-circle image42">
                                        </a> -->
                                    </div>
                                    <div class="media-right pull-right text-muted" v-if='!list.loading' @click='Unassign(list.childid,index)'>
                                        <span class='cur-p mt15 font16 el-icon-circle-close'></span>
                                    </div>
                                    <div class='childLoading' v-else>
                                        <span></span>
                                    </div>
                                    <div class="media-body media-middle">
                                        <h4 class="media-heading font12 mt8">{{list.name}}</h4>
                                        <div class="text-muted">{{list.className}}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="delChildModal" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" >
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("newDS", "Warning");?></h4>
                </div>
                <div class="modal-body">
                    <div v-if='delType=="one"'><?php echo Yii::t('directMessage', 'Proceed to remove?'); ?></div>
                    <div v-if='delType=="all"'><?php echo Yii::t('newDS', 'Proceed to remove all?'); ?></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
                    <button @click="delChild()" class="btn btn-primary"><?php echo Yii::t('global', 'OK'); ?></button>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/template" id="course-item-template">
    <tr>
        <td><%= course_code %></td>
        <td>
            <span class="<%= course_code %>-cn"><%= course_title %></span> <br> <span class="<%= course_code %>-en"><%= course_title_en %></span>
        </td>
        <td>
            <%if(teacher_name){  %>

                <button onClick="showTeacher(<%= course_id %>, '<%= course_code %>')"
                        class="btn btn-default btn-sm btn_<%= course_id %>" title="<?php echo Yii::t('attends', 'Assign Course Teacher'); ?>">
                    <span class="glyphicon glyphicon-user" aria-hidden="true"></span>
                    <span class='course-<%= course_id %>'><%= teacher_name %></span>
                </button>
                <a href="javascript:;" style="color: #d9534f">
                    <span class="glyphicon glyphicon-trash reset-<%= course_id %>"  onClick="ResetTeacher(<%= course_id %>)"></span>
                </a>
            <% }else{%>
                <button onClick="showTeacher(<%= course_id %>, '<%= course_code %>')"
                        class="btn btn-default btn-sm btn_<%= course_id %>" title="<?php echo Yii::t('attends', 'Assign Course Teacher'); ?>">
                    <span class="glyphicon glyphicon-user" aria-hidden="true"></span>
                    <span class='course-<%= course_id %>'><%= teacher_name %></span>
                </button>
            <% }%>
        </td>
        <td>
            <%if(ctypeid=='ME'){  %>
                <button onClick="showMembers(<%= course_id %>, '<%= course_code %>')" class="btn btn-default btn-sm" title="<?php echo Yii::t('attends', 'Assign Course Teacher'); ?>">
                    <span class="glyphicon glyphicon-user" aria-hidden="true"></span> 
                    <span><?php echo Yii::t('labels', 'Members'); ?></span>
                </button>
            <% }else{%>
                <button onClick="showStudent(<%= course_id %>, '<%= course_code %>')" class="btn btn-default btn-sm" title="<?php echo Yii::t('attends', 'Assign Course Teacher'); ?>">
                    <span class="glyphicon glyphicon-user" aria-hidden="true"></span> 
                    <span><?php echo Yii::t('attends', 'Students'); ?> <%if(course_count != undefined && course_count > 0){ %><span class="badge"><%= course_count %></span><% }%></span>
                </button>
            <% }%>
        </td>
        <td>
            <button onClick="updateTitle('<%= course_code %>')" class="btn btn-default btn-sm " title="<?php echo Yii::t('attends', 'Edit Course Title'); ?>">
                <span class="glyphicon glyphicon-edit" aria-hidden="true"></span> 
                <%if(ctypeid=='ME'){  %>
                    <span><?php echo Yii::t('attends', 'Meet Title'); ?></span>
                <% }else{%>
                    <span><?php echo Yii::t('attends', 'Course Title'); ?></span>
                <% }%>
        </button>
        </td>
    </tr>
</script>

<script type="text/template" id="student-item-template">
    <div class="col-md-4" id="ss_<%= childid%>">
        <div style="background-color: #F5F7FA;height: 55px; margin: 5px 0;padding: 10px;border-radius: 4px;position: relative;">
            <div style="margin-bottom: .6rem">
                <strong><%= name%></strong>
                <% if(child_status == 999){%>
                <span class="label label-danger"><?php echo Yii::t('child', 'Dropped Out'); ?></span>
                <%}%>
                <% if(status == 99){%>
                <span class="label label-warning">已在学生课表删除</span>
                <%}%>
            </div>
            <div><small class="text-muted"><%= className%></small></div>
            <div style="position: absolute;right: 10px;top: 20px;"><span class="glyphicon glyphicon-remove-circle text-danger" aria-hidden="true" onclick="delStudent(<%= childid%>)" title="Delete"></span></div>
        </div>
    </div>
</script>

<script>
    var courseTeacher = <?php echo CJSON::encode($coureTeacher); ?>;
    var allTeacher=<?php echo CJSON::encode($teachers); ?>;
    <?php foreach ($teachers as $teacherid => $teacher) :?>
    teacherid = <?php echo $teacherid; ?>;
    teachername = '<?php echo $teacher->getName(); ?>';
    teacher_occupation = '<?php echo $teacher->profile->occupation->getName(); ?>';
    teacher_avatar = '<?php echo OA::CreateOAUploadUrl('infopub/staff', $teacher->staffInfo->staff_photo) ?>';
    allTeacher[teacherid].name = teachername;
    allTeacher[teacherid].teacher_occupation = teacher_occupation;
    allTeacher[teacherid].user_avatar = teacher_avatar;
    <?php endforeach; ?>
    var ccourseid = 0;
    var ccoursecode = 0;
    var ctypeid = 0;
    var cteacherid = 0;
    var template = _.template($('#course-item-template').html());
    var data = courseTeacher["0"];
    var table = $('#course-table tbody');
    if (typeof data != 'undefined' && !_.isNull(data)) {
        $.each(data, function (_i, _v) {
            var view = template(data[_i]);
            table.append(view);
        })
    }
    // 选择教师自动提示
    $("#project").autocomplete({
        minLength: 0,
        source: projects,
        focus: function (event, ui) {
            $("#project").val(ui.item.value);
            cteacherid = ui.item.teacherid;
            return false;
        },
        select: function (event, ui) {
            $("#project").val(ui.item.value);
            cteacherid = ui.item.teacherid;
            return false;
        }
    });
    // 切换课程类型
    function switchType(_this, typeid) {
        //$.ajax({
        //    type: 'post',
        //    url: '<?php //echo $this->createUrl('AssignTeacher')?>//',
        //    data: {is_ajax:1},
        //    dataType: 'json',
        //    async: false
        //}).done(function(ret){
        //    data = ret;
        //    if (data.state == 'success') {
        //        courseTeacher =  data.data.coureTeacher
        //    }
        //    ctypeid = typeid;
        //    table.empty();
        //    $('.list-group-item').removeClass('active');
        //    $(_this).addClass('active');
        //    var data = courseTeacher[typeid];
        //    if (!_.isNull(data)) {
        //        $.each(data, function (_i, _v) {
        //            var view = template(data[_i]);
        //            table.append(view);
        //        })
        //    }
        //});
        ctypeid = typeid;
        var theadHtml=''
        if(ctypeid=='ME'){
            theadHtml="<th><?php echo Yii::t('attends', 'Course Code'); ?></th>"+
            "<th><?php echo Yii::t('attends', 'Course Title'); ?></th>"+
            "<th><?php echo Yii::t('attends', 'Owner'); ?></th>"+
            "<th><?php echo Yii::t('attends', 'Members'); ?></th>"+
            "<th><?php echo Yii::t('attends', 'Action'); ?></th>"
        }else{
            theadHtml="<th><?php echo Yii::t('attends', 'Course Code'); ?></th>"+
            "<th><?php echo Yii::t('attends', 'Course Title'); ?></th>"+
            "<th><?php echo Yii::t('attends', 'Assign Teacher'); ?></th>"+
            "<th><?php echo Yii::t('attends', 'Assign Students'); ?></th>"+
            "<th><?php echo Yii::t('attends', 'Action'); ?></th>"
        }
        $('#theadTr').html(theadHtml)
        table.empty();
        $('.list-group-item').removeClass('active');
        $(_this).addClass('active');
        var data = courseTeacher[typeid];
        if (!_.isNull(data)) {
            $.each(data, function (_i, _v) {
                var view = template(data[_i]);
                table.append(view);
            })
        }

    }

    function upCourseTeacher()
    {
        $.ajax({
            type: 'post',
            url: '<?php echo $this->createUrl('AssignTeacher')?>',
            data: {is_ajax:1},
            dataType: 'json',
            async: false
        }).done(function(ret){
            data = ret;
            if (data.state == 'success') {
                courseTeacher =  data.data.coureTeacher
            }
        });
    }

    function showAddCourse()
    {
        $("#course-add-modal").modal('show');
    }
    function addCourse()
    {
        var title_cn = $('#add_title_cn').val();
        var title_en = $('#add_title_en').val();
        var course_code = $('#add_course_code').val();
        $.ajax({
            type: 'post',
            url: '<?php echo $this->createUrl('addCourse')?>',
            data: {course_code: course_code, title_cn: title_cn, title_en: title_en},
            dataType: 'json',
            async: false
        }).done(function(ret){
            data = ret;
            if (data.state == 'success') {
                $("#course-add-modal").modal('hide');
                resultTip({"msg": "<?php echo Yii::t("message", "Data Saved!"); ?>", callback: function(){
                        reloadPage(window);
                }});
            } else {
                resultTip({"msg": data.message})
            }
        });
    }
    // 显示所有老师
    function showTeacher(courseid, coursecode) {
        ccourseid = courseid;
        ccoursecode = coursecode;
        cteacherid = courseTeacher[ctypeid][ccoursecode].teacher_id;
        $("#assigned").text("");
        if (courseTeacher[ctypeid][ccoursecode].teacher_name) {
            $("#assigned").text("<?php echo Yii::t('attends', 'Course has been assigned to '); ?>" + courseTeacher[ctypeid][ccoursecode].teacher_name + "<?php echo Yii::t('attends', ', new assgin will make a replacement.'); ?>");
        }
        $("#teaceher-modal").modal('show');
    }

    function ResetTeacher(courseid)
    {
        if (confirm('<?php  echo Yii::t("message", "Sure to delete?") ;?>') == true){
            if (!courseid) {
                alert('courseid Error');
            } else {
                $.ajax({
                    type: 'post',
                    url: '<?php echo $this->createUrl('RectTeacher')?>',
                    data: {
                        courseid: courseid,
                    },
                    dataType: 'json',
                }).done(function (res) {
                    if (res.state == 'success') {
                        upCourseTeacher();
                        $('.course-'+courseid).text('');
                        $('.reset-'+courseid).remove()
                    }
                })
            }
        }else{
            return false;
        }
    }

    // function showStudent(courseid, coursecode) {
    //     $('#cc_id').val(courseid)
    //     $('#cc_code').val(coursecode)
    //     $.ajax({
    //         type: 'get',
    //         url: '<?php echo $this->createUrl('showStudent')?>',
    //         data: {courseid: courseid, code: coursecode},
    //         dataType: 'json',
    //         async: false
    //     }).done(function (res) {
    //         $('#student_items').html('')
    //         $.each(res.data, function (i, v) {
    //             $('#student_items').append(_.template($('#student-item-template').html(), v))
    //         })
    //         $('#student-model-title').html(courseTeacher[ctypeid][coursecode].course_title_en)
    //         $("#student-modal").modal('show');
    //     })
    // }
    function showStudent(courseid, coursecode) {
        container.showStudent(courseid, coursecode)   
    }
    function cbSave(data) {
        $('#student_items').append(_.template($('#student-item-template').html(), data))
        $('#sid').html('')
    }

    function delStudent(sid) {
        if (confirm('确定删除学生吗？')) {
            var id = $('#cc_id').val()
            var code = $('#cc_code').val()
            $.ajax({
                type: 'post',
                url: '<?php echo $this->createUrl('delStudent')?>',
                data: {
                    id: id,
                    code: code,
                    sid: sid
                },
                dataType: 'json',
            }).done(function (res) {
                if (res.state == 'success') {
                    $('#ss_'+sid).remove()
                }else{
                    resultTip({error: 'warning',msg: res.message})
                }
            })
        }
    }

    // 更新教师
    function update(teacherid) {
        if (confirm('<?php echo Yii::t("attends", "Sure to proceed?"); ?>') == true){
            if (!ccourseid || !teacherid) {
                alert('courseid Error');
            } else {
                $.ajax({
                    type: 'post',
                    url: '<?php echo $this->createUrl('updateTeacher')?>',
                    data: {teacherid: teacherid, courseid: ccourseid},
                    dataType: 'json',
                    async: false
                }).done(function(ret){
                    data = ret;
                    if (data.state == 'success') {
                        $("#teaceher-modal").modal('hide');
                        $('.course-' + ccourseid).text(teachers[teacherid].name);
                        var typeid = ccoursecode.slice(0, 2);
                        courseTeacher[typeid][ccoursecode].teacher_id = teacherid;
                        courseTeacher[typeid][ccoursecode].teacher_name = teachers[teacherid].name;
                        var str = '<a href="javascript:;" style="color: #d9534f;margin-left: 8px">' +
                            '<span class="glyphicon glyphicon-trash" onClick="ResetTeacher('+ccourseid+')"></span></a>';
                        $(".btn_"+ccourseid).after(str);
                        resultTip({"msg": "<?php echo Yii::t("attends", "Assign successflly"); ?>"})
                    } else {
                        resultTip({"msg": "<?php echo Yii::t("attends", "Assign failed"); ?>"})
                    }
                });
            }
        }else{
            return false;
        }
    }

    function update2() {
        update(cteacherid);
    }

    // 更新标题
    function updateTitle(coursecode) {
        ccoursecode = coursecode
        var typeid = coursecode.slice(0, 2);
        var code = coursecode.slice(0, 5);
        // $('#course_item').empty();
        // $('#isAlldiv').addClass('hide');
        // $.each(courseTeacher, function (i, v) {
        //     $.each(v, function (i2, v2) {
        //         if (i != 0 && code == i2.slice(0, 5) && coursecode != i2) {
        //             $('#isAlldiv').removeClass('hide');
        //             $('#course_item').append('<li class="list-group-item"><b>' + v2.course_code + '</b> ' + v2.course_title +' '+ v2.course_title_en + '</li>')
        //         }
        //     });
        // });

        $("#title_cn").val(courseTeacher[typeid][coursecode].course_title);
        $("#title_en").val(courseTeacher[typeid][coursecode].course_title_en);
        $("#course_code").val(ccoursecode);
        $("#course-modal").modal('show');
    }

    // 更新标题动作
    function updateTitleAction() {
        // var is_all = $('#isAll').prop('checked') ? 1 : 0;
        var is_all = 0;
        var title_cn = $('#title_cn').val();
        var title_en = $('#title_en').val();

        $.ajax({
            type: 'post',
            url: '<?php echo $this->createUrl('updateCourse')?>',
            data: {coursecode: ccoursecode, title_cn: title_cn, title_en: title_en, is_all: is_all },
            dataType: 'json',
            async: false
        }).done(function(ret){
            data = ret;
            if (data.state == 'success') {
                for(var i = 0; i < data.data.length; i++){
                    var coursecode = data.data[i];
                    var typeid = coursecode.slice(0, 2);
                    courseTeacher[typeid][coursecode].course_title = title_cn;
                    courseTeacher[typeid][coursecode].course_title_en = title_en;
                    if (typeof courseTeacher[0] != 'undefined' && courseTeacher[0][coursecode]) {
                        courseTeacher[0][coursecode].course_title = title_cn;
                        courseTeacher[0][coursecode].course_title_en = title_en;
                    }

                    $('.' + coursecode + '-cn').text(title_cn);
                    $('.' + coursecode + '-en').text(title_en);
                }
                $("#course-modal").modal('hide');
                resultTip({"msg": "<?php echo Yii::t("message", "Data Saved!"); ?>"})
            } else {
                resultTip({"msg": "<?php echo Yii::t("message", "Data Saving Failed!"); ?>"})
            }
        });
    }
    function showMembers(courseid, coursecode) {
        container.meetCourseid=courseid
        container.meetCoursecode=coursecode
        $.ajax({
            type: 'post',
            url: '<?php echo $this->createUrl('showMeetTeacher')?>',
            data: {course_code: coursecode, course_id:courseid },
            dataType: 'json',
            async: false
        }).done(function(ret){
            data = ret;
            if (data.state == 'success') {
               container.teacherList=[]
               container.checkTeacherList=data.data
               for(var key in allTeacher){
                    if(!container.checkTeacherList[key]){
                        container.teacherList.push(allTeacher[key])
                    }
                }
                container.checkList=[]
                container.showSelect=false 
               $("#showMembers").modal('show');
            } else {
                resultTip({"msg": "<?php echo Yii::t("message", "Data Saving Failed!"); ?>"})
            }
        });
        
    }

    var container = new Vue({
        el: "#container",
        data: {
            searchStu:'',
            teacherList:[],
            checkList:[],
            showSelect:false,
            checked:false,
            meetCourseid:'',
            meetCoursecode:'',
            checkTeacherList:{},
            height:document.documentElement.clientHeight-330,
            selected:{},
            classList:[],
            classId:'',
            courseid: '',
            coursecode: '',
            delType:'',
            delId:'',
            delIndex:'',
            searchChild:'',
            searchChildList:[]
        },
        computed: {
            searchData: function() {
				var search = this.searchStu.toLowerCase();
				var searchVal = ''; //搜索后的数据
				if(search) {
					searchVal = this.teacherList.filter(function(product) {
						return Object.keys(product).some(function(key) {
							return String(product['name'].toLowerCase()).indexOf(search) !== -1;
						})
					})
					return searchVal;
				}
				return this.teacherList;
			}
        },
        watch: {
            searchChild:function(newVal,oldVal){
                clearTimeout(this.timeout)
                this.timeout = setTimeout(() => {
                    if(newVal!=''){
                        this.getSearchStudent()
                    }
                }, 1000)
            },
        },
        created:function(){
        },
        methods:{
            joinMeeting(){
                $.ajax({
                    type: 'post',
                    url: '<?php echo $this->createUrl('saveMeetTeacher')?>',
                    data: { course_id: this.meetCourseid, course_code:this.meetCoursecode,teacher_ids:this.checkList },
                    dataType: 'json',
                    async: false
                }).done(function(ret){
                    if (data.state == 'success') {
                        showMembers(container.meetCourseid,container.meetCoursecode)
                    } else {
                        resultTip({"msg": "<?php echo Yii::t("message", "Data Saving Failed!"); ?>"})
                    }
                });
            },
            checkAll(){
                this.checkList=[]
                if(this.checked){
                    this.teacherList.forEach((item,index) => {
                        this.checkList.push(item.uid)
                    })
                }
            },
            checkedData(e){
                if(e.target.checked){
                    if(this.checkList.length==this.teacherList.length){
                        this.checked=true
                    }
                }else{
                    this.checked=false
                }
            },
            isCheck(id){
                if(this.checkList.indexOf(id)!=-1){
                    return true
                }
            },
            showFocus(){
                this.showSelect=true
            },
            hideFocus(){
                this.showSelect=false
            },
            delTeacher(list){
                $.ajax({
                    type: 'post',
                    url: '<?php echo $this->createUrl('delMeetTeacher')?>',
                    data: { course_id: this.meetCourseid, course_code:this.meetCoursecode,teacher_id:list.uid },
                    dataType: 'json',
                    async: false
                }).done(function(ret){
                    data = ret;
                    if (data.state == 'success') {
                        showMembers(container.meetCourseid,container.meetCoursecode)
                        resultTip({
                            msg: data.state
                        });
                    } else {
                        resultTip({"msg": "<?php echo Yii::t("message", "Data Saving Failed!"); ?>"})
                    }
                });
            },

            showStudent(courseid,coursecode){
                this.courseid=courseid
                this.coursecode=coursecode
                var that=this
                $.ajax({
                    type: 'get',
                    url: '<?php echo $this->createUrl('middleClassList')?>',
                    data: {},
                    dataType: 'json',
                    async: false
                }).done(function (res) {
                    res.data.forEach(item => {
                        item.childData=[]
                    })
                    that.classList=res.data
                })

                $.ajax({
                    type: 'get',
                    url: '<?php echo $this->createUrl('showStudent')?>',
                    data: {courseid: courseid, code: coursecode},
                    dataType: 'json',
                    async: false
                }).done(function (res) {
                    that.selected=Object.values(res.data)
                    that.classId=''
                    that.delType=''
                    that.delId=''
                    that.delIndex=''
                    $("#addClassModal").modal('show');
                })
            },
            getChild(list){
                var that=this
                if(that.classId==list.classid){
                    that.classId=''
                    return
                }
                that.classId=list.classid
                this.classData=list
                var childId=[]
                if(this.selected!=null){
                    for(var i=0;i<this.selected.length;i++){
                        childId.push(this.selected[i].childid+'')
                    }
                }
                for(var i=0;i<that.classList.length;i++){
                    if(that.classList[i].classid==list.classid){
                        if(that.classList[i].childData.length!=0){
                            for(var j=0;j<that.classList[i].childData.length;j++){
                                if (childId.indexOf(that.classList[i].childData[j].id)!=-1) {
                                    that.classList[i].childData[j].disabled=true
                                }else{
                                    that.classList[i].childData[j].disabled=false
                                }
                            }
                            that.$forceUpdate()
                            return
                        }
                    }
                }
                that.childLoading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("/mteaching/journals/childList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        classId:list.classid
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            for(var i=0;i<data.data.length;i++){
                                data.data[i].className=list.title
                                data.data[i].stuLoading=true
                                if (childId.indexOf(data.data[i].id)!=-1) {
                                    data.data[i].disabled=true
                                }else{
                                    data.data[i].disabled=false
                                }
                            }
                            that.sortData(data.data)
                            for(var i=0;i<that.classList.length;i++){
                                if(that.classList[i].classid==list.classid){
                                    that.classList[i].childData=data.data
                                }
                            }
                            that.childLoading=false
                            that.$forceUpdate()
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            getSearchStudent(){
                let that = this
                this.searchStu=true
                var childId=[]
                if(this.selected!=null){
                    for(var i=0;i<this.selected.length;i++){
                        childId.push(+this.selected[i].childid)
                    }
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("/mteaching/journals/searchStudent") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        child_name:this.searchChild,
                        group: "MS"
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            for(var i=0;i<data.data.length;i++){
                                data.data[i].stuLoading=true
                                if (childId.indexOf(data.data[i].id)!=-1) {
                                    data.data[i].disabled=true
                                }else{
                                    data.data[i].disabled=false
                                }
                            }
                            that.searchChildList=data.data
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.searchStu=false
                        }
                    },
                    error: function(data) {
                        that.searchStu=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })  
            },
            sortData(list){
                <?php if (Yii::app()->language == 'zh_cn') { ?>
                    list.sort((a, b)=> a.name.localeCompare(b.name, 'zh'));
                <?php } else { ?>
                    list.sort((a, b) => a.name.charCodeAt(0) - b.name.charCodeAt(0));
                <?php } ?>
            },
            selectAll(list){
                var targetId=[]
                for(var i=0;i<list.childData.length;i++){
                    if (!list.childData[i].disabled) {
                        this.assignChildren(list.childData[i])
                    }
                }
            },
            assignChildren(list){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("saveStudent") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        sid:list.id,
                        id:  this.courseid,
                        code: this.coursecode
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            Vue.set(list, 'disabled', true);
                            that.selected.push(data.data)
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            batchDel(){
                this.delType='all'
                $("#delChildModal").modal('show');
            },
            Unassign(id,index){
                this.delType='one'
                this.delId=id
                this.delIndex=index
                $("#delChildModal").modal('show');
            },
            delChild(){
                if(this.delType=='all'){
                    for(var i=0;i<this.selected.length;i++){
                        this.delStudent(this.selected[i].childid,i)
                    }
                }else{
                    this.delStudent(this.delId,this.delIndex)
                }
            },
            delStudent(id,index){
                var that=this
                $.ajax({
                    type: 'post',
                    url: '<?php echo $this->createUrl('delStudent')?>',
                    data: {
                        id:  this.courseid,
                        code: this.coursecode,
                        sid: id
                    },
                    dataType: 'json',
                }).done(function (res) {
                    if (res.state == 'success') {
                        if(that.delType=='all'){
                            that.selected=[]
                        }else{
                            that.selected.splice(index,1)
                        }
                        for(var i=0;i<that.classList.length;i++){
                            for(var j=0;j<that.classList[i].childData.length;j++){
                                if(that.classList[i].childData[j].id==id){
                                    Vue.set(that.classList[i].childData[j], 'disabled', false);
                                }
                            }
                        }
                        $("#delChildModal").modal('hide');
                    }else{
                        resultTip({error: 'warning',msg: res.message})
                    }
                })
            }
        }
    })
</script>