<style>
    .table td {
        vertical-align: middle!important;
    }
    .table{
        color:#606266
    }
    .table tr th{
        background:#FAFAFA
    }
    [v-cloak] {
        display: none;
    }
    .ml-15{
        margin-left:-15px
    }
    .imgAvatar{
        border-radius:50%;
        width:60px;
        height:60px;
        border:1px solid #4D88D2;
    }
    .imgCover{
        width:100px;
        height:100px;
        object-fit:cover;
        border-radius:50%;
        border:1px solid #428bca
    }
    .invoiceList{
        border: 1px solid #ccc;
        border-radius: 4px;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        padding-left: 16px;
    }
    .font18{
        font-size:18px;
    }
    .font16{
        font-size:16px
    }
    .activeInput{
        background:#4D88D2;
        color: #fff;
    }
    .piont{
        width: 6px;
        height: 6px;
        background: #52C41A;
        display:inline-block;
        border-radius:50%;
        position: absolute;
        top: 12px;
    }
    .green{
        background: #52C41A; 
    }
    .red{
        background:#D9534F
    }
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Basic'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Admissions management'), array('//mcampus/admissions/index')) ?></li>
        <li class="active">录取学生转入</li>
    </ol>

    <div class="row">
        <!-- 左侧菜单 -->
        <div class="col-md-2 col-sm-2 mb10">
            <?php
            $this->widget('zii.widgets.CMenu',array(
                'items'=> $this->getMenu(),
                'id'=>'pageCategory',
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked background-gray'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>

        <div class="col-md-10 col-sm-12" id='datalist' v-cloak>
            <div class="mb20">
                <div class="pull-left">
                    <select class="form-control select_3" v-model='schoolYear' @change='schoolList()'>
                        <option v-for='(list,index) in yearList' :value='list'>{{list}}</option>
                    </select>
                </div>
                <div class="col-md-5">
                    <input type="text" class="form-control length_4" placeholder="请输入学生姓名"  v-model="search">
                </div>
                <div class='clearfix'></div>
            </div>
            <table class="table">
                <thead>
                    <tr>
                        <th>学生姓名</th>
                        <th>生日</th>
                        <th>监护人</th>
                        <th>转入日期</th>
                        <th>状态</th>
                        <th width=150>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for='(list,index) in searchData'>
                        <td>
                            <a href='javascript:void(0);' v-if='list.trans_childid!=""' @click='stuInfo(list.trans_childid)'>{{list.nameCn ? list.nameCn : list.firstName}}</a>
                            <span v-else>{{list.nameCn ? list.nameCn : list.firstName}}</span>
                        </td>
                        <td>{{list.birthday}}</td>
                        <td>
                            <div v-for="(item, key) in list.guardianList">
								{{ item.name }} {{ item.email }} {{item.mobile}}
								<br v-if="key + 1 != list.guardianList.length" />
							</div>
                        </td>
                        <td>{{list.trans_date}}</td>
                        <td> {{list.trans_childid!=''?'已转移':config.finalResult[list.finalResult]}}</td>
                        <td>
                            <a href="javascript:;" v-if='list.trans_childid==""' class="btn btn-xs btn-primary mr5" @click='transferIn(list)'>转入</a>
                            <a href="javascript:;" v-else class="btn btn-xs btn-primary mr5" @click='getInvoice(list)'>账单信息</a>
                        </td>
                    </tr>
                </tbody>
            </table>
            <div class="modal fade" id='transModel' tabindex="-1" role="dialog">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title">学生转入</h4>
                    </div>
                    <div class="modal-body">
                        <div>                        
                            <div  class="col-md-12 col-sm-12 mb15"><strong class='font14'>基本信息</strong></div>
                            <div  class="col-md-12 col-sm-12">
                                <img :src="childData.child_avatar_thumb" data-holder-rendered="true" class='imgAvatar mr20'>
                                <label class="radio-inline">
                                    <input type="radio" v-model='childData.is_legel_cn_name' value="1"> 法定名为中文
                                </label>
                                <label class="radio-inline">
                                    <input type="radio" v-model='childData.is_legel_cn_name' value="2"> 法定名为英文
                                </label>
                            </div>
                            <div v-if='childData.is_legel_cn_name==1'>
                                <div class="col-md-3 col-sm-3">
                                    <div class='color6 font12 mt15' >法定中文名字</div>
                                    <div class='font14 color3 mt5'>
                                        <input type="text" class="form-control" :value='childData.name_cn' v-model='childData.name_cn'>  
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-3">
                                    <div class='color6 font12 mt15' >法定姓（拼音）</div>
                                    <div class='font14 color3 mt5'>
                                        <input type="text" class="form-control" :value='childData.last_name_en' v-model='childData.last_name_en'> 
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-3">
                                    <div class='color6 font12 mt15' >法定名（拼音）</div>
                                    <div class='font14 color3 mt5'>
                                        <input type="text" class="form-control" :value='childData.first_name_en'  v-model='childData.first_name_en'> 
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-3">
                                    <div class='color6 font12 mt15' >昵称/英文名</div>
                                    <div class='font14 color3 mt5'>
                                        <input type="text" class="form-control" :value='childData.nick' v-model='childData.nick'> 
                                    </div>
                                </div>
                            </div>
                            <div v-if='childData.is_legel_cn_name==2'>
                                <div class="col-md-3 col-sm-3">
                                    <div class='color6 font12 mt15' >Legal Last Name</div>
                                    <div class='font14 color3 mt5'>
                                        <input type="text" class="form-control" :value='childData.last_name_en'  v-model='childData.last_name_en'> 
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-3">
                                    <div class='color6 font12 mt15' >Legal First Name</div>
                                    <div class='font14 color3 mt5'>
                                        <input type="text" class="form-control" :value='childData.first_name_en'  v-model='childData.first_name_en'> 
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-3">
                                    <div class='color6 font12 mt15' >Legal Middle Name</div>
                                    <div class='font14 color3 mt5'>
                                        <input type="text" class="form-control" :value='childData.middle_name_en' v-model='childData.middle_name_en'> 
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-3">
                                    <div class='color6 font12 mt15' >Preferred Name</div>
                                    <div class='font14 color3 mt5'>
                                        <input type="text" class="form-control" :value='childData.nick' v-model='childData.nick'> 
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-3">
                                    <div class='color6 font12 mt15' >Chinese Name</div>
                                    <div class='font14 color3 mt5'>
                                        <input type="text" class="form-control" :value='childData.name_cn'  v-model='childData.name_cn'> 
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-3">
                                <div class='color6 font12 mt15' >性别</div>
                                <div class='font14 color3 mt5'>
                                    <select class="form-control" v-model='childData.gender'>
                                        <option value='1'>男</option>
                                        <option value='2'>女</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-3">
                                <div class='color6 font12 mt15' >生日</div>
                                <div class='font14 color3 mt5'>
                                    <input type="text" class="form-control"  id="birthday" v-model='childData.birthday'  placeholder="<?php echo Yii::t("newDS", "Select a date");?>"  :value='childData.birthday'>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-3">
                                <div class='color6 font12 mt15' >国籍</div>
                                <div class='font14 color3 mt5'>
                                    <select class="form-control" v-model='childData.country'>
                                        <option v-for='(list,key,index) in configList.country' :value='key'>{{list}}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-3">
                                <div class='color6 font12 mt15' >身份证号</div>
                                <div class='font14 color3 mt5'>
                                    <input type="text" class="form-control" :value='childData.identity'  v-model='childData.identity'> 
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-3">
                                <div class='color6 font12 mt15' >母语</div>
                                <div class='font14 color3 mt5'>
                                    <select class="form-control" v-model='childData.lang'>
                                        <option v-for='(list,key,index) in configList.lang'  :value='key'>{{list}}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6">
                                <div class='color6 font12 mt15' >家庭住址</div>
                                <div class='font14 color3 mt5'>
                                    <input type="text" class="form-control" :value='childData.home_address' v-model='childData.home_address'> 
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-3">
                                <div class='color6 font12 mt15' >照片授权</div>
                                <div class="checkbox">
                                    <label>
                                        <input type="checkbox" :value='photoAuthorization' v-model='photoAuthorization'> 同意
                                    </label>
                                </div>
                            </div>
                            <div class='clearfix'></div>
                        </div>
                        <hr>
                        <div class="form-horizontal">                        
                            <div class="form-group">
                                <label for="inputEmail3" class="col-sm-2 control-label text-left">申请年级</label>
                                <div class="col-sm-10 mt10">
                                    {{gradeSchool(currentList.applyGrade)}}
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputPassword3" class="col-sm-2 control-label">预计上学日期</label>
                                <div class="col-sm-3"><input type="text" class="form-control"  id="start_date" v-model='childData.start_date'  placeholder="<?php echo Yii::t("newDS", "Select a date");?>"  :value='childData.start_date'>
                                </div>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                        <hr  class='col-md-12 col-sm-12 ml-15 mt5'>
                        <div class='pb10' v-if='slibingData.length==0'>                        
                            <div class='col-md-12 col-sm-12 '><strong class='font14'>监护人信息</strong></div>
                            <div class='mt15 col-md-12 col-sm-12'>监护人：父亲</div>
                            <div>
                                <div class="col-md-3 col-sm-3">
                                    <div class='color6 font12 mt15' >父亲姓名</div>
                                    <div class='font14 color3 mt5'>
                                        <input type="text" class="form-control"  v-model='childData.father_name' :value='childData.father_name'> 
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-3">
                                    <div class='color6 font12 mt15' >父亲手机</div>
                                    <div class='font14 color3 mt5'>
                                        <input type="text" class="form-control"  v-model='childData.father_phone' :value='childData.father_phone'> 
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-3">
                                    <div class='color6 font12 mt15' >父亲邮箱</div>
                                    <div class='font14 color3 mt5'>
                                        <input type="text" class="form-control"  v-model='childData.father_email' :value='childData.father_email'> 
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-3">
                                    <div class='color6 font12 mt15' >父亲国籍</div>
                                    <div class='font14 color3 mt5'>
                                        <select class="form-control" v-model='childData.father_nation'>
                                            <option v-for='(list,key,index) in configList.country' :value='key'>{{list}}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-3">
                                    <div class='color6 font12 mt15' >父亲单位</div>
                                    <div class='font14 color3 mt5'>
                                        <input type="text" class="form-control" v-model='childData.father_employer' :value='childData.father_employer'> 
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-3">
                                    <div class='color6 font12 mt15' >父亲职位</div>
                                    <div class='font14 color3 mt5'>
                                        <input type="text" class="form-control" v-model='childData.father_position' :value='childData.father_position'> 
                                    </div>
                                </div>
                            </div>
                            <div class='clearfix'></div>
                            <div class='mt15 col-md-12 col-sm-12'>监护人：母亲</div>
                            <div>
                                <div class="col-md-3 col-sm-3">
                                    <div class='color6 font12 mt15' >母亲姓名</div>
                                    <div class='font14 color3 mt5'>
                                        <input type="text" class="form-control"  v-model='childData.mother_name'  :value='childData.mother_name'> 
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-3">
                                    <div class='color6 font12 mt15' >母亲手机</div>
                                    <div class='font14 color3 mt5'>
                                        <input type="text" class="form-control"  v-model='childData.mother_phone'  :value='childData.mother_phone'> 
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-3">
                                    <div class='color6 font12 mt15' >母亲邮箱</div>
                                    <div class='font14 color3 mt5'>
                                        <input type="text" class="form-control"  v-model='childData.mother_email'  :value='childData.mother_email'> 
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-3">
                                    <div class='color6 font12 mt15' >母亲国籍</div>
                                    <div class='font14 color3 mt5'>
                                        <select class="form-control" v-model='childData.mother_nation'>
                                            <option v-for='(list,key,index) in configList.country' :value='key'>{{list}}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-3">
                                    <div class='color6 font12 mt15' >母亲单位</div>
                                    <div class='font14 color3 mt5'>
                                        <input type="text" class="form-control"  v-model='childData.mother_employer'  :value='childData.mother_employer'> 
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-3">
                                    <div class='color6 font12 mt15' >母亲职位</div>
                                    <div class='font14 color3 mt5'>
                                        <input type="text" class="form-control"  v-model='childData.mother_position'  :value='childData.mother_position'> 
                                    </div>
                                </div>
                            </div>
                            <div class='clearfix'></div>
                        </div>
                        <div v-else class='col-md-12 col-sm-12'>
                            <div class='mb15'><strong class='font14'>兄弟姐妹信息</strong></div>
                            <table class="table table-bordered ">
                                <thead>
                                    <tr>
                                        <th width='50'>操作</th>
                                        <th width="30%">姓名</th>
                                        <th width="30%">父亲信息</th>
                                        <th width="30%">母亲信息</th>
                                    </tr>
                                </thead>
                                <tbody  v-for='(list,index) in slibingData'>
                                    <tr v-for='(item,key) in list.childData'>
                                        <td>
                                            <input type="radio" v-model='siblingId' :value="item.childid"> 
                                        </td>
                                        <td >
                                            <a target="_blank" :href="'<?php echo $this->createUrl('/child/index/index'); ?>&childid='+item.childid">{{item.name}}</a> ({{item.birthday}})
                                        </td>
                                        <td >
                                            <div>{{list.fData.name}}</div>  
                                            <div>{{list.fData.mphone}}</div>  
                                            <div>{{list.fData.email}}</div>  
                                        </td>
                                        <td>
                                            <div>{{list.mData.name}}</div>  
                                            <div>{{list.mData.mphone}}</div>
                                            <div>{{list.mData.email}}</div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <div class="mb10">
                            <strong class='pull-left color3' style='line-height:30px'>转入类型</strong>
                            <select class="form-control select_3 pull-left ml20" v-model='transferType'>
                                    <option disabled='true' value='0'>请选择转入类型</option>
                                    <option value='conventional'>正常</option>
                                    <option  value='overwrite'>覆盖</option>
                                </select>
                                <div class='clearfix'></div>
                            </div>
                        </div>
                        <hr  class='col-md-12 col-sm-12 ml-15 mt5'>
                        <div class="col-md-12 col-sm-12">
                           <strong class='pull-left color3' style='line-height:30px'>转入班级</strong>
                           <select class="form-control select_3 pull-left ml20" v-model='childData.classid'>
                                <option disabled='true' value='0'>请选择转入班级</option>
                                <option v-for='(list,index) in classList' :value='list.classid'>{{list.title}}</option>
                            </select>
                            <div class='clearfix'></div>
                        </div>
                        <div class="col-md-12 col-sm-12 mt20">
                           <strong class='pull-left color3' style='line-height:30px'>绑定折扣</strong>
                           <select class="form-control select_3 pull-left ml20" v-model='childData.discount_id'>
                                <option disabled='true' value=''>请选择绑定折扣</option>
                                <option v-for='(list,index) in discountList' :value='list.id'>{{list.title}}</option>
                            </select>
                            <div class='clearfix'></div>
                        </div>
                        <div class='clearfix'></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                        <button type="button" class="btn btn-primary" @click='transConfirm' :disabled='btnDis'>转入</button>
                    </div>
                    </div><!-- /.modal-content -->
                </div><!-- /.modal-dialog -->
            </div><!-- /.modal -->
            <div class="modal fade" tabindex="-1" role="dialog" id='childInfo'>
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title"><?php echo Yii::t("site", "Student Information");?></h4>
                    </div>
                    <div class="modal-body">
                        <div class="col-md-12 borderRight text-center marginAuto mb20">
                            <img :src='stuData.photo' class="imgCover" alt="">
                            <p class='font14 fontBold mt10'>
                                <a  target="_blank" :href="'<?php echo $this->createUrl('/child/index/index'); ?>&childid='+childId">{{stuData.name}}</a> | {{childId}}</p>
                        </div>
                        <div  class="col-md-12 mb20 mt20">
                            <div  class="col-md-3">
                                <p class="font12 color6"> <?php echo Yii::t("labels", "Legal First Name");?></p>
                                <p class="font14 color3">{{stuData.first_name_en}}</p>
                            </div>
                            <div  class="col-md-3">
                                <p class="font12 color6"><?php echo Yii::t("labels", "Legal Middle Name");?></p>
                                <p class="font14 color3">{{stuData.middle_name_en}}</p>
                            </div>
                            <div  class="col-md-3">
                                <p class="font12 color6"><?php echo Yii::t("labels", "Legal Last Name");?></p>
                                <p class="font14 color3">{{stuData.last_name_en}}</p>
                            </div>
                            <div  class="col-md-3">
                                <p class='font12 color6'><?php echo Yii::t("labels", "Preferred Name");?></p>
                                <p class='font14 color3'>{{stuData.nick}}</p>
                            </div>
                        </div>
                        <div  class="col-md-12 ">
                            <div  class="col-md-3">
                                <p class="font12 color6"><?php echo Yii::t("labels", "Nationality");?></p>
                                <p class="font14 color">{{stuData.country}}</p>
                            </div>
                            <div  class="col-md-3">
                                <p class="font12 color6"><?php echo Yii::t("labels", "Date of Birth");?></p> 
                                <p class="font14 color">{{stuData.birthday_search}}</p>
                            </div>
                            <div  class="col-md-3">
                                <p class="font12 color6"><?php echo Yii::t("labels", "Gender");?></p> 
                                <p class="font14 color">{{stuData.gender==1?"男":"女"}}</p>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                        <hr>
                        <div  class="col-md-12">
                        <div  class="col-md-3">
                                <p class="font12 color6"><?php echo Yii::t("labels", "Bus NO.");?></p>
                                <p class="font14 color">{{stuData.busId}}</p>
                            </div>
                            <div  class="col-md-9">
                                <p class="font12 color6"><?php echo Yii::t("labels", "Bus Notes");?></p>
                                <p class="font14 color">{{stuData.BusContent}}</p>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                        <hr>
                        <div  class="col-md-12 mb20">
                            <div  class="col-md-3">
                                <p class="font12 color6"><?php echo Yii::t("labels", "Father");?></p>                           
                                <p class="font14 color">{{stuData.fName}}</p>
                            </div>
                            <div  class="col-md-3">
                                <p class="font12 color6"><?php echo Yii::t("labels", "Mobile Phone");?></p>
                            <p class="font14 color">{{stuData.fTel}}</p>
                        </div>
                            <div  class="col-md-6">
                                <p class="font12 color6"><?php echo Yii::t("child", "Email");?></p>
                                <p class="font14 color">{{stuData.fEmail}}</p>
                            </div>
                        </div>
                        <div  class="col-md-12 ">
                            <div  class="col-md-3">
                                <p class="font12 color6"><?php echo Yii::t("labels", "Mother");?></p>
                            <p class="font14 color">{{stuData.mName}}</p>
                            </div>
                            <div  class="col-md-3">
                                <p class="font12 color6"><?php echo Yii::t("labels", "Mobile Phone");?></p>
                                <p class="font14 color">{{stuData.mTel}}</p>
                            </div>
                            <div  class="col-md-6">
                                <p class="font12 color6"><?php echo Yii::t("child", "Email");?></p>
                                <p class="font14 color">{{stuData.mEmail}}</p>
                            </div>
                        </div>
                         <hr>
                        <div class="clearfix"></div>
                    </div>
                    </div>
                </div>
            </div>     
            <div class="modal fade" tabindex="-1" role="dialog" id='invoiceInfo'>
                <div class="modal-dialog" role="document" v-if='Object.keys(invoiceData).length!=0'>
                    <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title"><?php echo Yii::t("site", "账单信息");?></h4>
                    </div>
                    <div class="modal-body" >
                        <div class='font14'> 
                            <p class='font16'><strong>生成账单</strong></p>
                            <div v-if='Object.keys(invoiceData.transedInvoice).length==0'>
                                <div>选择要生成得账单 </div>
                                <div class='mt20' v-for='(list,index) in invoiceData.unTransInvoice'>
                                    <div class="input-group">
                                        <span class="input-group-addon">
                                            <input type="radio" :id="index"  v-model='type' :value="index">
                                        </span>
                                        <div class='invoiceList'>
                                            <div class='mt5'>{{list.title}}</div> 
                                            <div class='mb5'> <strong class='font18'>￥{{(list.amount/100).toFixed(2) + ''}}</strong></div>
                                        </div>
                                        
                                    </div>
                                    <!-- <label :for="index" class="invoiceList" :class='type==index?"activeInput":""'>
                                        <input type="radio"  :id="index"  v-model='type' :value="index" placeholder="Password">
                                        <div style='margin-top:-10px'> <strong class='font18'>￥{{(list.amount/100).toFixed(2) + ''}}</strong></div>
                                        <div class='pb10'>{{list.title}}</div> 
                                    </label>      -->
                                </div>
                                <div class='clearfix'></div>
                                <div class='mt20 mb5'>账单折扣</div>
                                <select class="form-control" v-model='discountId'>
                                    <option value='0'>请选择</option>
                                    <option v-for='(list,key,index) in invoiceData.discounts' :value='key'>{{list}}</option>
                                </select>
                                <div class='mt20'><button type="button" class="btn btn-primary"  @click='transInvoice()'>生成账单</button></div>
                            </div>
                            <div v-else class='mb20'>
                                <p><span class='glyphicon glyphicon-ok-sign' style='color:#52C41A'></span><span class='ml10'>账单已生成</span></p>
                                <div style='background:#E5E7EB;padding:16px;border-radius:5px' class='ml20'>
                                    <p><a :href="invoiceData.transedInvoice.link" target=_blank>{{invoiceData.transedInvoice.title}}</a> </p>
                                    <div class='font18'><strong>￥{{invoiceData.transedInvoice.amount}}</strong></div>
                                </div>
                               
                            </div>
                        </div>
                        <hr  v-if='invoiceData.paidItems.length!=0'>
                        <div v-if='invoiceData.paidItems.length!=0'>
                            <p class='font14'><strong>已支付账单列表</strong></p>
                            <!-- v-if='invoiceData.paidItems.length!=0 && invoiceStatus()' -->
                            <button type="button" class="btn btn-primary mb10" v-if='invoiceData.paidItems.length!=0 && invoiceStatus() && Object.keys(invoiceData.transedInvoice).length!=0'  @click='syncOrder()'>同步订单</button>
                            <table class="table">
                                <thead>
                                    <tr>
                                    <th>支付时间</th>
                                    <th>支付金额</th>
                                    <th>支付渠道</th>
                                    <th>同步状态</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for='(list,index) in invoiceData.paidItems'>
                                        <td>{{list.payTime}}</td>
                                        <th scope="row">{{(list.amount/100).toFixed(2) + ''}}</th>
                                        <td>{{list.payChannel}}</td>
                                        <td class='relative'><span class='piont' :class='list.status==0?"red":"green"'></span><span class='ml10'>{{list.status==0?"未同步":"已同步"}}</span> </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>     
        </div>
    </div>
</div>
<script>
    // $('#invoiceInfo').modal('show')
    $(function() {
		$("#birthday").datepicker({
			dateFormat: "yy-mm-dd ",
		});
        $( "#birthday").on('change', function () {
            datalist.childData.birthday = $('#birthday').val();
        });
        $("#start_date").datepicker({
			dateFormat: "yy-mm-dd ",
		});
        $( "#start_date").on('change', function () {
            datalist.childData.start_date = $('#start_date').val();
        });
	});
    var yearList = <?php echo json_encode($yearList); ?>;
    var config = <?php echo json_encode($config); ?>;
    var discountList = <?php echo json_encode($discountList); ?>;
    var datalist = new Vue({
		el: "#datalist",
		data: {
            stuData:{},
            childId:'',
			jsondata:[],
            yearList:yearList,
            config:config,
            discountList:discountList,
			search: "",
            transClass:'',
            schoolYear:'',
            currentList:{},
            childData:{},
            classList:{},
            configList:{},
            slibingData:[],
            siblingId:'',
            btnDis:false,
            transferType:'conventional',
            invoiceData:{},
            discountId:'0',
            type:'-1',
            invoiceList:{}
		},
		created: function() {
			this.search = ''  
            this.schoolYear=yearList[0]
            if(this.schoolYear!=''){
                this.schoolList()
            }
		},
		computed: {
			searchData: function() {
				var search = this.search.toLowerCase();
				var searchVal = ''; //搜索后的数据
				if(search) {
					searchVal = this.jsondata.filter(function(product) {
						return Object.keys(product).some(function(key) {
							return String(product['nameCn']).indexOf(search) !== -1 || String(product['firstName']).toLowerCase().indexOf(search)  !== -1 ;
						})
					})
					return searchVal;
				}
				return this.jsondata;
			},
            photoAuthorization: {
                get() {
                    return this.childData.photo_authorization === 1;
                },
                set(value) {
                    this.childData.photo_authorization = value ? 1 : 0;
                }
            }
  
		},
		methods: {
            gradeSchool(list){
                if(list!=undefined && this.config.class[list]){
                    return this.config.class[list].split("[[@]]")[0]
                }
            },
            stuInfo(childId){
                this.childId=childId
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("student/childInfo") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        childid:childId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                        that.stuData=data.data
                        $('#childInfo').modal()
                        } else {
                            resultTip({  
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            schoolList(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("applyData") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        startyear:this.schoolYear
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.jsondata=data.data
                        } else {
                            resultTip({
                                error: 'warning',
                                msg:data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg:data.message
                        });
                    },
                })
            },
            transferIn(list){
                this.currentList=list
                this.siblingId=''
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("applyOne") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        applyId:list.applyId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.childData=data.data.childData
                           that.classList=data.data.classList
                           that.configList=data.data.configList
                           that.slibingData=data.data.slibingData
                           $('#transModel').modal('show')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg:data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg:data.message
                        });
                    },
                })
				
            },
            transConfirm(){
                let that=this
                if(this.childData.start_date==''){
                    resultTip({
                        error: 'warning',
                        msg:'请选择预计上学日期'
                    });
                    return
                }
                // if(this.childData.classid=='' || this.childData.classid=='0'){
                //     resultTip({
                //         error: 'warning',
                //         msg:'请选择转入班级'
                //     });
                //     return
                // }
                if(this.childData.discount_id && this.childData.discount_id==''){
                    resultTip({
                        error: 'warning',
                        msg:'请选择账单折扣'
                    });
                    return
                }
                if(this.slibingData.length!=0 && this.siblingId==''){
                    resultTip({
                        error: 'warning',
                        msg:'请选择兄弟姐妹信息'
                    });
                    return
                }
                if(this.transferType==''){
                    resultTip({
                        error: 'warning',
                        msg:'请选择转入类型'
                    });
                    return
                }
                this.btnDis=true
                $.ajax({
                    url: '<?php echo $this->createUrl("transChild") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        transferType:this.transferType,
                        applyId:this.currentList.applyId,
                        siblingId:this.siblingId,
                        childData:this.childData
                        },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.schoolList()
                           $('#transModel').modal('hide')
                           that.btnDis=false

                        } else {
                            if(data.data && data.data!={}){
                                that.slibingData=data.data
                            }
                            resultTip({
                                error: 'warning',
                                msg:data.message
                            });
                            that.btnDis=false
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg:data.message
                        });
                        that.btnDis=false
                    },
                })

            },
            getInvoice(list){
                let that=this
                that.invoiceList=list
                $.ajax({
                    url: '<?php echo $this->createUrl("invoice") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        applyId:list.applyId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.invoiceData=data.data
                            if(data.data.transedInvoice.length==0 && data.data.unTransInvoice.length==0 ){
                                resultTip({
                                    error: 'warning',
                                    msg:'暂无账单信息'
                                });
                                return
                            }
                           $('#invoiceInfo').modal('show')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg:data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg:data.message
                        });
                    },
                })
            },
            transInvoice(){
                if(this.type=='-1' || this.type===''){
                    resultTip({
                        error: 'warning',
                        msg:'请选择要生成得账单'
                    });
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("transInvoice") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        applyId: this.invoiceList.applyId,
                        amount: this.invoiceData.unTransInvoice[this.type].amount,
                        type: this.invoiceData.unTransInvoice[this.type].type,
                        discountId: this.discountId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getInvoice(that.invoiceList)
                            resultTip({
                                msg:data.message
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg:data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg:data.message
                        });
                    },
                })
            },
            invoiceStatus(){
                if(this.invoiceData.paidItems.length!=0){
                    for(var i=0;i<this.invoiceData.paidItems.length;i++){
                        if(this.invoiceData.paidItems[i].status==0){
                            return true
                        }
                    }
                }else{
                    return false
                }
            },
            syncOrder(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("syncOrder") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        applyId: this.invoiceList.applyId,
                    },
                    success: function(data) {
                            that.getInvoice(that.invoiceList)
                            resultTip({
                                msg:'success'
                            });
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg:data.message
                        });
                    },
                })
            }
        },

	})
</script>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
