<?php

/**
 * 工作流统一入口类
 * <AUTHOR>
 */
class EntranceController extends ProtectedController{
    public $dialogWidth=500;  //兼容上版弹出窗口
    /**
     * 入口方法
     */
    public function actionIndex(){
        $request = Yii::app()->request;
        $definationId = $request->getParam('definationId',0);
        $nodeId = $request->getParam('nodeId',0);
        $operationId = $request->getParam('operationId',0);
        $branchId = $request->getParam('branchId',0);
        $action = $request->getParam('action',null);
        $oldWindow = $request->getParam('oldWindow',null);  //兼容老版弹出窗口
        if (!$definationId || $action===null){
            throw new Exception('参数错误！',500);
        }
        if (!empty($oldWindow)){
            $this->layout='//layouts/dialog';
        }
        Yii::import('application.components.workflow.*');
        $workflow = new WorkflowApi($definationId,$nodeId,$operationId,$branchId);
        //权限
        if (!Yii::app()->user->checkAccess('ivystaff_opschool')) {
            if ($workflow->useRole<=0){
                if ($workflow->useRole <0){
                    if (Yii::app()->request->isAjaxRequest){
                        $this->addMessage('state', 'success');
                        $this->addMessage('data',Yii::t("message", "Unauthorized operation, please contact IT Dept."));
                        $this->addMessage('callback','authorityCallback');
                        $this->showMessage();
                    }else{
                        Yii::app()->controller->forward('/child/message/error');
                        Yii::app()->end();
                    }
                }else{
                    if (Yii::app()->request->isPostRequest){
                        $this->addMessage('state', 'success');
                        $this->addMessage('data',Yii::t("message", "Unauthorized operation, please contact IT Dept."));
                        $this->addMessage('message',Yii::t("message", "Unauthorized operation, please contact IT Dept."));
                        $this->addMessage('callback','authorityCallback');
                        $this->showMessage();
                    }
                }
            }
        }
        $plugClass = ucfirst($workflow->definationObj->defination_handle);
        Yii::import('application.modules.workflow.plug.'.$plugClass);
        if (!class_exists($plugClass)){
            throw new Exception("类{$plugClass}文件不存在！",500);
        }
        $plug  = new $plugClass();
        if (!method_exists($plug,$action)){
            throw new Exception("{$plugClass}类中没有{$plug}方法！",500);
        }
        $plug->workflow = $workflow;
        $plug->controller = $this;
        $ret = $plug->$action();
        if (Yii::app()->request->isAjaxRequest){
            if ($request->getParam('showContract')) {
                $ret['showContract'] = 1;
            }
            $template = $this->renderPartial('index',array('dirname'=>  strtolower($plugClass),'template'=>$plug->getTemplate(),'data'=>$ret),true);
            $data['template'] = $template;
            if (isset($ret['jsFunction'])){
                $data['callback'] = $ret['jsFunction'];
            }
            $this->addMessage('state', 'success');
            $this->addMessage('message', '成功');
            $this->addMessage('data',$data);
            $this->addMessage('callback','showCheckList');
            if ($request->getParam('showContract')) {
                $this->addMessage('callback','showCheckList2');
            }
            $this->showMessage();
        }
        $this->render('index',array('dirname'=>  strtolower($plugClass),'template'=>$plug->getTemplate(),'data'=>$ret));
    }
}
