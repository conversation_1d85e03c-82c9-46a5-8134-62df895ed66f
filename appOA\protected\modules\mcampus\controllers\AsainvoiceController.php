 <?php

class AsainvoiceController extends BranchBasedController
{
    public $dialogWidth = 500;

    // 访问action的初级权限
    public $actionAccessAuths = array(
        'index'             => 'o_A_Access',
    );

    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        Yii::import('common.models.visit.*');
        Yii::import('common.models.attendance.*');

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        $this->branchSelectParams['urlArray'] = array('//mcampus/asainvoice/index');

        // jquery ui
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue2.js');
    }

    public function actionIndex($groupId = 0)
    {
        Yii::import('common.models.asainvoice.*');

        $courseGroup = AsaCourseGroup::getGroup($this->branchId, false, false);

        $criteria = new cDbCriteria;
        $criteria->compare('gid', $groupId);
        $criteria->compare('status', array(AsaCourse::STATUS_ACTIVE,AsaCourse::STATUS_FAIL));

        $courseModel = new CActiveDataProvider('AsaCourse', array(
            'criteria' => $criteria,
            'pagination'=>array(
                'pageSize'=>50,
            ),
        ));

        $crit = new cDbCriteria;
        $crit->compare('program_id', $groupId);
        $crit->compare('job_type', ">10");
        $staffCourseModel = new CActiveDataProvider('AsaCourseStaff', array(
            'criteria' => $crit,
            'pagination'=>array(
                'pageSize'=>10,
            ),
        ));

        $this->render('index', array(
            'courseGroup' => $courseGroup,
            'courseModel' => $courseModel,
            'groupId' => $groupId,
            'staffCourseModel' => $staffCourseModel,
        ));
    }

    // 新建更新课程组
    public function actionUpdateGroup($groupId = 0)
    {
        Yii::import('common.models.asainvoice.AsaCourseGroup');

        $model = AsaCourseGroup::model()->findByPk($groupId);
        if (!$model) {
            $model = new AsaCourseGroup;
        } else{
            $model->open_date_start = date('Y-m-d', $model->open_date_start);
            $model->open_date_end = date('Y-m-d', $model->open_date_end);
        }
        if (Yii::app()->request->isPostRequest) {
            $model->attributes = $_POST['AsaCourseGroup'];
            $model->open_date_start = strtotime($model->open_date_start);
            $model->open_date_end = strtotime($model->open_date_end);
            $model->updated = time();
            $model->updated_userid = $this->staff->uid;
            $model->schoolid = $this->branchId;
            if ($model->save()) {
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('callback', 'cbGroup');
                $this->showMessage();                
            }
            $error = current($model->getErrors());
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', $error[0]));
            $this->showMessage();
        }
        $this->renderpartial('updategruop', array('model' => $model));
    }

    // 添加更新课程
    public function actionUpdateCourse($id = 0, $groupId = 0)
    {
        Yii::import('common.models.asainvoice.*');
        //$weekDayList = Yii::app()->request->getParam('dayList', '');
        $period = Yii::app()->request->getParam('period', '');
       // Yii::msg($period);
        $model = AsaCourse::model()->findByPk($id);
        if (!$model) {
            $model = new AsaCourse;
            $model->gid = $groupId;
        }
        //var_dump($model);
        $weekday = array(1=>'星期一',2=>'星期二',3=>'星期三',4=>'星期四',5=>'星期五',6=>'星期六',7=>'星期日');
        //编辑时先查出选中的是周几（编辑页面显示默认选中）
        $criteria=new CDbCriteria;
        $criteria->condition = 'course_id=:course_id';
        $criteria->params = array(':course_id'=>$id);
        $criteria->order = "weekday,time_start asc";
        $week=AsaCourseSchedule::model()->findAll($criteria);

        $checkweek = array();
        foreach($week as $key=>$value){
            $checkweek[$value->weekday][$key]['startTime'] = $value->time_start;
            $checkweek[$value->weekday][$key]['endTime'] = $value->end_time;

        }
        //添加时间（周几)
        $modelWeek = new AsaCourseSchedule;
        if (Yii::app()->request->isPostRequest) {
            if(empty($period)){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', '请选择时段'));
                $this->showMessage();
            }

            $model->attributes = $_POST['AsaCourse'];
            $model->updated = time();
            $model->updated_userid = $this->staff->uid;
            $model->schoolid = $this->branchId;
            if ($model->save()) {
                if($id){
                    $criteria = new CDbCriteria;
                    $criteria->condition = 'course_id=:course_id';
                    $criteria->params = array(':course_id'=>$id);
                    AsaCourseSchedule::model()->deleteAll($criteria);
                }

                foreach($period as $week =>$_weekList){
                    foreach($_weekList as $k=>$data){
                        $modelWeek = new AsaCourseSchedule;
                        $modelWeek->course_schedule_id = $model->id .'_' . $week .'_' . str_replace(':', '', $data['startTime']);
                        $modelWeek->program_id = $model->gid;
                        $modelWeek->weekday = $week;
                        $modelWeek->course_id = $model->id;
                        $modelWeek->time_start = $data['startTime'];
                        $modelWeek->end_time = $data['endTime'];
                        $modelWeek->updated = time();
                        $modelWeek->updated_by = $this->staff->uid;
                        $modelWeek->save();
                    }
                }

                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('callback', 'cbCourse');
                $this->showMessage();   
            }
            $error = current($model->getErrors());
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', $error[0]));
            $this->showMessage();
        }
        $groupArray = array();
        $courseGroup = AsaCourseGroup::getGroup($this->branchId, false, false);
        foreach ($courseGroup as $v) {
            $groupArray[$v->id] = $v->title_cn;
        }
        $this->renderpartial('updateCourse', array(
            'model' => $model,
            'groupArray' => $groupArray,
            'modelWeek' =>  $modelWeek,
            'weekday' => $weekday,
            'checkweek' => $checkweek,
        ));
    }    
    // 添加更新教师
    public function actionUpdateTeacher($vendor_id = 0,$groupId = 0){
        Yii::import('common.models.staff.*');
        Yii::import('common.models.asainvoice.*');

        $criteria = new CDbCriteria;
        $criteria->compare('t.level', 1);
        $criteria->compare('t.isstaff', 1);
        $criteria->compare('profile.branch', $this->branchId);
        $criteria->with = 'profile';       //调用profile
        $teacherNameList = User::model()->findAll($criteria);

        $teacherlist = array();
        if($teacherNameList){
            foreach($teacherNameList as $k => $teacher){
                $teacherlist[$k]['value'] =  $teacher->getName();
                $teacherlist[$k]['teacherPhone'] =  $teacher->staff->mobile_telephone;
                $teacherlist[$k]['teacherId'] =  $teacher->uid;
                $teacherlist[$k]['teacherEmail'] = $teacher->email;
            }
        }

        $typeList = CommonUtils::LoadConfig('CfgASA');
        $model = AsaVendor::model()->findByPk($vendor_id);
        if (!$model) {
            $model = new AsaVendor;
        }
        //echo "string";exit;
        if (Yii::app()->request->isPostRequest){

            $request = Yii::app()->getRequest();
            $model->attributes = $_POST['AsaVendor'];
            $model->site_id = $this->branchId;
            $model->ivy_uid = Yii::app()->request->getParam('teacherId', ''); //老师id
            $model->cellphone = Yii::app()->request->getParam('teacherPhone', ''); //联系方式
            $model->email = Yii::app()->request->getParam('teacherEmail', '');   // 老师职位
            $model->type = Yii::app()->request->getParam('teacherType', '');  // 老师来源
            $model->active = Yii::app()->request->getParam('active', '');   // 老师职位(需要转为状态)
            $model->updated = time();
            $project= Yii::app()->request->getParam('teacher_job', '');
            $model->updated_by = $this->staff->uid;  // 修改用户id
            
            if($vendor_id==0){
                if($model->type == 1){
                    //根据老师id获取中英文名
                    $ivy_uid = $model->ivy_uid;
                    $teacherNameLists = User::model()->findByPk($ivy_uid);
                    foreach($teacherNameLists as $k=>$v){
                        $name = $teacherNameLists['name'];
                        $uname = $teacherNameLists['uname'];
                    }
                    $model->name_cn=$name;
                    $model->name_en=$uname;
                
                   if(empty($model->ivy_uid)){
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message', '老师不能为空'));
                        $this->showMessage();
                    }
                }else{
                    $model->name_cn = Yii::app()->request->getParam('project', ''); //老师中文
                    $model->name_en = Yii::app()->request->getParam('projects', ''); //老师英文
                }
            }else{
                $model->name_cn = Yii::app()->request->getParam('project', ''); //老师中文
                $model->name_en = Yii::app()->request->getParam('projects', ''); //老师英文
            }
            if ($model->save()) {
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('callback', 'cbTeacher');
                $this->showMessage();
            } else{
                $error = current($model->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', $error[0]));
                $this->showMessage();
            }
        }
        $this->renderpartial('updateTeacher', array(
            'model' => $model,
            'typeList'=>$typeList,
            'teacherList'=>$teacherlist,  
        ));
    }

    public function actionupdateBankInfo($vendor_id = 0){
        Yii::import('common.models.asainvoice.*');

        $model = AsaVendorInfo::model()->findByPk($vendor_id);
        if (!$model) {
            $model = new AsaVendorInfo;    
        }

        $bank = array('中国建设银行(CCB)','中国农业银行(ABC)','中国工商银行(ICBC)','中国银行(BOC)','中国民生银行(CMBC)','招商银行(CMB)','兴业银行(CIB)','交通银行(BCM)','中国光大银行(CEB)');
        if (Yii::app()->request->isPostRequest){
            $model->vendor_id=$vendor_id;
            $model->attributes = $_POST['AsaVendorInfo'];
            $model->bank = Yii::app()->request->getParam('bank','');
            if ($model->save()) {
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('callback', 'cbTeacher');
                $this->showMessage();
           }else{
                $error = current($model->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', $error[0]));
                $this->showMessage();
           }
        }
 
        $this->renderpartial('bank',array(
            'model' => $model,
            'bank'  => $bank,
        ));
    }
    // 删除课程
    public function actionDeleteCourse($id = 0)
    {
        Yii::import('common.models.asainvoice.*');

        $model = AsaCourse::model()->findByPk($id);
        if (!$model) {
            return false;
        }
        if (Yii::app()->request->isPostRequest) {
            //先查看是否有关联的账单
            $asainvoiceitem = $model->asainvoiceitem(array('condition'=>'status != ' . AsaInvoice::STATS_CANCELLED));
            if(!$asainvoiceitem){
                $model->status = AsaCourse::STATUS_INVALID;
                if ($model->save() ) {
                    //删除课程时间表的数据
                    AsaCourseSchedule::model()->deleteAll('course_id=:course_id', array(':course_id'=>$id));

                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message','success'));
                    $this->showMessage();   
                }
                $error = current($model->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', $error[0]));
                $this->showMessage();
            }else{
                //查询关联账单学生姓名
                // $childidList = array();
                // foreach($asainvoiceitem as $item){
                //     if ($item->status == AsaInvoice::STATS_UNPAID) {
                //         $childidList['unpaid'][$item->asainvoice->childid] = $item->asainvoice->childid;
                //     } else{
                //         $childidList['paid'][$item->asainvoice->childid] = $item->asainvoice->childid;
                //     }
                // }
                // $unpaidChildList = ChildProfileBasic::model()->findAllByPk($childidList['unpaid']);
                // $paidChildList = ChildProfileBasic::model()->findAllByPk($childidList['paid']);
                
                // $message = '';
                // if ($unpaidChildList) {
                //     $message .= '未支付账单：';
                // }
                // foreach($unpaidChildList as $child){
                //     $message .= $child->getChildName() . ',';
                // }
                // if ($paidChildList) {
                //     $message .= ' 已支付账单：';
                // }           
                // foreach($paidChildList as $child){
                //     $message .= $child->getChildName() . ',';
                // }
                $message = '此课程已产生过相关账单！';
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message',$message));
                $this->showMessage();
            }
        }
        $this->renderpartial('updateCourse', array(
            'model' => $model,
            'groupArray' => $groupArray,
        ));
    }

    //删除教师
    public function actionDeleteTeacher($vendor_id = 0){
        Yii::import('common.models.asainvoice.*');
        $model = AsaVendor::model()->findByPk($vendor_id);
        if (!$model) {
            return false;
        }

        $criteria = new CDbCriteria;
        $criteria->compare('vendor_id', $vendor_id);
        $coursestaff = AsaCourseStaff::model()->count($criteria);

        if(!$coursestaff){
            if (Yii::app()->request->isPostRequest) {
                if ($model->deleteByPk($vendor_id) ) {                
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message','success'));
                    $this->addMessage('callback', 'cbTeacher');
                    $this->showMessage();
                }
            }
        }else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message','该老师已分配课程！'));
            $this->showMessage(); 
        }
    }

    // 账单管理
    public function actionInvoice()
    {
        Yii::import('common.models.invoice.ChildReserve');
        Yii::import('common.models.asainvoice.*');

        // 获取所有班级
        $criteria = new cDbCriteria;
        $criteria->compare('stat', IvyClass::STATS_OPEN);
        $criteria->compare('schoolid', $this->branchId);
        $criteria->order = 'title';
        $classes = IvyClass::model()->findAll($criteria);

        // 获取符合要求的课程组
        $group = AsaCourseGroup::getGroup($this->branchId);

        // 获取新注册学生
        $criteria = new CDbCriteria;
        $criteria->compare('status', ChildProfileBasic::STATS_REGISTERED);
        $criteria->compare('schoolid', $this->branchId);
        $newChildObj = ChildProfileBasic::model()->findAll($criteria);

        $this->render('invoice', array(
            'classes'=>$classes,
            'group'=>$group,
            'newChildObj'=>$newChildObj,
        ));
    }

    // 保存账单
    public function actionSaveInvoice()
    {
        if (Yii::app()->request->isPostRequest) {
            $childid = Yii::app()->request->getParam('childid', 0);
            $classid = Yii::app()->request->getParam('classid', -1);
            $group = Yii::app()->request->getParam('group', 0);
            $items = Yii::app()->request->getParam('itemId', 0);
            $title = Yii::app()->request->getParam('title', 0);
            $discountid = Yii::app()->request->getParam('discount', 0);
            if ($classid < 0 || !$childid) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', '请先选择班级和学生'));
                $this->showMessage();
            }   
            if (!$group || !$items) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', '课程组与课程不能为空'));
                $this->showMessage();
            }            
            if (!$title) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', '账单标题不能为空'));
                $this->showMessage();
            }
            Yii::import('common.models.asainvoice.*');
            // 计算所需价格
            $amount_original = 0;
            $price = array();
            foreach ($items as $item) {
                $count = $_POST["itemCount_{$item}"];
                $course = AsaCourse::model()->findByPk($item);
                $price[$item] = $course->unit_price;
                $amount_original += $count * $course->unit_price;
            }
            // 计算折扣
            $isDiscount = 0;
            $discountModel = AsaDiscount::model()->findByPk($discountid);
            if ($discountModel) {
                $amount_pre_discount = $amount_original;
                $amount_original = $discountModel->calDiscount($amount_original);
                if ($amount_original < 0) {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message', '折扣价格大于课程价格'));
                    $this->showMessage();
                }
                $isDiscount = 1;
            }
            // 保存订单表
            $invoice = new AsaInvoice;
            $invoice->title = $title;
            $invoice->amount_original = round($amount_original);
            $invoice->amount_actual = 0;
            $invoice->status = AsaInvoice::STATS_UNPAID;
            $invoice->childid = $childid;
            $invoice->classid = $classid;
            $invoice->schoolid = $this->branchId;
            $invoice->updated = time();
            $invoice->updated_userid = $this->staff->uid;
            if (!$invoice->save()) {
                $error = current($invoice->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', $error[0]));
                $this->showMessage();
            }
            if ($isDiscount) {
                $discountLinkModel = new AsaInvoiceDiscountLink();
                $discountLinkModel->invoice_id = $invoice->id;
                $discountLinkModel->discount_id = $discountModel->id;
                $discountLinkModel->amount_pre_discount = $amount_pre_discount;
                $discountLinkModel->amount_final = $amount_original;
                $discountLinkModel->save();
            }
            foreach ($items as $item) {
                $count = $_POST["itemCount_{$item}"];
                $invoiceItem = new AsaInvoiceItem;
                $invoiceItem->course_group_id = $group;
                $invoiceItem->course_id = $item;
                $invoiceItem->order_id = $invoice->id;
                $invoiceItem->class_count = $count;
                $invoiceItem->unit_price = $price[$item];
                $invoiceItem->status = AsaInvoice::STATS_UNPAID;
                $invoiceItem->schoolid = $this->branchId;
                $invoiceItem->spot_hold = 1;
                $invoiceItem->updated = time();
                $invoiceItem->updated_userid = $this->staff->uid;
                if (!$invoiceItem->save()) {
                    $error = current($invoiceItem->getErrors());
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message', $error[0]));
                    $this->showMessage();
                }
            }
            $this->addMessage('state', 'success');
            $this->addMessage('data', array('childid'=>$childid));
            $this->addMessage('callback', 'cbShow');
            $this->addMessage('message', Yii::t('message', '保存成功'));
            $this->showMessage();
        }
    }

    // 获取未支付的账单
    public function actionGetInvoice()
    {
        $childid = Yii::app()->request->getParam('childid', 0);
        if (Yii::app()->request->isAjaxRequest && $childid) {
            Yii::import('common.models.asainvoice.*');

            $invoice = $this->getInvoice($childid, AsaInvoice::STATS_UNPAID);
            $data = array();
            foreach ($invoice as $v) {
                $data[$v->id]['title'] = $v->title;
                $data[$v->id]['amount'] = $v->amount_original;
                $data[$v->id]['info'] = '';
                foreach ($v->item as $item) {
                    $course = AsaCourse::model()->findByPk($item->course_id);
                    $data[$v->id]['info'] .= $course->title_cn .'(' . $item->unit_price .' × '. $item->class_count .')，';
                }
                $data[$v->id]['info'] = rtrim($data[$v->id]['info'], '，');

            }
            echo json_encode($data);
        }
    }

    // 获取已支付的账单
    public function actionPaidInvoice()
    {
        $childid = Yii::app()->request->getParam('childid', 0);
        if (Yii::app()->request->isAjaxRequest && $childid) {
            Yii::import('common.models.asainvoice.*');

            $invoice = $this->getInvoice($childid, AsaInvoice::STATS_PAID);
            $data = array();
            foreach ($invoice as $v) {
                $data[$v->id]['title'] = $v->title;
                $data[$v->id]['amount'] = $v->amount_original;
                $data[$v->id]['info'] = '';
                $data[$v->id]['date'] = date('Y-m-d', $v->updated);
                $data[$v->id]['user'] = User::model()->findByPk($v->updated_userid)->getName();
                foreach ($v->item as $item) {
                    $course = AsaCourse::model()->findByPk($item->course_id);
                    $data[$v->id]['info'] .= $course->title_cn .'(' . $item->unit_price .' × '. $item->class_count .')，';
                }
                $data[$v->id]['info'] = rtrim($data[$v->id]['info'], '，');

            }
            echo json_encode($data);
        }
    }

    // 现金付款
    public function actionCashPay()
    {
        if (Yii::app()->request->isPostRequest) {
            Yii::import('common.models.asainvoice.*');
            $invoiceId = Yii::app()->request->getParam('id', 0);
            $amount = Yii::app()->request->getParam('amount', 0);
            if (!$invoiceId || !$amount) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', 'fail');
                $this->showMessage();
            }

            $invoice = AsaInvoice::model()->findByPk($invoiceId);
            $payResult = $this->payInvoice($invoice, AsaInvoice::CASH);
            if ($payResult === true) {
                $this->addMessage('state', 'success');
                $this->addMessage('data', array('childid'=>$invoice->childid));
                $this->addMessage('message', '付款成功');
                $this->showMessage();
            } else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', $payResult);
                $this->showMessage();
            }
        }
    }

    // 微信支付
    public function actionWechatPay()
    {
        if (Yii::app()->request->isPostRequest) {
            Yii::import('common.models.asainvoice.*');
            Yii::import('common.models.wxpay.*');

            $invoiceId = Yii::app()->request->getParam('invoiceId', 0);
            $authCode = Yii::app()->request->getParam('authCode', 0);
            if (!$invoiceId || !$authCode) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '支付失败');
                $this->showMessage();
            }
            $invoice = AsaInvoice::model()->findByPk($invoiceId);
            if (!$invoice) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '账单不存在');
                $this->showMessage();
            }            
            if ($invoice->status != AsaInvoice::STATS_UNPAID) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '账单已付清');
                $this->showMessage();
            }

            // 生成微信账单
            $wxPayCfg = CommonUtils::LoadConfig('CfgWxPayGlobal');
            $cfg = $wxPayCfg['MMX'];
            $schoolid = $this->branchId;
            $totalAmount = $invoice->amount_original;
            $orderPrefix = strval($cfg['number_code']) . sprintf('%03s', $wxPayCfg[$schoolid]['number_code']) . sprintf('%06s', $invoiceId);

            $model = new AsaWechatpayOrder;
            $model->orderid = $model->genOrderID($orderPrefix);
            $model->payable_amount = $totalAmount;
            $model->schoolid = $schoolid;
            $model->invoice_id = $invoice->id;
            $model->childid = $invoice->childid;
            $model->type = 'MICROPAY';
            $model->order_time = time();
            $model->updated = time();
            $model->updated_userid = $this->staff->uid;
            if($model->save()){
                $pay = $this->beginWidget('common.extensions.wxPay.MicroPay', array(
                    'auth_code' => $authCode,
                    'body' => $invoice->title,
                    'detail' => $invoice->title,
                    'amount' => $totalAmount*100,
                    'orderid' => $model->orderid,
                    'device_info' => $this->branchObj->abb,
                    'cfg' => $cfg,
                ));
                $data = $pay->pay();
                $data = $pay->hideAppid($data);
                if($data['return_code'] == 'SUCCESS'){
                    if($data['result_code'] == 'SUCCESS'){
                        if($model->orderid == $data['out_trade_no']){
                            $model = AsaWechatpayOrder::model()->findByPk($data['out_trade_no']);
                            $model->status = 1;
                            $model->fact_amount = $data['total_fee']/100;
                            $model->save();
                            // 支付订单
                            $payResult = $this->payInvoice($invoice, AsaInvoice::WECHAT);
                        }
                    }
                    else{
                        $data['out_trade_no'] = $model->orderid;
                    }
                    echo CJSON::encode($data);
                }
            }
        }
    }

    //微信支付结果查询
    public function actionQueryWechat()
    {
        Yii::import('common.models.asainvoice.*');
        Yii::import('common.models.wxpay.*');

        $orderid = Yii::app()->request->getParam('orderid', '');
        $wxPayCfg = CommonUtils::LoadConfig('CfgWxPayGlobal');

        $micropay = $this->beginWidget('common.extensions.wxPay.MicroPay', array(
            'orderid' => $orderid,
            'cfg' => $wxPayCfg['MMX'],
        ));
        $data = $micropay->query();
        $data = $micropay->hideAppid($data);
        if($data['return_code'] == 'SUCCESS' && $data['result_code'] == 'SUCCESS' && $data['trade_state'] == 'SUCCESS'){
            if($orderid == $data['out_trade_no']){
                $model = AsaWechatpayOrder::model()->findByPk($data['out_trade_no']);
                $model->status = 1;
                $model->fact_amount = $data['total_fee']/100;
                $model->updated = time();
                $model->updated_userid = $this->staff->uid;
                $model->save();
                // 支付订单
                $invoice = AsaInvoice::model()->findByPk($model->invoice_id);
                $payResult = $this->payInvoice($invoice, AsaInvoice::WECHAT);
                $this->addMessage('state', 'success');
                $this->showMessage();
            }
        }
        $this->addMessage('state', 'fail');
        $this->showMessage();
    }

    // 查询异常记录
    public function actionUnpayWechat()
    {
        Yii::import('common.models.asainvoice.*');

        $criteria = new cDbCriteria;
        $invoiceId = Yii::app()->request->getParam('invoiceId');
        $criteria->compare('invoice_id', $invoiceId);
        $criteria->compare('status', 0);
        $wechatPay = AsaWechatpayOrder::model()->findAll($criteria);
        $order = array();
        foreach ($wechatPay as $v) {
            $order[] = $v->orderid;
        }
        echo CJSON::encode($order);
    }

    // 作废账单
    public function actionDelInvoice()
    {
        if (Yii::app()->request->isPostRequest) {
            Yii::import('common.models.asainvoice.*');

            $invoiceId = Yii::app()->request->getParam('invoiceId');
            $invoice = AsaInvoice::model()->findByPk($invoiceId);
            $invoice->status = AsaInvoice::STATS_CANCELLED;
            $invoice->save();
            foreach ($invoice->item as $asainvoiceitem) {
                $asainvoiceitem->status = AsaInvoice::STATS_CANCELLED;
                $asainvoiceitem->save();
            }
            $this->addMessage('state', 'success');
            $this->addMessage('data', array('childid'=>$invoice->childid));
            $this->showMessage();
        }
    }

    public function actionShowCourseInvoice($id = 0)
    {
        Yii::import('common.models.asainvoice.*');

        $model = AsaCourse::model()->findByPk($id);
        if ($model) {
            $asainvoiceitem = $model->asainvoiceitem(array('condition'=>'status != ' . AsaInvoice::STATS_CANCELLED, 'order'=>'status'));
            $childidList = array();
            foreach($asainvoiceitem as $item){
                $childidList['childid'][$item->id] = $item->asainvoice->childid;
                $childidList['status'][$item->id] = $item->status;
            }
            $childList = ChildProfileBasic::model()->findAllByPk($childidList['childid'], array('index'=>'childid'));
        }

        $this->renderpartial('courseInvoice', array(
            'model' => $model,
            'asainvoiceitem' => $asainvoiceitem,
            'childidList' => $childidList,
            'childList' => $childList,
        ));
    }

    public function actionSummary()
    {
        Yii::import('common.models.asainvoice.*');
        $status = Yii::app()->request->getParam('status', '');
        $paytype = Yii::app()->request->getParam('paytype', '');
        $instatus = Yii::app()->request->getParam('instatus', '');
        $times = Yii::app()->request->getParam('times', '');
        $username = Yii::app()->request->getParam('username', '');
        //--------------- 所有课程组 stat ------------------//
        $criteria = new CDbCriteria;
        $criteria->compare('schoolid', $this->branchId);
        $group = AsaCourse::model()->findAll($criteria);

        $grouplist = array();
        foreach($group as $_group){
            $grouplist[$_group->id] = $_group->getTitle();
        }
        $grouplist = array('全部课程', '9999' => '使用课程') + $grouplist;
        //---------------- 所有课程组 endd -----------------//

        //根据孩子名字查询孩子ID //
        $childId = array();
        if($username){
            $criteria = new CDbCriteria;
            $criteria->addCondition("concat_ws('*',name_cn,first_name_en,middle_name_en,last_name_en) like '%{$username}%' ");
            $criteria->compare('schoolid', $this->branchId);
            $model = ChildProfileBasic::model()->findAll($criteria);
            if($model){
                foreach($model as $_model){
                    $childId[] = $_model->childid;
                }
            }

        }

        $model = new AsaInvoiceItem;
        $crit = new CDbCriteria;
        $crit->compare('t.schoolid', $this->branchId);

        if($status == 9999) $crit->compare('asacourse.status', 1);
        if($status && $status != 9999) $crit->compare('asacourse.id', $status);
        if($paytype)$crit->compare('asainvoice.pay_type', $paytype);
        if($username) {
            $childlist = ($childId) ? $childId : $username;
            $crit->compare('asainvoice.childid', $childlist);
        }
        if($instatus){
            $crit->compare('asainvoice.status', $instatus);
        }else{
            $crit->compare('asainvoice.status', array(10,20));
        }
        if($times){
            $stat = strtotime($times);
            $end = strtotime($times) + 84600;
            $crit->compare('asainvoice.updated', ">={$stat}");
            $crit->compare('asainvoice.updated', "<{$end}");
        }
        $crit->with = array('asacourse','asainvoice');
        //$model = AsaInvoiceItem::model()->findAll($crit);

        $dataProvider = new CActiveDataProvider($model, array(
            'criteria'=>$crit,
            'sort' => array(
                //'defaultOrder' => 'add_timestamp DESC',
            ),
            'pagination'=>array(
                'pageSize'=>20,
            ),
        ));

        $this->render('summary', array(
            'dataProvider' => $dataProvider,
            'grouplist' => $grouplist,
        ));
    }



    /*
     * 本校园的所有老师
     */
    public function actionSchoolTeacherlist()
    {
        Yii::import('common.models.staff.*');
        Yii::import('common.models.asainvoice.*');

        $criteria = new CDbCriteria;
        $criteria->compare('t.level', 1);
        $criteria->compare('t.isstaff', 1);
        $criteria->compare('profile.branch', $this->branchId);
        $criteria->with = 'profile';       //调用profile
        $teacherNameList = User::model()->findAll($criteria);

        $teacherlist = array();
        if($teacherNameList){
            foreach($teacherNameList as $k => $teacher){
                $teacherlist[1][$k]['value'] =  $teacher->getName();
                $teacherlist[1][$k]['teacherPhone'] =  $teacher->staff->mobile_telephone;
                $teacherlist[1][$k]['teacherId'] =  $teacher->uid;
            }
        }

        //外部企业老师
        $criteria = new CDbCriteria;
        $criteria->compare('type', array('2','3'));
        $criteria->compare('active', 1);
        $criteria->compare('site_id', $this->branchId);
        $wTeacher = AsaVendor::model()->findAll($criteria);
        if($wTeacher){
            foreach($wTeacher as $k=>$_teacher){
                    $teacherlist[$_teacher->type][$k]['value'] =  $_teacher->getName();
                    $teacherlist[$_teacher->type][$k]['teacherPhone'] =  $_teacher->cellphone;
                    $teacherlist[$_teacher->type][$k]['teacherId'] =  $_teacher->vendor_id;


            }
        }

        echo CJSON::encode($teacherlist);
    }

    /*
     * 当前课程下的所有老师资料
     */
    public function actionTeacherlist()
    {
        Yii::import('common.models.asainvoice.*');
        $courseid = Yii::app()->request->getParam('courseid', '');

        if($courseid){
            // 员工内部 老师
            $criteria = new CDbCriteria;
            $criteria->compare('course_id', $courseid);
            $courseStaff = AsaCourseStaff::model()->findAll($criteria);
            $teacherlist = array();

            $type = $this->getteachertype();
            if($courseStaff){
                foreach($courseStaff as $k => $_coures){
                    $teacherlist[$k]['tacherName'] =  $_coures->vendor->getName();
                    $teacherlist[$k]['phone'] =  $_coures->vendor->cellphone;
                    $teacherlist[$k]['type'] =  $type['type'][$_coures->vendor->type];
                    $teacherlist[$k]['job_type'] =  $type['jobType'][$_coures->job_type];
                    $teacherlist[$k]['teacherPrice'] =  $_coures->unit_salary;
                    $teacherlist[$k]['staffId'] =  $_coures->id;
                }
            }

            echo CJSON::encode($teacherlist);
        }
    }


    /*
     * 增加老师
     *
     */
    public function actionAddteacher()
    {
        Yii::import('common.models.asainvoice.*');
        Yii::import('common.models.staff.*');

        $teacherid = Yii::app()->request->getParam('teacher', '');        // 内部员工的ID 或者 外部个人老师的ID
        $courseId = Yii::app()->request->getParam('courseId', '');        // 课程ID
        $teacherPrice = Yii::app()->request->getParam('teacherPrice', '');// 老师课时价格
        $teacherJob = Yii::app()->request->getParam('teacher_job', '');   // 老师职位
        $teacherType = Yii::app()->request->getParam('teacherType', '');  // 老师来源
        $teacherPhone = Yii::app()->request->getParam('teacherPhone', '');// 外部企业或者个人的ID

        if($teacherType == 1){
            if(empty($teacherid)){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', '老师不能为空'));
                $this->showMessage();
            }

            $criteria = new CDbCriteria;
            $criteria->compare('ivy_uid', $teacherid);
            $isteacher = AsaVendor::model()->find($criteria);
            if(empty($isteacher)){
                if($teacherPhone){
                    $criteria = new CDbCriteria;
                    $criteria->compare('cellphone', $teacherPhone);
                    $phonecount = AsaVendor::model()->count($criteria);
                    if($phonecount){
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message', "电话号码重复,请重新填写电话号码"));
                        $this->showMessage();
                    }
                }

                $teacherStaff = Staff::model()->findByPk($teacherid);
                $teacherStaff->mobile_telephone = $teacherPhone;
                if(!$teacherStaff->save()){
                    $error = current($teacherStaff->getErrors());
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message', $error[0]));
                    $this->showMessage();
                }
                $teacher = User::model()->findByPk($teacherid);
                $isteacher = new AsaVendor;
                $isteacher->name_cn = $teacher->name;
                $isteacher->name_en = $teacher->profile->first_name . " " . $teacher->profile->last_name;
                $isteacher->cellphone = $teacher->staff->mobile_telephone;
                $isteacher->email = $teacher->email;
                $isteacher->site_id = $teacher->profile->branch;
                $isteacher->active = 1;
                $isteacher->ivy_uid = $teacherid;
                $isteacher->type = $teacherType;
                $isteacher->updated = time();
                $isteacher->updated_by = Yii::app()->user->id;

                if(!$isteacher->save()){
                    $error = current($isteacher->getErrors());
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message', $error[0]));
                    $this->showMessage();
                }
            }
        }else{
            $isteacher = AsaVendor::model()->findByPk($teacherid);
            if(!$isteacher){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', "没有该老师"));
                $this->showMessage();
            }


        }

        if($isteacher){
            $criteria = new CDbCriteria;
            $criteria->compare('course_id', $courseId);
            $criteria->compare('vendor_id', $isteacher->vendor_id);
            $asastaffcount = AsaCourseStaff::model()->count($criteria);
            if($asastaffcount){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', '该老师已经添加过该课程'));
                $this->showMessage();
            }
        }

        $coures = AsaCourse::model()->findByPk($courseId);
        $courseGroup = AsaCourseGroup::model()->findByPk($coures->gid);
        if(empty($courseGroup)){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', "没有该课程组"));
            $this->showMessage();
        }

        $model = new AsaCourseStaff;
        $model->program_id = $courseGroup->id;
        $model->course_id = $coures->id;
        $model->vendor_id = $isteacher->vendor_id;
        $model->site_id = $this->branchId;
        $model->job_type = $teacherJob;
        $model->unit_salary = $teacherPrice;
        $model->status = 1;
        $model->updated = Yii::app()->user->id;
        $model->updated_by = time();
        $model->unit_type = 1;
        if($model->save()){
            $type = $this->getteachertype();
            $data = array(
                'tacherName' => $isteacher->getName(),
                'phone' => $isteacher->cellphone,
                'job_type' => $type['jobType'][$model->job_type],
                'teacherPrice' => $model->unit_salary,
                'type' => $type['type'][$isteacher->type],
                'staffId' => $model->id,
            );

            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message', "添加成功"));
            $this->addMessage('data', $data);
            $this->addMessage('callback', '_pushteacher');
            $this->showMessage();

        }else{
            $error = current($model->getErrors());
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', $error[0]));
            $this->showMessage();
        }
    }

    //根据ID 删除课程
    public function actionDelcourse()
    {
        Yii::import('common.models.asainvoice.*');
        $staffId = Yii::app()->request->getParam('id', '');
        if($staffId){
            $staff = AsaCourseStaff::model()->findByPk($staffId);
            if($staff){
                if($staff->delete()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('data', $staffId);
                    $this->addMessage('message', Yii::t('message', "删除成功"));
                    $this->addMessage('callback', 'removeTeacher');

                    $this->showMessage();
                }else{
                    $error = current($staff->getErrors());
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('global', "Deletion failed"));
                    $this->showMessage();
                }
            }
        }
    }


    // 教师考勤
    public function actionAttendance()
    {
        Yii::import('common.models.asainvoice.*');
        $weekday = Yii::app()->request->getParam('weekday', '');

        if($weekday){
            $weekdays =  date('N',strtotime($weekday));
            if($weekdays){
                $criteria = new CDbCriteria;
                $criteria->compare('t.schoolid', $this->branchId);
                $criteria->compare('t.status', 1);
                $criteria->compare('course.status', 1);
                $criteria->with = 'course';
                $asaCourseGroup = AsaCourseGroup::model()->findAll($criteria);
                $teacherAttendance = array();
                //  所有课程组
                foreach($asaCourseGroup as $key=>$_courseGroup){
                    $teacherAttendance['course_group'][$key]['courseGroupName'] = $_courseGroup->getName();
                    $teacherAttendance['course_group'][$key]['courseGroupId'] = $_courseGroup->id;
                    foreach($_courseGroup->course as $ke => $_course) {
                        $courseGroup[] = $_course->id;
                    }
                }

                $criteria = new CDbCriteria;
                $criteria->compare('t.site_id', $this->branchId);
                $criteria->compare('t.active', 1);
                $courseTeacher = AsaVendor::model()->findAll($criteria);
                if($courseTeacher){
                    //课程组下的所有课程
                    $typeList = CommonUtils::LoadConfig('CfgASA');
                    foreach($typeList['type'] as $k=>$_type){
                        $teacherAttendance['spare_teacher_list'][$k]['title'] = (Yii::app()->language == 'zh_cn') ? $_type['cn'] : $_type['en'];
                        foreach($courseTeacher as $key=>$_teacher){
                            if($k == $_teacher->type){
                                $teacherAttendance['spare_teacher_list'][$k]['data'][$key]['teacherName'] = $_teacher->getName();
                                $teacherAttendance['spare_teacher_list'][$k]['data'][$key]['teacherId'] = $_teacher->vendor_id;
                            }
                        }
                    }
                }

                $criteria = new CDbCriteria;
                $criteria->compare('t.weekday', $weekdays);
                $criteria->order = 'time_start ASC';
                $course = AsaCourseSchedule::model()->findAll($criteria);



                if($course){
                    //每个课程下的所有分配老师
                    $criteria = new CDbCriteria;
                    $criteria->compare('t.weekday', $weekdays);
                    $criteria->compare('attendance.service_start', date("Ymd",strtotime($weekday)));
                    $criteria->with = "attendance";
                    $coursesa = AsaCourseSchedule::model()->findAll($criteria);

                    foreach($coursesa as $v){
                        foreach($v->attendance as $key => $teacher){
                            $techerList[$teacher->course_schedule_id][$key]['teacherName'] = $teacher->vendorTeacher->getName();
                            $techerList[$teacher->course_schedule_id][$key]['teacherId'] = $teacher->vendor_id;
                            $techerList[$teacher->course_schedule_id][$key]['status'] = $teacher->status;
                            $techerList[$teacher->course_schedule_id][$key]['spare_teacherId'] = $teacher->substituter;
                            $techerList[$teacher->course_schedule_id][$key]['spare_teacherName'] = ($teacher->vendor) ? $teacher->vendor->getName(): "";
                        }
                    }

                    foreach($course as $key =>$_course){
                        $teacherAttendance['course_list'][$_course->course->gid][$_course->course_schedule_id]['courseName'] = $_course->course->getTitle();
                        $teacherAttendance['course_list'][$_course->course->gid][$_course->course_schedule_id]['courseId'] = $_course->course->id;
                        $teacherAttendance['course_list'][$_course->course->gid][$_course->course_schedule_id]['courseData'] = $_course->time_start . " - " .$_course->end_time;
                        $teacherAttendance['course_list'][$_course->course->gid][$_course->course_schedule_id]['scheduleId'] = $_course->course_schedule_id;
                        $teacherAttendance['course_list'][$_course->course->gid][$_course->course_schedule_id]['state'] = ($techerList[$_course->course_schedule_id]) ? 1 : 0;
                        foreach($_course->course->coursestaff as $k=>$_coursestaff){
                            if($techerList[$_course->course_schedule_id]){
                                $teacherAttendance['course_list'][$_course->course->gid][$_course->course_schedule_id]['teacher_list'] = $techerList[$_course->course_schedule_id];
                            }else{
                                $teacherAttendance['course_list'][$_course->course->gid][$_course->course_schedule_id]['teacher_list'][$k]['teacherName'] = $_coursestaff->vendor->getName();
                                $teacherAttendance['course_list'][$_course->course->gid][$_course->course_schedule_id]['teacher_list'][$k]['teacherId'] =  $_coursestaff->vendor_id;
                                $teacherAttendance['course_list'][$_course->course->gid][$_course->course_schedule_id]['teacher_list'][$k]['status'] =  0;
                            }
                        }
                    }
                }

                if(empty($teacherAttendance['course_list'])){
                    $teacherAttendance = array();
                }
                echo CJSON::encode($teacherAttendance);
            }
        }else{
            $this->render('attendance');
        }
    }


    /**
     *
     * }else{
    foreach($_course->course->coursestaff as $k=>$_coursestaff){
    $teacherAttendance['course_list'][$_course->course->gid][$_course->course_schedule_id]['teacher_list'][$k]['teacherName'] = $_coursestaff->vendor->getName();
    $teacherAttendance['course_list'][$_course->course->gid][$_course->course_schedule_id]['teacher_list'][$k]['teacherId'] =  $_coursestaff->vendor_id;
    $teacherAttendance['course_list'][$_course->course->gid][$_course->course_schedule_id]['teacher_list'][$k]['status'] =  0;
    }
    }
     * [ 增加老师考勤]
     * @param  [type] $scheduleId [课程时间表ID]
     * @param  [type] $vendorId [分配老师ID]
     * @param  [type] $substituter [替补老师ID] 可以为空
     * @param  [type] $status [状态]
     * @param  [type] $serviceTime [时间]
     * @return [type]          [本门课程老师签到的记录]
     */

    public function actionAddAttendance()
    {
        Yii::import('common.models.asainvoice.*');
        $scheduleId = Yii::app()->request->getParam('scheduleId', '');
        //$vendorId = Yii::app()->request->getParam('vendorId', array(37, 38, 39));
        $substituter = Yii::app()->request->getParam('substituterId', array());
        $status = Yii::app()->request->getParam('status', array());
        $serviceTime = Yii::app()->request->getParam('serviceTime', '');

        $criteria = new CDbCriteria;
        $criteria->compare('course_schedule_id', $scheduleId);
        $criteria->compare('service_start', date("Ymd",strtotime($serviceTime)));
        $countf = AsaStaffAttendance::model()->count($criteria);
        if(!$countf){
            $attendance = AsaCourseSchedule::model()->findByPk($scheduleId);
            if(!$attendance){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('global', "参数错误"));
                $this->showMessage();
            }

            $criteria = new CDbCriteria;
            $criteria->compare('program_id', $attendance->program_id);
            $criteria->compare('course_id', $attendance->course_id);
            $courseStaff = AsaCourseStaff::model()->findAll($criteria);
            $staffId = array();

            foreach($courseStaff as $staff){
                $staffId[$staff->vendor_id] = $staff->id;
            }

            foreach($status as $teacherId=>$_vendoeId){
                $model = new AsaStaffAttendance;
                $model->program_id = $attendance->program_id;
                $model->course_id = $attendance->course_id;
                $model->vendor_id = $teacherId;
                $model->status = $_vendoeId;
                $model->substituter = ($substituter[$teacherId]) ? $substituter[$teacherId] : "";
                $model->course_schedule_id = $attendance->course_schedule_id;
                $model->service_start = date("Ymd",strtotime($serviceTime));
                $model->service_end = date("Ymd",strtotime($serviceTime));
                $model->course_staff_id = $staffId[$teacherId];
                $model->updated = time();
                $model->updated_by = Yii::app()->user->id;
                if(!$model->save()){
                    $error = current($model->getErrors());
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('global', $error[0]));
                    $this->showMessage();
                }
            }

            $courseList = array();

            $courseList[$scheduleId]['courseName'] = $attendance->course->getTitle();
            $courseList[$scheduleId]['courseId'] = $attendance->course_id;
            $courseList[$scheduleId]['courseData'] = $attendance->time_start . " - " . $attendance->end_time;
            $courseList[$scheduleId]['scheduleId'] = $scheduleId;

            $criteria = new CDbCriteria;
            $criteria->compare('course_schedule_id', $attendance->course_schedule_id);
            $criteria->compare('service_start', date("Ymd",strtotime($serviceTime)));
            $attendance = AsaStaffAttendance::model()->findAll($criteria);
            if($attendance){
                $courseList[$scheduleId]['state'] = 1;
                foreach($attendance as $k=>$_teachder){
                    $courseList[$scheduleId]['teacher_list'][$k]['teacherName'] = $_teachder->vendorTeacher->getName();
                    $courseList[$scheduleId]['teacher_list'][$k]['teacherId'] = $_teachder->vendor_id;
                    $courseList[$scheduleId]['teacher_list'][$k]['status'] = $_teachder->status;
                    $courseList[$scheduleId]['teacher_list'][$k]['spare_teacherName'] = ($_teachder->vendor) ? $_teachder->vendor->getName() : "" ;
                    $courseList[$scheduleId]['teacher_list'][$k]['spare_teacherId'] = $_teachder->substituter;
                }
            }

            $this->addMessage('state', 'success');
            $this->addMessage('data', $courseList);
            $this->addMessage('message', Yii::t('message', "保存成功"));
            $this->addMessage('callback', 'attendanced');
            $this->showMessage();
        }else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('global', "已经有签到,清先删除签到"));
            $this->showMessage();
        }
        //echo CJSON::encode($courseList);
    }

    /**
     * [ 删除老师考勤]
     * @param  [type] $scheduleId [课程时间表ID]
     * @return [type]          [本门课程老师签到的记录]
     */

    public function actionDelAttendance()
    {
        Yii::import('common.models.asainvoice.*');

        $scheduleId = Yii::app()->request->getParam('scheduleId', '');
        $serviceTime = Yii::app()->request->getParam('serviceTime', '');

        $criteria = new CDbCriteria;
        $criteria->compare('course_schedule_id', $scheduleId);
        $criteria->compare('service_start', date("Ymd",strtotime($serviceTime)));
        $courseStaff = AsaStaffAttendance::model()->findAll($criteria);

        if($courseStaff){
            foreach($courseStaff as $_courseStaff){
                $_courseStaff->delete();
            }
            $this->addMessage('state', 'success');
            $this->addMessage('data', $scheduleId);
            $this->addMessage('message', Yii::t('message', "取消签到成功"));
            $this->addMessage('callback', 'cancelAttendance');
            $this->showMessage();
        }
    }


    public function actionDiscount()
    {
        $gid = Yii::app()->request->getParam('gid', 0);
        if (!$gid) {
            return false;
        }
        Yii::import('common.models.asainvoice.AsaDiscount');
        $criteria = new CDbCriteria;
        $criteria->compare('group_id', $gid);;
        $discount = AsaDiscount::model()->findAll($criteria);
        if (!$discount) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message','fail'));
            $this->showMessage(); 
        }
        $data = array();
        if ($discount) {
            foreach ($discount as $k => $v) {
                $data[$v->id] = $v->title;
            }
        }
        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->addMessage('message', Yii::t('message','success'));
        $this->showMessage(); 
    }

    /**
     * [payInvoice 账单付款]
     * @param  [type] $invoice [账单对象]
     * @param  [type] $payType [支付方式]
     * @return [type]          [支付结果]
     */
    public function payInvoice($invoice, $payType)
    {
        if (!$invoice) {
            return '账单不存在';
        }
        if ($invoice->status != AsaInvoice::STATS_UNPAID) {
            return '账单已付清';
        }
        $invoice->amount_actual = $invoice->amount_original;
        $invoice->pay_type = $payType;
        $invoice->status = AsaInvoice::STATS_PAID;
        $invoice->updated = time();
        $invoice->updated_userid = $this->staff->uid;
        if (!$invoice->save()) {
            return $error[0];
        }
        foreach ($invoice->item as $item) {
            $item->status = AsaInvoice::STATS_PAID;
            $item->updated = time();
            $item->updated_userid = $this->staff->uid;
            $item->save();
        }
        return true;
    }

    /**
     * [getInvoice 获取孩子账单]
     * @param  [type] $childid [孩子ID]
     * @param  [type] $status  [账单状态]
     * @return [type]          [description]
     */
    public function getInvoice($childid, $status)
    {
        $criteria = new CDbCriteria;
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('childid', $childid);
        $criteria->compare('status', $status);
        $invoice = AsaInvoice::model()->findAll($criteria);

        return $invoice;
    }

    //获取职位和来源
    public function getteachertype()
    {
        $typeList = CommonUtils::LoadConfig('CfgASA');
        $jobType = array();
        foreach($typeList['job_type'] as $k=>$job_type){
            $jobType['jobType'][$k] = (Yii::app()->language == 'zh_cn') ? $job_type['cn'] : $job_type['en'];
        }

        foreach($typeList['type'] as $k=>$job_type){
            $jobType['type'][$k] = (Yii::app()->language == 'zh_cn') ? $job_type['cn'] : $job_type['en'];
        }

        return $jobType;
    }

    public function actionList()
    {
        echo $this->getAction()->getId();
    }

    public function actionStaff()
    {
        Yii::import('common.models.asainvoice.*');
        $criteria = new CDbCriteria;    
        $teacherModel = new CActiveDataProvider('AsaVendor', array(
            'criteria' => $criteria,
            'sort' => array(
                'defaultOrder'=>'active DESC,updated DESC',
                ),
            'pagination'=>array(
                'pageSize'=>50,
            ),
        ));
        $typeList = CommonUtils::LoadConfig('CfgASA');
        $this->render('staff',array(
            'teacherModel' => $teacherModel,
            'typeList' => $typeList,
        ));
    }


    // 菜单项
    public function getMenu()
    {
        $mainMenu = array(
            array('label'=>Yii::t('management','课程管理'), 'url'=>array("/mcampus/asainvoice/index"), 'active'=>$this->getAction()->getId()=='index'?true:false),
            array('label'=>Yii::t('user',' 开具账单'), 'url'=>array("/mcampus/asainvoice/invoice"), 'active'=>$this->getAction()->getId()=='invoice'?true:false),
            array('label'=>Yii::t('user',' 老师管理'), 'url'=>array("/mcampus/asainvoice/staff"), 'active'=>$this->getAction()->getId()=='staff'?true:false),
            //array('label'=>Yii::t('user',' 汇总账单'), 'url'=>array("/mcampus/asainvoice/summary"), 'active'=>$this->getAction()->getId()=='summary'?true:false),
            array('label'=>Yii::t('user',' 教师考勤'), 'url'=>array("/mcampus/asainvoice/attendance"), 'active'=>$this->getAction()->getId()=='attendance'?true:false),
        );
        return $mainMenu;        
    }

    public function actionSelect(){
        $this->render('//layouts/common/branchSelect');
    }

    public function getTeacher($data)
    {
        $statuslist  =  array(
            '10' => "未付款",
            '20' => "款付清",
            '99' => "作废"
        );
        echo $statuslist[$data->asainvoice->status];
    }

    public function getJobType($data)
    {
        $typeList = CommonUtils::LoadConfig('CfgASA');
        return  (Yii::app()->language == 'zh_cn') ? $typeList['job_type'][$data->job_type]['cn'] : $typeList['job_type'][$data->job_type]['en']; ;
    }

    public function getUnitType($data)
    {
        $typeList = CommonUtils::LoadConfig('CfgASA');
        return  (Yii::app()->language == 'zh_cn') ? $typeList['unit_type'][$data->job_type]['cn'] : $typeList['unit_type'][$data->job_type]['en']; ;
    }
}