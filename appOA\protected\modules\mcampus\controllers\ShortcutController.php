<?php

class ShortcutController extends ProtectedController
{
    public $actionAccessAuths = array(
        'index' => 'ivystaff_hos_office',
    );

    public function init()
    {
        parent::init();

        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Campus Workspace');

        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue2.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue-tree/vue.min.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl.'/base/js/element/index.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/element/index.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/sortable/Sortable.min.js');

    }

    public function actionIndex()
    {
        $this->render('index');
    }

    public function actionAdminManageList()
    {
        $url = 'shortcuts/adminManage/list';
        $category = Yii::app()->request->getParam('category', 10);
        $this->remote($url, 'get',array(
            'category'=> $category
        ));
    }

    public function actionAdminManageSave(){
        $url = 'shortcuts/adminManage/save';
        $data = Yii::app()->request->getParam('data', 0);
        $this->remote($url, 'post',array(
            'data'=>$data
        ));
    }

    public function actionAdminManageSort(){
        $url = 'shortcuts/adminManage/sort';
        $ids = Yii::app()->request->getParam('ids', 0);
        $this->remote($url, 'post',array(
            'ids' => $ids
        ));
    }

    public function actionAdminManageRemove()
    {
        $url = 'shortcuts/adminManage/del';
        $group = Yii::app()->request->getParam('group', 0);
        $this->remote($url, 'post',array(
            'group' => $group,
        ));
    }

    public function remote($requestUrl, $method = 'post', $requestData = array())
    {
        $res = CommonUtils::requestDsOnline2($requestUrl, $requestData, $method);
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        }
        $this->showMessage();
    }
}
