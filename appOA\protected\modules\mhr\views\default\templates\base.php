<?php
$this->getCalendars();
if (count($this->calendarStartYear)) {
    $startYearsList = $this->calendarStartYear;
    $startYear = current($startYearsList);
} else {
    $n = date('n', time());
    $startYear = date('Y', time());
    if ($n <= 8) {
        $startYear = $startYear - 1;
    }
    $startYearsList = range($startYear, (OA::isShowNextYear(null,8,8) === TRUE) ? $startYear + 1 : $startYear);
}
$startYear = Yii::app()->request->getParam('startYear', $startYear);
$dates = $this->formatConfigDate($startYear);

//查询已分配年假的员工
$command = Yii::app()->db->createCommand();
$command->from(UserLeave::model()->tableName());
$command->where('flag=:flag and startyear=:startyear and type=:type', array(':flag' => 0, ':startyear' => $startYear, ':type' => UserLeave::TYPE_ANNUAL_LEAVE));
$asignData = $command->queryAll();
$asignDataList = array();
if (count($asignData)) {
    foreach ($asignData as $val) {
        $asignDataList[$val['staff_uid']] = UserLeave::getLeaveFormat($val['hours'], false);
    }
    unset($asignData);
}

//查询在职员工
$criter = new CDbCriteria;
$criter->select = 't.uid,t.name';
$criter->compare('t.rank', 0);
$criter->compare('profile.branch', $this->branchId);
$staff = User::model()->activeStaff()->with(
    array('profile' =>
        array(
            'select' => 'profile.first_name,profile.last_name,profile.occupation_en,profile.nationality',
            'with'=>'occupation'
        ),
    ))->findAll($criter);
$staffList = array();
$occupationList = array();
$countryList = array();
if (!empty($staff)) {
    foreach ($staff as $val) {
        $countryId = ($val->profile->nationality == 36) ? $val->profile->nationality : 1;
        $countryTxt = ($val->profile->nationality == 36) ? Yii::t('mhr', '中国') : Yii::t('mhr', '外国');
        $staffList[$val->uid]['uid'] = $val->uid;
        $staffList[$val->uid]['name'] = $val->getName();
        $staffList[$val->uid]['occupation'] = $val->profile->occupation_en;
        $staffList[$val->uid]['country'] = $countryId;
        $occupationList[$val->profile->occupation_en]['id'] = $val->profile->occupation_en;
        $occupationList[$val->profile->occupation_en]['name'] = $val->profile->occupation->cn_name;
        $countryList[$countryId]['id'] = $countryId;
        $countryList[$countryId]['name'] = $countryTxt;
    }
}
?>
<div id="leave-base-list">
    <div class="row">
        <?php  if (count($startYearsList) > 1):?>
        <div class="col-md-2">
            <?php
                foreach ($startYearsList as $val){
                    $mainMenu[] = array(
                    'label' => $val.'-'.($val+1),
                     'url' => array('//mhr/default/index', "category" => "base", 'startYear' => $val),
                    );
                }
                $this->widget('zii.widgets.CMenu', array(
                    'items' => $mainMenu,
                     'htmlOptions' => array('class' => 'nav nav-pills nav-stacked text-right background-gray'),
                     'activeCssClass' => 'active',
                     'itemCssClass' => ''
                    )
                );
            ?>
        </div>
        <?php endif;?>
        <div <?php  if (count($startYearsList) > 1){ echo 'class="col-md-10"';}else{echo 'class="col-md-12"';}?>>
            <div class="mb20"><button type="button" class="btn btn-primary mr10" onclick="renderStaffList(2);">按国籍显示</button><button type="button" class="btn btn-primary" onclick="renderStaffList(1);">按职位显示</button></div>
            <div id="occupation-list">
                <?php foreach ($occupationList as $val):?>
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h3 class="panel-title"><?php echo $val['name'];?></h3>
                        </div>
                        <?php echo CHtml::form($this->createUrl('//mhr/default/saveAsignHoliday'), 'Post', array('role'=>'form','class'=>'J_ajaxForm')); ?>
                        <div class="panel-body">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th width="60"><label><?php echo CHtml::checkBox('all',null,array('class'=>'J_newBlock_check'));?><span class="text"><?php echo Yii::t('mhr','全选');?></span></label></th>
                                        <th width="150"><?php echo Yii::t('mhr','用户姓名');?></th>
                                        <th width="100"><?php echo Yii::t('mhr','年假基数');?></th>
                                        <th width="260"><?php echo Yii::t('mhr','年假有效期');?></th>
                                    </tr>
                                </thead>
                                <tbody id="occupation-item-<?php echo $val['id'];?>">

                                </tbody>
                            </table>
                        </div>
                        <div class="panel-footer">
                            <button type="button" data-subcheck="1" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Save');?></button>
                            <button type="reset"  class="btn btn-default"><?php echo Yii::t('mhr', '重置');?></button>
                        </div>
                        <?php echo CHtml::endForm(); ?>
                    </div>
                <?php endforeach;?>
            </div>
            <div id="country-list">
                <?php foreach ($countryList as $val):?>
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h3 class="panel-title"><?php echo $val['name'];?></h3>
                        </div>
                        <?php echo CHtml::form($this->createUrl('//mhr/default/saveAsignHoliday'), 'post', array('role'=>'form','class'=>'J_ajaxForm')); ?>
                        <div class="panel-body">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th width="60"><label><?php echo CHtml::checkBox('all',null,array('class'=>'J_newBlock_check'));?><span class="text"><?php echo Yii::t('mhr','全选');?></span></label></th>
                                        <th width="150"><?php echo Yii::t('mhr','用户姓名');?></th>
                                        <th width="100"><?php echo Yii::t('mhr','年假基数');?></th>
                                        <th width="260"><?php echo Yii::t('mhr','年假有效期');?></th>
                                    </tr>
                                </thead>
                                <tbody id="country-item-<?php echo $val['id'];?>">

                                </tbody>
                            </table>
                        </div>
                        <div class="panel-footer">
                            <button type="button" data-subcheck="1" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Save');?></button>
                            <button type="reset"  class="btn btn-default"><?php echo Yii::t('mhr', '重置');?></button>
                        </div>
                        <?php echo CHtml::endForm(); ?>
                    </div>
                <?php endforeach;?>
            </div>
        </div>
    </div>
</div>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
$this->renderPartial('templates/_staffList');
?>
<script>
var staffList = <?php echo CJSON::encode($staffList);?>;
var occupationList = <?php echo CJSON::encode($occupationList);?>;
var countryList = <?php echo CJSON::encode($countryList);?>;
var dates = <?php echo CJSON::encode($dates);?>;
var asignDataList = <?php echo CJSON::encode($asignDataList);?>;
var startYear = <?php echo $startYear;?>;
var SingleStaffTemplate = _.template($('#staff-item-template').html());
$("#country-list").hide();
var classify = '';
$(function(){
    renderStaffList = function(type){
        var i=0;
        if (type == 1){
            $("#country-list").hide();
            $("#occupation-list").show();
            classify = 'country-list';
            $.each(occupationList,function(m,item){
                var count = $('#occupation-item-'+m).parent().find('td');
                if (_.size(count)>0){
                    i++;
                    return;
                }
            })
        }else{
            $("#country-list").show();
            $("#occupation-list").hide();
            classify = 'occupation-list';
            $.each(countryList,function(m,item){
                var count = $('#country-item-'+m).parent().find('td');
                if (_.size(count)>0){
                    i++;
                    return;
                }
            })
        }
        if (i>0){
            return false;
        }
        $.each(staffList,function(m,item){
            if (_.isUndefined(asignDataList[m])){
                var hours = 0;
            }else{
                var hours = asignDataList[m]
            }
            var view = SingleStaffTemplate({item:item,hours:hours,classify:classify});
            if (type == 1){
                var box = $('#occupation-item-'+item.occupation);
            }else{
                var box = $('#country-item-'+item.country);
            }
            box.append(view);
        })
    }
    renderStaffList(2);


//    $('#leave-base-list').on('click', 'a.J_newRow_edit',function(e){
//        e.preventDefault();
//        var tr = $(this).parents('tr');
//        var inputs = tr.find('input');
//        for(var i=0; i<inputs.length; i++){
//            var obj = $(inputs);
//            if ($(obj[i]).attr('disabled') == 'disabled'){
//                $(obj[i]).removeAttr('disabled');
//                $(this).find('i').removeClass('glyphicon-edit').addClass('glyphicon-ban-circle');
//            }else{
//                if (_.isUndefined(asignDataList[$(obj[i]).attr('datastaffid')])){
//                    var defaultVal = 0;
//                }else{
//                    var defaultVal = asignDataList[$(obj[i]).attr('datastaffid')];
//
//                }
//                $(obj[i]).val(defaultVal);
//                $(obj[i]).attr('disabled',true);
//                $(this).find('i').removeClass('glyphicon-ban-circle').addClass('glyphicon-edit');
//            }
//        }
//    });

//     $('#leave-base-list').on('click', '.J_newBlock_edit',function(e){
//        e.preventDefault();
//        var panel = $(this).parents('.panel');
//        var inputs = panel.find('input');
//        for(var i=0; i<inputs.length; i++){
//            var obj = $(inputs);
//            if ($(obj[i]).attr('disabled') == 'disabled'){
//                $(obj[i]).removeAttr('disabled');
//                $(obj[i]).parents('tr').find('i').removeClass('glyphicon-edit').addClass('glyphicon-ban-circle');
//                $(this).text('禁用');
//            }else{
//                $(obj[i]).attr('disabled',true);
//                $(obj[i]).parents('tr').find('i').removeClass('glyphicon-ban-circle').addClass('glyphicon-edit');
//                $(this).text('解禁');
//            }
//        }
//    });

     //单选
    $('#leave-base-list').on('click', 'input.J_check',function(e){
        var panel = $(this).parents('.panel');
        var total_check_all = panel.find('input.J_check');
        var all_newBlock_check = panel.find('input.J_newBlock_check');
        if(this.checked){
            $("#UserLeave_hours_"+classify+"_"+this.value).removeAttr('disabled');
            if(total_check_all.filter(':checked').length === total_check_all.length) {
                $(all_newBlock_check).attr('checked', true);
            }
        }else{
            $("#UserLeave_hours_"+classify+"_"+this.value).attr('disabled',true);
            $(all_newBlock_check).removeAttr('checked');
            if (_.isUndefined(asignDataList[this.value])){
                var defaultVal = 0;
            }else{
                var defaultVal = asignDataList[this.value];
            }
            $("#UserLeave_hours_"+classify+"_"+this.value).val(defaultVal);
        }
    })

    //全选
    $('#leave-base-list').on('click', 'input.J_newBlock_check',function(e){
        var panel = $(this).parents('.panel');
        var total_check_all = panel.find('input.J_check');
        if(this.checked){
            $(total_check_all).each(function(k,_this){
                _this.checked = true;
                $("#UserLeave_hours_"+classify+"_"+_this.value).removeAttr('disabled');
            });
        }else{
            $(total_check_all).each(function(k,_this){
                _this.checked = false;
                $("#UserLeave_hours_"+classify+"_"+_this.value).attr('disabled',true);
                 if (_.isUndefined(asignDataList[_this.value])){
                    var defaultVal = 0;
                }else{
                    var defaultVal = asignDataList[_this.value];
                }
                $("#UserLeave_hours_"+classify+"_"+_this.value).val(defaultVal);
            });
        }
    })


    succCallback = function(data){
        $.each(data,function(m,attr){
             var staffInput = $('#leave-base-list').find('input.form-control[datastaffid="'+m+'"]');
             $(staffInput).val(attr);
             asignDataList[m] = attr;
        })
    }
})
</script>