<?php

/**
 * This is the model class for table "ivy_users".
 *
 * The followings are the available columns in table 'ivy_users':
 * @property integer $uid
 * @property string $name
 * @property string $uname
 * @property string $email
 * @property string $url
 * @property string $user_avatar
 * @property string $user_regdate
 * @property string $user_icq
 * @property string $user_from
 * @property string $user_sig
 * @property integer $user_viewemail
 * @property string $actkey
 * @property string $user_aim
 * @property string $user_yim
 * @property string $user_msnm
 * @property string $pass
 * @property integer $posts
 * @property integer $attachsig
 * @property integer $rank
 * @property integer $level
 * @property string $theme
 * @property double $timezone_offset
 * @property string $last_login
 * @property string $umode
 * @property integer $uorder
 * @property integer $notify_method
 * @property integer $notify_mode
 * @property string $user_occ
 * @property string $bio
 * @property string $user_intrest
 * @property integer $isstaff
 * @property integer $admtype
 * @property integer $user_mailok
 * @property string $child_theme
 */
class User extends CActiveRecord
{
	public $changeEmail;
	public $changePassword;
	public $verifyPassword;
	public $uploadPhoto;
	public $iniPassword;
	public $chechOpUser;
    public $mphone;
    const HQ_BRANCH_ID = "BJ_TYG";
	/**
	 * Returns the static model of the specified AR class.
	 * @return User the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_users';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			//array('user_sig, bio', 'required'),
            array('user_sig', 'length','max'=>5),
			array('user_viewemail, posts, attachsig, rank, level, uorder, notify_method, notify_mode, isstaff, admtype, user_mailok', 'numerical', 'integerOnly'=>true),
			array('timezone_offset', 'numerical'),
			array('name, email', 'length', 'max'=>60),
			array('email','email',"on"=>"create"),
			array('changeEmail','email',"on"=>"update"),
			array('email','unique'),
			array('uname, user_yim', 'length', 'max'=>25),
			array('url, user_from, user_msnm, theme, user_occ', 'length', 'max'=>100),
			array('user_avatar', 'length', 'max'=>30),
			array('user_regdate, last_login, umode', 'length', 'max'=>10),
			array('user_icq', 'length', 'max'=>15),
			array('actkey', 'length', 'max'=>8),
			array('user_aim', 'length', 'max'=>18),
			array('pass', 'length', 'max'=>32),
			array('user_intrest, child_theme', 'length', 'max'=>150),
            array('bio','safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('uid, name, uname, email, url, user_avatar, user_regdate, user_icq, user_from, user_sig, user_viewemail, actkey, user_aim, user_yim, user_msnm, pass, posts, attachsig, rank, level, theme, timezone_offset, last_login, umode, uorder, notify_method, notify_mode, user_occ, bio, user_intrest, isstaff, admtype, user_mailok, child_theme', 'safe', 'on'=>'search'),
			array('verifyPassword', 'compare', 'compareAttribute'=>'changePassword', "on"=>"update", 'message' => Yii::t("user", "Password not match.")),
			array("uploadPhoto","file","types"=>"jpg, gif, png, jpeg", "allowEmpty"=>true),
			array("changeEmail",'unique', "attributeName"=>"email", "allowEmpty"=>true, "caseSensitive"=>false),

			array("email,changePassword","required","on"=>"create"),
			array('verifyPassword', 'compare', 'compareAttribute'=>'changePassword',"on"=>"create", 'message' => Yii::t("user", "Password not match.")),

			array('email,iniPassword','required','on'=>'staffCreate'),
			array('email','emailOrMP','on'=>'staffCreate, staffUpdate'),
			array('iniPassword', 'length', 'max'=>128, 'min' => 8, 'on'=>'staffCreate,addStaff, editStaff','message' => Yii::t("user", "Minimal password length 8 symbols.")),
			array('changePassword', 'length', 'max'=>128, 'min' => 8, 'on'=>'staffUpdate,create','message' => Yii::t("user", "Minimal password length 8 symbols."), 'allowEmpty'=>true),

			array('changePassword', 'length', 'max'=>128, 'min' => 4,'message' => Yii::t("user", "Minimal password length 4 symbols."),"allowEmpty"=>true),
            array('email','email','on'=>'sendAccount'),

            # appOA 员工管理场景 addStaff editStaff
            array('email, level', 'required', 'on'=>'addStaff, editStaff, addUser, addUser, editUser, checkUser'),
            array('iniPassword', 'required', 'on'=>'addStaff,checkUser'),
            array('email', 'email', 'on'=>'addStaff, editStaff, addUser, editUser, checkUser'),
			array('chechOpUser', 'required', 'on'=>'checkUser'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
            $relations = array(
		        'profile' => array(self::HAS_ONE, 'UserProfile', 'uid'),
                'profilesite' => array(self::HAS_ONE, 'UserProfileSite', 'uid'),
                'parent' => array(self::HAS_ONE, 'IvyParent', 'pid'),
                'parentF' => array(self::HAS_ONE, 'IvyParentFather', 'pid'),
                'parentM' => array(self::HAS_ONE, 'IvyParentMother', 'pid'),
                'staffInfo' => array(self::HAS_ONE, 'InfopubStaffExtend', 'userid'),
                'multBranch' => array(self::HAS_MANY, 'AdmBranchLink', 'uid', 'select' => 'schoolid'),
                'multBranchWithParam' => array(self::HAS_MANY, 'AdmBranchLink', 'uid', 'select' => 'schoolid', 'condition' => 'multBranchWithParam.type=:type'),
                'staff' => array(self::HAS_ONE, 'Staff', 'sid'),
                'clubUname' => array(self::HAS_ONE, 'ClubUsers', 'uid'),
                'staffApprover'=>array(self::HAS_ONE,'StaffApprover','staff_uid') //审批关系表
            );
            if (isset(Yii::app()->getModule('user')->relations))
                $relations = array_merge($relations, Yii::app()->getModule('user')->relations);
            return $relations;
    }

	public function emailOrMP($attribute, $params){
        if (!$this->hasErrors()){
			$ev=new CEmailValidator;
			$this->$attribute = trim($this->$attribute);
			if( $ev->validateValue($this->$attribute) === false ){
				if(strlen($this->$attribute) != 11 || !preg_match('/^1[3456789][0-9]{9}$/', $this->$attribute))//3456789
                $this->addError($attribute, Yii::t("userinfo", "Email or Mobile Phone Number"));
            }
        }
	}
	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'uid' => 'Uid',
			'name' => Yii::t("labels",'Chinese Name'),
			'uname' => 'Uname',
			'email' => Yii::t("labels",'Email'),
			'url' => 'Url',
			'user_avatar' => Yii::t("labels",'Photo'),
			'user_regdate' => 'User Regdate',
			'user_icq' => Yii::t("labels",'QQ'),
			'user_from' => 'User From',
			'user_sig' => Yii::t("labels",'English Title'),
			'user_viewemail' => 'User Viewemail',
			'actkey' => 'Actkey',
			'user_aim' => 'User Aim',
			'user_yim' => 'User Yim',
			'user_msnm' => 'User Msnm',
			'pass' => Yii::t("userinfo",'Password'),
			'posts' => 'Posts',
			'attachsig' => 'Attachsig',
			'rank' => 'Rank',
			'level' => Yii::t("userinfo",'Activate account'),
			'theme' => 'Theme',
			'timezone_offset' => 'Timezone Offset',
			'last_login' => 'Last Login',
			'umode' => 'Umode',
			'uorder' => 'Uorder',
			'notify_method' => 'Notify Method',
			'notify_mode' => 'Notify Mode',
			'user_occ' => 'User Occ',
			'bio' => Yii::t("userinfo",'Biography (Internal)'),
			'user_intrest' => Yii::t("labels",'Interests'),
			'isstaff' => 'Isstaff',
			'admtype' => 'Admtype',
			'user_mailok' => 'User Mailok',
			'child_theme' => 'Child Theme',
			'changeEmail' => 'Email',
			'changePassword' => Yii::t("userinfo",'Change Password'),
			'verifyPassword' => Yii::t("userinfo",'Verify Password'),
			'iniPassword' => Yii::t("labels",'Password'),
			'uploadPhoto' => Yii::t("labels",'Upload Image'),
			'chechOpUser' => Yii::t("labels",'以上信息是否正确'),
		);
	}

	public function scopes(){
		return array(
			'active' => array(
				'select' => 'uid, name, uname, pass, email, last_login',
			),
			'notsafe' => array(
				'select' => 'uid, name, uname, pass, email, last_login, level, isstaff, admtype',
			),
			'allStaff' => array(
				'condition' => 'isstaff = 1',
			),
			'activeStaff' => array(
				'condition' => 'isstaff = 1 AND level= 1 ',
			),
		);
	}


	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('uid',$this->uid);
		$criteria->compare('name',$this->name,true);
		$criteria->compare('uname',$this->uname,true);
		$criteria->compare('email',$this->email,true);
		$criteria->compare('url',$this->url,true);
		$criteria->compare('user_avatar',$this->user_avatar,true);
		$criteria->compare('user_regdate',$this->user_regdate,true);
		$criteria->compare('user_icq',$this->user_icq,true);
		$criteria->compare('user_from',$this->user_from,true);
		$criteria->compare('user_sig',$this->user_sig,true);
		$criteria->compare('user_viewemail',$this->user_viewemail);
		$criteria->compare('actkey',$this->actkey,true);
		$criteria->compare('user_aim',$this->user_aim,true);
		$criteria->compare('user_yim',$this->user_yim,true);
		$criteria->compare('user_msnm',$this->user_msnm,true);
		$criteria->compare('pass',$this->pass,true);
		$criteria->compare('posts',$this->posts);
		$criteria->compare('attachsig',$this->attachsig);
		$criteria->compare('rank',$this->rank);
		$criteria->compare('level',$this->level);
		$criteria->compare('theme',$this->theme,true);
		$criteria->compare('timezone_offset',$this->timezone_offset);
		$criteria->compare('last_login',$this->last_login,true);
		$criteria->compare('umode',$this->umode,true);
		$criteria->compare('uorder',$this->uorder);
		$criteria->compare('notify_method',$this->notify_method);
		$criteria->compare('notify_mode',$this->notify_mode);
		$criteria->compare('user_occ',$this->user_occ,true);
		$criteria->compare('bio',$this->bio,true);
		$criteria->compare('user_intrest',$this->user_intrest,true);
		$criteria->compare('isstaff',$this->isstaff);
		$criteria->compare('admtype',$this->admtype);
		$criteria->compare('user_mailok',$this->user_mailok);
		$criteria->compare('child_theme',$this->child_theme,true);
		$criteria->compare('attachment',$this->attachment,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	public function createParentRole(){
		//remove all groups for this user
		XoGroupsUsersLink::model()->deleteAll("uid=:uid", array(":uid"=>$this->uid));
		$link = new XoGroupsUsersLink;
		$link->groupid=9;
		$link->uid=$this->uid;
		$link->save();
	}

	/**
	* $lang = 'all', Yii::app()->language
	*
	**/
	public function getName($lang='all'){
		$sub = " (%s)";
		$cnName = $this->name;
		$enName = $this->user_sig. " " .$this->profile->first_name . " " . $this->profile->last_name;
		$enName = trim($enName);

		if($enName == "") return $cnName;
		elseif($cnName == "") return $enName;

		switch (Yii::app()->language){
			case "zh_cn":
				return $cnName . sprintf($sub, $enName);
			break;
			case "en_us":
				return $enName . sprintf($sub, $cnName);
			break;
		}
	}

    //固定中文在前
    public function getNameFixedCn($parentheses='1'){
        if($parentheses == 1){
            $sub = " (%s)";
        }else{
            $sub = " %s";
        }
        $cnName = $this->name;
        $enName = $this->user_sig. " " .$this->profile->first_name . " " . $this->profile->last_name;
        $enName = trim($enName);

        if($enName == "") return $cnName;
        elseif($cnName == "") return $enName;
        return $cnName . sprintf($sub, $enName);
    }

    public function getNameLang(){
        $cnName = $this->name;
        $enName = $this->profile->first_name . " " . $this->profile->last_name;
        $enName = trim($enName);

        if($enName == "") return $cnName;
        elseif($cnName == "") return $enName;

        switch (Yii::app()->language){
            case "zh_cn":
                return $cnName;
                break;
            case "en_us":
                return $enName;
                break;
        }
    }

    public function getPhotoSubUrl($thumb=true)
    {
        $avatar = '';
        $th = $thumb ? '/thumbs' : '';
        if ($this->user_avatar == 'blank.gif'){
            if ($this->profile->user_gender == 1){
                $avatar = Mims::CreateUploadUrl("users".$th."/male.png");
            }
            else {
                $avatar = Mims::CreateUploadUrl("users".$th."/female.png");
            }
        }
        else {
            $avatar = Mims::CreateUploadUrl("users".$th."/".$this->user_avatar);
        }
        return $avatar;
    }

    public function getPhotoSubUrlforOA($thumb=true)
    {
        $avatar = '';
        $th = $thumb ? '/thumbs' : '';
        if ($this->user_avatar == 'blank.gif'){
            if ($this->profile->user_gender == 1){
                $avatar = CommonUtils::CreateOAUploadUrl("users".$th, "male.png");
            }
            else {
                $avatar = CommonUtils::CreateOAUploadUrl("users".$th, "female.png");
            }
        }
        else {
            $avatar = CommonUtils::CreateOAUploadUrl("users".$th, $this->user_avatar);
        }
        return $avatar;
    }

    /*
     * 统计某学校某些职位下在职的员工数
     * @param string $schoolId          学校ID
     * @param array|int $positionIds    职位ID
     * @param boolean $admBranch        统计员工是否加上多校园情况
     * @return int num                  员工数
     */
    static public function countActiveStaff($schoolId,$positionIds,$admBranch = FALSE){
        $crite = new CDbCriteria;
        $crite->compare('profile.branch',$schoolId);
        $crite->compare('profile.occupation_en',$positionIds);
        $crite->scopes = 'activeStaff';
        $activeNum = self::model()->with('profile')->count($crite);
        if ($admBranch == TRUE){
            $admBranch = AdmBranchLink::model()->with('userProfile')->findAll('t.schoolid=:schoolid and t.type=:type',array(':schoolid'=>$schoolId,':type'=>AdmBranchLink::ADM_TYPE_CD));
            if (!empty($admBranch)){
                foreach ($admBranch as $val){
                    if ($val->userProfile->branch != $schoolId){
                        $activeNum++;
                    }
                }
            }
        }
        return $activeNum;
    }

    /*
     * 查询某学校某职位的用户
     * @param string $schoolId  学校ID
     * @param int $positionId   职位ID
     */
    public function getUserByPosition($schoolId =null,$positionId=0){
        $criter = new CDbCriteria;
        if ($schoolId!==null){
            $criter->compare('profile.branch', $schoolId);
        }
        if ($positionId){
             $criter->compare('profile.occupation_en', $positionId);
        }
        $criter->compare('t.isstaff', 1);
        $criter->addCondition('t.level>0 and t.rank<>7');
        $userModel = User::model()->with('profile')->findAll($criter);
        return (!empty($userModel)) ? $userModel : null;
    }

    /**
     * 是否为总部员工
     */
    public function isHQstaff($userid=0) {
		$userid = intval($userid);
		$officeBranch = CommonUtils::LoadConfig('CfgOffice');
        if($userid) {
            $user = User::model()->with('profile')->findByPk($userid);
            if($user && $user->level > 0 && $user->isstaff > 0) {
                return ( in_array($user->profile->branch, $officeBranch) );
            }
        } else {
            return ( in_array($this->profile->branch, $officeBranch) );
        }

        return false;
    }

    /**
     * 用户当前是否在线
     */
    public function isOnTYG()
    {
    	Yii::import('common.models.staff.*');
    	$today = strtotime(date('Y-m-d'));
    	$criteria = new CDbCriteria;
    	$criteria->compare('sid', $this->uid?$this->uid:0);
    	$criteria->compare('first_timestamp', '>'.$today);
    	$criteria->compare('state', 1);
    	return ApconnectRecord::model()->exists($criteria);
    }

    static function getSalutation(){
        return 	array(
            "Ms."=>"Ms.",
            "Mr."=>"Mr.",
            "Dr."=>"Dr.",
            "Miss"=>"Miss",
            "Mrs."=>"Mrs."
        );
    }

}