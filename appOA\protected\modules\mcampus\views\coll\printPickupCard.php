<?php
 if ($this->branchObj->type == 50) {
    $borderNone='borderNone';
    $logoWdith='width:200px';
   }else {
        $borderNone='ivy';
        $logoWdith='height:60px';

    } 
   if($this->branchId == 'BJ_QFF'){
       $borderNone='quanfaborderNone';
       $logoWdith='width:200px';
   } 	
?>
<style>
	.border{
		border:1px solid #ccc;
		border-radius:5px
	}
	.pb5{
		padding-bottom: 15px
	}
	.ptintImg{
		max-height: 120px;
		max-width:120px
	}
	.borderNone{
		/*border:1px solid #ccc;*/
		/*background: url(http://m2.files.ivykids.cn/cloud01-file-8025768FtLJ88p3PQvUtBNeUSKGQPXQBynO.jpg) no-repeat;*/
		/*background: url(http://m2.files.ivykids.cn/cloud01-file-5FpdD6zT4ph_0GlllvUzvxebzo_eM.jpg) no-repeat;*/
		background: url(https://m2.files.ivykids.cn/cloud01-file-5FlQzKJovaAD4S53-EuvacnHxpkC7.jpg) no-repeat;
		background-size:100% 100%;
		/* background:url('http://m2.files.ivykids.cn/cloud01-file-8025768FhElG0oo8VA01MzYMy9Wa9jXD4YB.jpg') no-repeat 100% 100% */
	}
    .quanfaborderNone{
		border:1px solid #ccc;
		background: url(https://m2.files.ivykids.cn/cloud01-file-8025768Fvhwd17XZV4h5Tm4E6j4YLlXZFl9.png) no-repeat;
		background-size:100% 100%;
	}
	.ivy{
		/*border:1px solid #ccc;*/
		background: url(http://m2.files.ivykids.cn/cloud01-file-8025768FqoRUgyX5mkN_M9pWPUH9fEAKZvK.jpg) no-repeat;
		background-size:100% 100%;
	}
	.borderRight{
		/*border-right:1px solid #ccc;*/
	}
	.padd0{
		padding: 0 !important
	}
	[v-cloak] {
		display: none;
	}
	.h100{
		height: 100%
	}
	.chilidPto{
		max-height:150px
	}
	.odd{
		padding-top:10px;
		border-top: 1px solid #ddd;
	}
	.classWid{
		display: inline-block;
		width:20%;
		margin-left: 0!important;
		margin-bottom:10px
	}
	.pl0{
		padding-left: 0
	}
	.relative{
		position: relative;
	}
	.absolute{
		position: absolute;
    bottom: 15px;
    left: 15px;
    font-size: 16px;
    color: #fff;
	}
	.datalist{
		float: left;
		margin:0 auto;
        width: 104px;
        overflow: hidden;
	}
	.datalist div{
		font-size:12px
	}
	.list{
		width:140px;
		margin:0 auto;
	}


    .parent-photo {
        width: 128px;
        border-radius: 3px;
    }
    .parent-text {
        margin-left: 57px;
        margin-top: 14px;
        font-size: 15px;
        font-weight: 500;
        color: #281E1C;
        line-height: 21px;
    }
    .child-photo {
        width: 104px;
        border-radius: 3px;
    }
    .child-text {
        margin-top: 6px;
        margin-bottom: 29px;
    }
    .child-text-01 {
        font-size: 13px;
        font-weight: 400;
        color: #281E1C;
        line-height: 17px;
    }
    .child-text-02 {
        font-size: 10px;
        font-weight: 400;
        color: #281E1C;
        line-height: 12px;
        margin-top: 3px;
    }
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li class="active"><a href="<?php echo $this->createUrl('index', array('branchId' => $this->branchId)); ?>">资料收集</a></li>
        <li class="active"><?php echo Yii::t('site', '详情') ?></li>
    </ol>
	<div class="row">
        <div class="col-md-2 col-sm-2">
            <ul class="nav nav-pills nav-stacked text-left background-gray" id="pageCategory">
                <li class="" ><a href="<?php echo $this->createUrl('index', array('branchId' => $this->branchId)); ?>">资料收集</a></li>
                <li class="" ><a href="<?php echo $this->createUrl('details', array('branchId' => $this->branchId)); ?>">详情</a></li>
            </ul>
        </div>

		<div class="col-md-10 col-sm-10" id='print' v-cloak>
            <p class='font14'><a href="<?php echo $this->createUrl('index', array('branchId' => $this->branchId)); ?>"><span class='glyphicon glyphicon-chevron-left font12' ></span> 资料收集</a> | <a   :href ="'<?php echo $this->createUrl('details', array('branchId' => $this->branchId)); ?>&coll_id='+coll_id+''" >详情</a> | <span>打印接送卡</span> </p>
            <div style="color: #F0AD4E;margin: 10px auto 0 auto"><span class="glyphicon glyphicon-info-sign"></span> 只可以打印审核通过的接送卡</div>
            <div v-if='classData.length!=0 && showData'>
                <p class="mb15 mt20">
                    <template v-for='(classdata,key,index) in classData'>
                        <label class="checkbox-inline classWid">
                            <input type="checkbox" id="inlineCheckbox1" :value="key" @click='pritData(classdata.classid,$event)'>{{classdata.title}}
                        </label>
                    </template>
                </p>
                <form action="<?php echo $this->createUrlReg('PrintCard', array('branchId' => $this->branchId,'coll_id'=>Yii::app()->request->getParam('coll_id', ''))); ?>" method="post">
                    <p v-if='arrChild.length!=0'>
                        <button class="btn btn-primary">打印</button>
                        <button class="btn btn-primary" name="for_vendor">打印 for Vendor</button>
                        <label class="ml15"><input type="checkbox" name="duplex_print" value="1"> 双面打印排版</label>
                        <label class="ml15"><input type="checkbox" v-model="selectAll"  @change="toggleSelectAll">全选</label>
                    </p>
                    <div class="col-md-12 padd0" >
                        <div class="col-md-6 col-sm-12 pb5 odd" v-for='(list,index) in arrChild'>
                            <div class="">
                                <label class="padd0 col-md-12">
                                    <p><input type="checkbox" :value='list.childId' name='childid[]' v-model="selectedItems"></p>

                                    <div class="<?php echo $borderNone;?> padd0" style="width: 544px;height:424px;">
                                        <div class="col-md-6 col-sm-6 col-xs-6 borderRight padd0 h100 text-center">
                                        <div class="" style="margin: 47px 21px 0 21px;">
                                            <div class="datalist" v-for='(parentlist,idx) in list.children' :class="(idx + 1) % 2==0?'ml20':''">
                                                <img :src="parentlist.avatar" alt="" class="child-photo">
                                                <div class="child-text">
                                                    <div class="child-text-01">{{parentlist.name}}</div>
                                                    <div class="child-text-02">{{parentlist.className}}</div>
                                                </div>
                                            </div>
                                            <div class="clearfix"></div>
                                        </div>
                                        </div>
                                        <div class="col-md-6 col-sm-6 col-xs-6">
                                            <div class="" style="margin-top: 111px;"><img :src="list.card.head_url" class="center-block parent-photo" alt="" style="width: 128px;"></div>
                                            <div class="parent-text">
                                                <div class=""><?php echo Yii::t('global','Name') ?><?php echo Yii::t('global',': ') ?>{{list.card.name}}</div>
                                                <div class=""><?php echo Yii::t('asa','Phone') ?><?php echo Yii::t('global',': ') ?>{{list.card.phone}}</div>
                                                <div class=""><?php echo Yii::t('site','Relationship') ?><?php echo Yii::t('global',': ') ?>{{list.card.relation}}</div>
                                            </div>
                                            <div class='text-left absolute'>
                                                    <div>{{list.school_year}}</div>
                                                    <div>{{list.school_title}}</div>
                                            </div>
                                        </div>
                                        <div class="clearfix"></div>
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div v-else class="alert alert-warning mt20" role="alert">暂无数据</div>
		</div>
	</div>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    var coll_id='<?php echo Yii::app()->request->getParam('coll_id', ''); ?>'
    var step_id='<?php echo Yii::app()->request->getParam('step_id', ''); ?>'
	var print = new Vue({
		el: "#print",
		data: {
            coll_id:coll_id,
            datas:{},
			startYearList:{},
			yid:'',
			YearList:'',
			classData:[],
			printchild:{},
			arrChild:[],
			logo:'',
			yearData:'',
			branchTitle:'',
            showData:false,
            selectedItems: [],
		},
        computed: {
            selectAll:{
                get() {
                    console.log(this.arrChild)
                    // 如果所有项都已选中，则全选框为选中状态，否则为未选中状态
                    return this.arrChild.every(item => this.selectedItems.includes(item.childId));
                },
                set(value) {
                    console.log(this.arrChild)
                    // 如果全选框被选中，则选中所有项，否则取消选中所有项
                    this.selectedItems = value ? this.arrChild.map(item => item.childId) : [];
                }
            }
        },
		created: function() {
            let that=this
            $.ajax({
                    url:'<?php echo $this->createUrlReg('pickupCardList'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:{
                        coll_id:this.coll_id,
                        step_id:step_id,
                    },
                    success:function(data){
                        if(data.state=="success"){
                           that.classData=data.data.class_list
                           that.printchild=data.data.card_list
                           that.showData=true
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
		},
		methods: {
            toggleSelectAll(event) {
                this.selectAll = event.target.checked;
            },
            Trim(str) { 
                if(str=='' || str==null){
                    return
                }
				var is_global='g'
				var result;
                
				result = str.replace(/(^\s+)|(\s+$)/g,"");
				if(is_global.toLowerCase()=="g")
				{
				result = result.replace(/\s/g,"");
				}
			  var tel=result.replace("+86",'')
				return tel;
		    },
			pritData(id,e){
				var checked=e.target.checked
				if(checked){
                    var selectChild = [];
					if(this.printchild[id]){
						 for(key in this.printchild[id]){
                             this.printchild[id][key]['pickups'].map((item, index) => {
                                 let children = this.printchild[id][key]['children']
                                 print.arrChild.push({
                                     children: children,
                                     card: item,
                                     classId: id,
                                     childId: this.printchild[id][key]['child_id'] + '-' + index
                                 })
                                 selectChild.push( this.printchild[id][key]['child_id'] + '-' + index)
                             })
			        	}
					}
				}else{
                    for(let i=print.arrChild.length-1; i>=0; i--) {
                        if(parseInt(id) == parseInt(print.arrChild[i].classId)){
                            print.arrChild.splice(i, 1);
                        }
                    }
				}
                this.selectedItems = this.arrChild.map(item => item.childId);//默认全选
			}
		},
	})  
</script>