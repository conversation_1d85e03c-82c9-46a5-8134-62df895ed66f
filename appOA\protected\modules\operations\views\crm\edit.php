<div class="fl">
<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'crm-form',
	'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm'),
));
$knowus = array();
foreach($this->cfg['knowus'] as $key=>$val){
    $knowus[$key]=  CommonUtils::autoLang($val['cn'], $val['en']);
}
?>

<div class="table_full">
    <table width="600" class="table_list" style="margin-bottom:0;">
        <col width="150" />
        <col width="400" />
        <col />
        <thead>
            <tr>
                <td colspan="3">添加来访记录</td>
            </tr>
        </thead>
        <tbody>
            <tr class="J_unique">
                <th><?php echo $form->labelEx($model,'email'); ?></th>
                <td>
                    <?php echo $form->textField($model,'email',array('maxlength'=>255,'class'=>'input length_6')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'email'); ?></div></td>
            </tr>
            <tr class="J_unique">
                <th><?php echo $form->labelEx($model,'tel'); ?></th>
                <td>
                    <?php echo $form->textField($model,'tel',array('maxlength'=>255,'class'=>'input length_6')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'tel'); ?></div></td>
            </tr>
            <?php if (!$model->isNewRecord):?>
            <tr class="J_unique">
                <th><?php echo $form->labelEx($model,'parent_name'); ?></th>
                <td>
                    <?php echo $form->textField($model,'parent_name',array('maxlength'=>255,'class'=>'input length_6')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'parent_name'); ?></div></td>
            </tr>
            <tr class="J_unique">
                <th><?php echo $form->labelEx($model,'child_name'); ?></th>
                <td>
                    <?php echo $form->textField($model,'child_name',array('maxlength'=>255,'class'=>'input length_6')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'child_name'); ?></div></td>
            </tr>
            <tr class="J_unique">
                <th><?php echo $form->labelEx($model,'birth_timestamp'); ?></th>
                <td>
                    <?php
                    $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                        "model"=>$model,
                        "attribute"=>"birth_timestamp",
                        "options"=>array(
                            'changeMonth'=>true,
                            'changeYear'=>true,
                            'dateFormat'=>'yy-mm-dd',
                        ),
                        'htmlOptions'=>array(
                            'class'=>'input'
                        ),
                    ));
                    ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'birth_timestamp'); ?></div></td>
            </tr>
            <tr class="J_unique">
                <th><?php echo $form->labelEx($model,'country'); ?></th>
                <td>
                    <?php $country = new Country;echo $form->dropDownList($model,'country',$country->getCountryList(),array('class'=>'select_6'));?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'country'); ?></div></td>
            </tr>
            <tr class="J_unique">
                <th><?php echo $form->labelEx($model,'address'); ?></th>
                <td>
                    <?php echo $form->textField($model,'address',array('maxlength'=>255,'class'=>'input length_6')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'address'); ?></div></td>
            </tr>
            <tr class="J_unique">
                <th><?php echo $form->labelEx($model,'communication'); ?></th>
                <td>
                    <?php echo $form->radioButtonList($model,'communication',IvyVisitsBasicInfo::communications());?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'communication'); ?></div></td>
            </tr>
            <tr class="J_unique">
                <th><?php echo $form->labelEx($model,'child_enroll'); ?></th>
                <td>
                    <?php
                    $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                        "model"=>$model,
                        "attribute"=>"child_enroll",
                        "options"=>array(
                            'changeMonth'=>true,
                            'changeYear'=>true,
                            'dateFormat'=>'yy-mm-dd',
                        ),
                        'htmlOptions'=>array(
                            'class'=>'input'
                        ),
                    ));
                    ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'child_enroll'); ?></div></td>
            </tr>
            <tr class="J_unique">
                <th><?php echo $form->labelEx($model,'knowus'); ?></th>
                <td>
                    <?php echo $form->dropDownList($model,'knowus',$knowus,array('class'=>'select_6'));?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'knowus'); ?></div></td>
            </tr>
            <tr class="J_unique">
                <th><?php echo $form->labelEx($model,'concerns'); ?></th>
                <td>
                    <?php echo $form->checkBoxList($model,'concerns',CHtml::listData($this->cfg['concerns'], 'k', CommonUtils::autoLang('cn', 'en')));?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'concerns'); ?></div></td>
            </tr>
            <tr class="J_unique">
                <th><?php echo $form->labelEx($model,'status'); ?></th>
                <td>
                    <?php echo $form->dropDownList($model,'status',$this->cfg['status'],array('class'=>'select_6'));?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'status'); ?></div></td>
            </tr>
            <?php endif;?>
        </tbody>
    </table>
</div>

<div class="btn_wrap">
    <div class="btn_wrap_pd">
        <?php
        if ($model->isNewRecord):
            echo CHtml::submitButton('检查是否登记过', array('class'=>'btn mr10', 'name'=>'checkex'));
        else:
            echo CHtml::htmlButton(Yii::t('global','Submit'), array('class'=>'btn btn_submit mr10 J_ajax_submit_btn'));
            echo CHtml::hiddenField('id', $model->id);
        endif;
        ?>
    </div>
</div>
<?php $this->endWidget(); ?>
</div>
<?php if (!$model->isNewRecord && $dataProvider):?>
<div class="fl">
    <?php
    $this->widget('ext.ivyCGridView.IvyCGridView', array(
        'id'=>'crm-r-grid',
        'dataProvider'=>$dataProvider,
        'colgroups'=>array(
            array(
                "colwidth"=>array(150,150,150,80),
            )
        ),
        'columns'=>array(
            array(
                'name'=>'category',
                'value'=>'Yii::app()->controller->cfg["category"][$data->category]',
            ),
            array(
                'name'=>'visit_timestamp',
                'value'=>'OA::formatDateTime($data->visit_timestamp)',
            ),
            array(
                'name'=>'schoolid',
                'value'=>'Yii::app()->controller->branchArr[$data->schoolid]',
            ),
            array(
                'class'=>'CButtonColumn',
                'template' => '{delete}',
                'updateButtonUrl'=>'Yii::app()->createUrl("/operations/crm/edit", array("id" => $data->id))',
                'updateButtonImageUrl'=>false,
            )
        ),
    ));
    ?>
    <div class="tac">
        <?php echo CHtml::link('添加', array('/operations/crm/editRecord', 'id'=>$model->id), array('class'=>'btn J_dialog', 'title'=>'添加'));?>
    </div>
</div>
<?php endif;?>