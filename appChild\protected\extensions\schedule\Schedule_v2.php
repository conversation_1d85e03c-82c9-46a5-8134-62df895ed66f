<?php
class Schedule_v2 extends CWidget{
	public $classId;
    public $weeknum;
    public $cssFile;
    public $assetsUrl;
	
	public function init(){
		Yii::import('common.models.schedule.*');

        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery');
        if (false !== $this->cssFile)
        {
            if (null === $this->cssFile)
                $this->cssFile = Yii::app()->theme->baseUrl.'/widgets/schedule/schedule_v2.css?t='.Yii::app()->params['refreshAssets'];
            $cs->registerCssFile($this->cssFile);
            $cs->registerCssFile(Yii::app()->theme->baseUrl.'/css/widgetbase.css?t='.Yii::app()->params['refreshAssets']);
            
            $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
            $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
            $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/phpfunc.js');
        }
	}
	
	public function run(){
        Yii::import('common.models.calendar.CalendarWeek');

        Mims::LoadHelper('HtoolKits');
        
        $class_model = IvyClass::model()->findByPk($this->classId);
        $yid        = $class_model->yid;

        $weeks = CalendarWeek::weeks($yid);
        $weekpk = array_keys($weeks);
        if (!$this->weeknum)
            $this->weeknum = end($weekpk);
        
        $crit = new CDbCriteria;
		$crit->compare('classid', $this->classId);
		$crit->compare('weeknumber', $this->weeknum);
		$schedule = ClassScheduleV2::model()->with('data')->find($crit);

        $sArr = explode('|', $class_model->periodtime);
        $classTime['start']  = strtotime($sArr[0].':'.$sArr[1]);
        $classTime['end']    = strtotime($sArr[2].':'.$sArr[3]);

		$this->render('schedule_v2',array('schedule'=>$schedule, 'class_model'=>$class_model, 'classTime'=>$classTime, 'weeks'=>$weeks, 'weeknum'=>$this->weeknum));
	}
	
}