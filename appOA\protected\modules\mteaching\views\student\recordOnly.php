<style>
    .listCom{
        border-radius: 8px;
        border: 1px solid #E8EAED;
        padding:24px 0 24px 24px
    }
    .labelbg{
        padding:2px;
        background: #F7F7F8;
        border-radius: 2px;
        border: 1px solid #D9D9D9;
    }
    .supportPlan{
        background: #FFFFFF;
        border: 1px solid #E8EAED;
        border-radius:4px;
    }

    .handlingResults{
        background: rgba(77,136,210,0.06);
        border-radius: 4px;
        border: 1px solid #E8EAED;
    }
    .handlingTitle{
        background: linear-gradient(90deg, #84BDEB 0%, #4D88D2 100%);
        border-radius: 4px 4px 0px 0px;
        border: 1px solid #E8EAED;
        padding:16px 0;
        color: #FFFFFF;
        text-align:center
    }
    .labelTag{
        width: 18px;
        height: 18px;
        background: #666666;
        border-radius: 9px;
        border: 1px solid #FFFFFF;
        display:inline-block;
        text-align: center;
        line-height: 17px;
        color: #fff;
        font-size: 12px;
    }
    .labelTagRed{
        background:#D9534F
    }
    .plr0{
        padding-left:0;
        padding-right:0
    }
    .font24{
        font-size:24px
    }
    .lineHeight{
        line-height:1
    }
    .blueBlock{
        width: 6px;
        height: 16px;
        background: #4D88D2;
        border-radius: 1px;
        display:block
    }
    .warningBg{
        background: #FAFAFA;
        border-radius: 4px;
        padding: 16px 24px;
        font-size:14px;
        height:140px
    }
    .line-clamp{
        overflow: hidden;
        word-break: break-all;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
    }
    .badgeNum{
        width: 18px;
        height: 18px;
        background: #666666;
        border-radius: 9px;
        border: 1px solid #FFFFFF;
        color:#fff;
        font-size:12px;
        text-align:center;
        line-height:18px;
        display:inline-block;
    }
    .warningStu{
        padding:8px 10px;
        display:inline-block;
    }
    .warningStu:hover{
        border-radius: 4px;
        background: #EBEDF0;
        cursor: pointer;
    }
    .allApply{
        width: 216px;
        height: 98px;
        background: #EEF4FB;
        border-radius: 4px;
        /* border: 2px solid #4D88D2; */
        margin-right:24px;
        padding:12px 16px;
        display:inline-block;
    }
    .bgPink{
        background: #FCF2F1;
    }
    .bgYellow{
        background: #FDF6ED;
    }
    .yearList{
        display: inline-block;
        position: absolute;
        right: 0;
        top: 3px;
    }
    .yearList .dropdown-menu{
        right:0;
        left:auto
    }
    .moreChild{
        height: 75px;
        background: #FFFFFF;
        border-radius: 4px;
        border: 1px solid #E8EAED;
        padding:16px;
        margin-bottom:16px;
        align-items:center
    }
    .classMore{
        height: 50px;
        background: #FFFFFF;
        border-radius: 4px;
        border: 1px solid #E8EAED;
        padding:16px;
        margin-bottom:16px;
        align-items:center
    }
    .moreChild:hover,.warningClassList:hover,.classMore:hover{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        border: 1px solid #4D88D2;
        cursor: pointer;
    }
    .warningClass{
        width:250px;
        background: #FAFAFA;
    }
    .warningClassList{
        padding:10px;
        font-size:14px;
        border: 1px solid #FAFAFA;
        color:#333;
    }
    .warningClassListCheck{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        border: 1px solid #4D88D2;
        cursor: pointer;
    }
    .bgPinkBorder{
        border:1px solid #D9534F
    }
    .bgYellowBorder{
        border:1px solid #F0AD4E
    }
    .el-loading-mask{
        z-index:1000
    }
    .height32{
        height:32px
    }
    .p12{
        padding:12px !important
    }
    .hoverImg:hover{
        /* border: 1px solid #4D88D2; */
        cursor: pointer;
    }
    .warning_outline{
        padding:6px 8px;
        background:#F7F7F8;
        margin-left:15px;
        border-radius:2px;
        color:#666666;
        font-size:12px
    }
    .dp-text-ellipsis-wrapper {
        display: flex;
        overflow: hidden;
        font-size: 12px;
        line-height: 20px;
    }
    .text {
        position: relative;
        overflow: hidden;
        line-height: 20px;
        text-align: justify;
        text-overflow: ellipsis;
        word-break: break-all;
    }
    .text::before {
        float: right;
        height: calc(100% - 20px);
        content: '';
    }
    .expand {
        position: relative;
        float: right;
        clear: both;
        margin-left: 20px;
        font-size: 14px;
        padding: 0 0 0 8px;
        color: #4D88D2;
        line-height: 20px;
        border-radius: 4px;
        cursor: pointer;
        z-index: 10;
    }
    
    .expand::before {
        position: absolute;
        left: 1px;
        color: #333;
        transform: translateX(-100%);
        content: '...';
    }
    .praise{
        width: 19px;
        height: 19px;
        margin-right: 6px;
    }
    .word_break{
        word-break: break-word;
    }
</style>
<div class="" id='container' v-cloak >
    <div class='relative'  v-if='initList.warning'>
        <ul class="nav nav-tabs" role="tablist">
            <li role="presentation" class="active" ><a href="javascript:void(0)" @click='initData(currentYear)'   role="tab" data-toggle="tab"><?php echo Yii::t("referral", "Secondary School"); ?></a></li>
        </ul>
        <div class='yearList'>
            <div class="btn-group">
                <button type="button" class="btn btn-link dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    {{initSchoolYear[currentYear]}} <span class="caret"></span>
                </button>
                <ul class="dropdown-menu">
                    <li v-for='(list,key,index) in initSchoolYear' @click='initData(key)'><a href="javascript:void(0)">{{list}}</a></li>
                </ul>
            </div>
        </div>
    </div>
    <div  v-loading="initLoading"  v-if='initList.warning'>
        <div class='flex mt20 mb16'>
            <span class='color6 font14 mt5'><?php echo Yii::t("global", "Filter"); ?>：</span>
            <div class='flex1'>
                <span v-if='searchName==""'>
                    <el-button  size="mini" @click='filterStu'><span class='glyphicon glyphicon-filter'> </span> <?php echo Yii::t("labels", "Student"); ?></el-button>
                    <el-button  size="mini" @click='filterClass'><span class='glyphicon glyphicon-filter'> </span> <?php echo Yii::t("labels", "Class"); ?></el-button>
                </span>
                <span v-else>
                    <el-tag
                        closable
                        :disable-transitions="false"
                        @close="handleClose()">
                        {{searchName}}
                    </el-tag>
                </span>
                <span class='warning_outline'><span class='el-icon-info mr5 font14'></span><?php echo Yii::t("referral", "Click on a student's photo or class to add a filter"); ?></span>
                <div class='mt12'>
                    <el-select v-model="filterBehaviorValue" style='width:240px' size='small' clearable placeholder="<?php echo Yii::t("referral", "All Problem Behavior"); ?>" @change='filterBehavior()'>
                        <el-option
                            label="<?php echo Yii::t("referral", "All Problem Behavior"); ?>"
                            value="">
                        </el-option>
                        <el-option
                            v-for='(list,index) in initList.searchCan'
                            :key="list.value"
                            :label="list.title"
                            :value="list.value">
                            <span class='flex align-items' v-if='list.value=="POSITIVEBEHAVIOR"'>
                                <img  class='praise'  src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/praise.png' ?>" alt="">
                                <span class='flex1'>{{ list.title }}</span>
                            </span>
                            <span class='flex align-items' v-else-if='list.value=="WINTERLANGUAGE"'>
                                    <img  class='praise'  src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/special1.png' ?>" alt="">
                                <span class='flex1'>{{ list.title }}</span>
                                <span class="badge ml20"  v-if='dataList.countProblemBehavior && dataList.countProblemBehavior[list.value]'>{{dataList.countProblemBehavior[list.value]}}</span>
                            </span>
                            <span class='flex align-items' v-else>
                                <span class='flex1'>{{ list.title }}</span>
                            </span>
                        </el-option>
                    </el-select>
                </div>
            </div>
            <!-- <span class='font14 color3'><?php echo Yii::t("referral", "Filter by problem behavior"); ?>：</span>
            <el-select v-model="filterBehaviorValue" size='small' clearable placeholder="<?php echo Yii::t("referral", "Problem Behavior"); ?>" @change='filterBehavior()'>
                <el-option
                    v-for='(list,index) in config.problem_behavior'
                    :key="list.value"
                    :label="list.title"
                    :value="list.value">
                </el-option>
            </el-select> -->
        </div>
        <div v-if='dataList.list'>
            <el-table
                :header-cell-style="{background:'#fafafa',color:'#333'}"
                :data="dataList.list"
                style="width: 100%;font-size:12px"
                v-loading="indexLoading"
                element-loading-background="rgba(255, 255, 255, 1)"
                :default-sort = "{prop: 'created_at', order: 'descending'}"
                @sort-change="sort_change"
            >
                <template slot="empty">
                    <?php echo Yii::t("ptc", "No Data"); ?>
                </template>
                <el-table-column
                    prop="child"
                    label="<?php echo Yii::t("newDS", "Student"); ?>"
                    min-width="200">
                    <template slot-scope="scope">
                        <div v-if='scope.row.child.length==1'>
                            <div class="media flex  align-items" v-for='(list,index) in scope.row.child'>
                                <div class="media-left pull-left media-middle relative">
                                <!-- <el-tooltip class="item" effect="dark" content="<?php echo Yii::t("referral", "Filter by this student"); ?>" placement="top"> -->
                                    <img :src="dataList.child_list[list.id].avatar" data-holder-rendered="true" class="avatar hoverImg" @click='filterList(list.id,dataList.child_list[list.id].name,"child")'>
                                <!-- </el-tooltip> -->
                                </div>
                                <div class="pull-left media-middle">
                                    <div class=" color3 lineHeight " >
                                        <!-- <el-tooltip class="item" effect="dark" content="<?php echo Yii::t("referral", "Filter by this student"); ?>" placement="top"> -->
                                            <span>
                                                <span @click='filterList(list.id,dataList.child_list[list.id].name,"child")' class='cur-p'>{{dataList.child_list[list.id].name}}</span> 
                                                <span v-if='list.tag==2 || list.tag==3' class='glyphicon glyphicon-bookmark font12' :class='list.tag==2?"waitingColor":list.tag==3?"colorRed":""'></span>
                                            </span>
                                            <!-- <span  :class='list.tag==2?"tagLabelYellow":list.tag==3?"tagLabel":""'>{{showTitle(list.tag,'ISP_type')}}</span> -->
                                        <!-- </el-tooltip> -->
                                    </div>
                                    
                                    <div class="color6 font12 lineHeight mt8">
                                        <!-- <el-tooltip class="item" effect="dark" content="<?php echo Yii::t("referral", "Filter by this class"); ?>" placement="top"> -->
                                            <span class='cur-p' @click='filterList(list.class_id,dataList.child_list[list.id].className,"class")'>{{dataList.child_list[list.id].className}}</span>
                                        <!-- </el-tooltip> -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-else>
                            <span v-for='(list,index) in scope.row.child' >
                                <!-- <el-tooltip class="item" effect="dark" content="<?php echo Yii::t("referral", "Filter by this student"); ?>" placement="top"> -->
                                    <img :src="dataList.child_list[list.id].avatar" data-holder-rendered="true" class="avatar32 mb8 mr4" @click='filterList(list.id,dataList.child_list[list.id].name,"child")'>
                                <!-- </el-tooltip> -->
                            </span>
                            <el-popover
                                placement="bottom"
                                :ref="`popover-${scope.row._id}`"
                                trigger="click">
                                <div class="media" v-for='(list,index) in scope.row.child'>
                                    <div class="media-left pull-left media-middle relative">
                                        <!-- <el-tooltip class="item" effect="dark" content="<?php echo Yii::t("referral", "Filter by this student"); ?>" placement="top"> -->
                                            <img :src="dataList.child_list[list.id].avatar" @click='filterList(list.id,dataList.child_list[list.id].name,"child",scope.row._id)' data-holder-rendered="true" class="avatar hoverImg">
                                        <!-- </el-tooltip> -->
                                    </div>
                                    <div class="pull-left media-middle">
                                        <div class="font12 color6 lineHeight mt8"> 
                                            <!-- <el-tooltip class="item" effect="dark" content="<?php echo Yii::t("referral", "Filter by this student"); ?>" placement="top"> -->
                                                <span>
                                                    <span @click='filterList(list.id,dataList.child_list[list.id].name,"child",scope.row._id)' class='cur-p'>{{dataList.child_list[list.id].name}}</span> 
                                                    <span v-if='list.tag==2 || list.tag==3' class='glyphicon glyphicon-bookmark font12' :class='list.tag==2?"waitingColor":list.tag==3?"colorRed":""'></span>
                                                </span>
                                            <!-- </el-tooltip> -->
                                        </div>
                                        <div class="color6 font12 lineHeight mt8">
                                            <!-- <el-tooltip class="item" effect="dark" content="<?php echo Yii::t("referral", "Filter by this class"); ?>" placement="top"> -->
                                                <span class='cur-p ' @click='filterList(list.class_id,dataList.child_list[list.id].className,"class",scope.row._id)'>{{dataList.child_list[list.id].className}}</span>
                                            <!-- </el-tooltip> -->
                                        </div>
                                    </div>
                                </div>
                                <span class='bluebg  cur-p' slot="reference"><?php echo Yii::t("referral", "Total "); ?>{{scope.row.child.length}}<?php echo Yii::t("referral", " "); ?></span>
                            </el-popover>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="id"
                    label="<?php echo Yii::t("labels", "ID"); ?>"
                    min-width="150">
                    <template slot-scope="scope">
                        <div class='color6'>No.{{scope.row.id}}</div>
                        <div class='color3'>{{dataList.staff_info[scope.row.created_by].name}}</div>
                        <el-tooltip class="item" effect="dark" :content="scope.row.created_date" placement="top">
                            <span class='color6 cur-p'>{{scope.row.diff_date}}</span>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="id"
                    label="<?php echo Yii::t("referral", "Event Description"); ?>"
                    min-width="450">
                    <template slot-scope="scope">
                    <div class="dp-text-ellipsis-wrapper"   :class='scope.row._id'  v-if='showHtml'>
                        <div class="text cur-p" :style="scope.row.more?textStyleObject:''" ref="text"  @click='scope.row.more=!scope.row.more' v-if='shouldShowExpandButton[scope.row._id]' >
                            <el-tooltip class="item" effect="dark" content="<?php echo Yii::t("behavior", "Positive Behavior"); ?>" placement="top">
                                <img  class='praise'  v-if='scope.row.detail.subtype=="POSITIVEBEHAVIOR"' src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/praise.png' ?>" alt="">
                            </el-tooltip><span :class="'span_'+scope.row._id" class='word_break'>{{scope.row.detail.desc}}</span>
                            <span class="expand" v-if='scope.row.more'></span>
                            <!-- <span v-if='!scope.row.more' class='cur-p bluebg' @click='scope.row.more=true'>收起</span> -->
                        </div>
                        <div  v-else-if='!shouldShowExpandButton[scope.row._id]' style='line-height:20px'>
                            <el-tooltip class="item" effect="dark" content="<?php echo Yii::t("behavior", "Positive Behavior"); ?>" placement="top">
                                <img  class='praise'  v-if='scope.row.detail.subtype=="POSITIVEBEHAVIOR"' src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/praise.png' ?>" alt="">
                            </el-tooltip><span :class="'span_'+scope.row._id" class='word_break'>{{scope.row.detail.desc}}</span>
                        </div>
                    </div>
                    </template>
                </el-table-column>
                <!-- <el-table-column
                    prop="created_by"
                    min-width="150"
                    label="<?php echo Yii::t("referral", "Submitter"); ?>">
                    <template slot-scope="scope">
                        <span class='color3'>{{dataList.staff_info[scope.row.created_by].name}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="created_at"
                    min-width="100"
                    sortable='custom'
                    label="<?php echo Yii::t("referral", "Time"); ?>">
                    <template slot-scope="scope">
                        <el-tooltip class="item" effect="dark" :content="scope.row.created_date" placement="top">
                            <span class='color3 font14'>{{scope.row.diff_date}}</span>
                        </el-tooltip>
                    </template>
                </el-table-column> -->
                <!-- <el-table-column
                    prop="office_user"
                    label="<?php echo Yii::t("referral", "Status"); ?>"
                    min-width="300">
                    <template slot-scope="scope">
                        <div class='flex'>
                            <div class='flex' v-if='scope.row.just_record==0'>
                                <div class='text-center minWidth mr50' v-for='(list,index) in scope.row.nodes_order'>
                                    <div v-if='scope.row.nodes_by_node[list].status==1'>
                                        <div class='relative'>
                                            <div class='firstBorder' v-if='scope.row.nodes_order.length>1 && index+1<scope.row.nodes_order.length'></div>
                                            <img :src="dataList.staff_info[scope.row.nodes_by_node[list].updated_by].photoUrl" alt="" class='avatar32'>
                                        </div>
                                        <div class='color3 font14'>{{scope.row.nodes_order_title[index]}}</div>
                                        <div class='color6 font12'>
                                            <el-tooltip class="item" effect="dark"  placement="top">
                                                <div slot="content">{{dataList.staff_info[scope.row.nodes_by_node[list].updated_by].name}}<?php echo Yii::t("referral", "Waiting"); ?><br/>{{scope.row.nodes_by_node[list].updated_at}}</div>
                                                <div class='color6'>{{scope.row.nodes_by_node[list].diff_date}}</div>
                                            </el-tooltip>
                                        </div>
                                    </div>
                                    <div v-else>
                                        <div>
                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/pending.png' ?>" alt="" class='avatar32'>
                                        </div>
                                        <div class='color3 font14'>{{scope.row.nodes_order_title[index]}}</div>
                                        <div class='font12 waitingColor'><?php echo Yii::t("referral", "In Process"); ?></div>
                                    </div>
                                </div>
                            </div>
                            <div v-if='scope.row.just_record==1'>
                                <?php echo Yii::t("referral", "Record only, no followup needed"); ?>
                            </div>
                        </div>
                    </template>
                </el-table-column> -->
                <el-table-column
                    prop="office_user"
                    label="<?php echo Yii::t("newDS", "Actions"); ?>"
                    width="120">
                    <template slot-scope="scope">
                        <el-button type="text" @click='showDetails(scope.row._id,"init")'class='font12 bluebg' v-if='scope.row.can_processing==1'><?php echo Yii::t("referral", "Process"); ?></el-button>
                        <el-button type="text" @click='showDetails(scope.row._id,"init")'class='font12 bluebg' v-if='scope.row.can_processing==0'><?php echo Yii::t("global", "View Detail"); ?></el-button>
                    </template>
                </el-table-column>
            </el-table>
            <nav aria-label="Page navigation" v-if='CopyPages.count_page>1'  class="text-left ml10">
                <ul class="pagination">
                    <li v-if='pageNum >1'>
                        <a href="javascript:void(0)" @click="plus(1)" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    <li v-else class="disabled">
                        <a aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    <li class="previous" v-if='pageNum >1'>
                        <a href="javascript:void(0)" @click="prev(pageNum)">‹</a>
                    </li>
                    <li class="disabled" v-else>
                        <a href="javascript:void(0)">‹</a>
                    </li>
                    <li v-for='(data,index) in pages.count' :class="{ active:data==pageNum }">
                        <a href="javascript:void(0)" @click="plus(data)">{{data}}</a>
                    </li>
                    <li class="previous" v-if='pageNum <CopyPages.count_page'>
                        <a href="javascript:void(0)" @click="next(pageNum)">›</a>
                    </li>
                    <li class="previous disabled" v-else>
                        <a href="javascript:void(0)">›</a>
                    </li>
                    <li v-if='pageNum <CopyPages.count_page'>
                        <a href="javascript:void(0)" @click="plus(CopyPages.count_page)" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li v-else class="disabled">
                        <a aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                </ul>
                <div class='summary mb10'>第 <span v-if='pageNum*CopyPages.per_page-CopyPages.per_page==0'>1</span><span v-else>{{pageNum*CopyPages.per_page-CopyPages.per_page}}</span>-{{pageNum*CopyPages.per_page}} 条, 共 {{CopyPages.total}} 条.</div>
            </nav>
        </div>
    </div>
    <!-- 认定记录 -->
    <div class="modal fade" id="identifiedModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("referral", "Behavior Submission Entries"); ?></h4>
                </div>
                <div class="modal-body p24 overflow-y scroll-box font14" :style="'max-height:'+(height)+'px;overflow-x: hidden;'" v-if='affirmRecordList.child_info'>
                    <div class="media pb10" >
                        <div class="media-left pull-left media-middle relative">
                            <img :src="affirmRecordList.child_info[affirmData.child_id].avatar" data-holder-rendered="true" class="avatar">
                        </div>
                        <div class="pull-left media-middle">
                            <div class="mt4 color3">
                                <span class='font14'>{{affirmRecordList.child_info[affirmData.child_id].name}}</span>
                                <span v-for='(item,ind) in affirmRecordList.child_info[affirmData.child_id].label' >
                                <span v-if='item.flag==2 || item.flag==3' class='glyphicon glyphicon-bookmark font12' :class='item.flag==2?"waitingColor":item.flag==3?"colorRed":""'></span>
                                    <!-- <span v-if='item.flag==2 || item.flag==3' :class='item.flag==2?"tagLabelYellow":item.flag==3?"tagLabel":""'>{{item.name}}</span> -->
                                </span>
                            </div>
                            <div class="color6 font12">{{affirmRecordList.child_info[affirmData.child_id].className}}</div>
                        </div>
                    </div>
                    <div class='row bgGrey m0'>
                        <div class='col-md-7 '>
                            <div class='font12 color6 mb12'><?php echo Yii::t("referral", "A behavior issue"); ?>：{{affirmRecordList.stat.behaviorNum}}</div>
                            <div class='flex mt12'>
                                <div class='flex1 text-center'>
                                    <div class='font14 color3 fontBold'>{{affirmRecordList.stat.inSchoolSuspensionNum}}</div>
                                    <div class='mt4 font12 color3'><?php echo Yii::t("referral", "In school suspension"); ?></div>
                                </div>
                                <div class='flex1 text-center'>
                                    <div class='font14 color3 fontBold'>{{affirmRecordList.stat.outSchoolSuspensionNum}}</div>
                                    <div class='mt4 font12 color3'><?php echo Yii::t("referral", "Out of school suspension"); ?></div>
                                </div>
                                <div class='flex1 text-center'>
                                    <div class='font14 color3 fontBold'>{{affirmRecordList.stat.serviceNum}}</div>
                                    <div class='mt4 font12 color3'><?php echo Yii::t("referral", "Community Service"); ?></div>
                                </div>
                            </div>
                        </div>
                        <div class='col-md-5 borderLeft'>
                            <div class='font12 color6 mb12 pl16'><?php echo Yii::t("report", "Other"); ?>：{{affirmRecordList.stat.otherNum}}</div>
                            <div class='flex mt12'>
                                <div class='flex1 text-center'>
                                    <div class='font14 color3 fontBold'>{{affirmRecordList.stat.undeterminedNum}}</div>
                                    <div class='mt4 font12 color3'><?php echo Yii::t("referral", "NOT a behavior issue"); ?></div>
                                </div>
                                <div class='flex1 text-center'>
                                    <div class='font14 color3 fontBold'>{{affirmRecordList.stat.recordOnlyNum}}</div>
                                    <div class='mt4 font12 color3'><?php echo Yii::t("referral", "Record only"); ?></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-for='(item,idx) in affirmRecordList.list' class='p16 presenter mt12 relative font14'>
                        <div v-if='item.result.identified==3'>
                            <div class='flex font14'>
                                <div class='flex1 color3 '>No.{{item.id}}</div>
                                <span class='btn-link' @click='showDetails(item.behavior_id,"show")'><?php echo Yii::t("global", "View Detail"); ?></span>
                            </div>
                            <div class='bgGrey mt16 p12'>
                                <div class='color6 font12'><?php echo Yii::t("referral", "Event Description"); ?>：</div>
                                <div class='color3 font12 mt5'>{{item.desc}}</div>
                            </div>
                            <div class='flex mt16'>
                                <span class='color6 font14'><?php echo Yii::t("referral", "Followup"); ?>：</span>
                                <div class='flex1 color3'><?php echo Yii::t("referral", "Record only, no followup needed"); ?></div>
                            </div>
                            <div class='flex mt12 align-items '>
                                <span class='color6 font14'><?php echo Yii::t("referral", "By"); ?>：</span>
                                <div class='flex1 color3'>
                                    <div class='flex align-items '>
                                        <img :src="affirmRecordList.staff_info[item.result.created_by].photoUrl" data-holder-rendered="true" class="avatar32">
                                        <span class='ml10'>{{affirmRecordList.staff_info[item.result.created_by].name}}</span>
                                    </div>
                                </div>
                            </div>
                            <div class='flex mt12'>
                                <span class='color6 font14'><?php echo Yii::t("ptc", "Time"); ?>：</span>
                                <div class='flex1 color3'>{{item.result.created_at}}</div>
                            </div>
                        </div>
                        <div v-else>
                            <div class='flex font14'>
                                <div class='flex1 color3 '>No.{{item.id}}</div>
                                <span class='btn-link' @click='showDetails(item.behavior_id,"show")'><?php echo Yii::t("global", "View Detail"); ?></span>
                            </div>
                            <div class='bgGrey mt16 p12'>
                                <div class='color6 font12'><?php echo Yii::t("referral", "Event Description"); ?>：</div>
                                <div class='color3 font12 mt5'>{{item.desc}}</div>
                            </div>
                            <div class='flex mt16'>
                                <span class='color6 font14'><?php echo Yii::t("referral", "Decision"); ?>：</span>
                                <div class='flex1 color3'><span class="label fontWeightNormal" :class='item.result.identified==1?"label-danger":"label-success"'>{{showTitle(item.result.identified,'fact')}}</span></div>
                            </div>
                            <div class='flex mt12'>
                                <span class='color6 font14'><?php echo Yii::t("referral", "Time in Office"); ?>：</span>
                                <div class='flex1 color3'>{{item.result.duration}}</div>
                            </div>
                            <div class='flex mt12'>
                                <span class='color6 font14'><?php echo Yii::t("referral", "Final Result"); ?>：</span>
                                <div class='flex1 color3'>
                                    <span class='mr10' v-for='(list,index) in item.result.actions'>{{showTitle(list,'actions')}}</span>
                                    <span v-if='item.followup.date'>{{item.followup.date[0]}} ~ {{item.followup.date[1]}}</span>
                                </div>
                            </div>
                            <div class='flex mt12'>
                                <span class='color6 font14'><?php echo Yii::t("referral", "Contact parents"); ?>：</span>
                                <div class='flex1 color3'><span class='mr10' v-for='(list,index) in item.result.contact_parents'>{{showTitle(list,'contact_parents')}}</span></div>
                            </div>
                            <div class='flex mt12'>
                                <span class='color6 font14'><?php echo Yii::t("referral", "Comment"); ?>：</span>
                                <div class='flex1 color3' v-html='htmlBr(item.result.remark)'></div>
                            </div>
                            <div class='flex mt12' v-if='item.result.attachments && (item.result.attachments.img || item.result.attachments.other)'>
                                <span class='color6 font14'><?php echo Yii::t("curriculum", "Attachments"); ?>：</span>
                                <div class='flex1 color3'>
                                    <div class=''>
                                        <ul class='mb12 imgLi'  id='record'  v-if='item.result.attachments.img'>
                                            <li v-for='(list,i) in item.result.attachments.img'>
                                                <img :src="affirmRecordList.attachmentList[list].file_key" class='imgList mb8 mr8' @click='showImg("record",item.result.attachments.img)' alt=""  >
                                            </li>
                                        </ul>
                                    </div>
                                    <div class='mt12 color3' v-if='item.result.attachments.other'>
                                        <div v-for='(list,j) in item.result.attachments.other'>
                                            <div class='flex fileLink' >
                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='affirmRecordList.attachmentList[list].mime_type=="application/pdf"' />
                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='affirmRecordList.attachmentList[list].mime_type=="application/vnd.ms-excel" || affirmRecordList.attachmentList[list].mime_type=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='affirmRecordList.attachmentList[list].mime_type=="application/msword" || affirmRecordList.attachmentList[list].mime_type=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='affirmRecordList.attachmentList[list].mime_type=="application/x-zip-compressed"' />
                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else />
                                                <a class='flex1 ml5' target= "_blank" :href="list.url">{{affirmRecordList.attachmentList[list].title}}
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class='flex mt12 align-items '>
                                <span class='color6 font14'><?php echo Yii::t("referral", "By"); ?>：</span>
                                <div class='flex1 color3'>
                                    <div class='flex align-items '>
                                        <img :src="affirmRecordList.staff_info[item.result.created_by].photoUrl" data-holder-rendered="true" class="avatar32">
                                        <span class='ml10'>{{affirmRecordList.staff_info[item.result.created_by].name}}</span>
                                    </div>
                                </div>
                            </div>
                            <div class='flex mt12'>
                                <span class='color6 font14'><?php echo Yii::t("ptc", "Time"); ?>：</span>
                                <div class='flex1 color3'>{{item.result.created_at}}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 详情 -->
    <div class="modal fade" id="contentModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static" style="overflow-y: auto;">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <div class='flex'>
                        <div class='flex1'>
                            <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "Detail"); ?>
                                <!-- <span class='text-primary cur-p font14 fontWeightNormal ml10' @click='allSendMessage()'>
                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/share.png' ?>" alt="" width='16' height='16'>
                                <span class='ml4 '><?php echo Yii::t("referral", "Share To"); ?></span>
                            </span> -->
                            </h4>
                        </div>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close" ><span aria-hidden="true">&times;</span></button>
                    </div>
                </div>
                <div class="modal-body p0 relative" v-if='contentList.behavior'>
                    <div><?php $this->renderPartial("detail", array('type' => 'processing'));?></div>
                    <div class='clearfix'></div>
                </div>
            </div>
        </div>
    </div>
     <!-- 重新认定 和 认定其他学生-->
     <div class="modal fade"  id="afreshResultModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel"  aria-labelledby="wantSayModalLabel"  data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">{{editStatus==2?"<?php echo Yii::t("referral", "Add other student"); ?>":"<?php echo Yii::t("referral", "Reset"); ?>"}}</h4>
            </div>
            <div class="modal-body p24 font14">
                <div v-if='editStatus!=0'>
                    <div v-if='editStatus==2'>
                        <div class='flex align-items'>
                            <span class='fontBold color3 flex1'><?php echo Yii::t("ptc", "Student"); ?></span> 
                            <template>
                                <el-select
                                        v-model="stuVal"
                                        filterable
                                        remote
                                        clearable
                                        class='inline-input flex1 formControl'
                                        reserve-keyword
                                        placeholder="<?php echo Yii::t("ptc", "Add student"); ?>"
                                        :remote-method="stuRemoteMethod"
                                        prefix-icon="el-icon-search"
                                        @change='addStu'
                                        :loading="loading">
                                    <el-option
                                            v-for="item in stuOption"
                                            :key="item.id"
                                            :label="item.name"
                                            class='optionSearch mb8'
                                            :value="item.id">
                                        <div class="media">
                                            <div class="media-left pull-left media-middle">
                                                <a href="javascript:void(0)">
                                                    <img :src="item.avatar" data-holder-rendered="true" class="media-object img-circle avatar">
                                                </a>
                                            </div>
                                            <div class="media-body mt5 media-middle">
                                                <h4 class="media-heading font14 color3 text_overflow">{{item.name}}</h4>
                                                <div class="text-muted text_overflow font12">{{ item.class_name }}</div>
                                            </div>
                                        </div>
                                    </el-option>
                                </el-select>
                            </template>
                        </div>
                        <div class='flex align-items waitingColor'><span class='glyphicon glyphicon-info-sign'></span> <span class='ml5'><?php echo Yii::t("referral", "Different result should be processed one by one."); ?></span> </div>
                        <div  class='mt8'>
                            <div class='row' v-if='contentList.behavior'>
                                <div class='col-md-6 mt10' v-for='(list,index) in contentList.behavior.child'>
                                    <div class='bgGrey p8 height65'>
                                        <label class="checkbox-inline flex">
                                            <input type="checkbox"   class='mt16' v-if='showDisabled(list.id)' disabled='true' > 
                                            <input type="checkbox"  :value="list.id" class='mt16' v-if='!showDisabled(list.id)' v-model='officeData.student_ids'> 
                                            <div class="media flex1 m0 authorizedMedia" >
                                                <div class="media-left pull-left media-middle">
                                                    <a href="javascript:void(0)">
                                                        <img :src="contentList.student_info_list[list.id].avatar" data-holder-rendered="true" class="avatar">
                                                    </a>
                                                </div>
                                                <div class="media-right pull-right mt15 text-right">
                                                    <span class='color6' v-if='showDisabled(list.id)'><?php echo Yii::t("referral", "Processed"); ?></span>
                                                </div>
                                                <div class="media-body media-middle">
                                                    <div class="lineHeight20  text-primary">
                                                        <span class='font14 color3 nowrap'>{{contentList.student_info_list[list.id].name}} </span>
                                                        <span v-if='list.tag==2 || list.tag==3' class='glyphicon glyphicon-bookmark font12' :class='list.tag==2?"waitingColor":list.tag==3?"colorRed":""'></span>
                                                    </div>
                                                    <div class="font12 color6 nowrap">{{contentList.student_info_list[list.id].className}}</div>
                                                </div>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                                <div class='col-md-6 mt10' v-for='(list,index) in addChildList' :key='list.id'>
                                    <div class='bgGrey p8 height65'>
                                        <label class="checkbox-inline flex">
                                            <input type="checkbox"  :value="list.id" class='mt16' v-model='officeData.student_ids'> 
                                            <div class="media flex1 m0 authorizedMedia" >
                                                <div class="media-left pull-left media-middle">
                                                    <a href="javascript:void(0)">
                                                        <img :src="list.avatar" data-holder-rendered="true" class="avatar">
                                                    </a>
                                                </div>
                                                <div class="media-right pull-right mt15 text-right">
                                                    <span class='el-icon-circle-close font16' @click='delChild(index,list.id)'></span>
                                                </div>
                                                <div class="media-body media-middle">
                                                    <div class="lineHeight20  text-primary">
                                                        <span class='font14 color3 nowrap'>{{list.name}} </span>
                                                        <span v-for='(item,ind) in list.label' >
                                                            <span v-if='item.flag==2 || item.flag==3' class='glyphicon glyphicon-bookmark font12' :class='item.flag==2?"waitingColor":item.flag==3?"colorRed":""'></span>
                                                        </span>
                                                    </div>
                                                    <div class="font12 color6 nowrap">{{list.className}}</div>
                                                </div>
                                                
                                            </div>
                                        </label>
                                    </div>
                                </div>
                                
                            </div>
                        </div>
                    </div>
                    <div  v-if='editStatus==1'>
                        <div class='row' v-if='reaffData.child'>
                            <div class='col-md-6 mt10'>
                                <div class="media flex1 m0" >
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                            <img :src="contentList.student_info_list[reaffData.child.id].avatar" data-holder-rendered="true" class="avatar">
                                        </a>
                                    </div>
                                    <div class="media-body pt4 media-middle">
                                        <div class="lineHeight20  text-primary">
                                            <span class='font14 color3 nowrap'>{{contentList.student_info_list[reaffData.child.id].name}} </span>
                                        </div>
                                        <div class="font12 color6 nowrap">{{contentList.student_info_list[reaffData.child.id].className}}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class='fontBold color3 mt24'><?php echo Yii::t("referral", "Decision"); ?></div>
                    <div  class='mt8'>
                        <div  >
                        <label class="radio-inline" v-for='(list,index) in config.fact'>
                            <input type="radio" name="inlineRadioOptions" v-model='officeData.identified' :value="list.value"> {{list.title}}
                        </label>
                        </div>
                    </div>
                    <div>                    
                        <div class='fontBold color3 mt24'><?php echo Yii::t("referral", "Final Result"); ?></div>
                        <div class='row'>
                            <div class='col-md-3 col-sm-12'  v-for='(list,index) in actionsList'>
                                <div class="checkbox m0 mt5" >
                                    <label>
                                        <input type="checkbox" v-model='officeData.actions'  :value="list.value" >{{list.title}}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class='fontBold color3 mt24'><?php echo Yii::t("referral", "Contact parents"); ?></div>
                    <div class='row'>
                        <div class='col-md-3 col-sm-12'  v-for='(list,index) in config.contact_parents'>
                            <div class="checkbox m0 mt5" >
                                <label>
                                    <input type="checkbox" v-model='officeData.contact_parents'  :value="list.value" >{{list.title}}
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class='otherBg mt10' v-if='showIndexOf("actions")'>
                        <input type="text" class="form-control" placeholder="<?php echo Yii::t("report", "Other"); ?>"  v-model='officeData.actions_other' >
                    </div>
                    <div class='fontBold color3 mt24'><?php echo Yii::t("referral", "Time in Office"); ?></div>
                    <div class='mt8'>
                        <el-time-picker
                            v-model="officeData.durationTime"
                            value-format="H:m"
                            @input="$forceUpdate()"
                            :format="pickerFormatText"
                            placement="bottom-start"
                            placeholder="<?php echo Yii::t("referral", "Time in Office"); ?>">
                        </el-time-picker>
                    </div>
                    <div class='fontBold color3 mt24'><?php echo Yii::t("referral", "Comment"); ?></div>
                    <div  class='mt8'><textarea class="form-control" placeholder="<?php echo Yii::t("teaching", "Input"); ?>" v-model='officeData.remark' rows="3"></textarea></div>
                    <div class='flex mb10 align-items mt24'>
                        <div class='fontBold color3 font14'><?php echo Yii::t("curriculum", "Attachments"); ?></div>
                        <div class='flex1 ml10'>
                            <el-button type="text" icon="el-icon-circle-plus-outline" id='afreshAddFile' class='font14 bluebg'><?php echo Yii::t("referral", "Upload"); ?></el-button>

                        </div>
                    </div>
                    <div>
                        <div class='' v-if='attachments.img && attachments.img.length>0'>
                            <div class='imgData mr8'  v-for='(list,i) in attachments.img'>
                                <div v-if="list.types=='1'">
                                    <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Uploading");?></div>
                                </div>
                                <div v-else>
                                    <img class='imgList' @click='uploadShowImg(list.file_key)'  :src="list.file_key" alt="">
                                    <span aria-hidden="true" class='closeImg' @click.stop='delImg("img",list,i)'>×</span>
                                </div>
                            </div>
                            <template v-if='loadingType==1'>
                                <div class='imgData mr8'  v-for='(list,i) in loadingList'>
                                    <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Upload queue");?></div>
                                </div>
                            </template>
                        </div>
                        <div>
                            <div class='mt16' v-if='attachments.other && attachments.other.length>0'>
                                <div class='flex uploadFile' v-for='(list,index) in attachments.other'>
                                    <span class='glyphicon glyphicon-paperclip mr8'></span>
                                    <a target="_blank" class='flex1'  style='line-height:26px' :href='list.file_key' v-if='!list.isEdit'>{{list.title}}</a>
                                    <span class='flex1' v-else><input type="input" class='inputStyle'  @keyup.enter="saveFile(list,index)"  v-model='attachmentName' ></span>
                                    <span v-if="list.types=='1'"></span>
                                    <template v-else>
                                        <template v-if='!list.isEdit'>
                                            <span class='glyphicon glyphicon-edit icon mr16' v-if='list.file_key!=""' @click.stop='list.isEdit=true,attachmentName=list.title'></span>
                                            <span class='glyphicon glyphicon-trash icon' v-if='list.file_key!=""' @click.stop='delImg("link",list,index)'></span>
                                        </template>
                                        <span style='width:90px'  class='text-right inline-block' v-else>
                                            <button type="button" class="btn btn-primary btn-xs" @click='saveFile(list,index)'><?php echo Yii::t("global", "Save");?></button>
                                            <button type="button" class="btn btn-default btn-xs" @click='list.isEdit=false'><?php echo Yii::t("global", "Cancel");?></button>
                                        </span>
                                    </template>
                                </div>
                            </div>
                            <template v-if='loadingType==2'>
                                <div class='flex uploadFile'  v-for='(list,i) in loadingList'>
                                    <span class='glyphicon glyphicon-paperclip mr8'></span>
                                    <a  class='flex1'  style='line-height:26px' > <?php echo Yii::t("directMessage", "Upload queue");?></a>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                <button type="button" class="btn btn-primary" @click='afreshOffice()' v-if='editStatus==2'><?php echo Yii::t("global", "OK"); ?></button>
                <button type="button" class="btn btn-primary" @click='afreshReaff()' v-if='editStatus==1'><?php echo Yii::t("global", "OK"); ?></button>
            </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
    <!-- 筛选学生 -->
    <div class="modal fade" id="moreChildListModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("referral", "Select Student"); ?></h4>
                </div>
                <div class="modal-body p24  font14" v-if='moreChildList.list'>
                    <div class='mb24 text-center'>
                        <el-input
                            class='length_5'
                            clearable
                            placeholder="<?php echo Yii::t("global", "Search"); ?>"
                            prefix-icon="el-icon-search"
                            v-model="searchChildVal">
                        </el-input>
                    </div>
                    <div class='row overflow-y scroll-box' v-if='childSearchData.length!=0'  :style="'max-height:'+(height)+'px;overflow-x: hidden;'">
                        <div class='col-md-6' v-for='(list,index) in childSearchData'>
                            <div class='moreChild flex' @click='filterList(list.child_id,moreChildList.child_list[list.child_id].name,"child")'>
                                <div class="media flex1" >
                                    <div class="media-left pull-left media-middle relative">
                                        <img :src="moreChildList.child_list[list.child_id].avatar" data-holder-rendered="true" class="avatar">
                                    </div>
                                    <div class="pull-left media-middle">
                                        <div class="font14 color3 mt4"> {{moreChildList.child_list[list.child_id].name}}
                                            <span v-for='(item,ind) in moreChildList.child_list[list.child_id].label' >
                                                <span v-if='item.flag==2 || item.flag==3' class='glyphicon glyphicon-bookmark font12' :class='item.flag==2?"waitingColor":item.flag==3?"colorRed":""'></span>
                                                <!-- <span v-if='item.flag==2 || item.flag==3' :class='item.flag==2?"tagLabelYellow":item.flag==3?"tagLabel":""'>{{item.name}}</span> -->
                                            </span>
                                        </div>
                                        <div class="color6 font12">{{moreChildList.child_list[list.child_id].className}}</div>
                                    </div>
                                </div>
                                <div>
                                    <div class='flex1 text-center'>
                                        <div class='font14 color3 fontBold'>{{list.num}}</div>
                                        <div class='mt4 font12 color6'><?php echo Yii::t("referral", "Record Only"); ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-else class='p24 text-center font14 color3'><?php echo Yii::t("ptc", "No Data"); ?></div>
                </div>
            </div>
        </div>
    </div>
    <!-- 筛选班级 -->
    <div class="modal fade" id="moreClassListModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("referral", "Select Class"); ?></h4>
                </div>
                <div class="modal-body overflow-y scroll-box font14" :style="'max-height:'+(height)+'px;overflow-x: hidden;'"  v-if='moreClassList.list'>
                    <div class='row' v-if='moreClassList.list.length!=0'>
                        <div class='col-md-6' v-for='(list,index) in moreClassList.list'>
                            <div class='classMore flex' @click='filterList(list.class_id,moreClassList.class_list[list.class_id],"class")'>
                                <div class="flex1 font14 color6" >
                                        {{moreClassList.class_list[list.class_id]}}
                                </div>
                                <div>
                                    <span class="badge">{{list.num}}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-else class='p24 text-center font14 color3'><?php echo Yii::t("ptc", "No Data"); ?></div>
                </div>
            </div>
        </div>
    </div>
    <div><?php $this->renderPartial("sendModal");?></div>
    <div><?php $this->renderPartial("transferModal");?></div>
</div>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>

$(function(){
        $('#main-edit-zone').find('.edit-title').html($('#user-profile-item li.active a').html());
    })
    $(document).ready(function () {
    // 通过该方法来为每次弹出的模态框设置最新的zIndex值，从而使最新的modal显示在最前面
        $(document).on('shown.bs.modal', '.modal.in', function(event) {
            var zIndex = 1040 + (10 * $('.modal:visible').length);
                $(this).css('z-index', zIndex);
                setTimeout(function() {
                    $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
                }, 0);
        }).on('hidden.bs.modal', '.modal', function(event) {
            if ($('.modal.in').size() >= 1) {
                $('body').addClass('modal-open')
            }
        })
    });

    var height=document.documentElement.clientHeight;
    var config = <?php echo CJSON::encode($config)?>;
    var id_num = <?php echo CJSON::encode($id_num)?>;
    var need_me = "<?php echo $_GET['needMe']?>"
    var container = new Vue({
        el: "#container",
        data: {
            search:'',
            height:height-220,
            config:config,
            teacherUid:'',
            studyTeacherUid:'',
            options:[],
            studyOptions:[],
            loading:false,
            dataList:{},
            contentList:{},
            time:'',
            principal_processing:'',
            remark:'',
            duration:'00:00',
            help:false,
            officeData:{},
            gradeData:{},
            resultData:{},
            ispData:{},
            planData:{},
            viewType:'',
            sendType:'',
            appendComment:'',
            appendText:false,
            refill:false,
            typeData:{},
            detail:{},
            order: "descending",
            orderProp:"created_at",
            pageNum:1,
            itemList:{},
            qiniuToken:'',
            loadingType:0,
            loadingList:[],
            handId:'',
            uploader:[],
            stuVal:'',
            stuOption:[],
            showBehavior:true,
            showStudy:true,
            attachments:{
                img:[],
                other:[]
            },
            addChildList:[],
            resultType:"",
            editStatus:0,
            reaffData:{},
            followItem:{},
            followData:{
                remark:'',
                date:'',
                time:''
            },
            followType:'',
            sendItem:{},
            classTeacher:{},
            teacher_ids:[],
            sendRemark:'',
            checkall:false,
            sendLogList:[],
            affirmRecordList:{},
            affirmData:{},
            forwardType:'',
            selectOptionWidth: null,
            adminForwardType:'',
            classId:'',
            currentYear:'',
            currentGrade_group:'2',
            initSchoolYear:{},
            initList:{},
            currentStat:{},
            currentTime: 4,// #1-今日 2-本周 3-本月 4-本学年
            currentProcessing:null, ////1-需要我处理 2-处理中
            moreChildList:{},
            searchChildVal:'',
            moreClassList:{},
            classWarning:'1',
            moreClassid:'',
            indexLoading:false,
            actionsList:[],
            initLoading:false,
            ispForwardType:'',
            GradeForwardType:'',
            behaviorValue:'',
            learningValue:'',
            behaviorTeacher:[],
            learningTeacher:[],
            officeList:{},
            sendTecherOptions:[],
            sendTeacherUid:[],
            needMe:need_me,
            pickerFormatText:"H <?php echo Yii::t("referral", "'Hour'"); ?> m <?php echo Yii::t("referral", "'Minutes'"); ?>",
            subForwardType:'',
            subjectList:[],
            subjectData:{},
            searchChild:[],
            searchClass:[],
            searchName:'',
            nextNode:{},
            CopyPages:{},
            filterBehaviorValue:'',
            lineClamp: 3,
            showHtml:true,
            shouldShowExpandButton: {},
        },
        watch:{
            "officeData.student_ids":{
                deep:true,
                handler(newVal){
                    if(newVal){
                        if(newVal.length>0){
                            let childAll=this.addChildList.concat(this.contentList.behavior.child)
                            let newArr2  = childAll.filter((i) => newVal.indexOf(i.id) !== -1)
                            let gradeList=[]
                            newArr2.forEach(item => {
                                gradeList.push(item.grade)
                            });
                            this.showActions(Array.from(new Set(gradeList)))
                        }else{
                            this.showActions([])
                        }
                        this.officeData.actions=[]
                    }

                }
            },
            "resultData.student_ids":{
                deep:true,
                handler(newVal){
                    if(newVal){
                        if(newVal.length>0){
                            let childAll=this.addChildList.concat(this.contentList.behavior.child)
                            let newArr2  = childAll.filter((i) => newVal.indexOf(i.id) !== -1)
                            let gradeList=[]
                            newArr2.forEach(item => {
                                gradeList.push(item.grade)
                            });
                            this.showActions(Array.from(new Set(gradeList)))
                        }else{
                            this.showActions([])
                        }
                        this.resultData.actions=[]
                    }

                }
            },
            'dataList': {
                handler(newVal) {
                if (newVal && newVal.list) {
                    this.$nextTick(() => {
                        newVal.list.forEach(key => {
                            this.getHeight(key._id);
                        });
                    });
                }
                },
                deep: true
            }
        },
        created: function() {
            let start_year = sessionStorage.getItem('start_year') || '';
            this.initData(start_year)
        },
        computed: {
            childSearchData: function() {
                var search = this.searchChildVal;
                let searchVal = ''; //搜索后的数据
                if(search) {
                    searchVal =Object.values(this.moreChildList.list).filter(function(product) {
                        return Object.keys(product).some(function(key) {
                            return String(product['child_name'].toLowerCase()).indexOf(search.toLowerCase()) !== -1;
                        })
                    })
                    return searchVal;
                }
                return this.moreChildList.list;
            },
            textStyleObject () {
                return {
                    'max-height': '6.7em'
                }
            }
        },
        methods: {
            getHeight(id){
                return new Promise((resolve) => {
                    this.$nextTick(() => {
                        const element = $(`.${id}`);
                        const span = $(`.span_${id}`);
                        const flag = element.height() >= 80 && span.height()>80;
                        resolve(flag);
                    });
                    }).then(flag => {
                    this.showHtml=false
                    this.$nextTick(() => { 
                        this.showHtml=true
                        this.shouldShowExpandButton[id] = flag;
                    });
                });
            },
            showActions(list){
                this.actionsList=[]
                let actionsData= JSON.parse(JSON.stringify(this.config.actions));
                if(list.length==0){
                    this.actionsList=actionsData
                    return
                }
                actionsData.forEach(item => {
                    list.forEach(list => {
                        if(item.apply.indexOf(list)!=-1) this.actionsList.push(item)
                    });
                });
                this.actionsList=Array.from(new Set(this.actionsList))
            },
            htmlBr(content){
                if(content!=null){
                    var reg=new RegExp("\n","g");
                    content= content.replace(reg,"<br/>");
                }
                return content
            },
            setOptionWidth(event){
                this.$nextTick(() => {
                    this.selectOptionWidth = event.srcElement.offsetWidth;
                    this.studyOptions=[]
                    this.options=[]
                });
            },
            showTitle(data,type){
                for(var i=0;i<this.config[type].length;i++){
                    if(this.config[type][i].value==data){
                        return this.config[type][i].title
                    }
                }
            },
            sort_change(column){
                this.order=column.order
                this.orderProp=column.prop
                if(this.currentProcessing==null){
                    this.getData()
                }else{
                    this.getProcessing(this.currentProcessing)
                }
            },
            initPage(){
                var _this = this;
                _this.CopyPages=JSON.parse(JSON.stringify(_this.pages))
                if(_this.CopyPages.count_page>=10){
                    _this.pages.count=[1,2,3,4,5,6,7,8,9,10]
               }else{
                    var numPage=[]
                    for(var i=1;i<=_this.CopyPages.count_page;i++){
                        numPage.push(i)
                    }
                    _this.pages.count=numPage
               }
            },
            pagesSize(){
                var _this = this;
                if(_this.pageNum<10){
                    if(_this.CopyPages.count_page>=10){
                        _this.pages.count=[1,2,3,4,5,6,7,8,9,10]
                    }else{
                        var numPage=[]
                        for(var i=1;i<=_this.CopyPages.count_page;i++){
                            numPage.push(i)
                        }
                        _this.pages.count=numPage
                    }
                }else if(_this.pageNum<=_this.CopyPages.count_page){
                    if(_this.CopyPages.count_page-_this.pageNum>=4){
                        var minPage=_this.pageNum-5
                        var maxPage=_this.pageNum+4
                    }else{
                        var minPage=_this.pageNum-(9-((_this.CopyPages.count_page-_this.pageNum)))
                        var maxPage=_this.pageNum+(_this.CopyPages.count_page-_this.pageNum)
                    }
                    var numPage=[]
                    for(var i=minPage;i<=maxPage;i++){
                        numPage.push(i)
                    }
                    _this.pages.count=numPage
                }
            },
            prev(index) {
                this.pageNum = Number(index) - 1
                this.pagesSize()
                if(this.currentProcessing==null){
                    this.getData()
                }else{
                    this.getProcessing(this.currentProcessing)
                }
            },
            plus(index) {
                this.pageNum = Number(index)
                this.pagesSize()
                if(this.currentProcessing==null){
                    this.getData()
                }else{
                    this.getProcessing(this.currentProcessing)
                }
            },
            next(index){
                this.pageNum = Number(index) + 1
                this.pagesSize()
                if(this.currentProcessing==null){
                    this.getData()
                }else{
                    this.getProcessing(this.currentProcessing)
                }
            },
            initData(year,result){
                let that=this
                this.initLoading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("allBehaviorListIndex") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        start_year:year,
                        grade_group:that.currentGrade_group,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.initList=data.data
                            sessionStorage.setItem('start_year',year);
                            that.currentYear=data.data.opt
                            that.initSchoolYear=data.data.schoolYear
                            that.currentStat=data.data.stat
                            if(!result){
                                if(id_num!='' || need_me!=''){
                                    that.currentProcessing=1
                                    that.getProcessing(1,"init")
                                }else{
                                    that.getData('init')
                                }
                            }
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.initLoading=false
                    },
                    error: function(data) {
                        that.initLoading=false
                    },
                })
            },
            filterBehavior(type){
                if(type){
                    this.filterBehaviorValue=''
                }
                this.getData('init')
            },
            getData(init){
                let that=this
                this.currentProcessing=null
                this.indexLoading=true
                if(init){
                    this.pageNum=1
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("allNewBehaviorList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        page:this.pageNum,
                        order: this.order,
                        orderProp:this.orderProp,
                        time: this.currentTime,
                        grade_group:2,//暂时只展示中学的
                        start_year: that.currentYear,
                        recordOnly:1,
                        child_id:this.searchChild,
                        class_id:this.searchClass,
                        problem_behavior:this.filterBehaviorValue
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            data.data.list.forEach(item => {
                                item.more=true
                            });
                            that.dataList=data.data
                            // that.itemList=data.data.meta
                            that.currentStat.new=data.data.meta.total
                            that.indexLoading=false
                            if(init){
                                that.pages=data.data.meta
                                that.CopyPages=data.data.meta
                                that.initPage()
                            }
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.indexLoading=false
                        }
                    },
                    error: function(data) {
                        that.indexLoading=false
                    },
                })
            },
            getProcessing(type,init){
                let that=this
                this.currentProcessing=type
                // this.currentTime=null
                if(init){
                    this.pageNum=1
                }
                this.indexLoading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("allBehaviorList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        page:this.pageNum,
                        order: this.order,
                        orderProp:this.orderProp,
                        type: type,
                        grade_group:that.currentGrade_group,
                        start_year: that.currentYear
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            data.data.list.forEach(item => {
                                item.more=true
                            });
                            that.dataList=data.data
                            // that.itemList=data.data.meta
                            if(init){
                                that.CopyPages=data.data.meta
                                that.pages=data.data.meta
                                that.initPage()
                            }
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.indexLoading=false
                    },
                    error: function(data) {
                        that.indexLoading=false
                    },
                })
            },
            showDetails(id,type){
                let that=this
                this.sendLogList=[]
                if(type=='show'){
                    // this.affirmData={}
                    // this.affirmRecordList={}
                }
                // if(type=='init'){
                //     that.getQiniu(id)
                // }
                if(that.uploader.length!=0) {
                    for(var i=0;i<that.uploader.length;i++){
                        that.uploader[i].destroy();
                    }
                }
                this.handId=id
                this.appendText=false
                this.refill=false
                this.help=''
                this.reaffData={}
                this.officeData={
                    to_admin: "", //是否需要校长处理 1是 0否
                    remark: "",
                    duration: "00:00",
                    durationTime:"0:0",
                    attachments:{
                        img:[],
                        other:[]
                    },
                    notify_ids:''
                }
                this.resultData={
                    identified:'',
                    actions_other:'',
                    duration: "00:00",
                    durationTime:"0:0",
                    remark:'',
                    actions:[],
                    attachments:{
                        img:[],
                        other:[]
                    },
                    student_ids:[],
                    contact_parents:[]
                }
                this.ispData={
                    help_type:'',
                    support:'',
                    remark:'',
                    attachments:{
                        img:[],
                        other:[]
                    },
                    notify_ids:''
                }
                this.gradeData={
                    remark:'',
                    attachments:{
                        img:[],
                        other:[]
                    },
                    notify_ids:''
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("getBehaviorDetail") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.contentList=data.data
                            that.detail=data.data.behavior.detail
                            $('#contentModal').modal('show')
                            if(type=='show'){
                                // $('#identifiedModal').modal('hide')
                            }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.loading = false;
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.loading = false;
                    },
                })
            },
            getSupport(group){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getSupport") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        group:group
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            if(data.data.behavior.teacher && data.data.behavior.teacher.length!=0){
                                that.behaviorTeacher=[{
                                    label:'<?php echo Yii::t("newDS", "Please select"); ?>',
                                    value:''
                                }]
                                data.data.behavior.teacher.forEach(item => {
                                    that.behaviorTeacher.push({
                                        label:data.data.teacher_list[item].name,
                                        value:item
                                    })
                                });
                            }
                            if(data.data.study.teacher && data.data.study.teacher.length!=0){
                                that.learningTeacher=[{
                                    label:'<?php echo Yii::t("newDS", "Please select"); ?>',
                                    value:''
                                }]
                                data.data.study.teacher.forEach(item => {
                                    that.learningTeacher.push({
                                        label:data.data.teacher_list[item].name,
                                        value:item
                                    })
                                });
                            }
                            that.planData=data.data
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            confirmTeacher(type){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("setSupportTeacher") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        teacher_id:type=='3'?this.teacherUid:this.studyTeacherUid,
                        type:type,//2-学习计划支持 3-行为计划支持
                        status: "1",//1-设置 2取消
                        group:that.contentList.behavior.child[0].grade
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.options=[]
                            that.studyOptions=[]
                            that.teacherUid=''
                            that.studyTeacherUid=''
                            resultTip({
                                msg: data.message
                            });
                            that.getSupport(that.contentList.behavior.child[0].grade)
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            delTeacher(show,list,index,type){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("setSupportTeacher") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        teacher_id:list,
                        type:type,//2-学习计划支持 3-行为计划支持
                        status: "2",//1-设置 2取消
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.planData[show].teacher.splice(index,1)
                            resultTip({
                                msg: data.message
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            studyRemoteMethod(query){
                let that=this
                if (query !== '') {
                    this.loading = true;
                    $.ajax({
                        url: '<?php echo $this->createUrl("teacherSearch") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            teacher_name:query,
                            group:that.contentList.behavior.child[0].grade
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.studyOptions = Object.values(data.data) ;
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                            that.loading = false;
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.loading = false;
                        },
                    })
                } else {
                    this.options = [];
                }
            },
            remoteMethod(query) {
                let that=this
                if (query !== '') {
                    this.loading = true;
                    $.ajax({
                        url: '<?php echo $this->createUrl("teacherSearch") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            teacher_name:query,
                            group:that.contentList.behavior.child[0].grade
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.options = Object.values(data.data) ;
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                            that.loading = false;
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.loading = false;
                        },
                    })
                } else {
                    this.options = [];
                }
            },
            showIndexOf(type){
                if(this.officeData[type] && this.officeData[type].indexOf("OTHER")!=-1){
                    return true
                }else{
                    return false
                }
            },
            saveComment(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("officeProcessAddComment") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        id:this.contentList.behavior._id,
                        comment:this.appendComment
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.appendText=false
                            resultTip({
                                msg: data.message
                            });
                            that.appendComment=''
                            that.showDetails(that.contentList.behavior._id)
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            showImg(id,list){
                var viewer = new Viewer(document.getElementById(id),{
                    fullscreen: false,
                    title:false,
                    scalable:false,
                    show:function(){
                        if(list.length==1){
                            $('.viewer-prev').hide()
                            $('.viewer-next').hide()
                        }
                    },
                    hide:function(){
                        viewer.destroy()
                    }
                });
                $("#"+id).click();
            },
            stuRemoteMethod(query) {
                let that=this
                if (query !== '') {
                    this.loading = true;
                    $.ajax({
                        url: '<?php echo $this->createUrl("studentSearch") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            child_name:query
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.stuOption = Object.values(data.data) ;
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                            that.loading = false;
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.loading = false;
                        },
                    })
                } else {
                    this.options = [];
                }
            },
            addStu(id){
                for(var i=0;i<this.addChildList.length;i++){
                    if(this.addChildList[i].id==this.stuVal){
                        resultTip({
                            error: 'warning',
                            msg: '不能重复添加'
                        });
                        return
                    }
                }
                this.stuOption.forEach(item => {
                    if(item.id==this.stuVal){
                        this.addChildList.push(item)
                    }
                })
                this.officeData.student_ids.push(this.stuVal)
                this.stuOption=[]
                this.stuVal=''
            },
            delChild(index,id){
                this.addChildList.splice(index, 1)
                this.officeData.student_ids=this.officeData.student_ids.filter((item)=>{return item!=id});
            },
            delImg(type,list,index){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("deleteAttachment") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        attachmentId:list._id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            if(type=='img'){
                                that.attachments.img.forEach((item,index) => {
                                    if (item._id==list._id) {
                                        that.attachments.img.splice(index, 1)
                                    }
                                })
                            }else{
                                that.attachments.other.forEach((item,index) => {
                                    if (item._id==list._id) {
                                        that.attachments.other.splice(index, 1)
                                    }
                                })
                            }
                            resultTip({
                                msg:data.state
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            saveFile(list,index) {
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("updateAttachmentName") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        attachmentId:list._id,
                        attachmentName:this.attachmentName

                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.attachments.other.forEach((item,index) => {
                                if (item._id==list._id) {
                                    Vue.set(that.attachments.other[index], 'title',data.data.title);
                                    Vue.set(that.attachments.other[index], 'file_key', data.data.url);
                                    Vue.set(that.attachments.other[index], 'isEdit', false);
                                }
                            })
                            resultTip({
                                msg:'<?php echo Yii::t("newDS", "Data Saved!");?>'
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            markEnd(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("setOver") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:this.contentList.behavior._id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getData(that.showType)
                            that.showDetails(that.contentList.behavior._id)
                            that.initData(that.currentYear,'result')
                            // $('#contentModal').modal('hide')
                            resultTip({
                                msg:data.state
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            sendMessage(item){
                this.sendItem=item
                let that=this
                this.teacher_ids=[]
                this.sendRemark=''
                this.checkall=false
                $.ajax({
                    url: '<?php echo $this->createUrl("getClassTeacherList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        class_id:[item.child.class_id]
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.classTeacher=data.data[item.child.class_id]
                            $('#sendTeacherModal').modal('show')
                            that.$nextTick(()=>{
                                $('.qrcodeShow').empty();
                                $('.qrcodeShow').qrcode({
                                    width: 120,
                                    height: 120,
                                    text: 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxe929eea87aaf012f&redirect_uri=https%3A%2F%2Fstaff.ivyonline.cn%2FrequestForm&response_type=code&scope=snsapi_userinfo&state=ivy#wechat_redirect',
                                    background: "#fff",//二维码的后景色
                                    // foreground: "#2f2c2c"//二维码的前景色
                                });
                            })

                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            allTeacher(){
                this.teacher_ids=[]
                if(this.checkall){
                    for(var keys in this.classTeacher){
                        this.teacher_ids.push(parseInt(keys))
                    }
                }
            },
            selectSendTeacher(){
                if(this.teacher_ids.length==Object.values(this.classTeacher).length){
                    this.checkall=true
                }else{
                    this.checkall=false
                }
            },
            saveSend(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("sendBehaviorMsgToTeacher") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        class_id:[this.sendItem.child.class_id],
                        id: this.sendItem._id,
                        remark: this.sendRemark,
                        "assist[class_id]":this.sendItem.child.class_id,
                        "assist[teacher_ids]":this.teacher_ids,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg: data.message
                            });
                            that.showDetails(that.contentList.behavior._id)
                            $('#sendTeacherModal').modal('hide')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            senTeacherOption(){
                this.sendTecherOptions=[]
            },
            sendConfirmTeacher(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("sendBehaviorMsgToTeacher") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        teacher_ids:this.sendTeacherUid,
                        id: this.contentList.behavior._id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.sendTecherOptions=[]
                            that.sendTeacherUid=[]
                            resultTip({
                                msg: data.message
                            });
                            that.showDetails(that.contentList.behavior._id)
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            loseFocus(val) {
                // 下拉框隐藏时
                if (!val) {
                    this.sendTecherOptions=[]
                }
            },
            sendRemoteMethod(query) {
                let that=this
                if (query !== '') {
                    this.loading = true;
                    $.ajax({
                        url: '<?php echo $this->createUrl("teacherSearch") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            teacher_name:query,
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.sendTecherOptions = Object.values(data.data) ;
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                            that.loading = false;
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.loading = false;
                        },
                    })
                } else {
                    this.options = [];
                }
            },
            filterStu(){
                let that=this
                if(that.moreChildList.list){
                    that.searchChildVal=''
                    $('#moreChildListModal').modal('show')
                    return
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("getRecordOnlyNumberByChild") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        grade_group:that.currentGrade_group,
                        start_year: that.currentYear
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            data.data.list.forEach(item => {
                                item.child_name=data.data.child_list[item.child_id].bilingual_name
                            });
                            that.moreChildList=data.data
                            that.searchChildVal=''
                            $('#moreChildListModal').modal('show')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            filterClass(){
                let that=this
                if(that.moreClassList.list){
                    $('#moreClassListModal').modal('show')
                    return
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("getRecordOnlyNumberByClass") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        grade_group:that.currentGrade_group,
                        start_year: that.currentYear
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.moreClassList=data.data
                            $('#moreClassListModal').modal('show')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            filterList(id,name,type,_id){
                if(_id){
                    this.$refs['popover-'+_id].doClose()
                }
                if(type=='child'){
                    this.searchChild=[id]
                    this.searchClass=[]
                }else{
                    this.searchClass=[id]
                    this.searchChild=[]
                }
                this.searchName=name
                this.getData('init')
                $('#moreChildListModal').modal('hide')
                $('#moreClassListModal').modal('hide')
            },
            handleClose() {
                this.searchName=''
                this.searchChild=[]
                this.searchClass=[]
                this.getData('init')
            },
            viewAffirm(list){
                let data={
                    child_id:list.id,
                    num:list.affirm_num
                }
               this.identifiedList(data,'content')
            },
            identifiedList(list,type){
                let that=this
                if(list.num==0){
                    return
                }
                this.affirmRecordList={}
                this.affirmData=list
                $.ajax({
                    url: '<?php echo $this->createUrl("allBehaviorAffirmRecordList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        child_id:list.child_id,
                        start_year: that.currentYear,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.affirmRecordList=data.data
                            $('#identifiedModal').modal('show')
                            if(type=='content'){
                                $('#contentModal').modal('hide')                                
                            }
                            if(type=='child'){
                                $('#moreChildListModal').modal('hide')
                            }
                            if(type=='class'){
                                $('#moreClassListModal').modal('hide')
                            }
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            showFollow(list){
                if(list.indexOf("OUTSCHOOLSUSPENSION")!=-1 || list.indexOf("INSCHOOLSUSPENSION")!=-1 ){
                    return true
                }else{
                    return false
                }
            },
            addReaff(){
                this.officeData={
                    identified:'',
                    actions_other:'',
                    duration:"0:0",
                    durationTime:"0:0",
                    remark:'',
                    actions:[],
                    attachments:{
                        img:[],
                        other:[]
                    },
                    student_ids:[],
                    contact_parents:[]
                }
                this.addChildList=[]
                this.attachments={
                    img:[],
                    other:[]
                }
                this.editStatus=2
                
                $('#afreshResultModal').modal('show')
            },
            showDisabled(id){
                var finallyId=[]
                this.contentList.finally_data.forEach(item => {
                    finallyId.push(item.child.id)
                });
                if(finallyId.indexOf(id)!=-1){
                    return true
                }else{
                    return false
                }
            },
            reaffirmation(data,type){
                this.officeData={
                    identified:data.result.identified,
                    actions_other:data.result.actions_other,
                    duration:data.result.duration,
                    remark:data.result.remark,
                    actions:data.result.actions,
                    attachments:{ 
                        img:[],
                        other:[]
                    },
                    contact_parents:data.result.contact_parents
                }
                var imgs=[]
                var others=[]
                if(data.result.attachments.img){
                    data.result.attachments.img.forEach(item => {
                        imgs.push(this.contentList.attachmentList[item]) 
                    });
                }
                if(data.result.attachments.other){
                    data.result.attachments.other.forEach(item => {
                        others.push(this.contentList.attachmentList[item]) 
                    });
                }
                this.attachments={
                    img:imgs,
                    other:others
                }
                var hourNum='0'
                var minuteNum='0'
                let hour=this.officeData.duration.indexOf("<?php echo Yii::t("referral", "Hour"); ?>")
                if(hour!=-1){
                    hourNum =this.officeData.duration.substring(0,hour)
                }
                let minute=this.officeData.duration.indexOf("<?php echo Yii::t("teaching", "Minutes"); ?>")
                if(minute!=-1){
                    if(hour!=-1){
                        minuteNum =this.officeData.duration.substring(hour+2,minute)
                    }else{
                        minuteNum =this.officeData.duration.substring(0,minute) 
                    }
                }
                this.officeData.durationTime=hourNum+':'+minuteNum
                this.editStatus=1
                this.showActions([data.child.level])
                this.reaffData=data
                if(this.uploader.length!=0) {
                    for(var i=0;i<this.uploader.length;i++){
                    this.uploader[i].destroy();
                    }
                }
                $('#afreshResultModal').modal('show')
                this.$nextTick(()=>{
                    config['token'] = this.qiniuToken;
                    config['browse_button'] ='afreshAddFile';
                    var uploader=new plupload.Uploader(config);
                    this.uploader.push(uploader);
                    uploader.init();
                })
            },
            afreshOffice(type){
                let that=this
                if(this.officeData.student_ids.length==0){
                    resultTip({
                        error: 'warning',
                        msg: '<?php echo Yii::t("referral", "Student"); ?>'
                    });
                    return
                }
                for(var i=0;i<this.attachments.img.length;i++){
                    this.officeData.attachments.img.push(this.attachments.img[i]._id)
                }
                for(var i=0;i<this.attachments.other.length;i++){
                    this.officeData.attachments.other.push(this.attachments.other[i]._id)
                }
                // for(var i=0;i<this.addChildList.length;i++){
                //     this.officeData.student_ids.push(this.addChildList[i].id)
                // }
                let hour=this.officeData.durationTime.split(':')
                if(parseInt(hour[0])==0){
                    this.officeData.duration=hour[1]+' <?php echo Yii::t("teaching", "Minutes"); ?>'
                }else{
                    this.officeData.duration=hour[0]+' <?php echo Yii::t("referral", "Hour"); ?> '+hour[1]+' <?php echo Yii::t("teaching", "Minutes"); ?>'
                }
                var data={id:this.contentList.behavior._id,data:this.officeData,help:0}
                $.ajax({
                    url: '<?php echo $this->createUrl("officeProcess") ?>',
                    type: "post",
                    dataType: 'json',
                    data:data,
                    success: function(data) {
                        if (data.state == 'success') {
                            $('#afreshResultModal').modal('hide')
                            resultTip({
                                msg: data.message
                            });
                            that.showDetails(that.contentList.behavior._id)
                            
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            afreshReaff(){
                let that=this
                for(var i=0;i<this.attachments.img.length;i++){
                    this.officeData.attachments.img.push(this.attachments.img[i]._id)
                }
                for(var i=0;i<this.attachments.other.length;i++){
                    this.officeData.attachments.other.push(this.attachments.other[i]._id)
                }
                let hour=this.officeData.durationTime.split(':')
                if(parseInt(hour[0])==0){
                    this.officeData.duration=hour[1]+' <?php echo Yii::t("teaching", "Minutes"); ?>'
                }else{
                    this.officeData.duration=hour[0]+' <?php echo Yii::t("referral", "Hour"); ?> '+hour[1]+' <?php echo Yii::t("teaching", "Minutes"); ?>'
                }
                var data={id:this.reaffData._id,data:this.officeData}
                $.ajax({
                    url: '<?php echo $this->createUrl("afreshAffirm") ?>',
                    type: "post",
                    dataType: 'json',
                    data:data,
                    success: function(data) {
                        if (data.state == 'success') {
                            $('#afreshResultModal').modal('hide')
                            resultTip({
                                msg: data.message
                            });
                            that.showDetails(that.contentList.behavior._id)
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            showFollow(list){
                if(list.indexOf("OUTSCHOOLSUSPENSION")!=-1 || list.indexOf("INSCHOOLSUSPENSION")!=-1 ){
                    return true
                }else{
                    return false
                }
            },
            follow(data,type){
                this.followItem=data
                this.followType=type
                this.followData={
                    remark:this.followItem.followup.remark?this.followItem.followup.remark:'',
                    date:this.followItem.followup.date?this.followItem.followup.date:'',
                    time:this.followItem.followup.time?this.followItem.followup.time:''
                }
                $('#followModal').modal('show')
            },
            saveFollow(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("saveFollowup") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:this.followItem._id,
                        data:this.followData

                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.showDetails(that.contentList.behavior._id)
                            resultTip({
                                msg:data.message
                            });
                             $('#followModal').modal('hide')

                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            allSendMessage(item){
                this.sendTecherOptions=[]
                $('#allSendLogModal').modal('show')
            },
        }
    })

</script>
