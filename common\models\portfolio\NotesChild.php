<?php

/**
 * This is the model class for table "ivy_notes_child".
 *
 * The followings are the available columns in table 'ivy_notes_child':
 * @property integer $id
 * @property integer $classid
 * @property integer $childid
 * @property integer $yid
 * @property integer $weekid
 * @property string $weeknumber
 * @property integer $stat
 * @property string $en_content
 * @property string $cn_content
 * @property integer $updated_timestamp
 * @property integer $userid
 */
class NotesChild extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return NotesChild the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_notes_child';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('classid, childid, weeknumber, stat, updated_timestamp, userid', 'required'),
			array('classid, childid, yid, weekid, stat, updated_timestamp, userid', 'numerical', 'integerOnly'=>true),
			array('weeknumber', 'length', 'max'=>10),
			array('en_content, cn_content, invalid, startyear', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, classid, childid, yid, weekid, weeknumber, stat, en_content, cn_content, updated_timestamp, userid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'yInfo'=>array(self::BELONGS_TO, 'Calendar', 'yid'),
            'cInfo'=>array(self::BELONGS_TO, 'IvyClass', 'classid'),
            'weekInfo'=>array(self::HAS_ONE, 'CalendarWeek', '', 'on'=>'t.yid = weekInfo.yid and t.weeknumber=weekInfo.weeknumber'),
            'childInfo' => array(self::BELONGS_TO, 'ChildProfileBasic', 'childid', 'together' => false, 'with' => 'homeaddr'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'classid' => 'Classid',
			'childid' => 'Childid',
			'yid' => 'Yid',
			'weekid' => 'Weekid',
			'weeknumber' => 'Weeknumber',
			'stat' => 'Stat',
			'en_content' => 'En Content',
			'cn_content' => 'Cn Content',
			'updated_timestamp' => 'Updated Timestamp',
			'userid' => 'Userid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('weekid',$this->weekid);
		$criteria->compare('weeknumber',$this->weeknumber,true);
		$criteria->compare('stat',$this->stat);
		$criteria->compare('en_content',$this->en_content,true);
		$criteria->compare('cn_content',$this->cn_content,true);
		$criteria->compare('updated_timestamp',$this->updated_timestamp);
		$criteria->compare('userid',$this->userid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
        /**
         * 得到孩子所上过的所有班级的ID
         * 为什么要写在这儿呢 除当前学年有周报告才证明上过此班（合并班）
         * @param int $childid
         * @return array
         */
        public function getClassIdList($childid){
            $cc_cri = new CDbCriteria();
            $cc_cri->compare('childid', $childid);
            $cc_cri->distinct  = 'classid';
            $cc_cri->select  = 'classid';
            $cc_info = $this->findAll($cc_cri);
            $classIds = null;
            if (!empty($cc_info)) {
                foreach ($cc_info as $cc) {
                    $classIds[$cc->classid] = $cc->classid;
                }
            }
            return $classIds;
        }
}