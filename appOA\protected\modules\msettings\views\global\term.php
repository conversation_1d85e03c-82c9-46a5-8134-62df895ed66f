<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','System Management'), array('/msettings'))?></li>
        <li class="active">双语配置</li>
    </ol>

    <div class="row">
        <div class="col-md-12">
            <div class="mb20">
                <?php
                echo CHtml::ajaxLink(
                    '<span class="glyphicon glyphicon-refresh" aria-hidden="true"></span>
                    Clear Cache</button>',
                    $this->createUrl('//msettings/global/clearTermCache'),
                    array(
						'type' => 'post',
						'dataType' => 'json',
						'success' => 'js:success', 
                        ),
                    array(
                        'class' => 'btn btn-success'
                    )
                )
                ?>
            </div>
            <div class="table-responsive">
                <table class="table table-condensed" id="J_table_list">
                    <thead>
                    <tr>
                        <th width="50"></th>
                        <th width="130">标记/排序</th>
                        <th width="200">英文名称</th>
                        <th width="125"></th>
                        <th width="200">中文名称</th>
                        <th>操作</th>
                    </tr>
                    </thead>
                    <?php
                    foreach ($items as $item) {
                        $count=count($item->diglossia);
                        $icon='zero_icon';
                        if($count>0){
                            $icon='J_start_icon glyphicon glyphicon-chevron-down';
                        }
                        ?>
                        <tbody>
                        <tr class="background-gray">
                            <td class="text-center text-primary" style="line-height: 30px;">
                                <span class="<?php echo $icon?>" data-id="<?php echo $item->category_id?>"></span>
                                <input name="data[cateid]" type="hidden" value="<?php echo $item->category_id?>" readonly>
                            </td>
                            <td>
                                <input name="data[category_sign]" type="text" class="form-control length_2" value="<?php echo $item->category_sign?>" readonly>
                            </td>
                            <td>
                                <input name="data[category_entitle]" type="text" class="form-control length_4" value="<?php echo $item->category_entitle?>" readonly>
                            </td>
                            <td>
                                <a href="#" class="J_addChild btn btn-primary btn-sm" style="display: none;" data-id="<?php echo $item->category_id?>" data-html="tbody" data-type="nav_2">
                                    <span class="glyphicon glyphicon-plus"></span>
                                    添加二级选项
                                </a>
                            </td>
                            <td>
                                <input name="data[category_cntitle]" type="text" class="form-control length_4" value="<?php echo $item->category_cntitle?>" readonly>
                            </td>
                            <td>
                                <a href="#" class="btn btn-info btn-sm J_newRow_edit"><i class="glyphicon glyphicon-pencil"></i></a>
                                <a href="<?php echo $this->createUrl('deleteCate', array('id'=>$item->category_id));?>" data-msg="请谨慎删除此数据，因为有可能在其他地方使用过了！" class="btn btn-danger btn-sm J_ajax_del"><i class="glyphicon glyphicon-remove"></i></a>
                            </td>
                        </tr>
                        </tbody>
                        <?php if($count>0){?>
                            <tbody id="J_table_list_<?php echo $item->category_id?>">
                            <?php
                            $i = 0;
                            foreach ($item->diglossia as  $childValue) {
                                ?>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>
                                        <input name="data[navid]" type="hidden" value="<?php echo $childValue->diglossia_id?>" readonly >
                                        <input name="data[weight]" type="text" class="form-control length_2" value="<?php echo $childValue->weight?>" readonly>
                                    </td>
                                    <td>
                                        <input name="data[entitle]" type="text" class="form-control length_4" value="<?php echo $childValue->entitle?>" readonly>
                                    </td>
                                    <td></td>
                                    <td>
                                        <input name="data[cntitle]" type="text" class="form-control length_4" value="<?php echo $childValue->cntitle?>" readonly>
                                    </td>
                                    <td>
                                        <a href="#" class="btn btn-info btn-sm J_newRow_edit"><i class="glyphicon glyphicon-pencil"></i></a>
                                        <a href="<?php echo $this->createUrl('delete', array('id'=>$childValue->diglossia_id));?>" data-msg="请谨慎删除此数据，因为有可能在其他地方使用过了！" class="btn btn-danger btn-sm J_ajax_del"><i class="glyphicon glyphicon-remove"></i></a>
                                    </td>
                                </tr>
                            <?php }?>
                            </tbody>
                        <?php }}?>
                </table>
                <table class="table">
                    <tr>
                        <td colspan="5" style="padding-left:38px;">
                            <a data-type="nav_1" data-html="tbody" href="" class="btn btn-primary" id="J_add_root">
                                <span class="glyphicon glyphicon-plus"></span>
                                添加选项分类
                            </a>
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <script>
            var root_tr_html = '<tr class="background-gray">\
                <td class="text-center text-primary" style="line-height: 30px;">\
                    <span class="glyphicon glyphicon-asterisk"></span>\
                </td>\
                <td>\
                    <input name="newroot[category_sign]" type="text" class="form-control length_2" value="">\
                </td>\
                <td>\
                    <input name="newroot[category_entitle]" type="text" class="form-control length_4" value="">\
                </td><td>\
                <td>\
                    <input name="newroot[category_cntitle]" type="text" class="form-control length_4" value="">\
                </td>\
                <td>\
                    <a href="#" class="btn btn-success btn-sm J_newRow_save"><i class="glyphicon glyphicon-floppy-saved"></i></a>\
                    <a href="" class="btn btn-danger btn-sm J_newRow_del"><i class="glyphicon glyphicon-remove"></i></a>\
                </td>\
            </tr>',
            child_tr_html = '<tr>\
                <td>&nbsp;</td>\
                <td>\
                    <input name="newchild[weight]" type="text" value="" class="form-control length_2">\
                </td>\
                <td>\
                    <input name="newchild[entitle]" type="text" class="form-control length_4" value="">\
                </td>\
                <td></td>\
                <td>\
                    <input name="newchild[cntitle]" type="text" class="form-control length_4" value="">\
                </td>\
                <td>\
                    <a href="#" class="btn btn-success btn-sm J_newRow_save"><i class="glyphicon glyphicon-floppy-saved"></i></a>\
                    <a href="#" class="btn btn-danger btn-sm J_newRow_del"><i class="glyphicon glyphicon-remove"></i></a>\
                    <input type="hidden" name="newchild[category_id]" value="id_"/>\
                </td>\
            </tr>';
            head.js('/themes/base/js/util_libs/forumTree_table.js');

            $('#J_table_list').on('click', 'a.J_newRow_save', function(e){
                e.preventDefault();
                var tr = $(this).parents('tr');
                var inputs = tr.find('input');
                var data = {};
                for(var i=0; i<inputs.length; i++){
                    var input = inputs[i];
                    data[input.name]=input.value;
                }
                $.post('', data, function(ret){
                    if(ret.state == 'success'){
                        resultTip({msg: ret.message, callback: function(){
                            reloadPage(window);
                        }});
                    }
                    else{
                        resultTip({error:1, msg: ret.message});
                    }
                }, 'json');
                console.log(data);
            });

            $('#J_table_list').on('click', 'a.J_newRow_edit', function(e){
                e.preventDefault();
//                $('#J_table_list tbody tr td input').each(function(index, value){
//                    $(value).attr('readonly', true);
//                });
                var tr = $(this).parents('tr');
                var inputs = tr.find('input');
                for(var i=0; i<inputs.length; i++){
                    var input = inputs[i];
                    $(input).removeAttr('readonly');
                }
                $(this).removeClass('J_newRow_edit btn-info').addClass('J_newRow_save btn-success').find('i').removeClass('glyphicon-pencil').addClass('glyphicon-floppy-saved');
            });
			
			function success(data){
				if(data.state == 'success'){
				resultTip({msg:data.message});
			}
			else{
				alert(data.message);
			}
			}
        </script>
    </div>