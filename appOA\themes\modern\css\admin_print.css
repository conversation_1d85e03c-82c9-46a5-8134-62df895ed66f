html,body, 
div, dl, dt, dd, ul, p, th, td,
h1, h2, h3, h4, h5, h6, 
pre, code, form, 
fieldset, legend{
	margin: 0;
	padding: 0;
    font: normal 12px "Microsoft Yahei",Arial,Helvetica,sans-serif;
}
cite, 
em, 
th {
	font-style: inherit;
	font-weight: inherit;
}
strong{
	font-weight:700;
}
td, 
th, 
div {
	word-break:break-all;
	word-wrap:break-word;
}
table {
	border-collapse: collapse;
	border-spacing:0;
}
th {
	text-align: left;
	font-weight:100;
}
ol li {
	list-style: decimal outside;
}
ol{
	padding:0 0 0 18px;
	margin:0;
}
li {
	list-style:none;
}
img {
	border: 0;
}
html {
	-webkit-text-size-adjust:none;
}
.btn_wrap{
	position:fixed;
	bottom:0;
	left:0;
	width:100%;
	z-index:5;
	_position: absolute;
	_top: expression(documentElement . scrollTop +   documentElement . clientHeight-this . offsetHeight);
}
.btn_wrap1{
	position:fixed;
	top:0;
	left:0;
	width:100%;
	z-index:5;
	_position: absolute;
	_bottom: expression(documentElement . scrollTop +   documentElement . clientHeight-this . offsetHeight);
}
.btn_wrap_pd{
	padding:0 10px 10px;
}
.btn_wrap_pd{
	padding:10px 20px 10px;
	background:#eaeaea url(../images/admin/content/btn_wrap.png) repeat-x;
	border-top:1px solid #d8d8d8;
}
.btn_wrap_pd .btn{
	min-width:80px;
	margin-right:10px;
	_width:100px;
}
.switch_list{
	height:25px;
	line-height:25px;
	overflow:hidden;
}
.switch_list li{
	float:left;
	width:180px;
}
.b{
	font-weight:700 !important;
}
.u{
	text-decoration:underline !important;
}
.i{
	font-style:italic !important;
}
.w{
	white-space:nowrap;
}
.tal{
	text-align:left !important;
}
.tac{
	text-align:center !important;
}
.tar{
	text-align:right !important;
}
.fl{
	float:left !important;
	display:inline;
}
.fr{
	float:right !important;
	display:inline;
}
.pr{
	position:relative;
}
.cp{
	cursor:pointer;
}
.vt{
	vertical-align:top;
}
.dn{
	display:none;
}
.btn {
	color: #333;
	background:#e6e6e6 url(../images/admin/content/btn.png);
	border: 1px solid #c4c4c4;
	border-radius: 2px;
	text-shadow: 0 1px 1px rgba(255, 255, 255, 0.75);
	padding:4px 10px;
	display: inline-block;
	cursor: pointer;
	font-size:100%;
	line-height: normal;
	text-decoration:none;
	overflow:visible;
	vertical-align: middle;
	text-align:center;
	zoom: 1;
	white-space:nowrap;
	font-family:inherit;
	_position:relative;
	margin:0;
}
.btn_submit,
.btn_submit:hover{
  color: #ffffff !important;
}
/*提交按钮*/
.btn_submit{
	background-position:0 -120px;
	background-color: #1b75b6;
	text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
	border-color: #106bab #106bab #0d68a9;
}
.btn_submit:hover{
	background-position:0 -160px;
}
.btn_submit:active{
	background-position:0 -201px;
}
.wp{
    margin: 50px auto !important;
    width: 700px;
}