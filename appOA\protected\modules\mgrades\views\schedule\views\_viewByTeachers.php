<div class="col-md-2">
    <div class="panel panel-default">
        <div class="panel-heading"><?php echo Yii::t('campus','Teachers List');?></div>
        <div class="list-group view-by-teacher-list">
            <!-- place holder -->
        </div>

    </div>
</div>
<div class="col-md-10 col-lg-8 teacher-schedule-zone">
    <table class="table table-bordered teacher-schedule" style="display: none;">
        <thead></thead>
        <tbody>
        </tbody>
    </table>
</div>

<script type="text/template" id="teachers-teacher-item-template">
    <a class="list-group-item" href="javascript:;" onclick="loadScheduleByTeacher(<%= uid %>, this)">
        <span> <% print(pageData.teacherList[uid].name); %></span>

        <span class="badge pull-right"><%- courseCount %></span>
    </a>
</script>

<script>
    var assignedTeachers = null;
    var assignedTeacherScheduleData = null;
    var teachersTeacherItemTemplate = _.template($('#teachers-teacher-item-template').html());

    var sortedTeacherData = null;

    var renderDatateachers = function(viewid){
        wrapper = $('.view-zone[data-viewid|="'+viewid+'"]');
        assignedTeacherScheduleData = _.groupBy(pageData.scheduleList1, 'teacher_uid');
        sortedTeacherData = _.map(assignedTeacherScheduleData, function(_tmpData, _tmpUid){
            return {uid: _tmpUid, courseCount: _.size(_tmpData)}
        });
        sortedTeacherData = _.sortBy(sortedTeacherData, 'courseCount');
        wrapper.find('.view-by-teacher-list').empty();

        _.each(sortedTeacherData, function(item){
            if(!_.isEmpty(pageData.teacherList[item.uid])){
                var _teacher = teachersTeacherItemTemplate(item);
                wrapper.find('.view-by-teacher-list').prepend(_teacher);
            }
        });
    }

    var loadScheduleByTeacher = function(tid, obj){
        wrapper.find('table.teacher-schedule').hide();
        var userScheduleData = assignedTeacherScheduleData[tid];

        if(!_.isEmpty(userScheduleData)){
            var table = wrapper.find('table.teacher-schedule');
            var thead = table.find('thead');
            var tbody = table.find('tbody');
            thead.empty();
            tbody.empty();

            //画表头
            var _tr = $('<tr class="active"></tr>').append('' +
                '<th class="warning" width=120><h4>'+
                '<span class="glyphicon glyphicon-calendar"></span> ' +
                '</h4></th>' +
                '');
            _.each( pageData.dayLabels, function(day, i){
                _tr.append($('<th class="active"></th>').html('<h4>'+day+'</h4>'));
            } );
            thead.append(_tr);

            //画表体
            _.each( programCfg.schedules[scheduleCode], function(timeslot){
                var _tr = $('<tr></tr>').append($('<th class="active"></th>').html('<h5>'+timeslot+'</h5>'));
                _.each( pageData.dayLabels, function(day, i){
                    _tr.append($('<td width="18%"></td>').attr('data-day',i).attr('data-timeslot',timeslot));
                } );
                tbody.append(_tr);
            });
            //填充表格内容
            _.each(userScheduleData, function(item,index){
                var _item = classesScheduleItemTemplate(item);
                var _class = classesScheduleClassInfoTemplate(item);
                tbody.find('td[data-timeslot|="'+item.timeslot+'"][data-day|="'+item.weekday+'"]')
                    .attr("data-subject",item.subject_flag)
                    .append(_item)
                    .append(_class);
            });
            table.show('blind',{},500);
            $(obj).siblings().removeClass('active');
            $(obj).addClass('active');
        }
    }
</script>