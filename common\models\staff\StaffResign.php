<?php

/**
 * This is the model class for table "ivy_staff_resign".
 *
 * The followings are the available columns in table 'ivy_staff_resign':
 * @property integer $id
 * @property integer $staff_uid
 * @property integer $resign_timestamp
 * @property integer $account_close_timestamp
 * @property integer $processed
 * @property string $updated
 * @property integer $updated_user
 * @property integer $departure_why
 */
class StaffResign extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_staff_resign';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('staff_uid, resign_timestamp, account_close_timestamp, departure_why, processed, updated, updated_user', 'required'),
			array('staff_uid, resign_timestamp, account_close_timestamp, processed, updated_user', 'numerical', 'integerOnly'=>true),
            array('branch_id, processed_time,entry_timestamp', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, staff_uid, resign_timestamp, account_close_timestamp,departure_why,processed, updated, updated_user', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'staff_uid' => 'Staff Uid',
			'resign_timestamp' => Yii::t('labels', 'Resign Date'),
			'account_close_timestamp' => Yii::t('labels', 'Account Deactive Date'),
			'processed' => 'Processed',
			'updated' => 'Updated',
			'updated_user' => 'Updated User',
			'departure_why' => Yii::t('labels', 'Reason'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('staff_uid',$this->staff_uid);
		$criteria->compare('resign_timestamp',$this->resign_timestamp);
		$criteria->compare('account_close_timestamp',$this->account_close_timestamp);
		$criteria->compare('processed',$this->processed);
		$criteria->compare('updated',$this->updated,true);
		$criteria->compare('updated_user',$this->updated_user);
		$criteria->compare('departure_why',$this->departure_why);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return StaffResign the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
