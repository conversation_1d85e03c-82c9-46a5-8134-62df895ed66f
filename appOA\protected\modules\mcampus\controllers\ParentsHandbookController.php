<?php

class ParentsHandbookController extends BranchBasedController
{

	public $actionAccessAuths = array(
		'Update'=>array('access'=>'o_A_Access', 'admin'=>'ivystaff_opgeneral'),
		'Delete'=>'ivystaff_opgeneral',

		'LogIndex'=>'o_A_Access',
		'LogAdd'=>'o_A_Adm_Support',
		'LogUpdate'=>'o_A_Adm_Support',
		'LogDelete'=>'o_A_Adm_Support',
	);

	public $dialogWidth = 600;
	public $time_hour = array('00','01','02','03','04','05','06','07','08','09',10,11,12,13,14,15,16,17,18,19,20,21,22,23);
	public $time_minute = array('00'=>'00','10'=>'10','20'=>'20','30'=>'30','40'=>'40','50'=>'50');

	public function createUrl($route, $params = array(), $ampersand = '&')
	{
	    if (empty($params['branchId'])) {
	        $params['branchId'] = $this->branchId;
	    }
	    return parent::createUrl($route, $params, $ampersand);
	}
	
	public function init(){
	    parent::init();
	    Yii::app()->theme = 'blue';
	    $this->layout = "//layouts/column1";

		$this->modernMenuFlag = 'campusOp';
		$this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
		$this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

		//初始化选择校园页面
		$this->branchSelectParams['hideOffice'] = false;
		$this->branchSelectParams['urlArray'] = array('//mcampus/parentsHandbook/index');
	}

	public function actionIndex()
	{
		Yii::import('common.models.IvyClass.*');
		Yii::import('common.models.ChildProfileBasic.*');
		Yii::import('common.models.feedback.*');


		$criteria = new CDbCriteria;
		$criteria->compare('schoolid', $this->branchId);
		$criteria->compare('stat', IvyClass::STATS_OPEN);
		$criteria->order = "child_age ASC, title ASC";
		$classlist = IvyClass::model()->findAll($criteria);//所有的班级

		$criteria = new CDbCriteria;
		$criteria->compare('schoolid', $this->branchId);
		$criteria->compare('status', ChildProfileBasic::STATS_ACTIVE);
		$childlist = ChildProfileBasic::model()->findAll($criteria);//所有的孩子

		$criteria = new CDbCriteria;
		$criteria->compare('version', 'v1');
		$criteria->compare('status', HandbookReport::STATS_AGREE);
		$HandbookReport = HandbookReport::model()->findAll($criteria);//
		$childs = array();
		foreach($HandbookReport as $v){
			$childs[$v->childid]['status'] = $v->status;
			$childs[$v->childid]['timestamp'] = $v->timestamp;
		}

		$_childlist = array();
		foreach($childlist as $k => $child){
			$_childlist[$k]['childid'] = $child->childid;
			$_childlist[$k]['name'] = $child->getChildName();
			$_childlist[$k]['classid'] = $child->classid;
			$_childlist[$k]['status'] = $childs[$child->childid]['status'];
			$_childlist[$k]['time'] = $childs[$child->childid]['timestamp'];
		}
		$_childlist = $this->mymArrsort($_childlist,'time');


		$this->render('index',array(
			'classlist'=>$classlist,
			'childlist'=>$_childlist,
		));
	}

	function mymArrsort($arr,$var){
		$tmp=array();
		$rst=array();
		foreach($arr as $key=>$trim){
			$tmp[$key]=$trim[$var];
		}
		arsort($tmp);
		$i=0;
		foreach($tmp as $key1=>$trim1){
			$rst[$i]=$arr[$key1];
			$i++;
		}
		return $rst;
	}
}
