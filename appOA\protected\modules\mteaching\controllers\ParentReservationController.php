<?php

class ParentReservationController extends TeachBasedController
{
    public $selectedClassId=0;
    public $selectedSemester='';
    public $selectedTask='';
    public $selectedChildId=0;
    public $semesters=array(1,2);
    public $ivyAccessToken = '';
    public $dsAccessToken = '';
    public $accessToken = '';
    public $childPlanList = array();

	public $printFW = array();

    public $isManager = false;

    // 访问action的初级权限
    public $actionAccessAuths = array(
        //'index'                 => 'o_A_Adm_Support',
        'Update'                => 'o_A_Adm_Support',
        'DeletsPlan'            => 'o_A_Adm_Support',
        'DelChild'              => 'o_A_Adm_Support',
        'InquireWechat'         => 'o_A_Adm_Support',
        'CanReserveChilds'      => 'o_A_Adm_Support',
        'UpdateChild'           => 'o_A_Adm_Support',
        'DelNum'                => 'o_A_Adm_Support',
        'AddNum'                => 'o_A_Adm_Support',
        //'Edit'                  => 'o_A_Adm_Support',
        'SetPlanStatus'         => 'o_A_Adm_Support',
        'ClearChild'            => 'o_A_Adm_Support',
        'SendMessage'           => 'o_A_Adm_Support',
        'SaveDuration'          => 'o_A_Adm_Support',
        'SaveTimeslot'          => 'o_A_Adm_Support',
        'OpenTimeslot'          => 'o_A_Adm_Support',
        'DelTimeslot'           => 'o_A_Adm_Support',
        'SaveTimeslotwithDay'   => 'o_A_Adm_Support',
        'SaveTimeslotwithItem'  => 'o_A_Adm_Support',
        'GetTimeslotInfo'       => 'o_A_Adm_Support',
        'GetDayInfo'            => 'o_A_Adm_Support',
        'GetDayInfoDS'          => 'o_A_Adm_Support',
        'CancelTimeslot'        => 'o_A_Adm_Support',
    );

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'teaching';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Teaching Tasks');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mteaching/parentReservation/index');
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/datatables/jquery.dataTables.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/datatables/dataTables.bootstrap.min.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl.'/base/js/datatables/dataTables.bootstrap.min.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/vue.global.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.js');
        
        Yii::import('common.models.portfolio.*');
        Yii::import('common.models.ptc.*');

        $this->setPageTitle('家长预约');

        $this->isManager = Yii::app()->user->checkAccess('ivystaff_opschool');
    }

    public function beforeAction($action){
        if(!parent::beforeAction($action)){
            return false;
        }

        $this->selectedClassId = Yii::app()->request->getParam('classid', 0);
        $this->selectedSemester = Yii::app()->request->getParam('semester', 0);
        $this->selectedTask = Yii::app()->request->getParam('task', 0);
        $this->selectedChildId = Yii::app()->request->getParam('childid', 0);

        return true;
    }

    function checkPermission($planModel) {
        if ($this->isManager) {
            return true;
        }
        return $planModel->creater == Yii::app()->user->id;
    }

    public function actionIndex()
    {
        $criteria = new CDbCriteria();
        $criteria->compare('school_id', $this->branchId);
        // $criteria->compare('calendar_id', $this->branchObj->schcalendar);
        $criteria->compare('status', array(0, 1));
        $criteria->compare('semester', 100);
        if (!$this->isManager) {
            $criteria->compare('creater', Yii::app()->user->id); 
        }
        $plansModel = new CActiveDataProvider('ParentMeetingPlan', array(
            'criteria' => $criteria,
            'sort' => array(
                'defaultOrder'=>'startyear DESC, created DESC',
            ),
            'pagination'=>array(
                'pageSize'=>50,
            ),
        ));

        if($plansModel) {
            foreach ($plansModel->getData() as $val) {
                $planid[] = $val->id;
            }

            $criteria = new CDbCriteria();
            $criteria->compare('planid', $planid);
            $planChildids = PmeetPlanChild::model()->findAll($criteria);
            if($planChildids){
                foreach ($planChildids as $val){
                    $this->childPlanList[$val->planid][$val->childid] = 1;
                }
            }

        }

        $this->render('index', array(
            'plansModel' => $plansModel,
        ));
    }

    // 增加WIDA考试预约
    public function actionUpdate()
    {
        $plan_id = Yii::app()->request->getParam('plan_id','');
        $model = ParentMeetingPlan::model()->findByPk($plan_id);
        if(!$model){
            $model = new ParentMeetingPlan();
            $model->default_duration = '';
            $model->timeslot_starts = '';
            $model->school_id = $this->branchId;
            $model->semester = 100;
            $model->classid = 0;
            $model->status = 0;
            $model->children_ids = '';
        } else {
            if (!$this->checkPermission($model)) {
                Yii::app()->controller->forward('/child/message/error');
                Yii::app()->end();
            }
        }
        if(Yii::app()->request->isPostRequest){
            $parentMeetingPlan = Yii::app()->request->getParam('ParentMeetingPlan','');
            if(!$parentMeetingPlan['title_cn']){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('teaching', 'Title Cn cannot be blank! '));
                $this->showMessage();
            }
            if(!$parentMeetingPlan['title_en']){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('teaching', 'Title En cannot be blank! '));
                $this->showMessage();
            }
            if(!$parentMeetingPlan['starYear']){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('teaching', 'Annual cannot be blank! '));
                $this->showMessage();
            }
            if(!$parentMeetingPlan['start_date']){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('teaching', 'Start date cannot be blank! '));
                $this->showMessage();
            }
            if(!$parentMeetingPlan['end_date']){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('teaching', 'End date cannot be blank! '));
                $this->showMessage();
            }

            $criteria = new CDbCriteria();
            $criteria->compare('branchid', $this->branchId);
            $criteria->compare('startyear', $parentMeetingPlan['starYear']);
            $criteria->order = 'startyear desc';
            $calendarModel = CalendarSchool::model()->find($criteria);

            $model->startyear = $parentMeetingPlan['starYear'];
            $model->calendar_id = $calendarModel->yid;
            $model->extra = json_encode($parentMeetingPlan);
            $model->creater = Yii::app()->user->id;
            $model->created = time();
            if($model->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('teaching', 'Save succefully!'));
                $this->addMessage('callback', 'cbWida');
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('', '保存失败！'));
            }
            $this->showMessage();

        }
        if($model){
            $data = json_decode($model->extra);
            $model->title_cn = $data->title_cn;
            $model->title_en = $data->title_en;
            $model->starYear = $data->starYear;
            $model->start_date = $data->start_date;
            $model->end_date = $data->end_date;
        }

        $starYear = $this->yearList();
        $this->renderPartial('update', array(
            'model' => $model,
            'starYear' => $starYear,
        ));
    }

    // 删除plan表得数据 2019-06-28
    public function actionDeletsPlan()
    {
        $plan_id = Yii::app()->request->getParam('plan_id','');
        if($plan_id){
            $model = ParentMeetingPlan::model()->findByPk($plan_id);
            $criteria = new CDbCriteria();
            $criteria->compare('planid', $model->id);
            $itemCount = ParentMeetingItem::model()->count($criteria);
            if(!$itemCount) {
                if(!$model->status) {
                    if ($model->delete()) {
                        $criteria = new CDbCriteria();
                        $criteria->compare('planid', $model->id);
                        $planChildModel = PmeetPlanChild::model()->findAll($criteria);
                        if($planChildModel){
                            foreach ($planChildModel as $data){
                                $data->delete();
                            }
                        }

                        $criteria = new CDbCriteria();
                        $criteria->compare('planid', $model->id);
                        $planItemMemoModel = ParentMeetingItemMemo::model()->findAll($criteria);
                        if($planItemMemoModel){
                            foreach ($planItemMemoModel as $memoData){
                                $memoData->delete();
                            }
                        }

                        $this->addMessage('state', 'success');
                        $this->addMessage('message', Yii::t('teaching', 'Save succefully!'));
                        $this->addMessage('callback', 'cbWida');
                    } else {
                        $this->addMessage('state', 'fail');
                        $err = current($model->getErrors());
                        $this->addMessage('message', $err[0]);
                    }
                    $this->showMessage();
                }else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '已经开放给家长，不可删除！');
                    $this->showMessage();
                }
            }
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('teaching','Time Slots existed, cannot be deleted! '));
            $this->showMessage();
        }
    }

    // 删除孩子 2019-06-28
    public function actionDelChild()
    {
        $plan_id = Yii::app()->request->getParam('plan_id', '');
        $childid = Yii::app()->request->getParam('childid', '');
        if(Yii::app()->request->isAjaxRequest) {
            if ($plan_id && $childid) {
                $criteria = new CDbCriteria();
                $criteria->compare('planid', $plan_id);
                $criteria->compare('childid', $childid);
                $countItem = ParentMeetingItem::model()->count($criteria);
                if ($countItem) {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('teaching','Reserve schedules already open to parents, cannot be deleted! '));
                    $this->showMessage();
                }
                $criteria = new CDbCriteria();
                $criteria->compare('planid', $plan_id);
                $criteria->compare('childid', $childid);
                $planChildModel = PmeetPlanChild::model()->find($criteria);
                if (!$planChildModel) {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '没有找到孩子信息！');
                    $this->showMessage();
                }
                $planChildModel->delete();
                $this->addMessage('state', 'success');
                $this->addMessage('message', '删除成功');
                $this->showMessage();
            }
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '参数错误！');
            $this->showMessage();
        }
        $this->addMessage('state', 'fail');
        $this->addMessage('message', '非法操作！');
        $this->showMessage();
    }

    public function actionDelAllChild() {
        $plan_id = Yii::app()->request->getParam('plan_id', '');
        if(Yii::app()->request->isAjaxRequest) {
            if ($plan_id) {
                $criteria = new CDbCriteria();
                $criteria->compare('planid', $plan_id);
                $reservedItems = ParentMeetingItem::model()->findAll($criteria);
                
                $reservedChildIds = array();
                foreach($reservedItems as $item) {
                    $reservedChildIds[$item->childid] = $item->childid;
                }

                $criteria = new CDbCriteria(); 
                $criteria->compare('planid', $plan_id);
                if(!empty($reservedChildIds)) {
                    $criteria->addNotInCondition('childid', $reservedChildIds);
                }
                $planChildModel = PmeetPlanChild::model()->findAll($criteria);

                if (!$planChildModel) {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '没有可删除的孩子信息！');
                    $this->showMessage();
                }

                $deleted = 0;
                $deletedChildIds = array();
                foreach ($planChildModel as $model) {
                    if($model->delete()) {
                        $deletedChildIds[] = $model->childid;
                        $deleted++;
                    }
                }

                if($deleted > 0) {
                    $this->addMessage('state', 'success');
                    $this->addMessage('data', $deletedChildIds);
                    $this->addMessage('message', "成功删除{$deleted}个未预约的孩子");
                } else {
                    $this->addMessage('state', 'fail'); 
                    $this->addMessage('message', '删除失败');
                }
                $this->showMessage();
            }
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '参数错误！');
            $this->showMessage();
        }
        $this->addMessage('state', 'fail');
        $this->addMessage('message', '非法操作！');
        $this->showMessage();
    }

    // 获取有没有给孩子家长发过微信提醒 2019-06-28
    public function actionInquireWechat()
    {
        $plan_id = Yii::app()->request->getParam('plan_id', '');
        $childid = Yii::app()->request->getParam('childid', '');
        if(Yii::app()->request->isAjaxRequest) {
            if($plan_id && $childid){
                $criteria = new CDbCriteria();
                $criteria->compare('planid', $plan_id);
                $criteria->compare('childid', $childid);
                $ptcWide = WxnotifPtc::model()->count($criteria);
                $this->addMessage('state', 'success');
                $this->addMessage('data', $ptcWide);
                $this->addMessage('message', Yii::t('event', 'success'));
                $this->showMessage();
            }
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '参数错误！');
            $this->showMessage();
        }
        $this->addMessage('state', 'fail');
        $this->addMessage('message', '非法操作！');
        $this->showMessage();
    }

    public function getTitle($data)
    {
        $titleData = json_decode($data->extra);
        echo (Yii::app()->language == 'zh_cn') ? $titleData->title_cn : $titleData->title_en;
    }

    public function getStarYear($data)
    {
        $titleData = json_decode($data->extra);
        $yearData = $this->yearList();
        echo $yearData[$titleData->starYear];
    }

    public function getStartDate($data)
    {
        $info = json_decode($data->extra, true);
        echo isset($info['start_date']) ? $info['start_date'] : '';
    }

    public function getEndDate($data)
    {
        $info = json_decode($data->extra, true);
        echo isset($info['end_date']) ? $info['end_date'] : '';
    }

    public function getChildCount($data)
    {
        $num = 0;
        if($this->childPlanList && $this->childPlanList[$data->id]){
            $num = array_sum($this->childPlanList[$data->id]);
        }
        echo $num;
    }
    public function getStatus($data)
    {
        echo $data->status == 1 ? '开启' : '关闭';
    }

    public function getClass($data)
    {
        $classList = IvyClass::getClassTypes(true,50);
        $ClassData = json_decode($data->extra);
        $class = array();
        if($ClassData){
            foreach ($ClassData->classData as $val){
                $class[] = $classList[$val];
            }


        }
        return ($class) ? implode(', ', $class) : '';
    }

    public function getButton($data)
    {
        // 暂时注释wida的编辑与删除
        echo CHtml::link(Yii::t('global','Edit'), array('update', 'plan_id' => $data->id), array('class' => 'J_modal btn btn-info btn-xs')) .' ';
        echo CHtml::link(Yii::t('global','Settings'), array('edit', 'plan_id' => $data->id), array('class' => 'btn btn-info btn-xs')) .' ';
        // echo CHtml::link(Yii::t('global','Delete'), array('deletsPlan', 'plan_id' => $data->id), array('class' => 'J_ajax_del btn btn-xs btn-danger', 'data-msg' => "本次测试计划已经向家长发送过邀请，删除之后家长无法完成预约 <br><br>确定删除吗？"));
    }

    // 所有可预约的学生
    public function actionCanReserveChilds()
    {
        $plan_id = Yii::app()->request->getParam('plan_id','');
        Yii::import('common.models.regextrainfo.*');
        $model = ParentMeetingPlan::model()->findByPk($plan_id);
        $data = array();
        if($model) {
            $criteria = new CDbCriteria();
            $criteria->compare('schoolid', $model->school_id);
            $criteria->compare('yid', $model->calendar_id);
            $classModel = IvyClass::model()->findAll($criteria);

            $classData = array();
            foreach ($classModel as $val){
                $classData[$val->classid] = $val->title;
            }

            $criteria = new CDbCriteria();
            $criteria->index = 'childid';
            $criteria->compare('classid', array_keys($classData));
            $childModel = ChildProfileBasic::model()->findAll($criteria);


            // 查询现在已经增加的学生
            $criteria = new CDbCriteria();
            $criteria->compare('planid', $plan_id);
            $criteria->index = 'childid';
            $planChildModel = PmeetPlanChild::model()->findAll($criteria);

            foreach ($childModel as $val) {
                if(isset($planChildModel) && $planChildModel[$val->childid]) {
                    continue;
                }
                $data[] = array(
                    'id' => $val->childid,
                    'name' => $val->getChildName(),
                    'photo' => $val->photo,
                    'classid' => $val->classid,
                    'classTitle' => $classData[$val->classid],
                );

            }
        }

        echo json_encode($data);

    }

    // 选择学生增加带学生列表
    public function actionUpdateChild()
    {
        $plan_id = Yii::app()->request->getParam('plan_id', '');
        $childids = Yii::app()->request->getParam('childids', array());
        $data = array();
        if($plan_id && $childids){
            // 检查是否已添加
            $criteria = new CDbCriteria();
            $criteria->compare('planid', $plan_id);
            $criteria->compare('childid', $childids);
            $planChildModel = PmeetPlanChild::model()->findAll($criteria);
            if (count($planChildModel) > 0) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '包含已经添加过的孩子！');
                $this->showMessage();
            }
            $childData = array();
            foreach ($childids as $childid){
                $model = new PmeetPlanChild();
                $model->childid = $childid;
                $model->planid = $plan_id;
                $model->created_by = Yii::app()->user->id;
                $model->created_at = time();
                $model->updated_by = Yii::app()->user->id;
                $model->updated_at = time();
                $model->save();
                $childData[$childid] = $childid;
            }

            if($childData) {
                $criteria = new CDbCriteria();
                $criteria->compare('childid', $childData);
                $criteria->index = 'childid';
                $childModel = ChildProfileBasic::model()->findAll($criteria);

                if($childData) {
                    foreach ($childData as $val) {
                        $data[$val] = array(
                            'id' => $val,
                            'name' => ($childModel[$val]) ? (Yii::app()->language == 'en_us') ? $childModel[$val]->getChildName(null, null, true) : $childModel[$val]->getChildName(true, true) : $val,
                            'photo' => $childModel[$val]->photo,
                            'ccUrl' => OA::genCCUrlHome($val),
                        );
                    }
                }
            }
        }
        $this->addMessage('state', 'success');
        $this->addMessage('message', '添加成功！');
        $this->addMessage('data', $data);
        $this->showMessage();
    }
    // 语言配置
    public function languageData()
    {
        $langModels = Term::model()->lang()->findAll();

        $titleLang = (Yii::app()->language == "zh_cn") ? "cntitle" : "entitle";
        $langcn = CHtml::listData($langModels, 'diglossia_id', $titleLang);

        return $langcn;
    }
    // 拿到当前学校所有得学年
    public function yearList()
    {
        Yii::import('common.models.calendar.*');
        $startYearList = array();
        $criteria = new CDbCriteria();
        $criteria->compare('branchid', $this->branchId);
        $criteria->order = 'startyear desc';
        $criteria->limit = 2;
        $calendarModel = CalendarSchool::model()->findAll($criteria);
        // 根据当前校园查询出所有得学年
        $startYearList = array();
        foreach ($calendarModel as $val){
            $yearEnd = $val->startyear  + 1;
            $startYearList[$val->startyear] = $val->startyear . ' - ' . $yearEnd . ' ' .Yii::t('labels','School Year');
        }

        return $startYearList;
    }

    // 删除指定时间 2019-06-27
    public function actionDelNum()
    {
        $planid = Yii::app()->request->getPost('planid', 0);
        $days = Yii::app()->request->getPost('days', array());
        $timeslot = Yii::app()->request->getPost('timeslot', '');
        $index = Yii::app()->request->getPost('index', '');
        Yii::import('common.models.ptc.*');

        if(Yii::app()->request->isAjaxRequest) {
            $dataYmd = array();
            foreach ($days as $val){
                $dataYmd[] = date("Ymd", $val);
            }
            $times = $this->timeslot($planid, $timeslot);

            $criter = new CDbCriteria();
            $criter->compare('planid', $planid);
            $criter->compare('target_date', $dataYmd);
            $criter->compare('timeslot', $times);
            $criter->compare('meet_index', $index);
            $des = ParentMeetingItem::model()->findAll($criter);

            if($des) {
                foreach ($des as $k => $v) {
                    $v->delete();
                }
            }

            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('event', 'success'));
            $this->addMessage('data', $index);
            $this->showMessage();
        }else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '非法操作');
            $this->showMessage();
        }
    }

    // 增加直定月份日期内得时间 2019-06-27
    public function actionAddNum()
    {
        $planid = Yii::app()->request->getPost('planid', 0);
        $days = Yii::app()->request->getPost('days', array());
        $timeslot = Yii::app()->request->getPost('timeslot', '');
        Yii::import('common.models.ptc.*');

        if(Yii::app()->request->isAjaxRequest) {
            $times = $this->timeslot($planid, $timeslot);
            $data = 0;
            foreach ($days as $k1 => $v1){
                $criter = new CDbCriteria();
                $criter->compare('planid', $planid);
                $criter->compare('target_date', date('Ymd', $v1));
                $criter->compare('timeslot', $times);
                $criter->limit = 1;
                $criter->order = "meet_index DESC";
                $oldModel = ParentMeetingItem::model()->find($criter);
                $meet_index = ($oldModel) ? $oldModel->meet_index + 1 : 0;
                $model = new ParentMeetingItem;
                $model->planid = $planid;
                $model->target_date = date('Ymd', $v1);
                $model->timeslot = $times;
                $model->meet_index = $meet_index;
                $model->stem = 0;
                $model->save();
                $data = $model->meet_index;
            }

            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('event','success'));
            $this->addMessage('data', $data);
            $this->showMessage();
        }else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '非法操作');
            $this->showMessage();
        }
    }

    public function updatePlans()
    {
        if (Yii::app()->request->isPostRequest()) {
            $plan_id = Yii::app()->request->getParam('plan_id');
            $title_cn = Yii::app()->request->getParam('title_cn');
            $title_en = Yii::app()->request->getParam('title_en');
            $yid = Yii::app()->request->getParam('yid');
            $classes = Yii::app()->request->getParam('classes');
        }
    }

	public function actionEdit()
	{
        parent::initExt();
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/plupload/plupload.full.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/jquery.qrcode.min.js');

        $plan_id = Yii::app()->request->getParam('plan_id', '');
        $selectMonth = Yii::app()->request->getParam('selectMonth', '');

        $taskData = $this->taskPtc($plan_id);
        $schoolGroup = $this->branchObj->group;
        if($selectMonth){
            $taskData['selectMonth'] = $selectMonth;
        }
        //Yii::msg($taskData);
        $this->render('edit', array(
            'qrCodeUrl'=> $this->getQrCodeUrl($plan_id),
            'taskData'=>$taskData,
            'schoolGroup'=>$schoolGroup,
        ));
	}

    public function getYidStartYearByClassIdAndBranchId($classid, $branchId){
        $result = Yii::app()->db->createCommand()
            ->select('c.yid as yid, y.startyear as startyear')
            ->from('ivy_class_list c')
            ->join('ivy_calendar_yearly y', 'y.yid=c.yid')
            ->where('c.classid=:classid and c.schoolid=:schoolid',
                array(
                    ':classid'=>$classid,
                    ':schoolid'=>$branchId
                ))
            ->queryRow();
        return $result;
    }

    //设置预约计划状态
    public function actionSetPlanStatus(){
        /**
         * POST过来变量1. status, 检查必须是 online 或者 offline 之一
         * POST过来变量2. plan_id, 预约计划主键
         *
         * 1. 根据plan_id 查出 ParentMeetingPlan 的AR
         * 2. 判断school_id是否一致 $this->branchId
         * 3. 设置状态字段 status 的值，online 对应 SReport::STATUS_ONLINE; offline 对应 SReport::STATUS_OFFLINE
         * 4. 返回data值json数据格式 {id: xxx, status: zzz}
         **/
        Yii::import('common.models.ptc.*');
        $status = Yii::app()->request->getPost('status', '');
        $plan_id = Yii::app()->request->getPost('plan_id', 0);
        $item = ParentMeetingPlan::model()->findByPk($plan_id);

        if($item && !$item->items && $status == 'online') {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '未设置时间段');
            $this->showMessage();
        }
        $schoolid = $item->school_id;
        if ($status == "online" || "offline") {
            if ($schoolid == $this->branchId) {
                if ($status == "online") {
                    $item->status = ParentMeetingPlan::STATUS_ONLINE;
                } else {
                    $item->status = ParentMeetingPlan::STATUS_OFFLINE;
                }
            }
        }
        if ($item->save()) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('event', 'success'));
            $this->addMessage('data', array('id' => $item->id, 'status' => $item->status));
        } else {
            $this->addMessage('state', 'fail');
            $err = current($item->getErrors());
            $this->addMessage('message', $err[0]);
        }

		$this->showMessage();
    }

    /**
     * WIDA考试预约 2019-06-21
     * @param int $classid
     * @param int $semester
     */
    public function taskPtc($plan_id){
        Yii::import('common.models.ptc.*');
        $plan = ParentMeetingPlan::model()->with('items')->findByAttributes(array(
            'id' => $plan_id,
        ));
        if (!$this->checkPermission($plan)) {
            Yii::app()->controller->forward('/child/message/error');
            Yii::app()->end();
        }
        Yii::import('common.models.calendar.Calendar');
        Yii::import('common.models.regextrainfo.*');
        $calendarObj = Calendar::model()->findByPk($this->calendarId);
       /* $default_duration = 30;

        if($this->branchObj->type == 50){
            $default_duration = 90;
            $classModel = IvyClass::model()->findByPk($this->selectedClassId);
            if (in_array($classModel->classtype, array('e6', 'e7', 'e8', 'e9', 'e10', 'e11', 'e12'))){
                $default_duration = 120;
            }elseif (in_array($classModel->classtype, array('mk'))){
                $default_duration = 20;
            }elseif (in_array($classModel->classtype, array('mc','mt'))){
                $default_duration = 30;
            }
        }*/

        foreach($plan->timeslotExplode() as $_time){
            $timeslots[] = array('time'=>$_time);
        }

        $criteria = new CDbCriteria();
        $criteria->compare('planid', $plan->id);
        $criteria->order = "target_date ASC, timeslot ASC, meet_index ASC";
        $item = ParentMeetingItem::model()->findAll($criteria);
        $select_childid = array();
        $item_values = array();
        foreach($item as $k=>$v){
            $timelotarray = explode(",", $v['timeslot']);
            $timestart= $timelotarray[0];
            $timeend = $timelotarray[1];
            $timestrto = strtotime($v['target_date']);
            $time = date('Y-m-d', $timestrto);
            $startIndex = sprintf("%02d",$v->meet_index);
            $endIndex = sprintf("%02d",$v->meet_index);;
            $start = $time.'T'.$timestart;
            $end = $time.'T'.$timeend;
            $item_values[$v->uniKey()] = array(
                'childid' => $v->childid,
                'start' => $start . ":" . $startIndex,
                'end' => $end . ":" . $endIndex,
            );
            $targetDate[$v->target_date] = $v->target_date;
            if($v->childid)
                $select_childid[$v->childid] = $start.' - '.$timeend;
        }

        $events = array();
        $arr_childid = array_keys($select_childid);
        if($arr_childid){
            $criteria = new CDbCriteria();
            $criteria->compare('childid', $arr_childid);
            $criteria->index = 'childid';
            $items = ChildProfileBasic::model()->findAll($criteria);
        }
        foreach($item_values as $unikey => $value){
            $events[] = array(
                'id' => $unikey,
                'title' => isset($items[$value['childid']])?$items[$value['childid']]->getChildName():'',
                'start' => $value['start'],
                'end' => $value['end'],
                'color' => $value['childid']?'#3a87ad':'#257e4a',
            );
        }
        $month = 0;
        $currentMonth = date('Y-m');
        if($targetDate){
            asort($targetDate);
            $monthData = reset($targetDate);
            $month = date("Y-m", strtotime($monthData));
        }

        $selectMonth = ($month) ? $month : $currentMonth;
        //Yii::msg($selectMonth);

        $criter = new CDbCriteria();
        $criter->compare('planid', $plan->id);
        $item = ParentMeetingItemMemo::model()->findAll($criter);
        $arr_memo = array();
        foreach($item as $k => $v){
            $arr_memo[$v->childid] = $v->memo;
        }

        $taskData['select_childid'] = $select_childid;
        $taskData['arr_childid'] = $arr_childid;
        $taskData['arr_memo'] = $arr_memo;
        $taskData['selectMonth'] = $selectMonth;  // 日历插件默认月份
        $taskData['events'] = $events;
        $taskData['timeslots'] = $timeslots;
        $taskData['plan'] = $plan;
        $planInfo = json_decode($plan->extra, true);
        $taskData['plan_start'] = isset($planInfo['start_date']) ? $planInfo['start_date'] : '';
        $taskData['plan_end'] = isset($planInfo['end_date']) ? $planInfo['end_date'] : '';
        $taskData['children'] = $this->getNameListByPlan($plan_id);
        $taskData['completeConfig'] = RegExtraInfo::completeConfig();

        $send_time = time() - 86400;
        $criteria = new CDbCriteria();
        $criteria->compare('planid', $plan->id);
        $criteria->compare('childid', array_keys($taskData['children']));
        $criteria->compare('send_time', ">={$send_time}");
        $wxnotifPtcModel = WxnotifPtc::model()->findAll($criteria);
        $expired = array();
        if($wxnotifPtcModel){
            foreach ($wxnotifPtcModel as $item){
                $expired[$item->childid] = $item->childid;
            }
        }
        $taskData['expired'] = $expired;
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/phpfunc.js');

        return $taskData;
    }

    // 获取现在已经增加的学生  2019-06-26
    public function getNameListByPlan($planId)
    {
        $criteria = new CDbCriteria();
        $criteria->compare('planid', $planId);
        $criteria->index = 'childid';
        $planChildModel = PmeetPlanChild::model()->findAll($criteria);

        $childData = array();
        if($planChildModel){
            $criteria = new CDbCriteria();
            $criteria->compare('childid', array_keys($planChildModel));
            $criteria->index = 'childid';
            $childModel = ChildProfileBasic::model()->findAll($criteria);

            foreach ($childModel as $item)
            {
                $parents[$item->fid] = $item->fid;
                $parents[$item->mid] = $item->mid;
            }

            $parentOpenid = array();
            $parentinfo = array();
            Yii::import('common.models.wechat.*');
            $crit = new CDbCriteria();
            $crit->compare('userid', $parents);
            $crit->compare('valid', 1);
            if ($this->branchObj->type == 50) {
                $crit->compare('account', 'ds');
            } else {
                $crit->compare('account', 'ivy');
            }
            $crit->with = 'info';
            $wechatModel = WechatUser::model()->findAll($crit);

            foreach ($wechatModel as $val) {
                $parentOpenid[$val->userid] = 1;
                if($val->info){
                    $information = json_decode($val->info->info);
                    $parentinfo[$val->userid][] = array(
                        'name' => $information->nickname,
                        'headimgurl' => $information->headimgurl,
                    );
                }
            }

            foreach ($childModel as $child){
                $fidArray = array();
                $midArray = array();
                $bindingStatus = 0;
                if (isset($parentOpenid)) {
                    if ($parentOpenid[$child->fid] || $parentOpenid[$child->mid]) {
                        $bindingStatus = 1;
                    }
                }
                if ($child->fid && $parentinfo[$child->fid]) {
                    $fidArray = $parentinfo[$child->fid];
                }
                if ($child->mid && $parentinfo[$child->mid]) {
                    $midArray = $parentinfo[$child->mid];
                }

                $childData[$child->childid] = array(
                    'id' => $child->childid,
                    'name' => ( Yii::app()->language == 'en_us' )? $child->getChildName(null,null, true): $child->getChildName(true, true),
                    'photo' => $child->photo,
                    'ccUrl' => OA::genCCUrlHome($child->childid),
                    'bindingStatus' => $bindingStatus,
                    'parent' => array_merge($fidArray,$midArray),
                );
            }
        }

        return $childData;
    }

    // 删除学生预约  2019-06-27
    public function actionClearChild()
    {
        $unikey = Yii::app()->request->getParam('unikey', '');
        $arr = explode("_", $unikey);
        $planid = $arr[0];
        $days = $arr[1];
        $times = $arr[2];
        $meet_index = $arr[3];
        Yii::import('common.models.ptc.*');
        $model = ParentMeetingPlan::model()->findByPk($planid);
        if($model->school_id == $this->branchId) {
            $criter = new CDbCriteria();
            $criter->compare('planid', $planid);
            $criter->compare('target_date', $days);
            $criter->compare('timeslot', $times);
            $criter->compare('meet_index', $meet_index);
            $item = ParentMeetingItem::model()->find($criter);
            $childid = $item->childid;
            $item->childid = 0;
            $item->op_userid = 0;
            $item->op_timestamp = 0;
            if ($item->save()) {
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('global', 'Data deleted'));
                $this->addMessage('data', array('unikey' => $unikey, 'childid' => $childid));
                $this->addMessage('callback', 'cbClearChild');
            } else {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', 'Data Saving Failed!'));
            }
        }
        $this->showMessage();
    }

    /**
     * 管理一对一家长预约 发通知
     * @param int $classid
     * @param int $planid
     * @param int $childid
     */
    public function actionSendMessage()
    {
        $planid = Yii::app()->request->getParam('planid', '');
        $childid = Yii::app()->request->getParam('childid', '');
        Yii::import('common.models.ptc.*');
        Yii::import('common.models.wechat.*');
        if(Yii::app()->request->isAjaxRequest) {
            $this->accessToken = ($this->branchObj->type == 50) ? CommonUtils::getAccessToken('ds'): CommonUtils::getAccessToken('ivy');
            if($this->accessToken) {
                $yid = Branch::model()->getBranchInfo($this->branchId, 'schcalendar');
                $calendarModel = Calendar::model()->findByPk($yid);
                $endYear = $calendarModel->startyear + 1;
                $stratYear = $calendarModel->startyear . ' - ' . $endYear;

                $criteria = new CDbCriteria();
                $criteria->compare('t.id', $planid);
                $criteria->compare('t.semester', 100);
                $criteria->compare('t.status', 1);
                $criteria->with = 'items';
                $criteria->order = "items.target_date ASC";
                $planModel = ParentMeetingPlan::model()->find($criteria);
                $message = Yii::t('teaching', 'No available time slot found or PTC not activated.');
                $items = array();
                if ($planModel) {
                    foreach ($planModel->items as $val) {
                        $items[$val->target_date] = date("Y/m/d", strtotime($val->target_date));
                    }
                }

                if ($items) {
                    $message = Yii::t('teaching', 'Notification can be sent only once within 24 hours.');
                    $send_time = time() - 86400;

                    $criteria = new CDbCriteria();
                    $criteria->compare('classid', 0);
                    $criteria->compare('planid', $planid);
                    $criteria->compare('childid', $childid);
                    $criteria->compare('send_time', ">={$send_time}");
                    $wxnotifPtcModel = WxnotifPtc::model()->count($criteria);

                    if (!$wxnotifPtcModel) {
                        $childModel = ChildProfileBasic::model()->findByPk($childid);
                        $parent = array();
                        if ($childModel->fid) {
                            $parent['fid'] = $childModel->fid;
                        }
                        if ($childModel->mid) {
                            $parent['mid'] = $childModel->mid;
                        }
                        $model = new WxnotifPtc();
                        $model->childid = $childModel->childid;
                        $model->schoolid = $this->branchId;
                        $model->classid = 0;
                        $model->planid = $planid;
                        $model->send_time = time();
                        $model->updated_at = time();
                        $model->updated_by = Yii::app()->user->id;
                        if ($model->save()) {
                            $message = '成功，家长未绑定微信';
                            $data = array();
                            if ($parent) {
                                $criteria = new CDbCriteria();
                                $criteria->compare('userid', $parent);
                                if ($this->branchObj->type == 50) {
                                    $criteria->compare('account', 'ds');
                                } else {
                                    $criteria->compare('account', 'ivy');
                                }
                                $criteria->compare('valid', 1);
                                $wechatUserModel = WechatUser::model()->findAll($criteria);

                                if ($wechatUserModel) {
                                    $childModel = ChildProfileBasic::model()->findByPk($childid);

                                    $num = 0;
                                    foreach ($wechatUserModel as $val) {
                                        $modelItme = new Wxnotif();
                                        $modelItme->openid = $val->openid;
                                        $modelItme->wx_account = ($this->branchObj->type == 50) ? 'ds' : 'ivy';
                                        $modelItme->task_type = ($this->branchObj->type == 50) ? 'dsWida' : 'ivyWida';
                                        $modelItme->taskid = $model->id;
                                        $modelItme->notify_at = time();
                                        $modelItme->randtag = uniqid();
                                        $modelItme->updated_at = time();
                                        $modelItme->updated_by = Yii::app()->user->id;
                                        $modelItme->save();
                                        $status = $this->sendMessageData($model->childid,$this->branchObj->type, $this->branchObj->title, $val->openid, $modelItme, $items);
                                        $modelItme->notified = $status;
                                        $modelItme->save();
                                        if ($status == 0) {
                                            $num++;
                                            $information = array();
                                            if ($val->info) {
                                                $information = json_decode($val->info->info, true);
                                            }
                                            $data[$childModel->childid][] = array(
                                                'name' => ($information) ? $information['nickname'] : "",
                                                'headimgurl' => ($information) ? $information['headimgurl'] : "",
                                            );
                                        }
                                    }
                                    $message = '微信通知发送完成' . $num . '个';
                                }
                            }

                            $this->addMessage('state', 'success');
                            $this->addMessage('message', $message);
                            $this->addMessage('data', $data);
                            $this->showMessage();
                        }
                    } else {
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', $message);
                        $this->showMessage();
                    }
                } else {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', $message);
                    $this->showMessage();
                }
            }else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', 'token获取失败');
                $this->showMessage();
            }

        }
    }

    // 发送模板消息
    public function sendMessageData($childid,$schoolid, $school,$openid,$modelItme, $items)
    {
        $url = 'https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=' . $this->accessToken;
        // 微信模板消息内容

        if ($schoolid == 50) {
            $appid = 'wxb1a42b81111e29f3';
            $state = 'ds';
            $template_id = 'ugR_4diTW1M_c0vfy5LnFiVcK-iV6V40KjfttyFzb-k';
            $ceshiOpenid = 'oHBKPwQUsreFHcZkzsq0CdW7VqEQ';
            //$first = '亲爱的家长：您好！'.$stratYear. $season_cn . $school->title_cn.'学生主导的家长会议预约已经开启。Dear parent, the reservation for '.$stratYear. $season_en .' Student Led Conference is now open.';
            $first = '预约已开启。Reservation is now open.';
            $type = '请咨询班级老师或校方工作人员';
            $remark = '请点击“详情”进行预约。Please tap on "Details" to schedule you time.';
            $timeData = (strtotime(reset($items)) != strtotime(end($items))) ? reset($items) . ' - ' . date("Y/m/d", strtotime(end($items))) : reset($items);
        } else {
            $appid = 'wx903fba9d4709cf10';
            $state = 'ivy';
            $template_id = 'lcF9djd0KumgCKkT2XbFNf0SVs9_bkUsp2GocyMAMnA';
            $ceshiOpenid = 'ouwmTjiT6aIh_TLd6Kldr2Cx4tCs';
            $type = '请咨询班级老师或校方工作人员';
            $remark = '请点击“详情”进行时间段预约。';
            $timeData = (strtotime(reset($items)) != strtotime(end($items))) ? reset($items) . ' - ' . end($items) : reset($items);
        }

        $redirectUrl = "http://www.ivyonline.cn/wechat/wida/index/randtag/$modelItme->randtag";
        $touser  = (OA::isProduction()) ? $openid  : $ceshiOpenid;

        $data = array(
            'touser' => $touser,
            'template_id' => $template_id,
            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid='.$appid.'&redirect_uri='.$redirectUrl.'&response_type=code&scope=snsapi_base&state='.$state.'#wechat_redirect',
            'data' => array(
                'first' => array('value' => $first),
                'keyword1' => array('value' => $school),
                'keyword2' => array('value' => $timeData),
                'remark' => array('value' => $remark, 'color' => '#ff6726'),
            ),
        );
        $jsonData = json_encode($data);
        $res = CommonUtils::httpGet($url, $jsonData);
        $jsonRes = CJSON::decode($res);

        Yii::log($state.'=>'. $childid .'=>'.$openid.'=>'.$jsonRes['errcode'], CLogger::LEVEL_INFO, 'wechatptc.teacher');
        return $jsonRes['errcode'];
    }

    public function getQrCodeUrl($id) 
    {
        if (in_array($this->branchId, CommonUtils::dsSchoolList())) {
            $appid = 'wxb1a42b81111e29f3';
            $state = 'ds';
        } else {
            $appid = 'wx903fba9d4709cf10';
            $state = 'ivy';
        }
        $redirectUrl = "https://wx.daystaracademy.cn/parentSchedule/" . $id;
        return 'https://open.weixin.qq.com/connect/oauth2/authorize?appid='.$appid.'&redirect_uri='.$redirectUrl.'&response_type=code&scope=snsapi_base&state='.$state.'#wechat_redirect';
    }

    // 增加默认时长 2019-06-21
    public function actionSaveDuration()
    {
        $duration = Yii::app()->request->getParam('duration', 0);
        $plan_id = Yii::app()->request->getParam('plan_id', 0);
        if($plan_id && $duration){
            Yii::import('common.models.ptc.*');
            $item = ParentMeetingPlan::model()->findByPk($plan_id);
            $item->default_duration = $duration;
            if($item->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', 'Data Saved!'));
            }
            else{
                $this->addMessage('state', 'fail');
                $err = current($item->getErrors());
                $this->addMessage('message', $err[0]);
            }
        }
        else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('teaching', 'PTC time cannot be blank'));
        }
		$this->showMessage();
    }

    // 增加开始时间 2019-06-21
    public function actionSaveTimeslot()
    {
        $plan_id = Yii::app()->request->getParam('plan_id', 0);
        $hour = Yii::app()->request->getParam('hour', 0);
        $minute = Yii::app()->request->getParam('minute', 0);
        if($plan_id && $hour && $minute){
            Yii::import('common.models.ptc.*');
            $item = ParentMeetingPlan::model()->findByPk($plan_id);
            $character = $item->timeslot_starts;
            if($character){
                $array = explode(",", $character);
                foreach($array as $k => $v){
                    $arr[$v] = $v;
                }
            }
            $newtimeslot = $hour.':'.$minute;
            if(!in_array($newtimeslot, $arr)){
                $arr[$newtimeslot] = $newtimeslot;
                sort($arr);
                $time_starts = implode(",", $arr);
                $item->timeslot_starts = $time_starts;
                if($item->save()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message', 'Data Saved!'));
                    $this->addMessage('data', array('time'=>$newtimeslot));
                }
                else{
                    $this->addMessage('state', 'fail');
                    $err = current($item->getErrors());
                    $this->addMessage('message', $err[0]);
                }
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', 'Data existed!'));
            }
        }
        else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('teaching', 'PTC time cannot be blank'));
        }
		$this->showMessage();
    }

    // 增加时间段 2019-06-26
    public function actionOpenTimeslot()
    {
        $planid = Yii::app()->request->getPost('planid', 0);
        $days = Yii::app()->request->getPost('days', array());
        $timeslot = Yii::app()->request->getPost('timeslot', '');

        Yii::import('common.models.ptc.*');
        if(Yii::app()->request->isAjaxRequest) {
            $planModel = ParentMeetingPlan::model()->findByPk($planid);
            if(!$planModel->default_duration){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '请先增加默认时长');
                $this->showMessage();
            }
            $times = $this->timeslot($planid, $timeslot);
            foreach ($days as $k1 => $v1) {
                $iArray = array();
                for ($i = 0; $i < 1; $i++) {
                    $iArray[] = $i;
                    $model = new ParentMeetingItem;
                    $model->planid = $planid;
                    $model->target_date = date('Ymd', $v1);
                    $model->timeslot = $times;
                    $model->meet_index = $i;
                    $model->stem = 0;
                    $model->save();
                }
            }

            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('event','success'));
            $this->addMessage('data', $iArray);
            $this->showMessage();
        }
        $this->addMessage('state', 'fail');
        $this->addMessage('message', '非法操作');
        $this->showMessage();
    }

    // 增加时间段里面的一个方法 2019-06-26
    public function timeslot($planid,$timeslot)
    {
        $criter = new CDbCriteria();
        $criter->compare('id', $planid);
        $item = ParentMeetingPlan::model()->find($criter);

        $duration = $item->default_duration;
        $timearray = explode(":", $timeslot);
        $hour = $timearray[0];
        $minute = $timearray[1];
        $newminute = $minute + $duration;
        if ($newminute) {
            $hour = floor($newminute / 60) + $hour;
            if ($hour < 10) {
                $hour = '0' . $hour;
            }
            $newminute = fmod($newminute, 60);
            if ($newminute == 0) {
                $newminute = '00';
            } elseif ($newminute == 5) {
                $newminute = '05';
            }
        }
        $endtime = $hour . ":" . $newminute;
        $times = $timeslot . ',' . $endtime;

        return $times;
    }

    public function actionDelTimeslot()
    {
        $plan_id = Yii::app()->request->getParam('plan_id', 0);
        $time = Yii::app()->request->getParam('time', 0);
        if($plan_id && $time){
            Yii::import('common.models.ptc.*');
            $item = ParentMeetingPlan::model()->findByPk($plan_id);
            // 查找是否添加
            $timeArr = explode(':', $time);
            $hour = $timeArr[0];
            $min = $timeArr[1] + $item->default_duration;
            if ($min >= 60) {
                $hour += (int) ($min / 60); // 计算进位的小时数
                $min = $min % 60;       // 计算剩余的分钟数
            }
            $hour =  str_pad($hour, 2, "0", STR_PAD_LEFT);
            $min =  str_pad($min, 2, "0", STR_PAD_LEFT);
            $newTime = sprintf('%s:%s', $hour, $min);
           
            $crit = new CDbCriteria();
            $crit->compare('planid', $plan_id);
            $crit->compare('timeslot', sprintf("%s,%s", $time, $newTime));
            $count = ParentMeetingItem::model()->count($crit);
            if ($count > 0) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '请先删除右侧已添加的时间数据');
                $this->showMessage();
            }

            $character = $item->timeslot_starts;
            $array = explode(",", $character);
            $arr = array();
            foreach($array as $k => $v){
                $arr[$v] = $v;
            }
            unset($arr[$time]);
            $time_starts = implode(",", $arr);
            $item->timeslot_starts = $time_starts;
            if($item->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('global', 'Data deleted'));
            }
            else{
                $this->addMessage('state', 'fail');
                $err = current($item->getErrors());
                $this->addMessage('message', $err[0]);
            }
        }
        $this->showMessage();
    }

    public function actionSaveTimeslotwithDay()
    {
        if($this->checkTeachers()){
            $unikey_del = array();
            $unikey_new = array();
            $planid = Yii::app()->request->getParam('planid', 0);
            $timeslot = Yii::app()->request->getParam('timeslot', array());
            $days = Yii::app()->request->getParam('days', array());
            Yii::import('common.models.ptc.*');
            foreach($days as $k=>$v){
                $arr[] = date('Ymd', $v);
            }
            $criter = new CDbCriteria();
            $criter->compare('planid', $planid);
            $criter->compare('target_date', $arr);
            $des = ParentMeetingItem::model()->findAll($criter);
            foreach($des as $k=>$v){
                if($v->childid == 0){
                    $unikey_del[] = $v->uniKey();
                    $v->delete();
                }
            }

            $success = 0;
            $fail = 0;
            $criter = new CDbCriteria();
            $criter->compare('id', $planid);
            $item = ParentMeetingPlan::model()->find($criter);
            $duration = $item->default_duration;
            foreach($days as $k1 => $v1){
                foreach($timeslot as $k2 => $v2){
                    $timearray = explode(":", $v2);
                    $hour= $timearray[0];
                    $minute = $timearray[1];
                    $newminute = $minute + $duration;
                    if($newminute){
                        $hour = floor($newminute/60) + $hour;
                        if($hour < 10){
                            $hour = '0'.$hour;
                        }
                        $newminute = fmod($newminute, 60);
                        if($newminute == 0){
                            $newminute = '00';
                        }
                        elseif($newminute == 5){
                            $newminute = '05';
                        }
                    }
                    $endtime = $hour.":".$newminute;
                    $times = $v2.','.$endtime;
                    $criter = new CDbCriteria();
                    $criter->compare('planid', $planid);
                    $criter->compare('target_date', date('Ymd', $v1));
                    $criter->compare('timeslot', $times);
                    $model = ParentMeetingItem::model()->find($criter);
                    if($model == null){
                        $model = new ParentMeetingItem;
                    }
                    if($model->isNewRecord){
                        $model->planid = $planid;
                        $model->target_date = date('Ymd', $v1);
                        $model->timeslot = $times;
                        $model->stem = 0;
                        $timelotarray = explode(",", $times);
                        $timestart= $timelotarray[0];
                        $timeend = $timelotarray[1];
                        $time = date('Y-m-d', $v1);
                        $start = $time.'T'.$timestart;
                        $end = $time.'T'.$timeend;
                        if($model->save()){
                            $unikey_new[] = array(
                                'id' => $model->uniKey(),
                                'start' => $start,
                                'end' => $end
                            );
                            $success = $success + 1;
                        }
                        else{
                            $fail = $fail + 1;
                        }
                    }
                }
            }
            if($fail){
                $this->addMessage('state', 'fail');
                $this->addMessage('message',
                    Yii::t('teaching', ':failNum items saving failed', array(':failNum'=>$fail)));
            }
            else{
                $this->addMessage('state', 'success');
                $this->addMessage('message',
                    Yii::t('teaching', ':failNum items saving success', array(':successNum'=>$success)));
                $this->addMessage('data', array('delTimeslot' => $unikey_del, 'newTimeslot' => $unikey_new));
                $this->addMessage('callback', 'cbChooseTimeslot');
            }
        }
        else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', 'System Error!'));
        }
		$this->showMessage();
	}

    protected function getRecentMonth($m='', $c=3, $d=2)
    {
		$con = explode('-', $m);
		$year = $con[0];
		$month = $con[1];

		$day = 1;
		$et[] = $m;
		for($i=1; $i<$c; $i++){
			$month -= 1;
			if($month == 0){
				$month = 12;
				$year -= 1;
				$et[] = date("Y-m", mktime(0,0,0,$month,$day,$year));
			}else{
			$et[] = date("Y-m", mktime(0,0,0,$month,$day,$year));
			}
		}
		$month += $c-1;
		for($i=0; $i<$d; $i++){
			$month += 1;
			if($month == 12){
				$month = 1;
				$year += 1;
				$et[] = date("Y-m", mktime(0,0,0,$month,$day,$year));
			}else{
			$et[] = date("Y-m", mktime(0,0,0,$month,$day,$year));
			}
		}
		sort($et);
		return $et;
	}

    public function actionSaveTimeslotwithItem()
    {
        $unikey = Yii::app()->request->getParam('unikey', '');
        $childid = Yii::app()->request->getParam('childid', 0);
        $endtime = Yii::app()->request->getParam('endtime', array());
        $memo = Yii::app()->request->getParam('memo', '');

        if(!$childid){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '孩子不能为空');
            $this->showMessage();
        }
        $model = ChildProfileBasic::model()->findByPk($childid);

        $op_timestamp = time();
        $arr = explode("_", $unikey);
        $planid = $arr[0];
        $days = $arr[1];
        $times = $arr[2];
        $meet_index = $arr[3];
        $timearr = explode(",", $times);
        $timestart = $timearr[0];
        $timeend = $endtime['hour'].':'.$endtime['minute'];
        /*if($this->branchObj->type == 50){
            $timeend = $timearr[1];
        }*/
        //Yii::msg($timeend);
        $timeslot = $timestart.','.$timeend;
        $ymd = date('Y-m-d', strtotime($days));
        $ymd_times = $ymd.'T'.$timestart.' - '.$timeend;
        Yii::import('common.models.ptc.*');
        $criter = new CDbCriteria();
        $criter->compare('planid', $planid);
        $criter->compare('target_date', $days);
        $criter->compare('timeslot', $times);
        $criter->compare('meet_index', $meet_index);
        $item = ParentMeetingItem::model()->find($criter);
        $model_schoolid = ParentMeetingPlan::model()->findByPk($planid);
        $models = ChildProfileBasic::model()->findByPk($item->childid);
        if($model_schoolid->school_id == $this->branchId){
            if($item->childid ==0 || $item->childid == $childid){
                $item->childid = $childid;
                $item->timeslot = $timeslot;
                $item->op_timestamp = ($childid) ? $op_timestamp : 0;
                $item->op_userid = ($childid) ? Yii::app()->user->getId() : 0;
                $item->stem = ($childid) ? (($item->stem) ? $item->stem : 3): 0;
                $criteria = new CDbCriteria();
                $criteria->compare('planid', $planid);
                $criteria->compare('childid', $childid);
                $itemmemo = ParentMeetingItemMemo::model()->find($criteria);
                if($itemmemo == null){
                    $itemmemo = new ParentMeetingItemMemo;
                }
                $itemmemo->planid = $planid;
                $itemmemo->childid = $childid;
                $itemmemo->memo = $memo;
                if($itemmemo->save()){
                    if($item->save()){
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', Yii::t('message', 'Data Saved!'));
                        $this->addMessage('data',
                            array('childid' => $childid, 'childName' => ($model) ? $model->getChildName() : "", 'unikey' => $unikey, 'memo' => $memo, 'childTimeslot' => $ymd_times));
                        $this->addMessage('callback', 'cbChooseChild');
                    }
                    else{
                        $this->addMessage('state', 'fail');
                        $err = current($item->getErrors());
                        $this->addMessage('message', $err[0]);
                    }
                }
                else{
                    $this->addMessage('state', 'fail');
                    $err = current($itemmemo->getErrors());
                    $this->addMessage('message', $err[0]);
                }
            }else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('teaching', 'Sorry, this slot is no longer available.'));
                $this->addMessage('data', array('childid' => '', 'childName' => '', 'unikey' => $unikey));
            }
        }
		$this->showMessage();
    }

    /**
     * @TODO 返回某个时间段的详细信息
     * @params unikey [planid_target_date_timeslot]
     * @return {endtimeh: x, endtimem: x, childid: x, memo: x}
     */
    public function actionGetTimeslotInfo()
    {
		$unikey = Yii::app()->request->getParam('unikey', '');
		$arr = explode("_", $unikey);
		$planid = $arr[0];
		$days = $arr[1];
		$times = $arr[2];
        $meet_index = $arr[3];
		$timearr = explode(",", $times);
		Yii::import('common.models.ptc.*');
		$criter = new CDbCriteria();
		$criter->compare('planid', $planid);
		$criter->compare('target_date', $days);
		$criter->compare('timeslot', $timearr[0].',', true);
		$criter->compare('meet_index', $meet_index);
		$item = ParentMeetingItem::model()->find($criter);
		$criteria = new CDbCriteria();
		$criteria->compare('planid', $planid);
		$criteria->compare('childid', $item->childid);
		$itemmemo = ParentMeetingItemMemo::model()->find($criteria);
		$timearr = explode(",", $item->timeslot);
		$timeend = $timearr[1];
		$endtimeh = explode(":", $timeend);
		$arr_json['endtimeh'] = $endtimeh[0];
		$arr_json['endtimem'] = $endtimeh[1];
		$arr_json['childid'] = $item->childid;
		$arr_json['memo'] = $itemmemo->memo;
		echo CJSON::encode($arr_json);
    }

    /**
     * @TODO 返回某天的预约信息
     * @params planid, target_date
     * @return {timeslot_s: 开始时间点}
     */
    public function actionGetDayInfo()
    {
		$planid = Yii::app()->request->getParam('planid', 0);
		$days = Yii::app()->request->getParam('target_date', 0);
		$arr = explode("/", $days);
		if($arr[1] < 10){
			$arr[1] = '0'.$arr[1];
		}
		if($arr[2] < 10){
			$arr[2] = '0'.$arr[2];
		}
		$target_date = implode("", $arr);
		Yii::import('common.models.ptc.*');
		$criter = new CDbCriteria();
		$criter->compare('planid', $planid);
		$criter->compare('target_date', $target_date);
		$item = ParentMeetingItem::model()->findAll($criter);
		foreach($item as $k => $v){
			$arr_childid[] = $v->childid;
			$timeslot = $v->timeslot;
			$arr_s = explode(",", $timeslot);
			$timeslot_s[] = Array(
			'timeslot_s' => $arr_s[0],
			'childid' => $v->childid
			);
		}
		if($timeslot_s){
			$criteria = new CDbCriteria();
			$criteria->compare('childid', $arr_childid);
			$criteria->index = 'childid';
			$items = ChildProfileBasic::model()->findAll($criteria);
			foreach($timeslot_s as $key => $v1){
				$events[] = array(
					'childid' => $v1['childid'],
					'childName' => isset($items[$v1['childid']])?$items[$v1['childid']]->getChildName():'',
					'timeslot_s' => $v1['timeslot_s']
				);
			}
		}
		echo CJSON::encode($events);
    }

    /**
     * @TODO 返回某天的预约信息 DS
     * @params planid, target_date
     * @return {timeslot_s: 开始时间点}
     */
    public function actionGetDayInfoDS()
    {
        $planid = Yii::app()->request->getParam('planid', 0);
        $days = Yii::app()->request->getParam('target_date', '');
        $arr = explode("/", $days);
        if($arr[1] < 10){
            $arr[1] = '0'.$arr[1];
        }
        if($arr[2] < 10){
            $arr[2] = '0'.$arr[2];
        }
        $target_date = implode("", $arr);
        Yii::import('common.models.ptc.*');
        $criter = new CDbCriteria();
        $criter->compare('planid', $planid);
        $criter->compare('target_date', $target_date);
        $item = ParentMeetingItem::model()->findAll($criter);
        foreach($item as $k => $v){
            $arr_childid[] = $v->childid;
            $timeslot = $v->timeslot;
            $arr_s = explode(",", $timeslot);
            $timeslot_s[] = Array(
                'timeslot_s' => $arr_s[0],
                'childid' => $v->childid,
                'meet_index' => $v->meet_index
            );
        }
        if($timeslot_s){
            $criteria = new CDbCriteria();
            $criteria->compare('childid', $arr_childid);
            $criteria->index = 'childid';
            $items = ChildProfileBasic::model()->findAll($criteria);
            foreach($timeslot_s as $key => $v1){
                $events[$v1['timeslot_s']][] = array(
                    'childid' => $v1['childid'],
                    'childName' => isset($items[$v1['childid']])?$items[$v1['childid']]->getChildName():'',
                    'timeslot_s' => $v1['timeslot_s'],
                    'meet_index' => $v1['meet_index']
                );
            }
        }
        echo CJSON::encode($events);
    }

    public function actionCancelTimeslot()
    {
        $planid = Yii::app()->request->getPost('planid', 0);
        $days = Yii::app()->request->getPost('days', array());
        $timeslot = Yii::app()->request->getPost('timeslot', '');
        Yii::import('common.models.ptc.*');

        if(Yii::app()->request->isAjaxRequest) {
            $dataYmd = array();
            foreach ($days as $val){
                $dataYmd[] = date("Ymd", $val);
            }
            $times = $this->timeslot($planid, $timeslot);

            foreach ($dataYmd as $_date) {
                if($planid && $_date && $times) {
                    $sql = "delete from pmeet_plan_item where planid=".$planid." and target_date='".$_date."' and timeslot='".$times."'";
                    $rs = Yii::app()->subdb->createCommand($sql)->query();
                }
            }

            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('event','success'));
            $this->addMessage('data', '');
            $this->showMessage();
        }else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '非法操作');
            $this->showMessage();
        }
    }

    public function actionPrintPtc()
    {
        $this->layout='//layouts/print';
        $this->printFW = $this->branchObj->getPrintHeader();
        Yii::import('common.models.ptc.*');
        $id = Yii::app()->request->getParam('id', 0);

        $planModel = ParentMeetingPlan::model()->findByPk($id);
        $title = '';
        if($planModel){
            $extra = json_decode($planModel->extra);
            $title = Yii::app()->language == 'zh_cn' ? $extra->title_cn : $extra->title_en;
        }

        $criteria = new CDbCriteria();
        $criteria->compare('planid', $id);
        $criteria->order  = 'target_date ASC';
        $item = ParentMeetingItem::model()->findAll($criteria);
        $dataArr = array();
        $events = array();
        if($item) {
            $select_childid = array();
            $item_values = array();
            foreach ($item as $k => $v) {
                $timelotarray = explode(",", $v['timeslot']);
                $item_values[$v->target_date][] = array(
                    'childid' => $v->childid,
                    'start' => $timelotarray[0],
                    'end' => $timelotarray[1]
                );
                if ($v->childid)
                    $select_childid[$v->childid] = $v->childid;
            }
            $items = array();
            if($select_childid){
                $criteria = new CDbCriteria();
                $criteria->compare('childid', $select_childid);
                $criteria->index = 'childid';
                $items = ChildProfileBasic::model()->findAll($criteria);
            }

            foreach ($item_values as $unikey => $value) {
                foreach ($value as $childName) {
                    $events[$unikey][] = array(
                        'title' => (isset($items) && isset($items[$childName['childid']])) ? $items[$childName['childid']]->getChildName() : '',
                        'start' => $childName['start'],
                        'end' => $childName['end']
                    );
                }
            }

            if($events) {
                $month = $this->getDateFromRange(reset(array_keys($events)), end(array_keys($events)));

                $array = array();
                $k = 0;
                $oldM = 0;

                // 根据开始时间和结束时间 弄成以月分隔的数组$array[自增下标][时间] = 周几 0-6
                foreach ($month as $val) {
                    $w = date("w", strtotime($val));
                    $m = date("m", strtotime($val));
                    if ($m != $oldM) {
                        $k += 1;
                        $oldM = $m;
                    }
                    $array[$k][$val] = $w;
                }


                $calendar = array();
                $key = 0;
                // 把$array里面每月的数组在以每周7天来分隔数组
                foreach ($array as $val) {
                    $num = 0;
                    foreach ($val as $d => $month) {
                        $keysa = date("Y-m", strtotime($d));
                        $calendar[$keysa][$num][] = $d;
                        if ($month == 6) {
                            $num += 1;
                        }
                    }
                    $key++;
                }

                //  以周的数组判断是否7个  前后补齐
                foreach ($calendar as $key => $val) {
                    foreach ($val as $k => $itemas) {
                        if (count($itemas) < 7) {
                            $star = strtotime(reset($itemas));
                            $end = strtotime(end($itemas));
                            $starmonthDate = date('w', $star);
                            $starmonthDay = date('d', $star);
                            $endmonthDate = date('w', $end);
                            $endmonthDay = date('m', $end);
                            // 补齐前面
                            if ($starmonthDate > 0) {
                                $before = $star - 86400;
                                for ($i = 0; $i < $starmonthDate; $i++) {
                                    if ($starmonthDay < 2) {
                                        array_unshift($itemas, 0);
                                    } else {
                                        $beforeTime = date("Ymd", $before);
                                        array_unshift($itemas, $beforeTime);
                                        $before -= 86400;
                                    }
                                }
                            }

                            // 补齐后面
                            if ($endmonthDate < 6) {
                                $after = $end + 86400;
                                for ($i = $endmonthDate; $i < 6; $i++) {
                                    $NewEndmonthDate = date('m', $after);
                                    if ($endmonthDay != $NewEndmonthDate) {
                                        array_push($itemas, 0);
                                    } else {
                                        $afterTime = date("Ymd", $after);
                                        array_push($itemas, $afterTime);
                                        $after += 86400;
                                    }
                                }
                            }
                        }

                        $dataArr[$key][$k] = $itemas;
                    }
                }
            }
        }

        $this->render('printptc', array('events'=>$events, 'dataArr' => $dataArr, 'title' => $title));
    }

    function getDateFromRange($startdate, $enddate){
        $stimestamp = strtotime($startdate);
        $etimestamp = strtotime($enddate);
        // 计算日期段内有多少天
        $days = ($etimestamp-$stimestamp)/86400+1;
        // 保存每天日期
        $date = array();
        for($i=0; $i<$days; $i++){
            $date[] = date('Ymd', $stimestamp+(86400*$i));
        }
        return $date;
    }

}
