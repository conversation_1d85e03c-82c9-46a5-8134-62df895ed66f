<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('default/index')); ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Routines'), array('//mcampus/default/index')) ?></li>
        <?php if(Yii::app()->params['siteFlag'] == 'daystar'){?>
            <li><?php echo CHtml::link(Yii::t('site', 'Student Attendance Management'), array('//mcampus/childsign/index2')) ?></li>
        <?php }else{?>
            <li><?php echo CHtml::link(Yii::t('site', 'Student Attendance Management'), array('//mcampus/childsign/index')) ?></li>
        <?php }?>
        <li class="active"><?php echo Yii::t('campus', 'Student Attendance'); ?></li>
    </ol>
    <div class="row">
        <?php
        $this->renderPartial('/childsign/left');
        ?>
        <div class="col-md-10">
            <div class="mb10 row">
                <!-- 搜索框 -->
                <form action="<?php echo $this->createUrl('signlog'); ?>" method="get">
                    <?php echo Chtml::hiddenField('branchId', $this->branchId); ?>
                    <div class="col-md-8 row">
                        <div class="form-group col-sm-3">
                            <?php echo Chtml::dropDownList('class', $_GET['class'], $classAry, array('class' => 'form-control', 'empty' => Yii::t('campus', 'All classes'))); ?>
                        </div>
                        <div class="form-group col-sm-3">
                            <?php echo Chtml::dropDownList('date', $date, $timeList, array('class' => 'form-control')); ?>
                        </div>
                        <div class="form-group col-sm-1">
                            <button class="btn btn-default" type="submit"><span class="glyphicon glyphicon-search"></span></button>
                        </div>
                        <?php if (Yii::app()->request->getParam('vacation_day', '')) { ?>
                            <a class="btn btn-info" href="<?php echo $this->createUrl('exporyVacation', array('type' => $_GET['type'], 'vacation_day' => Yii::app()->request->getParam('vacation_day', ''), 'search' => $_GET['search'])); ?>">导出</a>
                        <?php } ?>
                    </div>
                </form>
            </div>
            <?php
            foreach ($classAry as $classid => $classtitle) {
            ?>
                <?php if ($data[$classid]): ?>
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4><?php echo $classtitle; ?> </h4>
                    </div>
                    <div class="table-responsive" style="overflow-y: hidden;overflow-x: auto;">
                        <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th><?php echo Yii::t('safety', 'Student Name') ?></th>
                                <th width="200"><?php echo Yii::t('global', 'Action') ?></th>
                                <th width="200"><?php echo Yii::t('campus', 'Staff Name') ?></th>
                                <th width="200"><?php echo Yii::t('campus', 'Time') ?></th>
                            </tr>
                        </thead>
                            <?php foreach($data[$classid] as $item): ?>
                                <tr>
                                    <td><?php echo $item['childname']; ?></td>
                                    <td><?php echo $item['status']; ?></td>
                                    <td><?php echo $item['username']; ?></td>
                                    <td><?php echo $item['update']; ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </table>
                    </div>
                </div>
                <?php endif; ?>
            <?php }; ?>
        </div>
    </div>

    <!-- 显示底部学校选择栏 -->
    <?php $this->renderPartial('//layouts/common/branchSelectBottom'); ?>