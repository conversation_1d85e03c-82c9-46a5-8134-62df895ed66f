{"name": "sebastian/environment", "description": "Provides functionality to handle HHVM/PHP environments", "keywords": ["environment", "hhvm", "xdebug"], "homepage": "http://www.github.com/sebastianbergmann/environment", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8 || ^5.0"}, "autoload": {"classmap": ["src/"]}, "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}}