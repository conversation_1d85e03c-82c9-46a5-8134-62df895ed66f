<div class="container-fluid">
    <!-- 面包屑导航 -->
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Teaching Tasks'), array('//mteaching/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('g<PERSON><PERSON><PERSON>','Assign Reviewer');?></li>

    </ol>
    <div class="row">
        <!-- 左侧菜单栏 -->
        <div class="col-md-2">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->getReportMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
            ));
            ?>
        </div>
        <div class="col-md-2">
            <ul class="nav nav-pills nav-stacked background-gray">
                <?php
                foreach ($branches as $branch) {
                    $href = $this->createUrl('branch', array('branchId' => $branch->branchid));
                    $active = '';
                    if ($branch->branchid == $branchId) {
                        $active = 'active';
                    }
                    echo "<li class='{$active}'><a href='{$href}'>";
                    echo $branch->title;
                    echo '</a></li>';
                }
                ?>
            </ul>
        </div>
        <div class="col-md-8">
            <p >
                <button class="btn btn-primary J_modal" onclick="addteacher()">增加老师</button>
            </p>
            <div class="panel panel-default">
                <div class="panel-heading collapsed_Parents" role="tab" id="1401">
                    <div class="" data-toggle="collapse" style="cursor: pointer" data-parent="#accordion" href="#col-1401" aria-expanded="false" aria-controls="col-1401">审核老师列表
                    </div>
                </div>
                <div class="panel-collapse children_Class_SignIn collapse in" role="tabpanel" style="">
                    <div class="panel-body">
                        <div class="row unsign" style="border-left:5px solid #f0ad4e">
                            <?php if($srportModel){ ?>
                                <?php foreach ($srportModel as $teacher): ?>
                                <div class="col-lg-2 col-md-3 col-sm-4 col-xs-6 text-center">
                                    <label class="text-center" style="height: 140px !important;">
                                        <p>
                                            <img class="img-responsive img-rounded" style='margin:auto' src="<?php echo CommonUtils::childPhotoUrl('blank.gif'); ?>">
                                        </p>
                                        <div class="dropdown dropup">
                                            <button class="btn btn-default dropdown-toggle thebtn" type="button" id="dropdownMenu-<?php echo $teacher->uid ?>" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-studentname="<?php echo $teacher->uid ?>">
                                                <span class="caret"></span>
                                                <?php echo $teacherModel[$teacher->uid]->getNameLang() ?>
                                            </button>
                                            <ul class="dropdown-menu" aria-labelledby="dropdownMenu-<?php echo $teacher->uid ?>">
                                                <li><a class="J_ajax_del" href="<?php echo $this->createUrl('delTeacher', array('id' => $teacher->id)); ?>">删除</a></li>
                                            </ul>
                                        </div>
                                    </label>
                                </div>
                                <?php endforeach; ?>
                            <?php }else{ ?>
                                <div class='p10'>暂无审核老师</div>
                            <?php }?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modal" tabindex="-1" role="dialog" aria-labelledby="modal">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="exampleModalLabel"><?php echo $model->id ? '更新：' : '添加：'; ?></h4>
            </div>
            <?php $form = $this->beginWidget('CActiveForm', array(
                'id' => 'course-forms',
                'enableAjaxValidation' => false,
                'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form'),
                'action' => $this->createUrl('addTeacher', array('branchId' => $branchId)),
            )); ?>
            <div class="modal-body">
                <div class="form-group">
                    <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'uid'); ?></label>
                    <div class="col-xs-9">
                        <?php $this->widget('ext.search.StaffSearchBox', array(
                            'acInputCSS' => 'form-control',
                            'htmlOptions' => array('class'=>'form-control'),
                            'data' => array(),
                            'useModel' => true,
                            'model' => $model,
                            'attribute' => 'uid',
                            'allowMultiple' => false,
                            'withAlumni' => false,
                        )) ?>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
                <button type="button" class="btn btn-default"
                        data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
            </div>
            <?php $this->endWidget(); ?>
        </div>
    </div>
</div>
<script>
    function addteacher() {
        $("#SreportCheck_uid").empty();
        $('#modal').modal({backdrop: 'static', keyboard: false});
    }
</script>

