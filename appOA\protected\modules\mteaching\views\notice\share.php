
<div >
    <div v-if='shareType=="view"' style='margin-bottom:40px'><div class="alert alert-warning p10" role="alert"><?php echo Yii::t("reg", "This Qrcode is for internal preview, DO NOT share to parents.");?></div></div>
    <div class='relative pb20 mt20'>
        <div class='Options'>
            <div><label><?php echo Yii::t("reg", "Options");?></label></div>
            <div class='mb20'>
                <div class="checkbox">
                    <label>
                        <input type="checkbox" id="inlineCheckbox1" v-model='showSummary'> <?php echo Yii::t("reg", "Dispaly abstract");?>
                    </label>
                </div>
                <div class="checkbox">
                    <label>
                        <input type="checkbox" id="inlineCheckbox2" v-model='showPublisher'> <?php echo Yii::t("reg", "Display name and title");?>
                    </label>
                </div>
            </div>
            <p style='color:#F0AD4E' class='font14 flex'><span class='glyphicon glyphicon-info-sign mr5 pt2'></span><span class='flex1'><?php echo Yii::t("reg", "Please use screenshot tool to capture the picture.");?></span></p>
        </div>
        <div  class='Thumbnail'>
            <div :class='htmlContent==1?"checkImg":""' @click='htmlContent=1' class='mb20'>
                <img v-if='branchId=="BJ_IASLT" || branchId=="BJ_OE"' src='https://m2.files.ivykids.cn/cloud01-file-8025768FgIuTtiV4P2nyl6AtJ0H9Ytl4r2S.png'  alt="">
                <img v-else src="http://m2.files.ivykids.cn/cloud01-file-8025768Fg8bgQx14VrnmXf8IrtwtyklfFzl.png"  >
            </div>
            <div :class='htmlContent==2?"checkImg":""' @click='htmlContent=2' class='mb20'>
                <img v-if='branchId=="BJ_IASLT" || branchId=="BJ_OE"' src='https://m2.files.ivykids.cn/cloud01-file-8025768FjYIqR2OoiDvE6N2pWfGaZY6obUe.png'  alt="">
                <img v-else src="http://m2.files.ivykids.cn/cloud01-file-8025768Fr7k8-P1oL2U-JqFgI6PtmxP_yMH.png"  >
            </div>
            <div :class='htmlContent==3?"checkImg":""' @click='htmlContent=3' class='mb20'>
                <img v-if='branchId=="BJ_IASLT" || branchId=="BJ_OE"' src='https://m2.files.ivykids.cn/cloud01-file-8025768FlcKj3RfXYELVgH0zlo8vvvB8mDA.png'  alt="">
                <img v-else src="http://m2.files.ivykids.cn/cloud01-file-8025768FlMdkoEGxPVV5KYftp57KaVsUIMD.png"  >
            </div>
        </div>
        <div  class='shareContent' :style="branchId == 'BJ_IASLT' || branchId == 'BJ_OE'?'background: #42B36A':'background: #428BCA'" ref='shareContent1' v-if='htmlContent==1'>
            <div class='bgPto relative' :style="{backgroundImage: branchId == 'BJ_IASLT' || branchId == 'BJ_OE' ? 'url(https://m2.files.ivykids.cn/cloud01-file-8025768Fta3ITlMZF7Y3pZzb2jtYCmb__ED.png)' : 'url(http://m2.files.ivykids.cn/cloud01-file-8025768FsL-G-mcYYSw5mn_EmjZHNBW2XXo.png)'}" >
                <div class='shareStatus'>
                    <img v-if='shareList.level==1' style='width:88px' src="http://m2.files.ivykids.cn/cloud01-file-8025768FlypAT0B8zLKuO9DX53cRZknTZ4n.png" alt="">
                    <img v-if='shareList.level==2' style='width:88px' src="http://m2.files.ivykids.cn/cloud01-file-8025768FpO8GrirQ-a7qcC-1rPdIX0yv2fl.png" alt="">
                </div>
                <div class='sharePto text-center'>
                    <img class='logo' v-if='branchId=="BJ_IASLT" || branchId=="BJ_OE"' src='https://m2.files.ivykids.cn/cloud01-file-8025768FsPX1GEYk30H0SHFAloq0uJkXnnu.png'  alt="">
                    <img class='logo' v-else  src="https://m2.files.ivykids.cn/cloud01-file-8025768FuSnI0PSaaQ94OwfrBy7P4WW9hM3.png" alt="">
                    <div class='title'>{{shareList.level=='4'?"快讯 What's Happening":'公告 NOTICE'}}</div>
                    <div class='schoolTitle'>{{shareList.school_title}}</div>
                    <div class='view' v-if='shareType=="view"'>
                        <div>内部预览，请勿分享给家长</div>
                        <div>Internal preview only, do not share to parents</div>
                    </div>
                </div>
                <div class='contentText p20 ' style='background:#fff'>
                    <div class='color3 font16 title fontWight' >{{shareList.q_cn}}</div>
                    <div class='color3 font16 title fontWight'>{{shareList.q_en}}</div>
                    <div class="media mt10" v-if='shareList.sign_as_uid && addresser[shareList.sign_as_uid] && showPublisher'>
                        <div class="media-left pull-left media-middle">
                            <a href="javascript:void(0)">
                                <img class="media-object img-circle shareAvatar" :src="addresser[shareList.sign_as_uid].photoUrl" data-holder-rendered="true" >
                            </a>
                        </div>
                        <div class="media-body pt5 media-middle">
                            <h4  class="media-heading font12">{{addresser[shareList.sign_as_uid].name}}</h4>
                            <div class='text-muted'>{{shareList.sign_title}}</div>
                        </div>
                    </div>
                    <div class='color3 summary mt20' v-if='showSummary'>{{shareList.summary_cn}}</div>
                    <div class='color3 summary' v-if='showSummary'>{{shareList.summary_en}}</div>
                    <div class='time mt10 color6'>{{shareList.format_publish_at}}</div>
                    <div class='wechat'>
                        <img  :src="shareType=='view'?previewQrcode:shareQrcode" class='wechatImg mt10'>
                        <div class='wechatTitle color6 mt10'>微信长按识别 Wechat long press for details</div>
                        <!-- <div class='wechatTitle color6'>Wechat long press qrcode for details</div> -->
                    </div>
                </div>
                <div class='bottom'>
                    <img src="http://m2.files.ivykids.cn/cloud01-file-8025768Fq95CMHmofnMY8zDky250kSK0jjw.png" alt="">
                </div>
            </div>
        </div>
        <div  class='shareContent'  :style="branchId == 'BJ_IASLT' || branchId == 'BJ_OE'?'border:1px solid #1F7949':'border:1px solid #0710B0'"  ref='shareContent2' v-if='htmlContent==2'> 
            <div class='bgPto relative' :style="{backgroundImage: branchId == 'BJ_IASLT' || branchId == 'BJ_OE' ? 'url(https://m2.files.ivykids.cn/cloud01-file-8025768Fsen2rX5pvTyeTiAUBb-BVAaqxAB.png)' : 'url(http://m2.files.ivykids.cn/cloud01-file-8025768Fm92t6oYcOnmwk1MbwEGNhQlaQZV.png)'}">
                <div class='shareStatus'>
                    <img v-if='shareList.level==1' style='width:88px' src="http://m2.files.ivykids.cn/cloud01-file-8025768FlypAT0B8zLKuO9DX53cRZknTZ4n.png" alt="">
                    <img v-if='shareList.level==2' style='width:88px' src="http://m2.files.ivykids.cn/cloud01-file-8025768FpO8GrirQ-a7qcC-1rPdIX0yv2fl.png" alt="">
                </div>
                <div class='sharePto text-center'>
                    <img class='logo' v-if='branchId=="BJ_IASLT" || branchId=="BJ_OE"' src='https://m2.files.ivykids.cn/cloud01-file-8025768FsPX1GEYk30H0SHFAloq0uJkXnnu.png'  alt="">
                    <img class='logo mb20' v-else  src="https://m2.files.ivykids.cn/cloud01-file-8025768FuSnI0PSaaQ94OwfrBy7P4WW9hM3.png" alt="">
                    <div class='title' :style="branchId == 'BJ_IASLT' || branchId == 'BJ_OE'?'color:#1F7949;margin-top:50px':'color:#061F9D;margin-top:40px'">{{shareList.level=='4'?"快讯 What's Happening":'公告 NOTICE'}}</div>
                    <div class='schoolTitle1' :style="branchId == 'BJ_IASLT' || branchId == 'BJ_OE'?'color:#1F7949':'color:#061F9D'">{{shareList.school_title}}</div>
                    <div class='view' v-if='shareType=="view"'>
                        <div>内部预览，请勿分享给家长</div>
                        <div>Internal preview only, do not share to parents</div>
                    </div>
                </div>
                <div class='contentText p20' :style="branchId == 'BJ_IASLT' || branchId == 'BJ_OE'?'background: #EEFAEF':'background: #F2F5FF'">
                    <div class='color3 font16 title fontWight' >{{shareList.q_cn}}</div>
                    <div class='color3 font16 title fontWight'>{{shareList.q_en}}</div>
                    <div class="media mt10 " v-if='shareList.sign_as_uid && addresser[shareList.sign_as_uid] && showPublisher'>
                        <div class="media-left pull-left media-middle">
                            <a href="javascript:void(0)">
                                <img class="media-object img-circle shareAvatar" :src="addresser[shareList.sign_as_uid].photoUrl" data-holder-rendered="true" >
                            </a>
                        </div>
                        <div class="media-body pt5 media-middle">
                            <h4  class="media-heading font12">{{addresser[shareList.sign_as_uid].name}}</h4>
                            <div class='text-muted'>{{shareList.sign_title}}</div>
                        </div>
                    </div>
                    <div class='color3 summary mt20' v-if='showSummary'>{{shareList.summary_cn}}</div>
                    <div class='color3 summary' v-if='showSummary'>{{shareList.summary_en}}</div>
                    <div class='time mt10 color6'>{{shareList.format_publish_at}}</div>
                </div>
                <div class='wechat text-center p20'>
                    <img  :src="shareType=='view'?previewQrcode:shareQrcode" class='wechatImg'>
                    <!-- <div class='wechatTitle color6'>微信长按识别，查看详情</div>
                    <div class='wechatTitle color6'>Wechat long press qrcode for details</div> -->
                    <div class='wechatTitle color6 mt10'>微信长按识别 Wechat long press for details</div>
                </div>
            </div>
        </div>
        <div  class='shareContent' :style="{
            'background-size': 'cover',
            'background-image': branchId == 'BJ_IASLT' || branchId == 'BJ_OE' ? 'url(https://m2.files.ivykids.cn/cloud01-file-8025768Fiod_I3tJjnFxWXQ_7ZkA_ynZjtR.png)' : 'url(http://m2.files.ivykids.cn/cloud01-file-8025768FsubjqpWc9t2CQXvvEdMV4d9leHW.png)'
            }" ref='shareContent3' v-if='htmlContent==3'> 
            <div class='bgPto relative' style='' >
                <div class='shareStatus'>
                    <img v-if='shareList.level==1' style='width:88px' src="http://m2.files.ivykids.cn/cloud01-file-8025768FlypAT0B8zLKuO9DX53cRZknTZ4n.png" alt="">
                    <img v-if='shareList.level==2' style='width:88px' src="http://m2.files.ivykids.cn/cloud01-file-8025768FpO8GrirQ-a7qcC-1rPdIX0yv2fl.png" alt="">
                </div>
                <div class='sharePto text-center'>
                    <img class='logo' v-if='branchId=="BJ_IASLT" || branchId=="BJ_OE"' src='https://m2.files.ivykids.cn/cloud01-file-8025768FsPX1GEYk30H0SHFAloq0uJkXnnu.png'  alt="">
                    <img class='logo' v-else  src="https://m2.files.ivykids.cn/cloud01-file-8025768FuSnI0PSaaQ94OwfrBy7P4WW9hM3.png" alt="">
                    <div class='title' >{{shareList.level=='4'?"快讯 What's Happening":'公告 NOTICE'}}</div>
                    <div class='schoolTitle'>{{shareList.school_title}}</div>
                    <div class='view' v-if='shareType=="view"'>
                        <div>内部预览，请勿分享给家长</div>
                        <div>Internal preview only, do not share to parents</div>
                    </div>
                </div>
                <div class='contentText white p8'>
                    <div class=' font16 title fontWight' >{{shareList.q_cn}}</div>
                    <div class=' font16 title fontWight'>{{shareList.q_en}}</div>
                    <div class="media mt10" v-if='shareList.sign_as_uid && addresser[shareList.sign_as_uid] && showPublisher'>
                        <div class="media-left pull-left media-middle">
                            <a href="javascript:void(0)">
                                <img class="media-object img-circle shareAvatar" :src="addresser[shareList.sign_as_uid].photoUrl" data-holder-rendered="true" >
                            </a>
                        </div>
                        <div class="media-body pt5 media-middle">
                            <h4  class="media-heading font12">{{addresser[shareList.sign_as_uid].name}}</h4>
                            <div class=''>{{shareList.sign_title}}</div>
                        </div>
                    </div>
                    <div class=' summary mt20' v-if='showSummary'>{{shareList.summary_cn}}</div>
                    <div class=' summary' v-if='showSummary'>{{shareList.summary_en}}</div>
                    <div class='time mt10 white08'>{{shareList.format_publish_at}}</div>
                </div>
                <div class='wechat p24 white flex'>
                    <div class='flex1 white08'>
                        <div class='wechatTitle'>微信长按识别</div>
                        <div class='wechatTitle '>Wechat long press for details</div>
                        <!-- <div class='wechatTitle '>for details</div> -->
                        
                    </div>
                    <img  :src="shareType=='view'?previewQrcode:shareQrcode" class='wechatImg'>
                </div>
            </div>
        </div>
    </div>
</div>