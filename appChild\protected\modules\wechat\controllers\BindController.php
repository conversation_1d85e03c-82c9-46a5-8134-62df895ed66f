<?php

class BindController extends WeChatBasedv2Controller
{
	private $codeActive = 1800; // 验证码有效期30m

	// 手机号绑定
	public function actionIndex()
	{
		// $redirectUrl = $this->createUrl('account', array(
		// 	'openid' => $this->openid, 
		// 	'access_token' => $this->access_token,
		// 	'lang' => Yii::app()->language,
		// 	'state' => $this->state
		// ));
		// $this->redirect($redirectUrl);

		Yii::import('common.models.wechat.SmsVcode');
		$this->subPageTitle = Yii::t('bind', 'Link the account');
		$modelForm = new PhoneBindForm();
		if (isset($_POST['PhoneBindForm'])) {
			$modelForm->attributes = $_POST['PhoneBindForm'];
			$this->addMessage('state', 'fail');
			if (!$modelForm->validate()) {
				$error = current($modelForm->getErrors());
				Yii::app()->user->setFlash('err', Yii::t('bind', 'Verification failed'));
			}else{
				$criteria = new CDbCriteria();
				$criteria->compare('phone', $modelForm->phone);
				$criteria->compare('code', $modelForm->code);
				$criteria->compare('phone_valid', 1);
				$criteria->compare('auth_ok', 0);
				$criteria->compare('created', '>' . (time() - $this->codeActive));
				$model = SmsVcode::model()->find($criteria);
				if ($model) {
					$model->auth_ok = 1;
					$model->save();
					// 验证成功，保存信息
					$parents = IvyParent::model()->findAllByAttributes(array('mphone' => $model->phone));
					if (count($parents) == 1) {
						$this->updateWechatUser(current($parents)->pid);
		    			$url = $this->redirectUrl();
			    		$this->redirect($url);
					}else{
					    $pageData = array(
					        'label' => (count($parents) == 1) ? 
					            Yii::t('bind', 'The account information registered via this phone number is as follows, please verify your information and continue to link to our WeChat Official Account'):
					            Yii::t('bind', 'This phone number has registered for multiple accounts, please verify your information and select the correct account to link to our WeChat Official Account'),
					        'phone' => $model->phone,
					        'bind' => 'phone',
					    );

					    foreach ($parents as $parent) {
					        $user = $parent->user;
					        $pageData['items'][$parent->pid] = array(
					            'title' => $user->getName(),
					            'photo' => $user->getPhotoSubUrl(),
					            'account' => $this->account,
					            'accountTitle' => '',
					            'email' => $user->email,
					        );

					        foreach (ChildProfileBasic::model()->getChildsByParentId($parent->pid) as $child) {
					            $pageData['items'][$parent->pid]['children'][$child->childid] = array(
					                'name' => $child->getChildName(),
					                'photo' => $child->photo,
					                'birthday' => $child->birthday_search,
					            );
					        }
					    }

						$this->render('showParents', array(
							'pageData' => $pageData,
						));
						Yii::app()->end();
					}
				}else{
					Yii::app()->user->setFlash('err', Yii::t('bind', 'Verification failed'));
				}
			}
		}

		$this->render('index', array('model' => $modelForm));
	}

	// 选择绑定账号
	public function actionAssignUser()
	{
		Yii::import('common.models.wechat.*');
		$uid    = Yii::app()->request->getPost('uid', 0);
		$phone  = Yii::app()->request->getPost('phone', 0);

		$this->addMessage('state', 'fail');
		if (!$uid) {
			$this->showMessage();
		}
		// 验证用户
		$criteria = new CDbCriteria();
		$criteria->compare('phone', $phone);
		$criteria->compare('phone_valid', 1);
		$criteria->compare('auth_ok', 1);
		$criteria->compare('created', '>' . (time() - $this->codeActive));
		$count = SmsVcode::model()->count($criteria);
		if(!$count){
			$this->addMessage('message', Yii::t('bind', 'Verification failed'));
			$this->showMessage();
		}
		$parent = IvyParent::model()->findByPk($uid);
		if ($parent->mphone != $phone) {
			$this->addMessage('message', 'Wrong Phone');
			$this->showMessage();
		}
		if(!$this->updateWechatUser($uid)){
			$this->addMessage('message', Yii::t('bind', 'Save failed'));
			$this->showMessage();
		}

		$this->addMessage('state', 'success');
		$url = $this->redirectUrl();
		$this->addMessage('message', $url);
		$this->showMessage();
	}

	// 账号密码绑定
	public function actionAccount()
	{
		$this->subPageTitle = Yii::t('bind', 'Link the account');
	    
	    $modelForm = new UserBindForm();

	    if (isset($_POST['UserBindForm'])) {
	    	$this->addMessage('state', 'fail');

	    	Yii::import('application.modules.user.components.*');
	    	$modelForm->attributes = $_POST['UserBindForm'];
	    	$modelForm->wechatUname = $this->openid ? $this->openid :'null';
	    	if ($modelForm->validate()) {
	    		if($this->updateWechatUser($modelForm->userid)){
	    			$url = $this->redirectUrl();
		    		$this->redirect($url);
	    		}
	    		Yii::app()->user->setFlash('err', Yii::t('bind', 'Save Failed'));
	    	}else{
	    		$error = current($modelForm->getErrors());
	    		Yii::app()->user->setFlash('err', $error[0]);
	    	}
	    }

	    return $this->render('account', array('model' => $modelForm));
	}

	// 发送验证码
	public function actionVcode()
	{
		Yii::import('common.models.wechat.SmsVcode');
		$this->addMessage('state', 'fail');
		$model = new SmsVcode();
		if ($_POST['SmsVcode']) {

			$t = time();
			$previousTime = $t - 300;
			$ip = Yii::app()->request->getUserHostAddress();
			$criteria = new CDbCriteria();
			$criteria->compare('ip', $ip);
			$criteria->compare('created', '>'.$previousTime);
			$count = SmsVcode::model()->count($criteria);

			if ($count < 2000) {
				$model->attributes = $_POST['SmsVcode'];
				$code = (string)rand(1000, 9999);
				$model->type = SmsVcode::TYPE_ASA_PARENT;
				$model->code = $code;
				$model->phone_valid = 0;
				$model->auth_ok = 0;
				$model->ip = $ip;
				$model->created = $t;

				$count = IvyParent::model()->countByAttributes(array('mphone' => $model->phone));
				if ($count > 0) {
					$model->phone_valid = 1;
					$this->addMessage('state', 'success');
					
					$code = $model->code;
					$product = '';
					if ($this->openid) {
						$product = "绑定帐号";
					}
					$templateCode = 'SMS_70195386';
					if (Yii::app()->language == 'en_us') {
						$templateCode = 'SMS_70215510';
						$product = '';
						if ($this->openid) {
							$product = "binding wechat account";
						}
					}
					$signName = '北京市朝阳区启明星学校';
					if ($this->account == 'ivy') {
						$signName = '艾毅';
					}
					$mqData = array(
						'phoneNumbers' => $model->phone,
						'templateCode' => $templateCode,
						'templateParam' => array(
						    'code' => $model->code,
						    'product' => $product,
						),
						'signName' => $signName,
					);
					CommonUtils::addProducer('sms', "Sms.send", CJSON::encode($mqData), 0);
				}
				else {
					$this->addMessage('message', Yii::t('bind', 'Please ensure that your phone number has been registered'));
				}

				if (!$model->save()) {
					$error = current($model->getErrors());
					$this->addMessage('message', $error[0]);
				}
			}
			else {
				$this->addMessage('message', 'Request too frequent, please try again later');
			}
		}
		$this->showMessage();
	}

	// 保存更新微信用户表
	public function updateWechatUser($userid)
	{
		Yii::import('common.models.wechat.*');
		// 非微信用户
		// if (!$this->openid) {
		// 	Yii::app()->session['userid'] = $userid;
		// 	return true;
		// }
		// 微信用户
	    $wechatUser = $this->wechatUser;
	    if (!$wechatUser){
	        $wechatUser = new WechatUser();
		    $wechatUser->openid = $this->openid;
		    $wechatUser->unionid = $this->unionid;
		    $wechatUser->account = $this->account;
	    }

	    $wechatUser->userid = $userid;
	    $wechatUser->valid = 1;
	    $wechatUser->updated = time();
	    $wechatUser->last_contact = time();
	    if ($wechatUser->save()) {
	    	// 更新用户信息表
			$wechatUser->updateInfo();
			return true;
	    }else{
	    	$errors = current($wechatUser->getErrors());
	    	Yii::log($errors[0], 'info', 'wechatuser');
	    	return false;
	    }
	}

	public function redirectUrl()
	{
		if (!$this->openid) {
			// return $this->createUrl('nowechat');
			//return $this->createUrl('/wechat/regExtraInfo/step1');
		}
		return $this->createUrl('/wechat/user/bind', array(
			'openid' => $this->openid, 
			'access_token' => $this->access_token,
			'lang' => Yii::app()->language,
			'state' => $this->state
		));
	}

	public function actionNowechat()
	{
		Yii::app()->theme = 'mobile2';
		$this->render('nowechat');
	}

}
