<?php
if(!defined("IVY_POLICY"))
throw new CException('DO NOT USE THIS FILE DIRECTLY');

return array( 'policy'=>array(

	//是否老生老办法
	ENABLE_CONSTANT_TUITION => true,
    // 按天收费
    FENTAN_FACTOR => DAILY,

	//年保育费
	//ANNUAL_CHILDCARE_AMOUNT => 8000,

	//是否开放课外活动账单
	ENABLE_AFTERSCHOOLS => true,

	//图书馆工本费
	FEETYPE_LIBCARD => 10,

	//学期四个时间点；取前后两端
	TIME_POINTS   => array(201509,201512,201601,201607),

	//寒暑假月份，及其每月计费天数
	//HOLIDAY_MONTH => array(201402=>20,201408=>20),

	//年付开通哪些缴费类型
	PAYGROUP_ANNUAL => array(
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
			FEETYPE_SCHOOLBUS,
		),
		TUITION_CALC_BASE => BY_MONTH,
	),

	//学期付开通哪些缴费类型
	PAYGROUP_SEMESTER => array(
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
			FEETYPE_SCHOOLBUS,
		),
		TUITION_CALC_BASE => BY_SEMESTER,
	),

	//月份开通哪些缴费类型
	PAYGROUP_MONTH => array(
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
			FEETYPE_SCHOOLBUS,
		),
		TUITION_CALC_BASE => BY_MONTH,
	),

	//计算基准：BY_MONTH, 或者 BY_SETTING, 前者表示所有的学费都基于月费用计算，后者表示所有的学费都基于学期或学年
	TUITION_CALCULATION_BASE => array(

        BY_ANNUAL => array(
            AMOUNT => array(
                FULL5D => 89960,
                HALF2D => 33930,
                HALF3D => 48830,
                HALF5D => 67860,
            ),
        ),

        BY_SEMESTER1 => array(
            AMOUNT => array(
                //FULL5D => 47780,
                FULL5D => 43345,//2019
                // HALF2D => 12540,
                // HALF3D => 18050,
                // HALF5D => 25080,
                //HALF4D => 41731,
            ),
        ),

        BY_SEMESTER2 => array(
            AMOUNT => array(
                //FULL5D => 44970,
                FULL5D => 49405,//2019
                // HALF2D => 22440,
                // HALF3D => 32300,
                // HALF5D => 44880,
                //HALF4D => 62597,
            ),
        ),

        BY_MONTH => array(

            //月收费金额
            AMOUNT => array(
                FULL5D => 8750,
                HALF5D => 6600,
                HALF3D => 4750,
                HALF2D => 3300,
            ),

            //收费月份，及其权重
            MONTH_FACTOR => array(
                201509=>1,
                201510=>1,
                201511=>1,
                201512=>.8,
                201601=>1,
                201602=>.8,
                201603=>1,
                201604=>1,
                201605=>1,
                201606=>1,
                201607=>1
            ),

            //折扣
            GLOBAL_DISCOUNT => array(
                DISCOUNT_ANNUAL => '0.97:10:2015-12-01'
            )
        ),
	),

	//每餐费用
	LUNCH_PERDAY => 28,

	//账单到期日和账单开始日之差
	DUEDAYS_OFFSET => 1,

    //代金卷金额
    //VOUCHER_AMOUNT => 500,

));

?>