<?php
$this->breadcrumbs=array(
	Yii::t("navigations", 'Support')=>array('/child/support'),
	Yii::t("navigations", 'Reserve PTC Slot'),
);
$colorbox = $this->widget('application.extensions.colorbox.JColorBox');
$colorbox->addInstance('.colorbox', array('iframe' => false, 'width' => '800', 'height' => '80%'));
?>

<?php if(Yii::app()->user->getIsGuest()):?>

	<?php echo Yii::t("global","Not available to visitors.");?>
	
<?php else:?>

	<h1><label><?php echo Yii::t("support","Reserve Parent Teacher Conference Time Slot") ?></label></h1>
	
	<div>
        <h2>
            <?php echo $classObj->title;?>
        </h2>
        <?php
        if($events){
            ?>
            <div class="flash-info">
                <ul>
                    <li><?php echo Yii::t('support','Please click on the desired time slot in green to schedule an appointment.');?></li>
                    <li><?php echo Yii::t('support','Scheduled appointment cannot be modified online within 2 days of the reserved time slot; please contact the lead teacher directly to reschedule in this situation.');?></li>
                </ul>
            </div>
            <?php
            foreach($events as $mon=>$event){
                echo CHtml::openTag('div', array('class'=>'calendar-box'));
                echo $mon;
                $this->widget('common.extensions.EFullCalendar.EFullCalendar', array(
                    'id' => 'ptc-'.$mon,
//                    'themeCssFile'=>'cupertino/jquery-ui.min.css',
                    'fullCalendarCss'=>Yii::app()->theme->baseUrl.'/widgets/fullcalendar/fullcalendarcss.css',

                    // raw html tags
                    'htmlOptions'=>array(
                        // you can scale it down as well, try 80%
                        'style'=>'width:100%'
                    ),
                    // FullCalendar's options.
                    'options'=>array(
                        'header'=>array(),
                        'firstDay' => 0,
                        'defaultDate' => $mon,
                        'timezone' => 'local',
                        'eventClick' => "js:function(event, element){timeSlotClick(event, element)}",

                        //示例数据
                        'events' => $event,
                        'timeFormat' => 'HH:mm',
                        'displayEventEnd' => true,
                    )
                ));
                echo CHtml::closeTag('div');
            }
        }else{
            ?>
            <div class="flash-warning">
                <?php echo Yii::t('support','No available time slot currently (PTC are generally held at the end of each semester).'); ?>
            </div>
            <?php
        }
        ?>
	</div>
<?php endif;?>

<div id="dialog-form" style="display: none;">
    <div class="popup-box1">
        <h2><?php echo CommonUtils::addColon(Yii::t('support','Appointment Time Slot'));?> <small id="timeslot"></small></h2>
        <div class="flash-info">
            <ul>
                <li><?php echo Yii::t('support','Scheduled appointment cannot be modified online within 2 days of the reserved time slot; please contact the lead teacher directly to reschedule in this situation');?></li>
            </ul>
        </div>

        <p><?php echo Yii::t('support','Our teachers will share their observations of your child\'s strength, interests and challenges with you during the conference. If there is anything else that you would like to discuss with the teachers, please indicate below:');?></p>
        <p><textarea id="memo" name="memo" rows="4" style="width: 100%"><?php echo $memo;?></textarea></p>
        <p class="tar">
            <button class="w100" onclick="subPtc();"><?php echo Yii::t('global', 'Submit')?></button>
            <button class="w100" onclick="$.colorbox.close();"><?php echo Yii::t('global', 'Cancel')?></button>
        </p>
    </div>
</div>

<style>
    .fc-row table{
        margin-bottom: 0;
    }
    .event{
        cursor: pointer;
    }
</style>

<script>
    var unikey;
    var editable = <?php echo $editable;?>;
    console.log(editable)
    function subPtc(){
        var memo = $('#cboxLoadedContent #memo').val();
        $.post('<?php echo $this->createUrl('processPtc')?>', {unikey: unikey, memo: memo}, function(data){
            if(data.state == 'success'){
                var fcalendar = $("#ptc-"+data.data.mon);
                var event = fcalendar.fullCalendar('clientEvents', unikey)[0];
                event.title = '<?php echo $this->getChildName();?>';
                event.color = '#3a87ad';
                fcalendar.fullCalendar('updateEvent', event);

                if(data.data.uniKey != ''){
                    var fcalendar_old = $("#ptc-"+data.data.mon_old);
                    var event_old = fcalendar_old.fullCalendar('clientEvents', data.data.uniKey)[0];
                    event_old.title = '';
                    event_old.color = '#68BA00';
                    fcalendar_old.fullCalendar('updateEvent', event_old);
                }
                if(data.data.editable == 0){
                    editable = 0;
                }
                $('#dialog-form #memo').html(memo);
                $.colorbox.close();
            }
            else{
                cb.message = data.message;
                cb.msgreload();
            }
        }, 'json');
    }

    function timeSlotClick(event, element)
    {
        <?php if( !Yii::app()->user->checkAccess('editProfile',array("childid"=>$this->getChildId())) ):?>
            alert("No permission"+"\r\n"+"没有权限");
            return false;
        <?php endif;?>
        if(!editable){
            alert('<?php echo Yii::t('support', 'Scheduled appointment cannot be modified online within 2 days of the reserved time slot; please contact the lead teacher directly to reschedule in this situation.')?>');
            return false;
        }
        if(event.color != '#68BA00'){
            alert('<?php echo Yii::t('support', 'Please click on the desired time slot in green to schedule an appointment.')?>');
            return false;
        }
        unikey = event.id;
        var mms = event.start._d.getMinutes();
        var mme = event.end._d.getMinutes();
        var title = $.datepicker.formatDate('yy-mm-dd', event.start._d)+' '+event.start._d.getHours()+':'+((mms<10)?'0'+mms:mms)+' - '+event.end._d.getHours()+':'+((mme<10)?'0'+mme:mme);
        $('#timeslot').html(title);
        $.colorbox({width: 750, title: title, html: $('#dialog-form').html()});
    }
</script>