<?php if ($SNotesModel->en_content || $SNotesModel->en_important) { ?>
    <div class="journal-box">
        <h3>
            <span><?php echo Yii::t("portfolio", 'School News'); ?></span>
            <?php if (!$weeknum && !$yid): ?>
                <em><?php echo Mims::formatDateTime($SNotesModel->weekInfo->monday_timestamp) ?> - <?php echo Mims::formatDateTime($SNotesModel->weekInfo->monday_timestamp + (3600 * 24 * 5) - 1) ?></em>
            <?php endif; ?>
        </h3>

        <?php if ($SNotesModel->en_important): ?>
            <div class="key-reminder">
                <h5 class="title"></h5>
                <div class="reminder">
                    <script>
                        document.write( normalText(<?php echo CJSON::encode($SNotesModel->en_important); ?>) );
                    </script>
                </div>
                <em class="pinner"></em>
                <em class="left-bottom"></em>
                <em class="right-top"></em>
            </div>

        <?php endif; ?>
        <?php
        if (!empty($CDProfile)) {
            foreach ($CDProfile as $cd) {
                ?>
                <div class="span-7">
                <dl>
                    <dt>
                    <?php
                    if (isset($cd->staffInfo)):
                        echo CHtml::image(Mims::CreateUploadUrl('infopub/staff/' . $cd->staffInfo->staff_photo), $cd->getName(), array("title" => $cd->getName(), "class" => "face"));
                    endif;
                    ?>
                    </dt>
                    <dd>
                        <p class="cd"><?php echo Yii::t("portfolio", 'Campus Director'); ?></p>
                        <p><?php echo $cd->getName(); ?></p>
                    </dd>
                </dl>
                </div>
                <?php
            }
            ?>
            <div class="clear"></div>
            <?php
        }
        ?>

        <script>
            document.write( normalText(<?php echo CJSON::encode($SNotesModel->en_content); ?>) );
        </script>
    </div>
<?php } ?>
