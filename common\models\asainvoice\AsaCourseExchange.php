<?php

/**
 * This is the model class for table "ivy_asa_course_exchange".
 *
 * The followings are the available columns in table 'ivy_asa_course_exchange':
 * @property integer $id
 * @property integer $childid
 * @property string $schoolid
 * @property integer $new_cid
 * @property integer $old_cid
 * @property integer $new_course_num
 * @property integer $old_course_num
 * @property double $new_course_amount
 * @property double $old_course_amount
 * @property double $difference
 * @property integer $link_id
 * @property integer $status
 * @property integer $updated_userid
 * @property integer $updated_time
 * @property integer $create_userid
 * @property integer $create_time
 * @property integer $ufida_status
 */
class AsaCourseExchange extends CActiveRecord
{
	const SUBMIT = 0;	// 提交
	const SUCCESS = 1;	// 成功
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_asa_course_exchange';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('childid, schoolid, new_cid, old_cid, new_course_num, old_course_num, new_course_amount, old_course_amount, difference, link_id, status, updated_userid, updated_time, create_userid, create_time', 'required'),
			array('childid, new_cid, old_cid, new_course_num, old_course_num, link_id, status, updated_userid, updated_time, create_userid, create_time, ufida_status', 'numerical', 'integerOnly'=>true),
			array('new_course_amount, old_course_amount, difference', 'numerical'),
			array('schoolid', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, childid, schoolid, new_cid, old_cid, new_course_num, old_course_num, new_course_amount, old_course_amount, difference, link_id, status, updated_userid, updated_time, create_userid, create_time, first_invoice, pay_invoice, refund_invoice', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'asaNewCourse' => array(self::BELONGS_TO, 'AsaCourse', array('new_cid'=>'id')),
            'asaOldCourse' => array(self::BELONGS_TO, 'AsaCourse', array('old_cid'=>'id')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'childid' => 'Childid',
			'schoolid' => 'Schoolid',
			'new_cid' => 'New Cid',
			'old_cid' => 'Old Cid',
			'new_course_num' => 'New Course Num',
			'old_course_num' => 'Old Course Num',
			'new_course_amount' => 'New Course Pirce',
			'old_course_amount' => 'Old Course Price',
			'difference' => 'Difference',
			'link_id' => 'Link',
			'status' => 'Status',
			'updated_userid' => 'Updated Userid',
			'updated_time' => 'Updated Time',
			'create_userid' => 'Create Userid',
			'create_time' => 'Create Time',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('new_cid',$this->new_cid);
		$criteria->compare('old_cid',$this->old_cid);
		$criteria->compare('new_course_num',$this->new_course_num);
		$criteria->compare('old_course_num',$this->old_course_num);
		$criteria->compare('new_course_amount',$this->new_course_amount);
		$criteria->compare('old_course_amount',$this->old_course_amount);
		$criteria->compare('difference',$this->difference);
		$criteria->compare('link_id',$this->link_id);
		$criteria->compare('status',$this->status);
		$criteria->compare('updated_userid',$this->updated_userid);
		$criteria->compare('updated_time',$this->updated_time);
		$criteria->compare('create_userid',$this->create_userid);
		$criteria->compare('create_time',$this->create_time);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AsaCourseExchange the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
