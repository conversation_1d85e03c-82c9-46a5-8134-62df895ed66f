<?php

/**
 * This is the model class for table "ivy_reg".
 *
 * The followings are the available columns in table 'ivy_reg':
 * @property integer $id
 * @property integer $yid
 * @property string $startyear
 * @property integer $startdate
 * @property integer $enddate
 * @property integer $updated_by
 * @property integer $updated_at
 * @property integer $schoolid
 */
class Reg extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_reg';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('yid, startyear, schoolid, startdate, enddate, updated_by, updated_at', 'required'),
			array('yid, startdate, enddate, updated_by, updated_at', 'numerical', 'integerOnly'=>true),
			array('startyear, schoolid', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, yid, startyear, startdate, enddate, updated_by, updated_at', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'regCarSite' => array(self::HAS_MANY, 'RegCarSite', array('reg_id' => 'id')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'yid' => 'Yid',
			'startyear' => 'Startyear',
			'startdate' => 'Startdate',
			'enddate' => 'Enddate',
			'updated_by' => 'Updated By',
			'updated_at' => 'Updated At',
			'schoolid' => 'schoolid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('startyear',$this->startyear,true);
		$criteria->compare('startdate',$this->startdate);
		$criteria->compare('enddate',$this->enddate);
		$criteria->compare('updated_by',$this->updated_by);
		$criteria->compare('updated_at',$this->updated_at);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return Reg the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
