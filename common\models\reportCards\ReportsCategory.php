<?php

/**
 * This is the model class for table "ivy_reports_category".
 *
 * The followings are the available columns in table 'ivy_reports_category':
 * @property integer $id
 * @property integer $template_id
 * @property integer $parent_id
 * @property string $title_en
 * @property string $title_cn
 * @property string $options
 * @property integer $weight
 * @property integer $update_user
 * @property integer $updated
 * @property string $grade
 */
class ReportsCategory extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_reports_category';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('template_id, parent_id, weight, update_user, updated', 'required'),
			array('option_groupid, template_id, parent_id, weight, update_user, updated', 'numerical', 'integerOnly'=>true),
			array('title_en, title_cn', 'length', 'max'=>255),
			array('program, grade', 'length', 'max'=>10),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, template_id, parent_id, title_en, title_cn, option_groupid, weight, update_user, updated, program', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'items' => array(self::HAS_MANY, 'ReportsItem', 'category_id', 'order'=>'items.weight'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'template_id' => 'Template',
			'parent_id' => 'Parent',
			'program' => 'Curriculum',
			'title_en' => 'Title En',
			'title_cn' => 'Title Cn',
			'option_groupid' => 'Options',
			'weight' => 'Weight',
			'update_user' => 'Update User',
			'updated' => 'Updated',
			'grade' => 'Grade',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('template_id',$this->template_id);
		$criteria->compare('parent_id',$this->parent_id);
		$criteria->compare('program',$this->program,true);
		$criteria->compare('title_en',$this->title_en,true);
		$criteria->compare('title_cn',$this->title_cn,true);
		$criteria->compare('option_groupid',$this->options);
		$criteria->compare('weight',$this->weight);
		$criteria->compare('update_user',$this->update_user);
		$criteria->compare('updated',$this->updated);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return ReportsCategory the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public function getTitle()
    {
        return CommonUtils::autoLang($this->title_cn, $this->title_en);
    }

	function getCategoryData($branchId, $templateid, $uid) {
        $cfgCampusProgram = CommonUtils::LoadConfig('CfgCampusProgram');
        //教师权限判断（仅显示所教的学课）
        $flag = $this->teacherFlagShow($branchId, $uid);
        $criteria = new CDbCriteria();
        $criteria->compare('template_id', $templateid);
        if ($flag){
            $criteria->addCondition("program is null OR program = '' OR program in ('".implode("','", $flag)."')");
        }
        $criteria->order = 'weight';
        $categorys = ReportsCategory::model()->findAll($criteria);
        $roots = array();
        $subs = array();
        $items = array();
        $optionGroupIds = array();
        foreach($categorys as $cat){
            if($cat->parent_id == 0){
                $roots[$cat->id]['id'] = $cat->id;
                $roots[$cat->id]['option_groupid'] = $cat->option_groupid;
                $roots[$cat->id]['program'] = $cat->program;
                $roots[$cat->id]['title'] = $cfgCampusProgram[Branch::PROGRAM_DAYSTAR]['programs'][$cat->program];
            } else {
				$optionGroupIds[] = $cat->option_groupid;
                $subs[$cat->parent_id][$cat->id]['id'] = $cat->id;
                $subs[$cat->parent_id][$cat->id]['option_groupid'] = $cat->option_groupid;
                $subs[$cat->parent_id][$cat->id]['title'] = $cat->getTitle();
            }
        }

        $criteria = new CDbCriteria();
        $criteria->compare('template_id', $templateid);
        $criteria->order = 'weight';
        $reports = ReportsItem::model()->findAll($criteria);
        foreach($reports as $report){
            $items[$report->category_id][$report->id] = $report->getTitle();
        }
        return array(
            'roots' => $roots,
            'subs' => $subs,
            'items' => $items,
            'optionGroupIds' => $optionGroupIds,
        );
    }

    /***
     * @param $branchId
     * @param $startYear
     * @param $grade
     * @param $uid
     * @param $semester
     * @param $type string homeview-当前页面数据 preview预览数据【预览数据不限制所教program】
     * @return array
     */
    function getCategoryDataNew($branchId, $startYear, $grade, $uid, $semester = 1,$type='homeview') {
        $cfgCampusProgram = CommonUtils::LoadConfig('CfgCampusProgram');

        // 获取当前年级所有分类数据
        // 获取当前老师所教的program（仅显示所教的）2025-1-23去掉此限制
        $programs = '';
        if($type =='homeview'){
            $programs = $this->teacherFlagShow($branchId, $uid);
        }
        $criteria = new CDbCriteria();
        $criteria->compare('template_id', 0);
        $criteria->compare('grade', $grade);
        $criteria->order = 'weight';
        if ($programs){
            $criteria->addCondition("program is null OR program = '' OR program in ('".implode("','", $programs)."')");
        }
        $categorys = ReportsCategory::model()->findAll($criteria);
        $categoryIdList = array();
        foreach ($categorys as $category) {
            if ($category->parent_id != 0) {
                $categoryIdList[] = $category->id;
            }
        }

        // 查找当前学校、学年适用的 template item
        $criteria = new CDbCriteria();
        $criteria->compare('branch_id', $branchId);
        $criteria->compare('category_id', $categoryIdList);
        $criteria->compare('startyear', $startYear);
        $templateItemModelList = ReportsTemplateItem::model()->findAll($criteria);
        $temlateCategoryIdList = array();
        $temlateItemIdList = array();
		$disabledIdList = array();
        foreach ($templateItemModelList as $templateItemModel) {
            $temlateCategoryIdList[] = $templateItemModel->category_id;
            $temlateItemIdList[] = $templateItemModel->item_id;
			if ($semester == 1 && $templateItemModel->semester1 == 0) {
				$disabledIdList[] = $templateItemModel->item_id;
			}
			if ($semester == 2 && $templateItemModel->semester2 == 0) {
				$disabledIdList[] = $templateItemModel->item_id;
			}
        }

        // 获取所有标准
        $categoryItemModelList =  ReportsItem::model()->findAllByPk($temlateItemIdList);

        $roots = array();
        $subs = array();
        $items = array();
        $optionGroupIds = array();
        foreach($categorys as $cat){
            if ($cat->parent_id > 0 && !in_array($cat->id, $temlateCategoryIdList)) {
                continue;
            }
            if($cat->parent_id == 0){
                $roots[$cat->id]['id'] = $cat->id;
                $roots[$cat->id]['option_groupid'] = $cat->option_groupid;
                $roots[$cat->id]['program'] = $cat->program;
                $roots[$cat->id]['title'] = $cfgCampusProgram[Branch::PROGRAM_DAYSTAR]['programs'][$cat->program];
                $roots[$cat->id]['score_comment_separate'] = !empty($cfgCampusProgram[Branch::PROGRAM_DAYSTAR]['score_comment_separate'][$branchId]) && in_array($cat->program,$cfgCampusProgram[Branch::PROGRAM_DAYSTAR]['score_comment_separate'][$branchId]);
            } else {
                $optionGroupIds[] = $cat->option_groupid;
                $subs[$cat->parent_id][$cat->id]['id'] = $cat->id;
                // 显示 grabdebook 的标准组
                $subs[$cat->parent_id][$cat->id]['option_groupid'] = $cat->option_groupid;
                $subs[$cat->parent_id][$cat->id]['title'] = $cat->getTitle();
                $subs[$cat->parent_id][$cat->id]['gradebook'] = false;
                $subs[$cat->parent_id][$cat->id]['grademap'] = array();
                if (in_array($cat->option_groupid, array(5))) {
                    $subs[$cat->parent_id][$cat->id]['gradebook'] = in_array($cat->option_groupid, array(5));
                    $subs[$cat->parent_id][$cat->id]['grademap'] = array(4 => 'E', 3 => 'P', 2 => 'L', 1 => 'N');
                }
            }
        }
        foreach($categoryItemModelList as $item){
            $items[$item->category_id][$item->id] = $item->getTitle();
        }
        foreach ($roots as $key => $value) {
            if (!isset($subs[$key])) {
                unset($roots[$key]);
            }
        }
        return array(
            'roots' => $roots,
            'subs' => $subs,
            'items' => $items,
            'optionGroupIds' => $optionGroupIds,
            'disabledIdList' => $disabledIdList,
        );
    }

	/*
	* 判断当前用户，如果是老师显示所教的科目，否则显示全部
	*/
   public function teacherFlagShow($branchId, $uid){
	   Yii::import('common.models.grades.TeacherSubjectLink');
	   $flag=array();
	   $link = TeacherSubjectLink::model()->findAll('schoolid=:schoolid and teacher_uid=:teacher_uid and status=1',
		   array(':schoolid'=>$branchId,':teacher_uid'=>$uid)
	   );
	   if ($link){
		   foreach($link as $tag){
			   $flag[] = $tag->subject_flag;
		   }
	   }
	   return $flag;
   }
}
