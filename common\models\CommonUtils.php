<?php

use phpDocumentor\Reflection\PseudoTypes\False_;

class CommonUtils {

    const SECURITY_KEY = 'A23sW343eL8934ov2332E';//safe code
    const CONST_DOTSYMBOL = '● ';


    /**
     * 根据语言返回对应的字段值 中英文
     * @param string $cn_value
     * @param string $en_value
     * @param boolean $assign_lang 指定语言--- false 不指定语言环境是什么就是什么  true 中英文 cn 中文 en 英文
     */
    public static function autoLang($cn_value, $en_value, $assign_lang = false) {
        $lang_key = Yii::app()->language;
        $cn_value = trim($cn_value);
        $en_value = trim($en_value);
        if(!in_array($assign_lang, array('zh_cn', 'en_us'))){
            if (strtolower($assign_lang) == "cn") {
                $lang_key = "zh_cn";
            } else if (strtolower($assign_lang) == "en") {
                $lang_key = "en_us";
            }
        }

        if ($lang_key == "zh_cn") {
            if ($assign_lang === true) {
                if (!empty($cn_value) && !empty($en_value)) {
                    return $cn_value . " (" . $en_value . ")";
                }
            }
            if (empty($cn_value))
                return $en_value;
            else
                return $cn_value;
        }else {
            if ($assign_lang === true) {
                if (!empty($cn_value) && !empty($en_value)) {
                    return $en_value . "（" . $cn_value . "）";
                }
            }
            if (empty($en_value))
                return $cn_value;
            else
                return $en_value;
        }
    }
    
    
    /**
     * Summary
     * @param	Object	$category	配置文件名 目录在app/comp/config下
     * @return	Object				返回
     * 使用方法 若文件名为 CfgTest.php
     * 	$ret = Mims::LoadConfig('CfgTest');
     */
    static function LoadConfig($category){
        $alias = 'application.components.config.'.$category;
        $file = Yii::getPathOfAlias($alias).'.php';
        if(file_exists($file))
            $config = require($file);
        else{
            $alias = 'common.config.'.$category;
            $file = Yii::getPathOfAlias($alias).'.php';
            $config = require($file);
        }
        return $config;
    }
    
	static function getConfig($alias, $endwith='.php'){
		$file = Yii::getPathOfAlias( $alias ) . $endwith;
		if(file_exists($file)){
			return new CConfiguration( $file );
		}
		return null;
	}
    
    /**
     * 格式化日期
     * @param	Object	$timestamp	Description
     * @param	String	$dateWidth	'medium', 'short', 'long', null
     * @param	String	$timeWidth	'medium', 'short', 'long', null
     * @return	Object				Description
     */
    static function formatDateTime($timestamp, $dateWidth='medium', $timeWidth=null){
        if($timestamp)
        return Yii::app()->dateFormatter->formatDateTime($timestamp,$dateWidth, $timeWidth);    
    }

    static function getAge($birthday_time,$current_time=0,$isDate = false)
    {
		$current_time =($current_time == 0)? time():$current_time;
        if(empty($birthday_time)){
            $re_str = "N/A";
        }else{
            if(true == $isDate){
                $birthday_time = strtotime($birthday_time);
            }
            $result = self::timediff($birthday_time,$current_time);
            $re_str = "";
            if($result['year']>0){
                $re_str.= $result['year']." ".Yii::t("global", "Year(s)");
            }
            if($result['month']>0){
                $re_str.= " ".$result['month']." ".Yii::t("global", "Month");
            }
            if(empty($re_str)){
                $re_str = Yii::t("global", "Year(s)");
            }
        }
        return $re_str;
    }	

     static function timediff($begin_time, $end_time)
    {
        if($begin_time < $end_time) {
            $starttime = $begin_time;
            $endtime   = $end_time;
        } else {
            $starttime = $end_time;
            $endtime   = $begin_time;
        }

        $timediff = $endtime - $starttime;

        $years   = intval($timediff/(86400*30*12));

        $remain = $timediff%(86400*30*12);
        $months   = intval($remain/(86400*30));

        $remain = $timediff%(86400*30);
        $days   = intval($remain/86400);

        $remain = $remain%86400;
        $hours = intval($remain/3600);

        $remain = $remain%3600;
        $mins   = intval($remain/60);

        $secs   = $remain%60;

        $result = array("year"=>$years, "month"=>$months, "day"=>$days, "hour"=>$hours, "min"=>$mins, "sec"=>$secs);

        return $result;
    }

    //发帖时间显示，比如显示 1小时之前，1天前
    static function time_elapsed_string($ptime)
    {
        $etime = time() - $ptime;
        if ($etime < 1)
        {
            return Yii::t('club','just now');
        }

        $a = array( 12 * 30 * 24 * 60 * 60  =>  'year',
            30 * 24 * 60 * 60       =>  'month',
            24 * 60 * 60            =>  'day',
            60 * 60                 =>  'hour',
            60                      =>  'minute',
            1                       =>  'second'
        );

        foreach ($a as $secs => $str)
        {
            $d = $etime / $secs;
            if ($d >= 1)
            {
                $r = round($d);
                $dataType = $str . ($r > 1 ? 's' : '');
                $result = Yii::t('club', ':data '.$dataType.' ago', array(':data'=>$r));
                return $result;
            }
        }
    }

    // 随机密码
    static function randPass($len=6) {
        $m = array('i', 'l', 'o');

        foreach(range('a','z') as $word){
            if (!in_array($word, $m)){
                $_m[$word] = $word;
            }
        }
        foreach(range(2,9) as $word){
            $n[$word] = $word;
        }

        $s = '';
        for($i=0; $i<$len;$i++){
            if ($i%2==0){
                $s.=array_rand($_m);
            }
            else {
                $s.=array_rand($n);
            }
        }
        return $s;
    }

    static function checkMobile($s='')
    {
        if(!empty($s)){
            $pattern = "/^(1)\d{10}$/";
            if (preg_match($pattern, $s))
            {
                return true;
            }
            return false;
        }
    }

    function urlsafe_base64_encode($string) {
        $data = base64_encode($string);
        $data = str_replace(array('+','/','='),array('-','_',''),$data);
        return $data;
    }

    static function getMediaUrl2($thumb=false, $type='photo', $filename='', $campusType='ivy')
    {
        $picStyle = ($campusType == 'ivy') ? 'i1000' : 'v1000';

        $serverConfs = CommonUtils::LoadConfig('CfgMediaServer');
        $sConfs = $serverConfs['servers']['20'];

        $mediaUrl = $sConfs["url"];
        $decollator = ';;';
        if($type == 'video'){
            if (strpos($filename, $decollator) !== false) {
                $fileArr = explode($decollator, $filename);
                if($thumb){
                    if($fileArr[1] == 'videoTmp.jpg'){
                        $mediaUrl .= urlencode($fileArr[1]).'!vh120';
                    }else{
                        $mediaUrl .= urlencode($fileArr[1].'=/'.$fileArr[0]).'!vh120';
                    }
                }
                else{
                    $mediaUrl .= urlencode($fileArr[2].'=/'.$fileArr[0]);
                }
            } elseif (strpos($filename, 'o/s') !== false && strpos($filename, 'flv.mp4') !== false) {
                if($thumb){
                    $mediaUrl .='videoTmp.jpg!vh120';
                }
                else{
                    $mediaUrl .= urlencode($filename);
                }
            } else {
                $mediaUrl = Yii::app()->params['OABaseUrl'].'/images/video_encoding.gif';
            }
        } else {
            if (strpos($filename, $decollator) === false) {
                $mediaUrl .= $thumb ? $filename.'!h120' : $filename.'!'.$picStyle;
            } else {
                $fileArr = explode($decollator, $filename);
                $mediaUrl .= $thumb ? $fileArr[0].'!h120'.$fileArr[1] : $fileArr[0].'!'.$picStyle.$fileArr[1];
            }
        }
        return $mediaUrl;
    }

    static function getMediaUrl($thumb=false, $serverid=0, $type='photo', $filename='', $schoolid='', $classid=0, $weeknum=0)
    {
        $serverConfs = CommonUtils::LoadConfig('CfgMediaServer');
        $sConfs = $serverConfs['servers'][$serverid];
        if($serverid == 1){ # 自建服务器 ID.1
            $mediaUrl = $sConfs["url"] . "s_" . $schoolid . "/c_" . $classid . "/w_" . $weeknum ."/";
            if ($thumb){
                $filename = $type == 'video' ? str_replace('.flv', '.jpg', $filename) : $filename;
                $mediaUrl .= "thumbs/".$filename;
            }
            else {
                $mediaUrl .= $filename;
            }
        }
        else{ # 七牛服务器 目前 ID.20
            $mediaUrl = $sConfs["url"];
            $decollator = ';;';
            if($type == 'video'){
                if (strpos($filename, $decollator) !== false) {
                    $fileArr = explode($decollator, $filename);
                    if($thumb){
                        if($fileArr[1] == 'videoTmp.jpg'){
                            $mediaUrl .= urlencode($fileArr[1]).'!vh120';
                        }else{
                            $mediaUrl .= urlencode($fileArr[1].'=/'.$fileArr[0]).'!vh120';
                        }
                    }
                    else{
                        $mediaUrl .= urlencode($fileArr[2].'=/'.$fileArr[0]);
                    }
                } elseif (strpos($filename, 'o/s') !== false && strpos($filename, 'flv.mp4') !== false) {
                    if($thumb){
                        $mediaUrl .='videoTmp.jpg!vh120';
                    }
                    else{
                        $mediaUrl .= urlencode($filename);
                    }
                } else {
                    $mediaUrl = Yii::app()->params['OABaseUrl'].'/images/video_encoding.gif';
                }
            }
            else{
                if(strpos($filename, $decollator) === false){
                    $mediaUrl .= $thumb ? $filename.'!h120' : $filename.'!w600';
                }
                else{
                    $fileArr = explode($decollator, $filename);
                    $mediaUrl .= $thumb ? $fileArr[0].'!h120'.$fileArr[1] : $fileArr[0].'!w600'.$fileArr[1];
                }
            }
        }
        if(class_exists('Mims', false)){
            if(Mims::unIvy()){
                $mediaUrl = str_replace('!w600', '-ww600', $mediaUrl);
            }
        }
        elseif(class_exists('OA', false)){
            if(in_array($schoolid, self::dsSchoolList())){
                $mediaUrl = str_replace('!w600', '!ww600', $mediaUrl);
            }
        } else {
            if(in_array($schoolid, self::dsSchoolList())){
                $mediaUrl = str_replace('!w600', '!ww600', $mediaUrl);
            }
        }
        return $mediaUrl;
    }

    static function addColon($str, $lang=null){
        if(is_null($lang)){
            $lang = Yii::app()->language;
        }
        $str .= ($lang == 'zh_cn')? '：' : ': ';
        return $str;
    }

    static function hasCNChars($string){
        return preg_match("/[\x7f-\xff]/",$string);
    }

    static function CreateOAUploadUrl($subPath='', $file=''){
        if(isset(Yii::app()->params['OAUploadBaseUrl'])){
            return Yii::app()->params['OAUploadBaseUrl'] . '/' . $subPath . '/' . $file;
        }
        return Yii::app()->params['uploadBaseUrl'] . '/' . $subPath . '/' . $file;
    }

    static function isProduction() {
        if(class_exists('OA', false)) {
            return OA::isProduction();
        } elseif(class_exists('Mims', false)) {
            return Mims::isProduction();
        } elseif (isset(Yii::app()->params['isProduct'])) {
            return Yii::app()->params['isProduct'];
        }
        return false;
    }

    static function processAliYunOSS($files, $rootUploadPath=null, $bucketFlag='public', $enableOSS=null) {
        if(is_null($enableOSS)) {
            $enableOSS = (Yii::app()->params['enableAliYunOSS'] || CommonUtils::isProduction());
        }

        if(!empty($files) && is_array($files) && $enableOSS) {

            empty($rootUploadPath) and $rootUploadPath = Yii::app()->params['uploadPath'];
            $ossSDK = CommonUtils::initOSS($bucketFlag);

            //非正式系统发出的删除请求：不予处理，避免误删
            if (!CommonUtils::isProduction() ){
                unset($files['del']);
            }

            $ossSDK->processBatch($files, $rootUploadPath);
        }
    }

    static function initOSS($bucketFlag) {
        $bucket = 'ivy-www-uploads';
        if($bucketFlag == 'private') {
            $bucket = 'ivy-private-uploads';
        } elseif ($bucketFlag == 'ivyschools') {
            $bucket = 'ivyschools-www-uploads';
        }

        $ossSDK = Yii::createComponent(array(
            'class' => 'common.components.AliYun.OSS',
            'bucket' => $bucket
        ));

        return $ossSDK;
    }

    static function initOSSCS($bucketFlag) {
        $bucket = 'ivy-www-uploads';
        if($bucketFlag == 'private') {
            $bucket = 'ivy-private-uploads';
        } elseif ($bucketFlag == 'ivyschools') {
            $bucket = 'ivyschools-www-uploads';
        }

        $ossSDK = Yii::createComponent(array(
            'class' => 'common.components.AliYuncs.OSS',
            'bucket' => $bucket,
        ));

        return $ossSDK;
    }

    static function createDir($path) {
        if (!file_exists($path)) {
            self::createDir(dirname($path));
            mkdir($path, 0777);
        }
    }

    static function genCCUrlHome($childId=0, $journal=false){
        $key1 = "!ivyOnline";
        $key2 = "journal";

        if (!$childId) return null;
        $str = $childId;
        $str .= $key1;
        if($journal != false){
            $str .= "+" . $key2 . $journal;
        }
        $str = urlencode(base64_encode($str));
        return Yii::app()->urlManager->baseUrl . "/previewCC/" . $str;
    }
    
    /**
     * 判断孩子当前所在班级（小学/幼儿园）
     * @return  Boolean
    */
    public static function isGradeSchool($classid, $kgGrade=false){
        $ret = false;
        if ($classid){
            $classModel = IvyClass::model()->findByPk($classid);
            if ($kgGrade && $classModel->schoolid == 'BJ_QFF') {
                $ret = true;
            }
            $classType = IvyClass::getClassTypes(true,Branch::TYPE_CAMPUS_ELEMENTARY_SCHOOL);
            if (isset($classType[$classModel->classtype])){
                $ret = true;
            }
        }
        return $ret;
    }

    /**
     * 判断孩子所在班级是否为中学班级
     * @return  Boolean
    */
    public static function isMiddleSchool($classid){
        $ret = false;
        if ($classid){
            $classModel = IvyClass::model()->findByPk($classid);
            if (in_array($classModel->classtype, array('e6', 'e7', 'e8', 'e9', 'e10', 'e11', 'e12'))){
                $ret = true;
            }
        }
        return $ret;
    }

    /**
     * 根据pm2.5计算AQI
     * @param $pm25
     * @return int
     */
    static function AQI($pm25)
    {
        if($pm25 < 35){
            $aqi = (50-0)/(35-0)*($pm25-0)+0;
        }
        elseif($pm25 < 75){
            $aqi = (100-51)/(74.9-35.1)*($pm25-35.1)+51;
        }
        elseif($pm25 < 115){
            $aqi = (150-101)/(114.9-75)*($pm25-75)+101;
        }
        elseif($pm25 < 150){
            $aqi = (200-151)/(150.4-115.5)*($pm25-115.5)+151;
        }
        elseif($pm25 < 250){
            $aqi = (300-201)/(250.4-150.5)*($pm25-105.5)+201;
        }
        elseif($pm25 < 350){
            $aqi = (400-301)/(350.4-250.5)*($pm25-250.5)+301;
        }
        elseif($pm25 < 500){
            $aqi = (500-401)/(500.4-350.5)*($pm25-350.5)+401;
        }
        else{
            $aqi = 500;
        }

        return intval($aqi);
    }

    /**
     * [addProducer MQ消息生产者发送消息]
     * @param [type]  $mq_tag        [MQ tag]
     * @param [type]  $mq_key        [MQ key]
     * @param [type]  $mq_body       [MQ body]
     * @param integer $delay_minutes [MQ 延时时间]
     * 
     * example: CommonUtils::addProducer("test-tag", "WechatMsg.test", CJSON::encode(array("name"=>"ljn", "age"=>"20")), 0);
     * 
     */
    static function addProducer($mq_tag, $mq_key, $mq_body, $delay_minutes = 0){

        if (!$mq_tag || !$mq_key || !$mq_body) {
            return false;
        }

        Yii::import("common.components.AliYun.MQ.MQProducer");

        //构造消息发布者
        $producer = new MQProducer($mq_tag, $mq_key, $mq_body, $delay_minutes);
        //启动消息发布者
        $producer->process();

    }

    /**
     * [getAccessToken 根据标识获取远程服务器微信授权accesstoken]
     * @param  [type] $wechatKey [需要获取的账号表示]
     * @return [type]            [accesstoken]
     */
    static function getAccessToken($wechatKey)
    {
        $token = md5(CommonUtils::SECURITY_KEY);
        $data = array(
            "token" => $token,
            "wechatKey" => $wechatKey,
            "authKey" => md5(sprintf("%s%s", $wechatKey, CommonUtils::SECURITY_KEY))
        );
        $url = "http://apps.ivyonline.cn/webapi/wechat/getAccessToken";
        $res = CJSON::decode(CommonUtils::httpGet($url, $data));

        return isset($res['accessToken']) ? $res['accessToken'] : false;
    }

    /**
     * [delAccessToken 根据标识删除远程服务器微信accesstoken]
     * @param  [type] $wechatKey [需要获取的账号表示]
     * @return [type]            [accesstoken]
     */
    static function delAccessToken($wechatKey)
    {
        $token = md5(CommonUtils::SECURITY_KEY);
        $data = array(
            "token" => $token,
            "wechatKey" => $wechatKey,
            "authKey" => md5(sprintf("%s%s", $wechatKey, CommonUtils::SECURITY_KEY))
        );
        $url = "http://apps.ivyonline.cn/webapi/wechat/delToken";
        $res = CJSON::decode(CommonUtils::httpGet($url, $data));
        return isset($res['isDeleted']) && $res['isDeleted'] == 1 ? true : false;
    }

    // 发起 curl 请求
    static function httpGet($url, $data = '') {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_TIMEOUT, 500);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        if ($data) {
            curl_setopt($curl, CURLOPT_POST, 1);
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        }
        curl_setopt($curl, CURLOPT_URL, $url);

        $res = curl_exec($curl);
        curl_close($curl);

        return $res;
    }

    // 发起 curl 请求（json 格式）
    static function httpJson($url, $data = '', $header = array())
    {
        $startTime = microtime(true);
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_TIMEOUT, 500);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        if ($data) {
            curl_setopt($curl, CURLOPT_POST, 1);
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        }
        curl_setopt($curl, CURLOPT_URL, $url);
        $requestHeader = array(
            'Content-Type: application/json',
            'Content-Length: ' . strlen($data)
        );
        if ($header) {
            $requestHeader = array_merge($requestHeader, $header);
        }
        curl_setopt(
            $curl,
            CURLOPT_HTTPHEADER,
            $requestHeader
        );

        // Yii::log($url, CLogger::LEVEL_INFO, 'httpJson.start');

        $res = curl_exec($curl);
        
        // 记录日志
        $path = str_replace(array('http://dsonline.cn:8000/api/', 'https://api.ivyonline.cn/api/'), '', $url);
        $endTime = microtime(true);
        $executionTime = round($endTime - $startTime, 2);
        $logData = sprintf("exec:%s;path:%s;data:%s", $executionTime, $path, $data);
        // 存储curl的错误
        if ($res === false ) {
            Yii::log($logData, CLogger::LEVEL_ERROR, 'httpJson.error');
        } else {
            // var_dump($res);
        //    Yii::log($logData, CLogger::LEVEL_INFO, 'httpJson.res');
        }

        curl_close($curl);
        // Yii::log($logData, CLogger::LEVEL_INFO, 'httpJson.end');

        return $res;
    }

    //可选择请求方法
    static function requestDsOnline2($path, $data = array(), $method = 'post', $uid = false)
    {
        $url = "http://dsonline.cn:8000/api/";
        if (CommonUtils::isProduction()) {
            $url = "https://api.ivyonline.cn/api/";
        }
        $requestUrl = $url . $path;
        $requestData = count($data) > 0 ? json_encode($data) : "";
        $pos = strpos($requestUrl, '?') === false ? '?' : '&';
        $lang = Yii::app()->language == 'zh_cn' ? 'zh_CN' : 'en_US';
        $requestUrl .= $pos.'lang='.$lang;
        if ($uid == false) {
            $uid = Yii::app()->user->getId();
        }
        $token = md5($uid . self::SECURITY_KEY);
        $header = array(
            'OA-User: ' . $uid,
            'OA-Token: ' . $token,
        );
//        var_dump(self::sendHttpUseMethod($requestUrl, $method, $header,$requestData));die;
        return json_decode(self::sendHttpUseMethod($requestUrl, $method, $header,$requestData), true);
    }
    //可选择请求方法 暂时只可以使用 'POST','GET','PUT','DELETE',
    public static function sendHttpUseMethod($url, $method, $header=array(),$data='')
    {
        $method = strtoupper($method);
        $methodArr = array(
            'POST','GET','PUT','DELETE',
        );
        if(!in_array($method,$methodArr)){
            return 'method error';
        }
        $startTime = microtime(true);
        $curl = curl_init($url);
        curl_setopt($curl, CURLOPT_TIMEOUT, 500);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, $method);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        curl_setopt($curl, CURLOPT_URL, $url);
        $requestHeader = array(
            'Content-Type: application/json',
            'Content-Length: ' . strlen($data)
        );
        if ($header) {
            $requestHeader = array_merge($requestHeader, $header);
        }
        curl_setopt(
            $curl,
            CURLOPT_HTTPHEADER,
            $requestHeader
        );
        $response = curl_exec($curl);
        // 记录日志
        $path = str_replace(array('http://dsonline.cn:8000/api/', 'https://api.ivyonline.cn/api/'), '', $url);
        $endTime = microtime(true);
        $executionTime = round($endTime - $startTime, 2);
        $logData = sprintf("exec:%s;path:%s;data:%s", $executionTime, $path, $data);
        if ($response === false ) {
            Yii::log($logData, CLogger::LEVEL_ERROR, 'httpJson.error');
        }
        curl_close($curl);

        return $response;
    }

    /**
     * 向 dsonline 发送请求
     *
     * @param string $path
     * @param array $data
     * @param string $method
     * @return array
     */
    static function requestDsOnline($path, $data = array(), $method = 'post', $uid = false)
    {
        $url = "http://dsonline.cn:8000/api/";
        if (CommonUtils::isProduction()) {
            $url = "https://api.ivyonline.cn/api/";
        }

        $requestUrl = $url . $path;

        $requestData = count($data) > 0 ? json_encode($data) : "";

        $pos = strpos($requestUrl, '?') === false ? '?' : '&';
        $lang = Yii::app()->language == 'zh_cn' ? 'zh_CN' : 'en_US';
        $requestUrl .= $pos.'lang='.$lang;

        if ($uid == false) {
            $uid = Yii::app()->user->getId();
        }
        $token = md5($uid . self::SECURITY_KEY);
        $header = array(
            'OA-User: ' . $uid,
            'OA-Token: ' . $token,
        );
//        var_dump($requestUrl,self::httpJson($requestUrl, $method, $header));
			return json_decode(self::httpJson($requestUrl, $requestData, $header), true);
    }

    /**
     * 向 easyapply 发送请求
     *
     * @param string $path
     * @param array $data
     * @param $branchId
     * @return array
     */
    static function requestApply($path, $data = array(), $branchId)
    {
        $url = "http://pigx-gateway:9999/eaorg/api/data/";
        $header = array(
            'TENANT-ID: 1'
        );
        if (CommonUtils::isProduction()) {
            $url = "https://daystar.admin.isapply.com/eaorg/api/data/";
            $header = array(
                'TENANT-ID: 21990'
            );
            if (in_array($branchId, array('BJ_DS', 'BJ_SLT', 'BJ_QFF'))) {
                $header = array(
                    'TENANT-ID: 21991'
                );
            }
        }
        $requestUrl = $url . $path;

        $jsonData = count($data) > 0 ? json_encode($data) : "";

        $secret = "b09f412314bab7c88cb853004b564651";
        $key = md5($jsonData . $secret);
        $requestData = array(
            'jsonString' => $jsonData,
            'key' => $key
        );

        return json_decode(self::httpJson($requestUrl, json_encode($requestData), $header), true);
    }


    public static function getPdfUrl($customFile, $isCustom = 0, $byLang = false)
    {
        if (!$customFile) {
            return "";
        }
        if (substr($customFile, 0, 4) == 'http') {
            return $customFile;
        }
        if ($isCustom == 1) {
            $object = 'semester_report/custom_pdf/'.$customFile;
        } else {
            if (strpos($customFile, "https://transfer.api.ivykids.cn") === false) {
                $object = $customFile;
                if ($byLang) {
                    $suffix = Yii::app()->language == 'zh_cn' ? 'cn.pdf' : 'en.pdf';
                    $object .= $suffix;
                }
            } else {
                // 如果缓存字段存的链接，解析 url
                $query = parse_url($customFile, PHP_URL_QUERY);
                $queryArray = array();
                if ($query) {
                    parse_str($query, $queryArray);
                }
                if (!isset($queryArray['classid']) || !isset($queryArray['f'])) {
                    return "";
                }
                $classId = $queryArray['classid'];
                $fileName = $queryArray['f'];
                $object = "semester_report/auto_pdf/$classId/$fileName.pdf";
            }
        }
        $oss = CommonUtils::initOSS('private');
        return $oss->get_sign_url($object);
    }

    // 'big', 'normal', 'small'
    public static function childPhotoUrl($valPhoto, $size = 'normal') {
        if (filter_var($valPhoto, FILTER_VALIDATE_URL)) {
            if ($size == 'big') {
                $valPhoto = str_replace('!w200', '', $valPhoto);
            } elseif ($size == 'small') {
                $valPhoto = str_replace('!w200', '!w80', $valPhoto);
            }
            return $valPhoto;
        } else {
            if ($size == 'big') {
                $valPhoto = str_replace('!w200', '', $valPhoto);
                return self::CreateOAUploadUrl("childmgt", $valPhoto);
            }
            if ($size == 'small') {
                $valPhoto = str_replace('!w200', '', $valPhoto);
                $valPhoto .= '!w80';
                return self::CreateOAUploadUrl("childmgt", $valPhoto);
            }
            return self::CreateOAUploadUrl("childmgt", $valPhoto);
        }
    }

    public static function sendEmail($toAddress, $subject, $htmlBody, $tagName = '', $fromAlias = 'Mailer')
    {
        if (!CommonUtils::isProduction()) {
            return true;
        }
        if (!is_array($toAddress) || !$toAddress) {
            throw new Exception("收件人必须存在");
        }
        if (!$subject) {
            throw new Exception("主题不能为空");
        }
        if (!$htmlBody) {
            throw new Exception("内容不能为空");
        }
        $sendUrl = 'http://**************/index.php';

        $accountName = '<EMAIL>';
    
        $data = array(
            "accountName" => $accountName,
            "toAddress" => implode(',', $toAddress),
            "subject" => $subject,
            "htmlBody" => $htmlBody,
            "fromAlias" => $fromAlias,
            "tagName" => $tagName
        );
        $res = json_decode(self::httpJson($sendUrl, json_encode($data)), true);
        if ($res && isset($res['code']) && $res['code'] == 0) {
            return true;
        } else {
            return false;
        }
    }

    public static function allSchoolList()
    {
        return array_merge(self::dsSchoolList(), self::ivySchoolList());
    }

    public static function dsSchoolList($append = array())
    {
        return array_merge(array(
            'BJ_DS',
            'BJ_SLT',
            'BJ_QFF',
        ), $append);
    }
    
    public static function ivySchoolList()
    {
        return array(
            'BJ_IASLT',
            'BJ_OE',
        );
    }

    /**
     * 通用数组排序方法
     *
     * @param array  $array   要排序的数组
     * @param string $field   排序依据的字段
     * @param string $order   排序方向：asc（升序）或 desc（降序）
     * @return array          排序后的数组
     */
    public static function sortByField(array $array, $field, $order = 'asc')
    {
        // 自定义比较函数
        $cmp = function ($a, $b) use ($field, $order) {
            if ($a[$field] == $b[$field]) {
                return 0;
            }
            // 根据排序方向调整比较逻辑
            $result = ($a[$field] < $b[$field]) ? -1 : 1;
            return ($order === 'asc') ? $result : -$result;
        };

        // 使用 usort 对数组进行排序
        usort($array, $cmp);

        return $array;
    }

}
