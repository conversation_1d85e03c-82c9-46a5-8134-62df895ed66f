<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo Yii::t('site', 'Finance Management'); ?></li>
        <li class="active">课后课财务</li>
    </ol>

    <div class="row">
        <!-- 左侧菜单 -->
        <div class="col-md-2 col-sm-2 mb10">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->getMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
                'itemCssClass' => ''
            ));
            ?>
        </div>

        <!-- <div class="col-md-10 col-sm-12"> -->
        <div class="col-md-10 col-sm-12">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->RefundMenu(),
                'id' => 'AttendMenu',
                'htmlOptions' => array('class' => 'nav nav-tabs'),
                'activeCssClass' => 'active',
                'itemCssClass' => ''
            ));
            ?>
            <div class="page-header">
                <h3><?php echo Yii::t('asa', 'submitted'); ?>
                    <small><?php echo Yii::t("asa", "校园已提交，需总部课后课负责人确认（不显示微信退费类型）"); ?></small>
                </h3>
            </div>

            <div>
                <!-- Nav tabs -->
                <ul class="nav nav-tabs" role="tablist">
                    <li role="presentation" class="active"><a href="#IVY" aria-controls="IVY" role="tab" data-toggle="tab">IVY</a></li>
                    <li role="presentation"><a href="#DS" aria-controls="DS" role="tab" data-toggle="tab">DS</a></li>
                </ul>

                <!-- Tab panes -->
                <div class="tab-content">
                    <div role="tabpanel" class="tab-pane active" id="IVY">
                        <?php
                        $this->widget('ext.ivyCGridView.BsCGridView', array(
                            'id' => 'course-staff',
                            'afterAjaxUpdate' => 'js:head.Util.modal',
                            'dataProvider' => $submitted,
                            'template' => "{items}{pager}",
                            //状态为无效时标红
                            'rowCssClassExpression' => '( $data->status == 0 ? "active" : "" )',
                            'colgroups' => array(
                                array(
                                    //                            "colwidth" => array(50, 100, 100, 100, 100, 100, 100, 100, 100, 100),
                                )
                            ),
                            'columns' => array(
                                array(
                                    'name' => '#',
                                    'value' => '$data->id',
                                ),
                                array(
                                    'name' => 'site_id',
                                    'value' => array($this, "getSchoolName"),
                                ),
                                array(
                                    'name' => "childid",
                                    'value' => array($this, "getChildName"),
                                    'type' => "raw"
                                ),
                                array(
                                    'name' => Yii::t('child', '课程名称 (时间表名称)'),
                                    'value' => array($this, "getCourseName"),
                                    'type' => "raw"
                                ),
                                array(
                                    'name' => "refund_total_amount",
                                    'value' => array($this, "getAmount"),
                                ),
                                array(
                                    'name' => 'refund_method',
                                    'value' => array($this, "getRefundMethod"),
                                ),
                                array(
                                    'name' => 'dropout_date',
                                    'value' => 'date("Y-m-d", $data->dropout_date)',
                                ),
                                array(
                                    'name' => 'updated',
                                    'value' => 'date("Y-m-d", $data->updated)',
                                ),
                                array(
                                    'name' => Yii::t('asa', '操作'),
                                    'value' => array($this, "getCourseButton"),
                                ),
                            ),
                        ));
                        ?>
                    </div>
                    <div role="tabpanel" class="tab-pane" id="DS">
                    <?php
                        $this->widget('ext.ivyCGridView.BsCGridView', array(
                            'id' => 'course-staff-ds',
                            'afterAjaxUpdate' => 'js:head.Util.modal',
                            'dataProvider' => $submittedDs,
                            'template' => "{items}{pager}",
                            //状态为无效时标红
                            'rowCssClassExpression' => '( $data->status == 0 ? "active" : "" )',
                            'colgroups' => array(
                                array(
                                    //                            "colwidth" => array(50, 100, 100, 100, 100, 100, 100, 100, 100, 100),
                                )
                            ),
                            'columns' => array(
                                array(
                                    'name' => '#',
                                    'value' => '$data->id',
                                ),
                                array(
                                    'name' => 'site_id',
                                    'value' => array($this, "getSchoolName"),
                                ),
                                array(
                                    'name' => "childid",
                                    'value' => array($this, "getChildName"),
                                    'type' => "raw"
                                ),
                                array(
                                    'name' => Yii::t('child', '课程名称 (时间表名称)'),
                                    'value' => array($this, "getCourseName"),
                                    'type' => "raw"
                                ),
                                array(
                                    'name' => "refund_total_amount",
                                    'value' => array($this, "getAmount"),
                                ),
                                array(
                                    'name' => 'refund_method',
                                    'value' => array($this, "getRefundMethod"),
                                ),
                                array(
                                    'name' => 'dropout_date',
                                    'value' => 'date("Y-m-d", $data->dropout_date)',
                                ),
                                array(
                                    'name' => 'updated',
                                    'value' => 'date("Y-m-d", $data->updated)',
                                ),
                                array(
                                    'name' => Yii::t('asa', '操作'),
                                    'value' => array($this, "getCourseButton"),
                                ),
                            ),
                        ));
                        ?>
                    </div>
                </div>
            </div>

            <div class="page-header">
                <h3><?php echo Yii::t('asa', 'confirmed'); ?>
                    <small><?php echo Yii::t("asa", "总部课后课负责人已确认，需财务人员执行退费操作"); ?></small>
                </h3>
            </div>
            <div>
                <!-- Nav tabs -->
                <ul class="nav nav-tabs" role="tablist">
                    <li role="presentation" class="active"><a href="#IVY2" aria-controls="IVY2" role="tab" data-toggle="tab">IVY</a></li>
                    <li role="presentation"><a href="#DS2" aria-controls="DS2" role="tab" data-toggle="tab">DS</a></li>
                </ul>

                <!-- Tab panes -->
                <div class="tab-content">
                    <div role="tabpanel" class="tab-pane active" id="IVY2">
                        <?php
                        $this->widget('ext.ivyCGridView.BsCGridView', array(
                            'id' => 'course-grid',
                            'afterAjaxUpdate' => 'js:head.Util.modal',
                            'dataProvider' => $confirmed,
                            'template' => "{items}{pager}",
                            //状态为无效时标红
                            'rowCssClassExpression' => '( $data->status == 0 ? "active" : "" )',
                            'colgroups' => array(
                                array(
                                    //                            "colwidth" => array(50, 100, 100, 100, 100, 100, 100, 100, 100, 100),
                                )
                            ),
                            'columns' => array(
                                array(
                                    'name' => '#',
                                    'value' => '$data->id',
                                ),
                                array(
                                    'name' => 'site_id',
                                    'value' => array($this, "getSchoolName"),
                                ),
                                array(
                                    'name' => "childid",
                                    'value' => array($this, "getChildName"),
                                    'type' => "raw"
                                ),
                                array(
                                    'name' => Yii::t('child', '课程名称 (时间表名称)'),
                                    'value' => array($this, "getCourseName"),
                                    'type' => "raw"
                                ),
                                array(
                                    'name' => "refund_total_amount",
                                    'value' => array($this, "getAmount"),
                                ),
                                array(
                                    'name' => 'refund_method',
                                    'value' => array($this, "getRefundMethod"),
                                ),
                                array(
                                    'name' => 'dropout_date',
                                    'value' => 'date("Y-m-d", $data->dropout_date)',
                                ),
                                array(
                                    'name' => 'updated_confirm',
                                    'value' => '$data->updated_confirm ? date("Y-m-d", $data->updated_confirm): ""',
                                ),
                                array(
                                    'name' => Yii::t('asa', '操作'),
                                    'value' => array($this, "getCourseButton"),
                                ),
                            ),
                        ));
                        ?>
                    </div>
                    <div role="tabpanel" class="tab-pane" id="DS2">
                    <?php
                        $this->widget('ext.ivyCGridView.BsCGridView', array(
                            'id' => 'course-grid-ds',
                            'afterAjaxUpdate' => 'js:head.Util.modal',
                            'dataProvider' => $confirmedDs,
                            'template' => "{items}{pager}",
                            //状态为无效时标红
                            'rowCssClassExpression' => '( $data->status == 0 ? "active" : "" )',
                            'colgroups' => array(
                                array(
                                    //                            "colwidth" => array(50, 100, 100, 100, 100, 100, 100, 100, 100, 100),
                                )
                            ),
                            'columns' => array(
                                array(
                                    'name' => '#',
                                    'value' => '$data->id',
                                ),
                                array(
                                    'name' => 'site_id',
                                    'value' => array($this, "getSchoolName"),
                                ),
                                array(
                                    'name' => "childid",
                                    'value' => array($this, "getChildName"),
                                    'type' => "raw"
                                ),
                                array(
                                    'name' => Yii::t('child', '课程名称 (时间表名称)'),
                                    'value' => array($this, "getCourseName"),
                                    'type' => "raw"
                                ),
                                array(
                                    'name' => "refund_total_amount",
                                    'value' => array($this, "getAmount"),
                                ),
                                array(
                                    'name' => 'refund_method',
                                    'value' => array($this, "getRefundMethod"),
                                ),
                                array(
                                    'name' => 'dropout_date',
                                    'value' => 'date("Y-m-d", $data->dropout_date)',
                                ),
                                array(
                                    'name' => 'updated_confirm',
                                    'value' => '$data->updated_confirm ? date("Y-m-d", $data->updated_confirm): ""',
                                ),
                                array(
                                    'name' => Yii::t('asa', '操作'),
                                    'value' => array($this, "getCourseButton"),
                                ),
                            ),
                        ));
                        ?>
                    </div>
                </div>
            </div>



            <div class="page-header">
                <h3><?php echo Yii::t('asa', '错误'); ?>
                    <small><?php echo Yii::t("asa", "退费错误列表"); ?></small>
                </h3>
            </div>

            <?php
            $this->widget('ext.ivyCGridView.BsCGridView', array(
                'id' => 'course_error',
                'afterAjaxUpdate' => 'js:head.Util.modal',
                'dataProvider' => $refundError,
                'template' => "{items}{pager}",
                'colgroups' => array(
                    array(
                        //                            "colwidth" => array(50, 100, 100, 100, 100, 100, 100, 100, 100, 100),
                    )
                ),
                'columns' => array(
                    array(
                        'name' => '#',
                        'value' => '$data->id',
                    ),
                    array(
                        'name' => 'site_id',
                        'value' => array($this, "getSchoolName"),
                    ),
                    array(
                        'name' => "childid",
                        'value' => array($this, "getChildName"),
                        'type' => "raw"
                    ),
                    array(
                        'name' => Yii::t('child', '课程名称 (时间表名称)'),
                        'value' => array($this, "getCourseName"),
                        'type' => "raw"
                    ),
                    array(
                        'name' => "refund_total_amount",
                        'value' => array($this, "getAmount"),
                    ),
                    array(
                        'name' => 'refund_method',
                        'value' => array($this, "getRefundMethod"),
                    ),
                    array(
                        'name' => 'dropout_date',
                        'value' => '$data->dropout_date ? date("Y-m-d", $data->dropout_date): ""',
                    ),
                    array(
                        'name' => 'updated_done',
                        'value' => 'date("Y-m-d", $data->updated_done)',
                    ),
                    array(
                        'name' => Yii::t('asa', '操作'),
                        'value' => array($this, "getCourseButton"),
                    ),
                ),
            ));
            ?>
        </div>
        <!-- </div> -->
    </div>
</div>

<script>
    var modal = '<div class="modal fade" id="modal" class="1123" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog modal-lg" role="document"><div class="modal-content"></div></div></div>';
    $('body').append(modal);

    function cbUpdateCash() {
        // location.reload();
        $.fn.yiiGridView.update('course-staff');
        $.fn.yiiGridView.update('course-staff-ds');
        $.fn.yiiGridView.update('course-grid-ds');
        $.fn.yiiGridView.update('course-grid');
        $.fn.yiiGridView.update('course_error');
        $('#modal').modal('hide');
    }

    $('#myTabs a').click(function(e) {
        e.preventDefault()
        $(this).tab('show')
    })
</script>

<?php
//$this->renderPartial('//layouts/common/branchSelectBottom');
?>