<?php

class IndexController extends ChildBasedController
{
    public $dialogWidth   = 500;
    public $childMainMenu;
    public $errorActionId;
    public $policyApi;
    public $payDateToggle = true; #启明星类校园可选付款日期

    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['childid'])) {
            $params['childid'] = $this->childId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    //Action 权限 KV值
    protected $needValidateAction = array(
        'cancellunch'        => 'oChildGeneralAdmin', //取消午餐
        'processlunch'       => 'oChildGeneralAdmin', //处理取消午餐
        'processassignclass' => 'oChildGeneralAdmin', //处理状态及分班
        'processsavefapiao'  => 'oChildAdminAndFinanceAdmin', //保存发票

        'processwithdraw'       => 'oSeniorFinanceOp', //个人账户提现
        'processcleanbalance'   => 'oSeniorFinanceOp', //个人账户处置
        'processcredittransfer' => 'oSeniorFinanceOp', //个人账户转到兄弟姐妹
        'cleandeposit'          => 'oSeniorFinanceOp', //预缴学费余额清零

        'cancelbinding'        => 'oChildGeneralAdmin',        //取消折扣绑定
        'processcancelbinding' => 'oChildGeneralAdmin', //取消折扣绑定

        'processusecredit'      => 'oChildGeneralAdmin',            //使用个人账户
        'processpartialcashpay' => 'oChildGeneralAdmin',    //处理现金部分付清
        'processcashpay'        => 'oChildGeneralAdmin',            //全部或超额现金付款（超额部分进入个人账户）
        'processvoucher'        => 'oChildGeneralAdmin',            //处理代金卷
        'processcancelPay'      => 'oChildGeneralAdmin',
        'saveStarFlag'          => 'oChildGeneralAdmin',
    );

    protected $ParamBySchool = array(
        'processwithdraw',
        'processcleanbalance',
        'processcredittransfer',
        'cleandeposit',
    );

    public function beforeAction(CAction $action)
    {
        parent::beforeAction($action);
        if (!empty($this->needValidateAction)) {
            $actionId = strtolower($action->getId());
            if (!empty($this->needValidateAction[$actionId])) {
                $params = in_array($actionId, $this->ParamBySchool) ? array('schoolid' => $this->childObj->schoolid) : null;
                if (!Yii::app()->user->checkAccess($this->needValidateAction[$actionId], $params)) {
                    if (Yii::app()->request->isAjaxRequest) {
                        $ret['state'] = 'fail';
                        $ret['message'] = Yii::t("message", 'Unauthorized operation, please contact IT Dept.');
                        echo CJSON::encode($ret);
                        Yii::app()->end();
                    } else {
                        $this->errorActionId = $actionId;
                        $this->forward('//child/message/error');
                    }
                }
            }
        }
        Yii::import('application.components.policy.*');
        $this->policyApi = new PolicyApi($this->childObj->schoolid);
        return true;
    }

    public function init()
    {
        parent::init();
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl.'/modern/css/wizard/bootstrap-nav-wizard.css');
    }

    public function actionIndex()
    {
        $this->render('index');
    }

    //状态及分班，查看和c操作有权限区别
    public function actionStatus()
    {
        Yii::import('common.models.calendar.Calendar');
        Yii::import('common.models.invoice.ChildReserve');
        Yii::import('common.models.portfolio.ChildStudyHistory');

        $hasChangeAccess = Yii::app()->user->checkAccess("oChildGeneralAdmin");
        $schoolAccess = $this->checkBranchAccess($this->childObj->schoolid) || ($this->staff->profile->branch == $this->childObj->schoolid);
        if (Yii::app()->request->isAjaxRequest && Yii::app()->request->isPostRequest) {
            if ($hasChangeAccess && $schoolAccess) {
                Yii::import('common.models.invoice.*');
                $state = Yii::app()->request->getParam('state', 0);
                if ($this->policyApi->setChildStatus($this->childId, $state, 0)) {
                    echo CJSON::encode(array('state' => 'success'));
                }
            } else {
                echo CJSON::encode(array('state' => 'fail', 'message' => Yii::t('message', 'No permission')));
            }
            Yii::app()->end();
        }


        $branch = Branch::model()->findByPk($this->childObj->schoolid);

        $dataProvider = new CActiveDataProvider('ChildStudyHistory', array(
            'criteria'   => array(
                'condition' => 't.childid=:childid',
                'params'    => array("childid" => $this->childId),
                'order'     => 't.timestamp DESC',
                'with'      => array('classNonTogether', 'calendarNonTogether', 'userInfo'),
            ),
            'pagination' => array(
                'pageSize' => 40,
            ),
        ));

        $allBranches = $this->getAllBranch();
        $this->render('status', array(
            'branch'          => $branch,
            'allBranch'       => $allBranches,
            'dataProvider'    => $dataProvider,
            'hasChangeAccess' => $hasChangeAccess,
            'model'           => $this->childObj,
        ));
    }

    /**
     * 预计退学
     */
    public function actionDroppingOut()
    {
        Yii::import('common.models.invoice.*');
        Yii::import('common.models.child.*');
        $this->layout = '//layouts/dialog';
        if (isset($_POST['est_quit_date']) && Yii::app()->request->isPostRequest) {
            $est_quit_date = Yii::app()->request->getParam('est_quit_date', 0);#预计退学时间
            if (empty($est_quit_date)) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '时间不能为空');
                $this->showMessage();
            }
            if (!$this->checkDateFormat($est_quit_date)) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '时间格式不正确');
                $this->showMessage();
            }
//            $this->policyApi->setChildStatus($this->childId, ChildProfileBasic::STATS_DROPPINGOUT,0)
            $est_quit_date .= '23:59:59';
            if (
                $this->policyApi->setChildDroppingoutStatus($this->childId, ChildProfileBasic::STATS_DROPPINGOUT, 0, array('est_quit_date' => strtotime($est_quit_date)))
            ) {
                $this->addMessage('state', 'success');
                $this->addMessage('message', '成功');
                $this->addMessage('refresh', true);
                $this->showMessage();
            }
        }
        if ($this->childObj->est_quit_date) {
            $this->childObj->est_quit_date = date('Y-m-d', $this->childObj->est_quit_date);
        }
        $this->render('dropping_out', array(
            'model' => $this->childObj
        ));
    }

    /**
     * 已经退学
     */
    public function actionAlreadyDroppingOut()
    {
        Yii::import('common.models.invoice.*');
        Yii::import('common.models.child.*');
        $this->layout = '//layouts/dialog';
        if (isset($_POST['quit_date']) && Yii::app()->request->isPostRequest) {
            $quit_date = Yii::app()->request->getParam('quit_date', 0);#退学时间
            if (empty($quit_date)) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '时间不能为空');
                $this->showMessage();
            }
            if (!$this->checkDateFormat($quit_date)) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '时间格式不正确');
                $this->showMessage();
            }
            if ($this->policyApi->setChildDroppingoutStatus($this->childId, ChildProfileBasic::STATS_DROPOUT, 0, array('quit_date' => strtotime($quit_date)))) {
                $this->addMessage('state', 'success');
                $this->addMessage('message', '成功');
                $this->addMessage('refresh', true);
                $this->showMessage();
            }
        }
        if ($this->childObj->quit_date) {
            $this->childObj->quit_date = date('Y-m-d', $this->childObj->quit_date);
        } else {
            if ($this->childObj->est_quit_date) {
                $this->childObj->quit_date = date('Y-m-d', $this->childObj->est_quit_date);
            }
        }
        $this->render('AlreadyDroppingOut', array(
            'model' => $this->childObj
        ));
    }

    function checkDateFormat($date)
    {
        //匹配日期格式
        if (preg_match("/^([0-9]{4})-([0-9]{2})-([0-9]{2})$/", $date, $parts)) {
            //检测是否为日期
            if (checkdate($parts[2], $parts[3], $parts[1])) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    //取消午餐界面
    public function actionCancelLunch()
    {
        Yii::import('common.models.invoice.*');
        Yii::import('common.models.calendar.*');

        $schoolId = $this->childObj->schoolid;
        $branchList = parent::getAllBranch();
        $calendarId = $branchList[$schoolId]['yid'];
        if (Yii::app()->request->getParam('yid')) {
            $calendarId = Yii::app()->request->getParam('yid');
        }
        $calendarOdj = Calendar::model()->findByPk($calendarId);
        $policy = new IvyPolicy('refund', $calendarOdj->startyear, $schoolId);
        global $paymentMappings;
        $lunchType = $paymentMappings['dbFeeType'][FEETYPE_LUNCH];
        if (isset($policy->configs[LUNCH_PERDAY])) {
            $this->render('cancellunch', array("calendarId" => $calendarId, "lunchType" => $lunchType, "childId" => $this->childId, "schoolId" => $schoolId));
        } else {
            $this->render('_noservice');
        }
    }

    //操作取消或恢复午餐
    public function actionProcessLunch()
    {
        if (Yii::app()->request->isPostRequest) {
            Yii::import('common.models.invoice.*');
            Yii::import('common.models.calendar.*');

            if (Yii::app()->request->getIsAjaxRequest()) {
                $day = Yii::app()->request->getPost('day');
                $operate = Yii::app()->request->getPost('operate', 'DO');
                if ($operate == 'DO') {
                    $resultList['status'] = PolicyApi::cancelChildLunchByDate($this->childId, $day);
                } else {
                    $resultList['status'] = PolicyApi::recoverChildLunchByDate($this->childId, $day);
                }
                echo CJSON::encode($resultList);
            }
        }
    }

    //分班
    public function actionProcessAssignClass()
    {
        if (Yii::app()->request->isPostRequest) {
            Yii::import('common.models.invoice.*');
            Yii::import('common.models.portfolio.ChildStudyHistory');
            Yii::import('common.models.*');

            if (Yii::app()->request->getIsAjaxRequest()) {
                $classId = Yii::app()->request->getPost('classId');
                $type = Yii::app()->request->getPost('type');
                $schoolAccess = $this->checkBranchAccess($this->childObj->schoolid) || ($this->staff->profile->branch == $this->childObj->schoolid);
                if (!$schoolAccess && $type == 'current-class-list') {
                    echo CJSON::encode(array('state' => 'fail', 'message' => Yii::t('message', 'No permission')));
                    return;
                }
                if ($type == 'next-class-list') {
                    $model = ChildReserve::model()->findByAttributes(array('childid' => $this->childId));
                    $schoolAccess = $this->checkBranchAccess($model->schoolid) || ($this->staff->profile->branch == $this->childObj->schoolid);
                    if ($model && !$schoolAccess) {
                        echo CJSON::encode(array('state' => 'fail', 'message' => Yii::t('message', 'No permission')));
                        return;
                    }
                }
                if ($type == 'next-class-list' && $classId == 0) {
                    //取消下学年分班
                    $model->stat = 999;
                    $model->save();
                    $ChildStudyHistoryModel = new ChildStudyHistory();
                    $ChildStudyHistoryModel->childid = $model->childid;
                    $ChildStudyHistoryModel->schoolid = $model->schoolid;
                    $ChildStudyHistoryModel->classid = $model->classid;
                    $ChildStudyHistoryModel->calendar = $model->calendar;
                    $ChildStudyHistoryModel->semester = 0;
                    $ChildStudyHistoryModel->stat = 50;
                    $ChildStudyHistoryModel->timestamp = time();
                    $ChildStudyHistoryModel->userid = Yii::app()->user->id;
                    $ChildStudyHistoryModel->from_classid = 0;
                    $ChildStudyHistoryModel->save();

                    Yii::log($this->childId.'-'.Yii::app()->user->id, 'info', 'cancel_next_class');
                    $result['state'] = 'success';
                } else {
                    $result['state'] = PolicyApi::assignClass(array($classId), $this->childId) ? 'success' : 'fail';
                }
                echo CJSON::encode($result);
            }
        }
    }

    //操作发票信息
    public function actionProcessSavefapiao()
    {
        if (Yii::app()->request->isPostRequest) {
            $id = Yii::app()->request->getParam('id', 0);
            $number = Yii::app()->request->getParam('number', '');
            $title = Yii::app()->request->getParam('title', 0);
            $savetobasic = Yii::app()->request->getParam('savetobasic', 0);

            Yii::import('common.models.invoice.*');

            $model = InvoiceTransaction::model()->findByPk($id);
            $model->invoice_number = $number;
            $model->invoice_title = $title;
            if ($model->save()) {
                if ($savetobasic) {
                    ChildProfileBasic::model()->updateByPk($this->childId, array('invoice_title' => $title));
                }
                $this->addMessage('state', 'success');
                $this->addMessage('data', array('number' => $model->invoice_number, 'title' => $model->invoice_title));
            } else {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '保存失败');
            }
            $this->showMessage();
        }
    }

    public function actionCCUrl($childid)
    {
        $url = OA::genCCUrlHome($childid);
        $this->redirect($url);
    }

    //个人账户余额提现
    public function actionProcessWithDraw()
    {
        Yii::import('common.models.invoice.*');
        Yii::import('common.models.child.*');
        $this->layout = '//layouts/dialog';

        $model = new ChildCreditMemo;

        if (isset($_POST['ChildCreditMemo']) && Yii::app()->request->isPostRequest) {
            $model->attributes = $_POST['ChildCreditMemo'];
            if ($model->validate()) {
                if (PolicyApi::childOutCash($this->childId, $model->amount)) {
                    if ($model->memo) {
                        $model->save();
                    }
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', '成功');
                    $this->addMessage('refresh', true);
                } else {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '失败');
                }
            } else {
                $this->addMessage('state', 'fail');
                $errs = current($model->getErrors());
                if ($errs) {
                    $this->addMessage('message', $errs[0]);
                }
            }
            $this->showMessage();
        }

        $this->render('withdraw', array('model' => $model));
    }

    //家长放弃个人账户余额
    public function actionProcessCleanBalance()
    {
        Yii::import('common.models.invoice.*');
        $this->layout = '//layouts/dialog';

        $model = new CleanBanlanceForm;

        if (isset($_POST['CleanBanlanceForm'])) {
            if ($_POST['CleanBanlanceForm']['confirm']) {
                if ($this->policyApi->childBalanceDisposal($this->childId)) {
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', '成功');
                    $this->addMessage('refresh', true);
                } else {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '失败');
                }
            } else {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '请勾选确认继续');
            }
            $this->showMessage();
        }

        $this->render('cleanbalance', array('model' => $model));
    }

    //个人账户兄弟姐妹间转移
    public function actionProcessCreditTransfer()
    {
        Yii::import('common.models.invoice.*');
        Yii::import('common.models.child.CreditTransfer');
        $this->layout = '//layouts/dialog';

        $siblings = PolicyApi::getSiblings($this->childId);
        if (isset($siblings['isRelation']) && $siblings['isRelation'] === true) {
            $sibs = array();
            foreach ($siblings['info'] as $child) {
                $sibs[$child->childid] = CommonUtils::autoLang($child->name_cn, $child->first_name_en.' '.$child->last_name_en);
            }

            $model = new BanlanceToSiblingsForm;

            if (isset($_POST['BanlanceToSiblingsForm'])) {
                $model->attributes = $_POST['BanlanceToSiblingsForm'];
                if ($model->validate()) {
                    if (PolicyApi::transferChildBalance($this->childObj, $siblings['info'][$model->to], $model->amount)) {
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', '成功');
                        $this->addMessage('refresh', true);
                    } else {
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', '失败');
                    }
                } else {
                    $this->addMessage('state', 'fail');
                    $errs = current($model->getErrors());
                    if ($errs) {
                        $this->addMessage('message', $errs[0]);
                    }
                }
                $this->showMessage();
            }

            $this->render('balancetosiblings', array('model' => $model, 'sibs' => $sibs));
        }
    }

    //取消折扣绑定确认界面
    public function actionCancelBinding($bindingId)
    {
        $this->layout = '//layouts/dialog';
        Yii::import('common.models.invoice.*');
        $bindingDiscount = ChildDiscountLink::model()->findByPk($bindingId);
        if ($bindingDiscount->childid == $this->childId) {
            $criteria = new CDbCriteria();
            $criteria->compare('discount_id', $bindingDiscount->discount_id);
            $criteria->compare('childid', $this->childId);
            $criteria->compare('status', array(Invoice::STATS_UNPAID, Invoice::STATS_PARTIALLY_PAID));
            $items = Invoice::model()->findAll($criteria);
            $this->render('cancel_binding', array('model' => $items, 'bindingDiscount' => $bindingDiscount));
        }
    }

    //处理取消折扣绑定
    public function actionProcessCancelBinding($bindingId)
    {
        if (Yii::app()->request->isPostRequest && Yii::app()->request->isAjaxRequest) {
            Yii::import('common.models.invoice.*');
            $bindingDiscount = ChildDiscountLink::model()->findByPk($bindingId);
            if ($bindingDiscount->childid == $this->childId) {
                $discountId = $bindingDiscount->discount_id;
                if ($bindingDiscount->delete()) {
                    Yii::log(Yii::app()->user->id.'cancel-'.$this->childId.'discount-'.$discountId, 'info', 'discount.cancel');
                    if (isset($_POST['Invoice']['invoice_id'])) {
                        Yii::import('application.components.policy.PolicyApi');
                        foreach ($_POST['Invoice']['invoice_id'] as $val) {
                            if ($val) {
                                PolicyApi::cancelInvoice($val);
                            }
                        }
                    }
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', '成功');
                    $this->addMessage('data', array('childId' => $this->childId));
                    $this->addMessage('callback', 'callback');
                } else {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '失败');
                }
                $this->showMessage();
            }
        }
    }

    //POS机支付 无需权限控制
    public function actionUnpaid($t = 'pos')
    {
        $yeePayCfg = CommonUtils::LoadConfig('CfgYeePayGlobal');
        $POSenabled = ($t == 'pos' && isset($yeePayCfg[$this->childObj->schoolid])) ? true : false;

        Yii::import('common.models.invoice.*');

        $usePartial = Yii::app()->request->getParam('usePartial', '');
        $usePartial = ($usePartial == 'usePartial') ? true : false;

        $dataProvider = new CActiveDataProvider('Invoice', array(
            'criteria'   => array(
                'condition' => 't.childid=:childid AND t.inout=:inout AND t.status in(10, 30) AND t.payment_type != :preschool_subsidy',
                'params'    => array("childid" => $this->childId, ":inout" => 'in', ':preschool_subsidy' => 'preschool_subsidy'),
                'order'     => 't.timestamp DESC',
                'with'      => 'paidSum',
            ),
            'pagination' => array(
                'pageSize' => 2000,
            ),
        ));

        $this->render('unpaid', array("POSenabled" => $POSenabled, "dataProvider" => $dataProvider, 'usePartial' => $usePartial, 't' => $t));
    }

    //使用个人账户
    public function actionProcessUseCredit()
    {
        if (Yii::app()->request->isPostRequest && Yii::app()->request->isAjaxRequest) {
            $id = Yii::app()->request->getParam('id', 0);


            Yii::import('common.models.invoice.*');

            $model = Invoice::model()->findByPk($id);
            $ret = $this->policyApi->Pay(array($model), InvoiceTransaction::TYPE_USERCREDIT);
            if (!$ret) {
                $this->addMessage('state', 'success');
            } else {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', $ret);
            }
            $this->showMessage();
        }
    }

    //处理现金部分付清
    public function actionProcessPartialCashPay()
    {
        if (Yii::app()->request->isPostRequest && Yii::app()->request->isAjaxRequest) {
            $id = Yii::app()->request->getParam('id', 0);
            $amount = Yii::app()->request->getParam('amount', 0);

            Yii::import('common.models.invoice.*');

            $model = Invoice::model()->findByPk($id);

            if ($amount > 0.001 && $amount < ($model->amount - $model->paidSum)) {
                if ($this->childObj->school->type == 50) {
                    $this->addMessage('state', 'fail');
                    $payType = isset($_POST['transactiontype']) ? $_POST['transactiontype'] : '';
                    $paydate = isset($_POST['paydate']) ? $_POST['paydate'] : '';
                    if (!in_array($payType, array_keys($this->getDsPayType()))) {
                        $this->addMessage('message', '请选择付款类型');
                        $this->showMessage();
                    }
                    if ($this->payDateToggle == true && empty($paydate)) {
                        $this->addMessage('message', '请选择付款日期');
                        $this->showMessage();
                    } else {
                        $model->payDate = strtotime($paydate);
                    }
                } else {
                    $payType = InvoiceTransaction::TYPE_CASH;
                }
                if (!$this->policyApi->Pay(array($model), $payType, $amount)) {
                    $this->addMessage('state', 'success');
                } else {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '付款失败');
                }
            } else {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '输入金额请小于账单需支付金额并且大于0');
            }
            $this->showMessage();
        }
    }

    /**
     * 将未付账单（包括部分付清）选中之后提交到这里生成账单信息
     * @return    Object        Description
     */
    public function actionProcessYeePayOrderCreation()
    {
        if (Yii::app()->request->isPostRequest && Yii::app()->request->isAjaxRequest) {
            Yii::import('common.models.invoice.*');
            Yii::import('common.models.yeepay.*');
            extract($_POST);

            //$posInvoiceId_cid 账单id;
            //$finalAmount 账单id=>j金额;
            //$usePartial 是否使用分拆账单
            //$invoiceCount 选中的账单数量
            //$invoiceTotalAmount 选中的账单数量的金额

            sort($posInvoiceId_cid);
            $invoices = Invoice::model()->with('paidSum')->findAllByPk($posInvoiceId_cid);

            $yeePayCfg = CommonUtils::LoadConfig('CfgYeePayGlobal');
            //print_r($yeePayCfg);

            $canProcced = true;
            $count = $totalAmount = 0;

            if ($usePartial && count($invoices) !== 1) {
                $canProcced = false;
            }

            foreach ($invoices as $invoice) {
                //账单状态已经不是待付款状态
                if (!in_array($invoice->status, array(Invoice::STATS_UNPAID, Invoice::STATS_PARTIALLY_PAID))) {
                    $canProcced = false;
                    break;
                }
                //需付款于数据库中j计算出的需付款金额不一致
                if (!$usePartial) {
                    if (!isset($finalAmount[$invoice->invoice_id]) || (OA::formatMoney($finalAmount[$invoice->invoice_id]) != (OA::formatMoney($invoice->amount - $invoice->paidSum)))) {
                        $canProcced = false;
                        break;
                    }
                } else {
                    if ($finalAmount[$invoice->invoice_id] >= ($invoice->amount - $invoice->paidSum)) {
                        $canProcced = false;
                        break;
                    }
                }
                $count++;
                $totalAmount += $finalAmount[$invoice->invoice_id];
            }
            if (($invoiceCount != $count) || ($invoiceTotalAmount != $totalAmount)) {
                $canProcced = false;
            }
            if (!$canProcced) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t("payment", "Page expired, please reload."));
                $this->showMessage();
            }

            $orderPrefix = strval($yeePayCfg[$this->childObj->schoolid]['number_code']).sprintf('%06s', $posInvoiceId_cid[0]);
//			$orderId = YeepayOrder::model()->genOrderID($orderPrefix);
//			echo $orderId;

            $yeeOrder = new YeepayOrder;
            $yeeOrder->orderId = $yeeOrder->genOrderID($orderPrefix);
            $yeeOrder->payable_amount = $totalAmount;
            $yeeOrder->rb_BankId = 'POS-NET';
            $yeeOrder->schoolId = $this->childObj->schoolid;
            $yeeOrder->childId = $this->childId;
            $yeeOrder->operatorId = Yii::app()->user->id;
            $yeeOrder->payment_method = InvoiceTransaction::TYPE_POS;
            $yeeOrder->status = 0;
            $yeeOrder->orderTime = time();
            $yeeOrder->updateTime = time();
            $yeeOrder->save();

            foreach ($posInvoiceId_cid as $key) {
                $model = new YeepayOrderItem;
                $model->it_orderId = $yeeOrder->orderId;
                $model->status = 0;
                $model->orderTime = time();
                $model->updateTime = time();
                $model->invocieId = $key;
                $model->payable_amount = $finalAmount[$key];
                $model->save();
            }

            $this->addMessage('state', 'success');
            $this->addMessage('data', $yeeOrder->orderId);
            $this->showMessage();
        }
    }

    //提交到易宝，生成POS机账单
    public function actionPostYeePay($orderId = '')
    {
        $this->layout = '//layouts/dialog';
        $this->dialogWidth = 600;
        Yii::import('common.models.yeepay.*');
        Yii::import('common.components.yeepay.*');

        $yeeOrder = YeepayOrder::model()->findByPk($orderId);
        if ($yeeOrder->status == 0) {
            $yeePay = new YeepayCommon;
            $yeePay->init($this->childObj->schoolid);
            $url = $yeePay->getReqUrlOnline();
            $ret = array(
                "p0_Cmd"          => $yeePay->getP0Cmd(),
                "p1_MerId"        => $yeePay->getP1MerId(),
                "p2_Order"        => $yeeOrder->orderId,
                "p3_Amt"          => $yeeOrder->payable_amount,
                "p4_Cur"          => $yeePay->getP4Cur(),
                "p5_Pid"          => '',
                "p6_Pcat"         => '',
                "p7_Pdesc"        => '',
                "p8_Url"          => $yeePay->getP8Url(),
                "p9_SAF"          => $yeePay->getP9Saf(),
                "pd_FrpId"        => 'POS-NET',
                "pr_NeedResponse" => $yeePay->getPrNeedResponse(),
                "hmac"            => $yeePay->getReqHmacString($yeeOrder->orderId, $yeeOrder->payable_amount, '', '', '', '', 'POS-NET'),
            );

            $this->render('postyeepay', array('url' => $url, 'ret' => $ret));
        }
    }

    //全部或超额现金付款（超额部分进入个人账户）
    public function actionProcessCashPay()
    {
        if (Yii::app()->request->isPostRequest && Yii::app()->request->isAjaxRequest) {
            Yii::import('common.models.invoice.*');
            extract($_POST);

            $this->addMessage('state', 'fail');
            if (isset($posInvoiceId_cid) && $posInvoiceId_cid) {
                $schoolType = $this->childObj->school->type;
                $criteria = new CDbCriteria;
                $criteria->compare('invoice_id', $posInvoiceId_cid);
                $models = Invoice::model()->findAll($criteria);
                foreach ($models as $model) {
                    $tAmount += ($model->amount - $model->paidSum);
                    if ($schoolType == 50 && isset($paydate) && $paydate) {
                        $model->payDate = strtotime($paydate);
                    }
                }

                if (($amount - $tAmount) >= 0 || abs($amount - $tAmount) < 0.001) {
                    if ($schoolType == 50) {
                        $payType = isset($_POST['transactiontype']) ? $_POST['transactiontype'] : '';
                        if (!in_array($payType, array_keys($this->getDsPayType()))) {
                            $this->addMessage('message', '请选择付款类型');
                            $this->showMessage();
                        }
                        if ($this->payDateToggle == true && isset($paydate) && empty($paydate)) {
                            $this->addMessage('message', '请选择付款日期');
                            $this->showMessage();
                        }
                    } else {
                        $payType = InvoiceTransaction::TYPE_CASH;
                    }
                    if (!$this->policyApi->Pay($models, $payType, $amount)) {
                        $this->addMessage('state', 'success');
                    } else {
                        $this->addMessage('message', '付款失败');
                    }
                } else {
                    $this->addMessage('message', '实际付款不够，请使用组合付款');
                }
            } else {
                $this->addMessage('message', Yii::t("payment", "Please select at least one invoice"));
            }

            $this->showMessage();
        }
    }

    public function actionCleanDeposit($startyear = 0)
    {
        $this->layout = '//layouts/dialog';
        Yii::import('common.models.invoice.*');
        Yii::import('common.models.calendar.*');

        $model = new CleanDepositForm;

        if (isset($_POST['CleanDepositForm']) && Yii::app()->request->isPostRequest) {
            $model->attributes = $_POST['CleanDepositForm'];
            if ($model->confirm) {
                $criteria = new CDbCriteria;
                $criteria->compare('startyear', $model->startyear);
                $models = Calendar::model()->findAll($criteria);
                $yids = CHtml::listData($models, 'yid', 'yid');

                $ret = array();
                if ($yids) {
                    $criteria = new CDbCriteria;
                    $criteria->compare('yid', $yids);
                    $criteria->compare('childid', $this->childId);
                    $items = DepositHistory::model()->findAll($criteria);
                    foreach ($items as $item) {
                        $data = isset($ret[$item->yid]) ? $ret[$item->yid] : 0;
                        if ($item->inout == 'in') {
                            $ret[$item->yid] = $data + $item->amount;
                        } else {
                            $ret[$item->yid] = $data - $item->amount;
                        }

                        if ($ret[$item->yid] == 0) {
                            unset($ret[$item->yid]);
                        }
                    }

                    if ($ret) {
                        foreach ($ret as $yid => $amount) {
                            $dModel = new DepositHistory;
                            $dModel->childid = $this->childId;
                            $dModel->yid = $yid;
                            $dModel->amount = $amount;
                            $dModel->balance = 0;
                            $dModel->inout = 'out';
                            $dModel->tran_id = 0;
                            $dModel->timestamp = time();
                            $dModel->create_uid = Yii::app()->user->id;
                            $dModel->ufida_status = 0;
                            $dModel->save();
                        }
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', '成功');
                        $this->addMessage('refresh', true);
                    }
                }
            } else {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '请勾选确认继续');
            }
            $this->showMessage();
        }

        $depositList = PolicyApi::getDepositAmount($this->childId, 0, true);
        $model->amount = isset($depositList[$startyear]) ? $depositList[$startyear]['amount'] : 0;
        $model->startyear = $startyear;

        $this->render('cleandeposit', array('model' => $model));
    }

    /*
     * 处理代金卷业务
     */
    public function actionProcessVoucher()
    {
        $this->layout = '//layouts/dialog';
        Yii::import('application.components.policy.*');
        Yii::import('common.models.invoice.*');

        $modelForm = new VoucherForm;

        $id = Yii::app()->request->getParam('id', 0);
        $childid = Yii::app()->request->getParam('childid', 0);
        $model = Invoice::model()->findByPk($id);
        $voucherAmount = 0;
        $flag = Invoice::showVoucher($model, $model->invoice_id, $voucherAmount);

        if (isset($_POST['VoucherForm'])) {
            if ($_POST['VoucherForm']['confirm']) {
                if ($childid == $model->childid && $flag == true) {
                    //处理账单的业务（代金卷、预缴学费）
                    if (!$this->policyApi->processVoucher($model)) {
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', Yii::t('message', 'success'));
                        $this->addMessage('refresh', true);
                    } else {
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message', 'Failed!'));
                    }
                } else {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '非法提交！');
                }
            } else {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '请勾选确认继续');
            }
            $this->showMessage();
        }

        $this->render('voucher', array('model' => $modelForm, 'voucherAmount' => $voucherAmount));
    }

    # 返回DayStart的可选收款类型
    public function getDsPayType()
    {
        $payType = $GLOBALS['paymentMappings']['transactionType'];
        unset($payType[InvoiceTransaction::TYPE_USERCREDIT]);
        unset($payType[InvoiceTransaction::TYPE_POS]);
        unset($payType[InvoiceTransaction::TYPE_ONLINEPAYMENT]);
        unset($payType[InvoiceTransaction::TYPE_TOCREDIT]);
        unset($payType[InvoiceTransaction::TYPE_VOUCHER]);
        return $payType;
    }

    public function actionProcessCancelPay()
    {
        if (Yii::app()->request->isAjaxRequest && Yii::app()->request->isPostRequest) {
            Yii::import('common.models.invoice.*');
            $id = Yii::app()->request->getPost('id', 0);
            $model = Invoice::model()->with('invoiceTransaction')->findByAttributes(array('invoice_id' => $id, 'childid' => $this->childId));
            if ($model != null && !$this->policyApi->unPartPay($model)) {
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', 'success'));
            } else {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', 'Failed!'));
            }
            $this->showMessage();
        }
    }

    public function invoiceLink($data, $row)
    {
        if ($data->invoice_id) {
            echo CHtml::link(Yii::app()->controller->policyApi->renderFeeType($data->itemname), array(
                "/child/invoice/viewInvoice", "invoiceid" => $data->invoice_id
            ), array('class' => 'J_dialog'));
        } else {
            echo Yii::app()->controller->policyApi->renderFeeType($data->itemname);
        }
    }


    /**
     * 设置不同的标签
     *
     */
    public function actionSetFlag()
    {
        $flag1 = Yii::app()->request->getPost('setFlagData1', array());//设置为1的 添加
        $flag0 = Yii::app()->request->getPost('setFlagData0', array());//设置为0的 取消
        $dynamicFlag1 = Yii::app()->request->getPost('setDynamicFlagData1', array());//设置动态标签
        $dynamicFlag0 = Yii::app()->request->getPost('setDynamicFlagData0', array());//取消动态标签
        $data = array(
            'child_id'            => $this->childObj->childid,
            'setFlagData1'        => $flag1,
            'setFlagData0'        => $flag0,
            'setDynamicFlagData1' => $dynamicFlag1,
            'setDynamicFlagData0' => $dynamicFlag0,
        );
        $res = CommonUtils::requestDsOnline2('label/editChild', $data, 'post');
        if ($res['code']==0) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', '成功');
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '失败');
        }
        $this->showMessage();
    }


    /**
     * 获取孩子的标签
     */
    public function actionGetFlag()
    {
        $parentFlag = array();
        $parentMainInfo = CommonUtils::requestDsOnline2('label/child', array('child_id' => $this->childObj->childid), 'get');
        $mainInfo = $parentMainInfo['data']['main'];
        $pushTeacherInfo = $parentMainInfo['data']['push_teacher'];
        foreach ($mainInfo['label'] as $item) {
            $parentFlag[] = (int) $item['flag'];
        }
        $allConfig = CommonUtils::requestDsOnline('child/labelConfig', array('school_id' => ''));
        $canSetFlag = array();
        foreach ($allConfig['data'] as $k => $item) {
            $teacher_ids = explode(',', $item['teacher_ids']);
            if (in_array(Yii::app()->user->id, $teacher_ids) || empty($item['teacher_ids'])) {
                $canSetFlag[$k] = $item;
            }
        }
        $data = array(
            'mainInfo'     => $mainInfo,
            'flagInfo'     => $parentFlag,
            'flagConfig'   => $canSetFlag,
            'push_teacher' => $pushTeacherInfo,
            'school_id'    => $this->childObj->schoolid
        );
        $this->addMessage('state', 'success');
        $this->addMessage('message', '成功');
        $this->addMessage('data', $data);
        $this->showMessage();
    }

    public function remote($requestUrl, $requestData = array())
    {
        $res = CommonUtils::requestDsOnline($requestUrl, $requestData);
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        }
        $this->showMessage();
    }

    public function remote2($requestUrl, $method = 'post', $requestData = array())
    {
        $res = CommonUtils::requestDsOnline2($requestUrl, $requestData, $method);
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        }
        $this->showMessage();
    }
}