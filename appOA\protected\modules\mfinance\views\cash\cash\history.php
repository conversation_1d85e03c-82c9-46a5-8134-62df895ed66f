<?php
//查询某一学校交接历史
$crit = new CDbCriteria();
$crit->compare('t.schoolid', $this->branchId);
$crit->with = 'userInfo';
$crit->order = 't.timestmpe DESC';
$provider = new CActiveDataProvider('CashHandoverHistory', array(
    'criteria' => $crit,
    'pagination'=>array(
        'pageSize'=>15,
    ),
));
?>
<div class="panel panel-default">
    <div class="panel-heading">
        <h3 class="panel-title">已交接历史</h3>
    </div>
    <div class="panel-body">
        <?php
            $this->widget('zii.widgets.CListView', array(
                'id' => 'history-list',
                'dataProvider' => $provider,
                'enableSorting' => false,
                'itemView'=> 'cash/_history_list',
                'itemsTagName'=>'ul',
                'template' => "{summary}{items}{pager}",
                'itemsCssClass'=>'list-group',
                'pagerCssClass'=>'bs-pager',
                'pager'=>array('class'=>'BsCLinkPager'),
            ));
        ?>
    </div>
</div>

<!--modal template-->
<div class="modal fade bs-example-modal-lg" id="historyViewModal" tabindex="-1" role="dialog" aria-labelledby="historyViewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel">现金交接明细 <small></small><a href="#" onclick="startPrint()"> [打印]</a><a href="#" onclick="exportTable()"> [导出]</a></h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <table class="table table-hover" id="print-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>标题</th>
                                    <th>发票号</th>
                                    <th><?php echo Yii::t('labels','Name');?></th>
                                    <th>项目名称</th>
                                    <th>付款日期</th>
                                    <th>期间</th>
                                    <th>金额</th>
                                </tr>
                            </thead>
                            <tbody id="history-data">

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--history view template-->
<script type="text/template" id="history-view">
    <tr>
        <td><%=id%></td>
        <td><%=title%></td>
        <td><%=invoice_number%></td>
        <td><%=name%></td>
        <td><%=payment_type%></td>
        <td><%=timestampe%></td>
        <td><%=date%></td>
        <td><%=amount%></td>
    </tr>
</script>

<script>
var historyViewData = {};
var view = _.template($("#history-view").html());
$(function(){
    historyView = function(_this){
        var id = $(_this).data('id');
        if (_.isUndefined(loadHistoryData(id))){
            loadHistoryData(id);
        }
        $("#myModalLabel small").html(historyViewData[id].handover_date+' 交接 '+historyViewData[id].count+" 笔现金( "+historyViewData[id].amount+" )");
        $("#history-data").empty();
        _.each(historyViewData[id].info,function(v,k){
            $("#history-data").append(view(v));
        })
        openModal();
    }

    loadHistoryData = function(id){
        delete historyViewData[id];
        $.ajax({
            type: "POST",
            dataType: "json",
            async: false,
            url: "<?php echo $this->createUrl('//mcampus/cash/getHistoryData'); ?>",
            data: {id: id}
        }).done(function(data) {
            historyViewData[id] = data;
        });
    }

    openModal = function(){
        $("#historyViewModal").modal();
    }

    startPrint = function () {
        $("#historyViewModal").printThis();
    }

    exportTable = function () {
        var wb = XLSX.utils.book_new();
        var ws = XLSX.utils.table_to_sheet(document.getElementById('print-table'));
        XLSX.utils.book_append_sheet(wb, ws, "table");
        // generate Blob
        var wbout = XLSX.write(wb, {bookType: 'xlsx', type: 'array'});
        var blob = new Blob([wbout], {type: 'application/octet-stream'});
        // save file
        var link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = 'table.xlsx';
        link.click();
    }
})
</script>