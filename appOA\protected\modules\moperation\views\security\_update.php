<?php
$SecurityType = CommonUtils::LoadConfig('CfgSecurity');
$type = array();
foreach ($SecurityType['type'] as $k=>$item) {
    $type[$k] = (Yii::app()->language == 'zh_cn') ? $item['cn'] : $item['en'] ;
}

$form=$this->beginWidget('CActiveForm', array(
    'id'=>'visits-form',
    'enableAjaxValidation'=>false,
    'action' => $this->createUrl('addSafetySelf',array("id"=>$model->id)),
    'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
));

?>
<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
    <h4 class="modal-title"><?php echo Yii::t('site','增加检查项目')?></h4>
</div>
<div class="modal-body">

    <!-- 中文名称 -->
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'type'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->DropDownList($model, 'type', $type, array('maxlength' => 255, 'class' => 'form-control', 'empty' => '请选择类型')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'title_cn'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'title_cn',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <!-- 英文名称 -->
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'title_en'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'title_en',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <!-- 状态 -->
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'status'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->checkbox($model,'status', array('1'=>"开启"),array('template'=>'<label class="radio-inline">{input}{label}</label>','separator'=>'')); ?>
        </div>
    </div>
</div>
<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
    <button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
</div>

<?php $this->endWidget(); ?>
<script>
    // 回调：添加成功
    function cbSecurity() {
        $('#modal').modal('hide');
        $.fn.yiiGridView.update('security_type');
        location.reload()
    }
</script>
