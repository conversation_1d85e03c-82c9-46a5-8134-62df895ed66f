<?php

/**
 * This is the model class for table "ivy_e_classroom".
 *
 * The followings are the available columns in table 'ivy_e_classroom':
 * @property integer $id
 * @property string $schoolid
 * @property string $code
 * @property integer $capacity
 * @property integer $is_multifunc
 * @property integer $is_hall
 * @property integer $is_lib
 * @property integer $status
 * @property string $desc
 */
class EClassroom extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return EClassroom the static model class
	 */
	public $type = null;
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_e_classroom';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('schoolid, code, capacity, is_multifunc, is_hall, is_lib, status', 'required','on'=>'add,update'),
			array('capacity, is_multifunc, is_hall, is_lib, status', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>10),
			array('code', 'length', 'max'=>32),
			array('desc', 'length', 'max'=>255),
			array('code', 'unique', 'on'=>'add', 'criteria' =>array('condition' =>'schoolid=:schoolid','params' => array(':schoolid'=> $this->schoolid))),
//			array('code', 'checkCodel'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, schoolid, code, capacity, is_multifunc, is_hall, is_lib, status, desc', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'schoolid' => 'Schoolid',
			'code' => Yii::t('labels','Room Code'),
			'capacity' => Yii::t('labels','Capacity'),
			'is_multifunc' => Yii::t('labels','Multifunctional Room'),
			'is_hall' => Yii::t('labels','Hall'),
			'is_lib' => Yii::t('labels','Library'),
			'status' => Yii::t('labels','Status'),
			'desc' => Yii::t('labels','Description'),
			'type' => Yii::t('labels','Type'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('code',$this->code,true);
		$criteria->compare('capacity',$this->capacity);
		$criteria->compare('is_multifunc',$this->is_multifunc);
		$criteria->compare('is_hall',$this->is_hall);
		$criteria->compare('is_lib',$this->is_lib);
		$criteria->compare('status',$this->status);
		$criteria->compare('desc',$this->desc,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	static public function getStatus($id=-1){
		$ret = array('1'=>Yii::t('global','Active'),'0'=>Yii::t('global','Inactive'));
		return ($id>=0) ? $ret[$id] : $ret;
	}
}