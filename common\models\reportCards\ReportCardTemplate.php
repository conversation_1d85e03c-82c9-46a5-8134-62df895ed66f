<?php

/**
 * This is the model class for table "ivy_reports_template".
 *
 * The followings are the available columns in table 'ivy_reports_template':
 * @property integer $id
 * @property string $title_en
 * @property string $title_cn
 * @property string $for_age
 * @property string $branch_id
 * @property integer $in_use
 * @property integer $update_user
 * @property integer $active
 * @property integer $updated
 */
class ReportCardTemplate extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_reports_template';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('title_en, title_cn, branch_id, update_user, active, updated', 'required'),
			array('in_use, update_user, active, updated', 'numerical', 'integerOnly'=>true),
			array('title', 'length', 'max'=>255),
			array('for_age', 'length', 'max'=>6),
			array('for_age', 'safe'),
			array('branch_id', 'length', 'max'=>32),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, title, for_age, branch_id, in_use, update_user, active, updated', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'forAges' => array(self::HAS_MANY, 'ReportCardTemplateClass', 'template_id')
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'title_en' => Yii::t('labels', 'Title (English)'),
			'title_cn' => Yii::t('labels', 'Title (Chinese)'),
			'for_age' => Yii::t('labels', 'For Class'),
			'branch_id' => Yii::t('labels', 'Campus'),
			'in_use' => Yii::t('labels', 'In Use'),
			'update_user' => 'Update User',
			'active' => Yii::t('global', 'Active'),
			'updated' => 'Updated',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('title_en',$this->title_en,true);
		$criteria->compare('title_cn',$this->title_cn,true);
		$criteria->compare('for_age',$this->for_age,true);
		$criteria->compare('branch_id',$this->branch_id,true);
		$criteria->compare('in_use',$this->in_use);
		$criteria->compare('update_user',$this->update_user);
		$criteria->compare('active',$this->active);
		$criteria->compare('updated',$this->updated);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return ReportCardTemplate the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public function getTitle()
    {
        return CommonUtils::autoLang($this->title_cn, $this->title_en);
    }
}
