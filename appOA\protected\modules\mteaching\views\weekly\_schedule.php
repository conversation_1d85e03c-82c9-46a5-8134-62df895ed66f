<script>
    var pendingSave = false;
    var clipboard = new Array;
    var schduleId = <?php echo is_null($taskData['schedule']) ? 0 : $taskData['schedule']->id; ?>;
    var aTitle = <?php echo $taskData['activityTitle']?>;
    var userScheduleTemplats = {};
    var exportOtherData = {}; //从其他周导入的准备数据

    <?php foreach($taskData['userTemplates'] as $ut):?>
    <?php if(!empty($ut->data->data)):?>
    userScheduleTemplats[<?php echo $ut->scheduleid;?>] = <?php echo base64_decode($ut->data->data);?>;
    <?php endif;?>
    <?php endforeach;?>

    var bgColorMapping = {
        188:'#FFDBDB',	//自选活动
        189:'#D5FFC6',	//英文圆圈
        190:'#D2D6FF',	//间点
        191:'#DBF0FF',	//户外活动
        192:'#AFCC6A',	//午餐
        193:'#CCCCCC',	//午睡
        194:'#FFFFDB',	//中文圆圈
        195:'#C55186',	//园外活动
        196:'#DBF0FF',	//特色课
        1624:'#CF9996',	//桌面游戏
        1625:'#D4BfA7',	//日程设置
        1626:'#F6F0EA'	//反思
    };

    var userScheduleTemplats_0 = [
        {
            activityId: 188,
            content: "",
            period: 60,
            title: aTitle['188']['title']
        },
        {
            activityId: 189,
            content: "",
            period: 30,
            title: aTitle['189']['title']
        },
        {
            activityId: 190,
            content: "",
            period: 30,
            title: aTitle['190']['title']
        },
        {
            activityId: 191,
            content: "",
            period: 60,
            title: aTitle['191']['title']
        },
        {
            activityId: 192,
            content: "",
            period: 30,
            title: aTitle['192']['title']
        },
        {
            activityId: 193,
            content: "",
            period: 120,
            title: aTitle['193']['title']
        },
        {
            activityId: 188,
            content: "",
            period: 40,
            title: aTitle['188']['title']
        },
        {
            activityId: 190,
            content: "",
            period: 20,
            title: aTitle['190']['title']
        },
        {
            activityId: 194,
            content: "",
            period: 25,
            title: aTitle['194']['title']
        },
        {
            activityId: 191,
            content: "",
            period: 35,
            title: aTitle['191']['title']
        },
        {
            activityId: 1624,
            content: "",
            period: 35,
            title: aTitle['1624']['title']
        },
        {
            activityId: 1625,
            content: "",
            period: 35,
            title: aTitle['1625']['title']
        },
        {
            activityId: 1626,
            content: "",
            period: 35,
            title: aTitle['1626']['title']
        }
    ];
</script>
<?php
$scheduleData = base64_decode($taskData['schedule']->data->data);

?>
<div id="schedule" class="panel panel-default">
    <div class="panel-heading">
        <div class="dropdown col-md-6">
            <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
                <span class="glyphicon glyphicon-import"></span>
                <?php echo Yii::t('teaching','Import');?> <span class="caret"></span></button>
            <ul class="dropdown-menu" role="menu" id="setSchedule">
                <li id="tpl-system"><a href='javascript:void(0);' onclick="setSchedule(0);" role="menuitem">
                    <span class="glyphicon glyphicon-bell"></span>
                        <?php echo Yii::t('teaching','Default Schedule');?></a></li>
                <li><a href='javascript:void(0);' onclick="openOtherDialog();" role="menuitem">
                    <span class="glyphicon glyphicon-list"></span>
                        <?php echo Yii::t('teaching','Import form other week');?></a></li>
                <?php foreach($taskData['userTemplates'] as $tpl):?>
                    <li id="tpl-user-<?php echo $tpl->scheduleid;?>" scheduleid="<?php echo $tpl->scheduleid;?>">
                        <a onclick="setSchedule(<?php echo $tpl->scheduleid;?>)" href='javascript:void(0);'>
                            <span class="glyphicon glyphicon-chevron-right"></span> <span><?php echo $tpl->tempname;?></span></a></li>
                <?php endforeach;?>
            </ul>
        </div>
        <div class="col-md-3">
            <a href="<?php echo $this->createUrl('export', array('classid'=> $classid, 'weeknum' => $weeknum)) ?>" type="button" target="_blank" class="btn btn-primary pull-right" data-toggle="tooltip">
                <?php echo Yii::t('global','打印');?></a>
        </div>
        <div class="col-md-3">
            <div class="input-group">
                <span class="input-group-addon">
                    <label>
                        <?php
                        echo CHtml::checkBox('asTemplate', is_null($taskData['schedule']) ?
                            false : in_array($taskData['schedule']->id, array_keys($taskData['userTemplates'])) ? true : false);
                        ?> <span><?php echo Yii::t('teaching','Set as template');?></span>
                    </label>
                </span>
                <button id="btn-save-schedule" type="button" class="form-control btn btn-primary" data-toggle="tooltip">
                    <?php echo Yii::t('global','Save');?></button>
            </div>
        </div>
        <div class="clearfix"></div>
    </div>

    <div>
        <div id="sLeft">
            <div class="hli"><?php echo Yii::t('teaching','Time Slot');?></div>

            <!--place holder-->
            <div class="hul" id="timeNodes"></div>
        </div>
        <div id="sRight">
            <div>
                <?php for ($i = 0; $i <= 4; $i++) { ?>
                    <?php echo CHtml::openTag('div', array('class'=>"hli pull-left thx daytitle", 'id'=>'wd_'.$i, 'dayindex'=>$i));?>
                    <label><?php
                        $dayTitle[$i] = Yii::app()->locale->getWeekDayName($i + 1);
                        echo $dayTitle[$i]; ?></label>
                    <?php echo CHtml::closeTag('div');?>
                <?php } ?>
            </div>
            <div>
                <?php for ($i = 0; $i <= 4; $i++) { ?>
                    <div id='day-<?php echo $i?>' class="pull-left thx J_sde daydata" data-id='<?php echo $i?>'></div>
                <?php } ?>
            </div>
        </div>
        <div class="clearfix"></div>
    </div>
</div>

<!--=========================弹窗============================-->
<div class="core_pop_wrap" id="J_schedule_pop" style="display:none;">
    <div class="core_pop">
        <div style="width:500px;">
            <div class="pop_top">
                <a href="" id="J_schedule_pop_x" class="pop_close"><?php echo Yii::t('global','Close');?></a>
                <strong id='d_tit'></strong>
            </div>
            <div class="pop_cont">
                <div class="pop_table" style="height:auto;" id="TS-Form-Zone"> <!--Place Holder-->
                </div>
            </div>
            <div class="pop_bottom">
                <?php echo CHtml::hiddenField('TSFormCurrentDayIndex', 0); ?>
                <?php echo CHtml::hiddenField('TSFormCurrentTSIndex', 0); ?>
                <?php echo CHtml::hiddenField('TSFormPosition', 0); ?>
                <button id="J_schedule_pop_close" type="submit" class="btn btn-default pull-right">
                    <?php echo Yii::t('global','Cancel');?></button>
                <button type="button" onclick="saveTimeSlot();"  class="btn btn-primary mr10 pull-right" id="J_schedule_pop_sub">
                    <?php echo Yii::t('global','OK');?></button>
                <div class="tips_error" style="display:none;" id="J_etip"><?php echo Yii::t('teaching','Select both campus and class.');?></div>
            </div>
        </div>
    </div>
</div>
<!--===========================结束==========================-->


<div class="modal" id="exportFromOtherWeek">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">
                    <span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <h4 class="modal-title"><?php echo Yii::t('teaching','Export from other week');?></h4>
            </div>
            <div class="modal-body">
                <div class="mb15">
                    <div class="row">
                        <div class="col-md-12">
                            <h5><?php echo Yii::t('teaching','Schedule data of current or last year can be exported');?></h5>
                            <div id="hintbox" class="well well-lg">Loading data...</div>
                        </div>
                    </div>
                    <div id="weekSelect-box">
                        <div class="row">
                            <div class="col-md-6">
                                <?php echo CHtml::dropDownList('otherWeekStartYear',
                                    null,null,array('class'=>'form-control mb10',
                                        'onChange'=>'changeClassList()'));?>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <?php echo CHtml::dropDownList('otherWeekClassList',null,null,
                                    array('class'=>'form-control',
                                        'onChange'=>'getClassSchedules()',
                                        'size' => 10,
                                    ));?>
                            </div>
                            <div class="col-md-6">
                                <?php echo CHtml::dropDownList('otherWeekWeekList',null,null,
                                    array('class'=>'form-control',
                                        'size' => 10
                                    ));?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer" style="line-height: 30px;">
                <?php echo CHtml::hiddenField('avatar');?>
                <button type="button" class="btn btn-default pull-right" data-dismiss="modal">
                    <?php echo Yii::t('global','Cancel');?></button>
                <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10" onclick="doExportOtherWeek()">
                    <?php echo Yii::t('global','OK');?></button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<!-- Templates -->

<script type="text/template" id="item-timeslot-template">
    <p><strong><%= title %></strong></p><% print(nl2br(content)) %>
</script>

<script type="text/template" id="timeslot-form-template">
    <table width="100%">
        <tr>
            <th>
                <?php echo Yii::t('teaching','Curriculum Activity');?>
            </th>
            <td>
				<?php echo CHtml::dropDownList('TSFormProject', '',
				$taskData['projects'], array('class'=>'select_5 form-control','empty'=>Yii::t("global","Please Select"),'onchange'=>'projectChange()'));?>
			</td>
        </tr>
        <tr>
            <th>
                <?php echo Yii::t('teaching','Duration');?>
            </th>
            <td class="form-inline">
				<?php
				for($i=0;$i<9;$i++){
					$hour[$i]=$i;
				}
				echo CHtml::dropDownList('TSFormSethour', '', $hour, array('class'=>'form-control length_2'));
				?>
				<?php echo Yii::t('teaching','Hour');?>
				<?php
				for($i=0;$i<60;$i+=5){
					$min[$i]=$i;
				}
				echo CHtml::dropDownList('TSFormSetmin', '', $min, array('class'=>'form-control length_2'));
				?>
				<?php echo Yii::t('teaching','Minutes');?>
            </td>
        </tr>
        <tr>
            <th>
                <?php echo Yii::t('global','Title');?>
            </th>
            <td>
				<?php echo CHtml::textField('TSFormTitle', '', array('class'=>'form-control length_5'))?>
				<div id="project_tit"></div>
            </td>
        </tr>
        <tr>
            <th>
                <?php echo Yii::t('labels','Content');?>
            </th>
            <td>
				<?php echo CHtml::textArea('TSFormContent', '<%= content %>',
				array('class'=>'form-control length_5', 'style'=>'height: 92px;'))?>
			</td>
        </tr>
    </table>
</script>

<div id="toolbar-template" style="display:none;">
    <span title="<?php echo Yii::t('global', 'Edit');?>" onclick="TS_edit(this)" class="glyphicon glyphicon-pencil"></span>
    <span title="<?php echo Yii::t('global', 'Delete');?>" onclick="TS_delete(this)" class="glyphicon glyphicon-trash"></span>
    <span title="<?php echo Yii::t('global', 'Prepend');?>" onclick="TS_prepend(this)" class="glyphicon glyphicon-chevron-up"></span>
    <span title="<?php echo Yii::t('global', 'Append');?>" onclick="TS_append(this)" class="glyphicon glyphicon-chevron-down"></span>
</div>

<div id="toolbar-wday-template" style="display:none;">
    <span class="dropdown">
        <a title="<?php echo Yii::t('global', 'Duplicate To');?>"
           href="javascript:void(0)" class="dropdown-toggle" data-toggle="dropdown">
            <span class="glyphicon glyphicon-export"></span></a>
        <ul class="dropdown-menu">
            <!-- place holder -->
        </ul>
    </span>
    <span title="<?php echo Yii::t('global', 'Clear');?>" onclick="WD_clear(this)" class="glyphicon glyphicon-trash"></span>
</div>

<script type="text/template" id="wd-duplicate-template">
    <li>
        <a href="javascript:void(0)" onclick="WD_Duplicate(<%- dayFrom %>, <%- dayTo %>)">
            <span class="glyphicon glyphicon-chevron-right"></span> <?php echo Yii::t('global', 'Duplicate to');?> <%- dayTitle %>
            </a></li>
</script>

<script type="text/javascript">
var dayTitle = <?php echo CJSON::encode($dayTitle);?>;
var DisplayLengthRatio = 2.8;
var dialogShowing = false;
var currentFormData;
var currentFormView;
var activityIds = new Array;
var scheduleWeekData = new Array;

<?php
//初始化数据
if(!empty($scheduleData)): ?>
var dataStr = <?php echo $scheduleData;?>;
for(var i=0;i<5;i++){
    var dayData = 'scheduleWeekData[i] = ' + dataStr[i] + ';';
    eval( dayData );
}
<?php else: ?>
for(var i=0;i<5;i++){
    scheduleWeekData[i] = new Array;
}
<?php endif; ?>

var DailySchedules = new Array;
function iniTimeSlotItemTool(){
    $('div.timeslot').hover(
        function(){
            if($(this).children('div.toolbar').length == 0){
                $(this).prepend($('<div></div>').addClass('toolbar').html($('#toolbar-template').html()));
            }
        }
    );
    $('div.daytitle').hover(
        function(){
            if($(this).children('div.toolbar').length == 0){
                $(this).prepend($('<div></div>').addClass('toolbar text-primary').html($('#toolbar-wday-template').html()));
                var _dayIndex = $(this).attr('dayindex');
                var dropdownMenu = $(this).find('div.toolbar ul.dropdown-menu');
                if(_dayIndex == 4) dropdownMenu.addClass('dropdown-menu-right'); //align
                var copyToItemTpl = _.template( $('#wd-duplicate-template').html() );
                dropdownMenu.empty();
                _.each(dayTitle,function(_d,_i){
                    if(_dayIndex != _i){
                        var li = copyToItemTpl( {dayTo: _i, dayFrom: _dayIndex, dayTitle: _d} );
                        dropdownMenu.append(li);
                    }
                })
            }
        }
    );
}

region_pop = $('#J_schedule_pop');

function isRoutineActivities(aid)
{
    return typeof aTitle[aid] != "undefined"
}

function projectChange(){
    var defaultContent = "2. \r\n\r\n3. \r\n\r\n4. \r\n\r\n所有区域都开放。\r\nAll learning centers open.";
    var proVal = $('#TSFormProject').val();
    var _tit = $('#TSFormProject option:selected').text();
    // if ( 187 < proVal && proVal < 197 )
    if ( isRoutineActivities(proVal) )
    {
        $('#TSFormTitle').show().val( _tit );
        $('#project_tit').html( '' );
    }
    else if( proVal > 0)
    {
        $('#TSFormTitle').hide().val( '自由选择时间 (Free choice Time)' );
        var ahl = '1. <a href="<?php echo Yii::app()->params['OABaseUrl']?>/child/show/activity_show.php?aid='+proVal+'" target="_blank">'+_tit+'</a>';
        $('#project_tit').html( '<div>'+ahl+'</div><div>'+aTitle[proVal]['memo']+'&nbsp;</div>' );

        var _content = $('#TSFormContent').val();
        if(_content == ''){
            $('#TSFormContent').val( defaultContent );
        }
    }else{
        $('#TSFormTitle').show().val( '' );
        $('#project_tit').html( '' );
        $('#TSFormContent').val('');
    }
}

function getTimeSlots(classStart, classEnd){
    timeNodes = new Array();
    var i=0;
    var _t = classStart;

    var timeNodes = new Array;
    if(_t.getMinutes() != 0 && _t.getMinutes() != 30){
        timeNodes[i] = {
            period: 60 - _t.getMinutes(),
            text: ( ( _t.getHours() < 10) ? '0' + _t.getHours() : _t.getHours() ) + ':' + _t.getMinutes(),
            flag: 'first',
            odd: ( i % 2 ) ? 1 : 0
        }
        _t.setMinutes( 60 );
        i++;
    }
    while(_t < classEnd){
        timeNodes[i] = {
            flag: (i==0)?'first':'',
            period: 30,
            text: ( ( _t.getHours() < 10) ? '0' + _t.getHours() : _t.getHours() ) + ':' + ( ( _t.getMinutes() < 10) ? '0' + _t.getMinutes() : _t.getMinutes() ),
            odd: ( i % 2 ) ? 1 : 0
        }
        _t.setMinutes(_t.getMinutes() + 30, 0, 0);
        i++;
    }
    if(_t.getTime() == classEnd.getTime()){
        timeNodes[--i].flag = 'end';
        timeNodes[i].period = 30;
    }else{
        timeNodes[--i].flag = 'end';
        timeNodes[i].period = (classEnd.getMinutes() > 30 ) ? classEnd.getMinutes() - 30 : classEnd.getMinutes();
    }

    return timeNodes;
}

$(function(){

    $('#btn-save-schedule').popover(
        {   container: '#schedule',
            placement: 'top',
            content: '<?php echo Yii::t("teaching","Changes not saved!");?>',
            trigger: 'manual'});
    var classStart = new Date(parseInt(<?php echo ( $taskData['classTime']['start'] * 1000 );?>));
    var classEnd = new Date(parseInt(<?php echo ( $taskData['classTime']['end'] * 1000 );?>));

    var timeNodes = new Array;
    timeNodes = getTimeSlots(classStart, classEnd);
    var timeNodesCollection = new Backbone.Collection;

    var timeNodeView = Backbone.View.extend({
        tagName: 'div',
        className: 'tb',
        attributes:{
        },
        initialize:function(){
            this.className = this.model.get('odd') ? 'bg1' : 'bg2';
        },
        render: function(){
            this.$el.html( _.template('<div class="tbt"><%= text %></div>', this.model.attributes) );
            this.$el.attr('style','height:'+ DisplayLengthRatio * this.model.get('period') + 'px;');
            this.$el.addClass( this.model.get('odd') ? 'bg1' : 'bg2' );
            return this;
        }
    });
    var timeNodesView = Backbone.View.extend({
        el: $("#timeNodes"),
        initialize: function(){
            this.listenTo(timeNodesCollection, 'reset', this.addAll);
            timeNodesCollection.reset(timeNodes);
        },
        addAll: function(){
            timeNodesCollection.each(this.addOne,this);
        },
        addOne: function(timeNode){
            var item = new timeNodeView({model: timeNode});
            this.$el.append(item.render().el);
        }
    });

    var pageTimeNodes = new timeNodesView;

    //***********************************

    var TimeSlot = Backbone.Model.extend({
        defaults: function(){
            return {
                day: 0,
                sindex: 0,
                period: 0,
                title: '',
                activityId: 0,
                content: '',
                startTime: 0,
                color: ''
            }
        }
    });
    var DailySchedule = Backbone.Collection.extend({
        model: TimeSlot
    });
    var timeSlotView = Backbone.View.extend({
        className: 'timeslot',
        attributes:{
        },
        template: _.template($('#item-timeslot-template').html()),
        initialize:function(){
            this.className = this.model.get('odd') ? 'bg1' : 'bg2';
        },
        render: function(){
            this.$el.html( this.template(this.model.attributes) );
            this.$el.css('height', DisplayLengthRatio * this.model.get('period'));
            this.$el.attr('sindex', this.model.get('sindex'));
            this.$el.attr('day', this.model.get('day'));
            this.$el.attr('id', 'ts-' + this.model.get('day') + '-' + this.model.get('sindex'));

            var pid = parseInt(this.model.get('activityId'));
            // if ( 187 < pid && pid < 197 ){
            if ( isRoutineActivities(pid) ){
                this.$el.css('background-color', bgColorMapping[pid]);
            }
            return this;
        }
    });

    for(var i=0;i<5; i++){
        DailySchedules[i] = new DailySchedule;
    }

    pendingSave = false;

    var DailyView = Backbone.View.extend({
        attributes: {
            sindex: 0,
            day: 0
        },
        initialize: function(){
            this.listenTo(this.collection, 'reset', this.addAll);
        },
        addAll: function(){
            activityIds[ this.attributes.day ] = new Array;
            this.$el.html('');
            this.attributes.sindex=0;
            this.collection.each(this.addOne, this);
            iniTimeSlotItemTool();
            if(pendingSave){
                $('#btn-save-schedule').popover('show');
            }
        },
        addOne: function(timeslot){
            timeslot.set('sindex', this.attributes.sindex);
            timeslot.set('day', this.attributes.day);
            if( timeslot.get('activityId') ){
                activityIds[ this.attributes.day ].push(timeslot.get('activityId'));
            }
            var slot = new timeSlotView({model:timeslot});
            this.$el.append(slot.render().el);
            this.attributes.sindex++;

        }
    });

    var TimeSlotForm = Backbone.View.extend({
        el: $('#TS-Form-Zone'),
        template: _.template($('#timeslot-form-template').html()),
        initialize: function(){
            this.listenTo(this.model,'change', this.render);
        },
        render: function(){

            this.$el.html( this.template(this.model.attributes) );
            $('#TSFormProject').val(this.model.get('activityId'));
            $('#TSFormContent').val(this.model.get('content'));
            $('#TSFormTitle').val(this.model.get('title'));

            $('#TSFormSethour').val(parseInt(this.model.get('period')/60));
            $('#TSFormSetmin').val( (this.model.get('period')) % 60 );

            return this;
        }
    });

    var Daily = new Array;
    for(var i=0;i<5; i++){
        Daily[i] = new DailyView({
            collection: DailySchedules[i],
            el: $('#day-'+i),
            attributes:{
                day: i,
                sindex: 0
            }
        });
    }

    for(var i=0;i<5; i++){
        DailySchedules[i].reset(scheduleWeekData[i]);
    }

    $('#J_schedule_pop_x, #J_schedule_pop_close').on('click', function(e){
        e.preventDefault();
        region_pop.hide();
        dialogShowing = false;
    });

    currentFormData = new TimeSlot;
    currentFormView = new TimeSlotForm({model:currentFormData});

});

/**
 isEdit: -1: 新建, other: 编辑
 position: 0 修改本身； -1 之前插入； 1 之后插入
 */
function openTS_editDialog(element, isEdit, position){
    var id = $(element).parents('div.daydata').attr('data-id');
    $('#d_tit').html( '<?php echo Yii::t('teaching','Schedule Slot');?>' + ' <small>' + $('#wd_'+id + ' label').text()
    + '</small>' );
    $('#TSFormCurrentDayIndex').val(id);
    $('#TSFormPosition').val( position );

    if(isEdit==-1 || position!=0){
        $('#TSFormCurrentTSIndex').val(scheduleWeekData[id].length);
        currentFormData.set("period", 60);
        currentFormData.set("activityId", 0);
        currentFormData.set("content", '');
        currentFormData.set("title", '');
        //projectChange();
    }else{
        var _sindex = $(element).parents('div.timeslot').attr('sindex');
        if(isset(scheduleWeekData[id]) && isset(scheduleWeekData[id][_sindex])){
            currentFormData.set(scheduleWeekData[id][_sindex]);
            currentFormData.set("content", scheduleWeekData[id][_sindex].content.replace(/<div(.*?)<\/div>/gi, ''));
//            projectChange();
            $('#TSFormCurrentTSIndex').val(_sindex);
        }
    }
    if(position!=0){
        var _sindex = $(element).parents('div.timeslot').attr('sindex');
        $('#TSFormCurrentTSIndex').val(_sindex);
    }

    region_pop.show().css({
        left : ($(window).width() - region_pop.outerWidth())/2,
        top : ($(window).height() - region_pop.outerHeight())/2 //+ $(document).scrollTop()
    });

    dialogShowing = true;
}
//清除一天的安排
function WD_clear(obj){
    if(dialogShowing) return true;
    if(confirm('<?php echo Yii::t("teaching", "Clear schedule of the day?");?>')){
        pendingSave=true;
        var dayIndex = parseInt($(obj).parents('div.daytitle').attr('dayindex'));
        scheduleWeekData[dayIndex] = [];
        DailySchedules[dayIndex].reset(scheduleWeekData[dayIndex]);
    }
}
//复制一天的安排
function WD_clipBoard(dayIndex){
    if(dialogShowing) return true;
//    var dayIndex = $(obj).parents('div.daytitle').attr('dayindex');

    clipboard = new Array;
    for(var i=0; i<scheduleWeekData[dayIndex].length; i++){
        clipboard.push( scheduleWeekData[dayIndex][i] );
    }
}
//粘贴复制一天的安排
function WD_paste(dayIndex){
    if(dialogShowing) return true;
//    var dayIndex = $(obj).parents('div.daytitle').attr('dayindex');
    if( scheduleWeekData[dayIndex].length == 0 ||
        ( scheduleWeekData[dayIndex].length &&
            confirm('<?php echo Yii::t("teaching", "Overwrite schedule data of the day?");?>') ) ){
        scheduleWeekData[dayIndex] = [];
        for(var i=0; i<clipboard.length; i++){
            scheduleWeekData[dayIndex].push( clipboard[i] );
        }
        DailySchedules[dayIndex].reset(scheduleWeekData[dayIndex]);
    }
}
//复制粘贴
function WD_Duplicate(dayFromIndex, dayToIndex){
    pendingSave=true;
    WD_clipBoard(dayFromIndex);
    WD_paste(dayToIndex);
}

function reindexArray( array )
{
    var result = [];
    for( var key in array )
        result.push( array[key] );
    return result;
};
//编辑时间段
function TS_edit(obj){
    openTS_editDialog(obj, 0, 0);
}
//在某时间段之前插入
function TS_prepend(obj){
    openTS_editDialog(obj, 0, -1);
}
//在某时间段后添加
function TS_append(obj){
    openTS_editDialog(obj, 0, 1);
}
//删除某时间段
function TS_delete(obj){
    if(confirm('<?php echo Yii::t("message", "Sure to delete?");?>')){
        if(dialogShowing) return true;
        var dom = $(obj).parents('div.timeslot');
        var dayIndex = dom.attr('day');
        var slIndex = dom.attr('sindex');
        delete(scheduleWeekData[dayIndex][slIndex]);
        scheduleWeekData[dayIndex] = reindexArray(scheduleWeekData[dayIndex]);
        pendingSave=true;
        DailySchedules[dayIndex].reset(scheduleWeekData[dayIndex]);
    }
}
//导入整周时间安排模版
function setSchedule(id){
    if(confirm('<?php echo Yii::t("teaching","Overwrite schedule data of the week?");?>')){
        pendingSave=true;
        if(id){
            if(!_.isObject(userScheduleTemplats[id])){
                userScheduleTemplats[id] = eval('('+userScheduleTemplats[id]+')');
            }
            var dataStr = userScheduleTemplats[id];
            for(var i=0;i<5;i++){
                scheduleWeekData[i] = [];
                var cmd = ' scheduleWeekData['+i+'] = ' + dataStr[i] + ';'
                eval(cmd);
                DailySchedules[i].reset(scheduleWeekData[i]);
            }
        }else{
            for(var i=0;i<5;i++){
                scheduleWeekData[i] = [];
                scheduleWeekData[i] = userScheduleTemplats_0.slice(0);
                DailySchedules[i].reset(scheduleWeekData[i]);
            }
        }
    }
}

function saveTimeSlot(){
    $('#J_fail_info').remove();
    var dayIndex = $('#TSFormCurrentDayIndex').val();
    var tsIndex = $('#TSFormCurrentTSIndex').val();
    var position = $('#TSFormPosition').val();

    var p = parseInt($('#TSFormSethour').val() * 60 ) + parseInt($('#TSFormSetmin').val());
    var actId = $('#TSFormProject').val();
    if(!actId){
        $( '<span id="J_fail_info" class="text-warning"><i class="glyphicon glyphicon-exclamation-sign text-warning"></i> <?php echo Yii::t("teaching","Set Curriculum Activity");?></span>' ).appendTo($('#J_schedule_pop_sub').parent()).fadeIn( 'fast' );
        return false;
    }
    if(parseInt(p)<5){
        $( '<span id="J_fail_info" class="text-warning"><i class="glyphicon glyphicon-exclamation-sign text-warning"></i> <?php echo Yii::t("teaching","Set Duration");?></span>' ).appendTo($('#J_schedule_pop_sub').parent()).fadeIn( 'fast' );
        return false;
    }
    $('#project_tit').find('div:eq(1)').html('&nbsp;');
    var editedObj = {
        title: $('#TSFormTitle').val(),
        // content: (187 < actId && actId < 197) ? $('#TSFormContent').val() : $('#project_tit').html() + $('#TSFormContent').val(),
        content: (isRoutineActivities(actId)) ? $('#TSFormContent').val() : $('#project_tit').html() + $('#TSFormContent').val(),
        activityId: actId,
        period: p
    };
    var start = 0;
    if(position == -1){
        start = parseInt(tsIndex);
    }
    if(position == 1){
        start = parseInt(tsIndex) + 1;
    }
    if(position != 0){
        scheduleWeekData[dayIndex].splice(start, 0, editedObj);
        pendingSave = true;
    }else{
        var beforeSave = JSON.stringify( scheduleWeekData[dayIndex][tsIndex] );
        scheduleWeekData[dayIndex][tsIndex] = editedObj;
        if (beforeSave != scheduleWeekData[dayIndex][tsIndex]) pendingSave=true;
    }

    $('#J_schedule_pop_x').click();
    DailySchedules[dayIndex].reset(scheduleWeekData[dayIndex]);
}

//导入其他周
function openOtherDialog(){
    $('#exportFromOtherWeek').modal({backdrop:false});
    if(_.isEmpty(exportOtherData)){
        $.ajax({
            type: 'post',
            url: '<?php echo $this->createUrl('//mteaching/weekly/getWeekSchedule');?>',
            dataType: 'json'
        }).done(function(data){
            if(data.state == 'success'){
                exportOtherData = data.data;
                exportOtherData.schedules = {};
                var flag=false;
                _.each(exportOtherData.calendars, function(_yid, _startyear){
                    var _txt = _startyear+ ' ~ '+
                        parseInt( parseInt(_startyear) + 1 ) +
                        ' <?php echo Yii::t("labels","School Year");?>';
                    var _opt = $('<option></option>').val(_yid).html(_txt);
                    $('#otherWeekStartYear').prepend(_opt);
                });
                changeClassList();
                $('#exportFromOtherWeek #hintbox').hide();
            }
        });
    }
}

function changeClassList(){
    $('#otherWeekClassList option').remove();
    $('#otherWeekWeekList option').remove();
    var yid = $('#otherWeekStartYear').val();
    _.each(exportOtherData.classes[yid], function(title, classid){
        var _opt = $('<option></option>').val(classid).html(title);
        $('#otherWeekClassList').append(_opt);
    })
}

var scheduleLinks = {};

function getClassSchedules(){
    var yid = parseInt($('#otherWeekStartYear').val());
    var classid = parseInt($('#otherWeekClassList').val());
    if( _.isUndefined(exportOtherData['schedules'][classid])){
        $.ajax({
            type: 'post',
            url: '<?php echo $this->createUrl('//mteaching/weekly/getWeekSchedule');?>',
            dataType: 'json',
            async: false,
            data:{yid:yid, classid:classid}
        }).done(function(data){
            if(data.state == 'success'){
                userScheduleTemplats = _.extend(userScheduleTemplats, data.data.schedules);
                exportOtherData['schedules'][classid] = data.data.schedules[classid];

                scheduleLinks[classid] = data.data.schedulesLink[classid];
                console.log(scheduleLinks);
            }
        });
    }
    $('#otherWeekWeekList option').remove();
    _.each(scheduleLinks[classid], function(tplid, weeknum){
        var _opt = $('<option></option>').val(tplid).html('Week #'+weeknum);
        $('#otherWeekWeekList').append(_opt);
    })
}

function doExportOtherWeek(){
    var _classid = $('#otherWeekClassList').val();
    var _weeknum = $('#otherWeekWeekList').val();
    if(_classid * _weeknum){
        setSchedule(_weeknum);
        $('#exportFromOtherWeek').modal('hide');
    }
}

head.load('<?php echo Yii::app()->themeManager->baseUrl;?>/base/js/dialog/dialog.js', '<?php echo Yii::app()->themeManager->baseUrl;?>/base/js/util_libs/draggable.js', function(){
    region_pop.draggable( { handle : '.pop_top'} );
    $('div.J_sde').hover(
        function(e){
            var html = '<a id="add-sde" href="javascript:;" role="button"><span class="glyphicon glyphicon-plus"></span></a>';
            $(this).append(html);
            $('#add-sde').on('click', function(e){
                e.preventDefault();
                openTS_editDialog(this, -1, 0);
            });
        },
        function(e){
            $('#add-sde').remove();
        });

    $('#J_schedule_pop_x, #J_schedule_pop_close').on('click', function(e){
        e.preventDefault();
        region_pop.hide();
    });

    var saving = false;
    $('#btn-save-schedule').click(function(){
        if(saving) return false;

        saving = true;
        $(this).attr('disabled','disabled');
        $(this).text('Saving...');

        var dayName = new Array(0,1,2,3,4);
        var postData = {0:'',1:'',2:'',3:'',4:''};
        for(var i=0;i<scheduleWeekData.length;i++){
            postData[dayName[i]] = JSON.stringify(scheduleWeekData[i]);
        }

        $.ajax({
            dataType: 'json',
            type: 'POST',
            data:{
                scheduleData: JSON.stringify(postData),
                activityIds: activityIds.toString(),
                setTemplate: $('#asTemplate').is(':checked') ? 1 : 0
            }
        }).done(function(data){
            if(data.state == 'success'){
                if(data.data['removeTpl']){
                    $('#tpl-user-'+data.data['removeTpl']).remove();
                    delete userScheduleTemplats[data.data['removeTpl']];
                }
                if(data.data['addTpl']){
                    var li_ele = $('<li></li>')
                        .attr('scheduleid',data.data['addTpl'])
                        .attr('id', 'tpl-user-'+data.data['addTpl'])
                        .html("<a onclick='setSchedule("+data.data['addTpl']+")' href='javascript:void(0);'><span>"+data.data['tplName']+"</span></a>");
                    $('#setSchedule').append(li_ele);
                    userScheduleTemplats[data.data['addTpl']]=eval('('+data.data['tplData']+')');
                }
                $('#btn-save-schedule').popover('hide');
            }else{
                alert(data.message);
            }
            $('#btn-save-schedule').removeAttr('disabled');
            $('#btn-save-schedule').text('Save');
            saving = false;
            return false;
        });
    });

    if ( $('#tpl-user li').length == 0 ){
        $('#tpl-user').hide();
    }
});
</script>
<style>
    .popover {
        background: tomato;
    }

    .popover.top .arrow:after {
        border-top-color: tomato;
    }
    .input-group-addon label{margin-bottom: 0; font-weight: normal}
    #schedule .dropdown-menu{
        min-width: 200px;}
</style>