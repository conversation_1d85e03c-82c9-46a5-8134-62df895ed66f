<ul class="nav nav-pills" role="tablist">
    <li role="presentation" class="active"><a href="#all" aria-controls="all" role="tab" data-toggle="tab"><?php echo Yii::t("site", "All");?></a></li>
    <?php if(in_array($this->branchId, array('BJ_DS', 'BJ_SLT'))): ?>
        <li role="presentation"><a href="#es" aria-controls="profile" role="tab" data-toggle="tab"><?php echo Yii::t("site", "All in ES");?></a></li>
        <li role="presentation"><a href="#ss" aria-controls="profile" role="tab" data-toggle="tab"><?php echo Yii::t("site", "All in SS");?></a></li>
        <li role="presentation"><a href="#other" aria-controls="profile" role="tab" data-toggle="tab"><?php echo Yii::t("site", "Cross-division");?></a></li>
    <?php else: ?>
        <li role="presentation"><a href="#thisSchool" aria-controls="profile" role="tab" data-toggle="tab"><?php echo Yii::t("site", "All in this Campus");?></a></li>
    <?php endif; ?>
    <li role="presentation"><a href="#otherSchool" aria-controls="profile" role="tab" data-toggle="tab"><?php echo Yii::t("site", "Cross-campus");?></a></li>
    <li role="presentation" style="float: right;"><button class="btn btn-xs btn-primary" onclick="exportData()" type="button"><?php echo Yii::t("labels", "Export");?></button></li></button>
</ul>
<div class="tab-content">
    <div role="tabpanel" class="tab-pane active" id="all">
        <div class='mt20 mb20 font14 color3'> <?php echo Yii::t("site", "Family Number: ");?><?php echo "{$familyNum}"; ?>，<?php echo Yii::t("site", "Children Number: ");?><?php echo "{$childNum}"; ?> <span class='ml10'><?php echo Yii::t("site", "The list only counts active students.");?></span></div>
        <?php $i = 1;
        foreach ($childData as $k => $v) : ?>
            <div class="panel panel-default " id='StuFamily'>
                <div class="panel-body flex">
                    <div class='p10 bg'>
                        <div class='font14 color3'><?php echo Yii::t("site", "Family #");?> <?php echo $i; ?> </div>
                        <?php foreach ($v['children'] as $child) : ?>
                            <div class="media">
                                <div class="media-left pull-left media-middle">
                                    <a href="<?php echo $this->createUrl('/child/index/index', array('childid' => $child['id'])); ?>" target="_blank">
                                        <img src="<?php echo $child['photo']; ?>" data-holder-rendered="true" class="reserveImg">
                                    </a>
                                </div>
                                <div class="media-body pt5 media-middle">
                                    <h4 class="media-heading"><span class='font14 color3 mr10'><?php echo $child['name']; ?></span> <span class='font12 color6'><?php echo $child['id']; ?></span></h4>
                                    <div class="color6 font12 mt5 ">
                                        <?php if ($child['gradeGroup'] == 'SS') : ?>
                                            <?php if ($child['school_other'] == 1) { echo '<span class="label label-warning">'.$child['school_abb'].'</span>';} ?> <span class='classTitle ss'><?php echo $child['gradeGroup']; ?> | <?php echo $child['classTitle']; ?></span>
                                        <?php endif; ?>
                                        <?php if ($child['gradeGroup'] == 'ES' || $child['gradeGroup'] == 'KG') : ?>
                                            <?php if ($child['school_other'] == 1) { echo '<span class="label label-warning">'.$child['school_abb'].'</span>';} ?> <span class='classTitle'><?php echo $child['gradeGroup']; ?> | <?php echo $child['classTitle']; ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <div class='p15 parentFamily'>
                        <div class='font14 color3'><?php echo Yii::t("child", "Parent Info");?></div>
                        <?php if (isset($v['fData'])) : ?>
                            <div class='mt20'>
                                <div><span class='font14 color3 mr10'><?php echo $v['fData']['name']; ?></span> <span class="parentLabel"><?php echo Yii::t("site", "Father");?></span> <?php echo $v['fData']['id']; ?></div>
                                <div class='mt5 color6 font14'><span class="glyphicon glyphicon-phone"> </span><span class='mr20 ml5'><?php echo $v['fData']['mphone']; ?></span><span class="glyphicon glyphicon-envelope"> </span> <span class='ml5'><?php echo $v['fData']['email']; ?></span></div>
                            </div>
                        <?php endif; ?>
                        <?php if (isset($v['mData'])) : ?>
                            <div class='mt20'>
                                <div><span class='font14 color3 mr10'><?php echo $v['mData']['name']; ?></span> <span class="parentLabel"><?php echo Yii::t("site", "Mother");?></span> <?php echo $v['mData']['id']; ?></div>
                                <div class='mt5 color6 font14'><span class="glyphicon glyphicon-phone"> </span><span class='mr20 ml5'><?php echo $v['mData']['mphone']; ?></span><span class="glyphicon glyphicon-envelope"> </span> <span class='ml5'><?php echo $v['mData']['email']; ?></span></div>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class='clearfix'></div>
                </div>
            </div>
        <?php $i++;
        endforeach; ?>
    </div>
    <div role="tabpanel" class="tab-pane " id="es">
        <div class='mt20 mb20 font14 color3'><?php echo Yii::t("site", "Family Number: ");?><?php echo "{$esFamilyNum}"; ?>，<?php echo Yii::t("site", "Children Number: ");?><?php echo "{$esChildNum}"; ?><span class='ml10'><?php echo Yii::t("site", "The list only counts active students.");?></span></div>
        <?php $i = 1;
        foreach ($esData as $id) : $v = $childData[$id]; ?>
            <div class="panel panel-default " id='StuFamily'>
                <div class="panel-body flex">
                    <div class='p10 bg'>
                        <div class='font14 color3'><?php echo Yii::t("site", "Family #");?> <?php echo $i; ?></div>
                        <?php foreach ($v['children'] as $child) : ?>
                            <div class="media">
                                <div class="media-left pull-left media-middle">
                                    <a href="<?php echo $this->createUrl('/child/index/index', array('childid' => $child['id'])); ?>" target="_blank">
                                        <img src="<?php echo $child['photo']; ?>" data-holder-rendered="true" class="reserveImg">
                                    </a>
                                </div>
                                <div class="media-body pt5 media-middle">
                                    <h4 class="media-heading"><span class='font14 color3 mr10'><?php echo $child['name']; ?></span> <span class='font12 color6'><?php echo $child['id']; ?></span></h4>
                                    <div class="color6 font12 mt5 ">
                                        <?php if ($child['gradeGroup'] == 'SS') : ?>
                                            <?php if ($child['school_other'] == 1) { echo '<span class="label label-warning">'.$child['school_abb'].'</span>';} ?> <span class='classTitle ss'><?php echo $child['gradeGroup']; ?> | <?php echo $child['classTitle']; ?></span>
                                        <?php endif; ?>
                                        <?php if ($child['gradeGroup'] == 'ES' || $child['gradeGroup'] == 'KG') : ?>
                                            <?php if ($child['school_other'] == 1) { echo '<span class="label label-warning">'.$child['school_abb'].'</span>';} ?> <span class='classTitle'><?php echo $child['gradeGroup']; ?> | <?php echo $child['classTitle']; ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <div class='p15 parentFamily'>
                        <div class='font14 color3'><?php echo Yii::t("child", "Parent Info");?></div>
                        <?php if (isset($v['fData'])) : ?>
                            <div class='mt20'>
                                <div><span class='font14 color3 mr10'><?php echo $v['fData']['name']; ?></span> <span class="parentLabel"><?php echo Yii::t("site", "Father");?></span><?php echo $v['fData']['id']; ?></div>
                                <div class='mt5 color6 font14'><span class="glyphicon glyphicon-phone"> </span><span class='mr20 ml5'><?php echo $v['fData']['mphone']; ?></span><span class="glyphicon glyphicon-envelope"> </span> <span class='ml5'><?php echo $v['fData']['email']; ?></span></div>
                            </div>
                        <?php endif; ?>
                        <?php if (isset($v['mData'])) : ?>
                            <div class='mt20'>
                                <div><span class='font14 color3 mr10'><?php echo $v['mData']['name']; ?></span> <span class="parentLabel"><?php echo Yii::t("site", "Mother");?></span><?php echo $v['mData']['id']; ?></div>
                                <div class='mt5 color6 font14'><span class="glyphicon glyphicon-phone"> </span><span class='mr20 ml5'><?php echo $v['mData']['mphone']; ?></span><span class="glyphicon glyphicon-envelope"> </span> <span class='ml5'><?php echo $v['mData']['email']; ?></span></div>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class='clearfix'></div>
                </div>
            </div>
        <?php $i++;
        endforeach; ?>
    </div>
    <div role="tabpanel" class="tab-pane " id="ss">
        <div class='mt20 mb20 font14 color3'><?php echo Yii::t("site", "Family Number: ");?><?php echo "{$ssFamilyNum}"; ?>，<?php echo Yii::t("site", "Children Number: ");?><?php echo "{$ssChildNum}"; ?><span class='ml10'><?php echo Yii::t("site", "The list only counts active students.");?></span></div>
        <?php $i = 1;
        foreach ($ssData as $id) : $v = $childData[$id]; ?>
            <div class="panel panel-default " id='StuFamily'>
                <div class="panel-body flex">
                    <div class='p10 bg'>
                        <div class='font14 color3'><?php echo Yii::t("site", "Family #");?> <?php echo $i; ?></div>
                        <?php foreach ($v['children'] as $child) : ?>
                            <div class="media">
                                <div class="media-left pull-left media-middle">
                                    <a href="<?php echo $this->createUrl('/child/index/index', array('childid' => $child['id'])); ?>" target="_blank">
                                        <img src="<?php echo $child['photo']; ?>" data-holder-rendered="true" class="reserveImg">
                                    </a>
                                </div>
                                <div class="media-body pt5 media-middle">
                                    <h4 class="media-heading"><span class='font14 color3 mr10'><?php echo $child['name']; ?></span> <span class='font12 color6'><?php echo $child['id']; ?></span></h4>
                                    <div class="color6 font12 mt5 ">
                                        <?php if ($child['gradeGroup'] == 'SS') : ?>
                                            <?php if ($child['school_other'] == 1) { echo '<span class="label label-warning">'.$child['school_abb'].'</span>';} ?> <span class='classTitle ss'><?php echo $child['gradeGroup']; ?> | <?php echo $child['classTitle']; ?></span>
                                        <?php endif; ?>
                                        <?php if ($child['gradeGroup'] == 'ES' || $child['gradeGroup'] == 'KG') : ?>
                                            <?php if ($child['school_other'] == 1) { echo '<span class="label label-warning">'.$child['school_abb'].'</span>';} ?> <span class='classTitle'><?php echo $child['gradeGroup']; ?> | <?php echo $child['classTitle']; ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <div class='p15 parentFamily'>
                        <div class='font14 color3'><?php echo Yii::t("child", "Parent Info");?></div>
                        <?php if (isset($v['fData'])) : ?>
                            <div class='mt20'>
                                <div><span class='font14 color3 mr10'><?php echo $v['fData']['name']; ?></span> <span class="parentLabel"><?php echo Yii::t("site", "Father");?></span><?php echo $v['fData']['id']; ?></div>
                                <div class='mt5 color6 font14'><span class="glyphicon glyphicon-phone"> </span><span class='mr20 ml5'><?php echo $v['fData']['mphone']; ?></span><span class="glyphicon glyphicon-envelope"> </span> <span class='ml5'><?php echo $v['fData']['email']; ?></span></div>
                            </div>
                        <?php endif; ?>
                        <?php if (isset($v['mData'])) : ?>
                            <div class='mt20'>
                                <div><span class='font14 color3 mr10'><?php echo $v['mData']['name']; ?></span> <span class="parentLabel"><?php echo Yii::t("site", "Mother");?></span><?php echo $v['mData']['id']; ?></div>
                                <div class='mt5 color6 font14'><span class="glyphicon glyphicon-phone"> </span><span class='mr20 ml5'><?php echo $v['mData']['mphone']; ?></span><span class="glyphicon glyphicon-envelope"> </span> <span class='ml5'><?php echo $v['mData']['email']; ?></span></div>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class='clearfix'></div>
                </div>
            </div>
        <?php $i++;
        endforeach; ?>
    </div>
    <div role="tabpanel" class="tab-pane " id="other">
        <div class='mt20 mb20 font14 color3'><?php echo Yii::t("site", "Family Number: ");?><?php echo "{$otherFamilyNum}"; ?>，<?php echo Yii::t("site", "Children Number: ");?><?php echo "{$otherChildNum}"; ?><span class='ml10'><?php echo Yii::t("site", "The list only counts active students.");?></span></div>
        <?php $i = 1;
        foreach ($otherData as $id) : $v = $childData[$id]; ?>
            <div class="panel panel-default " id='StuFamily'>
                <div class="panel-body flex">
                    <div class='p10 bg'>
                        <div class='font14 color3'><?php echo Yii::t("site", "Family #");?> <?php echo $i; ?></div>
                        <?php foreach ($v['children'] as $child) : ?>
                            <div class="media">
                                <div class="media-left pull-left media-middle">
                                    <a href="<?php echo $this->createUrl('/child/index/index', array('childid' => $child['id'])); ?>" target="_blank">
                                        <img src="<?php echo $child['photo']; ?>" data-holder-rendered="true" class="reserveImg">
                                    </a>
                                </div>
                                <div class="media-body pt5 media-middle">
                                    <h4 class="media-heading"><span class='font14 color3 mr10'><?php echo $child['name']; ?></span> <span class='font12 color6'><?php echo $child['id']; ?></span></h4>
                                    <div class="color6 font12 mt5 ">
                                        <?php if ($child['gradeGroup'] == 'SS') : ?>
                                            <?php if ($child['school_other'] == 1) { echo '<span class="label label-warning">'.$child['school_abb'].'</span>';} ?> <span class='classTitle ss'><?php echo $child['gradeGroup']; ?> | <?php echo $child['classTitle']; ?></span>
                                        <?php endif; ?>
                                        <?php if ($child['gradeGroup'] == 'ES' || $child['gradeGroup'] == 'KG') : ?>
                                            <?php if ($child['school_other'] == 1) { echo '<span class="label label-warning">'.$child['school_abb'].'</span>';} ?> <span class='classTitle'><?php echo $child['gradeGroup']; ?> | <?php echo $child['classTitle']; ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <div class='p15 parentFamily'>
                        <div class='font14 color3'><?php echo Yii::t("child", "Parent Info");?></div>
                        <?php if (isset($v['fData'])) : ?>
                            <div class='mt20'>
                                <div><span class='font14 color3 mr10'><?php echo $v['fData']['name']; ?></span> <span class="parentLabel"><?php echo Yii::t("site", "Father");?></span><?php echo $v['fData']['id']; ?></div>
                                <div class='mt5 color6 font14'><span class="glyphicon glyphicon-phone"> </span><span class='mr20 ml5'><?php echo $v['fData']['mphone']; ?></span><span class="glyphicon glyphicon-envelope"> </span> <span class='ml5'><?php echo $v['fData']['email']; ?></span></div>
                            </div>
                        <?php endif; ?>
                        <?php if (isset($v['mData'])) : ?>
                            <div class='mt20'>
                                <div><span class='font14 color3 mr10'><?php echo $v['mData']['name']; ?></span> <span class="parentLabel"><?php echo Yii::t("site", "Mother");?></span><?php echo $v['mData']['id']; ?></div>
                                <div class='mt5 color6 font14'><span class="glyphicon glyphicon-phone"> </span><span class='mr20 ml5'><?php echo $v['mData']['mphone']; ?></span><span class="glyphicon glyphicon-envelope"> </span> <span class='ml5'><?php echo $v['mData']['email']; ?></span></div>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class='clearfix'></div>
                </div>
            </div>
        <?php $i++;
        endforeach; ?>
    </div>
    <div role="tabpanel" class="tab-pane " id="otherSchool">
        <div class='mt20 mb20 font14 color3'><?php echo Yii::t("site", "Family Number: ");?><?php echo "{$otherSchoolFamilyNum}"; ?>，<?php echo Yii::t("site", "Children Number: ");?><?php echo "{$otherSchoolChildNum}"; ?><span class='ml10'><?php echo Yii::t("site", "The list only counts active students.");?></span></div>
        <?php $i = 1;
        foreach ($ohterSchoolData as $id) : $v = $childData[$id]; ?>
            <div class="panel panel-default " id='StuFamily'>
                <div class="panel-body flex">
                    <div class='p10 bg'>
                        <div class='font14 color3'><?php echo Yii::t("site", "Family #");?> <?php echo $i; ?></div>
                        <?php foreach ($v['children'] as $child) : ?>
                            <div class="media">
                                <div class="media-left pull-left media-middle">
                                    <a href="<?php echo $this->createUrl('/child/index/index', array('childid' => $child['id'])); ?>" target="_blank">
                                        <img src="<?php echo $child['photo']; ?>" data-holder-rendered="true" class="reserveImg">
                                    </a>
                                </div>
                                <div class="media-body pt5 media-middle">
                                    <h4 class="media-heading"><span class='font14 color3 mr10'><?php echo $child['name']; ?></span> <span class='font12 color6'><?php echo $child['id']; ?></span></h4>
                                    <div class="color6 font12 mt5 ">
                                        <?php if ($child['gradeGroup'] == 'SS') : ?>
                                            <?php if ($child['school_other'] == 1) { echo '<span class="label label-warning">'.$child['school_abb'].'</span>';} ?> <span class='classTitle ss'><?php echo $child['gradeGroup']; ?> | <?php echo $child['classTitle']; ?></span>
                                        <?php endif; ?>
                                        <?php if ($child['gradeGroup'] == 'ES' || $child['gradeGroup'] == 'KG') : ?>
                                            <?php if ($child['school_other'] == 1) { echo '<span class="label label-warning">'.$child['school_abb'].'</span>';} ?> <span class='classTitle'><?php echo $child['gradeGroup']; ?> | <?php echo $child['classTitle']; ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <div class='p15 parentFamily'>
                        <div class='font14 color3'><?php echo Yii::t("child", "Parent Info");?></div>
                        <?php if (isset($v['fData'])) : ?>
                            <div class='mt20'>
                                <div><span class='font14 color3 mr10'><?php echo $v['fData']['name']; ?></span> <span class="parentLabel"><?php echo Yii::t("site", "Father");?></span><?php echo $v['fData']['id']; ?></div>
                                <div class='mt5 color6 font14'><span class="glyphicon glyphicon-phone"> </span><span class='mr20 ml5'><?php echo $v['fData']['mphone']; ?></span><span class="glyphicon glyphicon-envelope"> </span> <span class='ml5'><?php echo $v['fData']['email']; ?></span></div>
                            </div>
                        <?php endif; ?>
                        <?php if (isset($v['mData'])) : ?>
                            <div class='mt20'>
                                <div><span class='font14 color3 mr10'><?php echo $v['mData']['name']; ?></span> <span class="parentLabel"><?php echo Yii::t("site", "Mother");?></span><?php echo $v['mData']['id']; ?></div>
                                <div class='mt5 color6 font14'><span class="glyphicon glyphicon-phone"> </span><span class='mr20 ml5'><?php echo $v['mData']['mphone']; ?></span><span class="glyphicon glyphicon-envelope"> </span> <span class='ml5'><?php echo $v['mData']['email']; ?></span></div>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class='clearfix'></div>
                </div>
            </div>
        <?php $i++;
        endforeach; ?>
    </div>
    <div role="tabpanel" class="tab-pane " id="thisSchool">
        <div class='mt20 mb20 font14 color3'><?php echo Yii::t("site", "Family Number: ");?><?php echo "{$thisSchoolFamilyNum}"; ?>，<?php echo Yii::t("site", "Children Number: ");?><?php echo "{$thisSchoolChildNum}"; ?><span class='ml10'><?php echo Yii::t("site", "The list only counts active students.");?></span></div>
        <?php $i = 1;
        foreach ($thisSchoolIds as $id) : $v = $childData[$id]; ?>
            <div class="panel panel-default " id='StuFamily'>
                <div class="panel-body flex">
                    <div class='p10 bg'>
                        <div class='font14 color3'><?php echo Yii::t("site", "Family #");?> <?php echo $i; ?></div>
                        <?php foreach ($v['children'] as $child) : ?>
                            <div class="media">
                                <div class="media-left pull-left media-middle">
                                    <a href="<?php echo $this->createUrl('/child/index/index', array('childid' => $child['id'])); ?>" target="_blank">
                                        <img src="<?php echo $child['photo']; ?>" data-holder-rendered="true" class="reserveImg">
                                    </a>
                                </div>
                                <div class="media-body pt5 media-middle">
                                    <h4 class="media-heading"><span class='font14 color3 mr10'><?php echo $child['name']; ?></span> <span class='font12 color6'><?php echo $child['id']; ?></span></h4>
                                    <div class="color6 font12 mt5 ">
                                        <?php if ($child['gradeGroup'] == 'SS') : ?>
                                            <?php if ($child['school_other'] == 1) { echo '<span class="label label-warning">'.$child['school_abb'].'</span>';} ?> <span class='classTitle ss'><?php echo $child['gradeGroup']; ?> | <?php echo $child['classTitle']; ?></span>
                                        <?php endif; ?>
                                        <?php if ($child['gradeGroup'] == 'ES' || $child['gradeGroup'] == 'KG') : ?>
                                            <?php if ($child['school_other'] == 1) { echo '<span class="label label-warning">'.$child['school_abb'].'</span>';} ?> <span class='classTitle'><?php echo $child['gradeGroup']; ?> | <?php echo $child['classTitle']; ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <div class='p15 parentFamily'>
                        <div class='font14 color3'><?php echo Yii::t("child", "Parent Info");?></div>
                        <?php if (isset($v['fData'])) : ?>
                            <div class='mt20'>
                                <div><span class='font14 color3 mr10'><?php echo $v['fData']['name']; ?></span> <span class="parentLabel"><?php echo Yii::t("site", "Father");?></span><?php echo $v['fData']['id']; ?></div>
                                <div class='mt5 color6 font14'><span class="glyphicon glyphicon-phone"> </span><span class='mr20 ml5'><?php echo $v['fData']['mphone']; ?></span><span class="glyphicon glyphicon-envelope"> </span> <span class='ml5'><?php echo $v['fData']['email']; ?></span></div>
                            </div>
                        <?php endif; ?>
                        <?php if (isset($v['mData'])) : ?>
                            <div class='mt20'>
                                <div><span class='font14 color3 mr10'><?php echo $v['mData']['name']; ?></span> <span class="parentLabel"><?php echo Yii::t("site", "Mother");?></span><?php echo $v['mData']['id']; ?></div>
                                <div class='mt5 color6 font14'><span class="glyphicon glyphicon-phone"> </span><span class='mr20 ml5'><?php echo $v['mData']['mphone']; ?></span><span class="glyphicon glyphicon-envelope"> </span> <span class='ml5'><?php echo $v['mData']['email']; ?></span></div>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class='clearfix'></div>
                </div>
            </div>
        <?php $i++;
        endforeach; ?>
    </div>
</div>

<script>
    <?php $jsonData = json_encode($childData, JSON_PRETTY_PRINT); ?>
    const jsonData = <?php echo $jsonData; ?>;
    console.log('<?php echo json_encode($idData); ?>');
    function exportData() {
        // 创建一个新的工作簿
        const workbook = XLSX.utils.book_new();

        // 创建一个工作表
        const worksheetData = [];

        // 添加表头
        worksheetData.push(["家庭ID", "学校", "学生ID", "学生姓名", "学生班级", "父亲姓名", "父亲电话", "父亲邮件",  "母亲姓名", "母亲电话", "母亲邮件",]);

        Object.keys(jsonData).forEach(key => {
            jsonData[key].children.forEach(e => {
                const fName = jsonData[key].fData ? jsonData[key].fData.name : '';
                const fPhone = jsonData[key].fData ? jsonData[key].fData.mphone : '';
                const fEmail = jsonData[key].fData ? jsonData[key].fData.email : '';
                const mName = jsonData[key].mData ? jsonData[key].mData.name : '';
                const mPhone = jsonData[key].mData ? jsonData[key].mData.mphone : '';
                const mEmail = jsonData[key].mData ? jsonData[key].mData.email : '';
                const row = [key, e.school_abb, e.id, e.name, e.classTitle, fName, fPhone, fEmail, mName, mPhone, mEmail];
                worksheetData.push(row);
            });
        });
        // 将数据转换为 SheetJS 工作表
        const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

        // 将工作表添加到工作簿中
        XLSX.utils.book_append_sheet(workbook, worksheet);

        // 生成 Excel 文件并保存
        XLSX.writeFile(workbook, "family_list.xlsx");
    }
</script>