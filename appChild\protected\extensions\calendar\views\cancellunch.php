<style>
*{
	margin:0;
	padding:0;
}
#api-s-c{
	background:transparent;
	width:665px;
	color: #333333;
	font-family: "Trebuchet MS";
	font-size:12px;
}
#api-s-c ul, li {
    list-style-type: none;
}
#api-s-c .box-head{
	background: url("<?php echo Yii::app()->theme->baseUrl; ?>/images/shadow_header.png") no-repeat;
	height:18px;
	width:665px;
}
#api-s-c .box-footer{
	background: url("<?php echo Yii::app()->theme->baseUrl; ?>/images/shadow_bottom.png") no-repeat;
	height:14px;
	width:665px;
}
#api-s-c .calendar{
	background: url("<?php echo Yii::app()->theme->baseUrl; ?>/images/shadow_body.png") repeat-y scroll 0 0 transparent;
	padding: 0px 10px 4px;
}
#api-s-c .calendar h5{
	font-family: Georgia;
	font-size: 18px;
}
#api-s-c .calendar .caldesc{
	margin: 10px 0 10px 10px;
}
#api-s-c .calendar .caldesc li{
    background: url("http://www.ivyonline.cn/themes/ivy/images/right_gray.gif") no-repeat scroll 4px 8px transparent;
    color: #666666;
    line-height: 1.6em;
    padding-left: 14px;
}
#api-s-c .calendar .a-month{
	margin: 20px 0 40px;
}
#api-s-c .calendar .a-month .m-head{
	margin: 10px 0 4px;
	background: url("http://www.ivyonline.cn/themes/ivy/images/a-mon-head.gif") no-repeat scroll 0px 0px transparent;
	height: 51px;
}

.m-head li{
	float: left;
}
.m-head li.m-num{
	font-family: Georgia;
	font-size: 30px;
	color: #ffffff;
	line-height: 50px;
	width: 60px;
	text-align: center;
	display: inline-block;
}
.m-head li.m-year{
	font-family: Georgia;
	font-size: 18px;
	color: #2B3E42;
	line-height: 25px;
	margin-top: 20px;
	width: 60px;
	text-align: center;
	display: inline-block;

}
.m-head li.m-lite{
	font-family: Georgia;
	font-size: 16px;
	color: #2B3E42;
	line-height: 20px;
	margin-top: 0px;
	margin-left: -50px;
	width: 100px;
	text-align: left;
	display: inline-block;
}
.m-head li.m-days{
	float: right !important;
	font-family: Georgia;
	font-size: 12px;
	color: #2B3E42;
	line-height: 25px;
	margin-top: 20px;
	margin-right: 20px;
	width: 100px;
	text-align: right;
	display: inline-block;
}
.m-head li.m-days span{
	padding-right: 4px;
}
.m-data .l-main{
	float: left;
	margin-left: 9px;
	width: 323px;
	margin-right: 10px;
}
.m-data .r-desc{
	float: left;
	width: 300px;
}
#m-calendar{
	width:323px;
	margin:0;
	padding:0;
	border-left: 1px solid #D3CAAA;
	border-spacing: 0px;
	border-collapse:collapse;
}

#m-calendar td, #m-calendar th{
	line-height: 24px;
	width:46px;
	margin:0;
	padding:0;
	text-align:center;
	height:26px;
    border-bottom: 1px solid #D3CAAA;
    border-right: 1px solid #D3CAAA;
	font-family: Georgia;
	font-size: 12px;
}
#m-calendar th{
	background: #DED5B3;
	color: #ffffff;
}
#m-calendar .noschool, #m-calendar .schoolday, #m-calendar .weekend, #m-calendar .thisdate, #m-calendar .s1010{
	background:url("http://www.ivyonline.cn/themes/ivy/images/days-bg.gif") no-repeat scroll 0px 0px transparent;
	font-weight: bold;
}
#m-calendar .schoolday{
	background-position: 0 -26px;
	font-weight:bold;
}
#m-calendar .s1030{
	background-position: 0 -26px !important;
	font-weight: bold !important;
	color:#333333 !important;
}
#m-calendar .s1020{ /*fullday event*/
	background-position: 0 -52px;
	color:#ffffff;
	border-top: 0;
	border-left: 0;
}
#m-calendar .s1010{ /*fullday holiday*/
	background-position: 0 -78px;
	color:#ffffff;
}
#m-calendar .s2020{ /*halfday event*/
	background-position: 0 -130px;
	color:#2C5700;
}
#m-calendar .s2010{ /*halfday holiday*/
	background-position: 0 -156px;
	color:#2C5700;
}
#m-calendar .thisdate{
	background-position: 0 -104px;
	color:#ffffff;
	border-top: 0;
	border-left: 0;
}
#m-calendar .weekend{
	color:#999999;
	background-position: 0 0px;
	font-weight: normal;
}
#m-calendar .noschool{
	font-weight: normal;
}
#m-calendar .hint{
	color:#dedede;
	font-weight: normal;
}
.c-memo{
	padding-top: 26px;
	line-height: 26px;
	color: #2B3E42;
}
.clear{
	clear:both;
}

#m-calendar .tr_is_bg{
    background: url("<?php echo Yii::app()->theme->baseUrl?>/images/icons/booked.png") no-repeat scroll center center transparent;
	height: 28px;
}
#m-calendar .tr_refund_bg {
    background: url("<?php echo Yii::app()->theme->baseUrl?>/images/icons/refunded.png") no-repeat scroll center center transparent;
	height: 28px;
}
#m-calendar .waiting_bg {
    background: url("<?php echo Yii::app()->theme->baseUrl?>/images/loading.gif") no-repeat scroll center center transparent;
}
#m-calendar .waiting_check_bg {
    background: url("<?php echo Yii::app()->theme->baseUrl?>/images/icons/cancelled.png") no-repeat scroll center center transparent;
}
#m-calendar a{
	color:#000;
}
#m-calendar .have_lunch_bg {
    background: url("<?php echo Yii::app()->theme->baseUrl?>/images/icons/consumed.png") no-repeat scroll center center transparent;
}
</style>
<script type="text/javascript">
<!--
var i=0;
function cancelLunch(day)
{
    var operate = '';
	if($("#day_" + day).hasClass('tr_is_bg')){
		var confirm_info = '<?php echo Yii::t("payment", 'Are you sure to cancel?')?>';
        operate = 'DO';
	}else{
		var confirm_info = '<?php echo Yii::t("payment", 'Are you sure to recover?')?>';
        operate = 'UNDO';
	}
	if (window.confirm(confirm_info))
	{
		if (i==0){
			i = 1;
			$("#day_" + day).addClass('waiting_bg');
			$.ajax({
	            type : "POST",
	            url : '<?php echo Yii::app()->getController()->createUrl('//child/payment/cancelLunch')?>',
	            data : "day=" + day +"&operate="+ operate,
	            success : function(data) {
	                eval("var redata =" + data + ";");
	                if(redata.status == 0) {
	                	if (operate == 'DO'){
                            $("#day_" + day).removeClass('tr_is_bg');
                            $("#day_" + day).removeClass('waiting_bg');
                            $("#day_" + day).addClass('waiting_check_bg');
                        }else if (operate == 'UNDO'){
                            $("#day_" + day).removeClass('waiting_check_bg');
                            $("#day_" + day).removeClass('waiting_bg');
                            $("#day_" + day).addClass('tr_is_bg');
                        }
	                }
	                else
	                {
	                	$("#noticeDialog").html("<div>"+redata.status+"</div>");
	                	$("#noticeDialog").dialog("open");
	                    $("#day_" + day).removeClass('waiting_bg');
	                }
	                i=0;
	            }
	        });
		}
	}
}

function showAlert(){
	alert("No permission"+"\r\n"+"没有权限");
	return false;
}
//-->
</script>
<?php

$this->widget('zii.widgets.jui.CJuiDialog', array(
		'id'=>'noticeDialog',
		'options'=>array(
		// 提示
				'title'=>Yii::t("global", 'Error'),
				'autoOpen'=>false,
		),
));

$canCancel = Yii::app()->user->checkAccess('cancelLunch', array("childid" => $this->getController()->getChildId()));

?>
<p class="desc">
<?php echo Yii::t("lunch", "Please click on the checkmarks in the school calendar below to request for lunch cancellation and click again to resume the lunch.");?>
</p>

<p class="desc">
<?php echo Yii::t("lunch","Once the administrators approve the request for lunch cancellation, lunch fee will be refunded to the personal credit accordingly.");?>
<!--午餐取消的餐费会在行政确认之后返还到孩子个人账户。-->
</p>

<div class="flash-info">
<?php echo Yii::t('lunch', 'Note: Only lunch that has been paid can be cancelled.Please proceed the request for lunch cancellation/resumption before 9am everyday.')?>
</div>

	<table>
		<tr><td width="30">
			<?php echo CHtml::image(Yii::app()->theme->baseUrl."/images/icons/booked.png", Yii::t("lunch","Subscribed"));?>
		</td><td>
			<?php echo Yii::t("lunch","Lunch subscribed. Click to cancel lunch.");?>
		</td></tr>
		<tr><td>
			<?php echo CHtml::image(Yii::app()->theme->baseUrl."/images/icons/cancelled.png", Yii::t("lunch","Awaiting for approval"));?>
		</td><td>
			<?php echo Yii::t("lunch","Lunch cancelled and to be approved by administrators. Click to resume lunch.");?>
		</td></tr>
		<tr><td>
			<?php echo CHtml::image(Yii::app()->theme->baseUrl."/images/icons/consumed.png", Yii::t("lunch","Awaiting for approval"));?>
		</td><td>
			<?php echo Yii::t("lunch","Lunch consumed.");?>
		</td></tr>
		<tr><td>
			<?php echo CHtml::image(Yii::app()->theme->baseUrl."/images/icons/refunded.png", Yii::t("lunch","Awaiting for approval"));?>
		</td><td>
			<?php echo Yii::t("lunch","Lunch cancelled and refunded to personal credit.");?>
		</td></tr>
	</table>

<div id="api-s-c">

	<div class="box-head">
	</div>
	<div class="calendar">

		<h5><?php
			echo sprintf(Yii::t("global", "%d-%d School Calendar"), $schoolYear, $schoolYear+1);
		?></h5>

		<?php foreach($months as $key=>$month):?>
		<div class="a-month">
			<div class="m-head">
				<ul>
					<li class="m-days"><span><?php echo $month['schoolday'];?></span><?php echo Yii::t("global", 'Days');?></li>
					<li class="m-num"><?php echo $month['calendars']['month_num'];?></li>
					<li class="m-year"></li>
					<li class="m-lite"><?php echo $month['calendars']['year_month'];?></li>
				</ul>
			</div>
			<div class="clear"></div>
			<div class="m-data">
				<div class="l-main">
					<table id="m-calendar">
					<thead>
					<tr>
						<th><span title="Sunday">Sun</span></th>
						<th><span title="Monday">Mon</span></th>
						<th><span title="Tuesday">Tue</span></th>
						<th><span title="Wednesday">Wed</span></th>
						<th><span title="Thursday">Thu</span></th>
						<th><span title="Friday">Fri</span></th>
						<th><span title="Saturday">Sat</span></th>
					</tr>
					</thead>
					<tbody>
					<tr align="center">

					<?php
					if ($month['calendars']['calendar']):
					foreach($month['calendars']['calendar'] as $day):
					?>

					  <td class="
						<?php

						if(isset($day['hint'])&&$day['hint'])
						{
							echo 'hint ';
						}
						elseif(isset($day['isweekend'])&&$day['isweekend'] && !$day['isschoolday'])
						{
							echo 'weekend ';
						}
						elseif ($day['isschoolday'] && isset($lunchInday[$day['day']]['in']) && $lunchInday[$day['day']]['in'])
						{
							if (isset($refundLunchList[$day['day']]))
							{
								if ($refundLunchList[$day['day']]['is_check'])
								{
									echo 'tr_refund_bg ';
								}
								else
								{
									echo 'waiting_check_bg ';
								}
							}
							else
							{
								if ($day['daystamp']+$dayTamp > time())
								{
									echo 'tr_is_bg ';
								}
								else
								{
									echo 'have_lunch_bg ';
								}
							}
						}

						?>
					  "
                          id="day_<?php if (isset($day['day']))echo $day['day']?>"
					  >
						<?php
							if (empty($day['date']))
							{
								echo ' ';
							}
							else
							{
								if (isset($day['isschoolday'],$lunchInday[$day['day']]['in']) && $day['isschoolday'] && $lunchInday[$day['day']]['in'])
								{
									if (isset($refundLunchList[$day['day']])&&$refundLunchList[$day['day']]['is_check'])
									{
										echo $day['date'];
									}
									else
									{
										if ($day['daystamp']+$dayTamp > time())
										{
											if($canCancel)
											echo CHtml::link($day['date'],'javascript:void(0);',array('onclick'=>'cancelLunch('.$day['day'].');'));
											else
											echo CHtml::link($day['date'],'javascript:void(0);',array('onclick'=>'showAlert();'));
										}
										else
										{
											echo $day['date'];
										}
									}
								}
								else
								{
									echo $day['date'];
								}

							}

						?>
					  </td>
					<?php if(isset($day['newline']) && $day['newline']):?>
					</tr>
						<?php if(isset($day['islastday'])&&$day['islastday']==0):?>
						<?php endif;?>
					<?php endif;?>

					<?php endforeach;?>
					<?php endif;?>
					<?php if(!empty($month['endnulls'])):?>
						</tr>
					<?php endif;?>
					</tbody>
					</table>
				</div>
				<div class="r-desc c-memo">
                    <?php if (isset($descs[$key])):?>
                    <?php $f = new CFormatter();?>
					<?php echo $f->formatNtext((Yii::app()->language =='zh_cn') ? $descs[$key]['memo_cn']:$descs[$key]['memo_en']);?>
                    <?php endif;?>
				</div>
				<div class="clear"></div>
			</div>
		</div>
		<?php endforeach; ?>
		<div class="clear"></div>
	</div>

	<div class="box-footer">
	</div>
</div>

