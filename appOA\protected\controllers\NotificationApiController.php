<?php

/*
 * 通知类API
 */
class NotificationApiController extends Controller{
    
    private $notification;
    private $staffUid;
    public function init() {
        Yii::import('application.components.user.Notification');
        $this->notification = new Notification();
        $this->staffUid = unserialize(base64_decode($_POST['staff_uid']));
    }
    
    /*
     * API访问权限验证
     */
    public function beforeAction($action){
        if(!OA::isProduction()){
            return true;
        }
        if(Yii::app()->request->isPostRequest && isset($_POST['postTime']) && isset($_POST['flagid']))
        {
            $myCode = md5(sprintf("%s&%s&%s", $_POST['postTime'], $_POST['flagid'], OA::SECURITY_KEY));
			$time = time();
			if($myCode == $_POST['postKey'] && abs($time - $_POST['postTime'] ) < 300 ){
                return TRUE;
            }
        }
        return false;
    }
    
    /**
     * 保存通知信息
     * @param array $data e.g. array('staff_uid'=>$staffUid,'flagid'=>$flagId,'title'=>$title,'type'=>'type')
     * @staff_uid 需要通知人的ID
     * @flagid    关联业务的主键
     * @type      通知的类型 e.g. 请假审请、加班审请
     * @return boolean
     */
    public function actionSave() {
        if (is_array($this->staffUid) && count($this->staffUid)){
            foreach ($this->staffUid as $val){
                $data =  array('staff_uid'=>$val,'flagid'=>$_POST['flagid'],'title'=>$_POST['title'],'type'=>$_POST['type']);
                $this->notification->save($data);
            }
            return true;
        }
    }
    
    /**
     * 更新通知状态
     * @param int $flagId   业务关联表主键
     * @param int $type     通知分类表主键
     * @param int $staffId  通知人的主键
     * return boolean
     */
    public function actionUpdate(){
        return $this->notification->update($this->staffUid,$_POST['flagid'],$_POST['type']);
    }
    
     /**
     * 取消某个用户某种类型的业务通知
     * @param int $staffUid 需要取消的用户ID
     * @param int $flagId   相关联的业务ID
     * @param int $type     通知类型
     * @return boolean
     */
    public function actionDelete(){
        return $this->notification->deleteData($this->staffUid,$_POST['flagid'],$_POST['type']);
    }
    
     /**
     * 取得某个用户按类型统计的条数
     * @param int  $staffUid 员工ID
     * @return array e.g. array('hr_leave'=>2);
     */
    public function actionGet(){
        return $this->notification->getData($_POST['staff_uid']);
    }

    public function actionGetVisitCount()
    {
        $ret = array();
        $branchid = Yii::app()->request->getParam('branchid');

        $sql = "select count(*) as count from ivy_ivyschools_visit where status=0";
        if ($branchid != 'BJ_TYG')
            $sql .= " and schoolid='".$branchid."'";
        $rs = Yii::app()->db->createCommand($sql)->queryRow();
        $ret['pending'] = $rs['count'];

        $sql = "select count(*) as count from ivy_visits_record where category='appointment' and appointment_date=".strtotime('today');
        if ($branchid != 'BJ_TYG')
            $sql .= " and schoolid='".$branchid."'";
        $rs = Yii::app()->db->createCommand($sql)->queryRow();
        $ret['confirm'] = $rs['count'];

        Yii::import('common.models.points.*');
        $sql = "select count(*) as count from ".PointsOrder::model()->tableName()." where status in(".PointsStatus::STATS_CREATED.",".PointsStatus::STATS_CONFIRMED.",".PointsStatus::STATS_READYTOSHIP.") and category='order'";
        if ($branchid != 'BJ_TYG')
            $sql .= " and schoolid='".$branchid."'";
        $rs = Yii::app()->db->createCommand($sql)->queryRow();
        $ret['opgcount'] = $rs['count'];

        $sql = "select count(*) as count from ".PointsOrder::model()->tableName()." where status in(".PointsStatus::STATS_SHIPPING.",".PointsStatus::STATS_RECEIVED.") and category='order'";
        if ($branchid != 'BJ_TYG')
            $sql .= " and schoolid='".$branchid."'";
        $rs = Yii::app()->db->createCommand($sql)->queryRow();
        $ret['opscount'] = $rs['count'];

        echo CJSON::encode($ret);
    }
}
?>
