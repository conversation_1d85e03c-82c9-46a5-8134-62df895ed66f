<style>
    [v-cloak] {
		display: none;
	}
    .colorBlue{
        color:#4D88D2
    }
    .title{
        display: flex;
        margin-top:32px;
        align-items: center;
        background:#F0F5FB;
        padding:8px 24px;
        border-radius:2px
    }
    .lineHeight{
        line-height:15px
    }
    .blueLine{
        width: 4px;
        height: 14px;
        background: #4D88D2;
    }
    .maxWidth{
        max-width:750px
    }
    .status{
        border-radius: 4px;
        border: 1px solid #E5E6EB;
        padding:16px 18px
    }
    .colorRed{
        color:#D9534F
    }
    .colorWait{
        color:#F0AD4E
    }
    .colorGreen{
        color:#5CB85C
    }
    .tag{
        font-size: 12px;
        color: #333333;
        line-height: 12px;
        padding:4px 6px;
        background: #F2F3F5;
        border-radius: 2px;
    }
    .table{
        border:1px solid #E5E6EB !important;
        margin:0
    }
    .table  th{
        background:#F7F7F8;
    }
    .table  td, .table  th{
        border: 1px solid #E5E6EB !important;
        vertical-align: middle !important;
        padding:16px !important;
    }
    .table > tbody + tbody{
        border-top: 1px solid #E5E6EB;
    }
    .verticalTop{
        vertical-align: top !important;
    }
    .bgGreen{
        background:#5CB85C
    }
    .bgRed{
        background:#D9534F
    }
    .result{
        padding:4px 6px;
        font-size:12px;
        color:#fff;
        border-radius:3px
    }
    .linkFile{
        padding:6px 8px;
        background: #F7F7F8;
        border-radius: 4px;
    }
    .linkFile:hover{
        background: #F7F7F8;
        border-radius: 4px;
        cursor: pointer;
    }
    .linkImg{
        width: 72px;
        height:72px;
        position: relative;
        display: inline-block;
        border-radius: 4px;
        margin-right: 8px;
        margin-bottom: 8px;
        object-fit: cover;

    }
    .linkImg span{
        position: absolute;
        right:5px;
        top:5px;
        font-size:16px
    }
    .linkImg img{
        width: 72px;
        height:72px;
        border-radius: 4px;
        object-fit: cover;
    }
    .scroll-box::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius   : 10px;
        background-color: #ccc;
        background-image: none
    }
    .scroll-box::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0);
        background   : #fff;
        border-radius: 10px;
        border:none
    }
    .img32{
        width:32px;
        height:32px;
        border-radius:50%;
        object-fit: cover;
    }
    .link{
        display:inline-block;
        background: #F7F7F8;
        border-radius: 4px;
        cursor: pointer;
        margin-right:16px
    }
    .p0{
        padding:0
    }
    .width100{
        width:60px
    }
    .positionText{
        position: absolute;
        top: 16px;
    }
    .uploadLoading{
        width: 72px;
        height: 72px;
        background: #D9D9D9;
        border-radius: 4px;
        line-height: 72px;
        text-align: center;
    }
    .lineRight{
        position: absolute;
        height: 100%;
        width: 1px;
        background: #E5E6EB;
        right: 50%;
    }
    .horizontalLine{
        width: 40px;
        height: 1px;
        background: #D9D9D9;
        margin-top: 8px;
    }
    .imgData {
        display:inline-block
    }
    .stepNum{
        width: 18px;
        height: 18px;
        background: #F0F5FB;
        border: 1px solid #4D88D2;
        color: #4D88D2;
        border-radius:50%;
        display: inline-block;
        text-align:center;
        margin-top:2px
    }
    .stepNumCheck{
        background:#4D88D2;
        color: #fff;
    }
    .check{
        background: #FAFAFA;
        padding:12px 16px
    }
    .imgLi{
        list-style: none;
        padding-left: 0;
    }
    .imgLi li{
        display:inline-block
    }
    .viewer-prev::before, .viewer-next::before{
        background-image: none !important;
    }
    .bgEdit{
        background: #FAFAFA;
    }
    .label{
        font-size:100%
    }
    .lineStep{
        width: 1px;
        background: #4D88D2;
        position: absolute;
        left: 9px;
        top: 20px;
        height: 100%;
    }
    .max_width{
        max-width:750px
    }
    .tableText textarea{
        border: none;
        /* padding: 0; */
        resize: none;
        color:#666
    }
    .tableText textarea:focus{
        background: #F0F5FB;
    }
    .delIcon{
        background: #fff;
        border-radius: 50%;
    }
    .dot{
        width: 6px;
        height: 6px;
        background: #D9534F;
        border-radius: 50%;
        position: absolute;
        right: -4px;
        top: 6px;
    }
    .colorC{
        color:#ccc
    }
    .font_normal{
        font-weight: normal;
    }
    .tablist{
        border:1px solid #DCDEE0;
        padding:14px 20px;
        border-radius:4px;
        cursor: pointer;
    }
    .tablist:hover{
        border:1px solid #4D88D2;

    }
    .bgYellow{
        background:#FDF8F1
    }
    .bgRed{
        background:#FCF1F1
    }
    .Highlights{
        padding: 16px 24px;
        border-radius: 5px;
        align-items: center;
        display: flex;
        flex:1
    }
    .statusImg{
        width:16px;
        height:16px
    }
    .statusTag{
        font-size:12px;
        padding:4px 6px;
        border-radius:4px;
        margin-left: -4px;
    }
    .bgBlue{
        background:#F0F5FB
    }
    .bggreen{
        background:#F2F9F2
    }
    .filterDefault{
        color:#666;
        padding:6px 8px;
        cursor: pointer;
        display: inline-block;
    }
    .filterDefault .badge{
        color:#4D88D2;
        background:#F0F5FB;
        font-size:12px;
        padding:2px 5px
    }
    .filterActive{
        padding:6px 8px;
        background:#4D88D2;
        color:#fff;
        border-radius:4px;
        cursor: pointer;
        display: inline-block;
    }
    .filterActive .badge{
        color:#4D88D2;
        background:#fff;
        font-size:12px;
        padding:2px 5px
    }
    .text_overflow{
        white-space: nowrap; /* 不换行 */
        overflow: hidden; /* 隐藏超出的内容 */
        text-overflow: ellipsis; /* 用省略号表示被隐藏的部分 */
    }
    .qualityTime{
        padding:10px 16px;
    }
    .text-left{
        text-align:left !important
    }
    .rectifyAgain{
        padding:16px;
        border-radius:4px
    }
    .rectifyAgain .width60{
        width:60px
    }
    .rectifyAgain .borderSpan{
        border-left: 1px solid #999;
        padding-left: 10px;
        margin-left: 8px;
    }
    .badgeBlue{
        background:#4D88D2;
        color:#fff
    }
    .second{
        display:flex;
        align-items:center;
    }
    .second:hover .badge{
        background:#4D88D2 !important;
        color:#fff !important;
    }
    @media screen and (max-width: 767px) {
        .lineRight{
            display:none
        }
        .stepFlex{
            display:none
        }
        .checkTime{
            text-align:left
        }
    }
    .timeStatus{
        display: flex;
        justify-content: flex-end;
        margin-right: -16px;
        align-items:center;
        margin-top:8px
    }
    .textTime{
        color:#ED6A0C
    }
    .textTimeStatus{
        padding:0 12px;
        height: 20px;
        line-height:20px;
        color:#fff;
        border-radius:100px 0px 0px 100px;
        margin-left:15px
    }
    .textTimeStatus1{background: #F0AD4E;}
    .textTimeStatus2{background: #D9534F;}
    .textTimeStatus3{background: #5BC0DE;}
    .textTimeStatus4{background: #5CB85C;}
    .pl0{
        padding-left:0
    }
    .pr0{
        padding-right:0
    }
</style>
<div class="container-fluid"  id='template' v-cloak >
    <ol class="breadcrumb">
        <?php if($moperation==1){?>
        <li><?php echo CHtml::link(Yii::t('site','校园管理'), array('//moperation/default/index')) ?></li>
        <?php }else{?>
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <?php }?>
        <li><?php echo CHtml::link(Yii::t('site','质检系统'), array($this->branchSelectParams['urlArray'][0]))?></li>
        <li class="active">质检详情</li>
    </ol>
    <div class="row " v-if='temp_info.info'>
        <div class='col-md-12 col-sm-12 mt8'  v-loading.fullscreen.lock="loading">
            <div>
                <span class='font16 fontBold cur-p colorBlue' @click='tabTitle'>{{temp_info.info.title}} <span class='el-icon-arrow-down'></span></span>
                <span class='tag ml12'>{{detailData.tasks_data.title}}</span>
                <span class='tag ml12'>{{detailData.tasks_data.start_year}}</span>
                <span class='tag ml12'>{{detailData.school_info.title}}</span>
            </div>
            <div class='font12 color6 maxWidth mt12'>{{temp_info.info.desc}}</div>
            <div class='mt16 mb24' v-if='isHQstaff==1'>
                <div class='bggreen qualityTime'>
                    <div class='flex align-items font14'>
                        <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/quality5.png' ?>" alt="" style='width:24px;height:24px'>
                        <span class='color6  ml8'>质检计划</span>
                        <span class='colorBlue ml16 cur-p' @click='setDate()'><span class='el-icon-edit'></span> 编辑</span>
                    </div>
                    <div class='ml24 pl8  flex align-items mt10' v-if='detailData.master_data.start_date!=""'>
                        <span class='color3 font14 mr8 ' >{{detailData.master_data.start_date}}</span>
                        <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/quality7.png' ?>" style='width:32px;height:18px' alt="" v-if='detailData.master_data.check_form=="1"'> 
                        <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/quality6.png' ?>" style='width:32px;height:18px' alt="" v-if='detailData.master_data.check_form=="2"'> 
                       <span class='color6 font12 ml15' style='margin-top:2px' v-if='detailData.master_data.remark!=null && detailData.master_data.remark!=""'>备注：{{detailData.master_data.remark}}</span>
                    </div>
                </div>
            </div>
            <div class='mt24'>
                <el-tabs v-model="activeName" @tab-click="handleClick">
                    <el-tab-pane label="所有质检项" name="first">
                        <!-- :style="'max-height:'+(height-285)+'px;overflow-x: hidden;'" -->
                        <div  class='scroll-box mb24' >
                            <div class='text-right' v-if='isHQstaff==1'>
                                <div v-if='detailData.master_data.status==0'>   
                                    <button type="button" v-if='!initEdit' class="btn btn-default ml20" @click='cancelEdit'>放弃修改，返回</button>
                                </div>
                                <div v-else>
                                    <label class="checkbox-inline font14 relative" style='line-height:1.6'>
                                        <input type="checkbox" v-model='showeditOpenStatus'  @change='editOpenStatus("tip")'> 开放给校园（不合格的质检结果开放给校园后需要PD确认）
                                    </label>
                                    <button type="button" class="btn btn-primary ml20" @click='editQualityInspect'>修改</button>
                                </div>
                            </div>
                            <div class='flex mt24' v-if='detailData.master_data.status==0'>
                                <div class='flex1 flex mr20'>
                                    <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/qualityIcon2.png' ?>" alt="" class='statusImg  mt2 mr4'> 
                                    <span class='font14 mr16 colorWait'>亮点</span>
                                    <textarea class="form-control flex1" rows="3" placeholder="请输入" v-model='detailData.master_data.sparkle'></textarea>
                                </div>
                                <div class='flex1 flex ml20'>
                                    <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/qualityIcon1.png' ?>" alt="" class='statusImg  mt2 mr4'>
                                    <span class='font14 mr16 colorRed'> 反馈</span>
                                    <textarea class="form-control flex1" rows="3" placeholder="请输入" v-model='detailData.master_data.feedback'></textarea>
                                </div>
                            </div>
                            <div v-else>
                                <div class='flex mt24' style='align-items: stretch;'>
                                    <div class='Highlights mr12 bgYellow'>
                                        <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/qualityIcon2.png' ?>" alt="" class='statusImg mr4'> 
                                        <span class='font14 fontBold colorWait'>亮点</span>
                                        <div class='font12 color6 ml24 flex1 ' style='margin-top:2px'>
                                            <span v-if='detailData.master_data.sparkle==null' class='colorC'>无数据</span>
                                            <span v-else v-html='html(detailData.master_data.sparkle)'></span>
                                        </div>
                                    </div>
                                    <div class='Highlights ml12 bgRed'>
                                        <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/qualityIcon1.png' ?>" alt="" class='statusImg mr4'>
                                        <span class='font14 fontBold colorRed'>反馈</span>
                                        <div class='font12 color6 ml24 flex1 ' style='margin-top:2px'>
                                            <span  v-if='detailData.master_data.feedback==null' class='colorC'>无数据</span>
                                            <span v-else v-html='html(detailData.master_data.feedback)'></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class='mt24 font14 color3' v-if='detailData.master_data.status!=0'>
                                    <span class='mr8 ' :class='filterType=="all"?"filterActive":"filterDefault"' @click='switchInfo("all")'>全部</span>
                                    <span class='mr8' :class='filterType=="1"?"filterActive":"filterDefault"' @click='switchInfo(1)'>合格<span class="badge ml4">{{resultsNum(1)}}</span></span>
                                    <span class='mr8' :class='filterType=="2"?"filterActive":"filterDefault"' @click='switchInfo(2)'>不合格<span class="badge ml4">{{resultsNum(2)}}</span></span>
                                    <span class='mr8' :class='filterType=="99"?"filterActive":"filterDefault"' @click='switchInfo(99)'>不适用<span class="badge ml4">{{resultsNum(99)}}</span></span>
                                    <span class='mr8' :class='filterType==""?"filterActive":"filterDefault"' @click='switchInfo("")'>未填写<span class="badge ml4">{{resultsNum("")}}</span></span>
                                </div>
                                <div v-for='(list,index) in categories'>
                                    <div class='title'>
                                        <div class='blueLine'></div>
                                        <span class='font14 colorBlue fontBold ml8 lineHeight'>{{list.title}}</span> 
                                    </div>
                                    <div class=''>
                                        <div v-for='(item,idx) in list.sub'>
                                            <div v-if='item.attach.length!=0'>
                                                <table class='table table-bordered font14 mt24' style='width: 100%;table-layout: fixed'  v-for='(_item,i) in item.attach'>
                                                    <tr class='font14'>
                                                        <th colspan='8'>
                                                            <div class='flex'>
                                                                <div class='flex1'>{{_item.number}}.{{item.title}}</div>
                                                                <div>
                                                                    <span class='color9 font_normal'>检查形式：<span v-for='(check,i) in item.check_form' class='color3 font_normal'>{{temp_info.check_form[check]}}<span v-if='i+1<item.check_form.length'>、</span></span></span>
                                                                    <span class='color9 ml24 font_normal'>检查频率：<span class='color3 font_normal'>{{temp_info.check_frequency[item.frequency]}}</span> </span>
                                                                </div>
                                                            </div>
                                                        </th>
                                                    </tr>
                                                    <tr>
                                                        <th  class='font14 text-center color6' style='width:200px'>合格标准</th>
                                                        <td colspan='7' class='bgEdit font14'><div v-html='html(_item.standards)'></div></td>
                                                    </tr>
                                                    <tr>
                                                        <th class='text-center color6'>检查纪实</th>
                                                        <td colspan='5' style='vertical-align: top !important'>
                                                            <div class='mb8 font14 fontBold'><span class='el-icon-edit'></span> 描述</div>
                                                            <div v-if='detailData.master_data.status==0'>
                                                                <div v-if='!templateData[_item.id].showRecord'>
                                                                    <div  class='colorC font14 text-center ' @click='focusInput(templateData[_item.id])' >{{detailData.master_data.status==0?"请输入":"无数据"}}</div>                    
                                                                </div>
                                                                <el-input
                                                                    v-else
                                                                    :ref="'text'+templateData[_item.id].sub_attach_id"
                                                                    type="textarea"
                                                                    class='tableText'
                                                                    :rows="5"
                                                                    v-model='templateData[_item.id].record'
                                                                    placeholder="请输入内容">
                                                                </el-input>
                                                            </div>
                                                            <div v-else >
                                                                <div style='max-height:110px;overflow-y:auto' class='scroll-box color6'  v-if='templateData[_item.id].record!=""' v-html='html(templateData[_item.id].record)'></div>
                                                                <div  class='colorC font14 text-center' v-else >无数据</div>    
                                                            </div>
                                                        </td>
                                                        <td colspan='2' style='vertical-align: top !important'>
                                                            <div class='flex font14 pb10'>
                                                                <span class='flex1 fontBold'>
                                                                    <span class='el-icon-paperclip font16'></span> 附件（{{templateData[_item.id].file.adduploadLinkList.length+templateData[_item.id].file.adduploadImgList.length}}）
                                                                </span>
                                                                <div  v-if='detailData.master_data.status==0'>
                                                                    <div class='colorBlue cur-p' :id="'upload'+templateData[_item.id].sub_attach_id" @click="initUploader(templateData[_item.id].sub_attach_id)" >
                                                                        <span class='el-icon-plus'></span> 上传 
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div style='max-height:110px;overflow-y:auto' class='scroll-box' v-if='templateData[_item.id].file.adduploadLinkList.length>0 || templateData[_item.id].file.adduploadImgList.length>0'>
                                                                <div class='mt10'>
                                                                    <ul class='imgLi'  :id='"fileimg_"+_item.id'  v-if='templateData[_item.id].file.adduploadImgList.length>0'>
                                                                        <li class='imgData' v-for='(list,i) in templateData[_item.id].file.adduploadImgList'>
                                                                            <div v-if="list.types=='1'">
                                                                                <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Uploading");?></div>
                                                                            </div>
                                                                            <div v-else class='linkImg'>
                                                                                <span class='el-icon-error delIcon cur-p' @click='delFile(templateData[_item.id].file.adduploadImgList,i)' v-if='detailData.master_data.status==0'></span>
                                                                                <img  :src="list.fileUrl_thumb" alt="" :data-original="list.url" @click='showImg(_item.id,templateData[_item.id].file.adduploadImgList,"file")'>
                                                                            </div>
                                                                        </li>
                                                                    </ul>
                                                                </div>
                                                                <div class='mt8' v-if='templateData[_item.id].file.adduploadLinkList.length>0'>
                                                                    <div class='flex font14 align-items linkFile' v-for='(list,index) in templateData[_item.id].file.adduploadLinkList'>
                                                                        <span class='glyphicon glyphicon-paperclip mr8 colorBlue'></span>
                                                                        <a target="_blank" class='flex1'  style='line-height:26px' :href='list.url' v-if='!list.isEdit'>{{list.name}}</a>
                                                                        <span class='glyphicon glyphicon-trash icon' v-if='list.file_key!="" && detailData.master_data.status==0' @click='delFile(templateData[_item.id].file.adduploadLinkList,index)'></span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div v-else class='colorC font14 text-center ' >{{detailData.master_data.status==0?"上传":"无数据"}}</div>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <th  class='font14 text-center color6' >质检结果</th>
                                                        <td :colspan='7' class='relative'>
                                                            <div class='flex align-items'>                                                        
                                                                <div class='flex1 text-center' v-if='detailData.master_data.status==0'>                                                       
                                                                    <div  >
                                                                        <el-dropdown v-if='templateData[_item.id].results==""'>
                                                                            <span v-if='templateData[_item.id].results==""' class='colorC font14 cur-p' >请选择</span>
                                                                            <el-dropdown-menu slot="dropdown">
                                                                                <el-dropdown-item @click.native='templateData[_item.id].results=1'>合格</el-dropdown-item>
                                                                                <el-dropdown-item @click.native='templateData[_item.id].results=2'>不合格</el-dropdown-item>
                                                                                <el-dropdown-item @click.native='templateData[_item.id].results=99'>不适用</el-dropdown-item>
                                                                            </el-dropdown-menu>
                                                                        </el-dropdown>
                                                                        <div v-else>
                                                                            <el-dropdown  >
                                                                                <span class="label cur-p" :class='templateData[_item.id].results==2?"label-danger":templateData[_item.id].results==1?"label-success":"label-warning"'>{{templateData[_item.id].results==2?"不合格":templateData[_item.id].results==1?"合格":"不适用" }} <i class="el-icon-arrow-down el-icon--right"></i></span>
                                                                                </el-button>
                                                                                <el-dropdown-menu slot="dropdown">
                                                                                    <el-dropdown-item @click.native='templateData[_item.id].results=1'>合格</el-dropdown-item>
                                                                                    <el-dropdown-item @click.native='templateData[_item.id].results=2'>不合格</el-dropdown-item>
                                                                                    <el-dropdown-item @click.native='templateData[_item.id].results=99'>不适用</el-dropdown-item>
                                                                                </el-dropdown-menu>
                                                                            </el-dropdown>
                                                                            <!-- <span class='colorBlue font14 cur-p ' style='margin-right:32px' v-if='detailData.master_data.status==0 && templateData[_item.id].results!=""' @click='templateData[_item.id].results=""'>清空</span> -->
                                                                        </div>
                                                                    </div>
                                                                    <div v-if='templateData[_item.id].results==2' class='text-center'>
                                                                        <div class='font12 color6 mt20 mb4'>预计整改完成时间</div>
                                                                        <el-date-picker
                                                                            size='small'
                                                                            type="date"
                                                                            format="yyyy-MM-dd"
                                                                            value-format="yyyy-MM-dd"
                                                                            placement="bottom-start"
                                                                            v-model='templateData[_item.id].envision_day'
                                                                            placeholder="<?php echo Yii::t('ptc','Select a date') ?>">
                                                                        </el-date-picker>
                                                                    </div>
                                                                </div>
                                                                <div v-else class='flex1 '>
                                                                    <div v-if='templateData[_item.id].results!=""' class='text-center' style='display:inline-block;'>
                                                                        <span class="label " :class='templateData[_item.id].results==2?"label-danger":templateData[_item.id].results==1?"label-success":"label-warning"'>{{templateData[_item.id].results==2?"不合格":templateData[_item.id].results==1?"合格":templateData[_item.id].results==99?"不适用":'' }}</span>
                                                                        <span v-if='templateData[_item.id].results==2'>
                                                                            <span class='colorRed font14 ml10'>预计整改完成时间：{{templateData[_item.id].envision_day}}</span>       
                                                                            <!-- <span class='color3 font14'>{{templateData[_item.id].envision_day}}</span>                   -->
                                                                        </span>
                                                                    </div>
                                                                    <div v-else class='colorC font14 text-center'>无数据</div>
                                                                </div>
                                                                <div v-if='templateData[_item.id].quality_user'>
                                                                    <div class=''> 
                                                                        <div class='flex align-items'>
                                                                            <img class='img32' :src="detailData.user_info[templateData[_item.id].quality_user].photoUrl" alt="">
                                                                            <div class='ml8 mr16 font14 color3'>{{detailData.user_info[templateData[_item.id].quality_user].name}}</div>
                                                                            <div class='font12 color6 mt5'>{{templateData[_item.id].updated_at}}</div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class='text-right mt24 mb24' v-if='detailData.master_data.status==0'>
                                <label class="checkbox-inline relative">
                                    <input type="checkbox" v-model='showeditOpenStatus'> 开放给校园（不合格的质检结果开放给校园后需要PD确认）
                                </label>
                                <button type="button" class="btn btn-primary ml20" :disabled='btnDis' @click='saveQualityInspect'>提交</button>
                            </div>
                        </div>
                    </el-tab-pane>
                    <el-tab-pane label="整改记录" name="second">
                        <span slot="label"  class='relative second'><span class='dot' v-if='needMe=="1"'></span> 整改记录 <span class="badge ml5" :class='activeName=="second"?"badgeBlue":""' v-if='rectify_count!=0'>{{rectify_count}}</span></span>
                        <!-- :style="'max-height:'+(height-285)+'px;overflow-x: hidden;'" -->
                        <div v-if='showrecordList && recordList.length!=0'   class='scroll-box'>
                            <div class="panel panel-default" v-for='(list,index) in recordList'>
                                <div class="panel-heading" @click='list.show=!list.show'>
                                    <div class='row '>
                                        <div class='col-xs-12 col-md-7 col-sm-7 align-items flex'>                                        
                                            <span v-if='list.show' class='el-icon-arrow-up'></span>
                                            <span class='el-icon-arrow-down' v-else></span>
                                            <div class='flex1 ml12 mt4'>
                                                <div class='font14 color3 fontBold'>{{list.sub_attach_info.number}}</div>
                                                <div class='font12 color6 mt4'><span>所属子类别：{{list.sub_attach_info.sub_category.title}}</span><span class='ml24'>所属分类：{{list.sub_attach_info.sub_category.category.title}}</span></div>
                                            </div>
                                        </div>
                                        <div  class='col-xs-12 col-md-5 col-sm-5'>
                                            <div class='flex align-items'>
                                                <div class='flex1 stepFlex'></div>
                                                <div class='flex ml20'>
                                                    <div class='flex'>
                                                        <span class='font16 ' :class='list.PD_sure==2?"colorRed el-icon-warning":"colorWait el-icon-question"' v-if='list.PD_sure==0  || list.PD_sure==2'></span>
                                                        <span class='el-icon-success font16 colorGreen' v-if='list.PD_sure==1'></span>
                                                        <span class='color6 font12 ml4'>PD确认</span>
                                                    </div>
                                                    <div class='horizontalLine'></div>
                                                    <div class='flex'>
                                                        <span class='font16' :class='list.status==2?"colorRed el-icon-warning":"colorWait el-icon-question"' v-if='list.status==0 || list.status==2'></span>
                                                        <span class='el-icon-success font16 colorGreen' v-if='list.status==1 '></span>
                                                        <span class='color6 font12 ml4'>校园整改</span>
                                                    </div>
                                                    <div class='horizontalLine'></div>
                                                    <div class='flex'>
                                                        <span class='el-icon-question font16 colorWait' v-if='list.audit_status==0 || list.audit_status==1|| list.audit_status==3'></span>
                                                        <span class='el-icon-success font16 colorGreen' v-if='list.audit_status==2'></span>
                                                        <span class='color6 font12 ml4'>审核验收</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class='timeStatus'>
                                                <span class='textTime' v-if='list.timeOutStatus==1 || list.timeOutStatus==3'>预计整改完成时间：{{list.detail.record.envision_day}}</span>
                                                <span class='textTimeStatus textTimeStatus1'  v-if='list.timeOutStatus==3'>未超时</span>
                                                <span class='textTimeStatus textTimeStatus2'  v-if='list.timeOutStatus==1'>已超时</span>
                                                <span class='textTimeStatus textTimeStatus3'  v-if='list.timeOutStatus==2'>超时整改完成</span>
                                                <span class='textTimeStatus textTimeStatus4'  v-if='list.timeOutStatus==4'>按时整改完成</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="panel-body p0" v-show='list.show'>
                                    <div class='row relative'>
                                        <div class='lineRight'></div>
                                        <div class='col-md-6 col-sm-6 col-xs-12 pr0'>
                                            <div class='p24'>
                                                <div class='font14 fontBold'>合格标准</div>
                                                <div class='font14 color6 mt12' v-html='html(list.sub_attach_info.standards)'></div>
                                                <div class='flex mt24'>
                                                    <span class='font14 fontBold'>检查形式</span>
                                                    <div class='flex1 font14 color6 ml24'><span v-for='(check,i) in list.sub_attach_info.sub_category.check_form'>{{temp_info.check_form[check]}}<span v-if='i+1<list.sub_attach_info.sub_category.check_form.length'>、</span></span></div>
                                                </div>
                                                <div class='flex mt24'>
                                                    <span class='font14 fontBold'>检查频率</span>
                                                    <div class='flex1 font14 color6 ml24'>{{temp_info.check_frequency[list.sub_attach_info.sub_category.frequency]}}</div>
                                                </div>
                                                <div class='font14 fontBold mt24'>检查纪实</div>
                                                <div class='row mt16'>
                                                    <div class=' col-md-6 col-xs-12  flex align-items'>
                                                        <img class='img32' :src="list.user_info[list.detail.record.quality_user].photoUrl" alt="">
                                                        <span class='font14 color3 ml8 mr16 text_overflow'>{{list.user_info[list.detail.record.quality_user].name}}</span>
                                                        <el-tag size="mini">质检人</el-tag>
                                                    </div>
                                                    <div class='mt8 col-md-6 col-xs-12 text-right checkTime font12 color6'>质检时间：{{list.detail.record.updated_at}}</div>
                                                </div>
                                                <div class='font14 color6 mt16' v-html='html(list.detail.record.record)'></div>
                                                <ul class='imgLi mt16'  :id='"recordimg_"+list.detail.record.sub_attach_id'  v-if='list.detail.record.attach.img'>
                                                    <li v-for='(item,idx) in list.detail.record.attach.img'>
                                                        <img :src="item.fileUrl_thumb" alt="" :data-original="item.url" class='mr8 mb8 linkImg' @click='showImg(list.detail.record.sub_attach_id,list.detail.record.attach.img,"record")' alt=""  >
                                                    </li>
                                                </ul>
                                                <div  v-if='list.detail.record.attach.link' class='mt16'>
                                                    <div class='link linkFile mb10' v-for='(item,idx) in list.detail.record.attach.link'>
                                                        <span class='el-icon-paperclip font16 colorBlue'></span>
                                                        <a class='flex1 colorBlue ml5' :href='item.url' target="_blank">{{item.name}}</a>
                                                    </div>
                                                </div>
                                                <div  class='flex mt24'>
                                                    <span class='font14 fontBold'>预计整改完成时间</span>
                                                    <div class='flex1 font14 color6 ml24'>{{list.detail.record.envision_day}}</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class=' col-md-6 col-sm-6 col-xs-12 pl0' v-if='list.PD_sure==0  || list.PD_sure==2'>
                                            <div class='p24'>
                                                <div class='flex'>
                                                    <div class='blueLine'></div>
                                                    <span class='font14 color3 fontBold ml8 lineHeight'>PD确认</span> 
                                                </div>
                                                <div class='mt24'>
                                                    <div class='flex align-items' v-if='list.detail.rectify.is_PD_user && list.PD_sure==0'>
                                                        <div class='flex1 color3 font14'>确认本项整改内容</div>
                                                        <button type="button" class="btn btn-danger" @click='confirmPD(list,"2")'>拒绝</button>
                                                        <button type="button" class="btn btn-success ml12"  @click='confirmPD(list,"1")'>通过</button>
                                                    </div>
                                                    <div v-else>
                                                        <el-alert
                                                            v-if='list.PD_sure==0'
                                                            title="等待PD确认整改内容"
                                                            type="warning"
                                                            :closable="false"
                                                            show-icon>
                                                        </el-alert>
                                                        <div class='bgRed rectifyAgain' v-if='list.PD_sure==2'>
                                                            <div class='colorRed font14'><span class='el-icon-warning'></span><span class='ml5 fontBold'>整改内容未通过，已驳回给质检人</span></div>
                                                            <div class='flex font12 mt8' v-if='list.detail.rectify.PD_sure_content!="" && list.detail.rectify.PD_sure_content!=null'>
                                                                <span class='color6'>备注：</span>
                                                                <span class='flex1 color3 ml5' v-html='html(list.detail.rectify.PD_sure_content)'></span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class='col-md-6 col-sm-6 col-xs-12 pl0'  v-if='list.PD_sure==1'>
                                            <div class='p24'>
                                                <div class='bggreen rectifyAgain'>
                                                    <div class='colorGreen font14'><span class='el-icon-success'></span><span class='ml5 fontBold'>PD已确认本项整改内容</span></div>
                                                    <div class='flex font12 mt8' v-if='list.detail.rectify.PD_sure_content!="" && list.detail.rectify.PD_sure_content!=null'>
                                                        <span class='color6'>备注：</span>
                                                        <span class='flex1 color3 ml5' v-html='html(list.detail.rectify.PD_sure_content)'></span>
                                                    </div>
                                                </div>
                                                <div class='flex mt24 pt8'>
                                                    <div class='blueLine'></div>
                                                    <span class='font14 color3 fontBold ml8 lineHeight'>校园整改</span> 
                                                </div>
                                                <div v-if='list.status==0 || list.status==2'>  
                                                    <div class='mt24'>
                                                        <el-alert
                                                            v-if='list.status==0 && isHQstaff==1'
                                                            title="校园暂未提交整改情况"
                                                            type="warning"
                                                            :closable="false"
                                                            show-icon>
                                                        </el-alert>
                                                        <div v-if='list.status==2' class='bgYellow rectifyAgain'>
                                                            <div class='colorWait font14'><span class='el-icon-warning'></span><span class='ml5 fontBold'>审核不通过，校园待重新填写整改情况</span></div>
                                                            <div class='flex font12 mt12'>
                                                                <span class='color6 width60'>拒绝原因：</span>
                                                                <span class='flex1 color3 ml5' v-html='html(list.detail.rectify.fail_comment)'></span>
                                                            </div>
                                                            <div class='flex font12 mt8'>
                                                                <span class='color6 width60 '>操作人：</span>
                                                                <span class='flex1 color3 ml5'>{{list.user_info[list.detail.rectify.fail_user].name}} <span class='borderSpan'>{{list.detail.rectify.fail_time}}</span> </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div v-if='isHQstaff!=1'>                                      
                                                        <div class='flex mt24'>
                                                            <span class='font14 color6 width100'>整改措施</span>
                                                            <div class='flex1 ml24'>
                                                                <el-input
                                                                    type="textarea"
                                                                    :rows="3"
                                                                    v-model='list.detail.rectify.school_action'
                                                                    placeholder="请输入内容">
                                                                </el-input>
                                                            </div>
                                                        </div>
                                                        <div class='flex mt24'>
                                                            <span class='font14 color6 width100'> 整改后资料</span>
                                                            <div class='flex1 ml24'>
                                                                <div>
                                                                    <span class='colorBlue font14 cur-p' :id="'rectify'+list.detail.rectify.sub_attach_id" @click="rectifyUpload(index)">
                                                                        <span class='el-icon-circle-plus-outline '></span> 上传
                                                                    </span>
                                                                </div>
                                                                <div >
                                                                    <div class='mt10'>
                                                                        <div class='uploadImg' v-if='list.detail.rectify.file.adduploadImgList.length>0'>
                                                                            <div class='imgData'  v-for='(item,i) in list.detail.rectify.file.adduploadImgList'>
                                                                                <div v-if="item.types=='1'">
                                                                                    <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Uploading");?></div>
                                                                                </div>
                                                                                <div v-else class='linkImg'>
                                                                                    <span class='el-icon-error delIcon cur-p' @click='delFile(list.detail.rectify.file.adduploadImgList,i)'></span>
                                                                                    <img  :src="item.url" alt="">
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class='mt16' v-if='list.detail.rectify.file.adduploadLinkList.length>0'>
                                                                        <div class='flex font14 align-items linkFile' v-for='(item,i) in list.detail.rectify.file.adduploadLinkList'>
                                                                            <span class='glyphicon glyphicon-paperclip mr8 colorBlue'></span>
                                                                            <a target="_blank" class='flex1'  style='line-height:26px' :href='item.url' v-if='!item.isEdit'>{{item.name}}</a>
                                                                            <span class='flex1' v-else><input type="input" class='inputStyle'  @keyup.enter="item.name=attachmentName"  v-model='attachmentName' ></span>
                                                                            <template v-if='!item.isEdit'>
                                                                                <span class='glyphicon glyphicon-trash icon' v-if='item.file_key!=""' @click.stop='delFile(list.detail.rectify.file.adduploadLinkList,i)'></span>
                                                                            </template>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class='flex mt24'>
                                                            <span class='font14 color6 width100'>完成日期</span>
                                                            <div class='flex1 ml24'>
                                                                <el-date-picker
                                                                    size='small'
                                                                    type="date"
                                                                    v-model='list.detail.rectify.school_done_date'
                                                                    format="yyyy-MM-dd"
                                                                    value-format="yyyy-MM-dd"
                                                                    placement="bottom-start"
                                                                    placeholder="<?php echo Yii::t('ptc','Select a date') ?>">
                                                                </el-date-picker>
                                                            </div>
                                                        </div>
                                                        <hr>
                                                        <div><button type="button" class="btn btn-primary pull-right" :disabled='btnDis' @click='saveSchoolEdit(list,index)'>提交</button></div>
                                                        <div class='clearfix'></div>
                                                    </div>
                                                </div>
                                                <div v-if='list.status==1' class='pb20'>                                        
                                                    <div class='flex mt24'>
                                                        <span class='font14 color6 width100'>整改措施</span>
                                                        <div class='flex1 ml24 color3 font14' v-html='html(list.detail.rectify.school_action)'></div>
                                                    </div>
                                                    <div class='flex mt24'>
                                                        <span class='font14 color6 width100'> 整改后资料</span>
                                                        <div class='flex1 ml24'>
                                                            <div v-if='list.detail.rectify.attach.img || list.detail.rectify.attach.link'>   

                                                        
                                                            <ul class='imgLi'  :id='"rectifyimg_"+list.detail.rectify.sub_attach_id'  v-if='list.detail.rectify.attach.img'>
                                                                <li v-for='(item,idx) in list.detail.rectify.attach.img'>
                                                                    <img :src="item.fileUrl_thumb" alt="" :data-original="item.url" class='mr8 mb8 linkImg' @click='showImg(list.detail.rectify.sub_attach_id,list.detail.rectify.attach.img,"rectify")' alt=""  >
                                                                </li>
                                                            </ul>
                                                            <div  v-if='list.detail.rectify.attach.link' class='mt16'>
                                                                <div class='link linkFile mb10' v-for='(item,idx) in list.detail.rectify.attach.link'>
                                                                    <span class='el-icon-paperclip font16'></span>
                                                                    <a class='flex1 colorBlue ml5' :href='item.url' target="_blank">{{item.name}}</a>
                                                                </div>
                                                            </div>
                                                            </div>
                                                            <div v-else class='font14 color9'>
                                                                无
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class='flex mt24'>
                                                        <span class='font14 color6 width100'>完成日期</span>
                                                        <div class='flex1 ml24 color3 font14'>
                                                        {{list.detail.rectify.school_done_date}}
                                                        </div>
                                                    </div>
                                                    <div class='flex mt24  align-items'>
                                                        <span class='font14 color6 width100'>填写人</span>
                                                        <div class='flex1 ml24 color3 font14'>
                                                            <div class='flex align-items'>
                                                                <img class='img32' :src="list.user_info[list.detail.rectify.school_user].photoUrl" alt="">
                                                                <span class='font14 color3 ml8 mr16 flex1'>{{list.user_info[list.detail.rectify.school_user].name}}</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class='flex mt24'>
                                                        <span class='font14 color6 width100'>填写时间</span>
                                                        <div class='flex1 ml24 color3 font14'>
                                                        {{list.detail.rectify.school_time}}
                                                        </div>
                                                    </div>
                                                </div>
                                                <div v-if='isHQstaff==1 || (list.status==1 || list.status==2)'>                                        
                                                    <div class='flex mt24 pt8'>
                                                        <div class='blueLine'></div>
                                                        <span class='font14 color3 fontBold ml8 lineHeight'>审核验收</span> 
                                                    </div>
                                                    <div class='mt24'>
                                                        <div v-for='(node,node_index) in list.nodes'>
                                                            <div class='flex relative'>
                                                                <span class='lineStep' v-if='node_index+1<list.nodes.length'></span>
                                                                <div>
                                                                    <span class='stepNum'  :class='list.detail.rectify[node.status]==1?"stepNumCheck":""'>{{node_index+1}}</span> 
                                                                </div>
                                                                <div class='flex1 ml10 mb24'>
                                                                    <div class='font14 color3 mb12'>{{node.title}}</div>
                                                                    <div>
                                                                        <div class='check mt8'>
                                                                            <div class='row' v-if='list.detail.rectify[node.user].length>1'>
                                                                                <span class='col-md-4 flex align-items mb12' v-for='(item,i) in list.detail.rectify[node.user]'>
                                                                                    <img class='img32' :src="list.user_info[item].photoUrl" alt="">
                                                                                    <span class='font14 color3 ml8 text_overflow'>{{list.user_info[item].name}}</span>
                                                                                </span>
                                                                            </div>
                                                                            <div class='flex align-items' >
                                                                                <div class='flex1'>
                                                                                    <div v-if='list.detail.rectify[node.user].length==1'>
                                                                                        <div class='flex align-items'  v-for='(item,i) in list.detail.rectify[node.user]'>
                                                                                            <img class='img32' :src="list.user_info[item].photoUrl" alt="">
                                                                                            <span class='font14 color3 ml8 mr16 text_overflow'>{{list.user_info[item].name}}</span>
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class='colorWait font12 text-right mr16'  v-else-if='list.detail.rectify[node.status]==0 && list.detail.rectify[node.auth]'>
                                                                                        <span class='el-icon-info'></span> 任意成员操作即可
                                                                                    </div>
                                                                                </div>                                                                        
                                                                                <div v-if='list.status==0' class='color9 font14'>
                                                                                    未开始
                                                                                </div>
                                                                                <div v-if='list.status==1 || list.status==2'>
                                                                                    <div class='font14 colorWait' v-if='list.detail.rectify[node.status]==0 && !list.detail.rectify[node.auth]'>等待验收</div>
                                                                                    <div v-if='list.detail.rectify[node.status]==0 && list.detail.rectify[node.auth]'>
                                                                                        <button type="button" class="btn btn-danger" @click='deptCheckChange(list,node.name,"2")'>拒绝</button>
                                                                                        <button type="button" class="btn btn-success ml12"  @click='deptCheckChange(list,node.name,"1")'>通过</button>
                                                                                    </div>
                                                                                    <div v-if='list.detail.rectify[node.status]==1' class='text-right'>
                                                                                        <span class='color6 font12 text_overflow'>{{list.detail.rectify[node.time]}}</span>
                                                                                        <el-tag type="success" class='ml16'><span class='el-icon-check'></span> 已验收</el-tag>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <div v-if='list.detail.rectify[node.status]==1 && list.detail.rectify[node.comment]!=null && list.detail.rectify[node.comment]!=""' class='color6 mt8 font12'>
                                                                                备注：{{list.detail.rectify[node.comment]}}
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-else-if='showrecordList' class='mt24'>
                            <el-empty description="暂无数据"></el-empty>
                        </div>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </div>
    </div>
    <div style='position: fixed; z-index: 10000;display:none' class='nextImg'>
        <div id='prev' class='viewer-prev'></div>
        <div id='next' class='viewer-next'></div>
    </div>
    <!-- 删除 -->
    <div class="modal fade" id="tipModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static" data-keyboard="false"> 
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t('leave', '提示') ?></h4>
                </div>
                <div class="modal-body p24" >
                  <div v-if='tipType=="open"'>
                    <div v-if='showeditOpenStatus'><?php echo Yii::t('directMessage', '是否开放给校园（不合格的质检结果开放给校园后需要PD确认）') ?></div> 
                    <div v-else><?php echo Yii::t('directMessage', '是否取消开放给校园') ?></div> 
                  </div>
                  <div v-if='tipType=="cancel"'>
                        <div>您做的修改将被忽略，确定返回么？</div>
                  </div>
                </div>
                <div class="modal-footer"  v-if='tipType=="open"'>
                    <button type="button" class="btn btn-default" data-dismiss="modal" @click='showeditOpenStatus=!showeditOpenStatus'><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" @click='editOpenStatus()'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
                <div class="modal-footer"  v-if='tipType=="cancel"'>
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" @click='cancelEdit()'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="tabModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" > 
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t('leave', '切换质检项目') ?></h4>
                </div>
                <div class="modal-body p24 scroll-box" :style="'max-height:'+(height-160)+'px;overflow-x: hidden;'">
                    <div class='font14 color3 fontBold' v-if='detailData.tasks_data'>{{detailData.tasks_data.title}}</div>
                    <div class='row'>
                        <div v-for='(item,idx) in tabIndexList.list' class=' mt16 col-md-6' @click='id=item.id;tabClick()'>
                            <div class='tablist'>
                                <div class='font14 color3 fontBold cur-p mb8 text_overflow' >{{item.inspect_items.title}}</div>
                                <div v-if='tabIndexList.rectify_count[item.id].count==0' class='color9 mb8'>无整改项</div>
                                <div v-else class='mb8 color6 font12'><span class='fontBold color3' > {{tabIndexList.rectify_count[item.id].count}}</span> 个整改项</div>
                                <span v-if='item.status==4' class='statusTag colorBlue bgBlue'>待审核...</span>
                                <span v-if='item.status==3'  class='statusTag colorWait bgYellow'>待校园整改...</span>
                                <span v-if='item.status==6'  class='statusTag colorWait bgYellow'>待确认</span>
                                <span v-if='item.status==0 || item.status==1 || item.status==2'  class='statusTag colorRed bgRed'>待填写质检</span>
                                <span v-if='item.status==5'  class='statusTag colorGreen bggreen'>已完成 {{item.updated_time}}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 预计质检时间 -->
    <div class="modal fade" id="addDateModal" tabindex="-1" role="dialog" data-backdrop="static" >
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t('leave', '设置预计质检时间') ?></h4>
                </div>
                <div class="modal-body relative" style='padding:24px'>
                    
                    <div class='font14 color3 mb24' v-if='detailData.school_info'>
                        <span class='font14 color3 fontBold'>{{temp_info.info.title}} </span>
                        <span class='tag ml12'>{{detailData.school_info.title}}</span>
                    </div>
                    <div class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-2 control-label font14 color6 text-left">质检时间</label>
                            <div class="col-sm-10">
                                <el-date-picker
                                    size='small'
                                    type="date"
                                    format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd"
                                    placement="bottom-start"
                                    v-model='qualityTime'
                                    placeholder="<?php echo Yii::t('ptc','Select a date') ?>">
                                </el-date-picker>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label font14 color6 text-left">质检形式</label>
                            <div class="col-sm-10">
                                <label class="radio-inline">
                                    <input type="radio"  value="1" v-model='check_form'> 实地考察
                                </label>
                                <label class="radio-inline">
                                    <input type="radio"  value="2" v-model='check_form'> 远程抽查
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label font14 color6 text-left">备注</label>
                            <div class="col-sm-10">
                                <el-input
                                    type="textarea"
                                    :rows="2"
                                    placeholder="请输入内容"
                                    v-model="remark">
                                </el-input>
                            </div>
                        </div>
                    </div>
                </div> 
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel");?></button>
                    <el-button type="button" :disabled='btnDis' class="btn btn-primary" @click='confirmDate()' ><?php echo Yii::t("global", "OK");?></el-button>
                </div>
            </div>
        </div>
    </div>
    <!-- 质检不合格 -->
    <div class="modal fade" id="unQualityModal" tabindex="-1" role="dialog" data-backdrop="static" >
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentCommentsModalLabel">{{checkList.status=='2'?'拒绝':'确认'}}</h4>
                </div>
                <div class="modal-body relative" style='padding:24px'>
                    <div v-if="checkList.type=='confirm'">
                        <div class='font14 color6 mb12'>备注</div>
                        <textarea class="form-control" rows="3" v-model='reasonRemark' placeholder="请输入"></textarea>
                    </div>
                    <div v-else>
                        <div v-if="checkList.status=='2'" class='mb24'> <el-alert
                            title="拒绝后校园将重新整改"
                            type="warning"
                            :closable="false"
                            show-icon>
                        </el-alert></div>
                        <div>
                            <div class='font14 color6 mb12'>{{checkList.status=='2'?'拒绝原因':'备注'}}</div>
                            <textarea class="form-control" rows="3" v-model='reasonRemark' placeholder="请输入"></textarea>
                        </div>
                    </div>
                </div> 
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel");?></button>
                    <el-button type="button" :disabled='btnDis' v-if="checkList.type=='confirm'" class="btn btn-primary" @click='PDConfirm()' ><?php echo Yii::t("global", "OK");?></el-button>
                    <el-button type="button" :disabled='btnDis' v-else class="btn btn-primary" @click='unQuality()' ><?php echo Yii::t("global", "OK");?></el-button>
                    
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var height=document.documentElement.clientHeight;
    var isHQstaff = '<?php echo $isHQstaff ?>';
    var id = '<?php echo $_GET['id']?>';
    var config = {
        runtimes: 'html5,flash,html4', //上传模式,依次退化
        container: 'template', //上传区域DOM ID，默认是browser_button的父元素，
        browse_button: '', //上传选择的点选按钮，**必需**
        token: '',
        flash_swf_url: '/js/plupload/Moxie.swf', // flash的相对地址
        auto_start: true, //选择文件后自动上传，若关闭需要自己绑定事件触发上传
        multi_selection: false, //不允许多选
        url:'https://upload-z1.qiniup.com',
        id:'',
        init: {
            'FilesAdded': function(up, files) {
                container.disabledUpload=true
                let fileType=files[0].type.split('/')
                if(fileType[0]=="image"){
                  container.addloadingType=1
                }else{
                  container.addloadingType=2
                }
                container.addloadingList=files
                up.start();
            },
            BeforeUpload: function(up, file) {
                //设置参数
                up.setOption({
                    multipart_params:{token:container.token,'x:uploadId':container.uploadId}
                })
                let fileType=file.type.split('/')
                if(fileType[0]=="image"){
                    if(container.uoloadLinkType=='record'){
                        container.templateData[container.uploadId].file.adduploadImgList.push({types:'1'})
                    }else{
                        container.recordList[container.rectifyUploadIndex].detail.rectify.file.adduploadImgList.push({types:'1'})
                    }
                }else{
                    if(container.uoloadLinkType=='record'){
                        container.templateData[container.uploadId].file.adduploadLinkList.push({types:'1',name: '<?php echo Yii::t("directMessage", "Uploading");?>'})  
                    }else{
                        container.recordList[container.rectifyUploadIndex].detail.rectify.file.adduploadLinkList.push({name: '<?php echo Yii::t("directMessage", "Uploading");?>'})  
                    }
                }
                container.addloadingList.splice(0,1)
            },
            UploadProgress: function(up, file) {
                $('#progress').css('width', file.percent+'%').html(file.percent+'%'); 
            },
            'FileUploaded': function(up, file, info) {
                var response = eval('('+info.response+')');
                if(info.status == 200){
                    let fileType=response.data.mimeType.split('/')
                    if(container.uoloadLinkType=='record'){
                        if(fileType[0]=="image"){
                            container.templateData[response.data.uploadId].file.adduploadImgList.splice(container.templateData[response.data.uploadId].file.adduploadImgList.length-1,1)
                            container.templateData[response.data.uploadId].file.adduploadImgList.push({
                                file:response.data.key,
                                name:response.data.name,
                                url: response.data.url,
                                fileUrl_thumb:response.data.fileUrl_thumb,
                            })
                        }else{
                            container.templateData[response.data.uploadId].file.adduploadLinkList.splice(container.templateData[response.data.uploadId].file.adduploadLinkList.length-1,1)
                            container.templateData[response.data.uploadId].file.adduploadLinkList.push({
                                file:response.data.key,
                                name:response.data.name,
                                url: response.data.url,
                                fileUrl_thumb:response.data.fileUrl_thumb,
                                isEdit:false
                            })
                        }
                    }else{
                        if(fileType[0]=="image"){
                            container.recordList[container.rectifyUploadIndex].detail.rectify.file.adduploadImgList.splice(container.recordList[container.rectifyUploadIndex].detail.rectify.file.adduploadImgList.length-1,1)
                            container.recordList[container.rectifyUploadIndex].detail.rectify.file.adduploadImgList.push({
                                file:response.data.key,
                                name:response.data.name,
                                url: response.data.url,
                                fileUrl_thumb:response.data.fileUrl_thumb,
                            })
                        }else{
                            container.recordList[container.rectifyUploadIndex].detail.rectify.file.adduploadLinkList.splice(container.recordList[container.rectifyUploadIndex].detail.rectify.file.adduploadLinkList.length-1,1)
                            container.recordList[container.rectifyUploadIndex].detail.rectify.file.adduploadLinkList.push({
                                file:response.data.key,
                                name:response.data.name,
                                url: response.data.url,
                                fileUrl_thumb:response.data.fileUrl_thumb,
                                isEdit:false
                            })
                        }
                    }
                }
            },
            'Error': function(up, err, errTip) {
                if(err.response){
                    var response = eval('('+err.response+')');
                }
                else{
                    var response = {message: err.message};
                }
                if(response.error == 'token not specified'){
                    up.stop();
                    $.ajax({
                        url: '<?php echo $this->createUrl("journals/getCommentQiniuToken") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            journalId: container.journal_id
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                container.token=data.data.data
                                config['uptoken'] = data.data.data;
                                up.setOption("multipart_params", {"token":data.data.token,})
                                up.start();
                            } else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.msg
                                });
                            }
                        },
                        error: function(data) {

                        },
                    })
                }
            },
            'UploadComplete': function(up, file) {
                //队列文件处理完毕后,处理相关的事情
            }
        }
    };
    var  container = new Vue({
        el: "#template",
        data: {
            height:height,
            isHQstaff:isHQstaff,
            activeName:'first',
            detailData:{},
            temp_info:{},
            templateData:{},
            open_status:false,
            id:id,
            uploader:[],
            adduploadImgList:[],
            adduploadLinkList:[],
            addloadingList:[],
            addloadingType:0,
            token:'',
            uploadId:'',
            recordList:[],
            uoloadLinkType:'',
            rectifyUploadIndex:null,
            attachmentName:'',
            school_done:'',
            showrecordList:false,
            showeditOpenStatus:false,
            deptCheck:'',
            tipType:'',
            checkList:{},
            checkIndex:'',
            needMe:'',
            btnDis:false,
            initDataLoading:false,
            hasUnsavedChanges:false,
            watchHandler: null,
            initialData:{},
            initEdit:true,
            categories:{},
            categoriesAll:{},
            tabIndexList:{},
            filterType:'all',
            qualityTime:'',
            masks: {
                input: 'YYYY-MM-DD',
            },
            loading:false,
            check_form:'',
            remark:'',
            reasonRemark:'',
            rectify_count:null,
            checkNode:{}
        },
        created: function() {
            this.initData('tab')
        },
        beforeDestroy() {
            this.removeBeforeUnloadListener();
            if (this.watchHandler) {
                this.watchHandler(); // 移除监听器
            }
        },
        methods: {
            confirmDate(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("setQualityItemStartDate") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        id:this.id,
                        start_date:this.qualityTime,
                        check_form:this.check_form,
                        remark:this.remark,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.initData()
                            resultTip({
                                msg: data.message
                            });
                            $("#addDateModal").modal('hide')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })  
            },
            setDate(){
                this.qualityTime=this.detailData.master_data.start_date
                this.remark=this.detailData.master_data.remark
                this.check_form=this.detailData.master_data.check_form
                $("#addDateModal").modal('show')
            },
            html(data){
                if(data!='' && data!=null){
                    return  data.replace(/\n/g, '<br/>')
                }
            },
            addBeforeUnloadListener() {
                window.addEventListener('beforeunload', this.handleBeforeUnload);
            },
            removeBeforeUnloadListener() {
                window.removeEventListener('beforeunload', this.handleBeforeUnload);
            },
            handleBeforeUnload(event) {
                if (this.hasUnsavedChanges) {
                    event.returnValue = '您确定要离开此页面吗？未保存的数据将会丢失。';
                    event.preventDefault();
                }
            },
            initData(tip){
                let that=this
                if(tip && tip=='tip'){
                    $("#tabModal").modal('hide')
                    that.loading=true
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("getQualityItemDetail") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        id:this.id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            $("#tabModal").modal('hide')
                            that.rectify_count =data.data.rectify_count
                            that.needMe=data.data.needMe
                            that.showeditOpenStatus=data.data.master_data.open_status==1?true:false
                            that.detailData=data.data
                            var templateData={}
                            let categories=data.data.temp_info.info.categories
                            for(let i=0;i<categories.length;i++){
                                let item=categories[i]
                                for(let j=0;j<item.sub.length;j++){
                                    let subList=item.sub[j]
                                    for(let k=0;k<subList.attach.length;k++){
                                        if(data.data.tasks_item_sub_data[subList.attach[k].id]){
                                            let saveData=data.data.tasks_item_sub_data[subList.attach[k].id]
                                            subList.attach[k].results=saveData.results?saveData.results:''
                                            templateData[subList.attach[k].id]={
                                                sub_attach_id:subList.attach[k].id,
                                                record:saveData.record?saveData.record:'',
                                                showRecord:saveData.record!=null?true:false,
                                                results: saveData.results?saveData.results:'',
                                                envision_day:saveData.envision_day?saveData.envision_day:'',
                                                file: {
                                                    adduploadImgList:saveData.attach.img?saveData.attach.img:[],
                                                    adduploadLinkList:saveData.attach.link?saveData.attach.link:[]
                                                },
                                                updated_at:saveData.updated_at?saveData.updated_at:'',
                                                quality_user:saveData.quality_user?saveData.quality_user:''
                                            }
                                        }else{
                                            subList.attach[k].results=''
                                            templateData[subList.attach[k].id]={
                                                sub_attach_id:subList.attach[k].id,
                                                record:'',
                                                showRecord:false,
                                                results:'',
                                                envision_day:'',
                                                file: {
                                                    adduploadImgList:[],
                                                    adduploadLinkList:[]
                                                },
                                            }
                                        }
                                    }
                                }
                            }
                            that.categories=data.data.temp_info.info.categories
                            that.categoriesAll=JSON.parse(JSON.stringify(data.data.temp_info.info.categories))
                            that.initialData = JSON.parse(JSON.stringify(templateData));
                            that.templateData=templateData
                            if(that.uploader.length!=0) {
                                for(var i=0;i<that.uploader.length;i++){
                                that.uploader[i].destroy();
                                }
                            }
                            that.temp_info=data.data.temp_info;
                            if(data.data.master_data.status==0){
                                that.initEdit=true
                                that.getUploadToken('record')
                            }else{
                                that.initEdit=false
                            }
                            that.qualityTime=data.data.master_data.start_date || ''
                            that.$nextTick(() => {
                                that.initDataLoading = true;
                                that.startWatching();
                            })
                            that.loading=false
                            if(tip && tip=='tab' && that.showeditOpenStatus){
                                that.getRecordList()
                                that.activeName='second'
                            }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })  
            },
            resultsNum(type){
               let nums=Object.values(this.initialData).filter((i) => i.results === type)
               return nums.length || 0
            },
            switchInfo(type){
                this.filterType=type
                if(type=='all'){
                    this.categories=this.categoriesAll
                }else{
                    const filteredData = [];
                    this.categoriesAll.forEach(category => {
                        const filteredSub = category.sub.filter(subItem => {
                            return subItem.attach.some(attachItem => attachItem.results === type);
                        });
                        if (filteredSub.length > 0) {
                            filteredData.push({
                                ...category,
                                sub: filteredSub.map(subItem => ({
                                    ...subItem,
                                    attach: subItem.attach.filter(attachItem => attachItem.results === type)
                                }))
                            });
                        }
                    });
                    this.categories=filteredData
                }
                
            },
            startWatching() {
                this.watchHandler = this.$watch('templateData', {
                    deep: true,
                    handler: (newVal, oldVal) => {
                        if (JSON.stringify(newVal) !== JSON.stringify(this.initialData)) {
                            this.hasUnsavedChanges = true;
                            this.addBeforeUnloadListener();
                        }
                    }
                });
            },
            getUploadToken(type){
                this.uoloadLinkType=type
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getUploadToken") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        linkType:type
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.token=data.data
                            that.$nextTick(()=>{
                                if(type=='record'){
                                    for(var key in that.templateData){
                                        config['token'] =data.data;
                                        config['browse_button'] ='upload'+that.templateData[key].sub_attach_id;
                                        var uploader = new plupload.Uploader(config);
                                        that.uploader.push(uploader);
                                        uploader.init();
                                    }
                                }else{
                                    for(let i=0;i<that.recordList.length;i++){
                                        config['token'] =data.data;
                                        config['browse_button'] ='rectify'+that.recordList[i].detail.rectify.sub_attach_id;
                                        var uploader = new plupload.Uploader(config);
                                        that.uploader.push(uploader);
                                        uploader.init();
                                    }
                                }
                            })
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })  
            },
            initUploader(id){
               this.uploadId=id
            },
            focusInput(item){
                if(this.detailData.master_data.status==0){
                    item.showRecord=true
                    this.$nextTick(()=>{
                        let id='text'+item.sub_attach_id
                    })
                }
            },
            editQualityInspect(){
                this.switchInfo("all")
                Vue.set(this.detailData.master_data, 'status', 0);
                this.getUploadToken('record')
            },
            cancelEdit(type){
                if(type && this.hasUnsavedChanges){
                    this.tipType='cancel'
                    $("#tipModal").modal('show')
                    return
                }
                if(this.hasUnsavedChanges){
                    this.initData()
                }else{
                    Vue.set(this.detailData.master_data, 'status', 1);
                }
                $("#tipModal").modal('hide')
            },
            saveQualityInspect(){
                var sub_attach=[]
                for(let key in this.templateData){
                    if(this.templateData[key].results==2 && this.templateData[key].envision_day==''){
                        resultTip({
                                error: 'warning',
                                msg: '质检不合格请填写预计完成时间'
                            });
                        return
                    }
                    sub_attach.push({
                        sub_attach_id:this.templateData[key].sub_attach_id,
                        record:this.templateData[key].record,
                        results:this.templateData[key].results,
                        envision_day:this.templateData[key].envision_day,
                        file: {
                            adduploadImgList:this.templateData[key].file.adduploadImgList,
                            adduploadLinkList:this.templateData[key].file.adduploadLinkList,
                        },
                    })
                }
                let that=this
                that.btnDis=true
                $.ajax({
                    url: '<?php echo $this->createUrl("saveQualityInspect") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        "feedback": this.detailData.master_data.feedback,
                        "sparkle": this.detailData.master_data.sparkle,
                        "start_year":this.detailData.master_data.start_year,
                        "tasks_item_id": this.id,
                        "open_status": this.showeditOpenStatus?1:0,
                        "sub_attach":sub_attach
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.btnDis=false
                            resultTip({
                                msg: data.message
                            });
                            that.initData()
                            that.hasUnsavedChanges = false;
                            that.removeBeforeUnloadListener();
                            if (that.watchHandler) {
                                that.watchHandler(); // 移除监听器
                            }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.btnDis=false
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.btnDis=false
                    },
                })  
            },
            addNewQuality(){
                $("#addQualityModal").modal('show')
            },
            handleClick(){
                this.checkList={}
                if(this.activeName=='first'){
                    this.initData()
                }else{
                    this.getRecordList()
                }
            },
            tabClick(){
                if(this.activeName=='first'){
                    this.initData('tip')
                }else{
                    this.initData('tip')
                    this.getRecordList('tip')
                }
            },
            getRecordList(tip){
                let that=this
                if(tip){
                    $("#tabModal").modal('hide')
                    that.loading=true
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("getQualityRectifyList") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        tasks_item_id:this.id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            $("#tabModal").modal('hide')
                            that.needMe=data.data.needMe
                            that.rectify_count=data.data.rectify_count
                            for(let i=0;i<data.data.list.length;i++){
                                if(that.checkList.show && data.data.list[i].detail.rectify.id==that.checkList.detail.rectify.id){
                                    data.data.list[i].show=true
                                }else{
                                    data.data.list[i].show=false
                                }
                                data.data.list[i].detail.rectify.department_qualifiedCopy=JSON.parse(JSON.stringify(data.data.list[i].detail.rectify.department_qualified)) 
                                data.data.list[i].detail.rectify.file= {
                                    adduploadImgList:data.data.list[i].detail.rectify.attach.img || [],
                                    adduploadLinkList:data.data.list[i].detail.rectify.attach.link || []
                                }      
                            }
                            that.showrecordList=true
                            that.recordList=data.data.list
                            that.school_done=data.data.school_done
                            that.checkNode=data.data.nodes
                            if(data.data.school_done==0){
                                that.getUploadToken('correction')
                            }
                            that.loading=false
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })  
            },
            rectifyUpload(index){
                this.rectifyUploadIndex=index
            },
            delFile(img,index){
                img.splice(index,1)
            },
            saveFile(){

            },
            saveSchoolEdit(list,index){
                let that=this
                this.checkList=JSON.parse(JSON.stringify(list))
                that.btnDis=true
                $.ajax({
                    url: '<?php echo $this->createUrl("saveRectificationRecord") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        id: list.detail.rectify.id,
                        school_action: list.detail.rectify.school_action,
                        school_done_date: list.detail.rectify.school_done_date,
                        file:list.detail.rectify.file
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getRecordList()
                            resultTip({
                                msg: data.message
                            });
                            that.btnDis=false
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.btnDis=false
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.btnDis=false
                    },
                })  
            },
            confirmPD(list,status){
                this.checkList=JSON.parse(JSON.stringify(list))
                this.checkList.status=status
                this.checkList.type='confirm'
                this.reasonRemark=''
                $("#unQualityModal").modal('show')
            },
            PDConfirm(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("PDConfirm") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        rectify_id:this.checkList.detail.rectify.id,
                        PD_sure_content:this.reasonRemark,
                        PD_sure:this.checkList.status,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getRecordList()
                            resultTip({
                                msg: data.message
                            });
                            $("#unQualityModal").modal('hide')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                }) 
            },
            showImg(id,list,type){
                var element=type+'img_'+id
                var viewer = new Viewer(document.getElementById(element),{
                    fullscreen: false,
                    title:false,
                    scalable:false,
                    url: 'data-original',
                    show:function(){
                        $('.nextImg').show()
                        if(list.length==1){
                            $('.viewer-prev').hide()
                            $('.viewer-next').hide()
                        }else{
                            $('.viewer-prev').show()
                            $('.viewer-next').show() 
                        }
                    },
                    hide:function(){ 
                        viewer.destroy()
                        $('.nextImg').hide()
                    }
                });
                document.getElementById('next').onclick = function() {
                    viewer.next();
                }
                document.getElementById('prev').onclick = function() {
                    viewer.prev();
                }
                $("#"+id).click();
            },
            editOpenStatus(type){
                let that=this
                if(type){
                    this.tipType='open'
                    $("#tipModal").modal('show')
                    return
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("editOpenStatus") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        id:this.detailData.master_data.id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            $("#tipModal").modal('hide')
                            that.initData()
                            resultTip({
                                msg: data.message
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })  
            },
            deptCheckChange(list,type,status){
                this.checkList=JSON.parse(JSON.stringify(list))
                this.checkList.type=type
                this.checkList.status=status
                this.reasonRemark=''
                $("#unQualityModal").modal('show')
            },
            unQuality(){
                let that=this
                if(this.reasonRemark=='' && this.checkList.status=='2'){
                    resultTip({
                        error: 'warning',
                        msg: '请填写拒绝原因'
                    });
                    return
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("auditQualityRectify") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        rectify_id:this.checkList.detail.rectify.id,
                        type:this.checkList.type,
                        status:this.checkList.status,
                        comment:this.reasonRemark
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getRecordList()
                            resultTip({
                                msg: data.message
                            });
                            $("#unQualityModal").modal('hide')

                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                }) 
            },
            tabTitle(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("switchedItem") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        id:this.detailData.master_data.id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.tabIndexList=data.data
                            $("#tabModal").modal('show')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })  
                
            }
        },
    })
    
</script>
