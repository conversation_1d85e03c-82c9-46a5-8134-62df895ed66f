<div>
    <div class="panel panel-default mb0">
        <div class="panel-heading"><?php echo Yii::t('withdrawal','Admissions Interview(s)');?></div>
        <div class="panel-body">
            <div class='ml8'>
                <div class=' form-horizontal font14'>
                    <div class='flex flexStart mb20'>
                        <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Interviewer');?></div>
                        <div class='flex1 color3'>
                        <span class='talkUser'></span>
                        </div>
                    </div>
                    <div class='flex mb20  flexStart'>
                        <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Interview result');?></div>
                        <div class='flex1'>
                            <div class='mb12'>
                                <div class="radio mb10">
                                    <span class='notAllowed'>
                                        <label class='lableHeight eventsNone'>
                                            <input type="radio" name="talk_result"  class='talkResult' value="2">
                                            <?php echo Yii::t('withdrawal','End withdrawal, continue study');?>
                                        </label>
                                    </span>
                                </div>
                                <div class="radio mb10">
                                    <span class='notAllowed'>
                                        <label class='lableHeight eventsNone'>
                                            <input type="radio" name="talk_result"  class='talkResult' value="3">
                                            <?php echo Yii::t('withdrawal','Continue withdrawal');?>
                                        </label>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class='flex flexStart '>
                        <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','清算日期');?></div>
                        <div class='flex1 '>
                            <span class='settlement_date color3'></span>
                            <span class='color6 ml16 font12'>
                            <span class='el-icon-warning-outline mr5 '></span>以此日期为在学截止日进行费用清算</span></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    let data=childDetails.forms[0].data
    if(data.staff_info){
        $('.talkUser').text(data.staff_info[data.talk_user].name)
        $('.settlement_date').text(data.settlement_date)
        $('input[type="radio"][name="talk_result"][value='+data.talk_result+']').prop('checked', true);
    }

</script>
<style>
    .dot{
        width: 8px;
        height: 8px;
        background: #4D88D2;
        border-radius:50%;
        margin-top: 5px;
    }
    .borderAppli{
        border-radius: 4px;
        border: 1px solid #E5E6EB;
        padding:24px
    }
    .width100{
        width:90%
    }
    .flexStart{
        align-items:start
    }
    .flexWidth{
        width:150px
    }
    .flexStart .radio-inline{
        padding-top:0px
    }
    .mb0{
        margin-bottom:0px
    }
    
</style>