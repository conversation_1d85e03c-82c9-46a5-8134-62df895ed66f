<div class="form">
<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'address-form',
	'enableClientValidation'=>true,
	'clientOptions'=>array(
		'validateOnSubmit'=>true,
	),
)); ?>
	
	<div class="table_full">
		<table width="100%">
			<colgroup>
				<col width="200">
			</colgroup>
			<tbody>
				<tr>
					<th>
						<?php echo CHtml::activeLabelEx($model,'en_address'); ?>
					</th>
					<td>
						<?php echo CHtml::activeTextField($model,'en_address',array("size"=>64,'class'=>'input')) ?>
						<?php echo CHtml::error($model,'en_address'); ?>
					</td>
				</tr>
				<tr>
					<th>
						<?php echo CHtml::activeLabelEx($model,'en_district'); ?>
					</th>
					<td>
						<?php echo CHtml::activeTextField($model,'en_district',array("size"=>40,'class'=>'input')) ?>
						<?php echo CHtml::error($model,'en_district'); ?>					
					</td>
				</tr>
				<tr>
					<th>
						<?php echo CHtml::activeLabelEx($model,'en_city'); ?>
					</th>
					<td>
						<?php echo CHtml::activeTextField($model,'en_city',array("size"=>40,'class'=>'input')) ?>
						<?php echo CHtml::error($model,'en_city'); ?>
					</td>
				</tr>
				<tr>
					<th>
						<?php echo CHtml::activeLabelEx($model,'en_postcode'); ?>
					</th>
					<td>
						<?php echo CHtml::activeTextField($model,'en_postcode',array("size"=>40,'class'=>'input')) ?>
						<?php echo CHtml::error($model,'en_postcode'); ?>
					</td>
				</tr>
				<tr>
					<th>
						<?php echo CHtml::activeLabelEx($model,'en_telephone'); ?>
					</th>
					<td>
						<?php echo CHtml::activeTextField($model,'en_telephone',array("size"=>40,'class'=>'input')) ?>
						<?php echo CHtml::error($model,'en_telephone'); ?>
					</td>
				</tr>
			</tbody>
		</table>
	</div>
		<div class="btn_wrap_pd">
			<?php
				echo CHtml::submitButton(Yii::t("global", "Save"), array("class"=>"btn btn_submit"));
                foreach(Yii::app()->user->getFlashes() as $key => $message) {
                    echo '<div class="tips_' . $key . '">' . $message . "</div>\n";
                }
			?>
		</div>

	
<?php $this->endWidget(); ?>
</div><!-- form -->