<style>
    body {
        color: #666
    }

    .container {
        padding: 15px
    }

    .h4 {
        text-align: center
    }

    .list-unstyled {
        padding-left: 2em;
        margin-bottom: 0
    }

    p,
    li {
        display: flex
    }

    p span,
    li span {
        flex: 1;
        padding-left: 5px
    }
    .stInt{
        border-bottom:1px solid #000;
        display:inline-block
    }
    .stInt input {
        height:28px;
        outline: none;
        border:none;
        color:#898989
    }

    .guardian {
        width: 80px
    }

    .out {
        padding-left: 25px;
        padding-bottom: 16px
    }

    .committed {
        padding-top: 20px;
        padding-bottom:5px
    }

    .report {
        padding-left: 50px
    }

    #signature-div {
        width: 100%;
        height: 100%;
        display: none;
        position: fixed;
        left: 0;
        top: 0;
        z-index: 999;
        background-color: white;
    }

    #signature-div p {
        position: absolute;
        bottom: 0;
        left: 50%;
        margin-left: -91px
    }

    .signature-pad {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: white;
    }

    .signCan {
        position: relative;
        -webkit-box-flex: 1;
        -ms-flex: 1;
        flex: 1;
        border: 1px solid #f4f4f4;
    }

    .signBody {
        position: relative;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        font-size: 10px;
        width: 100%;
        height: 100%;
        max-height: 300px;
        background-color: #fff;
        border-radius: 4px;
        padding: 16px;
    }

    #signature-data img {
        width: 100px
    }

    .signText {
        padding: 16px;
    }

    .noneBto {
        margin-bottom: 0
    }

    .goOut {
        display: none
    }

    .select {
        /* height: 12px */
    }

    .inputs {
        padding-left: 5px
    }

    .media {
        padding: 15px
    }

    .toast-wrap {
        opacity: 0;
        position: fixed;
        bottom: 50%;
        color: #fff;
        width: 100%;
        text-align: center;
        display: none
    }

    .toast-msg {
        background-color: rgba(0, 0, 0, 0.7);
        padding: 10px;
        border-radius: 5px;
    }

    .toastAnimate {
        display: none;
        animation: toastKF 2s;
    }

    @keyframes toastKF {
        0% {
            opacity: 0;
        }

        25% {
            opacity: 1;
            z-index: 9999
        }

        50% {
            opacity: 1;
            z-index: 9999
        }

        75% {
            opacity: 1;
            z-index: 9999
        }

        100% {
            opacity: 0;
            z-index: 0
        }
    }

    .year {
        border-bottom: 1px;
        border-top: 1px;
        border-left: 1px;
        border-right: 10px;
        height: 25px;
        /* outline: none */
    }

    ::-webkit-datetime-edit {
        padding: 1px;
    }

    ::-webkit-datetime-edit-text {
        padding: 0 .3em;
    }

    ::-webkit-inner-spin-button {
        visibility: hidden;
    }

    ::-webkit-calendar-picker-indicator {
        border: 1px solid #ccc;
    }
    .m5{
        margin:10px
    }
    input[type="date"]:before{
        color:#A9A9A9;
        content:attr(placeholder);
    }
    input[type="date"].full:before {
        color:black;
        content:""!important;
    }
    .align-right{
        text-align:right;
        width:150px
    }
    table{
        width:100%;
        color:#898989
    }
    .classBto{
        border-bottom:1px solid #ccc;
        height:40px;
    }
    .classBto:last-child{
        border-bottom:none;

    }
    .classTop{
        height:40px;
    }
    .protocol{
        display:none
    }
    .startBtn{
        /* display:none */
    }
    .childLens{
        margin-bottom:10px;
        padding-bottom:10px;
        border-bottom:1px solid #ccc;
    }
    .childLens:last-child{
        border-bottom:none;
    }
    .childLens div{
        line-height:30px
    }
    .childLens div span{
        display:inline-block;
        width:120px;
        text-align:right
    }
    .bg{
        background:#e1e1e1;
        border-radius:4px
    }
    /* 清除样式 */
    .bgFixed{
        position: fixed;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background: #000;
        opacity: 0.5;
        display:none
    }
    .confirm {
        position: fixed;
        left: 50%;
        top: 50%;
        width: 270px;
        height: 170px;
        background: #fff;
        margin-left: -125px;
        margin-top: -100px;
        border-radius: 5px;
        padding: 20px;
        display:none
    }
    .clear{
    }
    .clearText{
        line-height:40px
    }
    .clearP{
        line-height:40px;
        margin-top:15px
    }
    .pL-20{
        padding-left:20px;

    }
    .pL-20:first-child{
        padding-top:5px
    }
</style>
<div class='container'>
    <div class='childInfo'> 
        <!-- <table >
            <tbody id='childs'>
            </tbody>
        </table> -->
        <div id='childs'></div>
        <button type="button" class="btn btn-primary btn-lg btn-block startBtn" onclick='start(this)'>开始填写</button>
    </div>
    <div class='protocol'>
        <h4 class="h4 mt-3"><?php echo Yii::t('reg', "COVID-19 Legal Guardian Affidavit") ?></h4>
            <table >
                <tbody id='parentInfo'>
                </tbody>
            </table>
        <div class='committed'>
        <!-- <div>
            <span><?php echo Yii::t('reg', "I am the") ?></span>
            <div class='inputs'></div>
        </div> -->
       
            <?php echo Yii::t('reg', "I hereby acknowledge the personal information provided herein is true and accurate to the best of my knowledge.") ?>
        </div>
        <p class='noneBto'>一. <span><?php echo Yii::t('reg', "For student and members living at the same address (“members”), between January 24, 2020 and now:") ?> </span></p>
        <div class='out'>
            <div class="form-check">
                <input class="form-check-input" type="radio" name="type" id="exampleRadios1" value="1">
                <label class="form-check-label" for="exampleRadios1">
                <?php echo Yii::t('reg', "No one has not left the city during this period.") ?>
                </label>
            </div>
            <div class="form-check">
                <input class="form-check-input" type="radio" name="type" id="exampleRadios2" value="2">
                <label class="form-check-label" for="exampleRadios2">
                    有出城史
                </label>
            </div>
            <div class='goOut'>
                <div><?php echo Yii::t('reg', "The student or other members left the city on for <strong class='stInt'><input type='text' class='country' name='destination'></strong> (Country/Province/City) and returned on <span class='goDate'></span> <input type='date' name='return_date' class='year' id='date' placeholder='请选择日期' > (date). (If multiple members have travelled, list the one who returned last)") ?>
                </div>
            </div>
        </div>
        <p>二. <span> <?php echo Yii::t('reg', "Prior to returning to school, student and other people living at the same address have all completed the mandatory 14-day quarantine requirement.  No one has exhibited COVID-19 symptoms.  No one is a confirmed or suspected COVID-19 patient.") ?></span></p>
        <p>三. <span> <?php echo Yii::t('reg', "Upon returning to school, I will adhere to the procedures required by the school.  These include but are not limited to the following:") ?></span></p>
        <ul class="list-unstyled">
            <li>1. <span> <?php echo Yii::t('reg', "Wear masks during pick-up and drop-off of students") ?></span></li>
            <li>2. <span><?php echo Yii::t('reg', "Pick-up and drop-off students at the designated time and place according to school’s instructions.") ?> </span></li>
            <li>3. <span> <?php echo Yii::t('reg', "Comply with school zone designations and not enter the restricted zone designated for students and teaching staff only.") ?></span></li>
            <li>4. <span> <?php echo Yii::t('reg', "Complete a daily temperature check for the family. Immediately report to the school with any abnormal temperature readings or suspicious symptoms, and voluntarily submit to home quarantine if needed.  Once symptoms disappear, the student must obtain approval from the school before returning.") ?></span></li>
            <li>5. <span> <?php echo Yii::t('reg', "Notify the school if:") ?> </span></li>
        </ul>
        <ul class='report'>
            <li>a. <span> <?php echo Yii::t('reg', "The student, members living at the same address, or family members have been in contact with people from a high-risk region (as defined by Chinese government or WHO), confirmed carriers, or suspected carriers") ?></span> </li>
            <li>b. <span> <?php echo Yii::t('reg', "The student, members living at the same address, or family members exhibit COVID-19 symptoms such as fever, dry cough, fatigue, nausea, or shortness of breath") ?></span></li>
            <li>c. <span> <?php echo Yii::t('reg', "I have any reason to suspect that the student may be infected") ?></span></li>
        </ul>
        <p><?php echo Yii::t('reg', "I hereby acknowledge the truthfulness and accuracy of the information provided herein.  I commit myself to adhering to the school’s guidelines above. I am solely responsible for all legal matters pertaining to the consequences of my actions.") ?></p>
        <div>
            <div class='float-right'><?php echo Yii::t('reg', "Affiant Signature:") ?>
                <button class="btn btn-info btn-sm" type="button" id="signature-save">
                    <span class="fa fa-pen"></span> <?php echo Yii::t('reg', "Agree & sign") ?>
                </button>
            </div>
            <div class="clearfix"></div>
            <p id="signature-data" class=' float-right'></p>
            <div class="clearfix"></div>
            <input type="text" id="filedata" name="signature" value="" hidden="hidden">
            <input type="text" name="childid" value="<?php echo $childid; ?>" hidden="hidden">
    
            <p class='float-right date'></p>
        </div>
        <div class="clearfix"></div>
        <p>
            <button type="submit" class="btn btn-primary btn-lg btn-block" id='disabled' onclick='save()'><?php echo Yii::t('reg', "Submit") ?></button>
        </p>
    </div>
</div>
<div class="wrapper" id="signature-div">
    <div class="signBody">
        <div class="signCan">
            <canvas id="signature-pad" class="signature-pad"></canvas>
        </div>
    </div>
    <div class="signText"><?php echo Yii::t('reg', "Rotate your phone horizontal for better view to sign") ?></div>
    <canvas id="watermark" width="100%" height="150px" style="display:none;"></canvas>
    <p>
        <button type="button" class="btn btn-primary exit ml-1"><?php echo Yii::t('bind', "Confirm") ?></button>
        <button type="button" class="btn btn-outline-primary clear ml-1"><?php echo Yii::t('reg', "Rewrite") ?></button>
        <button type="button" class="btn btn-outline-dark dropOut ml-1"><?php echo Yii::t('bind', "Cancel") ?></button>
    </p>
</div>
<div class="toast-wrap">
    <span class="toast-msg"></span>
</div>
<div class='bgFixed'></div>
<div class="confirm">
    <div class="popup__text">
        <h5  class='text-center clear'>提示</h5>
        <div class='text-center clearText'>确定要清除吗？</div>
        <p class='clearP float-right'><span class='text-center'><button type="button" class="btn btn-outline-dark btn-sm" onclick='cancel()'>取消</button></span><span class='text-center'><button type="button" class="btn btn-outline-primary btn-sm" onclick='rewrite()'>确定</button></span></p>
    </div>
</div>
<script>
    var parent_data = <?php echo json_encode($parentData); ?>;
    var childname = <?php echo json_encode($childname); ?>;
    var schoolname = <?php echo json_encode($schoolname); ?>;
    var childid = <?php echo $childid; ?>;
    var childData = <?php echo json_encode($childData); ?>;
    var class_itle = '<?php echo $classTitle; ?>';
    // 初始化画板
    console.log(childData)
    var scrollTops;
    var canvas = document.getElementById('signature-pad');
    $("#date").on("input",function(){
        if($(this).val().length>0){
            $(this).addClass("full");
        }
        else{
            $(this).removeClass("full");
        }
    });
    childInfo()
    init()
    function childInfo(){
        var html = ''
        var lens=[]
        for (var i = 0; i < childData.length; i++) {
            // html = 
            if(childData[i].isSubmit==1){
                lens.push(i)
            }
            if(childData[i].isSubmit==1){
                html+='<div class="childLens Filled">'+
                    '<div><span><?php echo Yii::t('reg', "Student Name:") ?></span><strong>'+childData[i].childname+
                            '（已填写）</strong></div>'+
                    '<div><span><?php echo Yii::t('reg', "Campus:") ?></span><strong>'+childData[i].schoolid+'</strong></div>'+
                    '<div><span><?php echo Yii::t('reg', "Class:") ?></span><strong>'+childData[i].classid+'</strong></div>'+
                    '<div class="text-center mb2"><button type="button" class="btn btn-primary btn-sm ml-1" onclick="clearData('+childData[i].childid+')">清除</button><button type="button" class="btn btn-info btn-sm ml-1" onclick="childView('+childData[i].childid+',this)">查看</button></div>'+
                '</div>'
                }else{
                    html+='<div  class="childLens">'+
                    '<div><span><?php echo Yii::t('reg', "Student Name:") ?></span><strong><div class="form-check form-check-inline"><input class="form-check-input" type="checkbox" name="childs" id="child'+i+'" value='+childData[i].childid+' onchange="checkChild('+childData[i].childid+',this)"><label class="form-check-label label" for="child'+i+'" > '+childData[i].childname+'</label></div></strong></strong></strong></div>'+
                    '<div><span><?php echo Yii::t('reg', "Campus:") ?></span><strong>'+childData[i].schoolid+'</strong></div>'+
                    '<div><span><?php echo Yii::t('reg', "Class:") ?></span><strong>'+childData[i].classid+'</strong></div>'+

                '</div>'
                }
        }
        console.log(lens)
        if(lens.length==childData.length){
            $('.startBtn').hide()
        }
        $('#childs').html(html)
    }
    // 页面初始化
    function init(){
        $('.protocol input').attr("readonly",false)
        $('input[name="parent_relationship_name"]').attr("disabled",false)
        $('input[name="type"]').attr("disabled", false)
        $('.goDate').html('')
        $('.year').show()
        var dats='<tr  class="classTop">'+
                    '<td class="align-right align-bottom"><?php echo Yii::t('reg', "Name of Parent/Guardian:") ?></td>'+
                    '<td class="align-left align-bottom"><strong class="stInt"><input type="text" class="parents" name="parent_name"></strong></td>'+
                '</tr>'+
                ' <tr>'+
                    ' <td class="align-right align-bottom"><?php echo Yii::t('reg', "Mobile Number:") ?></td>'+
                    ' <td class="align-left align-bottom"><strong class="stInt"><input type="text" class="telphone" name="parent_tel"></strong></td>'+
                ' </tr><tr><td  class="align-top align-right mt-5"><?php echo Yii::t('reg', "I am the:") ?></td><td class="parentsInp"></td></tr>'
        $('#parentInfo').html(dats)
        var str = ''
        $('.inputs').html('')
        for (var i = 0; i < parent_data.length; i++) {
            str = '<div class="pL-20 form-check"><input class="form-check-input select" type="radio" name="parent_relationship_name" id="inlineRadio' + i + '" value=' + parent_data[i].parent_relationship + '>' +
                '<label class="form-check-label" for="inlineRadio' + i + '">' + parent_data[i].parent_relationship_name + '</label></div>'
            
            $('.parentsInp').append(str)

            if (parent_data[i].selected == 1) {
                $('.parents').val(parent_data[i].parent_name)
                $('.telphone').val(parent_data[i].parent_tel)
                $('input[type=radio][name=parent_relationship_name][value=' + parent_data[i].parent_relationship + ']').attr("checked", 'checked')
            }
        }
        $('#signature-data').html('')
        var date = new Date();
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        var strDate = date.getDate();
        if (month >= 1 && month <= 9) {
            month = "0" + month;
        }
        if (strDate >= 0 && strDate <= 9) {
            strDate = "0" + strDate;
        }
        $('input[name="type"]').each(function(){
            this.checked = false;
        });
        $('.goOut').hide()
        $('.country').val('')
        $('.year').val('')
        var currentdate = year + '年' + month + '月' + strDate + '日';
        $('.date').html(currentdate)
        $('input[name="type"]').change(function() {
            if ($('input[name="type"][value="1"]').prop("checked")) {
                $('.goOut').hide()
            } else {
                $('.goOut').show()
            }
        })
    }
    // 错误提示
    function toast(msg) {
        setTimeout(function() {
            document.getElementsByClassName('toast-wrap')[0].getElementsByClassName('toast-msg')[0].innerHTML = msg;
            var toastTag = document.getElementsByClassName('toast-wrap')[0];
            toastTag.className = toastTag.className.replace('toastAnimate', '');
            $('.toast-wrap').show()
            setTimeout(function() {
                toastTag.className = toastTag.className + ' toastAnimate';
            }, 100);
        }, 500);
        setTimeout(function() {
            $('.toast-wrap').hide()
        }, 2500);
    }
    function checkChild(id,obj){
        $(".Filled").removeClass("bg");
        if($(obj).is(":checked")){
            $(obj).parent().parent().parent().parent().addClass('bg')
        }else{
            $(obj).parent().parent().parent().parent().removeClass('bg')
        }
        var chk_value =[];
        $('input[name="childs"]:checked').each(function(){   
            chk_value.push($(this).val());
        });
        console.log(chk_value)
        // if(chk_value.length>0){
            $('.startBtn').show()
        // }
        // else{
        //     $('.startBtn').hide()
        // }
        $('.protocol').hide()
    }
    //开始填写
    function start(obj){
        var chk_value =[];  
        $('input[name="childs"]:checked').each(function(){   
            chk_value.push($(this).val());
        });
        console.log(chk_value)
        if(chk_value.length==0){
            toast('请选择学生')
            return
        }
        $(obj).hide()
        $('.protocol').show()
        $('#disabled').show()
        $('#signature-save').show()
        init()
    }
    //重写
    var rewriteChildId=''
    function clearData(id){
        $('.bgFixed').show()
        $('.confirm').show()
        rewriteChildId=id
    }
    function cancel(){
        $('.bgFixed').hide()
        $('.confirm').hide()
    }
    function rewrite(){
        $.ajax({
            url: "<?php echo $this->createUrl('rewrite') ?>",
            type: 'post',
            dataType: 'json',
            data: {
                childid:rewriteChildId
            },
            success: function(data) {
                if (data.state == 'success') {
                    toast(data.message)
                    setTimeout(function() {
                        location.reload()
                    }, 2500);
                }
                if (data.state == 'fail') {
                    toast(data.message)
                    $('#disabled').attr("disabled", false);
                }
            },
            error: function(data) {
                toast('请求错误')
                $('#disabled').attr("disabled", false);
            }
        })
    }
    //查看
    function childView(id,obj){
        $(".childLens").removeClass("bg");
        $(obj).parent().parent().addClass('bg')

        // $('.startBtn').hide()
        $('input[name="childs"]').each(function(){
            this.checked = false;
        });        
        $.ajax({
            url: "<?php echo $this->createUrl('looksee') ?>",
            type: 'post',
            dataType: 'json',
            data: {
                childid:id
            },
            success: function(data) {
                if (data.state == 'success') {
                    var res=data.data
                    console.log(data)
                    $('.protocol input').attr("readonly","readonly")
                    $('input[name="parent_relationship_name"]').attr("disabled", "disabled")
                    $('input[name="type"]').attr("disabled", "disabled")
                    $('.telphone').val(res.parent_tel)
                    $('.parents').val(res.parent_name)
                    $('input[name="type"]').each(function(){
                        if($(this).val()==res.type){
                            this.checked = true;
                        }else{
                            this.checked = false;
                        }
                    });
                    $('input[name="parent_relationship_name"]').each(function(){
                        if($(this).val()==res.parent_relationship){
                            this.checked = true;
                        }else{
                            this.checked = false;
                        }
                    });
                    if(res.type==2){
                        $('.goOut').show()
                        $('.country').val(res.destination)
                        $('.goDate').html(res.return_date)
                        $('.year').hide()
                    }else{
                        $('.goOut').hide()
                        $('.country').val('')
                        $('.year').val('')
                        $('.goDate').html('')
                    }
                    $('#signature-data').html('<img src=' + res.signature + ' class="signatureImg" >')
                    $('.date').html(res.updated_at)
                    $('.startBtn').hide()
                    $('.protocol').show()
                    $('#disabled').hide()
                    $('#signature-save').hide()
                }
                if (data.state == 'fail') {
                    toast(data.message)
                    $('#disabled').attr("disabled", false);
                }
            },
            error: function(data) {
                toast('请求错误')
                $('#disabled').attr("disabled", false);
            }
        })
    }
    // 保存信息
    function save() {
        $('#disabled').attr("disabled", "disabled");
        var chk_value =[];  
        $('input[name="childs"]:checked').each(function(){   
            chk_value.push($(this).val());
        });
        console.log(chk_value)
        if(chk_value.length==0){
            toast('请选择学生')
            $('#disabled').attr("disabled", false);
            return
        }
        var type = $('input[name="type"]:checked').val();
        if($('.telphone').val().trim()==''){
            toast('请输入联系电话')
            $('#disabled').attr("disabled", false);
            return
        }
        if (type == undefined) {
            toast('请选择出行情况')
            $('#disabled').attr("disabled", false);
            return
        }
        if (!$('.signatureImg')[0]) {
            toast('请同意并签字')
            $('#disabled').attr("disabled", false);
            return
        }
        
        var data = {}
        if (type == 1) {
            data = {
                childids: chk_value,
                parent_name: $('.parents').val(),
                parent_tel: $('.telphone').val(),
                parent_relationship: $('input[name="parent_relationship_name"]:checked ').val(),
                type: type,
                signature: $('.signatureImg')[0].src
            }
        } else {
            if ($('.country').val() == '') {
                toast('请填写去往的城市或者国家')
                $('#disabled').attr("disabled", false);
                return
            }
            if ($('.year').val() == '') {
                toast('请选择返回日期')
                $('#disabled').attr("disabled", false);
                return
            }
            var date = $('.year').val()
            data = {
                childids: chk_value,
                parent_name: $('.parents').val(),
                parent_tel: $('.telphone').val(),
                parent_relationship: $('input[name="parent_relationship_name"]:checked ').val(),
                type: type,
                signature: $('.signatureImg')[0].src,
                destination: $('.country').val(),
                return_date: date
            }
        }
        $.ajax({
            url: "<?php echo $this->createUrl('save') ?>",
            type: 'post',
            dataType: 'json',
            data: data,
            success: function(data) {
                if (data.state == 'success') {
                    toast(data.message)
                    var str=' <div class="alert alert-primary" role="alert">'+
                        '<i class="fas fa-check"></i> 填写完成</div><button type="button" class="btn btn-primary btn-lg btn-block "  onclick="location.reload()">返回</button>'
                        setTimeout(function() {
                            $('.container').html(str)
                    }, 2500);
                    
                }
                if (data.state == 'fail') {
                    toast(data.message)
                    $('#disabled').attr("disabled", false);
                }
            },
            error: function(data) {
                toast('请求错误')
                $('#disabled').attr("disabled", false);
            }
        })
    }
    
    // // 切换孩子
    // function setChildid(childid) {
    //     deleteCookie('childid');
    //     setCookie('childid', childid, 30);
    //     location.reload()
    // }

    // // 设置cookie
    // function setCookie(name, value, days) {
    //     if (days) {
    //         var date = new Date();
    //         date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
    //         var expires = "; expires=" + date.toGMTString();
    //     } else {
    //         var expires = "";
    //     }
    //     document.cookie = name + "=" + value + expires + "; path=/";
    // }

    // // 删除cookie
    // function deleteCookie(name) {
    //     setCookie(name, "", -1);
    // }

    $('.exit').click(function() {
        $('html').css({
            'overflow': 'initial',
            'position': 'initial'
        });
        $('html,body').scrollTop(scrollTops);
        var canvas = document.getElementById('signature-pad');
        var data = canvas.toDataURL("image/png");
        // $("#signature-data").attr('src', data);
        $("#signature-data").html('<img src=' + data + ' class="signatureImg" >')
        $("#filedata").val(data);
        $("#signature-data").show();
        $('#signature-div').hide();
        var width = canvas.width / 8
        var height = canvas.height / 8
        $('#signature-data img').height(height)
        $('#signature-data img').width(width)
    })
    // 重写
    $('.clear').click(function() {
        signatureInit();
    })
    $('.dropOut').click(function() {
        $('html').css({
            'overflow': 'initial',
            'position': 'initial'
        });
        $('html,body').scrollTop(scrollTops);
        $('#signature-div').hide();
        clea = canvas.getContext("2d");
        clea.clearRect(0, 0, 500, 500);
    })
    // 点击全名书写
    $('#signature-save').click(function() {
        //canvas全屏
        $('#signature-div').show();
        signatureInit();
    });

    function signatureInit() {
        scrollTops = $(window).scrollTop();
        $('html').css({
            'overflow': 'hidden',
            'position': 'fixed'
        })
        ratio = Math.max(window.devicePixelRatio || 1, 1);
        canvas.width = canvas.offsetWidth * ratio;
        canvas.height = canvas.offsetHeight * ratio;
        canvas.getContext("2d").scale(ratio, ratio);
        var signaturePad = new SignaturePad(canvas);
        watermark(canvas);
    }

    function hengshuping() {
        if (window.orientation == 180 || window.orientation == 0) {
            if ($('.wrapper').is(':visible')) {
                clea = canvas.getContext("2d");
                clea.clearRect(0, 0, 500, 500);
                setTimeout("signatureInit()", 200);
            }
        }
        if (window.orientation == 90 || window.orientation == -90) {
            if ($('.wrapper').is(':visible')) {
                clea = canvas.getContext("2d");
                clea.clearRect(0, 0, 500, 500);
                setTimeout("signatureInit()", 200);
            }
        }
    }
    window.addEventListener("onorientationchange" in window ? "orientationchange" : "resize", hengshuping, false);
    // 绘制水印
    function watermark(canvas) {
        var date = new Date();
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        var strDate = date.getDate();
        if (month >= 1 && month <= 9) {
            month = "0" + month;
        }
        if (strDate >= 0 && strDate <= 9) {
            strDate = "0" + strDate;
        }
        var currentdate = year + month + strDate
        var cw = $('#watermark')[0];
        cw.width = $(document).width();
        var ctx = cw.getContext("2d"); //返回一个用于在画布上绘图的环境


        ctx.fillStyle = "rgba(100,100,100,0.1)";
        if (window.orientation == 90 || window.orientation == -90) {
            ctx.font = "30px 黑体";
            ctx.rotate(-10 * Math.PI / 180);
            $('.signText').hide()
            ctx.fillText("<?php echo Yii::t('reg', "家长防疫行为承诺书专用") ?>" + currentdate + "", 100, 130);
        } else if (window.orientation == 0 || window.orientation == 180) {
            ctx.font = "20px 黑体";
            ctx.rotate(-18 * Math.PI / 180);
            $('.signText').show()
            ctx.fillText("<?php echo Yii::t('reg', "家长防疫行为承诺书专用") ?>" + currentdate + "", 0, 130);
        }

        ctx.rotate('20*Math.PI/180'); //坐标系还原
        var crw = canvas;
        ctxr = canvas.getContext("2d");
        ctxr.width = $(window).height();
        ctxr.clearRect(0, 0, 500, 500); //清除整个画布 
        var pat = ctxr.createPattern(cw, "repeat"); //在指定的方向上重复指定的元素  
        ctxr.fillStyle = pat;
        ctxr.fillRect(0, 0, crw.width, crw.height);
    }
</script>