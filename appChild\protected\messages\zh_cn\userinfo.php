<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yiic message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE, this file must be saved in UTF-8 encoding.
 *
 * @version $Id: $
 */
return array (
  'I have signed the agreement to choose Alternative Lunch Menu for my child.' => '我已经签署了特殊餐协议，同意孩子食用特殊餐。',
  'Please :contact office staff to change.' => '请 :contact 办公室管理团队修改此选项',

  'An account has not been created yet.  To create an account, please enter the following information' => '帐号未创建，请填下下面的表格创建帐号',
  'Change Email?' => '修改Email?',
  'Change Password' => '修改密码',
  'Child Profile' => '孩子资料',
  'Edit' => '编辑',
  'Family Directory' => '班级通讯录',
  'Father\'s Profile' => '父亲资料',
  'Female' => '女',
  'Home Address' => '家庭地址',
  'I give permission to the school, to use pictures taken of my child in school, for marketing purposes.' => '我同意学校将我的孩子的照片用于市场推广。',
  'Leave blank if no change.' => '不做更改请留空',
  'Login Email' => '登录Email',
  'Male' => '男',
  'Mother\'s Profile' => '母亲资料',
  'Name' => '姓名',
  'Parent Profile' => '家长资料',
  'Password' => '密码',
  'Please edit the following information correctly. An automatic email will be sent to Campus Support Team if you make changes to “Special Needs”, so they will be well aware of this and inform teachers as well.' => '请正确编辑以下信息。如果您对“特殊需求”做出更改，系统将自动发送邮件到校园支持团队，他们将会得知这个变化并通知班级老师。',
  'Please input a valid birthday.' => '请输入正确的生日',
  'Please input either Chinese Name or First Name' => '请输入中文姓名或者英文名',
  'Please put a mark in the box for item(s) to be published in internally distributed :fd' => '请选择您愿意在 :fd 中公开的项目：',
  'Please review the miscellenous information below and correct if necessary. Click Save when completed.' => '请正确填写紧急联系人、孩子过敏等情况',
  'Should a situation arise when emergency medical attention is required and we are unable to contact any of the above, please indicate,on the space provided below, the hospital we will take your child to. If not filled in, the school will select a hospital on its own. The school will not be responsible for any costs or fees incurred.' => '如果遇紧急情况，学生需要医疗服务，而学校无法联系到上述联系人，请在下列横线注明家长希望把学生送至的医院或诊所名。若家长不指定任何医院，校园会代为选择医院。学校不会承担所有有关费用。',
  'Switch' => '切换',
  'User does not match parents IDs' => '用户信息不匹配，请重试或联络校园。',
  'Verify Password' => '确认密码',
  'View Alternative Lunch Agreement' => '查看特殊餐协议',
  'Who do you authorize to pick up your child?' => '谁被授权可以接送您的孩子',
  'Who should we contact for emergency situations?' => '紧急情况的联系人',
  'You can edit the profiles and your home address here. Please keep this updated with your most recent information.' => '您可以在此编辑孩子和家长资料以及家庭地址。请保持这些信息为最新状态。',
  'You have %s new replies.' => '%s 条新回复',
  'You have %s unpaid invoices.' => '%s 张未付账单',
  'Your sharing function has been enabled.' => '您已经开放了分享功能。',
  'Your sharing function is disabled. Please input access code and click “Enable”.' => '您的分享功能暂未开放。请输入访客口令并点击“开放”。',
  'contact' => '联系',
);
