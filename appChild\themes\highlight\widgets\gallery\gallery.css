.cf:before,.cf:after{content: ".";display: block;height: 0;visibility: hidden;}
.cf:after{clear: both;}
.cf{zoom:1;}
.none{display:none}
#mainimg{border:6px solid #fff;}
ul, li {list-style:none;}
section, header, footer, aside, article, nav {display: block;}
header,section .description,section .num,section .tool li,#picbox,#photolist,.photolist .btn_l,.photolist .btn_r,.items,.search,footer,#photolist .btn_l,#photolist .btn_r{background-color:#f0f0f0;}
.grey{opacity:.6;filter:alpha(opacity=60);}
nav{width:650px;float:left;padding-left:15px}
.topnav {
    font-size: 12px;
    margin: 5px auto 0;
    width: 980px;
}
.topnav a {
    padding: 0 6px;
}
.tools,article{margin:0 auto;}
.tools div{float:right;height:51px;}
/*.tools{ position:relative}*/
.tools .description{height: 51px;overflow: hidden;padding: 0 15px;width: 248px;float: left;font-weight:normal;font-size: 20px;line-height: 51px;}
.tools .description p .more{ cursor:pointer}
.tools .description p .more:hover{text-decoration: underline;}
.tools .description p .more s{ display:block;background: url(slide_p.gif) no-repeat scroll -120px 0  transparent;width:10px;display: inline-block;height: 11px;*height:15px }
.tools .description p .more.up s{ background-position: -120px -23px;}
.tools .description p .more.up{right: 0;position: absolute;margin: 5px 20px;bottom: 0;}
.tools .num{width:105px;margin-left:3px; float:left;font-size: 28px;line-height: 51px;text-align: center;}
.num .curr{font-size: 26px; color:#ff9600;}
.num .all{font-size: 26px;}
/*.tools .tool {width:162px;_width:165px;}*/
.tools .tool li{display:block;width:51px;height:51px;float:left;margin-left:3px;margin-bottom:3px;background: url(slide_p.gif) no-repeat scroll  -106px -231px transparent; cursor:pointer;}
.tools .tool .prev{ background-position: -106px -231px; }
.tools .tool .prev.on,.black .tools .tool .prev.on{background-position: -272px -231px;}
.tools .tool .play{ background-position:-312px 1px; }
.tools .tool .play.on,.black .tools .tool .play.on{ background-position:-325px -231px; }
.tools .tool .play.stop{ background-position:-159px -231px; }
.tools .tool .play.stop.on,.black .tools .tool .play.stop.on{ background-position:-377px 0; }
.tools .tool .next{ background-position:-212px -231px; }
.tools .tool .next.on,.black .tools .tool .next.on{ background-position:-378px -231px; }
.tools .tool .share{ background-position: -106px -284px; }
.tools .tool .share.on,.black .tools .tool .share.on{ background-position: -272px -284px; }
.tools .tool .list{ background-position:-159px -284px; }
.tools .tool .list.on,.black .tools .tool .list.on{ background-position:-325px -284px; }
.tools .tool .list.s{ background-position:-176px 1px; }
.tools .tool .list.s.on,.black .tools .tool .list.s.on{ background-position:-240px 1px; }
.tools .tool .download{ background-position:-212px -284px; }
.tools .tool .download.on,.black .tools .tool .download.on{ background-position:-378px -284px; }


article{overflow:hidden; position:relative; }
article .summary{color:#fff;overflow:hidden;z-index:50;background-color:#000;opacity: 0.7;}
.summary .icon{width:15px; background-color:#272727;padding:10px 5px; text-align:center;font-size: 13px; line-height: 17px;display: block;float: left;z-index:2;cursor: pointer;position:relative;}
.summary .icon:hover{opacity:.8;filter:alpha(opacity=80);}
.summary .icon s{background: url(slide_p.gif) no-repeat scroll -143px -25px  transparent;display:block;width:15px;height:15px;margin-top:5px;color:#272727}
.summary .icon.off s{background: url(slide_p.gif) no-repeat scroll -143px 0  transparent;}
.summary .description{width: 685px;float: left;z-index:2;position:relative;overflow: hidden;}
.summary .description p{margin: 0;padding: 10px;}
.summary .bg{width:100%;height:100%;z-index:1;filter:alpha(opacity=70); }
.summary p a {color:#fff}
#picbox{position:relative}
#picbox .p_left,#picbox .p_right{
    display:block; background: url(spacer.gif) repeat scroll 0 0 transparent; position:absolute;top:0;width:50%;height:100%; left:0;
    cursor: url(left.cur), auto;z-index:40;
}
#picbox .p_right{ right: 0;left:auto;cursor: url(right.cur), auto;}
#picbox table{width:100%;text-align:center;text-align: center;margin-bottom: 10px;}
#picbox table tr td{text-align: center;padding: 10px;}
#imgLoading{display:none;text-align: center;position: absolute;left: 40%;  top: 40%;width: 150px; height: 100px;padding: 10px;background: #000; opacity: 0.7;filter:alpha(opacity=70);color: #fff; border-radius: 10px;}
#photolist {width:100000px; background-color:#fff;position: relative;}
#photolist ul{padding-left:55px; position:relative;left:0}
#photolist .btn_l,#photolist .btn_r{width:55px;height:75px;position: absolute;top:0;background: url(slide_p.gif) no-repeat scroll    -182px -144px   #f0f0f0; cursor:pointer}
#photolist .btn_r{left: 655px; background-position: -186px -56px ;}
#photolist .btn_l.on,.black #photolist .btn_l.on{background-position: -280px -144px;}
#photolist .btn_r.on,.black #photolist .btn_r.on{background-position: -280px -56px;}
#photolist .btn_r.end,#photolist .btn_l.end{cursor:default;}
#photolist li {float:left;margin-left: 3px; position:relative;width: 100px;height: 75px;overflow: hidden;}
#photolist li img{display: block;margin: 0 auto;}
#photolist li.on s{width:90px;height:65px; border: 5px solid #ff9600;position: absolute;top:0;left: 0;cursor:pointer}
#photolist li:hover{opacity:.8;filter:alpha(opacity=80);}

#photolist.list{width:auto;background-color: #f0f0f0; position:relative;zoom: 1;}
#photolist.list .mask{height: 550px;overflow: hidden;background-color: #f0f0f0;position:relative;_margin-left: -35px; }
#photolist.list ul{padding-left:0;}
#photolist.list li{margin-left: 35px;margin-top: 35px;}
#photolist.list li img{ border:1px solid #a9a9a9}
#photolist.list li.on s{ border:5px solid #ff9600}
#photolist.list .btn_l, #photolist.list .btn_r{display:none}

#photolist.list .listnav {background-color: #f0f0f0;padding-bottom: 20px;}
#photolist .listnav {padding-bottom: 0;}
#photolist.list .listnav ul{display: table;margin: 0 auto;_width:200px}
#photolist.list  .listnav ul li{width:15px;height:15px;background: url(slide_p.gif) no-repeat scroll -3px -58px  #f0f0f0; cursor:pointer;margin-left:5px}
#photolist.list  .listnav ul li.on{ background-position:-24px -58px;}
#photolist.list  .listnav ul li.next,.black #photolist.list  .listnav ul li.next{background-color: #272727; background-position: -136px 2px;width: 30px;margin-left: 10px;}
#photolist.list  .listnav ul li.prev,.black #photolist.list  .listnav ul li.prev{background-position:-136px -24px;background-color: #272727;width: 30px;}


#more{background-color:#f0f0f0;color: #666;padding: 50px;display:none}
#more h5{font-size: 60px;font-weight: normal;line-height: 80px;}
#more p{font-size: 30px;line-height: 80px;}
#more p .timecount{ color:#ff9600}
#more .tj a,#more .tj  a:link,#more .tj  a:visited{color: #fff;}
#more .tj div{ margin-bottom: 20px;}
#more .tj span{background-color: #000; cursor:pointer; color: #fff;display: block;float: right;font-size: 26px;width: 300px;padding-left: 90px; overflow:hidden;height:60px;position: relative;line-height: 60px;}
#more .tj span:hover{opacity:.8;filter:alpha(opacity=80);}
#more .tj span s{background: url(slide_p.gif) no-repeat scroll -60px -42px  #000;height: 60px;left: 30px;position: absolute; width: 40px;top: 0;}
#more .tj .replay span s{ background-position:-60px -42px;}
#more .tj .nextplay span s{background-position:-390px -100px;}
#more .tj .close span s{background-position:-125px -42px;}

.toHome {background: url(slide_p.gif) no-repeat scroll 0 -178px transparent; display: block; height: 50px;left: 88%;overflow: hidden;position: absolute; text-indent: -999px;top: 15%;width: 100px;}
header,.tools,#photolist,.items,.search,footer,article{margin-top:5px}

.black{ background-color:#000;color:#ccc}
.black a,.black a:link,.black a:visited{color:#ccc}
.black header .logo{ border-right:1px solid #515151;}
.black header,.black section .description,.black section .num,.black section .tool li,.black #picbox,.black #photolist,.black .photolist .btn_l,.black .photolist .btn_r,.black .items,.black .search,.black footer,.black #photolist .btn_l,.black #photolist .btn_r,.black #photolist.list .mask,.black #photolist.list .listnav{background-color:#333}
.black header .logo a{ background-position:-750px -115px}
.black .tools .num{ background-position:-450px -231px}
.black .tools .tool .prev{ background-position:-560px -231px}
.black .tools .tool .play{ background-position:-695px 0}
.black .tools .tool .play.stop{ background-position:-614px -232px}
.black .tools .tool .next{ background-position:-666px  -231px}
.black .tools .tool .share{ background-position:-560px -284px}
.black .tools .tool .list{ background-position:-613px -284px}
.black .tools .tool .list.s{background-position:-630px 1px}
.black .tools .tool .download{ background-position:-666px -284px}
.black #photolist .btn_l{ background-position:-455px -150px}
.black #photolist .btn_r{ background-position:-545px -150px}
.black  .toHome{ background-position:-500px -2px}
.black  .tools .description p .more s{ background-position:-457px 0;height:12px}
.black  .tools .description p .more.up s{ background-position:-457px -25px}
.black #photolist.list .listnav ul li.on{background-position:-627px -77px}
.black #photolist.list .listnav ul li{background-position:-606px -77px}
s{color: transparent;}
#SourceName{color: #666666;font-size: 12px; right: 20px;position: absolute; top: 20px;}
#SourceName img  {vertical-align: top;}
#SourceName a:link,#SourceName a:visited,#SourceName a:active,#SourceName a:hover{color: #666;}
.play .stoptip{display: none !important;}
.stop{position: relative;}
.stop .stoptip{display: block !important;position: absolute;color: #666666;bottom: 51px;width: 49px;text-align: center;}