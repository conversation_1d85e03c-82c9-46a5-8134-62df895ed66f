<?php $form=$this->beginWidget('CActiveForm', array(
    'id'=>'bulletin-edit-form',
    'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
)); ?>
    <div class="pop_cont" style="height: 490px;overflow-y: auto;">
        <?php if(Dailymail::getCate($this->branchId)): ?>
            <div class="form-group">
                <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'cate'); ?></label>
                <div class="col-xs-9">
                    <?php
                    echo $form->radioButtonList($model, 'cate', Dailymail::getCate($this->branchId), array('separator'=>'', 'template'=>'<div class="col-xs-3">{input} {label}</div>'));
                    ?>
                </div>
            </div>
        <?php endif; ?>
        <div class="form-group">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'priority'); ?></label>
            <div class="col-xs-9">
                <?php
                echo $form->radioButtonList($model, 'priority', Dailymail::getPrioritys(), array('separator'=>'', 'template'=>'<div class="col-xs-3">{input} {label}</div>'));
                ?>
            </div>
        </div>
        <div class="form-group">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'startdate'); ?></label>
            <div class="col-xs-9" style="z-index: 9999;">
                <?php
                $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                    "model"=>$model,
                    "attribute"=>"startdate",
                    "options"=>array(
                        'changeMonth'=>true,
                        'changeYear'=>true,
                        'dateFormat'=>'yy-mm-dd',
                    ),
                    'htmlOptions'=>array(
                        'class'=>'form-control length_3'
                    ),
                ));
                ?>
            </div>
        </div>
        <div class="form-group">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'dueDay'); ?></label>
            <div class="col-xs-9">
                <?php
                echo $form->dropDownList($model, 'dueDay', Dailymail::getDueDay(), array('class'=>'form-control length_3', 'onchange'=>'cDue(this)'));
                ?>
            </div>
        </div>
        <div class="form-group" id="duedatewarp" style="<?php if($model->dueDay!=99):?>display: none;<?php endif;?>">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'duedate'); ?></label>
            <div class="col-xs-9" style="z-index: 9999;">
                <?php
                $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                    "model"=>$model,
                    "attribute"=>"duedate",
                    "options"=>array(
                        'changeMonth'=>true,
                        'changeYear'=>true,
                        'dateFormat'=>'yy-mm-dd',
                    ),
                    'htmlOptions'=>array(
                        'class'=>'form-control length_3'
                    ),
                ));
                ?>
            </div>
        </div>
        <div class="form-group">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'stat'); ?></label>
            <div class="col-xs-9">
                <?php echo $form->checkBox($model, 'stat', array('value'=>10));?>
            </div>
        </div>
        <div class="form-group" style="height: 233px;">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'en_content'); ?></label>
            <div class="col-xs-9">
                <?php echo $form->textArea($model, 'en_content', array('maxlength' => 255, 'class' => 'form-control tinymce')); ?>
            </div>
        </div>
    </div>
    <div class="pop_bottom">
        <button id="J_dialog_close" type="button" class="btn btn-default pull-right"><?php echo Yii::t('global','Cancel');?></button>
        <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
    </div>
<?php $this->endWidget(); ?>

<script>
    function cDue(_this)
    {
        if($(_this).val() == 99){
            $('#duedatewarp').show();
        }
        else{
            $('#duedatewarp').hide();
        }
    }
</script>