<?php

/**
 * This is the model class for table "ivy_asa_course".
 *
 * The followings are the available columns in table 'ivy_asa_course':
 * @property integer $id
 * @property integer $gid
 * @property string $title_cn
 * @property string $title_en
 * @property string $vendor
 * @property integer $unit_price
 * @property integer $default_count
 * @property integer $capacity
 * @property integer $status
 * @property integer $weight
 * @property integer $updated
 * @property integer $updated_userid
 * @property integer $schedule_id
 * @property integer $refund_class_index
 * @property integer $course_provider
 * @property integer $assistant_fee
 */
class AsaCourse extends CActiveRecord
{
	const STATUS_ACTIVE = 1;
	const STATUS_FAIL = 0;
	const STATUS_TWO = 2;
	const STATUS_INVALID = 99;

    public $refundClassIndex;
    public $refundClassIndexTime;
    public $refundClassStatus = 0;
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_asa_course';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('title_cn, title_en, unit_price, default_count, schedule_id, refund_class_index, remaining_class_count, capacity, capacity_min, age_min, age_max, status, updated, updated_userid, schoolid, age_check', 'required'),
			array('gid, default_count, schedule_id, refund_class_index, course_provider, assistant_fee, capacity, status, weight, updated, updated_userid, ufida_update, age_check', 'numerical', 'integerOnly'=>true),
			//array('unit_price', 'numerical', 'min'=>0.01),
			array('title_cn, title_en, vendor', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, gid, title_cn, title_en, vendor, assistant_fee, unit_price, default_count, schedule_id, course_provider, refund_class_index, capacity, status, weight, updated, updated_userid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'asainvoiceitem' => array(self::HAS_MANY, 'AsaInvoiceItem', array('course_id'=>'id')),
			'coursestaff' => array(self::HAS_MANY, 'AsaCourseStaff', array('course_id'=>'id')),
			'courseschedule' => array(self::BELONGS_TO, 'AsaCourseSchedule', array('id'=>'course_id')),
			'coursesgroup' => array(self::BELONGS_TO, 'AsaCourseGroup', array('gid'=>'id')),
			'discount' => array(self::HAS_MANY, 'AsaDiscount', array('course_id'=>'id')),
			'schedule' => array(self::HAS_ONE, 'AsaSchedule', array('id'=>'schedule_id')),
			'scheduleItem' => array(self::HAS_ONE, 'AsaScheduleItem', array('schedule_id'=>'schedule_id')),
            'courseNumber' => array(self::BELONGS_TO, 'AsaCourseNumber', array('id'=>'course_id')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'gid' => Yii::t('asa', 'ASA Project'),
			'title_cn' => Yii::t('asa', 'Course Name (CN)'),
			'title_en' => Yii::t('asa', 'Course Name (EN)'),
			'vendor' => Yii::t('asa', '供应商'),
			'unit_price' => Yii::t('asa', 'Price/Class'),
			'default_count' => Yii::t('asa', 'Class Number'),
			'capacity' => Yii::t('asa', 'Capacity'),
			'capacity_min' => Yii::t('asa', '最小开课人数'),
			'age_min' => Yii::t('asa', '最小适合年龄'),
			'age_max' => Yii::t('asa', '最大适合年龄'),
			'status' => Yii::t('asa', 'Status'),
			'weight' => Yii::t('asa', 'weight'),
			'updated' => 'Updated',
			'updated_userid' => 'Updated Userid',
			'schedule_id' => 'schedule Id',
			'refund_class_index' => Yii::t('asa', '可退费时间'),
			'course_provider' => Yii::t('asa', '课程提供方'),
			'assistant_fee' => Yii::t('asa', '助教费用'),
			'age_check' => Yii::t('asa', '年龄检查'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria = new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('gid',$this->gid);
		$criteria->compare('title_cn',$this->title_cn,true);
		$criteria->compare('title_en',$this->title_en,true);
		$criteria->compare('vendor',$this->vendor,true);
		$criteria->compare('unit_price',$this->unit_price);
		$criteria->compare('default_count',$this->default_count);
		$criteria->compare('capacity',$this->capacity);
		$criteria->compare('status',$this->status);
		$criteria->compare('weight',$this->weight);
		$criteria->compare('updated',$this->updated);
		$criteria->compare('updated_userid',$this->updated_userid);
		$criteria->compare('schedule_id',$this->schedule_id);
		$criteria->compare('refund_class_index',$this->refund_class_index);
		$criteria->compare('course_provider',$this->course_provider);
		$criteria->compare('assistant_fee',$this->assistant_fee);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AsaCourse the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	public function getTitle()
	{
		switch (Yii::app()->language) {
			case "zh_cn":
				return  $this->title_cn ;
				break;
			case "en_us":
				return  $this->title_en;
				break;
		}
	}

    public static function getConfig()
    {
        $age = array();
        $class = array();
        for ($i = 2; $i < 7; $i++) {
            $age[$i] = Yii::t("asa", "{year}Yr", array('{year}'=>$i));
        }
        $age[18] = Yii::t("asa", "Parent");
        $class[100] = Yii::t("asa", "KG");
        for ($i = 1; $i < 13; $i++) {
            $class[$i+100] = Yii::t("asa", "Gr{class}", array('{class}'=>$i));
        }

        return  array(
            'age' => $age,
            'class' => $class,
        );
    }

    public static function getLastSignin($courseId = array())
    {
        if($courseId){
                $criteria = new CDbCriteria;
                $criteria->compare('course_id', $courseId);
            $criteria->compare('status', ">0");
            $criteria->order = "service_end DESC";
            $criteria->index = "service_end";
            $attrndanceObj = AsaStaffAttendance::model()->findAll($criteria);

            if($attrndanceObj){
                return $attrndanceObj;
            }else{
                return false;
            }
        }
        return false;
    }
}
