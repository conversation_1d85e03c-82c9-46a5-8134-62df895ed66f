<div>
    <ul>
        <?php if ($model){
            foreach($model as $m){
            ?>
        <li class="srs">
            <?php if ($m->semester == 10){?>
            <a href="http://www.ivyonline.cn/child/show/semester_report_pdf.php?yid=<?php echo $m->yid;?>&classid=<?php echo $m->classid;?>&childid=<?php echo $m->childid;?>&semester=10" target="_blank"><i></i></a>
            <a href="http://www.ivyonline.cn/child/show/semester_report_pdf.php?yid=<?php echo $m->yid;?>&classid=<?php echo $m->classid;?>&childid=<?php echo $m->childid;?>&semester=10" target="_blank"><?php echo Yii::t("portfolio", "Fall");?>（<?php echo Yii::t("portfolio", "Finished");?>）</a>
            <?php }elseif($m->semester == 20){?>
            <a href="http://www.ivyonline.cn/child/show/semester_report_pdf.php?yid=<?php echo $m->yid;?>&classid=<?php echo $m->classid;?>&childid=<?php echo $m->childid;?>&semester=20" target="_blank"><i></i></a>
            <a href="http://www.ivyonline.cn/child/show/semester_report_pdf.php?yid=<?php echo $m->yid;?>&classid=<?php echo $m->classid;?>&childid=<?php echo $m->childid;?>&semester=20" target="_blank"><?php echo Yii::t("portfolio", "Spring");?>（<?php echo Yii::t("portfolio", "Finished");?>）</a>
            <?php }?>
        </li>
        <li class="c"></li>
        <?php }}else{?>
        <li><?php echo Yii::t("portfolio", "The Progress Report will be available online at the end of the semester. Please check back later. Thank you.");?></li>
        <?php }?>
    </ul>
</div>
<style type="text/css">
html {margin:0;padding:0;border:0;}
body{font-size:75%;color:#222;background:#fff;font-family:"Helvetica Neue", Arial, Helvetica, sans-serif;margin: 0;padding: 0;}
li, ul, li, ol {margin:0;padding: 0}
ul {list-style-type:none;}
ol {list-style-type:none;}
li {list-style-type:none;}
.srs{
    <?php if (count($model)==2){?>
    float: left; width: 49%;
    <?php }?>
    text-align: center;
}
.srs i{display: block; width: 48px; height: 48px;background: url(http://www.ivyonline.cn/images/filetype/file_ext_pdf.png) transparent;margin: 0 auto;overflow: hidden;}
</style>