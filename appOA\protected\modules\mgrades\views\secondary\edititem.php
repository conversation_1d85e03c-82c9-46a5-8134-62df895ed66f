<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo Yii::t('site', 'Campus Workspace'); ?></li>
        <li><?php echo Yii::t('site', 'Reports Templates'); ?></li>
        <li class="active"><?php echo $model->getName() ?></li>
    </ol>

    <?php if ($items): ?>
        <div class="row">
            <div class="col-md-2">
                <ul class="nav nav-pills nav-stacked background-gray">
                    <?php foreach ($items as $_item) { ?>
                    <li <?php if ($cid == $_item->id) {
                        echo 'class="active"';
                    } ?>>
                        <?php echo CHtml::link($_item->getName(), array('edititem', 'id' => $model->id, 'cid' => $_item->id)) ?></li>
                    </li>
                <?php } ?>
                </ul>
            </div>
            <div class="col-md-10">
                <?php if ($cid): ?>
                    <div class="mb10">
                        <button type="button" class="btn btn-primary" data-toggle="modal" data-keyboard="false" data-target='#modal3'
                                data-backdrop="static">
                            <span class="glyphicon glyphicon-plus"></span> <?php echo Yii::t('principal', 'Add a Criteria'); ?>
                        </button>
                    </div>
                <?php endif; ?>
                <?php if ($subs): ?>
                    <?php foreach ($subs as $sub): ?>
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <div class="row">
                                <div class="col-md-10"><?php echo $sub->title_cn ?></div>
                                <div class="col-md-2">
                                    <a href="<?php echo $this->createUrl('delereport', array("id" => $sub->id)); ?>"
                                       class="J_ajax_del btn btn-xs btn-danger pull-right"><span
                                            class="glyphicon glyphicon-minus"></span></a>
                                    <button type="button" class="btn btn-xs btn-primary pull-right mr5"
                                            onclick="addItem(<?php echo $sub->id ?>);">
                                        <span class="glyphicon glyphicon-plus"></span></button>
                                    <a href="<?php echo $this->createUrl('createreport', array("id" => $sub->id)); ?>"
                                       class="J_modal btn btn-xs btn-primary pull-right mr5"><span
                                            class="glyphicon glyphicon-pencil"></span></a>




                                </div>
                                </div>
                            </div>
                            <div class="bg-info p15">
                                <?php echo nl2br($sub->getName()) ?>
                            </div>
                            <ul class="list-group">
                                <?php foreach ($sub->items as $item): ?>
                                    <li class="list-group-item" data-id="<?php echo $item->id; ?>"
                                        data-sid="<?php echo $sub->id; ?>">
                                        <div class="row">
                                            <div class="col-md-10">
                                                <code class="pull-left" style="font-size: 20px"><?php echo $item->fraction ?></code>
                                                <p class="col-md-11 text-info"><?php echo nl2br($item->getName()) ?></p>
                                            </div>
                                            <div class="col-md-2">
                                                <a role="button" class="btn btn-xs btn-danger J_ajax_del pull-right" href="<?php echo $this->createUrl
                                                          ('delItem', array('id' => $item->id)) ?>"><span class="glyphicon glyphicon-minus"></span></a>
                                                <a href="<?php echo $this->createUrl('getItem', array("id" => $item->id)); ?>"
                                                   class="J_modal btn btn-xs btn-primary pull-right mr5"><span class="glyphicon glyphicon-pencil"></span></a>
                                                <a role="button" class="btn btn-xs btn-success sort-header pull-right mr5" style="cursor: move;"><span class="glyphicon glyphicon-move"></span></a>
                                            </div>
                                        </div>

                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    <?php else: ?>
        <div class="col-md-12">
            <div class="alert alert-warning" role="alert">
                <?php echo Yii::t('report', 'Please add report categories first.'); ?>
            </div>
        </div>
    <?php endif; ?>
</div>

<div class="modal fade" id="addModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title"><?php echo Yii::t('report', 'Items Edit'); ?>
                    <small></small>
                </h4>
            </div>
            <form class="form-horizontal J_ajaxForm"
                  action="<?php echo $this->createUrl('saveItembat'); ?>"
                  method="POST">
                <div class="modal-body">
                    <?php for ($i = 0; $i < 11; $i++): ?>
                        <blockquote>
                        <div class="form-group">
                            <div class="col-md-2 mb5">
                                <?php echo CHtml::textField('id[]', '', array('class' => 'form-control',
                                    'encode' => false, 'placeholder' => '分数')); ?>
                            </div>
                            <div class="col-sm-5 mb5">
                                <?php echo CHtml::textArea('title_en[]', '', array('class' => 'form-control', 'rows'=>6,
                                    'encode' => false, 'placeholder' => '英文内容')); ?>
                            </div>
                            <div class="col-sm-5">
                                <?php echo CHtml::textArea('title_cn[]', '', array('class' => 'form-control', 'rows'=>6,
                                    'encode' => false, 'placeholder' => '中文内容')); ?>
                            </div>
                        </div>
                        </blockquote>
                        <?php if($i != 10 ){
                            echo "<hr/>";
                        }?>
                    <?php endfor; ?>
                </div>
                <div class="modal-footer">
                    <input type="hidden" name="category_id" id="ReportsItem_category_id">
                    <input type="hidden" name="template_id" id="ReportsItem_template_id"
                           value="<?php echo $model->id; ?>">
                    <button type="button" class="btn btn-default pull-right"
                            data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
                    <button type="submit"
                            class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global', 'Submit'); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="modal fade" id="modal3" tabindex="-1" role="dialog" aria-labelledby="modal">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">×</span></button>
                <h4 class="modal-title"><?php echo Yii::t('principal', 'Add a Criteria'); ?></h4>
            </div>
            <?php $form = $this->beginWidget('CActiveForm', array(
                'id' => 'visits-form',
                'enableAjaxValidation' => false,
                'action' => $this->createUrl('addcategory', array("cid" => $cid)),
                'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form'),
            ));
            ?>
            <div class="modal-body">
                <div class="form-group">
                    <label class="col-xs-3 control-label"><?php echo $form->labelEx($achievement,'title_cn'); ?></label>
                    <div class="col-xs-9">
                        <?php echo $form->textField($achievement,'title_cn',array('maxlength'=>255,'class'=>'form-control')); ?>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-xs-3 control-label"><?php echo $form->labelEx($achievement,'title_en'); ?></label>
                    <div class="col-xs-9">
                        <?php echo $form->textField($achievement,'title_en',array('maxlength'=>255,'class'=>'form-control')); ?>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-xs-3 control-label"><?php echo $form->labelEx($achievement,'introduction_cn'); ?></label>
                    <div class="col-xs-9">
                        <?php echo $form->textArea($achievement,'introduction_cn',array('rows' => 6,'class'=>'form-control')); ?>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-xs-3 control-label"><?php echo $form->labelEx($achievement,'introduction_en'); ?></label>
                    <div class="col-xs-9">
                        <?php echo $form->textArea($achievement,'introduction_en',array('rows' => 6,'class'=>'form-control')); ?>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit"
                        class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
                <button type="button" class="btn btn-default"
                        data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
            </div>
            <?php $this->endWidget(); ?>
        </div>
    </div>
</div>

<script>
    function addItem(pid) {
        $('#addModal #ReportsItem_category_id').val(pid);
        $('#addModal').modal({backdrop: 'static', keyboard: false});
    }
    function cbSuccess() {
        setTimeout(function () {
            location.reload();
        }, 1000)
    }

    var modal = '<div class="modal fade" id="modal" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog" role="document"><div class="modal-content"></div></div></div>';
    $('body').append(modal);
    $(".list-group").sortable({
        placeholder: "ui-state-highlight",
        handle: ".sort-header",
        axis: 'y',
        opacity: 0.8,
        start: function (event, ui) {
            $('.ui-state-highlight').height($(ui['item'][0]).outerHeight()).css("list-style-type", "none");
        },
        update: function (event, ui) {
            var sort = {};
            var cc = 1;
            $(ui['item'][0]).parents('.list-group').find('li').each(function () {
                sort[$(this).data('id')] = cc++;

            });
            $.post('<?php echo $this->createUrl('updateSort');?>', {sort: sort}, function (data) {
                if(data.state == 'fail'){
                    resultTip({error: 'warning', msg: data.message});
                }
            }, 'json');
        }
    })
</script>