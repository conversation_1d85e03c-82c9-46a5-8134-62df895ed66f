<?php

/**
 * This is the model class for table "ivy_event_member".
 *
 * The followings are the available columns in table 'ivy_event_member':
 * @property integer $id
 * @property integer $event_id
 * @property string $parent_name
 * @property string $child_name
 * @property integer $child_birthday
 * @property string $email
 * @property string $tel
 * @property integer $adult_number
 * @property integer $kid_number
 * @property string $address
 * @property integer $knowus
 * @property string $from
 * @property integer $timestamp
 */
class EventMember extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return EventMember the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_event_member';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('event_id, parent_name, child_birthday, tel, timestamp', 'required'),
			array('event_id, child_birthday, timestamp', 'numerical', 'integerOnly'=>true),
			array('parent_name, child_name, email, tel, address, from', 'length', 'max'=>255),
            array('adult_number, kid_number, knowus', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, event_id, parent_name, child_name, child_birthday, email, tel, adult_number, kid_number, address, knowus, from, timestamp', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'event_id' => 'Event',
			'parent_name' => Yii::t('event', 'Parent Name'),
			'child_name' => Yii::t('event', 'Child Name'),
			'child_birthday' => Yii::t('event', 'Child Birthday'),
			'email' => Yii::t('event', 'Email'),
			'tel' => Yii::t('event', 'Tel'),
			'adult_number' => Yii::t('event', 'Adult Number'),
			'kid_number' => Yii::t('event', 'Kid Number'),
			'address' => Yii::t('event', 'Address'),
			'knowus' => Yii::t('event', 'Knowus'),
			'from' => 'From',
			'timestamp' => Yii::t('event', 'Timestamp'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('event_id',$this->event_id);
		$criteria->compare('parent_name',$this->parent_name,true);
		$criteria->compare('child_name',$this->child_name,true);
		$criteria->compare('child_birthday',$this->child_birthday);
		$criteria->compare('email',$this->email,true);
		$criteria->compare('tel',$this->tel,true);
		$criteria->compare('adult_number',$this->adult_number);
		$criteria->compare('kid_number',$this->kid_number);
		$criteria->compare('address',$this->address,true);
		$criteria->compare('knowus',$this->knowus);
		$criteria->compare('from',$this->from,true);
		$criteria->compare('timestamp',$this->timestamp);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}