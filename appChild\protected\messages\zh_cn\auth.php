<?php

/**
 * Message translations.
 *
 * This file is automatically generated by 'yiic message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE, this file must be saved in UTF-8 encoding.
 *
 * @version $Id: $
 */
return array(
    'Name of Child' => '孩子姓名',
    'Name of Parent' => '家长姓名',
    'Verify Code' => '验证码',
    'Special Notes' => '备注',
	'What is my IvyOnline account?' => '我的艾毅在线帐号是什么？',
    "We understand you're having problems logging on to IvyOnline. Please fill in the form below and our campus staff will follow up after you submit it." => "我们了解到您由于某种原因而无法登录艾毅在线，请填写下面的表单，校园的行政人员收到后会第一时间帮您解决登录问题。",
    "Please kindly note that our campus staff may contact you when verifying your information. Thank you!" => "请注意：校园行政在核对您的信息时，可能会与您取得联系，请知晓。谢谢。",
    "Thank you for your submission. Our campus support team will follow up on this request shortly." => "感谢您提交信息，稍后校园支持团队会协助跟进。",
);
