<?php

/**
 * This is the model class for table "ivy_class_list".
 *
 * The followings are the available columns in table 'ivy_class_list':
 * @property integer $classid
 * @property string $schoolid
 * @property integer $yid
 * @property integer $teacher
 * @property string $title
 * @property string $child_age
 * @property string $capacity
 * @property string $fill_ratio
 * @property string $fte_ratio
 * @property string $picture
 * @property integer $stat
 * @property string $periodtime
 * @property integer $child_total
 * @property integer $child_inclass
 * @property integer $child_boys
 * @property integer $child_girls
 * @property integer $child_waiting
 * @property integer $child_applys
 * @property string $introduction
 * @property integer $updated_timestamp
 * @property integer $created_timestamp
 * @property integer $userid
 * @property string $classtype
 * @property integer $spring_target
 * @property integer $fall_target
 * @property integer $ufida_update
 */
class IvyClass extends CActiveRecord
{

    const STATS_PREPARING   = 0;    //预备中
    const STATS_OPEN        = 10;     //开放
    const STATS_CLOSED      = 20;     //关闭

    public $periodData;

    public $uploadPhoto;

    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return IvyClass the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'ivy_class_list';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('schoolid, yid,  title, child_age, periodData', 'required'),
            array('yid, teacher, stat, child_total, capacity, child_inclass, child_boys, child_girls, child_waiting, child_applys, updated_timestamp, created_timestamp, userid, spring_target, fall_target, ufida_update', 'numerical', 'integerOnly' => true),
            array('schoolid, title, child_age, fill_ratio, fte_ratio, picture, periodtime', 'length', 'max' => 255),
            array('classtype', 'length', 'max' => 5),
            array('classtype', 'checkClassType'),
            array('introduction, schedule_code', 'safe'),
            // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array('classid, schoolid, yid, teacher, title, child_age, capacity, fill_ratio, fte_ratio, picture, stat, periodtime, child_total, child_inclass, child_boys, child_girls, child_waiting, child_applys, introduction, updated_timestamp, created_timestamp, userid, classtype, spring_target, fall_target, ufida_update', 'safe', 'on' => 'search'),
            array("uploadPhoto", "file", "types" => "jpg, gif, png, jpeg", "allowEmpty" => true),
        );
    }

    public function checkClassType($attribute, $params)
    {
        //        if (!$this->hasErrors()) {
        if (!in_array(trim($this->classtype), array_keys(IvyClass::getAgeMapping()))) {
            $this->addError("classtype", Yii::t("labels", "Invalid classtype"));
        }
        //        }
    }
    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
            'classInfo' => array(self::HAS_MANY, 'ChildStudyHistory', 'classid'),
            'schoolInfo' => array(self::BELONGS_TO, 'Branch', 'schoolid'),
            'calendarInfo' => array(self::BELONGS_TO, 'Calendar', 'yid'),
            'teacherInfo' => array(self::HAS_MANY, 'ClassTeacher', 'classid', 'order' => 'teacherInfo.isheadteacher desc, teacherInfo.weight'),
            'children' => array(self::HAS_MANY, 'ChildProfileBasic', array('classid' => 'classid'), 'with' => 'nextYear', 'order' => 'children.status ASC'),
            'reserveChildren' => array(self::HAS_MANY, 'ChildReserve', array('classid' => 'classid'), 'with' => 'childProfile'),
            'studentCount' => array(self::STAT, 'ChildProfileBasic', 'classid', 'condition' => 'status<100 or status=888'),
            'reserveCount' => array(self::STAT, 'ChildReserve', 'classid', 'condition' => 'stat<100 or stat=888'),
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'classid' => 'Classid',
            'schoolid' => 'Schoolid',
            'yid' => Yii::t('labels', 'School Year'),
            'teacher' => 'Teacher',
            'title' => Yii::t('labels', 'Class Title'),
            'child_age' => 'Child Age',
            'capacity' => Yii::t('labels', 'Capacity'),
            'fill_ratio' => 'Fill Ratio',
            'fte_ratio' => 'Fte Ratio',
            'picture' => 'Picture',
            'stat' => Yii::t('labels', 'Status'),
            'periodtime' => 'Periodtime',
            'child_total' => 'Child Total',
            'child_inclass' => 'Child Inclass',
            'child_boys' => 'Child Boys',
            'child_girls' => 'Child Girls',
            'child_waiting' => 'Child Waiting',
            'child_applys' => 'Child Applys',
            'introduction' => Yii::t('labels', 'Room NO.'),
            'updated_timestamp' => 'Updated Timestamp',
            'created_timestamp' => 'Created Timestamp',
            'userid' => 'Userid',
            'classtype' => Yii::t('labels', 'Class Type'),
            'spring_target' => 'Spring Target',
            'fall_target' => 'Fall Target',
            'ufida_update' => 'Ufida Update',
            'periodData' => Yii::t('labels', 'period slot')
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search()
    {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('classid', $this->classid);
        $criteria->compare('schoolid', $this->schoolid, true);
        $criteria->compare('yid', $this->yid);
        $criteria->compare('teacher', $this->teacher);
        $criteria->compare('title', $this->title, true);
        $criteria->compare('child_age', $this->child_age, true);
        $criteria->compare('capacity', $this->capacity, true);
        $criteria->compare('fill_ratio', $this->fill_ratio, true);
        $criteria->compare('fte_ratio', $this->fte_ratio, true);
        $criteria->compare('picture', $this->picture, true);
        $criteria->compare('stat', $this->stat);
        $criteria->compare('periodtime', $this->periodtime, true);
        $criteria->compare('child_total', $this->child_total);
        $criteria->compare('child_inclass', $this->child_inclass);
        $criteria->compare('child_boys', $this->child_boys);
        $criteria->compare('child_girls', $this->child_girls);
        $criteria->compare('child_waiting', $this->child_waiting);
        $criteria->compare('child_applys', $this->child_applys);
        $criteria->compare('introduction', $this->introduction, true);
        $criteria->compare('updated_timestamp', $this->updated_timestamp);
        $criteria->compare('created_timestamp', $this->created_timestamp);
        $criteria->compare('userid', $this->userid);
        $criteria->compare('classtype', $this->classtype, true);
        $criteria->compare('spring_target', $this->spring_target);
        $criteria->compare('fall_target', $this->fall_target);
        $criteria->compare('ufida_update', $this->ufida_update);

        return new CActiveDataProvider($this, array(
            'criteria' => $criteria,
        ));
    }

    public static function formatPeriodtime($str)
    {
        return preg_replace("/^(\w+)\|(\w+)\|(\w+)\|(\w+)$/i", "$1:$2-$3:$4", $str);
    }

    public static function formatSchoolYear($startyear)
    {
        return $startyear . ' - ' . ($startyear + 1);
    }

    /**
     * 取班级信息
     * @param int $classId
     * @param array|string $param
     * @return array|string|obj
     * <AUTHOR>
     */
    public function getClassInfo($classId, $param = array())
    {
        if ($classId) {
            $obj = $this->findByPk($classId);
            if (is_array($param) && count($param)) {
                return $obj->getAttributes($param);
            } elseif (is_string($param) && strlen($param)) {
                return $obj->getAttribute($param);
            } else {
                return $obj;
            }
        }
        return null;
    }

    public static function getClassTypes($bilingual = true, $type = Branch::TYPE_CAMPUS_MI_PRESCHOOL)
    {
        $MI = array(
            'c' => 'CaRousel' . (($bilingual) ? ' (亲子班)' : ''),
            'n' => 'Nursery' . (($bilingual) ? ' (托班)' : ''),
            'b' => 'Pre-K1' . (($bilingual) ? ' (小班)' : ''),
            'p' => 'Pre-K2' . (($bilingual) ? ' (中班)' : ''),
            'k' => 'Kindergarten' . (($bilingual) ? ' (大班)' : ''),
        );
        $MM = array(
            'n' => 'Nursery' . (($bilingual) ? ' (托班)' : ''),
            'mt' => 'Toddler' . (($bilingual) ? ' (学步班)' : ''),
            'mc' => 'CASA' . (($bilingual) ? ' (3-6混龄班)' : ''),
            'mr' => 'Reggio' . (($bilingual) ? ' (Reggio)' : ''),
            'b' => 'Pre-K1' . (($bilingual) ? ' (小班)' : ''),
            'p' => 'Pre-K2' . (($bilingual) ? ' (中班)' : ''),
            'k' => 'Kindergarten' . (($bilingual) ? ' (大班)' : ''),
        );
        $PS = array(
            'mk' => 'Kinder Class' . (($bilingual) ? ' (中文：大班)' : ''),
            'e1' => 'Grade 1' . (($bilingual) ? ' (一年级)' : ''),
            'e2' => 'Grade 2' . (($bilingual) ? ' (二年级)' : ''),
            'e3' => 'Grade 3' . (($bilingual) ? ' (三年级)' : ''),
            'e4' => 'Grade 4' . (($bilingual) ? ' (四年级)' : ''),
            'e5' => 'Grade 5' . (($bilingual) ? ' (五年级)' : ''),
            'e6' => 'Grade 6' . (($bilingual) ? ' (六年级)' : ''),
            'e7' => 'Grade 7' . (($bilingual) ? ' (七年级)' : ''),
            'e8' => 'Grade 8' . (($bilingual) ? ' (八年级)' : ''),
            'e9' => 'Grade 9' . (($bilingual) ? ' (九年级)' : ''),
            'e10' => 'Grade 10' . (($bilingual) ? ' (十年级)' : ''),
            'e11' => 'Grade 11' . (($bilingual) ? ' (十一年级)' : ''),
            'e12' => 'Grade 12' . (($bilingual) ? ' (十二年级)' : ''),
        );
        $DS = array_merge($MM, $PS);

        if ($type) {
            switch ($type) {
                case Branch::TYPE_CAMPUS_MI_PRESCHOOL:
                default:
                    return $MI;
                    break;
                case Branch::TYPE_CAMPUS_MONT_PRESCHOOL:
                    return $MM;
                    break;
                case Branch::TYPE_CAMPUS_ELEMENTARY_SCHOOL:
                    return $PS;
                    break;
                case Branch::TYPE_CAMPUS_DAYSTAR:
                    return $DS;
                    break;
            }
        } else {
            return array(
                Branch::TYPE_CAMPUS_MI_PRESCHOOL => $MI,
                Branch::TYPE_CAMPUS_MONT_PRESCHOOL => $MM,
                Branch::TYPE_CAMPUS_ELEMENTARY_SCHOOL => $PS,
                Branch::TYPE_CAMPUS_DAYSTAR => $DS,
            );
        }
    }

    public static function getAgeMapping()
    {
        return array(
            'c' => 1,
            'n' => 2,
            'b' => 3,
            'p' => 4,
            'k' => 5,
            'mt' => 2,
            'mc' => 3,
            'mr' => 5,
            'mk' => 5,
            'e1' => 7,
            'e2' => 8,
            'e3' => 9,
            'e4' => 10,
            'e5' => 11,
            'e6' => 12,
            'e7' => 13,
            'e8' => 14,
            'e9' => 15,
            'e10' => 16,
            'e11' => 17,
            'e12' => 18,
        );
    }

    public static function getAgeList()
    {
        return array(
            "2" => '2岁',
            "3" => '3岁',
            "4" => '4岁',
            "5" => '5岁',
        );
    }

    public static function getClassList($schoolid, $yid, $onlyPrimaryClass = false, $onlyKG = false)
    {
        $crit = new CDbCriteria();
        $crit->compare('schoolid', $schoolid);
        $crit->compare('yid', $yid);
        //        $crit->compare('stat', 10);
        if ($onlyPrimaryClass) {
            $crit->compare('child_age', '>=5');
        }
        if ($onlyKG) {
            $crit->compare('child_age', '<=5');
        }
        $crit->order = 'child_age ASC, title ASC';
        return IvyClass::model()->findAll($crit);
    }


    /**
     * 此函数下的内容慎动（影响DS收费系统）
     * @return array
     * <AUTHOR> Yu <<EMAIL>>
     */
    public static function getClassTypeForFee()
    {
        return array(
            'mt' => 'BY_TODDLER',
            'mc' => 'BY_TODDLER',
            'mk' => 'BY_KINDERGARTEN',
            'e1' => 'BY_GRADE',
            'e2' => 'BY_GRADE',
            'e3' => 'BY_GRADE',
            'e4' => 'BY_GRADE',
            'e5' => 'BY_GRADE',
            'e6' => 'BY_MIDDLE',
            'e7' => 'BY_MIDDLE',
            'e8' => 'BY_MIDDLE',
            'e9' => 'BY_HIGH',
            'e10' => 'BY_HIGH',
            'e11' => 'BY_HIGH_DP',
            'e12' => 'BY_HIGH_DP',
        );
    }

    /**
     * 获取学部下班级及学生信息
     *
     * @param array $groupList
     * @param string $schoolId
     * @param int $yid
     * @return array
     */
    public static function getChildInfoByGroup($groupList, $schoolId, $yid)
    {
        $data = array();
        foreach ($groupList as $item) {
            $data[$item] = 0;
        }
        $groupClass = self::getClassListByGroup($groupList, $schoolId, $yid);
        foreach ($groupClass as $group => $classes) {
            $classidList = array();
            $classInfo = array();
            foreach ($classes as $class) {
                $classInfo[] = array(
                    'classid' => $class->classid,
                    'classTitle' => $class->title,
                );
                $classidList[] = $class->classid;
            }
            if (!$classidList) {
                $data[$group] = array();
                continue;
            } else {
                $crit = new CDbCriteria();
                $crit->compare("status", "<100");
                $crit->compare("classid", $classidList);
                $childModels = ChildProfileBasic::model()->findAll($crit);
                $childInfo = array();
                foreach ($childModels as $childModel) {
                    $childInfo[] = array(
                        'childid' => $childModel->childid,
                        'classid' => $childModel->classid,
                        'childName' => $childModel->getChildName(),
                        'childPhoto' => CommonUtils::childPhotoUrl($childModel->photo),
                        'fid' => $childModel->fid,
                        'mid' => $childModel->mid,
                    );
                }
                $data[$group]['classInfo'] = $classInfo;
                $data[$group]['childInfo'] = $childInfo;
            }
        }
        return $data;
    }

    /**
     * 获取指定学部下的班级列表
     *
     * @param string $schoolId
     * @param int $yid
     * @param array $groupList, 空数组时获取所有
     * @return array
     */
    public static function getClassListByGroup($schoolId, $yid, $groupList)
    {
        if (!$groupList) {
            $groupList = array_keys(self::getGradeGroupList());
        }
        $classType = array();
        $classToGroup = self::getClassToGroup();

        foreach ($classToGroup as $key => $group) {
            if (in_array($group, $groupList)) {
                $classType[] = $key;
            }
        }
        $crit = new CDbCriteria();
        $crit->compare('schoolid', $schoolId);
        $crit->compare('yid', $yid);
        $crit->compare('classtype', $classType);
        $crit->compare('stat', self::STATS_OPEN);
        $crit->order = 'child_age ASC, title ASC';
        $models = self::model()->findAll($crit);
        $data = array();
        foreach ($models as $model) {
            $group = $classToGroup[$model->classtype];
            $data[$group][] = $model;
        }
        return $data;
    }

    // 获取学部列表
    public static function getGradeGroupListBySchoolId($schoolId)
    {
        $array = array(
            'BJ_DS' => array(
                "ES" => Yii::t("newDS", "ES"),
                "MS" => Yii::t("newDS", "MS"),
                "HS" => Yii::t("newDS", "HS"),
            ),
            'BJ_SLT' => array(
                "ES" => Yii::t("newDS", "ES"),
            ),
            'BJ_QFF' => array(
                "KG" => Yii::t("newDS", "KG"),
            ),
        );
        return isset($array[$schoolId]) ? $array[$schoolId] : array("IVYKG" => Yii::t("newDS", "KG"));
    }

    // 获取学部列表
    public static function getGradeGroupList()
    {
        return array(
            "KG" => Yii::t("newDS", "KG"),
            "ES" => Yii::t("newDS", "ES"),
            "MS" => Yii::t("newDS", "MS"),
            "HS" => Yii::t("newDS", "HS"),
        );
    }
    // 班级类型与学部的映射
    public static function getClassToGroup()
    {
        return array(
            'c' => 'KG',
            'n' => 'KG',
            'b' => 'KG',
            'p' => 'KG',
            'mc' => 'KG',
            'mr' => 'KG',
            'mk' => 'ES',
            'e1' => 'ES',
            'e2' => 'ES',
            'e3' => 'ES',
            'e4' => 'ES',
            'e5' => 'ES',
            'e6' => 'MS',
            'e7' => 'MS',
            'e8' => 'MS',
            'e9' => 'HS',
            'e10' => 'HS',
            'e11' => 'HS',
            'e12' => 'HS',
        );
    }
}
