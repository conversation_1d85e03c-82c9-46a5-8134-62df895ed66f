<?php
/**
 * author: xin.ran
 */
class CXoWebUserStaff extends CWebUser
{
	private $_keyPrefix;
	const XOGROUP_PARENT=9;
	const XOGROUP_STAFF=4;
	
	const ROLE_PARENT='parent';
	const ROLE_STAFF='staff';

	public $logoutUrl="";
    public $roles = null;

    public function init(){
        parent::init();
        $this->getRoles();
    }

	public function getStateKeyPrefix()
	{
		if($this->_keyPrefix!==null)
			return $this->_keyPrefix;
		else
			return $this->_keyPrefix=md5('Yii.CXoWebUser.Apps.Mims');
	}
	
	/**
	 * 初始化用户角色
	 */
	public function grantRole($userid=0,$forceRefresh=false)
	{
		if(!$userid) return false;
		$auth=Yii::app()->authManager;
		$roles = $auth->getAuthAssignments($userid);
		
		if( ( empty($roles) || $forceRefresh ) && $userid ){
		    Yii::trace('entering grant role');
		    if($forceRefresh && !emtpy($roles)){
				foreach($roles as $role){
				    $auth->revoke($role, $userid );	
				}
		    }
			$groups = Yii::app()->user->getXoGroups($userid);
			if(!empty($groups)){
				if(in_array(self::XOGROUP_STAFF, $groups)){
				    $auth->assign(self::ROLE_STAFF,$userid);
					$this->setState('_firstLogin', true);
					return true;
				}else{
					return false;
				}
			}
		}else{
			foreach(array_keys($roles) as $role){
				if(in_array($role, array(self::ROLE_PARENT, self::XOGROUP_STAFF))){
					return true;
				}
				
			}
		}
		return false;
	}
	
	///**
	// * 继承beforeLogin, 根据返回觉得是否login
	// */
	//protected function beforeLogin($id,$states,$fromCookie)
	//{
	//	return $this->grantRole($id);
	//}		

	/**
	 * 获取XOOPS中的用户组
	 */
	public function getXoGroups($userid)
	{
		$xoGroups = array();
		$groups = ($userid) ? XoGroupsUsersLink::model()->findAllByAttributes(array("uid"=>$userid)) : array();
		foreach($groups as $group){
			$xoGroups[$group->groupid] = $group->groupid;
		}
		return $xoGroups;
	}
	
	public function getAdminBranches($type=null){
		if(!empty($type)){
			$data = CHtml::listData(
				AdmBranchLink::model()->findAllByAttributes(
					array("type"=>$type, "uid"=>$this->getId())),
				'schoolid', 'schoolid'
			);
			return $data;
		}
		return array();
	}

    public function getRoles(){
        if(is_null($this->roles) && $this->getId()){
            $this->roles = array_keys( Yii::app()->authManager->getRoles($this->getId()) );
        }
        return $this->roles;
    }
	
	public function checkAccess($operation, $params=array(), $omitOnGroups=array('ivystaff_it')){
		$result = parent::checkAccess($operation, $params);
		if(!$result && !empty($omitOnGroups)){
			$inters = array_intersect( $this->roles, $omitOnGroups );
			if(!empty($inters)){
				$result = true;
			}
		}
		return $result;
	}
}