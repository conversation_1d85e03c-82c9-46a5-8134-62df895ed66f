<?php

/**
 * This is the model class for table "ivy_visits_config".
 *
 * The followings are the available columns in table 'ivy_visits_config':
 * @property integer $id
 * @property string $schoolid
 * @property string $week
 * @property string $day
 * @property string $time
 * @property integer $update_timestamp
 * @property integer $status
 * @property integer $status_text_cn
 * @property integer $status_text_en
 */
class VisitsConfig extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_visits_config';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('schoolid, time', 'required'),
			array('update_timestamp, status', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>25),
			array('week, day, time, status_text_cn, status_text_en', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, schoolid, week, day, time, update_timestamp, status, status_text_cn, status_text_en', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'schoolid' => 'Schoolid',
			'week' => 'Week',
			'day' => 'Day',
			'time' => 'Time',
			'update_timestamp' => 'Update Timestamp',
			'status' => 'Status',
			'status_text_cn' => 'Status Text Cn',
			'status_text_en' => 'Status Text En',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('week',$this->week,true);
		$criteria->compare('day',$this->day,true);
		$criteria->compare('time',$this->time,true);
		$criteria->compare('update_timestamp',$this->update_timestamp);
		$criteria->compare('status',$this->status);
		$criteria->compare('status_text_cn',$this->status_text_cn);
		$criteria->compare('status_text_en',$this->status_text_en);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return VisitsConfig the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
