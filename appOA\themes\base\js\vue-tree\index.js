!function(e,n){"object"==typeof exports&&"object"==typeof module?module.exports=n():"function"==typeof define&&define.amd?define("vue2-org-tree",[],n):"object"==typeof exports?exports["vue2-org-tree"]=n():e["vue2-org-tree"]=n()}(this,function(){return function(e){function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}var t={};return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{configurable:!1,enumerable:!0,get:r})},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},n.p="dist",n(n.s=0)}([function(e,n,t){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=t(1);n.default=r.a},function(e,n,t){"use strict";function r(e){r.installed||(r.installed=!0,e.component(o.a.name,o.a))}var o=t(2);o.a.install=r,"undefined"!=typeof window&&window.Vue&&window.Vue.use(o.a),n.a=o.a},function(e,n,t){"use strict";function r(e){t(3)}var o=t(5),a=t(7),s=t(4),i=r,l=s(o.a,a.a,!1,i,null,null);n.a=l.exports},function(e,n){},function(e,n){e.exports=function(e,n,t,r,o,a){var s,i=e=e||{},l=typeof e.default;"object"!==l&&"function"!==l||(s=e,i=e.default);var u="function"==typeof i?i.options:i;n&&(u.render=n.render,u.staticRenderFns=n.staticRenderFns,u._compiled=!0),t&&(u.functional=!0),o&&(u._scopeId=o);var d;if(a?(d=function(e){e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,e||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),r&&r.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(a)},u._ssrRegister=d):r&&(d=r),d){var c=u.functional,p=c?u.render:u.beforeCreate;c?(u._injectStyles=d,u.render=function(e,n){return d.call(n),p(e,n)}):u.beforeCreate=p?[].concat(p,d):[d]}return{esModule:s,exports:i,options:u}}},function(e,n,t){"use strict";var r=t(6);n.a={name:"Vue2OrgTree",data:function(){return{dragState:{dragg:null,drop:null}}},components:{OrgTreeNode:{render:r.a,functional:!0}},props:{data:{type:Object,required:!0},props:{type:Object,default:function(){return{label:"label",expand:"expand",children:"children"}}},horizontal:Boolean,selectedKey:String,collapsable:Boolean,renderContent:Function,labelWidth:[String,Number],labelClassName:[Function,String],selectedClassName:[Function,String]},methods:{onDragStart:function(e,n){var t=this.dragState;try{e.dataTransfer.setData("text/plain","")}catch(e){}t.drag=n},onDragOver:function(e,n){e.preventDefault()},onDrop:function(e,n){e.preventDefault();var t=this.dragState,r=t.drag;t.drop=n,this.$emit("on-node-drop",e,r,n)}}}},function(e,n,t){"use strict";function r(e,n){var t=function(e,n){for(var t=arguments.length,r=Array(t>2?t-2:0),o=2;o<t;o++)r[o-2]=arguments[o];if("function"==typeof e){var a=n.target&&n.target.className;if("string"==typeof a&&a.indexOf("org-tree-node-btn")>-1)return;e.apply(null,[n].concat(r))}};return function(r){if(Array.isArray(e)){var o=!0,a=!1,s=void 0;try{for(var i,l=e[Symbol.iterator]();!(o=(i=l.next()).done);o=!0){var u=i.value;t(u,r,n)}}catch(e){a=!0,s=e}finally{try{!o&&l.return&&l.return()}finally{if(a)throw s}}}else t(e,r,n)}}function o(e,n,t){var r=t.props,o=["org-tree-node"],a=[],l=n[r.props.children];return d(n,r.props.children)?o.push("is-leaf"):r.collapsable&&!n[r.props.expand]&&o.push("collapsed"),a.push(s(e,n,t)),r.collapsable&&!n[r.props.expand]||a.push(i(e,l,t)),e("div",{domProps:{className:o.join(" ")}},a)}function a(e,n,t){var r=t.props,o=t.listeners,a=o["on-expand"],s=["org-tree-node-btn"];return n[r.props.expand]&&s.push("expanded"),e("span",{domProps:{className:s.join(" ")},on:{click:function(e){return a&&a(e,n)}}})}function s(e,n,t){var o=t.props,s=t.listeners,i=n[o.props.label],l=o.renderContent,c=s[u.CLICK],p=s[u.MOUSEOUT],f=s[u.MOUSEOVER],v=s[u.DRAGSTART],m=s[u.DRAGOVER],g=s[u.DROP],h=[];if("function"==typeof l){var b=l(e,n);b&&h.push(b)}else h.push(i);o.collapsable&&!d(n,o.props.children)&&h.push(a(e,n,t));var y=["org-tree-node-label-inner"],x=o.labelWidth,O=o.labelClassName,_=o.selectedClassName,C=o.selectedKey;return"number"==typeof x&&(x+="px"),"function"==typeof O&&(O=O(n)),O&&y.push(O),"function"==typeof _&&(_=_(n)),_&&C&&n[C]&&y.push(_),e("div",{domProps:{className:"org-tree-node-label",draggable:!0},on:{dragstart:r(v,n),dragover:r(m,n),drop:r(g,n)}},[e("div",{domProps:{className:y.join(" ")},style:{width:x},on:{click:r(c,n),mouseout:r(p,n),mouseover:r(f,n)}},h)])}function i(e,n,t){if(Array.isArray(n)&&n.length){var r=n.map(function(n){return o(e,n,t)});return e("div",{domProps:{className:"org-tree-node-children"}},r)}return""}function l(e,n){return o(e,n.props.data,n)}var u={CLICK:"on-node-click",MOUSEOUT:"on-node-mouseout",MOUSEOVER:"on-node-mouseover",DRAGSTART:"on-node-drag-start",DRAGOVER:"on-node-drag-over",DROP:"on-node-drop"},d=function(e,n){return!(Array.isArray(e[n])&&e[n].length>0)};n.a=l},function(e,n,t){"use strict";var r=function(){var e=this,n=e.$createElement,t=e._self._c||n;return t("div",{staticClass:"org-tree-container"},[t("div",{staticClass:"org-tree",class:{horizontal:e.horizontal,collapsable:e.collapsable}},[t("org-tree-node",{attrs:{data:e.data,props:e.props,horizontal:e.horizontal,"label-width":e.labelWidth,collapsable:e.collapsable,"render-content":e.renderContent,"label-class-name":e.labelClassName,"selected-class-name":e.selectedClassName,"selected-key":e.selectedKey},on:{"on-expand":function(n,t){return e.$emit("on-expand",n,t)},"on-node-focus":function(n,t){return e.$emit("on-node-focus",n,t)},"on-node-click":function(n,t){return e.$emit("on-node-click",n,t)},"on-node-mouseover":function(n,t){return e.$emit("on-node-mouseover",n,t)},"on-node-mouseout":function(n,t){return e.$emit("on-node-mouseout",n,t)},"on-node-drag-start":function(n,t){return e.onDragStart(n,t)},"on-node-drag-over":function(n,t){return e.onDragOver(n,t)},"on-node-drop":function(n,t){return e.onDrop(n,t)}}})],1)])},o=[],a={render:r,staticRenderFns:o};n.a=a}])});
//# sourceMappingURL=index.js.map