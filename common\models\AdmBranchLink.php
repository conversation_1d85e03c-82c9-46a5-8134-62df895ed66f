<?php

/**
 * This is the model class for table "ivy_adm_branch_link".
 *
 * The followings are the available columns in table 'ivy_adm_branch_link':
 * @property integer $id
 * @property integer $uid
 * @property string $type
 * @property string $schoolid
 * @property integer $startyear
 * @property integer $userid
 */
class AdmBranchLink extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return AdmBranchLink the static model class
	 */
	 
	const ADM_TYPE_PD = 'pd';
	const ADM_TYPE_CD = 'cd';
	const ADM_TYPE_OM = 'om';
	const ADM_TYPE_FINANCE = 'fn'; //财务，审批流中的审批权限
	const ADM_TYPE_FINANCE_MGR = 'fnmgr'; //财务经理，提现、余额处置的权限
    const ADM_TYPE_SURVEY_PARENT = 'parentsurvey';
    const ADM_TYPE_SURVEY_STAFF = 'staffsurvey';
    const ADM_TYPE_ASA = 'asa';

	const ADM_TYPE_OUTDATED = 'adm_school';
	
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_adm_branch_link';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('schoolid,uid', 'required'),
			array('uid, startyear, userid', 'numerical', 'integerOnly'=>true),
			array('type', 'length', 'max'=>32),
			array('schoolid', 'length', 'max'=>10),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, uid, type, schoolid, startyear, userid', 'safe', 'on'=>'search'),
		);
	}
	
	
	public function scopes(){
		return array(
			'adminBranch' => array(
				'condition' => 'type="adm_school"',
			),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
            return array(
                'user'=>array(self::BELONGS_TO,'User','uid'),
                'userProfile'=>array(self::BELONGS_TO,'UserProfile','uid'),
            );
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'uid' => 'Uid',
			'type' => 'Type',
			'schoolid' => 'Schoolid',
			'startyear' => 'Startyear',
			'userid' => 'Userid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('uid',$this->uid);
		$criteria->compare('type',$this->type,true);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('startyear',$this->startyear);
		$criteria->compare('userid',$this->userid);

		return new CActiveDataProvider(get_class($this), array(
			'criteria'=>$criteria,
		));
	}
	
	/**
	 * 根据用户的ID，获取所管理的学校信息
	 * @param int $userId
	 * @return array
	 * <AUTHOR>
	 */
	public function getMultipleBranches($userId)
	{
		$resultList = array();
		if ($userId)
		{
			$criteria = new CDbCriteria;
			$criteria->select = 'schoolid';
			$criteria->compare('uid', $userId);
			$branchObj = $this->findAll($criteria);
			//$branchObj = $this->adminBranch()->findAll($criteria);
			if (!empty($branchObj))
			{
				foreach ($branchObj as $v)
				{
					$resultList[$v->schoolid] = $v->schoolid;
				}
			}
		}
		return $resultList;
	}
    
    /*
     * 根据学校ID与类型取得管理此校园的用户
     * @param string $schoolid
     * @param string $type
     * @return obj
     */
    public function getUserBySchool($schoolid,$type){
        $criter = new CDbCriteria;
		if (!empty($schoolid)){
			 $criter->compare('schoolid', $schoolid);
		}
        $criter->compare('type', $type);
        $userModel =$this->model()->with('user')->findAll($criter);
        return (empty($userModel)) ? null : $userModel;
    }
    
    /**
     * 多校园工作流审核组列表
     */
    public static function getMultSchoolWorkflowCheck(){
        $multAdmin = OA::getMultiAdmType();
        $excludeList = array('edu','staffsurvey','parentsurvey','adm_school');
        foreach ($multAdmin as $admin=>$val){
            if (!in_array($admin,$excludeList)){
                $sureMultAdmin[$admin] = $val;
            }
        }
        unset($multAdmin);
        unset($excludeList);
        return $sureMultAdmin;
	}
	
	/**
	 * 判断用户在指定校园是否拥有指定类型的权限
	 *
	 * @param [string] $schoolid
	 * @param [int] $uid
	 * @param [array] $type
	 * @return bool
	 */
    public static function checkAccess($schoolid, $uid, $type)
    {
        $type = array(self::ADM_TYPE_ASA, self::ADM_TYPE_CD);
        $crit = new CDbCriteria();
        $crit->compare('schoolid', $schoolid);
        $crit->compare('uid', $uid);
        $crit->compare('type', $type);
		$exist = self::model()->exists($crit);
		return $exist;
    }
}