/*
===================
@explain: 系统后台内容区域
@copyright: Copyright 2012,phpwind.com
@author: <EMAIL>
$Id: admin_style.css 24419 2013-01-30 05:53:47Z yanchixia $
===================
*/
html,body, 
div, dl, dt, dd, ul, p, th, td,
h1, h2, h3, h4, h5, h6, 
pre, code, form, 
fieldset, legend{
	margin: 0;
	padding: 0;
}
cite, 
em, 
th {
	font-style: inherit;
	font-weight: inherit;
}
strong{
	font-weight:700;
}
td, 
th, 
div {
	word-break:break-word;
	word-wrap:break-word;
}
table {
	border-collapse: collapse;
	border-spacing:0;
}
th {
	text-align: left;
	font-weight:100;
}
ol li {
	list-style: decimal outside;
}
ol{
	padding:0 0 0 18px;
	margin:0;
}
li {
	list-style:none;
}
img {
	border: 0;
}
html {
	-webkit-text-size-adjust:none;
}
/*
===================
html5相关标签
===================
*/
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
nav,
section {
  display: block;
}

/*
===================
清除浮动
* cc 作用于父模型
* c 作用于浮动模型后，父模型区域里
===================
*/
.cc{
	zoom:1;
}
.cc:after{
	content:'\20';
	display:block;
	height:0;
	clear:both;
	visibility: hidden;
}
.c{
	clear:both;
	height:0;
	font:0/0 Arial;
	overflow:hidden;
	width:0;
}

/*
===================
间距
===================
*/
.mb5{
	margin-bottom:5px !important;
}
.mb10{
	margin-bottom:10px !important;
}
.mb15{
	margin-bottom:15px !important;
}
.mb20{
	margin-bottom:20px !important;
}
.mr5{
	margin-right:5px !important;
}
.mr10{
	margin-right:10px !important;
}
.mr15{
	margin-right:15px !important;
}
.mr20{
	margin-right:20px !important;
}
.ml5{
	margin-left:5px !important;
}
.ml10{
	margin-left:10px !important;
}
.ml15{
	margin-left:15px !important;
}
.ml20{
	margin-left:20px !important;
}
.p5{
	padding:5px;
}
.p10{
	padding:10px;
}
.p15{
	padding:15px;
}
.p20{
	padding:20px;
}

/*
===================
基础样式
* b 加粗
* u 下划线
* i 斜体
* w 文本不换行
* tal 文本靠左显示
* tac 文本居中显示
* tar 文本靠右显示
* fl 左浮动
* fr 右浮动
* pr 相对定位
* cp 鼠标点击手势
* vt 表格内容靠上显示
===================
*/
.b{
	font-weight:700 !important;
}
.u{
	text-decoration:underline !important;
}
.i{
	font-style:italic !important;
}
.w{
	white-space:nowrap;
}
.tal{
	text-align:left !important;
}
.tac{
	text-align:center !important;
}
.tar{
	text-align:right !important;
}
.fl{
	float:left !important;
	display:inline;
}
.fr{
	float:right !important;
	display:inline;
}
.pr{
	position:relative;
}
.cp{
	cursor:pointer;
}
.vt{
	vertical-align:top;
}
.dn{
	display:none;
}

/*
===================
字体
* f10 小字体
* f12 正常字
* f14 大字
* f16 更大
* f24 标题类
* fn 文本去粗
===================
*/
.f10{
	font-size:10px;
}
.f12{
	font-size:12px !important;
}
.f14{
	font-size:14px;
}
.f16{
	font-size:16px;
}
.f24{
	font-size:24px;
	font-family:Arial, "Microsoft Yahei", Simsun;
}
.fn{
	font-weight:normal !important;
}

/*
===================
系统文本颜色
===================
*/
.red{
	color:#ff0000 !important;
}
.green{
	color:#008800 !important;
}
.org{
	color:#ff6600 !important;
}
.gray{
	color:#999999 !important;
}
.blue{
	color:#266aae !important;
}
.s6{
	color:#666;
}

/*
===================
灰色渐变背景
===================
*/
.user_group dt,
#error_tips h2,
.task_item_list .hd,
.medal_term .ct h6,
.app_info .hd,
.app_present .hd,
.app_updata .hd,
.sql_list dt{
	background:#f9f9f9;
	background-repeat: no-repeat;
	background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#ffffff), color-stop(25%, #ffffff), to(#f4f4f4));
	background-image: -webkit-linear-gradient(#ffffff, #ffffff 25%, #f4f4f4);
	background-image: -moz-linear-gradient(top, #ffffff, #ffffff 25%, #f4f4f4);
	background-image: -ms-linear-gradient(#ffffff, #ffffff 25%, #f4f4f4);
	background-image: -o-linear-gradient(#ffffff, #ffffff 25%, #f4f4f4);
	background-image: linear-gradient(#ffffff, #ffffff 25%, #f4f4f4);
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#f4f4f4', GradientType=0);
	border-bottom:1px solid #dfdfdf;
}

/*
===================
按钮【可在input、button、a使用，可自由组合】
>>	btn									默认按钮
>>	btn btn_big					大按钮
>>	btn btn_error				错误按钮
>>	btn btn_success			确认按钮
>>	btn btn_submit			提交按钮
===================
*/
button::-moz-focus-inner, 
input::-moz-focus-inner {
  border: 0;
  padding: 0;
}
.btn {
	color: #333;
	background:#e6e6e6 url(../images/admin/content/btn.png);
	border: 1px solid #c4c4c4;
	border-radius: 2px;
	text-shadow: 0 1px 1px rgba(255, 255, 255, 0.75);
	padding:4px 10px;
	display: inline-block;
	cursor: pointer;
	font-size:100%;
	line-height: normal;
	text-decoration:none;
	overflow:visible;
	vertical-align: middle;
	text-align:center;
	zoom: 1;
	white-space:nowrap;
	font-family:inherit;
	_position:relative;
	margin:0;
}
a.btn{
	*padding:5px 10px 2px !important;
}
input.btn,
button.btn{
	*padding: 4px 10px 1px !important;
}
.btn em{
	font-size:10px;
	font-style:normal;
	padding-left:2px;
	font-family:Arial;
	vertical-align:1px;
}
.btn .add{
	width:9px;
	height:9px;
	background:url(../images/admin/content/btn_add.png) center center no-repeat;
	display:inline-block;
	vertical-align:middle;
	margin:-3px 5px 0 -3px;
	line-height:9px;
}
/*悬停*/
.btn:hover {
	background-position: 0 -40px;
	color: #333;
	text-decoration: none;
}
/*点击后*/
.btn:active {
	background-position:0 -81px;
}
/*大按钮*/
.btn_big {
	font-size: 1.2em;
	line-height: normal;
	padding: 7px 18px;
	border-radius: 2px;
}
input.btn_big,
button.btn_big{
	*padding: 6px 18px 3px !important;
}
/*其他按钮文字颜色*/
.btn_error,
.btn_error:hover,
.btn_success,
.btn_success:hover,
.btn_submit,
.btn_submit:hover{
  color: #ffffff !important;
}
/*提交按钮*/
.btn_submit{
	background-position:0 -120px;
	background-color: #1b75b6;
	text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
	border-color: #106bab #106bab #0d68a9;
}
.btn_submit:hover{
	background-position:0 -160px;
}
.btn_submit:active{
	background-position:0 -201px;
}
/*确认按钮*/
.btn_success{
	background-color: #89bf00;
	background-position:0 -240px;
	text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
	border-color: #6bad01 #6bad01 #63a100;
}
.btn_success:hover{
	background-position:0 -280px;
}
.btn_success:active{
	background-position:0 -321px;
}
/*错误按钮*/
.btn_error{
  background-color: #f29307;
	background-position:0 -360px;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  border-color: #e77c0e #e77c0e #dd7204;
}
.btn_error:hover{
	background-position:0 -400px;
}
.btn_error:active{
	background-position:0 -441px;
}
/*失效*/
input.disabled,
input.disabled:hover,
textarea.disabled,
textarea.disabled:hover,
select.disabled,
button.disabled,
button.disabled:hover{
	border-color: #ccc !important;
	background: #e9e9e9 !important;
	box-shadow: none !important;
	color: #999 !important;
	cursor: not-allowed;
	text-shadow: 1px 1px 0 #fff !important;
}

/*
===================
表单样式
===================
*/
.placeholder{
	color:#999;
}
.input, 
textarea, 
select {
	padding:4px 4px;
	font-size: 100%;
	line-height:18px;
	border:1px solid #ccc;
	background-color:#fff;
	box-shadow:2px 2px 2px #f0f0f0 inset;
	/*border-radius: 1px;*/
	vertical-align: middle;
  margin: 0;
	font-family: inherit;
}
.input:focus,textarea:focus{
	outline:2px solid #99d8f5;
	border-color:#7bbff2;
	background-color:#fffbde;
}
.input_hd,.textarea_hd{
	background:#fff url(../images/admin/content/input_hd.png) 0 bottom repeat-x;
}
.input{
	height:18px;
}
input.radio,
input[type="radio"],
input.checkbox,
input[type="checkbox"]{
	vertical-align:-3px;
	padding:0;
	margin-right:3px;
	*marign-rignt:0;
}
textarea{
	height:72px;
  overflow: auto;
  vertical-align: top;
	resize: vertical;
}
select, 
input[type="file"] {
	height: 28px;
	line-height: 28px;
}
select[size]{
	height:auto;
}
.form_success{
	border-color:#3e973e #87c787 #87c787 !important;
	outline:2px solid #87c787;
}
.form_error{
	border-color:#bc5050 #f2a6a6 #f2a6a6 !important;
	outline:2px solid #f2a6a6;
}

/*
===================
输入框文本框长度
===================
*/
.length_0{
	width:20px;
}
.length_1{
	width:50px;
}
.length_2{
	width:110px;
}
.length_3{
	width:170px;
}
.length_4{
	width:230px;
}
.length_5{
	width:290px;
}
.length_6{
	width:350px;
}

/*
===================
选择select专属长度
===================
*/
.select_1{
	width:60px;
}
.select_2{
	width:120px;
}
.select_3{
	width:180px;
}
.select_4{
	width:240px;
}
.select_5{
	width:300px;
}
.select_6{
	width:360px;
}

/*
===================
必填
===================
*/
.must_red{
	font-family:Simsun;
	color:#ff0000;
	margin-top:3px;
	margin-left:-13px; 
	position:absolute;
	font-size:14px;
}

/*
===================
分页
>>	pages_pre		上一页
>>	pages_next	下一页
>>	strong			激活状态
>>	a:hover			鼠标悬停状态
===================
*/
.pages a,
.pages strong{
	display:inline-block;
	line-height:28px;
	padding:0 10px;
	border:1px solid #d9d9d9;
	background-color:#ffffff; 
	text-decoration:none;
	color:#666;
	font-family:Simsun;
	-webkit-transition: all 0.2s ease-out;transition: all 0.2s ease-out;
	margin-right:3px;
}
.pages a:hover{
	color:#fff;
	background-color:#24557d;
	border-color:#fff;
	text-decoration:none;
}
.pages strong{
	color:#fff;
	background-color:#24557d;
	border-color:#fff;
}
.pages span{
	padding:0 10px;
	line-height:23px;
}

/*
===================
载入提示
* loading 载入中
* loading_error 载入错误
===================
*/
.loading{
	border:1px solid #e4e4e4;
	background:#fffde3 url(../images/admin/content/loading.gif) 10px center no-repeat;
	line-height:27px;
	padding:0 15px 0 30px;
	border-radius: 2px;
	-webkit-box-shadow: 0 0 5px #e4e4e4;
	box-shadow: 0 0 5px #e4e4e4;
	color:#222;
	font-weight:700;
	font-size:12px;
	display:inline-block;
}
.simpleloading{
	background:url(../images/admin/content/loading.gif) 2px 2px no-repeat transparent;
	width: 16px;
	padding: 2px;
	height: 16px;
	display:inline-block;
	vertical-align: middle;
}
.loading_error{
	background:#ffe3e3;
	padding-left:15px;
}

/*
===================
操作提示
===================
*/
.tips,
.tips_block span{
	line-height:25px;
	text-align:center;
	padding:0 10px;
}
.tips{
	border:1px solid #f4d5ba;
	background:#ffffe9;
}
.tips_error,
.tips_success,
.tips_loading{
	color:#cc3333;
	padding-left:18px;
	background:url(../images/admin/tips/tips_follow.png) -20px 2px no-repeat;
	display:inline-block;
	line-height:18px;
}
.tips_loading{
	background:url(../images/admin/content/loading.gif) 0 center no-repeat;
	color:#333333;
}
.tips_success{
	color:#008800;
	background-position:0 -19px;
}
.tips_block .tips_error{
	background:#cc3333;
	color:#fff;
	display:block;
	padding:0;
	margin:0;
}
.tips_block .tips_success{
	background:#008800;
	color:#fff;
	display:block;
	padding:0;
	margin:0;
}
.tips_light{
	padding:5px 10px 5px 28px;
	line-height:25px;
	border:1px solid #f4d5ba;
	background:#ffffe9 url(../images/admin/tips/light_mini.png) 8px 10px no-repeat;
	margin-bottom:10px;
}
/*气泡弹出提示*/
.tips_bubble{
	z-index:1;
	position:absolute;
	margin-top:45px;
	line-height:1.5;
	color:#666;
	padding:10px 10px;
	border:1px solid #f4d1a5;
	background-color:#fffbe7;
	width:180px;
	margin-left:30px;
}
/*下箭头*/
.tips_bubble .core_arrow_bottom{
	position:absolute;
	bottom:9px;
	right:10px;
	padding:0 20px;
}
.tips_bubble .core_arrow_bottom span,
.tips_bubble .core_arrow_bottom em{
	position:absolute;
	width:0;
	height:0;
	padding:0;
	margin:9px 0 0 2px;
	border-bottom:8px transparent dashed;
	border-right:8px transparent dashed;
	border-left:8px transparent dashed;
	overflow:hidden;
}
.tips_bubble .core_arrow_bottom span{
	border-top:8px #fffbe7 solid;
}
.tips_bubble .core_arrow_bottom em{
	border-top:8px #f4d1a5 solid;
	margin-top:10px;
}

/*
===================
全局弹窗
*pop_top 头
	**strong 标题
	**pop_close 关闭
*pop_cont 中
	**pop_table 表格
*pop_bottom 尾
*pop_tips 提示
*pop_loading 加载状态
===================
*/
.core_pop_wrap{
	background-color: #fff;
	border: 1px solid #ccc;
	box-shadow:0 0 5px rgba(0,0,0,0.2);
	border-radius: 3px;
	position:fixed;
	_position:absolute;/*ie6*/
	z-index:10;
	color:#333;
	outline:none;
}
.core_pop_wrap a{
	color:#336699;
}
.pop_top{
	line-height:18px;
	padding:9px 0 8px;
	border-top:1px solid #fff;
	border-bottom:1px solid #e7e7e7;
	background:#f6f6f6;
	zoom:1;
	margin-bottom:5px;
}
.pop_top:after{
	content:'\20';
	display:block;
	height:0;
	clear:both;
	visibility: hidden;
	width:1px;
}
.pop_top strong{
	text-indent:15px;
	font-size:14px;
	color:#333;
	font-weight:700;
	white-space:nowrap;
	margin-right:10px;
	float:left;
}
.pop_top select{
	float:left;
	padding:1px;
	line-height:22px;
	height:22px;
}
.pop_top ul{
	border-bottom:1px solid #ccc;
	height:25px;
}
.pop_top li{
	float:left;
	display:block;
	line-height:25px;
	height:25px;
	padding:0 15px;
	cursor:pointer;
}
.pop_top li.current{
	float:left;
	border:1px solid #ccc;
	background:#fff;
	border-bottom:0 none;
}
.pop_top .pop_close{
	margin-right:15px;
}
.pop_bottom{
	padding:10px 15px;
	border-top:1px solid #e9e9e9;
	height:30px;
	/*white-space:nowrap;*/
}
.pop_bottom label{
	display:inline-block;
	padding-top:3px;
}
.pop_bottom .btn{
	padding-left:20px;
	padding-right:20px;
}
.pop_bottom .tips_error,
.pop_bottom .tips_success{
	max-width:200px;
	float:left;
}
.founder_pop .pop_bottom .tips_error{
	width:150px;
}
.pop_cont{
	background:#fff;
	color:#333;
	padding:10px 15px 10px;
}
.pop_table{
	height:365px;
	overflow-y:auto;
	position:relative;
}
.pop_table th,
.pop_table td{
	padding:6px 0 6px;
}
.pop_table th{
	height:26px;
	line-height:26px;
	vertical-align:top;
	white-space:nowrap;
	padding-right:20px;
}
.pop_table tr:hover th{
	color:#000;
}
.pop_tips{
	background:#f7f7f7;
	line-height:24px;
	padding:0 10px;
	margin:0 10px 0;
	color:#666;
}
/*关闭*/
.pop_close{
	margin-top:5px;
	float:right;
	width:9px;
	height:8px;
	overflow:hidden;
	text-indent:-2000em;
	background:url(../images/admin/content/close.png) no-repeat;
	-webkit-transition: all 0.2s ease-out;
	margin-left:10px;
}
.pop_close:hover{
	background-position:0 -8px;
}
/*读取中*/
.pop_loading{
	background:url(../images/admin/content/loading.gif) center center no-repeat;
	height:80px;
	width:80px;
	margin:auto;
}

/*
===================
内容样式开始
===================
*/
body{
	font-family: "Microsoft Yahei", Arial;
	color:#333;
	font-size:12px;
	background:#f3f3f3;
	line-height:1.5;
	_width:98%;
	overflow-x:auto;
	overflow-y:auto;
}
body.body_none{
	background:#fff;
}
a{
	color:#266aae;
	text-decoration:none;
}
a:hover{
	text-decoration:underline;
}
/*
===================
全局
===================
*/
.wrap{
	padding:20px 20px 70px;
}
.wrap_pop{
	padding:20px;
}
/*
===================
主选项卡
===================
*/
.nav{
	margin-bottom:15px;
	min-height:35px;
}
.nav ul{
	height:35px;
	float:left;
}
.nav li,
.nav .return{
	float:left;
}
.nav li a,
.nav .return a{
	float:left;
	line-height:32px;
	color:#666;
	padding:0 15px;
	text-align:center;
}
.nav li a:hover{
	border-bottom:2px solid #aaa;
	color:#333;
	text-decoration:none;
}

.nav li.current a{
	border-bottom:2px solid #266aae;
	font-weight:700;
	color:#266aae;
}
.nav .return a{
	background:url(../images/admin/content/return.png) 2px center no-repeat;
	color:#990000;
	padding:0 20px;
}
/*
===================
校园选择组件
===================
*/
#branch-selector{float: left;}
#branch-selector .selected{padding:2px 30px 2px 12px;color:#fff;display: block; white-space:nowrap;background: url(../images/admin/dropdown.png) no-repeat right 6px #333;cursor:pointer;}
#branch-selector .selected:hover{background-position: right -94px; color:#F27B04;}
#branch-selector .list{position: absolute; left: 0; top: 22px; width: 280px;background:#333; padding: 10px; position: absolute;z-index: 999;}
#branch-selector .list em{position: absolute; right:4px; top: 2px;font-style:normal;color:#fff;cursor:pointer;}
#branch-selector .list a{text-decoration: none;color:#ddd;}
#branch-selector .list a{margin-right: 10px;line-height: 20px; display: inline-block; padding: 0 4px; border-radius: 2px; margin-bottom: 6px;}
#branch-selector .list a.current{background:#fff;color:#333;}
#branch-selector .list a:hover{color:#fff;background-color:#F27B04;}
#branch-selector .list a i.extra-number{margin-left: 2px;font-style: normal;background: #dedede;border-radius: 6px;padding: 0 3px;font-size: 10px;color: #333;}
#branch-selector .list a.current i.extra-number{color: #fff;background: #F27B04;}

/*
===================
二级选项卡
===================
*/
.nav_minor{
	margin-top:-5px;
	position:relative;
	margin-bottom:15px;
	height:24px;
	/*overflow:hidden;*/
}
.nav_minor li{
	float:left;
}
.nav_minor li a{
	float:left;
	line-height:24px;
	color:#666;
	padding:0 15px;
	text-align:center;
}
.nav_minor li a:hover{
	text-decoration:none;
	background:#e4e4e4;
}
.nav_minor li.current a{
	background:#ddd;
	color:#333;
	font-weight:700;
}
/*
===================
标题栏组
===================
*/
.h_a{
	padding:5px 9px;
	border-top:1px solid #ddd;
	border-bottom:1px solid #ffffff;
	color:#666666;
	font-weight:700;
	background:#e6e6e6;
	font-size:12px;
}
tr.h_a{
	background:#e6e6e6;
	padding:0;
}
tr.h_a th,
tr.h_a td
{
	padding-top:5px;
	padding-bottom:5px;
	line-height:18px;
	background:#e6e6e6 !important;
	border-top:1px solid #ddd;
	border-bottom:1px solid #ffffff;
}
/*
===================
数据表格列表
===================
*/
.table_list{
	border-top:1px solid #ebebeb;
	margin-bottom:10px;
}
/*.table_list tr:nth-child(odd) td,
.table_list tr:nth-child(odd) th {
	background-color: #f9f9f9;
}*/
.table_list .h_a{
	border-top:0 none;
}
.table_list td{
	padding:7px 10px 9px;
}
.table_list tr:hover td{
	color:#000;
	background-color:#f9f9f9;
}
.table_list thead tr td,
.table_list thead tr th,
.table_list thead tr:hover td{
	padding:5px 9px;
	background:#e6e6e6;
	border-top:1px solid #ddd;
	border-bottom:1px solid #ffffff;
	color:#666 !important;
}
.table_list thead tr td select{
	padding:1px;
	height:23px;
	line-height:23px;
}
.table_list thead label{
	white-space:nowrap;
}
.table_list .thumb{
	line-height:1;
	vertical-align:top;
	margin-right:10px;
	float:left;
}
.table_list tr.bgA td{
	background-color:#f0f8fc;
}
/*
===================
功能表格列表
===================
*/
.table_full{
	margin-bottom:10px;
}
.table_full table{
	table-layout:fixed;
}
.table_full tr,
.table_full tr:hover th,
.table_full tr:hover td,
.table_full th,
.table_purview tr,
.table_purview th,
.table_purview td,
.table_list td,
.nav,
.prompt_text,
.search_type,
.search_photo_list,
.design_ct dl,
.pop_design_tablelist td,
.pop_design_tablelist th,
.table_full tr.tr_checkbox td,
.search_list dl{
	background:url(../images/admin/content/hr.png) 0 bottom repeat-x;
}
.table_full th{
	padding:7px 10px 9px;
	background-color:#f8f8f8;
	border-right:1px solid #e5e3e3;
	font-weight:100;
	line-height:24px;
	vertical-align:top;
}
.table_full .th{
	width:160px;
}
.table_full th .s1{
	padding-left:5px;
}
.table_full td{
	padding:7px 10px 9px 15px;
	color:#666;
	vertical-align:top;
}
.table_full tr:hover th{
	background-color:#eef3f6;
}
.table_full tr:hover td{
	color:#000;
	background-color:#eef3f6;
}
.table_full tr .fun_tips{
	color:#999;
	line-height:24px;
}
.table_full tr:hover .fun_tips{
	color:#000;
}
.table_full tr.tr_checkbox th,
.table_full tr.tr_checkbox td{
	background-color:#fffaeb;
}
/*
===================
功能表格列表/交叉型
TODO:
===================
*/
.table_purview{
}
.table_purview th{
	padding:7px 10px 9px;
	font-weight:100;
	background-color:#f9f9f9;
}
.table_purview td{
	padding:5px 10px 7px 15px;
	border-left:1px solid #e5e3e3;
}
.table_purview tr:hover th,
.table_purview tr:hover td{
	background-color:#f9f9f9;
}
.table_purview  tr.hd_bar td{
	border:0 none;
	background-color:#f9f9f9;
}
.table_purview tr.hd_bar th{
	color:#000;
	font-weight:700;
	background-color:#f9f9f9;
}
.table_purview tr.hd td,.table_purview tr.hd th{
	background-color:#fffeee;
}
/*
===================
表格选中样式型
===================
*/
tr.high_light td,tr.high_light th{
	border-bottom:1px solid #ebdf99 !important;
	background-color:#fff8cf !important;
	background-image:url(../images/admin/content/high_light.png) !important;
	background-repeat:repeat-x !important;
}
/*
===================
三列列表
===================
*/
.three_list{}
.three_list li{
	float:left;
	width:33%;
	height:25px;
	line-height:25px;
	overflow:hidden;
}
.three_list li.all_check{
	width:100%;
	padding-left:2px;
}
/*
===================
双列列表
===================
*/
.double_list{}
.double_list li{
	float:left;
	width:49%;
	line-height:25px;
	height:25px;
	overflow:hidden;
}
.double_list li.all_check{
	width:100%;
}
.double_list li span{
	padding-left:20px;
	color:#666;
}
.double_list li label span{
	padding:inherit;
	color:#666;
}
/*
===================
单选组列表
===================
*/
.single_list li{
	line-height:25px;
	height:25px;
}
/*
===================
开关选项
===================
*/
.switch_list{
	height:25px;
	line-height:25px;
	overflow:hidden;
}
.switch_list li{
	float:left;
	width:120px;
}
/*
===================
全局选中高亮
===================
*/
input:checked+span{
	color:#008800 !important;
}
input:radio+span{
	color:#008800 !important;
}
/*
===================
用户组列表
===================
*/
.user_group{
	border:1px solid #ccc;
	background:#fff;
	/*width:685px;*/
	position:relative;
}
.user_group dl{
	border-top:1px solid #ccc;
	position:relative;
	margin-top:-1px;
}
.user_group dt{
	line-height:25px;
	height:25px;
	padding:0 5px;
	font-weight:700;
	color:#000;
}
.user_group dt input{
	_margin:3px 0 0 0;
	_padding:0;
}
.user_group dd{
	zoom:1;
	padding:5px 0;
	position:relative;
}
.user_group dd:after{
	content:'\20';
	display:block;
	height:0;
	clear:both;
	visibility: hidden;
}
.user_group dd label{
	display:inline;
	margin-bottom:1px;
	float:left;
	width:336px;
	margin-left:5px;
}
.user_group dd input{
	float:left;
	margin-top:4px;
	*margin-top:0;
}
.user_group dd span{
	/*display:block;*/
	padding:2px 0 2px 5px;
	color:#666;
}

/*
===================
数据组列表
===================
*/
.sql_list{
	border:1px solid #ccc;
	background:#fff;
	width:558px;
	position:relative;
}
.sql_list dl{
	border-top:1px solid #ccc;
	position:relative;
	margin-top:-1px;
}
.sql_list dt{
	line-height:25px;
	height:25px;
	padding:0 5px;
	font-weight:700;
	color:#000;
	color:#666;
}
.sql_list dd{
	height:300px;
	overflow:hidden;
	overflow-y:auto;
}
.sql_list dd p{
	border-bottom:1px solid #e4e4e4;
	padding:3px 5px;
	line-height:25px;
	height:25px;
	overflow:hidden;
}
.sql_list dd p:hover{
	background:#f7f7f7;
}
.sql_list span{
	float:left;
}
.sql_list .span_1{
	width:50px;
}
.sql_list .span_2{
	width:300px;
}
.sql_list .span_3{
	width:170px;
}


/*===================4月16日归类到这里,todo===================*/

/*
===================
任务条件组
===================
*/
.task_item_list{
	border:1px solid #ccc;
	background:#fff;
	width:358px;
	overflow:hidden;
}
.task_item_list .hd{
	height:25px;
}
.task_item_list .hd li{
	margin-left:-1px;
	border-left:1px solid #dfdfdf;
	float:left;
	width:50%;
	text-align:center;
}
.task_item_list .hd a{
	line-height:25px;
	font-weight:700;
	color:#000;
	display:block;
}
.task_item_list .hd a:hover{
	text-decoration:none
}
.task_item_list .hd .current a{
	background:#ffffff;
	height:26px;
}
.task_item_list .ct ul{
	padding:5px 0 10px 10px;
}
.task_item_list .ct li{
	float:left;
	width:33%;
	line-height:24px;
	height:24px;
	overflow:hidden;
}
/*
===================
列表横排操作组合
===================
*/
.cross{
	width:390px;
	overflow:hidden;
}
.cross ul{
	margin-left:-10px;
}
.cross li{
	line-height:30px;
	height:30px;
	padding:0 0 10px 0;
	overflow:hidden;
}
.cross img{
	vertical-align:middle;
}
.cross li span{
	margin-left:10px;
	float:left;
	height:30px;
}
.cross li .span_1{
	width:30px;
}
.cross li .span_2{
	width:80px;
}
.cross li .span_3{
	width:120px;
}
.cross li .span_4{
	width:180px;
}
/*
===================
图标 导航、版块管理
展开收起、所属
===================
*/
.start_icon,
.away_icon,
.zero_icon,
.plus_icon,
.plus_end_icon{
	background:url(../images/admin/content/icon_list.png) no-repeat;
	display:inline-block;
	vertical-align:middle;
}
.start_icon,
.away_icon,
.zero_icon,
.plus_none_icon{
	width:20px;
	height:20px;
	overflow:hidden;
	cursor:pointer;
}
.away_icon{
	background-position:0 -20px;
}
.zero_icon{
	background-position:-20px 0;
	cursor:default;
}
.plus_icon,
.plus_end_icon{
	width:40px;
	height:24px;
	background-position:0 -43px;
	margin-right:5px;
}
.plus_end_icon{
	background-position:0 -70px;
}
.plus_on_icon{
	background-position:0 -100px;
	cursor:default;
}
.plus_none_icon{
	background-position:999px 999px;
	cursor:default;
}
/*
===================
独立页面提交按钮框
===================
*/
.btn_wrap{
	position:fixed;
	bottom:0;
	left:0;
	width:100%;
	z-index:5;
	_position: absolute;
	_top: expression(documentElement . scrollTop +   documentElement . clientHeight-this . offsetHeight);
}
.btn_wrap_pd{
	padding:0 10px 10px;
}
.btn_wrap .btn_wrap_pd{
	padding:10px 20px 10px;
	background:#eaeaea url(../images/admin/content/btn_wrap.png) repeat-x;
	border-top:1px solid #d8d8d8;
}
.btn_wrap_pd .btn{
	min-width:80px;
	margin-right:10px;
	_width:100px;
}
.select_pages{
	float:right;
	line-height:26px;
	color:#999;
}
.select_pages a{
	font-size:14px;
}
.select_pages span{
	font-family:Simsun;
	color:#999;
	padding:0 10px 0;
}
/*
===================
属性转移 如：角色权限添加删除
===================
*/
.shift{}
.shift select{
	width:160px;
	padding:5px;
}
.shift_operate{
	padding:70px 10px 0;
}
.shift h4{
	margin-bottom:5px;
	font-size:12px;
}
.shift .btn{
	font-family:Simsun;
}
.shift .select_div{
	border:1px solid #ccc;
	background:#fff;
	height:180px;
	overflow-y:auto;
	width:158px;
	float:left;
	line-height:25px;
	text-indent:10px;
	padding-bottom:5px;
}
.shift .select_div dl{
	padding:5px 5px 0;
}
.shift .select_div dl dd{
	display:none;
}
.shift .select_div dl.current dd{
	display:block;
}
.shift .select_div dl dt{
	font-weight:700;
	background:#e9e9e9 url(../images/admin/content/down.png) 115px center no-repeat;
}
.shift .select_div dl.current dt{
	background:#e9e9e9 url(../images/admin/content/up.png) 115px center no-repeat;
}
.shift .select_div a{
	display:block;
	color:#666;
}
.shift .select_div a:hover,
.shift .select_div a.current{
	display:block;
	background:#e6e6e6;
	text-decoration:none;
}

/*
===================
大功能提示说明
===================
*/
.prompt_text{
	padding:10px;
	color:#666;
	margin-bottom:10px;
}
.prompt_text:hover{
	color:#000;
}
.prompt_text ol,
.prompt_text ul{
	padding:0 0 5px 2em;
	margin:0;
	line-height:1.5;
}
.prompt_text ul li{
	list-style: disc outside;
}
/*
===================
页面级提示
===================
*/
#error_tips{
	border:1px solid #d4d4d4;
	background:#fff;
	-webkit-box-shadow: #ccc 0 1px 5px;
	-moz-box-shadow: #ccc 0 1px 5px;
	-o-box-shadow:#ccc 0 1px 5px;
	box-shadow: #ccc 0 1px 5px;
	filter: progid: DXImageTransform.Microsoft.Shadow(Strength=3, Direction=180, Color='#ccc');
	width:500px;
	margin:50px auto;
}
#error_tips h2{
	font:bold 14px/40px Arial;
	height:40px;
	padding:0 20px;
	color:#666;
}
.error_cont{
	padding:20px 20px 30px 80px;
	background:url(../images/admin/tips/light.png) 20px 20px no-repeat;
	line-height:1.8;
}
.error_return{
	padding:10px 0 0 0;
}
/*
===================
搜索结构
===================
*/
.search_type{
	padding:10px;
}
.search_type li{
	_height:30px;
}
.search_type li label{
	display:inline-block;
	width:70px;
	line-height:24px;
}
.search_type li select{
	vertical-align:top;
}
.search_type .ul_wrap{
	width:100%;
	min-height:1px;
	zoom:1;
}
.search_type .ul_wrap:after{
	content:'\20';
	display:block;
	height:0;
	clear:both;
	visibility: hidden;
}
.search_type li{
	float:left;
	width:350px;
	margin-right:20px;
	min-height:30px;
	padding-bottom:5px;
}
.search_type li.two{
	width:700px;
}
.search_type .btn_side{
	text-align:center;
	padding-top:10px;
	padding-right:140px;
}
.search_type .btn{
}

/*
===================
链接型添加
===================
*/
.link_add{
	background:url(../images/admin/content/link_add.png) 3px 3px no-repeat;
	display:inline-block;
	padding-left:20px;
	color:#ff5500;
}
/*
===================
方位选择 例子：水印
===================
*/
.locate{
	border:1px solid #ccc;
	background:#fff;
	width:209px;
}
.locate ul{
	margin-top:-1px;
	margin-left:-1px;
}
.locate li{
	border-left:1px solid #ccc;
	border-top:1px solid #ccc;
	float:left;
	width:69px;
	height:40px;
	text-align:center;
}
.locate li a{
	color:#333;
	display:block;
	border:1px solid #fff;
	line-height:38px;
}
.locate li a:hover{
	text-decoration:none;
	background:#e4e4e4;
}
.locate li.current a{
	background:#266aae;
	color:#fff;
}
/*
===================
版块搜索下拉
===================
*/
.forum_search_pop{
	position:absolute;
	border:1px solid #ccc;
	border-bottom-width:2px;
	background:#fff;
	width:178px;
}
.forum_search_pop ul{
	border:1px solid #fff;
}
.forum_search_pop li{
	line-height:25px;
	height:25px;
	overflow:hidden;
	text-indent:15px;
}
.forum_search_pop li a{
	text-decoration:none;
	display:block;
	color:#333;
}
.forum_search_pop li.current a{
	background:#f0f0f0;
	color:#266aae;
}

/*
===================
地区库--路径
===================
*/
.yarnball {
	border-left:1px solid #d9d9d9;
	overflow:hidden;
	padding-right:10px;
	vertical-align:middle;
	display:inline-block;
	position:relative;
}
.yarnball ul {
	float:left;
	margin-left:-5px;
}
.yarnball ul li{
	/*border-top:1px solid #d9d9d9;*/
	/*border-bottom:1px solid #d9d9d9;*/
	float:left;
	/*width:90px;*/
}
.yarnball ul li a,
.yarnball ul li em{
	background:url(../images/admin/content/yarnball.png) top right no-repeat;
}
.yarnball ul li a{
	color: #666;
	display:block;
	line-height:30px;
	height:30px;
	overflow:hidden;
	text-decoration: none;
	text-align:center;
	padding:0 16px 0 12px;
}
.yarnball ul li a:hover{
	text-decoration:none;
}
.yarnball ul li em{
	width:9px;
	height:30px;
	display: block;
	/*position:absolute;*/
	background-position:right 0;
	margin-top:-30px;
	z-index:2;
	/*margin-left:90px;*/
}
.yarnball ul li.hover a{
	background-position:0 -30px;
	color:#333;
}
.yarnball ul li.hover em{
	background-position:right -30px;
}
.yarnball ul li.li_disabled a{
	background-position:0 -60px;
	color:#333;
	cursor:default;
}
.yarnball ul li.li_disabled em{
	background-position:right -60px;
}

/*
===================
代码复制
===================
*/
.textarea_code{
	border:1px solid #ccc;
	background:#fff;
	padding:2px 5px;
	font-family: "Consolas", "Bitstream Vera Sans Mono", "Courier New", Courier, monospace !important;
	line-height:1.8;
	width:280px;
	overflow:hidden;
}

/*
===================
友情链接分类
===================
*/
.cate_link span{
	display:inline-block;
	padding:0 5px;
	background:#ddd;
	margin:0 5px 5px 0;
}
.cate_link_down{
	margin-top:3px;
	cursor:pointer;
	background:url(../images/admin/content/select_down.png) 0 0 no-repeat;
	width:15px;
	height:15px;
	float:left;
	overflow:hidden;
	margin-right:10px;
}
.cate_link_down:hover{
	background-position:0 bottom;
}

/*
===================
全局下拉菜单
===================
*/
.core_menu{
	position:absolute;
}
.core_menu_list{
	border:1px solid #dddddd;
	background:#ffffff;
	width:118px;
	padding:5px;
	box-shadow: 3px 3px 5px #cccccc;
}
.core_menu_list li{
	float:left;
	width:100%;
	margin:0;
	padding:2px 5px;
	line-height:18px;
	height:18px;
}
.core_menu_list li a{
	display:block;
	text-indent:10px;
	color:#333333;
	border:0 none;
	float:left;
	width:100%;
	margin:0;
	border-radius: 0;
}
.core_menu_list li a:hover{
	border:0 none;
	background:#3366cc;
	color:#fff;
	text-decoration:none;
}

/*
===================
勋章颁发条件
===================
*/
.medal_term{
}
.medal_term .hd{
	margin-bottom:10px;
}
.medal_term .hd span{
	font-family:Simsun;
	margin:0 10px;
}
.medal_term .ct{
	border:1px solid #ccc;
	width:358px;
	border-bottom:0 none;
}
.medal_term .ct h6{
	height:24px;
	line-height:24px;
	text-align:center;
	font-size:12px;
}
.medal_term .ct dl{
	height:24px;
	line-height:24px;
	border-bottom:1px solid #ccc;
	overflow:hidden;
}
.medal_term .ct dt{
	float:left;
	width:80px;
	text-align:center;
}
.medal_term .ct dd{
	float:left;
	border-left:1px solid #ccc;
}
.medal_term .ct dd.num{
	width:100px;
	font-family:Simsun;
	text-align:center;
}
.medal_term .ct dd.title{
	width:160px;
	text-indent:10px;
}


/*
===================
单图上传
===================
*/
.single_image_up{
}
.single_image_up a{
	width:0;
	height:0;
	display:block;
	overflow:hidden;
	text-indent:-2000em;
}

/*
===================
单附件上传
===================
*/
.single_file_up{
	width:80px;
	height:20px;
	position:relative;
	margin-bottom:5px;
	overflow:hidden;
}
.single_file_up a{
	height:20px;
	width:80px; 
	display:block;
	line-height:20px;
	text-indent:25px;
	background:url(../images/admin/content/file_up.png) no-repeat;
	filter:alpha(opacity=80);
	-moz-opacity:0.8;
	opacity:0.8;
	text-decoration:none;
	color:#333;
}
.single_file_up:hover a{
	text-decoration:none;
	filter:alpha(opacity=100);
	-moz-opacity:1;
	opacity:1;
}
.single_file_up input,
.single_file_up object{
	width:80px;
	height:22px;
	position:absolute;
	top:0;
	right:0;
	background:none;
	filter:alpha(opacity=0);
	-moz-opacity:0;
	opacity:0;
	cursor:pointer;
	outline:none;
}

/*
===================
进度条
===================
*/
.progress_bar{
	border:1px solid #c6c6c6;
	background:#fff;
	height:9px;
	display:inline-block;
	width:100%;
	vertical-align:middle;
	position:relative;
}
.progress_bar span{
	background-color: #669201;
	border:1px solid #577d00;
	display:inline-block;
	height:9px;
	position:absolute;
	margin-top:-1px;
	left:-1px;
	font:0/0 Arial;
}

/*
===================
颜色筛选
===================
*/
.color_pick{
	border:1px solid #ccc;
	padding:2px 13px 2px 3px;
	background:#fff url(../images/admin/content/down.png) 40px center no-repeat;
	display:inline-block;
	cursor:pointer;
	height:20px;
	overflow:hidden;
	vertical-align:middle;
	position:relative;
	line-height:normal;
}
.color_pick:hover{
	background-color:#fffbde;
}
.color_pick.color_current{
	border-color:#aaa #aaa #555;
	background-color:#fffbde;
}
.color_pick em{
	height:20px;
	width:34px;
	display:inline-block;
	background:url(../images/admin/content/transparent.png);
}
.color_pick.color_big{
	background-image:none;
	width:80px;
	padding:3px;
	height:60px;
	text-align:center;
	color:#333;
}
.color_pick.color_big em{
	width:80px;
	height:38px;
	*margin-bottom:3px;
	text-align:center;
}
/*字体颜色选择组合*/
.color_pick_dom{
	width:70px;
	width:300px;
}
.color_pick_dom ul{
	height:29px;
	width:170px;
	padding:3px 0 0 0;
}
.color_pick_dom li{
	float:left;
	margin-right:10px;
	_margin-right:8px;
	white-space:nowrap;
}
.color_pick_dom li input{
	_margin:0 0 -1px -3px;
	_padding:0;
}
.color_pick_dom li.none{
	margin-right:0;
}
.color_pick_dom .color_pick{
	background-position:151px center;
}
.color_pick_dom .color_pick em{
	width:145px;
}
.color_pick_dom .case{
	float:right;
	width:100px;
	border:1px solid #ccc;
	background:#fff;
	padding:10px 10px;
}

/*
===================
地区库--弹窗内容
===================
*/
.pop_region_list{
	padding-bottom:10px;
}
.pop_region_list ul{
	padding-left:2px;
}
.pop_region_list ul li{
	float:left;
	line-height:20px;
}
.pop_region_list ul li a,
.pop_region_list ul li span{
	display:block;
	padding:0 5px;
	color:#333;
	white-space:nowrap;
	border-radius:2px;
}
.pop_region_list ul li a:hover{
	background:#e0e0e0;
	text-decoration:none;
}
.pop_region_list ul li.current a,
.pop_region_list ul li.current span{
	background:#266aae;
	color:#ffffff;
}
.pop_region_list .hr{
	background:#e4e4e4;
	height:1px;
	overflow:hidden;
	font:0/0 Arial;
	clear:both;
	margin:10px 0;
}
.pop_region_list .filter{
	padding:10px 0;
}
.pop_region_list .filter a{
	margin-right:12px;
}
.pop_region_list .filter a.current{
	color:#333;
	font-weight:700;
}
.pop_region_list .list{
	border:1px solid #ccc;
	height:108px;
	overflow-x:hidden;
	overflow-y:auto;
}
.pop_region_list .list ul{
	padding:5px;
}
.pop_region_list .list li{
	float:left;
	width:33%;
	cursor:pointer;
	text-indent:5px;
}
.pop_region_list .list li:hover{
	background:#f7f7f7;
}

/*
===================
seo弹窗
===================
*/
.pop_seo{
	border:1px solid #ccc;
	background:#fffeee;
	padding:9px;
	width:280px;
	position:absolute;
}
.pop_seo .close{
	float:right;
	width:9px;
	height:8px;
	overflow:hidden;
	text-indent:-2000em;
	background:url(../images/admin/content/close.png) no-repeat;
	-webkit-transition: all 0.2s ease-out;
	margin:3px 3px 0 10px;
}
.pop_seo .close:hover{
	background-position:0 -8px;
}
.pop_seo .hd{
	margin-bottom:5px;
	color:#000;
}
.pop_seo .ct a{
	display:inline-block;
	line-height:25px;
	margin-right:20px;
}


/*
===================
条款协议
===================
*/
.agreements{
	padding:10px 0;
}
.agreements .pre{
	border:1px solid #ccc;
	background:#fff;
	width:500px;
	height:200px;
	overflow-x:hidden;
	overflow-y:scroll;
	padding:15px;
	margin-bottom:10px;
	line-height:1.7;
}
.agreements p{
	margin-bottom:1em;
	text-indent:2em;
	color:#666;
}
.agreements h1{
	font-size:18px;
	text-align:center;
	margin-bottom:15px;
}
.agreements h2,
.agreements h3,
.agreements h4{
	margin-bottom:1em;
}
.agreements ol,
.agreements ul{
	margin-left:1em;
	margin-bottom:1em;
}

/*
===================
排序箭头
===================
*/
.sort_down,
.sort_down_old,
.sort_up,
.sort_up_old{
	background:url(../images/admin/appcenter/arrow.png) right -37px no-repeat;
	display:inline-block;
	padding-right:24px;
	cursor:pointer;
}
.sort_down_old{
	background-position:right 3px;
}
.sort_up{
	background-position:right -117px;
}
.sort_up_old{
	background-position:right -77px;
}

/*
===================
应用-基本信息
===================
*/
.app_info{
	border:1px solid #e4e4e4;
	background:#fff;
	box-shadow:0 1px 5px rgba(0,0,0,0.1);
	margin-bottom:20px;
}
.app_info .hd{
	padding:8px 15px;
}
.app_info .ct{
	padding:8px 15px 20px;
}
.app_info h1{
	font-size:24px;
	font-family:"Microsoft Yahei";
	font-weight:400;
	margin-bottom:5px;
}
.app_info ul{
	padding-bottom:10px;
}
.app_info li{
	line-height:25px;
	float:left;
	width:45%;
}
.app_info li em{
	display:inline-block;
	width:80px;
	color:#666;
}
.app_info li.li{
	width:100%;
}
/*
===================
应用介绍
===================
*/
.app_present{
	border:1px solid #e4e4e4;
	background:#fff;
	box-shadow:0 1px 5px rgba(0,0,0,0.1);
	margin-bottom:20px;
}
.app_present .hd{
	padding:8px 15px;
}
.app_present .ct{
	padding:8px 15px 20px;
}
/*
===================
应用截图
===================
*/
.app_thumb li{
	float:left;
	margin-right:20px;
	margin-top:20px;
}
.app_thumb li img{
	vertical-align:top;
	height:266px;
	width:200px;
}
/*
===================
应用-更新历史
===================
*/
.app_updata{
	border:1px solid #e4e4e4;
	background:#fff;
	box-shadow:0 1px 5px rgba(0,0,0,0.1);
	margin-bottom:20px;
}
.app_updata .hd{
	padding:8px 15px;
}
.app_updata .ct{
	padding:8px 15px 20px;
}
.app_updata .time{
	font-weight:700;
	margin-bottom:5px;
}
.app_updata .version{
	margin-bottom:5px;
}

/*
===================
站长统计
===================
*/
.pop_stats{
	width:350px;
}
.pop_stats table td,
.pop_stats table th{
	padding:5px 10px;
	border:1px solid #ccc;
}
.pop_stats thead td,
.pop_stats thead th{
	background:#f7f7f7;
}
.pop_stats thead th{
	width:120px;
}

/*
===================
多图上传
===================
*/
.widget_upload_photos{
	padding-bottom:5px;
}
.widget_upload_photos li{
	position:relative;
	float:left;
	width:50px;
	height:50px;
	border:1px solid #ccc;
	background:#f7f7f7;
	overflow:hidden;
	margin-right:5px;
	line-height:50px;
	text-align:center;
}
.widget_upload_photos li input{
	height:50px;
	border:0;
	padding:0;
	margin:0;
	width:50px;
	position:absolute;
	left:0;
	top:0;
	filter:alpha(opacity=00);
	-moz-opacity:0;
	opacity:0; 
}

/*
===================
历史版本
===================
*/
.widget_history_file{
	border:1px solid #ccc;
	background:#fffeee;
	margin:0 auto 15px;
}
.widget_history_file dt{
	float:left;
	width:70px;
	padding:5px 10px;
}
.widget_history_file dd{
	float:left;
	padding:5px 10px;
	border-left:1px solid #ccc;
}
.widget_history_file dd.num{
	width:60px;
}
.widget_history_file dd.time{
	width:80px;
}
.widget_history_file dd.name a{
	padding-left:20px;
	background:url(../images/admin/content/zip.png) 0 center no-repeat;
	display:inline-block;
}

/*
===================
更新日志
===================
*/
.widget_update_log h3{
	font-size:12px;
	margin-bottom:5px;
}
.widget_update_log table{
	border:1px solid #ccc;
}
.widget_update_log td,
.widget_update_log th{
	border:1px solid #ccc;
	padding:5px 10px;
}
.widget_update_log thead th,
.widget_update_log thead td{
	background:#f7f7f7;
}

/*
===================
弹窗-高级搜索
===================
*/
.pop_advanced_search{
	width:450px;
}
.pop_advanced_search .pop_cont{
	overflow:hidden;
}
.pop_advanced_search ul{
	margin-left:-4%;
}
.pop_advanced_search li{
	float:left;
	width:46%;
	margin-left:4%;
	height:60px;
}
.pop_advanced_search li.all{
	width:100%;
}
.pop_advanced_search p{
	padding-bottom:3px;
}
.pop_advanced_search .gap{
	display:inline-block;
	width:42px;
	text-align:center;
}

/*
===================
左选右变-用户组管理权限下置顶和提前
===================
*/
.about_list{
	border-top:1px dashed #ccc;
	margin-top:10px;
}
.about_list dl{
	padding-top:10px;
}
.about_list dt{
	float:left;
	width:80px;
	line-height:26px;
}
.about_list dd{
	overflow:hidden;
	line-height:26px;
}
.about_list dd p{
	color:#999;
}
.about_list label{
	display:inline-block;
	width:100px;
	color:#666;
}
.about_list:hover label{
	color:#333;
}

/*
===================
邮件示例效果
===================
*/
.email_example{
	border:1px solid #ccc;
	background:#fff;
	padding:10px 15px;
	color:#333;
	width:268px;
	line-height:1.8;
}

/*
===================
变量样例
===================
*/
.variable_sample{
	border-top:1px solid #e4e4e4;
	border-left:1px solid #e4e4e4;
	border-right:1px solid #e4e4e4;
	background:#fff;
	float:left;
}
.variable_sample li{
	border-bottom:1px solid #e4e4e4;
	line-height:25px;
	height:25px;
}
.variable_sample li span{
	float:left;
	width:200px;
	font-family:"Courier New", Courier, monospace;
	border-left:1px solid #e4e4e4;
	padding-left:10px;
	color:#333;
}
.variable_sample li em{
	padding-left:10px;
	float:left;
	width:200px;
	background:#f7fbff;
}

/*
===================
照片搜索结果
===================
*/
.search_photo_list{
	padding:20px 0 0;
}
.search_photo_list li{
	float:left;
	width:90px;
	height:90px;
	padding:4px;
	border:1px solid #ccc;
	background:#fff;
	position:relative;
	display:inline;
	margin:0 20px 20px 0;
}
.search_photo_list li label{
	display:block;
}
.search_photo_list li input{
	position:absolute;
	right:0;
	bottom:0;
}

/*
===================
门户调用编辑
===================
*/
.design_ct dl{
	padding:7px 0;
}
.design_ct dt{
	float:left;
	width:120px;
	line-height:26px;
	padding:0 10px;
}
.design_ct dd{
	margin-left:150px;
}
.design_ct dd .three_list{
	width:360px;
}
.design_ct .pop_design_code{
	padding:10px;
}
.design_ct .pop_design_tablelist{
}

/*
===================
模块模板
===================
*/
/*左侧*/
.pop_design_code{
	width:440px;
	float:left;
}
.pop_design_code textarea{
	width:430px;
	height:280px;
	font-family:"Courier New", Courier, monospace;
	margin-bottom:5px;
}
/*右侧*/
.pop_design_case{
	float:left;
	width:180px;
	height:350px;
	overflow-x:hidden;
	overflow-y:auto;
	padding:10px 0;
}
.pop_design_case .thbg{
	background:#fff;
}
/*高帅富编辑效果外框*/
.CodeMirror {
	margin-bottom: 10px;
}

/*
===================
表格排序列表
===================
*/
.pop_design_tablelist{
	width:100%;
	table-layout:fixed;
	border-top:1px solid #e4e4e4;
	margin-bottom:10px;
}
.pop_design_tablelist thead td,
.pop_design_tablelist thead th{
	background-color:#f7f7f7;
}
.pop_design_tablelist thead td select{
	padding:1px;
	line-height:22px;
	height:22px;
}
.pop_design_tablelist td,
.pop_design_tablelist th{
	padding:7px 0;
}
.pop_design_tablelist th{
	padding-left:10px;
}
.pop_design_tablelist th input{
	margin:0;
	padding:0;
}
.pop_design_tablelist .subject{
	white-space:nowrap;
	text-overflow:ellipsis;
	line-height:18px;
	height:18px;
	overflow:hidden;
}


/*
===================
非操作型提示
>>	作用与操作成功等，自动关闭效果的弹窗
===================
*/
.pop_showmsg_wrap:focus{
	outline:0 none;
}
.pop_showmsg_wrap,
.pop_showmsg{
	background:#f6fbfe url(../images/admin/tips/pop_showmsg.png) no-repeat;
}
.pop_showmsg_wrap{
	position:fixed;
	_position:absolute;
	z-index:11;
	height:55px;
	padding-right:9px;
	background-position:right 0;
	border-radius: 8px;
	box-shadow:0 0 10px #e1e1e1;
}
.pop_showmsg{
	height:55px;
	font-size:14px;
	background-position:left 0;
	border-top-left-radius:8px;
	border-bottom-left-radius:8px;
	display:inline-block;
}
.pop_showmsg span{
	padding:10px 10px 10px 68px;
	display:inline-block;
	line-height:36px;
	height:35px;
	text-shadow: 0 1px 1px #eee;
	color:#333;
}
.pop_showmsg span.success, .pop_showmsg span.tip-success{
	background:url(../images/admin/tips/success.gif) 20px center no-repeat;
}
.pop_showmsg span.warning, .pop_showmsg span.tip-warning{
	background:url(../images/admin/tips/warning.gif) 20px center no-repeat;
}


/*
===================
模板列表
===================
*/
.design_page{
	padding-bottom:10px;
	width:800px;
}
.design_page li{
	float:left;
	margin-right:23px;
	display:inline;
	box-shadow:0 0 1px rgba(0,0,0,0.1);
	background:#fff;
	margin-bottom:20px;
	border:1px solid;
	border-color:#ecebeb #e1e0e0 #d5d5d5 #e1e0e0;
	width:230px;
	height:310px;
	position:relative;
}
.design_page .img{
	display:block;
	padding:10px;
}
.design_page li img{
	display:block;
}
.design_page li .ft{
	position:absolute;
	left:0;
	right:0;
	bottom:0;
	width:100%;
	background:#f8f8f8;
	border-top:1px solid #eeeeee;
	padding:5px 0;
	border-bottom:1px solid #fff;
}
.design_page li .ft .org{
	padding:0 0 0 10px;
}
.design_page li .ft a{
	color:#666;
	margin:0 0 0 10px;
}
.design_page li .title{
	padding:0 10px 0;
	font-size:14px;
	line-height:18px;
	height:18px;
	overflow:hidden;
	margin-bottom:3px;
	white-space:nowrap;
	text-overflow:ellipsis;
	-ms-text-overflow:ellipsis;
	word-wrap:normal;
}
.design_page li .descrip{
	padding:0 10px 3px;
	color:#999;
	line-height:18px;
	height:18px;
	overflow:hidden;
	white-space:nowrap;
	text-overflow:ellipsis;
	-ms-text-overflow:ellipsis;
	word-wrap:normal;
} 
.design_page li .type{
	padding:0 10px 8px;
	color:#999;
}
.design_page li .type span{
	margin-right:10px;
}

/*
===================
首页重要提醒
===================
*/
.home_tips{
	border:1px solid #f0d9af;
	background:#fdf8e4;
	color:#666;
	padding:10px 15px;
	margin-bottom:15px;
}
.home_tips h4{
	font-size:12px;
	font-weight:700;
	color:#af8133;
	margin-bottom:10px;
}

/*
===================
首页系统信息
===================
*/
.home_info{
	padding:5px 10px 20px;
	color:#666;
}
.home_info li{
	line-height:25px;
	zoom:1;
}
.home_info li:after{
	content:'\20';
	display:block;
	height:0;
	clear:both;
	visibility: hidden;
}
.home_info li em{
	float:left;
	width:100px;
	font-style:normal;
}
.home_info li span{
	display:block;
	overflow:hidden;
}

/*
===================
应用安装进度条
===================
*/
.install_schedule_bg,
.install_schedule,
.install_schedule span{
	background:url(../images/admin/content/install_schedule.png) no-repeat;
}
.install_schedule_bg{
	height:15px;
	width:202px;
	padding:0 4px;
	margin:5px 0;
}
.install_schedule{
	background-position:right -17px;
	height:15px;
}
.install_schedule span{
	background-position:0 -17px;
	display:block;
	width:5px;
	height:15px;
}
.install_load{
	line-height:18px;
}

/*
===================
搜索结果展示
===================
*/
.search_list{
}
.search_list h2{
	font-size:14px;
	padding:10px;
}
.search_list dl{
	padding:0 2em 10px;
	margin:0 0 10px;
}
.search_list dt{
	font-size:14px;
}

/*
===================
复制设置 下箭头 （横向扩展）
===================
*/
.pop_expand{
	bottom:50px;
	position:fixed;
	left:120px;
	width:440px;
	z-index:6;
}
.pop_expand .core_arrow_bottom{
	position:absolute;
	bottom:9px;
	left:25px;
	padding:0 25px;
}
.pop_expand .core_arrow_bottom span,
.pop_expand .core_arrow_bottom em{
	position:absolute;
	width:0;
	height:0;
	padding:0;
	margin:9px 0 0 2px;
	border-bottom:8px transparent dashed;
	border-right:8px transparent dashed;
	border-left:8px transparent dashed;
	overflow:hidden;
}
.pop_expand .core_arrow_bottom span{
	border-top:8px #ffffff solid;
}
.pop_expand .core_arrow_bottom em{
	border-top:8px #c1c1c1 solid;
	margin-top:10px;
}

/*
===================
在线升级 进度
===================
*/
.stepstat .current {
	color: #009900;
}
.stepstat li {
	color: #CCCCCC;
	float: left;
	margin-right: 15px;
}

/*
===================
系统更新提示
===================
*/
.system_update{
	border:1px solid #d3d3c8;
	background:#ffffee;
	padding:8px 20px;
	position:absolute;
	width:500px;
	text-align:center;
	left:50%;
	margin-left:-250px;
	top:10px;
}
.system_update a{
	color:#266AAE;
	margin:0 0 0 20px;
}
.system_update a:hover{
	text-decoration:underline;
}

/*全局应用图标效果*/
.app_icon{
	display:inline-block;
	vertical-align:top;
	width:80px;
	height:80px;
	position:relative;
	background:#f7f7f7;
}
.app_icon b{
	position:absolute;
	left:0;
	top:0;
	width:80px;
	height:80px;
	background:url(../images/admin/content/app_bg.png) no-repeat;
	_background:none;
}

/*
===================
在线更新
===================
*/
.upgrade_page{
	width:800px;
}

/*
===================
无内容提示
===================
*/
.not_content_mini{
	border:1px solid #e4e4e4;
	background:#fff;
	color:#666;
	text-align:center;
	padding:20px;
	margin-bottom:20px;
}
.not_content_mini i{
	width:16px;
	height:16px;
	display:inline-block;
	vertical-align:middle;
	margin-right:10px;
	background:url(../images/admin/content/small.png) 0 -60px no-repeat;
}
.not_content_mini i.success{
	background-position:-40px -20px;
}

div.form span.required
{
	color: red;
}

div.form div.error label,
div.form label.error,
div.form span.error
{
	color: #C00;
}

div.form div.error input,
div.form div.error textarea,
div.form div.error select,
div.form input.error,
div.form textarea.error,
div.form select.error
{
	background: #FEE;
	border-color: #C00;
}



/***********  pager ************/
/**
 * CSS styles for CLinkPager.
 *
 * <AUTHOR> Xue <<EMAIL>>
 * @link http://www.yiiframework.com/
 * @copyright 2008-2010 Yii Software LLC
 * @license http://www.yiiframework.com/license/
 * @since 1.0
 */

ul.yiiPager
{
	font-size:11px;
	border:0;
	margin:0;
	padding:0;
	line-height:100%;
	display:inline;
}

ul.yiiPager li
{
	display:inline;
}

ul.yiiPager a:link,
ul.yiiPager a:visited
{
	border:solid 1px #9aafe5;
	font-weight:bold;
	color:#0e509e;
	padding:1px 6px;
	text-decoration:none;
}

ul.yiiPager .page a
{
	font-weight:normal;
}

ul.yiiPager a:hover
{
	border:solid 1px #0e509e;
}

ul.yiiPager .selected a
{
	background:#2e6ab1;
	color:#FFFFFF;
	font-weight:bold;
}

ul.yiiPager .hidden a
{
	border:solid 1px #DEDEDE;
	color:#888888;
}

/**
 * Hide first and last buttons by default.
 */
ul.yiiPager .first,
ul.yiiPager .last
{
	display:none;
}
.correct_span,
.error_span{
	display:block;
	float:left;
	width:20px;
	height:16px;
	text-indent:-2000em;
	overflow:hidden;
	background:url(../images/icons/icon.png) no-repeat;
	margin-right:5px;
}
.error_span{
	background-position:0 -23px;
}

/*
===================
数据表写入
===================
*/
.displayResult{
/*	box-shadow:5px 5px 5px #f7f7f7 inset;
	border:1px solid #bdbcbc;
	width:520px;*/
	height:320px;
	padding:10px;
	overflow:hidden;
	display:block;
	overflow-y:scroll;
	margin:0px auto;
	font-size:12px;
	outline:none;
}
.displayResult ul{
	line-height:1.8;
}