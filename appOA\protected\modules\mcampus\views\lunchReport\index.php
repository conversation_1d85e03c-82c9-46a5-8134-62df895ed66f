<?php
    //查询方式
    $type = $this->getType();
    $defaultType = $type[$t];
    //时间
    $time = time();
    $startDate = date('Y-m-d',  mktime(0,0,0,date('m',$time),1, date('Y',$time)));
    $endDate = date('Y-m-d',  mktime(0,0,0,date('m',$time),date('t',$time), date('Y',$time)));
?>
<style>
    .ui-datepicker{
        z-index:3 !important
    }
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Campus Support'), array('//mcampus/default/index'))?></li>
        <li class="active"><?php echo Yii::t('site', 'Lunch Report');?></li>
    </ol>
    <div class="panel panel-default">
        <div class="panel-heading"><?php echo Yii::t('lunch', 'Lunch report'); ?></div>
        <div class="panel-body">
            <div class="well">
                <div class="row">
                    <?php echo CHtml::form($this->createUrl('searchData'), 'post',array('class'=>' J_ajaxForm')) ?>
                    <div class="col-sm-12 form-inline flex">
                            <div class="btn-group mr12">
                                <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><?php echo $defaultType;?><span class="caret ml5"></span></button>
                                <ul class="dropdown-menu">
                                  <li><a href="<?php echo $this->createUrl('//mcampus/lunchReport/index',array('t'=>'day'));?>"><?php echo Yii::t('lunch', 'Search by date'); ?></a></li>
                                  <li><a href="<?php echo $this->createUrl('//mcampus/lunchReport/index',array('t'=>'month'));?>"><?php echo Yii::t('lunch', 'Search by month'); ?></a></li>
                                </ul>
                            </div>
                            <div class="btn-group">
                            <?php $this->widget('zii.widgets.jui.CJuiDatePicker',array(
                                        'name'=>'startdate',
                                        'value'=>$startDate,
                                        'options'=>array(
                                            'dateFormat'=>'yy-mm-dd',
                                        ),
                                        'htmlOptions'=>array(
                                            'class'=>'form-control ',
                                            'placeholder'=> ($t === 'day') ? '选择日期': '开始日期',
                                        ),
                            ));?>
                            <?php if ($t === 'month'):?>
                                <?php $this->widget('zii.widgets.jui.CJuiDatePicker',array(
                                            'name'=>'enddate',
                                            'value'=>$endDate,
                                            'options'=>array(
                                                'dateFormat'=>'yy-mm-dd',
                                            ),
                                            'htmlOptions'=>array(
                                                'class'=>'form-control ',
                                                'placeholder'=>'结束日期',
                                            ),
                                ));?>
                            <?php endif;?>

                            </div>
                            <div class='ml12'>
                                <label class="radio-inline ">
                                    <input type="radio" name="byType" value='HAVELUNCH' checked="checked"><?php echo Yii::t('lunch', 'Lunch'); ?>
                                </label>
                                <label class="radio-inline ">
                                    <input type="radio" name="byType" value="CANCELLUNCH"><?php echo Yii::t('lunch', 'Canceled lunch'); ?>
                                </label>
                                <?php echo CHtml::hiddenField('t', $t);?>
                                <button type="button" class="btn btn-primary J_ajax_submit_btn ml12"><?php echo Yii::t('lunch', 'Search'); ?></button>
                            </div>
                            <!-- <label class="radio-inline">
                                <input type="radio" name="byType" value='HAVELUNCH' checked="checked"> <?php echo Yii::t('lunch', 'Lunch'); ?>
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="inlineRadioOptions" id="inlineRadio2" value="option2"> <?php echo Yii::t('lunch', 'Canceled lunch'); ?>
                            </label> -->
                    </div>
                    <?php echo CHtml::endForm();?>
                </div>
            </div>
            <div class="row">
                <div class='rowData'></div>
                <div class="col-sm-2" id="lunch-count">

                </div>
                <div class="col-lg-10" id="lunch-list">

                </div>
            </div>
        </div>
    </div>
</div>
<?php
    $this->branchSelectParams['extraUrlArray'] = array('//mcampus/lunchReport/index');
    $this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script type="text/template" id="lunch-count-template">
    <div class="list-group">
        <a href="javascript:void(0);" class="list-group-item active" data-class="all" onclick="showClass(this);">
            <span class="badge"><%=sum%></span>
            <?php echo Yii::t('lunch','全部班级');?>
        </a>
        <%_.each(classList,function(val,key){%>
            <a href="javascript:void(0);" class="list-group-item" data-class="<%=val.classid%>" onclick="showClass(this);">
                <span class="badge"><%=val.sum%></span>
                <%=val.title%>
            </a>
        <%})%>
    </div>
</script>
<script type="text/template" id="lunch-list-template">
    <div class="panel panel-default">
        <div class="panel-heading">
          <h3 class="panel-title"><%=title%><span class="badge pull-right"><%=sum%></span></h3>
        </div>
        <div class="panel-body" data-classid="<%=classid%>">

        </div>
    </div>
</script>

<script type="text/template" id="day-child-list-template">
    <div class="col-sm-2 mb10"><span class="glyphicon glyphicon-user mr5"></span><a href="/child/index/index?childid=<%= childid %>" target="_blank"><%=childName%></a></div>
</script>

<script type="text/template" id="month-child-list-template">
    <div class="col-sm-12">
        <p>
            <span class="glyphicon glyphicon-user mr5"></span><a href="/child/index/index?childid=<%= childid %>" target="_blank"><%=childName%></a><hr style="margin:0;">
        </p>
        <div class="row" data-child="<%=childid%>">

        </div>
        <p style="height:10px;"></p>
    </div>
</script>

<script type="text/template" id="lunch-info-template">
    <div class="col-sm-1"><%=lunch%></div>
</script>

<script>
$(document).ready(function(){
    var lunchCountTemplate = _.template($('#lunch-count-template').html());
    var lunchListTemplate = _.template($('#lunch-list-template').html());
    var dayChildListTemplate = _.template($('#day-child-list-template').html());
    var monthChildListTemplate = _.template($('#month-child-list-template').html());
    var lunchInfoTemplate = _.template($('#lunch-info-template').html());
    showData = function(data){
        $("#lunch-count").empty();
        $("#lunch-list").empty();
        console.log(data);
        if(data.state === 'success'){
            if (data.sum==0){
                $(".rowData").html('<div class="col-sm-12 color3 font14 text-center mt20 mb20"><?php echo Yii::t('ptc', 'No Data') ?></div>');
            }else{
                $(".rowData").html('');
                $("#lunch-count").append(lunchCountTemplate({sum:data.sum,classList:_.values(data.classList)}));
                appendClass(_.values(data.classList));
                if (data.t === 'day'){
                    dayShowChildren(data.childInfo);
                }else{
                    monthShowChildren(_.values(data.childInfo));
                    showLunch(data.lunchInfo);
                }
            }
        }else{
            resultTip({msg: data.message, error: 'warning'});
        }
    }

    //追加班级
    appendClass = function(data){
        _.each(data,function(classInfo){
            var lt = lunchListTemplate(classInfo);
            $("#lunch-list").append(lt);
        })
    }

    //显示孩子
    dayShowChildren = function(data){
         _.each(data,function(childInfo){
            var div = $("#lunch-list").find('div[data-classid|="'+childInfo.classid+'"]');
            var lt = dayChildListTemplate({childName:childInfo.childName,childid:childInfo.childid});
            div.append(lt);
        })
    }

    //显示班级
    showClass = function(_this){
        //菜单选中
        $('.list-group a').each(function(){
            $(this).removeClass('active');
        });
        $(_this).addClass('active');
        var classId = $(_this).data('class');

        //循环隐藏
        $('#lunch-list>div').each(function(){
            $(this).addClass('hide');
        });
        if (classId === 'all'){
            $('#lunch-list>div').each(function(){
                $(this).removeClass('hide');
            });
        }else{
            $('#lunch-list').find('div[data-classid|="'+classId+'"]').parent().removeClass('hide');
        }

    }

    //月显示孩子
    monthShowChildren = function(data){
        _.each(data,function(childInfo){
            var div = $("#lunch-list").find('div[data-classid|="'+childInfo.classid+'"]');
            var lt = monthChildListTemplate(childInfo);
            div.append(lt);
        })
    }

    showLunch = function(data){
        _.each(data,function(lunchInfo){
            var div = $("#lunch-list").find('div[data-child|="'+lunchInfo.childid+'"]');
            var lt = lunchInfoTemplate(lunchInfo);
            div.append(lt);
        })
    }
})
</script>

