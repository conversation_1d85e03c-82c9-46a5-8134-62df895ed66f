<?php
class StaffSearchHandy extends CWidget
{
	public $inputCSS = 'length_5';
	public $name = 'searchStaff';
	public $popPosition = array('my'=>"left top", 'at'=>"left bottom", 'collision'=>"none");
	public $simpleDisplay = false;
    public $displayLabel = null;
    public $restrictSchool = '';
	
	public function run(){
        if($this->displayLabel === null){
            $this->displayLabel = Yii::t('navs','Staff Search');
        }
		$this->render('staffsearchhandy');
	}
}