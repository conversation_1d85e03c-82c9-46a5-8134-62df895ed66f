<div class="shortCuts">
    <div id="quick_max">
        <h5><?php echo Yii::t("global","Quick Links");?><em id="quick-link-min" title="<?php echo Yii::t("global","Hide Quick Links");?>"></em></h5>
		<div class="sep1"></div>
		<div class="sep2"></div>
        <?php $this->widget('zii.widgets.CMenu',array(
            //'itemTemplate'=>'{menu}<em></em>',
            //'htmlOptions'=>array(
                //'class'=>"sf-menu",
                //),
            //'activateParents'=>true,
            'id'=>'shortCuts',
            'items'=>Mims::getShortCuts(),
        )); ?>
    </div>
    <div id="quick_min" title="<?php echo Yii::t("global","Show Quick Links");?>"></div>
</div>
<script>
    function posl()
    {
        var offset=$('#mainbox').offset();
		_left = offset.left + 7;
        $('.shortCuts').css('left', _left+'px');
    }
    posl();
    $(window).resize(function(){
        posl();
    })
    
    $('#quick-link-min').click(function(){
        $('#quick_max').hide();
        $('#quick_min').show();
        $.cookie('quick-link', '0', {path:'/'});
    });
    $('#quick_min').click(function(){
        $(this).hide();
        $('#quick_max').show();
        $.cookie('quick-link', '1', {path:'/'});
    });
    var fc = $.cookie('quick-link')
    if (fc == 0){
        $('#quick-link-min').click();
    }
    else if(fc == 1){
        $('#quick-link').click();
    }
</script>

<style>
.shortCuts .sep1{
	height: 0;
	border:0;
	margin: 0 4px 0 3px;
	border-bottom: 1px solid #fff;
}
.shortCuts .sep2{
	height: 0;
	border:0;
	margin: 0 3px 0 4px;
	border-bottom: 1px solid #BBBB92;
}
.shortCuts h5{
	margin: 0;
	line-height: 28px;
}
.shortCuts ul{
	padding: 10px;
}
</style>