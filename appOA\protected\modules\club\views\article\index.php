<div class="search_type cc mb10"></div>
<div class="mb10">
    <a href="<?php echo $this->createUrl("/club/article/create");?>" class="btn"><span class="add"></span><?php echo Yii::t('club', '添加文章');?></a>
</div>
<?php 
$this->widget('ext.ivyCGridView.IvyCGridView', array(
	'id'=>'club-article-grid',
	'dataProvider'=>$model->search(),
	'filter'=>$model,
    'colgroups'=>array(array('colwidth'=>array(20,50))),
	'columns'=>array(
         array(
            'class' => 'CCheckBoxColumn',
            'id' => "article_id",
            'value' => '$data->id',
            'selectableRows' => 2,
            'checkBoxHtmlOptions' => array(
                'name' => 'article_id[]',
            ),
        ),
		array(
            'name'=>'id',
        ),
        array(
            'name'=>'branch_id',
            'value'=>'$data->branch->abb',
        ),
        'title',
        array(
            'name'=>'category',
            'value' => 'ClubArticle::getClubCategory($data->category)',
            'type' => 'raw',
            'filter' => CHtml::activeDropDownList($model, "category", array('' => Yii::t('club', '全部')) + ClubArticle::getClubCategory()),
        ),
		array(
            'name'=>'author_email',
            'value'=>'$data->user->email',
            'filter'=>false,
        ),
        array(
            'name'=>'language',
            'value'=>'ClubArticle::getClubLanguage($data->language)',
            'type' => 'raw',
            'filter' => CHtml::activeDropDownList($model, "language", array('' => Yii::t('club', '全部')) + ClubArticle::getClubLanguage()),
        ),
        array(
            'name'=>'status',
            'value'=>'ClubArticle::getClubStatus($data->status)',
            'type' => 'raw',
            'filter' => CHtml::activeDropDownList($model, "status", array('' => Yii::t('club', '全部')) + ClubArticle::getClubStatus()),
        ),
        array(
            'name'=>'privacy',
            'value'=>'ClubArticle::getClubPrivacy($data->privacy)',
            'type' => 'raw',
            'filter' => CHtml::activeDropDownList($model, "privacy", array('' => Yii::t('club', '全部')) + ClubArticle::getClubPrivacy()),
        ),
		array(
            'class' => 'CButtonColumn',
            'template' => '{update} {delete}',
            'updateButtonUrl' => 'Yii::app()->createUrl("/club/article/create", array("id" => $data->id))',
            //'updateButtonOptions' => array('target' => '_blank'),
        ),
	),
    'template' => "{items}{summary}{pager}"
)); ?>

<?php if ( !empty($model->search()->data) )
    echo CHtml::button(Yii::t('club', '批量生成缓存'),array('onclick'=>'generateCache();return false;'));
?>

<script>
function generateCache(){
     var article_id_all = $("#club-article-grid .items input[type=checkbox]").not('#article_id_all').serialize();
     if (article_id_all == ""){
        resultTip({error:true, msg: '必须选择一遍文章！'});
     }else{
        $.ajax({
            type: 'GET',
            url: '<?php echo $this->createUrl('/club/article/generateCache?');?>'+article_id_all,
            dataType: 'json'
    }).done(function(data){
        if (data == true){
            resultTip({error:false, msg: '缓存生成成功！'});
        }else{
            resultTip({error:false, msg: '缓存生成失败！'});
        }
    });
     }
}
</script>
