<?php
/**
 * 配置模块调用
 * 配置权限分为两种，oGeneralConfigAccess 为浏览权限; oGeneralConfigAdmin 为管理权限； 本Controller下原则上只有这两种权限
 * 如果不是用这两种权限，在actionIndex 中调用XXX方法时，需要传入自定义权限的名称
 * 在具体的XXX方法中， 用permOptions=>array('checkAccess'=>true,'checkAdmin'=>true)来配置是否需要进行权限检查
 *
 * 请注意，1. 完成一个配置功能时需要将权限配置到权限脚本的配置文件中
 *  oGeneralConfigAccess; oGeneralConfigAdmin
 */
class DefaultController extends ProtectedController
{
    public $dialogWidth = 400;
	public $Perm = array(
		'access'=>'oGeneralConfigAccess',
		'admin' =>'oGeneralConfigAdmin'
	);
	
	public $mainMenu;
	
	public function actionIndex($target='', $op='')
	{
	
//		$this->mainMenu = array(
//			array(
//				'label' => '积分兑换配置',
//				'url' => array('//settings/default/index', 'target'=>'PointsConfig')
//			),
//			array(
//				'label' => '管理多校园',
//				'url' => array('//settings/default/index', 'target'=>'MultBranch')
//			),
//		);	
	
		$func = ucfirst(strtolower($target)) . ucfirst(strtolower($op));
		if(method_exists($this, $func)){
		
			//如果不用默认权限，需要指定权限名称
			//$Perm['access'] = 'o' . $func . 'Access';
			//$Perm['admin']  = 'o' . $func . 'Admin';
			$Perm = null;
			
			$this->$func($Perm);
		}else{
			$this->render('index');
		}
	}
		
	/**
	 * oPointsConfigAccess, oPointConfigAdmin;
	 * @param	Object	$perms	Description
	 * @return	Object			Description
	 */
	public function PointsConfig( $perms = null ){
        Yii::import('common.models.points.*');
		$permOptions = array(
			'checkAccess' => true,
			'checkAdmin' => true,
		);
		if(empty($perms)) $perms = $this->Perm;

		if($permOptions['checkAccess'] && !empty($perms['access'])){
			if( ! Yii::app()->user->checkAccess($perms['access'])){
				throw new CException("Permission denied");
			}
		}
		
				
        if (Yii::app()->request->isAjaxRequest){
			if($permOptions['checkAdmin'] && !empty($perms['admin'])){
				if( ! Yii::app()->user->checkAccess($perms['admin'])){
					echo CJSON::encode(array('ret'=>0, 'msg'=>'No permission'));
					Yii::app()->end();
				}
			}
			
            $id     = Yii::app()->request->getPost('id', array());
            $toggle = Yii::app()->request->getPost('toggle', array());
            $maxn   = Yii::app()->request->getPost('maxn', array());
            
            if ($id){
                foreach ($id as $skey=>$tog){
                    if ($tog){
                        $model = PointsConfig::model()->findByPk($skey);
                        if ($model === null){
                            $model = new PointsConfig;
                        }
                        
                        $model->schoolid = $skey;
                        $model->status = isset($toggle[$skey]) ? 1 : 0;
                        $model->maximum = isset($maxn[$skey]) ? $maxn[$skey] : 0;
                         if ($model->save()){
                             $this->addMessage('state', 'success');
                             $this->addMessage('message', Yii::t('message','Data saved!'));
                         }
                         else {
                             $this->addMessage('fail', 'success');
                             $this->addMessage('message', Yii::t('message','Saving Failed!'));
                         }
                    }
                }
            }
            $this->showMessage();
        }
        
		$criteria = new CDbCriteria;
        $criteria->compare('t.type', 20);
        $criteria->compare('t.status', 10);
        $criteria->order='t.abb asc';
        $schools = Branch::model()->with('points')->findAll($criteria);
		$this->render('points/admin',array(
            'schools'=>$schools,
		));
	}
	
	public function MultBranch()
	{
        $mainMenu = array();
        foreach (OA::getMultiAdmType() as $mKey=>$mult){
            $mainMenu[] = array(
                'label' => $mult,
                'url' => array('//settings/default/index','target'=>'multBranch', 'type'=>$mKey),
            );
        }
        $this->mainMenu = $mainMenu;
	
		$permOptions = array(
			'checkAccess' => true,
			'checkAdmin' => true,
		);
		if(empty($perms)) $perms = $this->Perm;
		if($permOptions['checkAccess'] && !empty($perms['access'])){
			if( ! Yii::app()->user->checkAccess($perms['access'])){
				throw new CException("Permission denied");
			}
		}
	 	
		$type = Yii::app()->request->getParam("type", "pd");
		
		$criteria = new CDbCriteria;
        if ($type != 'edu')
            $criteria->compare('t.admtype', 1);
		$criteria->compare('t.isstaff', 1);
		$criteria->addCondition('t.level>0 and t.rank<>7');
		$userList = User::model()->with(array(
					'multBranchWithParam'=>array('params'=>array(':type'=>$type))
				)
			)->findAll($criteria);
		$allBranch = $this->getAllBranch();;
		foreach($allBranch as $bid => $b){
			$branchList[$bid] = $b['title'];
		}
		$this->render('multbranch/admin',array('userList'=>$userList,'branchList'=>$branchList, 'type'=>$type));
	}
    
    public function actionAddMultBranch($type)
    {
        $permOptions = array(
			'checkAccess' => true,
			'checkAdmin' => true,
		);
        
        $model = new AdmBranchLink;
        $model->type = $type;
        
        if (isset($_POST['AdmBranchLink'])){
            AdmBranchLink::model()->deleteAll('uid=:useid and type=:type',array(':useid'=>$_POST['AdmBranchLink']['uid'], ':type'=>$_POST['AdmBranchLink']['type']));
            foreach ($_POST['AdmBranchLink']['schoolid'] as $schoolid){
                $model = new AdmBranchLink;
                $model->attributes = $_POST['AdmBranchLink'];
                $model->userid = Yii::app()->user->id;
                $model->schoolid = $schoolid;
                $model->save();
            }
            if ($type != 'edu')
                User::model()->updateByPk($model->uid, array('admtype'=>1));
            $this->addMessage('state', 'success');
            $this->addMessage('message', '成功');
            $this->addMessage('refresh', true);
            $this->addMessage('referer', $this->createUrl('/settings/default/index', array('target'=>'multBranch', 'type'=>$model->type)));
            $this->showMessage();
        }
        
        $criteria = new CDbCriteria;
        $criteria->compare('type', 20);
        $criteria->order = 'branchid asc';
        $branch = Branch::model()->findAll($criteria);
        
        $this->render('multbranch/addbranch', array('model'=>$model, 'branch'=>$branch));
    }
    
    public function actionSchoolslist($id=0, $type='')
    {
        $permOptions = array(
			'checkAccess' => true,
			'checkAdmin' => true,
		);
		if(empty($perms)) $perms = $this->Perm;
		if($permOptions['checkAccess'] && !empty($perms['access'])){
			if( ! Yii::app()->user->checkAccess($perms['access'])){
				throw new CException("Permission denied");
			}
		}
        
        if (Yii::app()->request->isPostRequest){
            if($permOptions['checkAdmin'] && !empty($perms['admin'])){
				if( ! Yii::app()->user->checkAccess($perms['admin'])){
					echo CJSON::encode(array('ret'=>0, 'msg'=>'No permission'));
					Yii::app()->end();
				}
			}
            
            $school   = Yii::app()->request->getPost('school', array());
            
            AdmBranchLink::model()->deleteAll('uid=:useid and type=:type',array(':useid'=>$id, ':type'=>$type));
            
            $_data = '';
            foreach($school as $sid=>$_tv){
                $admBranch = new AdmBranchLink;
                $admBranch->uid = $id;
                $admBranch->type = $type;
                $admBranch->schoolid = $sid;
                $admBranch->save();
                $_data .= '<span>'.$_tv.'</span>&nbsp;&nbsp;&nbsp;&nbsp;';
            }
            
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message','Data saved!'));
            $this->addMessage('data', CJSON::encode(array('uid'=>$id, '_data'=>$_data)));
            $this->addMessage('callback', 'callback');
            $this->showMessage();
        }
        
        $this->layout='//layouts/dialog';
        $allBranch = $this->getAllBranch();
        
        $criteria = new CDbCriteria;
        $criteria->compare('uid', $id);
        $criteria->compare('type', $type);
        $criteria->index='schoolid';
        $adbs = AdmBranchLink::model()->findAll($criteria);
        
        foreach($allBranch as $bid => $b){
			$branchList[$bid] = array(
                'title' => $b['title'],
                'selected' => isset( $adbs[$bid] ) ? 1 : 0,
            );
		}
        
        $this->render('multbranch/schoolslist', array('branchList'=>$branchList));
    }
    
    public function actionDeleteMult()
    {
        $type   = Yii::app()->request->getParam('type', '');
        $uid    = Yii::app()->request->getParam('uid', 0);
        
        if ($uid){
            if (AdmBranchLink::model()->deleteAll('uid=:useid and type=:type',array(':useid'=>$uid, ':type'=>$type))) {
                $criteria = new CDbCriteria;
                $criteria->compare('uid', $uid);
                $count = AdmBranchLink::model()->count($criteria);
                if (!$count){
                    User::model()->updateByPk($uid, array('admtype'=>0));
                }
                $this->addMessage('state', 'success');
            }
            else {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '失败');
            }
            $this->showMessage();
        }
    }
    
    public function diglossia()
	{
        $criteria = new CDbCriteria;
        $criteria->order='category_sign asc';
        $items = DiglossiaCategory::model()->with('diglossia')->findAll($criteria);
		$this->render('diglossia/admin',array('items'=>$items));
	}
    
    public function editDiglossia()
	{
        $this->layout = '//layouts/dialog';
        
        $type   = Yii::app()->request->getParam('type', '');
        $id     = Yii::app()->request->getParam('id', 0);
        $catid  = Yii::app()->request->getParam('catid', 0);
        
        if ($type == 1){
            $model = DiglossiaCategory::model()->findByPk($id);
            if ($model === null)
                $model = new DiglossiaCategory;
            
            if (isset($_POST['DiglossiaCategory'])){
                $model->attributes = $_POST['DiglossiaCategory'];
                if ($model->save()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('refresh', true);
                    $this->addMessage('message', '成功');
                }
                else{
                    $this->addMessage('state', 'fail');
                    $err = current($model->getErrors());
                    $this->addMessage('message', $err?$err[0]:'失败');
                }
                $this->showMessage();
            }
        }
        elseif($type == 2){
            $model = Term::model()->findByPk($id);
            if ($model === null)
                $model = new Term;
            if ($catid)
                $model->category_id=$catid;
            if (isset($_POST['Term'])){
                $model->attributes = $_POST['Term'];
                if ($model->save()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('refresh', true);
                    $this->addMessage('message', '成功');
                }
                else{
                    $this->addMessage('state', 'fail');
                    $err = current($model->getErrors());
                    $this->addMessage('message', $err?$err[0]:'失败');
                }
                $this->showMessage();
            }
        }
		$this->render('diglossia/edit',array('model'=>$model, 'type'=>$type, 'catid'=>$catid));
	}
    
    public function delDiglossia()
    {
        $type   = Yii::app()->request->getParam('type', '');
        $id     = Yii::app()->request->getParam('id', 0);
        
        if ($type == 1){
            $criteria = new CDbCriteria();
            $criteria->compare('category_id', $id);
            $count = Term::model()->count($criteria);
            if (!$count){
                $model = DiglossiaCategory::model()->findByPk($id);
            }
            else {
                $model = null;
            }
        }
        elseif ($type == 2) {
            $model = Term::model()->findByPk($id);
        }
        
        if ($model !== null){
            $model->delete();
            $this->addMessage('state', 'success');
            $this->addMessage('message', '成功');
        }
        else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '失败');
        }
        $this->showMessage();
    }
	
}