<?php
class TeachingContent extends CWidget{
	public $classId;
    public $yId;
    public $weekNum;
    public $schoolid;
    public $month;
    public $type;

    public function init(){
        $cs = Yii::app()->clientScript;
        $cs->registerCssFile(Yii::app()->theme->baseUrl.'/widgets/lunch/lunch.css?t='.Yii::app()->params['refreshAssets']);
        $cs->registerCssFile(Yii::app()->theme->baseUrl.'/css/widgetbase.css?t='.Yii::app()->params['refreshAssets']);
    }

	public function run(){
        Yii::import('common.models.calendar.CalendarWeek');
        Yii::import('common.models.portfolio.TeachingMonthly');

        $lKey = Yii::app()->language == 'zh_cn' ? 'cn' : 'en';


        $criteria = new CDbCriteria();
        $criteria->compare('yid', $this->yId);
        $criteria->compare('branchid', $this->schoolid);
        $criteria->compare('is_selected', 1);
        $calendarSchool = CalendarSchool::model()->find($criteria);
        $pmonth = "";
        $nmonth = "";
        if($calendarSchool->startyear >= 2021){
            $this->month = $this->month ? $this->month : 1;
            $months = TeachingMonthly::configType();
            $criteria = new CDbCriteria();
            $criteria->compare('classid', $this->classId);
            $criteria->compare('yid', $this->yId);
            $criteria->compare('unit_type', $this->month);
//        $criteria->compare('stat_'.$lKey, 20);
            $model = TeachingMonthly::model()->with('content')->find($criteria);
        }else {
            $weeks = array();
            $criteria = new CDbCriteria();
            $criteria->compare('yid', $this->yId);
            $criteria->order = 'weeknumber asc';
            $items = CalendarWeek::model()->findAll($criteria);
            foreach ($items as $item) {
                $weeks[$item->weeknumber] = $item->monday_timestamp;
            }

            $months = array();
//            foreach ($weeks as $week) {
//                $_month = date('Y-m', $week);
//                $months[$_month] = $_month;
//            }

            $months = TeachingMonthly::configType();

            $this->month = $this->weekNum ? date('Y-m', $weeks[$this->weekNum]) : $this->month;

            $criteria = new CDbCriteria();
            $criteria->compare('classid', $this->classId);
            $criteria->compare('yid', $this->yId);
            //$criteria->compare('month', $this->month);
            $criteria->compare('unit_type', $this->month);

//        $criteria->compare('stat_'.$lKey, 20);
            $model = TeachingMonthly::model()->with('content')->find($criteria);

            $indexMonths = array_values($months);
            $_indexMonths = array_flip($indexMonths);
            $pmonth = isset($indexMonths[$_indexMonths[$this->month] - 1]) ? $indexMonths[$_indexMonths[$this->month] - 1] : reset($indexMonths);
            $nmonth = isset($indexMonths[$_indexMonths[$this->month] + 1]) ? $indexMonths[$_indexMonths[$this->month] + 1] : end($indexMonths);
        }

        if (is_object($model) && is_object($model->content)) {
            $model->content->expVista();
        }

        $this->render('teachingcontent', array('model'=>$model, 'months'=>$months, 'pmonth'=>$pmonth, 'nmonth'=>$nmonth));
    }
	
}