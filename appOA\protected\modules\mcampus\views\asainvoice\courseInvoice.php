<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
    <h4 class="modal-title" id="exampleModalLabel"><?php echo $model->getTitle() ?></h4>
</div>

<div class="modal-body">
    <?php if(!$childidList): ?>
        <h4 class="text-center">无相关孩子</h4>
    <?php else: ?>
        <table class="table table-bordered">
            <tr>
                <th>序号</th>
                <th>学生</th>
                <th>父亲手机</th>
                <th>母亲手机</th>
                <th>账单</th>
            </tr>
        <?php 
            $num=0;
            if($childidList){
                foreach ($childidList['childid'] as $itemid=>$childid) {
                    $num++;
                    $child = $childList[$childid];
                    $parents = $child->getParents();
                    echo '<tr>';
                    echo '<td>'.$num.'</td>';
                    echo '<td>' . $child->getChildName() . '</td>';
                    // echo '<td>' . ($parents['father'] ? $parents['father']->getName() .'<br>'.$parents['father']->parent->mphone : '') . '</td>';
                    // echo '<td>' . ($parents['mother'] ? $parents['mother']->getName() .'<br>'.$parents['mother']->parent->mphone : '') . '</td>';                   
                    echo '<td>' . ($parents['father'] ? $parents['father']->parent->mphone : '') . '</td>';
                    echo '<td>' . ($parents['mother'] ? $parents['mother']->parent->mphone : '') . '</td>';
                    echo '<td>' . ($childidList['status'][$itemid] == AsaInvoice::STATS_PAID ? '已支付' : '未支付') . '</td>';
                    echo '</tr>';
                }
            } 
        ?>
        </table>
    <?php endif; ?>
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
</div>