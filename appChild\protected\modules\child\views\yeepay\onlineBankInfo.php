<?php

function prepareBanKInfo($yeepayBankInfo) {
    $bankList = array();

    foreach ($yeepayBankInfo as $key => $value) {
        $title = $value['en_name'] . ' ' . $value['cn_name'];
        $bankList[$key] = CHtml::image(
                        Yii::app()->getThemeManager()->getBaseUrl() . '/base/images/bankLogo4Yeepay/' . $value['logoname'], $title, array("title" => $title)
        );
    }

    return $bankList;
}

if (true === $yeepayAvailable) {
    $usedBefore = empty($usedBank) ? false : true;
    ?>
    <link rel="stylesheet" type="text/css" href="<?php echo Yii::app()->theme->baseUrl . '/css/paying.css?t=' . Yii::app()->params['refreshAssets']; ?>"/>

    <div id="usedbankinfo" class="bankinfo">
        <?php
        $faq = sprintf('<span class="faq"><em></em><span>%s</span></span>', Yii::t('payment', 'Online Banking FAQ'));
        echo CHtml::openTag("div", array("class" => "alignRight fr"));
        echo CHtml::link($faq, array("//child/yeepay/onlineNotice"), array("class" => "colorbox"));
        echo CHtml::closeTag("div");

        Yii::import('application.components.CGroupRadioButtonList');

        $allBankList = prepareBanKInfo($yeepayBankInfo);
        $usedBankList = prepareBanKInfo($usedBank);

        $bankName = array_keys($usedBankList);
        $selected = array_shift($bankName);

        echo CHtml::openTag("h2");
        echo Yii::t("payment", "Bank List");
        echo CHtml::closeTag("h2");
        if ($usedBefore) {
            echo CHtml::openTag("h3");
            echo Yii::t("payment", 'Favorite');
            echo CHtml::closeTag("h3");
            echo CHtml::openTag("div", array("class" => "bank-list"));
            echo MCHtml::groupRadioButtonList(
                    "yeeBankId", $selected, $usedBankList, 0, array(
                "container" => "ul",
                "separator" => "",
                "template" => "<li>{input}{label}</li>",
                    )
            );
            echo CHtml::openTag("div", array("class" => "clear"));
            echo CHtml::closeTag("div");
            echo CHtml::closeTag("div");
        }

        $allBankList = array_diff($allBankList, $usedBankList);

        if ($usedBefore) {
            echo CHtml::openTag("h3");
            echo Yii::t("payment", 'Other Banks');
            echo CHtml::closeTag("h3");
        } else {
            echo CHtml::openTag("p", array("class" => "flash-info"));
            echo Yii::t("payment", 'Please make the online payment via a bank where the online banking has been enabled.');
            echo CHtml::closeTag("p");
        }

        echo CHtml::openTag("div", array("class" => "bank-list"));
        echo MCHtml::groupRadioButtonList(
                "yeeBankId", $selected, $allBankList, count($usedBankList), array(
            "container" => "ul",
            "separator" => "",
            "template" => "<li>{input}{label}</li>",
                )
        );
        echo CHtml::openTag("div", array("class" => "clear"));
        echo CHtml::closeTag("div");
        echo CHtml::closeTag("div");
        $payNotice1 = Yii::t('payment', 'Please select at least one invoice.');
        $payNotice2 = Yii::t('payment', 'Please select a bank');
        $payNotice3 = Yii::t("payment", 'Please select up to 6 invoices each time to proceed the payment.');
        ?>
        <div class="clearfix"></div>


        <div id="operateCol" class="operateCol_div" style="display: none">
            <ul>
                <li><span class="btn001"><a id="confirmPay" href="#"
                                            onclick="confirmInvoiceInfo('yeepay','<?php echo $payNotice1; ?>','<?php echo $payNotice2; ?>','<?php echo $payNotice3; ?>');return false" class="operatecol"><span><?php echo Yii::t("payment", 'Make Payment'); ?>
                            </span> </a></span>
                </li>
            </ul>
        </div>


        <div class="none-ie-warning flash-warning" style="display: none">
            <p>
                <?php echo Yii::t("payment", 'Most online banking sites do not support this browser. Please use IE explorer.'); ?>
            </p>
            <p>
                <?php echo CHtml::link(Yii::t("payment", 'Thank you for the reminder. I wish to proceed to make the payment using this browser.'), 'javascript:;', array('onclick' => "confirmInvoiceInfo('yeepay','" . $payNotice1 . "','" . $payNotice2 . "','" . $payNotice3 . "');return false")); ?>
            </p>
        </div>

    </div>

    <script type="text/javascript">

        $(document).ready(function(){

            if (!$.browser.msie) {
                // 非IE浏览器 网上支付
                $("#tabYeepay .none-ie-warning").show();
                $("#tabYeepay .operateCol_div").remove();
            }else{
                $("#tabYeepay .operateCol_div").show();
            }

        });

    </script>

    <?php
} else {

    echo '<div class="flash-info">';
    echo Yii::t("payment", 'Sorry, online payment is not available for this campus.');
    echo '</div>';
}
?>