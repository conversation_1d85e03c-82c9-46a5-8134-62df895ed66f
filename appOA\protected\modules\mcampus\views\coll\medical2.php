<div id='homeAddress'>
    <div class="progress">
        <div class="progress-bar progress-bar-success" :style="'width:'+ parsefloat(filled/total) +'%;'">
            <span v-if='filled!=0' :title="filled+'/'+total">{{parsefloat(filled/total)}}% ({{filled}}/{{total}})</span>
            <span v-else>0% ({{filled}}/{{total}})</span>
        </div>

    </div>

    <div class='pull-left' id="nameSearchComp"></div>
    <p class='pull-right'>
        <button type="button" class="btn btn-default btn-sm"  v-if='type=="filled"' :disabled='btnCon' @click='batchPass("modal")'>批量通过</button>
        <button type="button" class="btn btn-default btn-sm ml10"  @click='exportTab()'>导出表格</button>
        <button type="button" class="btn btn-primary btn-sm ml10" v-if='type=="filled"' :disabled='btnCon'  @click='print()'>打印</button>
    </p>
    <div class='clearfix'></div>
    <div class='relative'>
        <div class='loading' v-show='loading'>
            <span></span>
        </div>
        <ul class="nav nav-tabs" role="tablist">
            <li role="presentation" class="active"><a href="#home" aria-controls="home" role="tab" data-toggle="tab"  @click='getInfo("filled")'>已填写 <span class="badge">{{filled}}</span></a></li>
            <li role="presentation"><a href="#unstart" aria-controls="unstart" role="tab" data-toggle="tab" @click='getInfo("unfilled")'>未填写 <span class="badge badge-error">{{unfilled}}</span></a></li>
        </ul>
        <div class="tab-content">
            <div role="tabpanel" class="tab-pane active" id="home">
                <table class="table table-bordered mt20" id='filled' v-show='list.length!=0'>
                    <thead>
                    <tr>
                        <th width='30' class="nosort">
                            <input type="checkbox" id="inlineCheckbox1" value="option1"  v-model='all'  @change='checkAll($event)'>
                        </th>
                        <th width='150'><?php echo Yii::t('global', 'Name') ?></th>
                        <th width='200'><?php echo Yii::t('labels', 'Class') ?></th>
                        <th width='150'><?php echo Yii::t('labels', 'Date of Birth') ?></th>
                        <th width='100'><?php echo Yii::t('labels', 'Status') ?></th>
                        <th width='150'>提交时间</th>
                        <th width='100' class="nosort"><?php echo Yii::t('global', 'Action') ?></th>
                    </tr>
                    </thead>
                    <tbody v-if='isShow'>
                        <tr v-for='(item,index) in list'>
                            <th>
                                <input type="checkbox" id="inlineCheckbox1" :value="item.childid" :disabled='item.step_status==4?true:false' v-model='child_ids'  @change='childCheck($event)'>
                            </th>
                            <td >
                                <a href="javascript:;"  @click="overalls(item.childid)">
                                    {{item.name}}
                                </a>
                            </td>
                            <td > {{item.class_name}}</td>
                            <td >{{item.birthday}}</td>
                            <td >
                                <i class='glyphicon glyphicon-remove-sign' v-if='item.step_status==3' style='color:#D5514E'></i>
                                <i class='glyphicon glyphicon-ok-sign'  v-if='item.step_status==4' style='color:#5cb85c'></i>
                                <i class='glyphicon glyphicon-registration-mark'  v-if='item.step_status==1' style='color:#f0ad4e'></i>
                                <i class='glyphicon glyphicon-question-sign'  v-if='item.step_status==0' style='color:#f0ad4e'></i>
                            {{statusList[item.step_status]}}</td>

                            <td >{{item.step_data.submitted_at}} </td>
                            <td >
                                <a class="btn btn-primary btn-xs" href='javascript:;' @click='auditList(index,"no")'><?php echo Yii::t('global', 'View Detail') ?></a>
                                <a class="btn btn-primary btn-xs" :href="'<?php echo $this->createUrlReg('PrintDetail', array('branchId' => $this->branchId,'coll_id'=>$coll_id)); ?>&childid='+item.childid+'&step_id=medical2'" target="_blank">
                                    <?php echo Yii::t('global', 'Print') ?>
                                </a>

                            </td>
                        </tr>
                    </tbody>
                </table>

                <div class="mt15" v-show='list.length==0'>
                    <div class="alert alert-warning" role="alert"><?php echo Yii::t('ptc', 'No Data') ?></div>
                </div>
            </div>
            <!-- 未填写的数据 -->
            <div role="tabpanel" class="tab-pane " id="unstart">
                <table class="table table-bordered mt20" id='unfilled' v-show='unList.length!=0'>
                    <thead>
                    <tr>
                        <th><?php echo Yii::t('global', 'Name') ?></th>
                        <th><?php echo Yii::t('labels', 'Class') ?></th>
                        <th><?php echo Yii::t('labels', 'Date of Birth') ?></th>
                        <th><?php echo Yii::t('labels', 'Status') ?></th>
                    </tr>
                    </thead>
                    <tbody  v-if='isShow'>
                    <tr v-for='(item,index) in unList'>
                            <td >
                                <a href="javascript:;"  @click="overalls(item.childid)">
                                    {{item.name}}
                                </a>
                            </td>
                            <td > {{item.class_name}}</td>
                            <td >{{item.birthday}}</td>
                            <td >{{statusList[item.step_status]}}</td>
                        </tr>
                    </tbody>
                </table>

                <div class="mt15" v-show='unList.length==0'>
                    <div class="alert alert-warning" role="alert"><?php echo Yii::t('ptc', 'No Data') ?></div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" tabindex="-1" role="dialog" id='auditDialog' data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title"><?php echo Yii::t('global', 'View Detail') ?></h4>
            </div>
            <div class="modal-body" v-if='Object.keys(rejectionList).length != 0'>
                <div class="media col-md-12 ">
                    <div class="media-left pull-left media-middle">
                        <a href="javascript:void(0)">
                            <img :src="rejectionList.avatar" data-holder-rendered="true" class="media-object img-circle avatarImage">
                        </a>
                    </div>
                    <div class="media-body pt10 media-middle">
                        <h4 class="media-heading font14">{{rejectionList.name}}</h4>
                        <div class="text-muted">{{rejectionList.class_name}}</div>
                    </div>
                </div>
                <div class='clearfix'></div>
                <div class='mt20 mb20' v-if='rejectionList.step_data.insurance_companies'>
                    <div class='col-md-6 mt10 color6'><span class='color3 mr10'><?php echo Yii::t('reg', '个人医疗/意外保险信息 （没有请填无）') ?>：</span>
                        <span :style="{color:setColor(rejectionList.step_data.insurance_companies,rejectionList.step_data.last_submitted_log.insurance_companies??rejectionList.step_data.insurance_companies)}">
                            {{rejectionList.step_data.insurance_companies}}
                        </span>
                    </div>
                    <div class='col-md-12 mt20 mb20'>
                        <p><span class='color3 mr10'><?php echo Yii::t('reg', '请上传保险卡正反面照片') ?> ：</p>
                        <img v-for='(list,index) in rejectionList.step_data.insurance_card_photos'  @click='bigImg(list)'  :src="list" alt="" class='stepUpImg'>
                    </div>
                    <div  class='col-md-12 mt10 color3'><strong><?php echo Yii::t('reg', 'Preferred Hospital') ?></strong></div>
                    <div class='col-md-6 mt10 color6' v-for='(list,index) in rejectionList.step_data.intended_hospitals'>
                        <span class='color3 mr10'> <?php echo Yii::t('reg', 'Preferred Hospital') ?>{{index+1}}：</span>
                        <span :style="{color:setColor(list, rejectionList.step_data.last_submitted_log.intended_hospitals ? rejectionList.step_data.last_submitted_log.intended_hospitals[index] ?? '' : '')}">{{list}}</span>
                    </div>
                    <div class='col-md-12 mt20 mb10'>
                        <p><?php echo Yii::t('site', 'Emergency Contact') ?>：</p>
                        <div class="col-md-12 mt10 mb10" v-for="(item,index) in rejectionList.step_data.emergency_contact" :key="index">
                            <p>姓名： <span :style="nameStyle(item,'name')">{{item.name}}</span></p>
                            <p>手机号：<span :style="nameStyle(item,'mobile')">{{item.mobile}}</span></p>
                            <p>邮箱：<span :style="nameStyle(item,'email')">{{item.email}}</span></p>
                        </div>
                    </div>
                    <div class='col-md-6 mt10 color6'>
                        <span class='color3 mr10'><?php echo Yii::t('reg', 'ADD/ADHD') ?>：</span>
                        <span :style="{color:setColor(rejectionList.step_data.is_adhd,rejectionList.step_data.last_submitted_log.is_adhd??rejectionList.step_data.is_adhd)}">{{rejectionList.step_data.is_adhd==1?'是':'否'}}</span>
                    </div>
                    <div class='col-md-6 mt10 color6'>
                        <span class='color3 mr10'><?php echo Yii::t('reg', 'Heart Disorder') ?>：</span>
                        <span :style="{color:setColor(rejectionList.step_data.is_heart_disease,rejectionList.step_data.last_submitted_log.is_heart_disease??rejectionList.step_data.is_heart_disease)}">{{rejectionList.step_data.is_heart_disease==1?'是':'否'}}</span>
                    </div>
                    <div class='col-md-6 mt10 color6'>
                        <span class='color3 mr10'><?php echo Yii::t('reg', 'Frequent Ear Infections /Hearing Problems') ?>：</span>
                        <span :style="{color:setColor(rejectionList.step_data.frequent,rejectionList.step_data.last_submitted_log.frequent??rejectionList.step_data.frequent)}">{{rejectionList.step_data.frequent==1?'是':'否'}}</span>
                    </div>
                    <div class='col-md-6 mt10 color6'><span class='color3 mr10'><?php echo Yii::t('reg', 'Asthma') ?>：</span>
                        <span :style="{color:setColor(rejectionList.step_data.asthma,rejectionList.step_data.last_submitted_log.asthma??rejectionList.step_data.asthma)}">{{rejectionList.step_data.asthma==1?'是':'否'}}</span>
                    </div>
                    <div class='col-md-6 mt10 color6'><span class='color3 mr10'><?php echo Yii::t('reg', 'Hepatitis') ?>：</span>
                        <span :style="{color:setColor(rejectionList.step_data.hepatitis,rejectionList.step_data.last_submitted_log.hepatitis??rejectionList.step_data.hepatitis)}">{{rejectionList.step_data.hepatitis==1?'是':'否'}}</span>
                    </div>
                    <div class='col-md-6 mt10 color6'><span class='color3 mr10'><?php echo Yii::t('reg', 'Back problems or scoliosis') ?>：</span>
                        <span :style="{color:setColor(rejectionList.step_data.problems,rejectionList.step_data.last_submitted_log.problems??rejectionList.step_data.problems)}">{{rejectionList.step_data.problems==1?'是':'否'}}</span>
                    </div>
                    <div class='col-md-6 mt10 color6'><span class='color3 mr10'><?php echo Yii::t('reg', 'Gastrointestinal Disorder') ?>：</span>
                        <span :style="{color:setColor(rejectionList.step_data.gastrointertianl,rejectionList.step_data.last_submitted_log.gastrointertianl??rejectionList.step_data.gastrointertianl)}">{{rejectionList.step_data.gastrointertianl==1?'是':'否'}}</span>
                    </div>
                    <div class='col-md-6 mt10 color6'><span class='color3 mr10'><?php echo Yii::t('reg', 'Bone Fractures') ?>：</span>
                        <span :style="{color:setColor(rejectionList.step_data.fractures,rejectionList.step_data.last_submitted_log.fractures??rejectionList.step_data.fractures)}">{{rejectionList.step_data.fractures==1?'是':'否'}}</span>
                    </div>
                    <div class='col-md-6 mt10 color6'><span class='color3 mr10'><?php echo Yii::t('reg', 'Skin Problems') ?>：</span>
                        <span :style="{color:setColor(rejectionList.step_data.skinProblems,rejectionList.step_data.last_submitted_log.skinProblems??rejectionList.step_data.skinProblems)}">{{rejectionList.step_data.skinProblems==1?'是':'否'}}</span>
                    </div>
                    <div class='col-md-6 mt10 color6'>
                        <span class='color3 mr10'><?php echo Yii::t('reg', 'Diabetes') ?>：</span>
                        <span :style="{color:setColor(rejectionList.step_data.diabetes,rejectionList.step_data.last_submitted_log.diabetes??rejectionList.step_data.diabetes)}">{{rejectionList.step_data.diabetes==1?'是':'否'}}</span>
                    </div>
                    <div class='col-md-6 mt10 color6'>
                        <span class='color3 mr10'><?php echo Yii::t('reg', 'Vision Problems') ?>：</span>
                        <span :style="{color:setColor(rejectionList.step_data.visionProblems,rejectionList.step_data.last_submitted_log.visionProblems??rejectionList.step_data.visionProblems)}">{{rejectionList.step_data.visionProblems==1?'是':'否'}}</span>
                    </div>
                    <div class='col-md-6 mt10 color6'>
                        <span class='color3 mr10'><?php echo Yii::t('reg', 'Epilepsy/Seizure Disorder') ?>：</span>
                        <span :style="{color:setColor(rejectionList.step_data.seizureDisorde,rejectionList.step_data.last_submitted_log.seizureDisorde??rejectionList.step_data.seizureDisorde)}">{{rejectionList.step_data.seizureDisorde==1?'是':'否'}}</span>
                    </div>
                    <div class='col-md-6 mt10 color6'>
                        <span class='color3 mr10'><?php echo Yii::t('reg', 'Tuberculosis') ?>：</span>
                        <span :style="{color:setColor(rejectionList.step_data.is_tuberculosis,rejectionList.step_data.last_submitted_log.is_tuberculosis??rejectionList.step_data.is_tuberculosis)}">{{rejectionList.step_data.is_tuberculosis==1?'是':'否'}}</span>
                    </div>
                    <div class='col-md-6 mt10 color6'>
                        <span class='color3 mr10'><?php echo Yii::t('reg', 'Cough and expectoration lasting more than 2 weeks') ?>：</span>
                        <span :style="{color:setColor(rejectionList.step_data.tuberculosisOne,rejectionList.step_data.last_submitted_log.tuberculosisOne??rejectionList.step_data.tuberculosisOne)}">{{rejectionList.step_data.tuberculosisOne==1?'是':'否'}}</span>
                    </div>
                    <div class='col-md-6 mt10 color6'>
                        <span class='color3 mr10'><?php echo Yii::t('reg', 'There is blood in the mucus repeatedly coughed up') ?>：</span>
                        <span :style="{color:setColor(rejectionList.step_data.tuberculosisTwo,rejectionList.step_data.last_submitted_log.tuberculosisTwo??rejectionList.step_data.tuberculosisTwo)}">{{rejectionList.step_data.tuberculosisTwo==1?'是':'否'}}</span>
                    </div>
                    <div class='col-md-6 mt10 color6'>
                        <span class='color3 mr10'><?php echo Yii::t('reg', 'Recurrent fever lasting more than 2 weeks') ?>：</span>
                        <span :style="{color:setColor(rejectionList.step_data.tuberculosisThree,rejectionList.step_data.last_submitted_log.tuberculosisThree??rejectionList.step_data.tuberculosisThree)}">{{rejectionList.step_data.tuberculosisThree==1?'是':'否'}}</span>
                    </div>
                    <div class='col-md-6 mt10 color6'>
                        <span class='color3 mr10'><?php echo Yii::t('reg', 'Are there tuberculosis patients among the family members, relatives and friends who are often in contact with the immediate family over the last 2 years') ?>：</span>
                        <span :style="{color:setColor(rejectionList.step_data.tuberculosisFour,rejectionList.step_data.last_submitted_log.tuberculosisFour??rejectionList.step_data.tuberculosisFour)}">{{rejectionList.step_data.tuberculosisFour==1?'是':'否'}}</span>
                    </div>
                    <div class='col-md-6 mt10 color6'>
                        <span class='color3 mr10'><?php echo Yii::t('reg', '过敏') ?>：</span>
                        <span :style="{color:setColor(rejectionList.step_data.allergies,rejectionList.step_data.last_submitted_log.allergies??rejectionList.step_data.allergies)}">{{rejectionList.step_data.allergies==1?'是':'否'}}</span>
                    </div>
                    <div class='col-md-6 mt10 color6' v-if='rejectionList.step_data.allergies==1'><span class='color3 mr10'>
                            <?php echo Yii::t('reg', '食物过敏') ?>：</span>
                        <span :style="{color:setColor(rejectionList.step_data.allergies_food,rejectionList.step_data.last_submitted_log.allergies_food??rejectionList.step_data.allergies_food)}">{{rejectionList.step_data.allergies_food}}</span>
                    </div>
                    <div class='col-md-6 mt10 color6' v-if='rejectionList.step_data.allergies==1'>
                        <span class='color3 mr10'><?php echo Yii::t('reg', '药物过敏') ?>：</span>
                        <span :style="{color:setColor(rejectionList.step_data.allergies_medication,rejectionList.step_data.last_submitted_log.allergies_medication??rejectionList.step_data.allergies_medication)}">{{rejectionList.step_data.allergies_medication}}</span>
                    </div>
                    <div class='col-md-6 mt10 color6' v-if='rejectionList.step_data.allergies==1'>
                        <span class='color3 mr10'><?php echo Yii::t('reg', '其他过敏') ?>：</span>
                        <span :style="{color:setColor(rejectionList.step_data.allergies_other,rejectionList.step_data.last_submitted_log.allergies_other??rejectionList.step_data.allergies_other)}">{{rejectionList.step_data.allergies_other}}</span>
                    </div>
                    <div class='col-md-6 mt10 color6'>
                        <span class='color3 mr10'><?php echo Yii::t('reg', '定期使用药物') ?>：</span>
                        <span :style="{color:setColor(rejectionList.step_data.is_use_drugs,rejectionList.step_data.last_submitted_log.is_use_drugs??rejectionList.step_data.is_use_drugs)}">{{rejectionList.step_data.is_use_drugs==1?'是':'否'}}</span>
                        <span  v-if='rejectionList.step_data.is_use_drugs==1'> ({{rejectionList.step_data.use_drugs}})</span>
                    </div>
                    <div class='col-md-6 mt10 color6'>
                        <span class='color3 mr10'><?php echo Yii::t('reg', 'Other Illnesses (please specify)') ?>：</span>
                        <span :style="{color:setColor(rejectionList.step_data.other,rejectionList.step_data.last_submitted_log.other??rejectionList.step_data.other)}">{{rejectionList.step_data.other}}</span>
                    </div>
                </div>
                <div class='clearfix'></div>
                <div class='col-md-12 mt20 mb20'>
                    <p><span class='color3 mr10'><?php echo Yii::t('reg', 'Physical Examination Report') ?> ：</p>
                    <img v-for='(list,index) in rejectionList.step_data.examination_reports'  @click='bigImg(list)'  :src="list" alt="" class='stepUpImg'>
                </div>
                <div class='col-md-12 mt20 mb20'>
                    <p><?php echo Yii::t('reg', 'A Copy of the Vaccination Record') ?>：</p>
                     <img v-for='(list,index) in rejectionList.step_data.vaccines_reports'  @click='bigImg(list)' :src="list" alt="" class='stepUpImg'>
                </div>
                <div class='col-md-12 mt20 mb20' v-if='rejectionList.sign_url!=""'>
                    <p><?php echo Yii::t('event', 'Signature') ?>：</p>
                    <img :src="rejectionList.sign_url" alt="" class='signImgDetails'>
                </div>
                <div class='clearfix'></div>
                <div class="panel panel-default mt20">
                    <div class="panel-heading">
                        <h3 class="panel-title"><?php echo Yii::t('reg', '审核') ?></h3>
                    </div>
                    <div class="panel-body">
                        <div class='col-md-12 col-sm-12 font14'>
                            <div class='col-md-6 col-sm-6 borderRight scroll-box' style='max-height:250px;overflow-y:auto'>
                                <p><strong>当前审核状态</strong></p>

                                <div>
                                    {{statusList[rejectionList.step_status]}}
                                    <button type="button" class="btn btn-primary  btn-xs pull-right" @click='resetStatus()'  v-if='rejectionList.step_status==3 || rejectionList.step_status==4'><?php echo Yii::t('global', 'Reset') ?></button>
                                </div>
                                <p class='mt10'><strong>审核记录</strong></p>
                                <div>
                                    <div v-for='(list,index) in rejectionList.audit_log' class='ml10' style='border-left:1px dashed #ccc'>
                                    <p style='margin-left:-7px;background: #fff;' >
                                        <span v-if='list.status==4'>
                                        <span class='glyphicon glyphicon-ok-sign greenIcon'></span><span> <?php echo Yii::t('global', 'Confirmed') ?></span>
                                        </span>
                                        <span v-if='list.status==3'>
                                        <span class='glyphicon glyphicon-info-sign redIcon'></span> <span> <?php echo Yii::t('reg', 'Rejected') ?></span>
                                        </span>
                                        <span v-if='list.status==0'>
                                        <span class='glyphicon glyphicon-question-sign yellowIcon'></span> <span> <?php echo Yii::t('global', 'Reset') ?></span>
                                        </span>
                                        </p>
                                    <p class='ml10' style='background:#F7F7F8;line-height:22px'  v-if='list.comment!="" && list.comment!=null'><?php echo Yii::t('reg', '备注：') ?>{{list.comment}}</p>
                                    <p class='pl10 font12'>{{list.date}} {{list.user}}</p>
                                    </div>
                                </div>
                            </div>
                            <div class='col-md-6 col-sm-6' >
                                <p><strong>审核</strong></p>
                                <div class='p10 grey'>
                                    <label class="radio ml20">
                                        <input type="radio" id="inlineradio1" v-model='audit_status' value="4"> <?php echo Yii::t('labels', 'Yes') ?>
                                    </label>
                                    <label class="radio ml20">
                                        <input type="radio" id="inlineradio2" v-model='audit_status' value="3"> 未通过
                                    </label>
                                    <textarea class="form-control" rows="3" placeholder='请输入备注' v-model='comment'></textarea>
                                    <div v-if='audit_status==3 || type_status==2'>
                                        <p class='mt10'>修改建议将直接显示在家长端，请准确措辞。</p>
                                        <div class="checkbox">
                                            <label>
                                                <input type="checkbox"  v-model='send_wechat'>将审核结果发送至家长微信
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class='mt20'>
                                    <button type="button"  class="btn btn-primary pull-right" :disabled='btnCon' @click='saveInfo()'><?php echo Yii::t("newDS", "确认");?></button>
                                    <button type="button" class="btn btn-default  pull-right mr10" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class='clearfix'></div>
                <!--                护士审核-->
                <div class="panel panel-default mt20">
                    <div class="panel-heading">
                        <h3 class="panel-title">护士审核情况汇总 Summary of Nurse Review Status</h3>
                    </div>
                    <div class="panel-body">
                        <div>
                            <div><label for="">入学体检 Entrance Physical Examination</label></div>
                            <label class="radio-inline" >
                                <input type="radio" name="physicalExamination" v-model='nurse.physicalExamination' value="2">已提交 Submitted
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="physicalExamination" v-model='nurse.physicalExamination' value="1">未提交  Not Submitted
                            </label>
                            <input type="text" class="form-control mt10"  placeholder="请输入备注信息" v-model='nurse.physicalExaminationDetail'>
                            <input type="text" class="form-control mt10"  placeholder="Please fill in English only" v-model='nurse.physicalExaminationDetailEn'>
                        </div>
                        <div class='mt20'>
                            <div><label for="">长期用药需求 Long-term Medication Needs</label></div>
                            <label class="radio-inline" v-for='(list,key,i) in common'>
                                <input type="radio" name="longMedication" v-model='nurse.longMedication' :value="key"> {{list}}
                            </label>
                            <input type="text" class="form-control mt10"  placeholder="请输入备注信息" v-model='nurse.longMedicationDetail'>
                            <input type="text" class="form-control mt10"  placeholder="Please fill in English only" v-model='nurse.longMedicationDetailEn'>
                        </div>

                        <div class='mt20'>
                            <div><label for="">既往病史 Medical History</label></div>
                            <div class='flex align-items'>
                                <label class="radio-inline" v-for='(list,key,i) in common'>
                                    <input type="radio" name="medicalHistory" v-model='nurse.medicalHistory' :value="key"> {{list}}
                                </label>
                                <div class='flex1 ml20' v-show='nurse.medicalHistory==2'>
                                    <input type="text" class="form-control select_2 pull-left  mr10 ml0" @blur='publish_atVal("medicalHistoryDate")' id="medicalHistoryDate0" v-model='nurse.medicalHistoryDate[0]' :value='nurse.medicalHistoryDate[0]'  placeholder="<?php echo Yii::t("newDS", "Select a date"); ?>" >
                                    <span class='pull-left' style='line-height:30px'>-</span>
                                    <input type="text" class="form-control select_2 pull-left ml10"  @blur='expired_atVal("medicalHistoryDate")' id="medicalHistoryDate1" v-model='nurse.medicalHistoryDate[1]'   :value='nurse.medicalHistoryDate[1]'  placeholder="<?php echo Yii::t("newDS", "Select a date"); ?>" >
                                </div>
                            </div>
                            <input type="text" class="form-control mt10"  placeholder="请仅用中文填写"  v-if="nurse.medicalHistory==2" v-model='nurse.medicalHistoryDetailCn'>
                            <input type="text" class="form-control mt10"  placeholder="Please fill in English only"   v-if="nurse.medicalHistory==2" v-model='nurse.medicalHistoryDetailEn'>
                        </div>

                        <div class='mt20'>
                            <div><label for="">过敏史 Allergy History</label></div>
                            <label class="radio-inline" v-for='(list,key,i) in common'>
                                <input type="radio" name="allergy" v-model='nurse.allergy' :value="key"> {{list}}
                            </label>
                            <div v-show='nurse.allergy==2' >
                                <div class='p10 grey mt10'>
                                    <div class='flex align-items'>
                                        <div class="checkbox">
                                            <label>
                                                <input type="checkbox" name="foodAllergy" v-model='nurse.foodAllergy'>食物过敏 Food Allergy
                                            </label>
                                        </div>
                                        <div class='flex1 ml20' v-show='nurse.foodAllergy'>
                                            <input type="text" class="form-control select_2 pull-left  mr10 ml0" @blur='publish_atVal("foodAllergyDate")' id="foodAllergyDate0" v-model='nurse.foodAllergyDate[0]' :value='nurse.foodAllergyDate[0]'  placeholder="<?php echo Yii::t("newDS", "Select a date"); ?>" >
                                            <span class='pull-left' style='line-height:30px'>-</span>
                                            <input type="text" class="form-control select_2 pull-left ml10"  @blur='expired_atVal("foodAllergyDate")' id="foodAllergyDate1" v-model='nurse.foodAllergyDate[1]'   :value='nurse.foodAllergyDate[1]'  placeholder="<?php echo Yii::t("newDS", "Select a date"); ?>" >
                                        </div>
                                    </div>
                                    <input type="text" class="form-control mb10"  placeholder="请仅用中文填写" v-if='nurse.foodAllergy' v-model='nurse.foodAllergyDetailCn'>
                                    <input type="text" class="form-control mb10"  placeholder="Please fill in English only" v-if='nurse.foodAllergy' v-model='nurse.foodAllergyDetailEn'>

                                    <div  class='flex align-items'>
                                        <div class="checkbox">
                                            <label>
                                                <input type="checkbox" name="medAllergy" v-model='nurse.medAllergy' >药品过敏 Medical Allergy
                                            </label>
                                        </div>
                                        <div class='flex1 ml20' v-show='nurse.medAllergy'>
                                            <input type="text" class="form-control select_2 pull-left  mr10 ml0" @blur='publish_atVal("medAllergyDate")' id="medAllergyDate0" v-model='nurse.medAllergyDate[0]' :value='nurse.medAllergyDate[0]'  placeholder="<?php echo Yii::t("newDS", "Select a date"); ?>" >
                                            <span class='pull-left' style='line-height:30px'>-</span>
                                            <input type="text" class="form-control select_2 pull-left ml10"  @blur='expired_atVal("medAllergyDate")' id="medAllergyDate1" v-model='nurse.medAllergyDate[1]'   :value='nurse.medAllergyDate[1]'  placeholder="<?php echo Yii::t("newDS", "Select a date"); ?>" >
                                        </DIV>
                                    </div>
                                    <input type="text" class="form-control mb10 "  placeholder="请仅用中文填写" v-if='nurse.medAllergy' v-model='nurse.medAllergyDetailCn'>
                                    <input type="text" class="form-control mb10 "  placeholder="Please fill in English only" v-if='nurse.medAllergy' v-model='nurse.medAllergyDetailEn'>

                                    <div class='flex align-items'>
                                        <div class="checkbox">
                                            <label>
                                                <input type="checkbox" name="otherAllergy" v-model='nurse.otherAllergy' >其它过敏 Other Allergies
                                            </label>
                                        </div>
                                        <div class='flex1 ml20' v-show='nurse.otherAllergy'>
                                            <input type="text" class="form-control select_2 pull-left  mr10 ml0" @blur='publish_atVal("otherAllergyDate")' id="otherAllergyDate0" v-model='nurse.otherAllergyDate[0]' :value='nurse.otherAllergyDate[0]'  placeholder="<?php echo Yii::t("newDS", "Select a date"); ?>" >
                                            <span class='pull-left' style='line-height:30px'>-</span>
                                            <input type="text" class="form-control select_2 pull-left ml10"  @blur='expired_atVal("otherAllergyDate")' id="otherAllergyDate1" v-model='nurse.otherAllergyDate[1]'   :value='nurse.otherAllergyDate[1]'  placeholder="<?php echo Yii::t("newDS", "Select a date"); ?>" >
                                        </div>
                                    </div>
                                    <input type="text" class="form-control mb10"  placeholder="请仅用中文填写" v-if='nurse.otherAllergy' v-model='nurse.otherAllergyDetailCn'>
                                    <input type="text" class="form-control mb10"  placeholder="Please fill in English only" v-if='nurse.otherAllergy' v-model='nurse.otherAllergyDetailEn'>

                                    </div>

                            </div>
                        </div>
                        <div class='mt20'>
                            <div><label for=""><?php echo Yii::t('reg', '通知人') ?></label></div>
                            <div class='staffInput' @click='showStaff("add")'>
                                <span class='flex1' v-if='staff_ids.length==0'>请选择通知人员</span>
                                <span class='flex1' v-else>
                                    <span class="el-tag el-tag--info el-tag--small el-tag--light mr10 mb5 mt5" v-for='(list,index) in staff_ids'>
                                        <span class="el-select__tags-text" v-if='allDept[list]'>{{allDept[list].name}}</span>
                                        <i class="el-tag__close el-icon-close" @click.stop='staffUnassign(list,index)'></i>
                                    </span>
                                </span>
                                <span class='el-icon-arrow-down'></span>
                            </div>
                        </div>
                        <div class='mt20'>
                            <div><label for="">疫苗 Vaccination</label></div>
                            <div class="mt10" v-for='(list,key,i) in immunizationList'>
                                <input type="checkbox" id="inlineCheckbox1" :value="key" v-model='nurse.immunization'> {{list}}
                            </div>
                            <input type="text" class="form-control mt10"  placeholder="请输入备注信息" v-model='nurse.immunizationDetail'>
                        </div>
                        <div class='mt20'>
                            <div><label for="">新冠疫苗 COVID-19 Vaccine</label></div>
                            <label class="radio-inline">
                                <input type="radio" name="inlineRadioOptions"  value="1" v-model='nurse.isCOVID'> 已接种 Already Vaccinated
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="inlineRadioOptions"  value="2"  v-model='nurse.isCOVID'> 未接种 Not Yet Vaccinated
                            </label>
                            <div class='p10 grey mt10' v-if='nurse.isCOVID==1'>
                                <div>
                                    <button type="button" class="btn btn-primary" @click='addCOVID'>
                                        <span class="glyphicon glyphicon-plus" aria-hidden="true"></span> 增加接种针数 Add Vaccine
                                    </button>
                                </div>
                                <div v-for='(list,i) in COVID' class='mt10'>
                                    <span v-if="i==0">
                                        第一针 1st Shot:
                                    </span>
                                    <span v-if="i==1">
                                        第二针 2nd Shot:
                                    </span>
                                    <span v-if="i==2">
                                        第三针 3rd Shot:
                                    </span>
                                    <input type="text" class="form-control select_2 inline-block mr10 ml0"  :id="'COVID_' + i" v-model='list.date'  placeholder="<?php echo Yii::t("newDS", "Select a date"); ?>"  :value='list.date'>
                                    <span class='glyphicon glyphicon-trash ml20' @click='delCOVID(i)'></span>
                                </div>
                            </div>
                            <input type="text" class="form-control mt10"  placeholder="请输入备注信息" v-model='nurse.COVIDDetail'>
                        </div>
                        <div class='mt20'>
                            <div><label for="">其它情况说明</label></div>
                            <textarea class="form-control" rows="3" v-model='nurse.otherDetailCn' placeholder="请仅用中文填写"></textarea>
                        </div>
                        <div class='mt20'>
                            <div><label for="">Other Information</label></div>
                            <textarea class="form-control" rows="3" v-model='nurse.otherDetailEn' placeholder="Please fill in English only"></textarea>
                        </div>

                        <div class='mt20'>
                            <div><label for="">备注（仅护士可见）Note(For Nurse Only)</label></div>
                            <textarea class="form-control" rows="3" v-model='nurse.noteNurseOnly' placeholder=""></textarea>
                        </div>

                        <div class='mt20'>
                            <div class="pull-left">
                                <button type="button"  class="btn btn-primary " :disabled='btnCon' @click='saveRemarks("send")'><?php echo Yii::t("newDS", "保存并发送邮件");?></button>
                                <p style="margin-top: 5px;margin-bottom: 0px" v-if="rejectionList.step_data.last_remarks_send_time">
                                    <span>最近通知时间：{{rejectionList.step_data.last_remarks_send_time}}</span>
                                </p>
                            </div>
                            <div class="pull-right">
                                <button type="button"  class="btn btn-primary ml10 pull-right" :disabled='btnCon' @click='saveRemarks("save")'><?php echo Yii::t("newDS", "仅保存");?></button>
                                <button type="button" class="btn btn-default pull-right" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                                <div class='clearfix'></div>
                                <p style="margin-top: 5px;margin-bottom: 0px" v-if="rejectionList.step_data.last_remarks_time">
                                    <span>最近保存时间：{{rejectionList.step_data.last_remarks_time}}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </div>
    <div class="modal fade" id="stuDetails" tabindex="-1" role="dialog" >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" ><?php echo Yii::t('attends', '整体情况') ?> <span id="remarks_t"></span></h4>
                </div>
                <div class="modal-body color6" v-if='childData.child'>
                    <p><strong class='font14 color3'>学生资料</strong></p>
                    <div class='flex'>
                        <div style='width:80px'><img :src="childData.child.avatar" class='stuImg'></div>
                        <div class='flex1'>
                            <p class='mt10'><strong class='font14 color3 studentName'>{{childData.child.name}}</strong> </p>
                            <p class='mb20'>{{childData.child.class_name}}</p>
                            <div  v-if='childData.parents.finfo!=null'>
                                <div class='col-md-4'>
                                <strong class='color3'><?php echo Yii::t('global', 'Father') ?>：</strong>
                                    <span class='glyphicon glyphicon-user'></span> {{childData.parents.finfo.name}}
                                </div>
                                <div class='col-md-4'>
                                    <span class='glyphicon glyphicon-phone'></span> {{childData.parents.finfo.mphone}}
                                </div>
                                <div class='col-md-4'>
                                    <span class='glyphicon glyphicon-envelope'></span> {{childData.parents.finfo.email}}
                                </div>
                            </div>
                            <div class='clearfix'></div>
                            <div class='mt10' v-if='childData.parents.minfo!=null'>
                                <div class='col-md-4'>
                                <strong class='color3'><?php echo Yii::t('global', 'Mother') ?>：</strong>
                                    <span class='glyphicon glyphicon-user'></span> {{childData.parents.minfo.name}}
                                </div>
                                <div class='col-md-4'>
                                    <span class='glyphicon glyphicon-phone'></span> {{childData.parents.minfo.mphone}}
                                </div>
                                <div class='col-md-4'>
                                    <span class='glyphicon glyphicon-envelope'></span> {{childData.parents.minfo.email}}
                                </div>
                            </div>
                        </div>
                    </div>
                    <ul class="nav nav-wizard pb20 mt20">
                        <li v-for='(list,index) in childData.step_status'>
                            <a href="javascript::" class='color6' :data-tab="list.step_id" :data-status="list.status" :data-name="childData.child.name"  >
                                <span class="number">{{index+1}}</span>{{list.title}}
                            </a>
                            <p class='mt10 color6' >
                                <span :class="list.status==4?'greenStep':list.status==3?'redStep':list.status==1 || list.status==2?'orgStep':''">{{ statusList[list.status]}}</span></p>
                        </li>
                    </ul>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel') ?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 选择成员 -->
    <div class="modal fade" id="addStaffModal" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("newDS", "选择成员");?></h4>
                </div>
                <div class="modal-body relative" style='padding:0'>
                    <span class='borderLeftpos'></span>
                    <div style='max-height:600px;' class='p24 row'>
                        <div class='col-md-6 col-sm-6'>
                            <div>
                                <select v-model="school_id"  size='small' class="form-control" style='width:100%' placeholder="<?php echo Yii::t('teaching', 'Please select') ?>" @change='showStaff()'>
                                    <option
                                        v-for="(item,key,index) in schoolList"
                                        :key="key"
                                        :label="item.title"
                                        :value="key">
                                    </option>
                                </select>
                            </div>
                            <div class='mt10'>
                                <input type="text"
                                placeholder="搜索"
                                class="form-control"
                                v-model='searchText' >
                            </div>
                            <div  class="tab-pane active mt15 scroll-box" id="class" v-if='searchText==""'  style='max-height:460px;overflow-y:auto'>
                                <div v-for='(list,index) in currentDept.list' class='relative mb16'>
                                    <p  @click='showDepname(list)'>
                                        <span  class='font14 color606 cur-p'>{{list.dep_name}} </span>
                                        <span class='el-icon-arrow-down ml5' v-if='dep_name!=list.dep_name'></span>
                                        <span class='el-icon-arrow-up ml5' v-else></span>
                                    </p>
                                    <div  class='border scroll-box mr10 childList' v-if='dep_name==list.dep_name'>
                                        <div class="flex align-items listMedia" v-for='(item,idx) in list.user'>
                                            <div class='flex flex1' v-if='currentDept.user_info[item.uid]'>
                                                <img :src="currentDept.user_info[item.uid].photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                                <div class="flex1 ml10 flex1Text">
                                                    <div class=" font14 mt4 color3 text_overflow">{{currentDept.user_info[item.uid].name}}</div>
                                                    <div class="font12 color6 text_overflow">{{currentDept.user_info[item.uid].hrPosition}}</div>
                                                </div>
                                            </div>
                                            <div >
                                                <span class='cur-p font16 text-primary  el-icon-circle-plus-outline' v-if='!currentDept.user_info[item.uid].disabled' @click='assignStaff(item,index,idx)'></span>
                                                <span v-else><?php echo Yii::t('directMessage', 'selected') ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else>
                                <div v-if='searchStaffList.length!=0' class='mt24 scroll-box'  style='max-height:460px;overflow-y:auto'>
                                    <div class="flex align-items listMedia" v-for='(item,idx) in searchStaffList'>
                                        <div class='flex flex1' >
                                            <img :src="item.photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                            <div class="flex1 ml10 flex1Text">
                                                <div class=" font14 mt4 color3 text_overflow">{{item.name}}</div>
                                                <div class="font12 color6 text_overflow">{{item.hrPosition}}</div>
                                            </div>
                                        </div>
                                        <div >
                                            <span class='cur-p font16 text-primary  el-icon-circle-plus-outline' v-if='!currentDept.user_info[item.uid].disabled' @click='assignStaff(item,idx,"search")'></span>
                                            <span v-else><?php echo Yii::t('directMessage', 'selected') ?></span>
                                        </div>
                                    </div>
                                </div>
                                <div v-else-if='searchText!=""'>
                                        <div class='font14 color6 text-center mt20'>暂无数据</div>
                                </div>
                            </div>
                        </div>
                        <div class='col-md-6 col-sm-6 borderLeft'>
                            <p class='mt10 font14 color606'>
                            <?php echo Yii::t("newDS", " ");?>{{staffSelected.length}}<?php echo Yii::t("newDS", "成员");?>
                                <button class="btn btn-link pull-right btn-xs font14" v-if='staffSelected.length!=0' type="button" @click='batchDel("staff")'><?php echo Yii::t("newDS", "清空");?></button>
                            </p>
                            <div class='scroll-box p10 overflow-y' style='height:500px'>
                                <div class="flex align-items listMedia" v-for='(item,idx) in staffSelected'>
                                    <div class='flex flex1' v-if='allDept[item]'>
                                        <img :src="allDept[item].photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                        <div class="flex1 ml10 flex1Text">
                                            <div class=" font14 mt4 color3 text_overflow">{{allDept[item].name}}</div>
                                            <div class="font12 color6 text_overflow">{{allDept[item].hrPosition}}</div>
                                        </div>
                                    </div>
                                    <div @click='Unassign(item,idx)'>
                                        <span class='closeChild cur-p mt10 font16 el-icon-circle-close'></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" @click='confirmSatff()'>确定</button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="tipsModal" tabindex="-1" role="dialog" >
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" ><?php echo Yii::t('attends', '批量通过') ?> <span id="remarks_t"></span></h4>
                </div>
                <div class="modal-body color6" >
                    <div class='color3 font14'>确认批量通过审核吗？</div> 
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel') ?></button>
                    <button type="button" class="btn btn-primary" :disabled='btnTips'  @click='batchPass()'><?php echo Yii::t('global', '确定') ?></button>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var step_id = '<?php echo $step_id?>';
    var coll_id = '<?php echo $coll_id?>';
    var type = '<?php echo $type?>';
    var type_status = '<?php echo $type_status?>';

    var  registrations = new Vue({
        el: "#homeAddress",
        data: {
            type_status:type_status,
            list:[],
            unfilled:0,
            coll_id:coll_id,
            filled:0,
            total:0,
            type:type,
            logList:[],
            statusList:[],
            audit_status:'',
            comment:'',
            child_id:'',
            childData:{},
            child_ids:[],
            rejectionList:{},
            unList:{},
            btnCon:false,
            send_wechat:true,
            isShow:true,
            nurse:{},
            nurse_backup:{},
            common:{
                1:'<?php echo Yii::t('reg', '否 No') ?>',
                2:'<?php echo Yii::t('reg', '是 Yes') ?>',
            },
            immunizationList:{
                1:'<?php echo Yii::t('reg', '4年级A+C加强已完成 Completed G4 Meningitis A+C Booster') ?>',
                2:'<?php echo Yii::t('reg', '7年级乙肝加强已完成 Completed G7 Hepatitis B Booster') ?>',
                3:'<?php echo Yii::t('reg', '7岁前疫苗已全部完成 Completed All Vaccines Before 7 Years') ?>',
                4:'<?php echo Yii::t('reg', '需追踪/补种疫苗 Tracking/Updating Vaccination Records Needed') ?>',
            },
            resetStatusIndex:'',
            loading:false,
            all:false,
            btnTips:false,
            COVID:[],
            searchText:'',
            currentDept:{},
            staffSelected:[],
            dep_name:'',
            staff_ids:[],
            staff_ids_backup:[],
            DetailId:{},
            school_id:'',
            schoolList:[],
            extraData:[],
        },
        created: function() {
            this.getInfo(this.type)
            this.getSchool()
            eventBus.$on('listChanged', list => {
                if(sessionStorage.getItem('tabType')  === 'filled'){
                    this.list = list;
                }else{
                    this.unList = list
                }
            });
        },
        watch: {
            'nurse.medicalHistory': {
                handler(newVal, oldVal) {
                    if(newVal==2 && this.nurse_backup.medicalHistory == 1){
                        this.allDept = this.allDeptDef
                        this.staff_ids = Object.keys(this.allDeptDef);
                    }else{
                        this.staff_ids =  this.staff_ids_backup
                        this.allDept = this.allDept_backup
                    }
                },
                deep: true // 监听嵌套对象的变化
            },
            'nurse.allergy': {
                handler(newVal, oldVal) {
                    if(newVal==2 && this.nurse_backup.allergy == 1){
                        this.allDept = this.allDeptDef
                        this.staff_ids = Object.keys(this.allDeptDef);
                    }else {
                        this.staff_ids =  this.staff_ids_backup
                        this.allDept = this.allDept_backup
                    }
                },
                deep: true // 监听嵌套对象的变化
            }
        },
        computed: {
            searchStaffList: function() {
                var search = this.searchText;
                var searchVal = ''; //搜索后的数据
                if(search) {
                    searchVal =Object.values(this.currentDept.user_info).filter(function(product) {
                        return Object.keys(product).some(function(key) {
                            return String(product['name'].toLowerCase()).indexOf(search.toLowerCase()) !== -1;
                        })
                    })
                    return searchVal;
                }
                return this.searchStaffList;
            },
        },
        methods: {
            nameStyle(item,type) {
                if(type == 'name'){
                    const emergencyContactName = this.rejectionList?.step_data?.last_submitted_log?.emergency_contact?.[this.index]?.name;
                    return { color: this.setColor(item.name, emergencyContactName || item.name) };
                }else if(type == 'mobile'){
                    const emergencyContactMobile = this.rejectionList?.step_data?.last_submitted_log?.emergency_contact?.[this.index]?.mobile;
                    return { color: this.setColor(item.mobile, emergencyContactMobile || item.mobile) };
                }else{
                    const emergencyContactEmail = this.rejectionList?.step_data?.last_submitted_log?.emergency_contact?.[this.index]?.email;
                    return { color: this.setColor(item.email, emergencyContactEmail || item.email) };
                }
            },
            setColor(newData,oldData){
                return newData !== oldData ? 'red' : 'initial';
            },
            publish_atVal(type){
                let that=this
                setTimeout(function() {
                    that.$nextTick(()=>{
                        that.nurse[type][0] = $('#'+type+'0').val();
                    })
                }, 500);
            },
            expired_atVal(type){
                let that=this
                setTimeout(function() {
                    that.$nextTick(()=>{
                        that.nurse[type][1] = $('#'+type+'1').val();
                    })
                }, 500);
            },
            addCOVID(){
                if(this.COVID.length >=3){
                    resultTip({error: 'warning', msg: '做多添加三次'});
                    return false;
                }
                this.COVID.push({
                    date:''
                })
                let that=this
                this.$nextTick(()=>{
                    for(var i=0;i<this.COVID.length;i++){
                        $('#COVID_'+i).datepicker({
                            dateFormat: 'yy-mm-dd',
                            selectOtherMonths: false,
                            showOtherMonths: false,
                            changeMonth: true,
                            changeYear: true,
                            firstDay: 0,
                            onSelect: function (date,str) {
                                let id=str.id
                                that.COVID[i-1].date=date
                            }
                        });
                    }
                })
            },
            delCOVID(index){
                this.COVID.splice(index, 1);
            },
            overalls(id){
                let that=this
                $.ajax({
                    url:'<?php echo $this->createUrlReg('getStudentOverall'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:{
                        child_id:id,
                        coll_id:coll_id,
                    },
                    success:function(data){
                        if(data.state=="success"){
                            that.childData=data.data
                            $('#stuDetails').modal('show')
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            parsefloat(num) {
                return Number(num * 100).toFixed(2);
            },
            getInfo(type){
                let that = this
                this.type=type
                this.isShow=false
                $.ajax({
                    url:'<?php echo $this->createUrlReg('stepTemplateInfo'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:{
                        coll_id:coll_id,
                        step_id:step_id,
                        type:type
                    },
                    success:function(data){
                        if(data.state=="success"){
                            if(type=='unfilled'){
                                that.unList = data.data.list
                            }else{
                                that.list = data.data.list
                            }
                            that.unfilled = data.data.unfilled
                            that.filled = data.data.filled
                            that.total = data.data.count
                            that.statusList=data.data.statusList
                            that.logList=data.data.logList
                            that.extraData=data.data.extraData
                            that.isShow=true
                            if(that.resetStatusIndex!==''){
                                that.auditList(that.resetStatusIndex)
                            }
                            sessionStorage.setItem('tabType', type);
                            sessionStorage.setItem('listUpdated',  JSON.stringify(data.data.list));
                            that.$forceUpdate();
                            that.$nextTick(()=>{
                                that.DataTable(data)
                            })
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            DataTable(data){
                let that=this
                    that.$nextTick(()=>{
                        $('#stuDetails').modal('hide');
                        $('#nameSearchComp').html(data.data.nameSearchComp)
                        if(that.type=='filled'){
                            var table = $('#filled').DataTable({
                                aaSorting: [5, 'desc'], // 默认排序
                                paging: false,
                                info: false,
                                searching: false,
                                "destroy": true,
                                columnDefs: [ {
                                    "targets": 'nosort',
                                    "orderable": false
                                } ],
                            });
                        }else{
                            var table = $('#unfilled').DataTable({
                                aaSorting: [1, 'desc'], // 默认排序
                                paging: false,
                                info: false,
                                "destroy": true,
                                searching: false,
                            });
                        }
                    })

            },
            auditList(index,type){
                let that=this
                that.resetStatusIndex=index
                that.child_id = that.list[index]['childid']
                $.ajax({
                    url:'<?php echo $this->createUrlReg('getStepOneChildrenData'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:{
                        coll_id:coll_id,
                        step_id:step_id,
                        childid:that.child_id
                    },
                    success:function(data){
                        if(data.state=="success"){
                            that.rejectionList=data.data
                            if(data.data.step_data.remarks && data.data.step_data.remarks.length!=0){
                                that.staff_ids=that.rejectionList.step_data.remarks.email_to ?? []
                                that.staff_ids_backup=JSON.parse(JSON.stringify(that.staff_ids))
                                that.allDept=that.rejectionList.step_data.email_staff_list
                                that.allDept_backup=JSON.parse(JSON.stringify(that.allDept))
                                that.allDeptDef=that.rejectionList.step_data.email_staff_list_def
                                that.nurse=data.data.step_data.remarks
                                //记录原始数据
                                that.nurse_backup=JSON.parse(JSON.stringify(that.nurse))
                                if(that.nurse.isCOVID==1 && that.nurse.COVID){
                                    that.COVID=that.nurse.COVID
                                }
                                if(that.nurse.allergy=='1' && that.nurse.allergy){
                                    Vue.set(that.nurse, "otherAllergy",false);
                                    Vue.set(that.nurse, "medAllergy",false);
                                    Vue.set(that.nurse, "foodAllergy",false);
                                }
                                if(that.nurse.immunization==undefined || !Array.isArray(that.nurse.immunization)){
                                    that.nurse.immunization=[]
                                }
                            }else{
                                that.staff_ids=[]
                                that.allDept={}
                                that.nurse={
                                    longMedication:0,
                                    longMedicationDetail:'',
                                    medicalHistory:0,
                                    medicalHistoryDetail:'',
                                    allergy:0,
                                    otherAllergy:false,
                                    medAllergy:false,
                                    foodAllergy:false,
                                    otherAllergyDetailCn:'',
                                    otherAllergyDetailEn:'',
                                    medAllergyDetailCn:'',
                                    medAllergyDetailEn:'',
                                    foodAllergyDetailCn:'',
                                    foodAllergyDetailEn:'',
                                    immunization:[],
                                    immunizationDetail:'',
                                    physicalExamination:'',
                                    physicalExaminationDetail:'',
                                    COVIDDetail:'',
                                    COVID:[],
                                    isCOVID:'',
                                    otherDetailCn:'',
                                    otherDetailEn:'',
                                    noteNurseOnly:'',
                                }
                                that.COVID=[]
                            }
                            that.audit_status=''
                            that.comment=''
                            if(type){
                                let ids=['medicalHistoryDate0','medicalHistoryDate1','otherAllergyDate0','otherAllergyDate1','medAllergyDate0','medAllergyDate1','foodAllergyDate0','foodAllergyDate1']
                                $('#auditDialog').modal('show')
                                that.$nextTick(()=>{
                                    for(var i=0;i<ids.length;i++){
                                        $('#'+ids[i]).datepicker({
                                            dateFormat: 'yy-mm-dd',
                                        });
                                    }
                                })
                                if(that.COVID.length>0){
                                    that.$nextTick(()=>{
                                        for(var i=0;i<that.COVID.length;i++){
                                            $('#COVID_'+i).datepicker({
                                                dateFormat: 'yy-mm-dd',
                                                selectOtherMonths: false,
                                                showOtherMonths: false,
                                                changeMonth: true,
                                                changeYear: true,
                                                firstDay: 0,
                                                onSelect: function (date,str) {
                                                    let id=str.id
                                                    that.COVID[i-1].date=date
                                                }
                                            });
                                        }
                                    })
                                }
                            }
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            exportTab() {
                if(this.type=='filled'){
                    const filename ='医疗信息已填写.xlsx';
                    const ws_name = "SheetJS";
                    var exportDatas = [];
                    for(var i=0;i<this.list.length;i++){
                        var contact=''
                        for(var j=0;j<this.list[i].step_data.emergency_contact.length;j++){
                            contact+='联系人'+(j+1) + '：（姓名：'+this.list[i].step_data.emergency_contact[j].name+'电话：'+this.list[i].step_data.emergency_contact[j].mobile+'邮箱：'+this.list[i].step_data.emergency_contact[j].email+'）'
                        }
                        var data={
                            'ID':this.list[i].childid,
                            '<?php echo Yii::t('global', 'Name') ?>':this.list[i].name,
                            '<?php echo Yii::t('global', '头像') ?>':'<table><img src='+ this.list[i].avatar +' width=100></table>',
                            "<?php echo Yii::t('labels', 'Class') ?>":this.list[i].className,
                            "<?php echo Yii::t('labels', 'Date of Birth') ?>":this.list[i].birthday,
                            "<?php echo Yii::t('labels', 'Status') ?>":this.statusList[this.list[i].step_status],

                            "<?php echo Yii::t('labels', 'Barcode') ?>":this.list[i].barcode,
                            "<?php echo Yii::t('labels', 'Educational Student ID') ?>":this.list[i].educational_id,
                            "<?php echo Yii::t("labels",'中文法定名')?>": this.list[i].is_legel_cn_name==1 ? '是':'否',
                            "<?php echo Yii::t("labels",'Chinese Name')?>":this.list[i].cn_name,
                            "<?php echo Yii::t("labels",'Legal First Name')?>":this.list[i].first_name_en,
                            "<?php echo Yii::t("labels",'Legal Middle Name')?>":this.list[i].middle_name_en,
                            "<?php echo Yii::t("labels",'Legal Last Name')?>":this.list[i].last_name_en,
                            "<?php echo Yii::t("labels",'Preferred Name')?>":this.list[i].nick,
                            "<?php echo Yii::t("labels",'Gender')?>":this.list[i].gender == 1 ? '男' : '女',
                            "<?php echo Yii::t("labels",'Nationality')?>":this.list[i].country,
                            "<?php echo Yii::t("labels",'Language')?>":this.list[i].lang,
                            "<?php echo Yii::t("labels",'Father Name')?>":this.list[i].father_name,
                            "<?php echo Yii::t("labels",'Father Email')?>":this.list[i].father_email,
                            "<?php echo Yii::t("labels",'Father Mobile')?>":this.list[i].father_phone,
                            "<?php echo Yii::t("labels",'Mother Name')?>":this.list[i].mather_name,
                            "<?php echo Yii::t("labels",'Mother Email')?>":this.list[i].mather_email,
                            "<?php echo Yii::t("labels",'Mother Mobile')?>":this.list[i].mather_phone,
                            "入学日期":this.list[i].attend_school_time,
                            "<?php echo Yii::t("labels",'Dwelling Place')?>":this.list[i].address,
                            "<?php echo Yii::t("labels",'ID NO./Passport NO.')?>":this.list[i].identity,
                            "入学学年":this.list[i].first_startyear,

                            "提交时间":this.list[i].step_data.submitted_at,
                            "意向医院1":this.list[i].step_data.intended_hospitals[0],
                            "意向医院2":this.list[i].step_data.intended_hospitals[1],
                            "紧急联系人":contact,
                            "多动症":this.list[i].step_data.is_adhd==1?'是':'否',
                            "心脏病":this.list[i].step_data.is_heart_disease==1?'是':'否',
                            "过敏（食品、药品、其他）":this.list[i].step_data.allergies==1?'是':'否',
                            "耳部疾病（听力）":this.list[i].step_data.frequent==1?'是':'否',
                            "哮喘":this.list[i].step_data.asthma==1?'是':'否',
                            "肝炎":this.list[i].step_data.hepatitis==1?'是':'否',
                            "背部或脊柱问题":this.list[i].step_data.problems==1?'是':'否',
                            "消化系统疾病":this.list[i].step_data.gastrointertianl==1?'是':'否',
                            "骨折":this.list[i].step_data.fractures==1?'是':'否',
                            "皮肤病":this.list[i].step_data.skinProblems==1?'是':'否',
                            "糖尿病":this.list[i].step_data.diabetes==1?'是':'否',
                            "视力/色觉问题":this.list[i].step_data.visionProblems==1?'是':'否',
                            "癫病":this.list[i].step_data.seizureDisorde==1?'是':'否',
                            "肺结核":this.list[i].step_data.is_tuberculosis==1?'是':'否',
                            "咳嗽、咳痰持续2周以上":this.list[i].step_data.tuberculosisOne==1?'是':'否',
                            "反复咳出的痰中带血":this.list[i].step_data.tuberculosisTwo==1?'是':'否',
                            "反复发热持续2周以上":this.list[i].step_data.tuberculosisThree==1?'是':'否',
                            "2年以上结核":this.list[i].step_data.tuberculosisFour==1?'是':'否',
                            "过敏":this.list[i].step_data.allergies==1?'是':'否',
                            "食物过敏":this.list[i].step_data.allergies==1?this.list[i].step_data.allergies_food:'',
                            "药物过敏":this.list[i].step_data.allergies==1?this.list[i].step_data.allergies_medication:'',
                            "其他过敏":this.list[i].step_data.allergies==1?this.list[i].step_data.allergies_other:'',
                            "其他说明":this.list[i].step_data.other,
                            "定期使用药物":this.list[i].step_data.is_use_drugs==1?'是':'否',
                            "定期使用药物说明":this.list[i].step_data.is_use_drugs==1?this.list[i].step_data.use_drugs:'',
                            "护士审核":this.list[i].step_data.remarks!=null?'是':'否',
                            }
                        if(this.list[i].step_data.remarks!=null){
                            var immunization=''
                            if(this.list[i].step_data.remarks.immunization && this.list[i].step_data.remarks.immunization.length!=0){
                                for(var j=0;j<this.list[i].step_data.remarks.immunization.length;j++){
                                    immunization+=this.immunizationList[this.list[i].step_data.remarks.immunization[j]]+' '
                                }
                            }
                            var COVIDList=''
                            if(this.list[i].step_data.remarks.COVID && this.list[i].step_data.remarks.COVID.length!=0){
                                for(var k=0;k<this.list[i].step_data.remarks.COVID.length;k++){
                                    COVIDList+='第'+(k+1)+'针'+ this.list[i].step_data.remarks.COVID[k].date+' '
                                }
                            }
                            data=Object.assign(data,{
                                "入学体检":this.list[i].step_data.remarks.physicalExamination==2?'已提交':'未提交',
                                "入学体检备注":this.list[i].step_data.remarks.physicalExaminationDetail,
                                "长期用药需求":this.list[i].step_data.remarks.longMedication==2?'是':'否',
                                "长期用药需求备注":this.list[i].step_data.remarks.longMedicationDetail,
                                "既往病史":this.list[i].step_data.remarks.medicalHistory==2?'是':'否',
                                "既往病史备注":this.list[i].step_data.remarks.medicalHistoryDetailCn,
                                "Medical History":this.list[i].step_data.remarks.medicalHistoryDetailEn,
                                "过敏史":this.list[i].step_data.remarks.allergy==2?'是':'否',
                                "是否食物过敏":this.list[i].step_data.remarks.foodAllergy==true?'是':'否',//foodAllergy
                                "食物过敏备注":this.list[i].step_data.remarks.foodAllergyDetailCn,
                                "Food Allergy":this.list[i].step_data.remarks.foodAllergyDetailEn,
                                "其它过敏":this.list[i].step_data.remarks.otherAllergy==true?'是':'否',
                                "其它过敏备注":this.list[i].step_data.remarks.otherAllergyDetailCn,
                                "Other Allergies":this.list[i].step_data.remarks.otherAllergyDetailEn,
                                "是否药物过敏":this.list[i].step_data.remarks.medAllergy==true?'是':'否',
                                "药物过敏备注":this.list[i].step_data.remarks.medAllergyDetailCn,
                                "Medical Allergy":this.list[i].step_data.remarks.medAllergyDetailEn,
                                "疫苗":immunization,
                                "疫苗备注":this.list[i].step_data.remarks.immunizationDetail,
                                '新冠疫苗':COVIDList,
                                "新冠疫苗备注":this.list[i].step_data.remarks.COVIDDetail,
                                "其他备注":this.list[i].step_data.remarks.otherDetailCn,
                                "Other Information":this.list[i].step_data.remarks.otherDetailEn,
                            })
                        }
                        exportDatas.push(data)
                    }
                    var wb=XLSX.utils.json_to_sheet(exportDatas,{
                        origin:'A1',// 从A1开始增加内容
                        header: ['ID',
                            '<?php echo Yii::t('global', 'Name') ?>',
                            '<?php echo Yii::t('global', '头像') ?>',
                            '<?php echo Yii::t('labels', 'Class') ?>',
                            '<?php echo Yii::t('labels', 'Date of Birth') ?>',
                            '<?php echo Yii::t('labels', 'Status') ?>',
                            '<?php echo Yii::t('labels', 'Barcode') ?>',
                            '<?php echo Yii::t('labels', 'Educational Student ID') ?>',
                            '<?php echo Yii::t('labels','中文法定名')?>',
                            '<?php echo Yii::t('labels','Chinese Name')?>',
                            '<?php echo Yii::t('labels','Legal First Name')?>',
                            '<?php echo Yii::t('labels','Legal Middle Name')?>',
                            '<?php echo Yii::t('labels','Legal Last Name')?>',
                            '<?php echo Yii::t('labels','Preferred Name')?>',
                            '<?php echo Yii::t('labels','Gender')?>',
                            '<?php echo Yii::t('labels','Nationality')?>',
                            '<?php echo Yii::t('labels','Language')?>',
                            '<?php echo Yii::t('labels','Father Name')?>',
                            '<?php echo Yii::t('labels','Father Email')?>',
                            '<?php echo Yii::t('labels','Father Mobile')?>',
                            '<?php echo Yii::t('labels','Mother Name')?>',
                            '<?php echo Yii::t('labels','Mother Email')?>',
                            '<?php echo Yii::t('labels','Mother Mobile')?>',
                            '入学日期',
                            '<?php echo Yii::t('labels','Dwelling Place')?>',
                            '<?php echo Yii::t('labels','ID NO./Passport NO.')?>',
                            '入学学年',
                            '提交时间',
                            '意向医院1',
                            '意向医院2',
                            '紧急联系人',
                            '多动症',
                            '心脏病',
                            '过敏（食品、药品、其他）',
                            '耳部疾病（听力）',
                            '哮喘',
                            '肝炎',
                            '背部或脊柱问题',
                            '消化系统疾病',
                            '骨折',
                            '皮肤病',
                            '糖尿病',
                            '视力/色觉问题',
                            '癫病',
                            '肺结核',
                            '咳嗽、咳痰持续2周以上',
                            '反复咳出的痰中带血',
                            '反复发热持续2周以上',
                            '2年以上结核',
                            '过敏',
                            '食物过敏',
                            '药物过敏',
                            '其他过敏',
                            '其他',
                            '其他说明',
                            '定期使用药物',
                            '定期使用药物说明',
                            '护士审核',
                            '入学体检',
                            '入学体检备注',
                            '长期用药需求',
                            '长期用药需求备注',
                            '既往病史',
                            '既往病史备注',
                            '过敏史',
                            '是否食物过敏',
                            '食物过敏备注',
                            '其它过敏',
                            '其它过敏备注',
                            '是否药物过敏',
                            '药物过敏备注',
                            '疫苗',
                            '疫苗备注',
                            '新冠疫苗',
                            '新冠疫苗备注',
                            '其他备注']
                    });
                    const workbook = XLSX.utils.book_new();
                    XLSX.utils.book_append_sheet(workbook, wb, ws_name);
                    const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                    const blob = new Blob([wbout], {type: 'application/octet-stream'});
                    let link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = filename;
                    link.click();
                    setTimeout(function() {
                        // 延时释放掉obj
                        URL.revokeObjectURL(link.href);
                        link.remove();
                    }, 500);
                }else{
                    var title='医疗信息未填写.xlsx'
                    const ws_name = "SheetJS";
                    var exportDatas = [];
                    for(var i=0;i<this.unList.length;i++){
                        exportDatas.push(
                            {
                            'ID':this.unList[i].childid,
                            '<?php echo Yii::t('global', 'Name') ?>':this.unList[i].name,
                            "<?php echo Yii::t('labels', 'Class') ?>":this.unList[i].className,
                            "<?php echo Yii::t('labels', 'Date of Birth') ?>":this.unList[i].birthday,
                            "<?php echo Yii::t('labels', 'Status') ?>":this.statusList[this.unList[i].step_status],

                            "<?php echo Yii::t('labels', 'Barcode') ?>":this.unList[i].barcode,
                            "<?php echo Yii::t('labels', 'Educational Student ID') ?>":this.unList[i].educational_id,
                            "<?php echo Yii::t("labels",'中文法定名')?>": this.unList[i].is_legel_cn_name==1 ? '是':'否',
                            "<?php echo Yii::t("labels",'Chinese Name')?>":this.unList[i].cn_name,
                            "<?php echo Yii::t("labels",'Legal First Name')?>":this.unList[i].first_name_en,
                            "<?php echo Yii::t("labels",'Legal Middle Name')?>":this.unList[i].middle_name_en,
                            "<?php echo Yii::t("labels",'Legal Last Name')?>":this.unList[i].last_name_en,
                            "<?php echo Yii::t("labels",'Preferred Name')?>":this.unList[i].nick,
                            "<?php echo Yii::t("labels",'Gender')?>":this.unList[i].gender == 1 ? '男' : '女',
                            "<?php echo Yii::t("labels",'Nationality')?>":this.unList[i].country,
                            "<?php echo Yii::t("labels",'Language')?>":this.unList[i].lang,
                            "<?php echo Yii::t("labels",'Father Name')?>":this.unList[i].father_name,
                            "<?php echo Yii::t("labels",'Father Email')?>":this.unList[i].father_email,
                            "<?php echo Yii::t("labels",'Father Mobile')?>":this.unList[i].father_phone,
                            "<?php echo Yii::t("labels",'Mother Name')?>":this.unList[i].mather_name,
                            "<?php echo Yii::t("labels",'Mother Email')?>":this.unList[i].mather_email,
                            "<?php echo Yii::t("labels",'Mother Mobile')?>":this.unList[i].mather_phone,
                            "入学日期":this.unList[i].attend_school_time,
                            "<?php echo Yii::t("labels",'Dwelling Place')?>":this.unList[i].address,
                            "<?php echo Yii::t("labels",'ID NO./Passport NO.')?>":this.unList[i].identity,
                            "入学学年":this.unList[i].first_startyear,
                            });
                    }
                    var wb=XLSX.utils.json_to_sheet(exportDatas,{
                        origin:'A1',// 从A1开始增加内容
                        header: ['ID',
                            '<?php echo Yii::t('global', 'Name') ?>',
                            '<?php echo Yii::t('labels', 'Class') ?>',
                            '<?php echo Yii::t('labels', 'Date of Birth') ?>',
                            '<?php echo Yii::t('labels', 'Status') ?>'],
                    });
                    const workbook = XLSX.utils.book_new();
                    XLSX.utils.book_append_sheet(workbook, wb, ws_name);
                    const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                    const blob = new Blob([wbout], {type: 'application/octet-stream'});
                    let link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = title;
                    link.click();
                    setTimeout(function() {
                        // 延时释放掉obj
                        URL.revokeObjectURL(link.href);
                        link.remove();
                    }, 500);
                }

            },
            checkAll(e){
                this.child_ids=[]
                if(e.target.checked){
                    for(var i=0;i<this.list.length;i++){
                        if(this.list[i].step_status!=4){
                            this.child_ids.push(this.list[i].childid)
                        }
                    }
                    this.all=true
                }else{
                    this.all=false
                }
            },
            childCheck(e){
                let checkListLen=this.list.filter((item)=>{return item.step_status!=4});
                if(e.target.checked){
                    if(this.child_ids.length==checkListLen.length){
                        this.all=true
                    }else{
                        this.all=false
                    }
                }else{
                    this.all=false
                }
            },
            batchPass(modal){
                let that = this
                if(this.child_ids.length==0){
                    resultTip({error: 'warning', msg: '请选择学生'});
                    return
                }
                if(modal){
                    $('#tipsModal').modal('show')
                    return
                }
                this.loading=true
                this.btnCon=true
                this.btnTips=true
                $.ajax({
                    url:'<?php echo $this->createUrlReg('batchPass'); ?>',
                    type: 'post',
                    dataType:'json',
                    data:{
                        child_ids:this.child_ids,
                        step_id:step_id,
                        coll_id:this.coll_id
                    },
                    success:function(data){
                        if(data.state=="success"){
                            resultTip({
								msg: data.state
							});
                            that.getInfo(that.type)
                            $('#auditDialog').modal('hide')
                            $('#tipsModal').modal('hide')
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                        that.btnCon=false
                        that.loading=false
                        that.btnTips=false
                    },
                    error:function(data){
                        that.btnCon=false
                        that.loading=false
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            print(){
                let that = this
                if(this.child_ids.length==0){
                    resultTip({error: 'warning', msg: '请选择学生'});
                    return
                }
                let  child_ids = this.child_ids.toString()
                window.open('<?php echo $this->createUrlReg('batchPrint'); ?>&childid='+child_ids+'&step_id='+step_id+'&coll_id='+this.coll_id,'_blank');
            },
            resetStatus(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrlReg("stepAudit") ?>',
                    type: "get",
                    dataType: 'json',
                    data:{
                        child_id:this.child_id,
                        step_id:step_id,
                        audit_status:0,
                        coll_id:this.coll_id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
								msg: data.state
							});
                            that.getInfo(that.type)
                            // $('#auditDialog').modal('hide')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                    },
                })
            },
            saveInfo(){
                let that = this
                if(this.audit_status==''){
                    resultTip({
                        error: 'warning',
                        msg: '请选择审核状态'
                    });
                    return
                }
                that.btnCon=true
                var dataList={
                    child_id:this.child_id,
                    step_id:step_id,
                    audit_status:this.audit_status,
                    coll_id:this.coll_id,
                    comment: this.comment,
                }
                if(this.audit_status==3 || this.type_status==2){
                   var dataList=Object.assign({send_wechat:that.send_wechat?1:0},dataList)
                }
                $.ajax({
                    url:'<?php echo $this->createUrlReg('stepAudit'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:dataList,
                    success:function(data){
                        if(data.state=="success"){
                            resultTip({
								msg: data.state
							});
                            that.getInfo(that.type)
                            // $('#auditDialog').modal('hide')
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                        that.btnCon=false
                    },
                    error:function(data){
                        that.btnCon=false
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            bigImg(list){
                var opacityBottom = '<div class="opacityBottom" style = "display:none"><img class="bigImg" src="' + list + '"></div>';
                $(document.body).append(opacityBottom);
                this.toBigImg();//变大函数
            },
            toBigImg() {
                $(".opacityBottom").addClass("opacityBottom");//添加遮罩层
                $(".opacityBottom").show();
                $("html,body").addClass("none-scroll");//下层不可滑动
                $(".bigImg").addClass("bigImg");//添加图片样式
                $(".opacityBottom").click(function () {//点击关闭
                    $("html,body").removeClass("none-scroll");
                    $(".opacityBottom").remove();
                });
            },
            getSchool(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrlReg("schoolList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        step_id:step_id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.schoolList=data.data.list
                            that.school_id=data.data.school_id
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            showStaff(list){
                let that=this
                $.ajax({
                    url:'<?php echo $this->createUrlReg('getAllDepartment'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:{
                        step_id:step_id,
                        school_id:this.school_id
                    },
                    success:function(data){
                        if(data.state=="success"){
                            that.currentDept=data.data
                            that.allDept=Object.assign( that.allDept, data.data.user_info)
                            that.dep_name=''
                            that.searchText=''
                            if(list){
                                that.staffSelected= JSON.parse( JSON.stringify (that.staff_ids))
                                $("#addStaffModal").modal('show')
                            }
                            for(let key in that.currentDept.user_info){
                                if(that.staffSelected.indexOf(key+'')!=-1){
                                    Vue.set(that.currentDept.user_info[key], 'disabled', true);
                                }
                            }
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            staffUnassign(list,index){
                if(this.allDept[list]){
                    Vue.set(this.allDept[list], 'disabled', false);
                }
                this.staff_ids.splice(index,1)
            },
            showDepname(list){
                if(this.dep_name==list.dep_name){
                    this.dep_name=''
                    return
                }
                this.dep_name=list.dep_name
                for(let i=0;i<list.user.length;i++){
                    if(this.staffSelected.indexOf(list.user[i].uid)!=-1){
                        Vue.set(this.currentDept.user_info[list.user[i].uid], 'disabled', true);
                    }
                }
            },
            assignStaff(list,index,idx){
                if(idx=='search'){
                    this.searchStaffList[index].disabled=true
                }else{
                    Vue.set(this.currentDept.user_info[list.uid], 'disabled', true);
                }
                this.staffSelected.push(list.uid)
            },
            Unassign(list,index){
                if(this.currentDept.user_info[list]){
                    Vue.set(this.currentDept.user_info[list], 'disabled', false);
                }
                this.staffSelected.splice(index,1)
            },
            confirmSatff(){
                this.staff_ids= JSON.parse( JSON.stringify (this.staffSelected))
                $("#addStaffModal").modal('hide')
            },
            selectAll(list,index){
                list.user.forEach(item => {
                    if(!this.currentDept.user_info[item.uid].disabled && item.group_id==0){
                        this.staffSelected.push(item.uid)
                        Vue.set(this.currentDept.user_info[item.uid], 'disabled', true);
                    }
                });
            },
            batchDel(type){
                this.staffSelected=[]
                for(var key in this.currentDept.user_info){
                    if(this.staffSelected.indexOf(key)!=-1){
                        Vue.set(this.currentDept.user_info[key], 'disabled', true);
                    }else{
                        Vue.set(this.currentDept.user_info[key], 'disabled', false);
                    }
                }
            },
            saveRemarks(type){
                let that = this
                if(this.nurse.longMedication=='2' && (this.nurse.longMedicationDetail=='' || this.nurse.longMedicationDetail==null)){
                    resultTip({
                        error: 'warning',
                        msg: '请填写长期用药需求详情'
                    });
                    return
                }
                if(this.nurse.medicalHistory=='2' && (this.nurse.medicalHistoryDetailCn=='' || this.nurse.medicalHistoryDetailCn==null || this.nurse.medicalHistoryDetailEn=='' || this.nurse.medicalHistoryDetailEn==null)){
                    resultTip({
                        error: 'warning',
                        msg: '请填写既往病史需求详情'
                    });
                    return
                }
                if(this.nurse.medicalHistory=='2' && (this.nurse.medicalHistoryDate.length==0 || this.nurse.medicalHistoryDate[0]=='' || this.nurse.medicalHistoryDate[1]=='')){
                    resultTip({
                        error: 'warning',
                        msg: '请选择既往病史时间'
                    });
                    return
                }
                if((this.nurse.foodAllergy === 'true' || this.nurse.foodAllergy === true) && (this.nurse.foodAllergyDate.length==0 || this.nurse.foodAllergyDate[0]=='' || this.nurse.foodAllergyDate[1]=='')){
                    resultTip({
                        error: 'warning',
                        msg: '请选择食物过敏时间'
                    });
                    return
                }
                if((this.nurse.foodAllergy === 'true' || this.nurse.foodAllergy === true)&& (this.nurse.foodAllergyDetailCn==''  || this.nurse.foodAllergyDetailCn==null || this.nurse.foodAllergyDetailEn=='' || this.nurse.foodAllergyDetailEn==null)){
                    resultTip({
                        error: 'warning',
                        msg: '请填写食物过敏详情'
                    });
                    return
                }

                if((this.nurse.medAllergy === 'true' || this.nurse.medAllergy === true) && (this.nurse.medAllergyDate.length==0 || this.nurse.medAllergyDate[0]=='' || this.nurse.medAllergyDate[1]=='')){
                    resultTip({
                        error: 'warning',
                        msg: '请选择药物过敏时间'
                    });
                    return
                }
                if((this.nurse.medAllergy === 'true' || this.nurse.medAllergy === true) && (this.nurse.medAllergyDetailCn==''  || this.nurse.medAllergyDetailCn==null || this.nurse.medAllergyDetailEn=='' || this.nurse.medAllergyDetailEn==null)){
                    resultTip({
                        error: 'warning',
                        msg: '请填写药物过敏详情1'
                    });
                    return
                }

                if((this.nurse.otherAllergy === 'true' || this.nurse.otherAllergy === true)&& (this.nurse.otherAllergyDate.length==0 || this.nurse.otherAllergyDate[0]=='' || this.nurse.otherAllergyDate[1]=='')){
                    resultTip({
                        error: 'warning',
                        msg: '请选择其他过敏时间'
                    });
                    return
                }
                if((this.nurse.otherAllergy === 'true' || this.nurse.otherAllergy === true)&& (this.nurse.otherAllergyDetailCn==''  || this.nurse.otherAllergyDetailCn==null || this.nurse.otherAllergyDetailEn=='' || this.nurse.otherAllergyDetailEn==null)){
                    resultTip({
                        error: 'warning',
                        msg: '请填写其他过敏详情'
                    });
                    return
                }
                if(this.nurse.isCOVID==undefined || this.nurse.isCOVID==null){
                    Vue.set(this.nurse, "COVID", []);
                    Vue.set(this.nurse, "isCOVID", '');
                }
                if(this.nurse.isCOVID==1){
                    this.nurse.COVID=this.COVID
                }else{
                    Vue.set(this.nurse, "COVID", []);
                }
                this.nurse.email_to=this.staff_ids
                $.ajax({
                    url:'<?php echo $this->createUrlReg('saveRemarks'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:{
                        child_id:this.child_id,
                        step_id:step_id,
                        remarks:this.nurse,
                        coll_id:this.coll_id,
                        type:type
                    },
                    success:function(data){
                        if(data.state=="success"){
                            resultTip({
								msg: data.state
							});
                            that.getInfo(that.type)
                            // $('#auditDialog').modal('hide')
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            getLength(data) {
                if (Array.isArray(data)) {
                    return data.length;
                } else if (typeof data === 'object' && data !== null) {
                    let keys = Object.keys(data);
                    return keys.length;
                } else {
                    return 'Invalid data';
                }
            }
        }
    })



</script>
<style>
    .staffInput{
        border: 1px solid #ccc;
        padding: 8px;
        border-radius: 4px;
        color: #333;
        display:flex;
        align-items:center;
        cursor: pointer;
    }
    .borderLeftpos {
        display: inline-block;
        border-left: 1px solid #E4E7ED;
        height: 100%;
        width: 1px;
        position: absolute;
        left: 50%;
    }
    .allCheck{
        position: absolute;
        right: 10px;
        top: 0;
    }
    .border{
        border: 1px solid #DCDFE6;
        padding: 16px;
        border-radius: 4px;
    }
    .listMedia{
        padding:8px;
        border: 1px solid #fff;
    }
    .listMedia:hover{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        border: 1px solid #4D88D2;
        cursor: pointer;
    }
    .img42{
      width: 42px;
      height: 42px;
      object-fit: cover;
      border-radius:50%;
    }
    a[data-status="0"] {
        cursor: default;
        text-decoration: none;
    }
</style>