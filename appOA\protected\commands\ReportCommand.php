<?php

class ReportCommand extends CConsoleCommand {
    public $batchNum = 200;

	public function init(){
		Yii::setPathOfAlias('common', dirname(__FILE__) . '/../../../common');
	}

    /**
     * @param int $surveyId
     * @param string $branchId
     * 生成调查问卷(员工、家长都适用)
     */
    public function actionGenSurveyReport($surveyId=0, $branchId='all') {
        $surveyId = intval($surveyId);
        if($surveyId) {
            Yii::import('common.components.general.CommonContentFetcher');

            $crit = new CDbCriteria();
            $crit->compare('status', 10);
            $branches = CHtml::listData(Branch::model()->findAll($crit), 'branchid', 'abb');
            if ( $branchId != "all" && in_array($branchId, array_keys($branches))) {
                $cycleBranches[$branchId] = $branches[$branchId];
            } else {
                $cycleBranches = $branches;
            }

            foreach($cycleBranches as $_branchId => $_branchTitle){
                echo "---> processing " . $_branchTitle . ' ('.$_branchId . ")\n";
                CommonContentFetcher::genSurveyReport($surveyId, $_branchId);
                echo "\n      .......completed!" . "\n\n";
            }
        }
    }

    public function actionExportStaffSurveyReport($surveyId=0) {
        $surveyId = intval($surveyId);
        if($surveyId) {
            Yii::import('common.models.survey.*');
            $crit = new CDbCriteria();
            $crit->compare('status', 10);
            $branches = CHtml::listData(Branch::model()->findAll($crit), 'branchid', 'abb');
            $surveyModel = WSurvey::model()->findByPk($surveyId);
            $templateId = $surveyModel->getOnlyTemplateId();
            $topicData = WSurveyTopic::model()->getSurveyTopics($templateId);

            foreach($topicData as $_topic) {
                $topicIds[] = $_topic['id'];
            }

            Yii::import('common.components.general.CommonContentFetcher');
            $reportData = CommonContentFetcher::getStaffSurveyReportData($surveyId);

//            print_r($reportData);return;
//            $this->excelHeader(sprintf("%s %s", $surveyModel->title_cn, $surveyModel->title_en));
            $csv = '';
            $csv .= _t("Entity");
            $csv .= _t("Question");
            $csv .= _t("Points");
            $csv .= _t("Votes");
            $csv .= "\n";

            foreach($branches as $branchId=>$branchTitle){
                if(isset($reportData[$branchId])){
                    foreach($topicIds as $_index=>$_topicId){
                        $num = $_index + 1;
                        if(isset($reportData[$branchId][$_topicId])){
                            for($v=5;$v>0;$v--){
                                $csv .= _t($branchTitle);
                                $csv .= _t("T" . $num);
                                $csv .= _t($v);
                                $csv .= isset($reportData[$branchId][$_topicId]['data'][0][$v]) ? _t(intval($reportData[$branchId][$_topicId]['data'][0][$v])) : '';
                                $csv .= "\n";
                            }
                        }
                    }
                }
            }
            file_put_contents('d:\a.csv', $csv);
        }
    }

    private function excelHeader($title="MyFile")
    {
        header("Content-type:application/vnd.ms-excel");
        header("Content-Disposition:filename=".$title."_child_list.xls");
    }


}

function _t($str='', $withT=true)
{
    $str = "\"".$str."\"";
    $str = iconv("UTF-8", "GBK", $str);
    return ($withT) ? $str . "," : $str;
}