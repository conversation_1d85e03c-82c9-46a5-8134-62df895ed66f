<?php

class WeChatBasedv2Controller extends Controller
{
	public $appId;
	public $appSecret;
	public $account;
	public $siteTitle;

	public $openid;
	public $access_token; // web
	public $unionid;

	public $wechatUser;
	public $parent;
	public $childObjs;
	public $activeChildids = array();
	public $campusId;

	public $layout = '//layouts/mainv2';

	private $msg = array();

	public $state = 'ds';

	public function init()
	{
		Yii::import('common.models.wechat.*');
		// 设置语言
		$this->setLanguage();
		if ($state = Yii::app()->request->getParam('state', '')) {
			Yii::app()->request->cookies['state'] = new CHttpCookie('state', $state);
		}else{
			$state = Yii::app()->request->cookies['state'] ? Yii::app()->request->cookies['state']->value : 'ds';
		}
		$this->state = $state;
		$this->setAppid($state);

		$openid = Yii::app()->request->cookies['openid'] ? Yii::app()->request->cookies['openid']->value : Yii::app()->request->getParam('openid', '');
		$access_token = Yii::app()->request->cookies['access_token'] ? Yii::app()->request->cookies['access_token']->value : Yii::app()->request->getParam('access_token', '');
		$unionid = Yii::app()->request->cookies['unionid'] ? Yii::app()->request->cookies['unionid']->value : Yii::app()->request->getParam('unionid', '');
		Yii::app()->theme = 'mobile2';
		
		if ($openid && $access_token){
			$url = 'https://api.weixin.qq.com/sns/auth?access_token=%s&openid=%s';
			$ret = json_decode(file_get_contents(sprintf($url, $access_token, $openid)));
Yii::log(json_encode($ret));
			if (isset($ret->errcode) && $ret->errcode == 0){
				$this->openid = $openid;
				$this->access_token = $access_token;
				$this->unionid = $unionid;
			}
			else {
				unset(Yii::app()->request->cookies['openid']);
				unset(Yii::app()->request->cookies['access_token']);
				unset(Yii::app()->request->cookies['unionid']);
				$url = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb1a42b81111e29f3&redirect_uri=http://www.ivyonline.cn/wechat/user/bind/state/ds&response_type=code&scope=snsapi_userinfo&state=ds&connect_redirect=1#wechat_redirect';
				$this->redirect($url);
			}
		}
		elseif (isset($_GET['code'])) {
			$code = $_GET['code'];
Yii::log($code);
			$url = 'https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=authorization_code';
			$ret = json_decode(file_get_contents(sprintf($url, $this->appId, $this->appSecret, $code)));
			if (isset($ret->openid) && isset($ret->access_token) && isset($ret->unionid)) {
Yii::log($ret->openid);
Yii::log($ret->access_token);
Yii::log($ret->unionid);
				Yii::app()->request->cookies['openid'] = new CHttpCookie('openid', $ret->openid);
				Yii::app()->request->cookies['access_token'] = new CHttpCookie('access_token', $ret->access_token);
				Yii::app()->request->cookies['unionid'] = new CHttpCookie('unionid', $ret->unionid);
				$this->openid = $ret->openid;
				$this->access_token = $ret->access_token;
				$this->unionid = $ret->unionid;

				$pathInfo = Yii::app()->request->pathInfo;
				$redirectUrl = Yii::app()->createUrl($pathInfo) . "/?state=$this->state&openid=$this->openid&access_token=$this->access_token&unionid=$this->unionid";
Yii::log($redirectUrl);
				$this->redirect($redirectUrl);
			}
		}
		else {
			$this->layout = '//layouts/mainv2';
			$this->render("//../modules/wechat/views/bind/nowechat");
			Yii::app()->end();
		}
		$this->setWechatUser();
	}

	private function setAppid($state)
	{		
		$config = array(
			'ivy' => array(
				'appId' => 'wx903fba9d4709cf10',
				'appSecret' => '388486edf736f1c078c808f291ce0075',
				'siteTitle' => Yii::t("global","IvyOnline"),
			),
			'ds' => array(
				'appId' => 'wxb1a42b81111e29f3',
				'appSecret' => '7fec437b1b5cbbb64b32e170e6857a7f',
				'siteTitle' => Yii::t("global","Daystar Online"),
			),
		);
		if ($state) {
			$this->appId = $config[$state]['appId'];
			$this->account = $state;
			$this->appSecret = $config[$state]['appSecret'];
			$this->siteTitle = $config[$state]['siteTitle'];
			if ($state == 'ds') {
				Yii::app()->params['unIvy'] = true;
			}
		}
	}

	public function setLanguage()
	{
		$lang = 'zh_cn';
		if(isset($_GET['lang'])){
			$lang = strtolower($_GET['lang']);
			if(!in_array($lang, array("en_us","zh_cn"))){
				$lang = "en_us";
			}
			Mims::setLangCookie($lang);
		}else{
			$langcookie = Yii::app()->request->cookies['child_mims_lang'];
			if (isset($langcookie->value)) {
				$lang = $langcookie->value;
			}
		}
		Yii::app()->language = $lang;
	}

	public function setWechatUser()
	{
		$this->wechatUser = WechatUser::model()->findByPk($this->openid);
		$userid = '';
		if ($this->wechatUser) {
			$userid = $this->wechatUser->userid;
		}else{
			$userid = Yii::app()->session['userid'];
		}
		if ($userid) {
			$this->setParent($userid);
		}

	}

	public function setParent($userid)
	{
		$this->parent = IvyParent::model()->findByPk($userid);
		if (!empty($this->parent) && count(@unserialize($this->parent->childs))) {
			$cids = @unserialize($this->parent->childs);
			$this->childObjs = ChildProfileBasic::model()->findAllByPk($cids, array("index" => "childid","order"=>"childid DESC"));

			foreach($this->childObjs as $child){
				if($child->status < 100){
					$this->activeChildids[] = $child->childid;
					if(!empty($child->schoolid)){
						$this->campusId = $child->schoolid;
					}
				}
			}
			//孩子id从大到小排列
			rsort($this->activeChildids);
		}
	}

	public function addMessage($key = '', $value='') {
		$accept = array('referer', 'refresh', 'state', 'message', 'callback', 'data', 'error');
		if ( in_array($key, $accept) ){
			$this->msg[$key]=$value;
		}
	}

	public function showMessage() {
		echo CJSON::encode($this->msg);
		Yii::app()->end();
	}

	public function createUrl($route, $params = array(), $ampersand = '&', $parentOnly = false)
	{
		if (!$parentOnly) {
			if (empty($params['openid'])) {
				$params['openid'] = $this->openid;
			}
			if (empty($params['access_token'])) {
				$params['access_token'] = $this->access_token;
			}
			if (empty($params['state'])) {
				$params['state'] = $this->state;
			}
		}
		return parent::createUrl($route, $params, $ampersand);
	}
}
