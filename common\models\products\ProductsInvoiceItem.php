<?php

/**
 * This is the model class for table "ivy_products_invoice_item".
 *
 * The followings are the available columns in table 'ivy_products_invoice_item':
 * @property integer $id
 * @property integer $cid
 * @property integer $pid
 * @property integer $paid
 * @property integer $childid
 * @property integer $classid
 * @property string $school_id
 * @property double $unit_price
 * @property integer $num
 * @property integer $iid
 * @property integer $invoice_id
 * @property integer $updated_userid
 * @property integer $updated_time
 * @property integer $status
 */
class ProductsInvoiceItem extends CActiveRecord
{
    public $countnum;
    public $totalAmount;
    public $childsNextClass;
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_products_invoice_item';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('cid, pid, paid, childid, classid, school_id, unit_price, num, iid, invoice_id, updated_userid, updated_time', 'required'),
			array('cid, pid, paid, childid, classid, num, iid, invoice_id, updated_userid, updated_time, status', 'numerical', 'integerOnly'=>true),
			array('unit_price', 'numerical'),
			array('school_id, refund_info', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, cid, pid, paid, childid, classid, school_id, unit_price, num, iid, invoice_id, updated_userid, updated_time, status, refund_info', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'invoice' => array(self::BELONGS_TO, 'ProductsInvoice', array('iid'=>'id')),
            'product' => array(self::HAS_ONE, 'Products', array('id'=>'pid')),
            'attr' => array(self::HAS_ONE, 'ProductsAttr', array('id'=>'paid')),
            'nextYear' => array(self::HAS_ONE, 'ChildReserve', array('childid'=>'childid')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'cid' => 'Cid',
			'pid' => 'Pid',
			'paid' => 'Paid',
            'childid' => Yii::t('user','Child Name'),
            'classid' => Yii::t('teaching','class'),
            'school_id' => Yii::t('user','School Name'),
			'unit_price' => Yii::t('payment','Amount'),
			'num' => Yii::t('glogob','商品数量'),
			'iid' => 'Iid',
			'invoice_id' => 'Invoice',
			'updated_userid' => 'Updated Userid',
			'updated_time' => 'Updated Time',
			'status' => 'Status',
			'refund_info' => 'Refund Info',
            'childsNextClass'=>Yii::t('user','下学年班级')
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('cid',$this->cid);
		$criteria->compare('pid',$this->pid);
		$criteria->compare('paid',$this->paid);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('school_id',$this->school_id,true);
		$criteria->compare('unit_price',$this->unit_price);
		$criteria->compare('num',$this->num);
		$criteria->compare('iid',$this->iid);
		$criteria->compare('invoice_id',$this->invoice_id);
		$criteria->compare('updated_userid',$this->updated_userid);
		$criteria->compare('updated_time',$this->updated_time);
		$criteria->compare('status',$this->status);
		$criteria->compare('refund_info',$this->refund_info);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return ProductsInvoiceItem the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
