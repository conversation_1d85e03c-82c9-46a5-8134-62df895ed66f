<style>
     [v-cloak] {
        display: none;
    }
    .flexWidth {
        width: 120px
    }
    .text-ellipsis {
        text-overflow: -o-ellipsis-lastline;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
    }
    .lineHeight{
        line-height:48px
    }
    .image{
        width: 48px; 
        height: 48px;
        object-fit:cover;
    }
    .point{
        width: 5px;
        height: 5px;
        display: inline-block;
        
        border-radius: 50%;
        margin-right: 5px;
        margin-bottom: 1px;
    }
    .red{
        background: #d9534f;
    }
    .green{
        background: #5cb85c;
    }
    .gray{
        color:#999;
        background:#999;
    }
    .yellow{
        background: #f0ad4e;
    }
    .parentReply{
        width:60%;
        margin:0 auto
    }
    .loading{
        width:98%;
        height:100%;
        background:#fff;
        position: absolute; 
        opacity: 0.5;
        z-index: 99
    }
    .loading span{
        width:100%;
        height:100%;
        display:block;
        background: url("<?php echo Yii::app()->theme->baseUrl?>/images/loading.gif")no-repeat center center ;
    }
    .fontBold{
        font-weight: bold;
    }
    .maxHieght{
        max-height:300px;
        overflow-y:auto;
        border: 1px solid #ccc;
        padding: 10px;
        border-radius: 5px;
    }
    .tooltipBtn{
        padding: 0 0.6em 0;
        font-size: 12px;
    }
    .image{
        width: 48px; 
        height: 48px;
        object-fit:cover;
    }
    .questionImage{
        width: 40px; 
        height: 40px;
        object-fit:cover;
    }
    .unsubmitted{
        position: absolute;
        border-radius: 0px 100px 100px 0px;
        background: #D9534F;
        left: 0;
        top: 0;
        color: #fff;
        padding: 1px 5px;
    }
    .pt5{
        padding-top:5px
    }
    .danger{
        background:#d9534f;
        color:#fff
    }
    .defaultBg{
        background:#e9e9e9;
        color:#666
    }
    .warning{
        color:#f0ad4e;
    }
    .dangerColor{
        color:#d9534f;
    }
    .express{
        color:#4D88D2
    }
    .absoluteImg{
        position: absolute;
        left: -16px;
        top: -18px;
    }
    .absoluteImg15{
        position: absolute;
        left: -16px;
        top: -16px;
    }
    .email{
        background: #E7F4FF;
        color: #428BCA;
        display: inline-block;
        margin-top: 11px;
        padding: 5px 10px;
        border-radius: 20px;
    }
    .content img{
        max-width:100%;
        margin-bottom: 1em; 
    }
    .sendGroup{
        width:6px;
        height:6px;
        background:#666666FF;
        border-radius:50%;
        display: inline-block;
        margin-right:10px
    }
    .badgeNum{
        display: inline-block;
        width: 6px;
        height: 6px;
        background: #d9534f;
        border-radius: 50%;
        margin-top: 6px;
    }
    .borderTop{
        border-top:1px solid #ddd;
    }
    .borderBto{
        border-bottom:1px solid #ddd;
    }
    .image32{
        width: 32px; 
        height: 32px;
        object-fit:cover;
    }
    .lineHeight32{
        line-height:32px
    }
    .borderOnline{
        display:inline-block;
        width:28px;
        height:28px;
        border:1px solid #ccc;
        border-radius:50%;
        text-align:center;
        line-height:25px;
        font-size:14px;
        margin:0 12px 12px 0
    }
    .borderRight{
        border-right:1px solid #E4E7ED
    }
    .borderOnline:hover{
        background:#4D88D2;
        color:#fff;        
    }
    .option{
        width: 22px;
        height: 17px;
        /* background: #759FFC; */
        border-radius: 9px;
        display: inline-block;
        text-align:center;
        line-height:17px;
        color:#fff
    }
    .optionFilter{
        display: inline-block;
        width:44px;
        height:24px;
        text-align:center;
        line-height:24px;
        border-radius:5px;
        font-weight:600
    }
    .optionFilter:hover{
        background:#F7F7F8;
        color:#333;
    }
    .optionFilterActive{
        color:#fff;
        background:#4D88D2
    }
    .hoursMinutes{
        width:70px;
        float:left
    }
    .p0{
        padding:0
    }
    .gradeList{
        padding: 10px 16px 16px;
        background: #F7F7F8;
    }
    .questionsActive{
        background:#4D88D2;
        color:#fff
    }
    .shareContent{
        width:375px;
        /* border-radius: 18px; */
        margin:0 auto
    }
    .shareStatus{
        position: absolute;
        left:0;
        top:0
    }
    .bgPto{
        background-repeat: no-repeat;
        background-size: contain;
    }
    .sharePto .logo{
        width:150px;
        margin-top:32px;
    }
    .sharePto .title{
        font-size: 26px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #FFFFFF;
        line-height: 38px;
        margin-top:24px
    }
    .sharePto .view{
        width: 100%;
        margin-top:8px;
        background: #E69119;       
        font-size: 12px;
        color:#fff;
        padding:6px
    }
    .shareContent .contentText{
        margin:16px 16px 0;
        border-radius: 16px;
    }
    .contentText .title{
        font-size:16px;
        line-height: 20px;
        margin-bottom:4px
    }
    .contentText .summary{
        font-size: 13px;
        line-height: 19px;
        margin-bottom:4px
    }
    .shareContent .bottom{
        margin:0 16px
    }
    .shareContent .bottom img{
        width: 100%;
        margin-bottom:16px
    }
    .contentText .wechat{
        border-top: 1px dashed #D0D0D0;
        margin-top: 20px;
        text-align: center;
    }
    .wechat{
        justify-content: center;
        align-items: center;
    }
    .fontWight{
        font-weight:600
    }
    .white{
        color:#fff
    }
    
    .Thumbnail{
        position: absolute;
        right: 0;
        top: 0;
        width:100px;
        text-align:center
    }
    .Thumbnail img{
        width:90px;
        padding:10px
    }
    .Thumbnail div{
        background:#F7F7F8
    }
    .Thumbnail .checkImg{
        border: 2px solid #4D88D2;
        border-radius: 8px;
    }
    .wechatImg{
        width: 125px;
        height: 125px;
        padding: 5px;
        background: #fff
    }
    .shareAvatar{
        width:40px;
        height:40px;
        object-fit:cover;
        border:1px solid #fff
    }
    .white08{
        opacity: 0.8;
    }
    .p24{
        padding:24px !important
    }
    .p8{
        padding:8px
    }
    .Options{
        position: absolute;
        left: 0;
        top: 0;
        width:200px
    }
    .pt2{
        padding-top:2px
    }
    .search{
        height: 60px;
    }
    .search .searchDiv{
        width: 600px;
        text-align: center;
        margin: 0 auto;
    }
    .delSearchText{
        position: absolute;
        left: -25px;
        top: 10px;
        z-index: 8;
        color: #ccc;
    }
    mark {
        background: yellow;
        padding: 0;
    }
    .bg7 {
        background-color: #777777 !important;
    }
    .wechatQrcode{
        display: inline-block;
        padding: 6px 10px;
        color: #50B674;
        background: #FFFFFF;
        border-radius: 4px;
        border: 1px solid #50B674;
        cursor: pointer;
    }
    .wechatQrcode img{
        width:16px;
        float:left;
        border-radius:50%
    }
    .qrcodeBox{
        position: absolute;
        left: 0px;
        top: 45px;
        width: 290px;
        padding:20px 10px;
        border: 1px solid #ddd;
        background: #fff;
        border-radius: 4px;
        text-align:center;
        font-size:14px;
        color:#333;
        box-shadow: 0px 2px 8px 0px rgb(0 0 0 / 20%);
        z-index:9
    }
    .qrcodeBox .qrcodeWechat{
        width:124px;
        height:124px;
        margin:0 auto
    }
    .wechatIcon{
        height:16px;
        position: absolute;
        bottom: 0;
        right: 0;
        width: 16px;
    }
    .avatarWechat{
        width:60px !important;
        height:60px  !important;
        border-radius:50%
    }
    .resetBtn{
        float:right;
        padding-top:2px
    }
    .schoolTitle,.schoolTitle1{
        color: #fff;
        font-size: 16px;
        margin-top: 8px;
        position: relative;
        display: inline-block;
    }
    .schoolTitle:before, .schoolTitle:after {
        content: ''; 
        position: absolute; 
        top: 50%;
        background: #fff; 
        width: 25px;
        height: 1px;
    }
    .schoolTitle:before,.schoolTitle1:before{
        left: -35px;
    }
    .schoolTitle:after,.schoolTitle1:after {
        right: -35px;
    }
    .schoolTitle1:before, .schoolTitle1:after {
        content: ''; 
        position: absolute; 
        top: 50%;
        background: #061F9D; 
        width: 25px;
        height: 1px;
    }
    .fileImg{
        width:72px;
        height:72px;
        object-fit: cover;
        display: inline-block;
        margin-bottom:10px;
        cursor: pointer;
    }
    .fileLink{
        background: #F7F7F8;
        display: inline-block;
        border-radius: 4px;
        border: 1px solid #EBEDF0;
        padding: 6px 12px;
        font-size: 14px;
        margin-right: 16px;
        margin-bottom:8px;
        line-height:20px
    }
    .fileLink img{
        width:20px
    }
    .contentAvatar{
        width: 44px;
        height: 44px;
        border-radius: 50%;
        object-fit:cover;
    }
    .imgLi{
        list-style: none;
        padding-left: 0;
    }
    .imgLi li{
        display:inline-block
    }
    .m0{
        margin:0
    }
    .category{
        background: #E7F4FF;
        border-radius: 100px;
        padding:5px 10px
    }
    .selectTeacher{
        width:300px;
        max-height: 300px;
        position: absolute;
        background: #fff;
        border: 1px solid #E8EAED;
        border-radius: 3px;
        top:35px;
        overflow-y: auto;
        z-index:1;
        left:0px;
        padding:8px 8px 16px 8px ;
        box-shadow: 0px 0px 6px 0px rgba(0,0,0,0.04), 0px 2px 4px 0px rgba(0,0,0,0.12);
    }
    .selectName{
        height:30px;
        line-height:30px;
        padding-left:10px
    }
    .selectName:hover{
        background:#EEF4FB
    }
    .inputSelect{
        width:300px;
    }
    .tagLabel{
        background: #EBEDF0;
        font-size: 12px;
    }
</style>
<div class="container-fluid" id='container' v-cloak  @click="hideQrcode">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Teaching Tasks'), array('//mteaching/default/index')) ?></li>
        <li><?php echo Yii::t('newDS', 'Notice') ?></li>
        <li><?php echo Yii::t("newDS", "Notice List");?></li>
    </ol>
    <div class="row">
        <div class="col-md-2 col-sm-12">
            <div class="list-group" id="classroom-status-list">
                <a href="<?php echo $this->createUrl('index'); ?>" class="list-group-item status-filter active"><?php echo Yii::t("newDS", "Notice List");?></a>
                <a href="<?php echo $this->createUrl('feedback'); ?>" class="list-group-item status-filter"><span v-if='toReplyNum>"0"' class='badgeNum pull-right'></span><?php echo Yii::t("newDS", "Parent Feedback");?></a>
            </div>
            <div class="text-center" v-if='qrcodeImg!=""'>
                <h5><?php echo Yii::t("newDS", "Preview on wechat");?></h5>
                <p><img class='qrcodeImg' :src="qrcodeImg" alt=""></p>
            </div>
            <div class='wechatQrcode mr20 relative' @click.stop='showUnWechatQrcode' v-if='wechatData.state==0'>
                <div>
                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/wechatDM.png' ?>" alt="">
                    <span class='ml4'><?php echo Yii::t("directMessage", "Wechat Notification"); ?></span> 
                </div>
                <div class="qrcodeBox"  v-if='scanQrcodeBox'>
                    <div class='font14 color3'><strong><?php echo Yii::t("directMessage", "Wechat Notification"); ?></strong></div>
                    <div class='mt8 font12 color6'><?php echo Yii::t("directMessage", "Response parent feedback on wechat!"); ?></div>
                    <div>
                        <div id='wechatQrcodeBind' alt="" class='qrcodeWechat mt24'></div>
                        <div class='font12 mt16 color6 '><?php echo Yii::t("directMessage", "Wechat scan to bind your account."); ?></div>
                    </div>
                </div>
            </div>
            <div class='wechatQrcode mr20 relative' @click.stop='qrcodeBox=true'  v-if='wechatData.state==1'>
                <div>
                    <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/wechatDM.png' ?>" alt="" v-if='wechatData.headimgurl=="" || wechatData.headimgurl==null'>
                    <img :src="wechatData.headimgurl" alt="" v-if='wechatData.headimgurl!=""'>
                    <span class='ml4'><?php echo Yii::t("directMessage", "Wechat Enabled"); ?></span>  
                </div>
                <div  class="qrcodeBox"  v-if='qrcodeBox'>
                    <div class='font14 color3'><strong><?php echo Yii::t("directMessage", "Wechat Enabled"); ?></strong></div>
                    <div class='mt8 font12 color6'><?php echo Yii::t("directMessage", "Response parent feedback on wechat!"); ?></div>
                    <div class='relative inline-block' v-if='wechatData.headimgurl!=""'>
                        <img :src="wechatData.headimgurl" alt="" class='avatarWechat mt16'>
                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/wechatDM.png' ?>" alt="" class='wechatIcon'>
                    </div>
                    <div class='font12 mt16 color6 '  v-if='wechatData.nickname!=""'>{{wechatData.nickname}}</div>
                    <div class='font12 mt24 mt16 color6 '><button class="btn btn-default" type="button" @click.stop='unbind()'><?php echo Yii::t("directMessage", "Unbind your account"); ?></button></div>
                </div>
            </div> 
        </div>
        <div class="col-md-10 col-sm-12" >
            <div class='loading'  v-if='initLoading'>
                <span></span>
            </div>
            <div>
                <div class="panel panel-default">
                    <div class="panel-heading flex">
                        <h4 class='flex1 m0'>
                            <span><?php echo Yii::t("newDS", "Notice List");?></span>
                            <div class="btn-group mr10">
                                <button type="button" class="btn btn-link dropdown-toggle  btn-sm" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                {{categoryFilter==''?'<?php echo Yii::t("newDS", "All Cateogry");?>':showCategoryList(categoryFilter)}} <span class="caret"></span>
                                </button>
                                <ul class="dropdown-menu" style='min-width:100px'>
                                    <li><a href="javascript:void(0)" @click='categoryFilter="",getList()'><?php echo Yii::t("newDS", "All Cateogry");?></a></li>

                                    <li v-for='(list,index) in categoryList'><a href="javascript:void(0)"  @click='categoryFilter=list.key,getList()'>{{list.value}}</a></li>
                                </ul>
                            </div>
                        </h4>
                        <div class='pull-right'>
                            <div class="btn-group mr10">
                                <button type="button" class="btn btn-link dropdown-toggle  btn-sm" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                {{gradeGroupType==''?'<?php echo Yii::t("newDS", "All Division");?>':gradeGroupList[gradeGroupType]}} <span class="caret"></span>
                                </button>
                                <ul class="dropdown-menu" style='min-width:100px'>
                                    <li><a href="javascript:void(0)" @click='gradeGroupType="",getList()'><?php echo Yii::t("newDS", "All Division");?></a></li>

                                    <li v-for='(list,key,index) in gradeGroupList'><a href="javascript:void(0)"  @click='gradeGroupType=key,getList()'>{{list}}</a></li>
                                </ul>
                            </div>
                            <a href="<?php echo $this->createUrl('edit', array('id' => 0)); ?>" class="btn  btn-primary btn-sm pull-right" role="button"><span class='glyphicon glyphicon-plus'></span> <?php echo Yii::t("newDS", "New");?></a>
                        </div>
                    </div>
                    <div class="panel-body relative" >    
                        <div class='search'>
                            <div class='searchDiv flex align-items'> 
                                <div class="input-group flex1">
                                    <span class="input-group-addon text-primary"  style='background:#fff'>
                                        <a href='javascript:;' @click='chooseTeacher()'>{{Object.keys(staff).length==0?'<?php echo Yii::t("global", "All");?>':staff.name}} <span class="caret"></span></a> 
                                    </span>
                                    <input type="text" v-model='searchText' placeholder="<?php echo Yii::t("newDS", "Input text to search title or content");?>" class="form-control" @keyup.enter='getList()'> 
                                    <a href="javascript:;" class="input-group-addon relative"  @click='getList()'><span class="glyphicon glyphicon-search"></span> <?php echo Yii::t("global", "Search");?>
                                        <span class='glyphicon glyphicon-remove delSearchText' v-if='searchText!=""'  @click='searchText="";getList()'></span>
                                    </a>
                                </div>
                                <div style='width:102px;text-align:left' class='ml16'>
                                    <span class='text-primary  cur-p' v-if='staff.name || isSearch'  @click='staff="";searchText="";getList()'><?php echo Yii::t("newDS", "Clear Query Filters");?></span>
                                </div>
                            </div>
                        </div>  
                        <div class='loading'  v-if='loading'>
                            <span></span>
                        </div>
                        <p class='text-center mt20 color9' v-if='dataList.length==0 && !loading && !initLoading' ><?php echo Yii::t("newDS", "No items found");?></p>
                        <div v-else>
                            <div class='clearfix'></div>
                            <div v-for='(list,idx) in dataList'>
                                <div class='col-md-8 col-sm-8 relative'>
                                    <img class='absoluteImg' :class='idx==0?"absoluteImg15":""'  v-if='list.on_top==1'  src="https://m3.media.ivykids.cn/ds2/top_en.png" alt="">
                                    <div class='pull-left text-center length_1'>
                                        <span class='glyphicon glyphicon-flag font14 text-center' :class='list.level==1?"dangerColor":list.level==2?"warning":list.level==4?"express":""'  v-if='list.level==1 || list.level==2 || list.level==4' onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" :title="levelList[list.level]" data-placement="top"></span>
                                    </div>
                                    <div style='padding-left:50px'>
                                        <p class='text-ellipsis font14 text-primary cur-p fontBold listTitle' data-toggle="modal"  @click='viewContent(list)'>{{list.q}}</p>
                                        <p class='text-ellipsis font14 color6 listSummary'>{{list.summary}}</p>
                                        <div>
                                            <span class="label label-default defaultBg mr5 fontWeightNormal" v-for='(list,index) in list.grade_text'>{{list}}</span>                            
                                        </div>
                                        <div class='email cur-p '   v-if='list.require_emailing==1&&emailTaskData[list._id]'  data-toggle="modal"  @click='emailData(list)'>
                                            <span class="glyphicon glyphicon-envelope  mr5"></span><?php echo Yii::t("newDS", "Batch Emailing Report");?>
                                        </div>
                                        <div class='mt15'>
                                            <span class='category text-primary cur-p' @click='categoryUpdate(list,idx,"show")'>{{showCategoryList(list.category)}} <span class="el-icon-arrow-right"></span></span>
                                          
                                        </div>
                                        <div class="media " v-if='list.sign_as_uid && addresser[list.sign_as_uid]'>
                                            <div class="media-left pull-left media-middle">
                                                <a href="javascript:void(0)">
                                                    <img class="media-object img-circle image" :src="addresser[list.sign_as_uid].photoUrl" data-holder-rendered="true" >
                                                </a>
                                            </div>
                                            <div class="media-right pull-right">
                                                <span style="font-size:24px" class="glyphicon glyphicon-qrcode cur-p" @click='oneQrcode(list)' aria-hidden="true"></span>
                                            </div>
                                            <div class="media-body pt10 media-middle">
                                                <h4  class="media-heading font12">{{addresser[list.sign_as_uid].name}}</h4>
                                                <div class='text-muted'>{{list.sign_title}}</div>
                                            </div>
                                        </div>
                                        <div class='pt10' v-if='list.publish_type!=0'>
                                            <span class='text-muted'><?php echo Yii::t("newDS", "Published:");?> {{list.format_publish_at}}</span>
                                            <span class='text-muted ml20' v-if='list.expired_at==0'><?php echo Yii::t("newDS", "Expire: never");?></span>
                                            <span class='text-muted ml20' v-else>{{release(list,'date')}}</span>
                                        </div>
                                        <p class='pt10'>
                                            <span class='text-muted'><?php echo Yii::t("newDS", "Edit: ");?>{{userInfo(list,'name')}}</span>
                                            <span class='text-muted ml20'><?php echo Yii::t("newDS", "Edit Time:");?> {{list.format_updated}}</span>
                                        </p>
                                    </div>
                                </div>
                                <div  class='col-md-3 col-sm-3'>
                                    <div class='col-md-3 col-sm-3 text-center'>
                                        <div class='text-muted mb10'><?php echo Yii::t("newDS", "Viewed");?></a></div> 
                                        <div class='cur-p text-primary' @click='parentReply(list,"view")'>{{numberPeople(list,'look')}}</div>
                                    </div>
                                    <div class='col-md-6 col-sm-6  text-center'>   
                                        <div class='text-muted mb10 '><?php echo Yii::t("newDS", "Status");?></div> 
                                        <div  v-if='list.publish_type==0'>
                                            <span class='mr5' style='color:#d9534f'>
                                                <i class='glyphicon glyphicon-ban-circle' style='top:2px'  ></i>
                                                <span><?php echo Yii::t("newDS", "offline");?></span> 
                                            </span>
                                        </div>
                                        <div v-else class='mr5'>
                                            <span v-if='list.timeState==10' class='text-muted'><i class='glyphicon glyphicon-ok-circle' style='top:2px;color:#999'  onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' title='<?php echo Yii::t("newDS", "online");?>'  data-placement="top"></i> <?php echo Yii::t("newDS", "Not Started");?></span>
                                            <span v-else-if='list.timeState==20' class='text-muted'><i class='glyphicon glyphicon-ok-circle' style='top:2px;color:#999'  onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' title='<?php echo Yii::t("newDS", "online");?>'  data-placement="top"></i> <?php echo Yii::t("newDS", "Expired");?></span>
                                            <span v-else style='color:#5cb85c'><i class='glyphicon glyphicon-ok-circle' style='top:2px;color:#5cb85c'  onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' title='<?php echo Yii::t("newDS", "online");?>'  data-placement="top"></i> <?php echo Yii::t("newDS", "Active");?></span>
                                        </div>
                                    </div>
                                    <div class='col-md-3 col-sm-3  text-center'>   
                                        <div class='text-muted mb10 '><?php echo Yii::t("newDS", "Comment");?></div> 
                                        <div class='text-muted' v-if='list.comment_enabled==0'><?php echo Yii::t("newDS", "Disabled");?></div>
                                        <div class='text-primary cur-p'  @click='wantSay(list)' v-if='list.comment_enabled==1'>{{list.comment_num}}</div>
                                    </div>
                                    <div class='col-md-12 col-sm-12  mt20'  v-if='list.publish_type!=0 && list.require_response==1'>   
                                        <div class='text-muted mb10'><?php echo Yii::t("newDS", "Completion Progress");?> <a href="#"  data-toggle="modal" @click='parentReply(list,"confirm")'><?php echo Yii::t("newDS", "(Detail)");?></a> </div> 
                                        <div class="progress">  
                                            <div class="progress-bar progress-bar-striped" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" 
                                            :style="numberPeople(list,'progressWidth')">
                                            {{numberPeople(list,'progress')}}
                                            </div>
                                        </div>
                                    </div>
                                    <div class='col-md-12 col-sm-12  mt20'  v-if='list.publish_type!=0 && list.require_response==2'>   
                                        <div class='text-muted mb10'><?php echo Yii::t("newDS", "Single Question");?> <a href="#"  data-toggle="modal" @click='question(list,"one")'><?php echo Yii::t("newDS", "(Detail)");?></a> </div> 
                                        <div class="progress">  
                                            <div class="progress-bar progress-bar-striped" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" 
                                            :style="numberPeople(list,'progressWidth')">
                                            {{numberPeople(list,'progress')}}
                                            </div>
                                        </div>
                                    </div>
                                    <div class='col-md-12 col-sm-12  mt20'  v-if='list.publish_type!=0 && list.require_response==3'>   
                                        <div class='text-muted mb10'><?php echo Yii::t("newDS", "Mini survey");?> <a href="#"  data-toggle="modal" @click='question(list,"multiple")'><?php echo Yii::t("newDS", "(Detail)");?></a> </div> 
                                        <div class="progress">  
                                            <div class="progress-bar progress-bar-striped" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" 
                                            :style="numberPeople(list,'progressWidth')">
                                            {{numberPeople(list,'progress')}}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class='col-md-1 col-sm-1 text-right'>
                                    <div class="dropdown pull-right">
                                        <button class="btn btn-default btn-sm dropdown-toggle" type="button" id="dropdownMenu1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                                        <?php echo Yii::t("newDS", "Actions");?>
                                            <span class="caret"></span>
                                        </button>
                                        <ul class="dropdown-menu" aria-labelledby="dropdownMenu1">
                                            <li><a href="javascript:void(0)"   @click='publishInfo(list)'><?php echo Yii::t("newDS", "Notice Publish");?></a></li>
                                            <li >
                                            <li><a href="javascript:void(0)" v-if='multiple==1'  @click='copyNotice(list)'><?php echo Yii::t("newDS", "Duplicate to Other Campuses");?></a></li>
                                            <li >
                                            <a :href="'<?php echo $this->createUrl('edit'); ?>&id='+list._id" ><?php echo Yii::t("global", "Edit");?></a></li>
                                            <li><a href="javascript:void(0)" v-if='list.publish_type!=0 && list.require_response==1' data-toggle="modal" @click='parentReply(list)'><?php echo Yii::t("newDS", "Completion Progress");?></a></li>
                                            <!-- <li><a href="javascript:void(0)" v-if='list.publish_type!=0 && list.require_emailing==0' data-toggle="modal"  @click='sendEmail(list)'><?php echo Yii::t("newDS", "Send batch email notification");?></a></li> -->
                                            <!-- <li ><a href="javascript:void(0)" v-if='list.require_emailing==1' data-toggle="modal"  @click='emailData(list)'><?php echo Yii::t("newDS", "Batch Emailing Report");?></a></li> -->
                                            <li ><a href="javascript:void(0)"   @click='delNotice(list)'><?php echo Yii::t("global", "Delete");?></a></li>
                                            
                                        </ul>
                                    </div>
                                </div> 
                                <div class='clearfix'></div>
                                <hr  v-if='idx!=dataList.length-1'> 
                            </div>   
                        </div>
                          
                    </div> 
                    <nav aria-label="Page navigation" v-if='CopyPages.pages>1'  class="text-left ml10">
                        <ul class="pagination">
                            <li v-if='pageNum >1'>
                                <a href="javascript:void(0)" @click="plus(1)" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            <li v-else class="disabled">
                                <a aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            <li class="previous" v-if='pageNum >1'>
                                <a href="javascript:void(0)" @click="prev(pageNum)">‹</a>
                            </li>
                            <li class="disabled" v-else>
                                <a href="javascript:void(0)">‹</a>
                            </li>
                            <li v-for='(data,index) in pages.pages' :class="{ active:data==pageNum }">
                                <a href="javascript:void(0)" @click="plus(data)">{{data}}</a>
                            </li>
                            <li class="previous" v-if='pageNum <CopyPages.pages'>
                                <a href="javascript:void(0)" @click="next(pageNum)">›</a>
                            </li>
                            <li class="previous disabled" v-else>
                                <a href="javascript:void(0)">›</a>
                            </li>
                            <li v-if='pageNum <CopyPages.pages'>
                                <a href="javascript:void(0)" @click="plus(CopyPages.pages)" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li v-else class="disabled">
                                <a aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
    <!-- 问卷报告 -->
    <div class="modal fade" id="questionModal" tabindex="-1" role="dialog" aria-labelledby=" parentCommentsModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "Check Result");?></h4>
            </div>
            <div class="modal-body p24 scroll-box"  :style="'max-height:'+(height-180)+'px;overflow-x: hidden;'">
                <div v-if='answerType=="multiple"'>
                    <p><label><?php echo Yii::t("newDS", "survey list");?></label></p>
                    <p>
                        <span class='borderOnline cur-p' :class='index==questionsIndex?"questionsActive":""' v-for='(list,key,index) in questionsList.questions_list' @click='showQuestions(list,index)'>{{index+1}}</span>
                    </p>
                    <div v-if='Object.keys(currentQuestionData).length!=0'>
                        <div class=''><label><?php echo Yii::t("newDS", "Topic");?></label><span class="label label-default ml10">{{!currentQuestionData.option?"<?php echo Yii::t("newDS", "Non-choice question.");?>":currentQuestionData.is_multiple==1?"<?php echo Yii::t("newDS", "Multiple choice question");?>":"<?php echo Yii::t("newDS", "Single choice question");?>"}}</span></div>
                        <div class='mb20 pb5 font14'>{{currentQuestionData.title}}</div>
                        <p><label><?php echo Yii::t("newDS", "Survy detail");?></label></p>
                        <div class='flex mb20'>
                            <span class='mt5'><?php echo Yii::t('campus', 'Select Classes') ?>：</span>
                            <div class='flex1 ml-20'>
                                <select class="form-control" v-model='questionGrade' @change='showQuestions(currentQuestionData,questionsIndex)'>
                                    <option value='0'><?php echo Yii::t("campus", "All Classes"); ?></option>
                                    <option v-for='(list,key,index) in questionsList.grade_group_list' :value='key'>{{list}}</option>
                                </select>
                            </div>
                        </div>
                        <div v-if='!currentQuestionData.option && questionOptionData.answer_list'>
                            <div v-if='questionOptionData.answer_list.no_option && questionOptionData.answer_list.no_option.length!=0' class='scroll-box' style='max-height:480px;overflow-y:auto;padding-right:8px'>
                                <div class="p10 mb10 relative" v-for='(list,index) in questionOptionData.answer_list.no_option'  style='border:1px solid #E5E7EB;border-radius:6px;'>
                                    <span class='unsubmitted' v-if='list.status==1'><?php echo Yii::t('reg', 'Not submitted') ?></span>
                                    <div class='media' :class='list.status==1?"mt20":""'>
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img class="media-object img-circle questionImage" :src="questionOptionData.child_info_list[list.child_id].avatar"  data-holder-rendered="true" >
                                            </a>
                                        </div>
                                        <div class="media-right pull-right">
                                        
                                        </div>
                                        <div class="media-body pt5 media-middle">
                                            <div><strong>{{questionOptionData.child_info_list[list.child_id].name}}</strong></div>
                                            <p class='text-muted '>{{questionOptionData.child_info_list[list.child_id].class_name}}</p>
                                            <div class='flex mt5' v-if='currentQuestionData.is_memo==1 || currentQuestionData.is_memo==2'>
                                                <div class='flex1' v-html='list.memo'></div>
                                            </div>
                                            <!-- <div class='text-muted mt10'>3分钟前</div> -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else class='mt10'>
                                <div class="alert alert-warning" role="alert"><?php echo Yii::t('ptc', 'No Data') ?></div>
                            </div>
                        </div>
                        <div v-show='currentQuestionData.option && questionOptionData.answer_list' class='row'>
                            <div class='col-sm-6 borderRight' ref='echartsHeight'>
                                <p><label><?php echo Yii::t("newDS", "Results Overview");?></label></p>
                                <p class='text-center font14'><a href="javascript:;"  @click='parentReply(confirmData,"confirm")'>
                                    <?php echo Yii::t("newDS", "Total");?> {{questionOptionData.option_sum}} <?php echo Yii::t("newDS", "feedback.");?> <span class='ml5 el-icon-arrow-right'></span></a></p>
                                <div id='multipleEchart' style='height:300px;width:100%'></div> 
                                <div v-for='(list,index) in questionsOptionMemoList' class='mb8'>{{list}}</div> 
                            </div>
                            <div class='col-sm-6'>
                                <p><label><?php echo Yii::t("newDS", "Results detail");?></label></p>
                                <p class='cur-p'>
                                    <span class='optionFilter' :class='optionId==""?"optionFilterActive":""' @click='questionOptionRemark("all")'><?php echo Yii::t("global","All");?></span>
                                    <span class='optionFilter' v-for='(list,index) in questionOptionData.sort' :class='optionId==list?"optionFilterActive":""'  @click='questionOptionRemark(list)'>{{optionA[index]}}</span>
                                </p>
                                <div class='scroll-box'  :style="'max-height:'+(echartsHeight)+'px;overflow-x: hidden;padding-right:8px'">
                                    <div v-if='questionsOptionMemo.length!=0' class='mt10'>
                                        <div class=" p10 mb10 relative" v-for='(list,index) in questionsOptionMemo'  style='border:1px solid #E5E7EB;border-radius:6px;'>
                                            <span class='unsubmitted' v-if='list.status==1'><?php echo Yii::t('reg', 'Not submitted') ?></span>
                                            <div class='media' :class='list.status==1?"mt20":""'>
                                            <div class="media-left pull-left media-middle">
                                                <a href="javascript:void(0)">
                                                    <img class="media-object img-circle questionImage" :src="questionOptionData.child_info_list[list.child_id].avatar"  data-holder-rendered="true" >
                                                </a>
                                            </div>
                                            <div class="media-right pull-right">
                                                <span  v-for='(_list,i) in list.option'>
                                                <span class='option mt10' :style="{background:color[optionA[_list]]}">{{optionA[_list]}}</span>
                                                    {{color[_list]}}
                                                    </span>
                                            </div>
                                            <div class="media-body pt5 media-middle">
                                                <div><strong>{{questionOptionData.child_info_list[list.child_id].name}}</strong></div>
                                                <p class='text-muted '>{{questionOptionData.child_info_list[list.child_id].class_name}}</p>
                                                <div class='flex mt5'  v-if='currentQuestionData.is_memo==1 || currentQuestionData.is_memo==2'>
                                                    <div  style='width:40px'><?php echo Yii::t("labels","Memo");?>：</div>
                                                    <div class='flex1' v-html='list.memo'></div>
                                                </div>
                                                <!-- <div class='text-muted mt10'>3分钟前</div> -->
                                            </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div v-else class='mt10'>
                                        <div class="alert alert-warning" role="alert"><?php echo Yii::t('ptc', 'No Data') ?></div>
                                    </div>
                                </div>
                            </div>
                            <div class='clearfix'></div>
                        </div>
                        </div>
                </div>
                <div v-if='answerType=="one" && questionData.config'>
                    <div class='mb15 font14'>{{questionData.config.t_cn}}</div>
                    <div class='mb15 mt15 font14'>{{questionData.config.t_en}}</div>
                    <div class='row'>
                        <div class='col-sm-6 borderRight' ref='echartsHeightOne'>
                            <p><label><?php echo Yii::t("newDS", "Results Overview");?></label></p>
                            <!--  @click='parentReply(confirmData,"confirm")' -->
                            <p class='text-center'><a href="javascript:;"  @click='parentReply(confirmData,"confirm")'><?php echo Yii::t("newDS", "Total");?>&nbsp;{{questionData.optionTotal}}&nbsp;<?php echo Yii::t("newDS", "feedback.");?><span class='ml5 el-icon-arrow-right'></span></a></p>
                            <div id='echart' style='height:300px;width:100%'></div> 
                            <div v-for='(list,index) in optionMemoList' class='mb8'>{{list}}</div> 
                        </div>
                        <div class='col-sm-6'>
                            <p><label><?php echo Yii::t("newDS", "Memo List");?></label></p>
                            <p class='cur-p'>
                                <span class='optionFilter' :class='optionId==""?"optionFilterActive":""' @click='optionRemark("all")'> <?php echo Yii::t("global","All");?></span>
                                <span class='optionFilter' v-for='(list,index) in sort' :class='optionId==list?"optionFilterActive":""'  @click='optionRemark(list)'>{{optionA[index]}}</span>
                                <!-- <span class='optionFilter optionFilterActive'>B</span> -->
                            </p>
                            <div class='scroll-box' :style="'max-height:'+(echartsHeightOne)+'px;overflow-x: hidden;padding-right:8px'" >
                                <div class="media p10 mb10 " v-for='(list,index) in optionMemo'  style='border:1px solid #E5E7EB;border-radius:6px;'>
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                            <img class="media-object img-circle image" :src="optionChildInfo[list.childId].avatar"  data-holder-rendered="true" >
                                        </a>
                                    </div>
                                    <div class="media-right pull-right">
                                            <span class='option mt10' :style="{background:color[optionA[list.option]]}">{{optionA[list.option]}}</span>
                                            {{color[list.option]}}
                                    </div>
                                    <div class="media-body pt10 media-middle">
                                        <div><strong>{{optionChildInfo[list.childId].name}}</strong></div>
                                        <p class='text-muted '>{{optionChildInfo[list.childId].class_name}}</p>
                                        <div class='flex mt5'>
                                            <div  style='width:40px'><?php echo Yii::t("labels","Memo");?></div>
                                            <div class='flex1' v-html='list.memo'></div>
                                        </div>
                                        <!-- <div class='text-muted mt10'>3分钟前</div> -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <span style="color:#666"><?php echo Yii::t("newDS", "All students will exported (blank option means not repsonded student)"); ?></span>
                <button type="button" class="btn btn-primary" v-if='answerType=="one"' :disabled='exportBtn' @click='exportData()'>{{exportBtn?'<?php echo Yii::t("global", "Exporting").'...';?>':"<?php echo Yii::t("user", "Export");?>"}}</button>
                <button type="button" class="btn btn-primary" v-if='answerType=="multiple"' :disabled='exportBtn' @click='exportSurveyData()'>{{exportBtn?'<?php echo Yii::t("global", "Exporting").'...';?>':"<?php echo Yii::t("user", "Export");?>"}}</button>
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
            </div>
            </div>
        </div>
    </div>
    <!-- 家长回复 -->
    <div class="modal fade" id="parentReplyModal" tabindex="-1" role="dialog" style='overflow:auto'>
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel">{{confirmData.q_cn}}</h4>
                </div>
                <div class="modal-body">
                    <div  class="scroll-box"  :style="'max-height:'+(height-240)+'px;overflow-x: hidden;padding-right:8px'">
                        <div class="well well-sm">  
                            <span class='text-muted mr10'><?php echo Yii::t("newDS", "Not-Read ");?><?php echo Yii::t("newDS", "(Gray)");?> </span> 
                            <a href="javascript:;" class='mr10'><?php echo Yii::t("newDS", "Read ");?><?php echo Yii::t("newDS", "(Blue)");?></a> 
                            <span class='text-muted' v-if='confirmData.publish_type!=0 && confirmData.require_response==1'>
                            <?php echo Yii::t("newDS", "Confirmed ");?> <span class="label  label-success">Y</span> </span> 
                            <div class='mt5'> <?php echo Yii::t("newDS", "Total ");?><?php echo Yii::t("global", ": ");?> {{confirmData.student_num}}</div>
                            <div class='mt5'><?php echo Yii::t("newDS", "Read ");?><?php echo Yii::t("global", ": ");?> {{viewList.length}}</div>
                            <div class='mt5' v-if='confirmData.publish_type!=0 && confirmData.require_response!=0'><?php echo Yii::t("newDS", "Confirmed ");?><?php echo Yii::t("global", ": ");?> {{responseData[confirmData._id]?responseData[confirmData._id].confirmedNum:0}}</div>
                        </div>
                        <div class="panel panel-default" v-for='(list,index) in classList'>
                            <div class="panel-heading">
                                {{list.title}}
                            </div> 
                            <div class="panel-body row">
                                <ul class="nav nav-pills">
                                    <li  v-for='(item,key) in list.item'>
                                        <a  class='relative' :class='confirmedChildid(item.id,"view")?"":"text-muted"' @click='hrefChild(item.id)' href="javascript:;" class="labels">{{item.name}}
                                            <span class=" label  label-success" v-if='(confirmData.require_response==1 || confirmData.require_response==3) && confirmedChildid(item.id,"confirm")'>Y</span>
                                            <template v-if='confirmData.require_response==2 && noticeChildOption[item.id]' >
                                                <span class='option'  v-if='viewOption(noticeChildOption[item.id])!=""'  :style="{background:color[viewOption(noticeChildOption[item.id])]}">{{viewOption(noticeChildOption[item.id])}}</span>
                                            </template>

                                            <span @click.stop="surveyReset(item.id)" class="glyphicon glyphicon-repeat resetBtn  ml4" aria-hidden="true" v-if='(confirmData.require_response==2 || confirmData.require_response==3) && confirmedChildid(item.id,"confirm")' onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' title='<?php echo Yii::t("newDS", "重置");?>'  data-placement="top"></span>
                                        </a>
                                        
                                    </li>
                                </ul> 
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 邮件报告 -->
    <div class="modal fade " id="emailModal" tabindex="-1" role="dialog">
        <div class="modal-dialog">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title"><?php echo Yii::t("newDS", "Task Status:");?> {{stepList[sendnewDS.step]}} <span @click="syncEmailTask" class="glyphicon glyphicon-refresh" aria-hidden="true"></span></h4>
            </div>
            <div class="modal-body">
                <div class="mb20" >
                    <div class='col-md-4 col-sm-4 text-center'>
                        <h2>{{sendnewDS.totalNum}}</h2>
                        <p class='font14'><?php echo Yii::t("newDS", "Total Email");?></p>
                    </div>
                    <div class='col-md-4 col-sm-4 text-center'>
                        <h2>{{sendnewDS.successNum}}</h2>
                        <p class='font14'><?php echo Yii::t("newDS", "Delivered");?></p>
                    </div>
                    <div class='col-md-4 col-sm-4 text-center'>
                        <h2>{{sendnewDS.redundantNum}}</h2>
                        <p class='font14'><?php echo Yii::t("newDS", "Duplicate Emails");?></p>
                    </div>
                    <div class='clearfix'></div>
                </div>
                <ul class="nav nav-tabs pt10" id="myTab">
                    <li :class="emailType=='emailFail'?'active':''"><a href="#emailFail" @click='emailList("emailFail")'><?php echo Yii::t("newDS", "Deliver failed");?> <span :class="Object.keys(errorList).length==0?'badge':'badge danger'">{{Object.keys(errorList).length}}</span></a></li>

                    <li :class="emailType=='noEmail'?'active':''"><a href="#noEmail"  @click='emailList("noEmail")'><?php echo Yii::t("newDS", "No valid emails");?> <span :class="noEmailList.length==0?'badge':'badge danger'">{{noEmailList.length}}</span></a></li>
                </ul>
                <div class="tab-pane mt20"  id="emailFail" v-if="emailType=='emailFail'">
                    <div v-for='(list,key,index) in errorList'>
                        <div class='col-md-4 col-sm-4'>
                            <div class="media ">
                                <div class="media-left pull-left media-middle">
                                    <a href="javascript:void(0)">
                                        <img class="media-object img-circle image" :src="childInfo[key].childPhoto" data-holder-rendered="true" >
                                    </a>
                                </div>
                                <div class="media-body media-middle">
                                    <h4  class="media-heading font12 lineHeight"> <a target="_blank"
                                        :href="'<?php echo $this->createUrl('/child/index/index'); ?>&childid='+key" class="">{{childInfo[key].childName}}</a></h4>
                                </div>
                            </div>
                        </div>
                        <div class='col-md-4 col-sm-4 text-center lineHeight' v-for='(item,idx) in  list'>
                            <span class='text-danger'>{{item}}</span>  
                        </div>
                        <div class='clearfix'></div>
                        <hr  v-if='index!=Object.keys(errorList).length-1'>
                    </div>
                </div>
                <div class="tab-pane mt20"  id='noEmail'  v-if="emailType=='noEmail'">
                    <div class='col-md-4 col-sm-4 mb10' v-for='(list,id) in noEmailList'>
                        <div class="media ">
                            <div class="media-left pull-left media-middle">
                                <a href="javascript:void(0)">
                                    <img class="media-object img-circle image":src="childInfo[list].childPhoto" data-holder-rendered="true" >
                                </a>
                            </div>
                            <div class="media-body media-middle">
                                <h4  class="media-heading font12 lineHeight"><a target="_blank" 
                                :href="'<?php echo $this->createUrl('/child/index/index'); ?>&childid='+list" class="">{{childInfo[list].childName}}</a></h4>
                                </div>
                            </div>
                        </div>
                    </div>
                <div class='clearfix'></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
            </div>
            </div>
        </div>
    </div>
    <!-- 发送邮件 -->
    <div class="modal fade" id="sendEmailModal" tabindex="-1" role="dialog"  >
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("newDS", "Send email notification to parents");?></h4>
                </div>
                <div class="modal-body" v-if='sendEmailList.timeState==0'>
                    <p><?php echo Yii::t("newDS", "Email notification will be sent to parents of following grade groups:");?>  
                        <!-- <span class='text-primary' @click='viewTemplate()'>查看模板</span> -->
                    </p>
                    <div style='margin-bottom:25px'>
                        <p class='ml20' v-for='(list,index) in sendEmailList.grade_text'><span class='sendGroup'></span>{{list}}</p>
                    </div>
                    <div class="checkbox">
                        <label>
                        <input type="checkbox" v-model='sendConfirm'> <?php echo Yii::t("newDS", "Confirm to send");?>
                        </label>
                    </div>
                    <div class="form-group ml20 mt20" v-if='sendConfirm'>
                        <label for="exampleInputEmail1" class='fontWeightNormal'><?php echo Yii::t("newDS", "Please input your DaystarOnline password to preceed");?></label>
                        <input type="password" class="form-control length_4"  v-model='sendPassword' id="exampleInputEmail1" placeholder="password">
                    </div>
                </div>
                <div class="modal-body" v-else>
                    <span><?php echo Yii::t("newDS", "You can only send notification of PUBLISHED and ACTIVE notices to parents.");?></span>
                </div>
                <div class="modal-footer" v-if='sendEmailList.timeState==0'>
                    <button type="button" class="btn btn-primary" @click='sendTemplate'><?php echo Yii::t("newDS", "Send");?> </button>
                </div>
                <div class="modal-footer" v-else>
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 我想说 -->
    <div class="modal fade" id="wantSayModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "Comment List: ");?>{{wantSayData.q}}</h4>
            </div>
            <div class="modal-body">
                <div v-if='commentsList.length==0' class="alert alert-info" role="alert"><?php echo Yii::t("newDS", "No items found");?></div>
                <div v-for='(item,index) in childIdList'>
                    <div v-for='(list,idx) in commentsList[item]' class='mb20'>
                        <div v-if='list.creator_type=="parent"'>
                            <div class="media ">
                                <div class="media-left pull-left media-middle">
                                    <a href="javascript:void(0)">
                                    <img class="media-object img-circle image32" :src="commentsChildInfo[list.child_id].childPhoto" data-holder-rendered="true" >
                                    </a>
                                </div>
                                <div class="media-body media-middle">
                                    <h4  class="media-heading color3 font14">{{commentsChildInfo[list.child_id].childName}}</h4>
                                    <div class='text-muted'>{{commentsClassInfo[list.class_id] ? commentsClassInfo[list.class_id].title : ''}} 
                                    </div>
                                </div>
                            </div>
                            <div style='margin-left:42px'>
                                <div class='font14 mt5' v-html='list.content'></div>
                                <div class='mt10'>
                                    <ul class='mb12 imgLi' :id='list.creator_type+"_"+idx+"_"+item'  v-if='list.imgUrl.length!=0'>
                                        <li v-for='(_list,j) in list.imgUrl'>
                                            <img :src="_list.url" class='fileImg mr8' @click='showImg(list,idx,item)' alt=""  >
                                        </li>
                                    </ul>
                                    <div >
                                        <div class='flex fileLink' v-for='(item,j) in list.pdfUrl'>
                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='item.mimetype=="application/pdf"' />
                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='item.mimetype=="application/vnd.ms-excel" || item.mimetype=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='item.mimetype=="application/msword" || item.mimetype=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='item.mimetype=="application/x-zip-compressed"' />
                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                            <a class='flex1 ml5' target= "_blank" :href="item.url">{{item.title}}
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <p class='text-muted pt10'>
                                    <span v-if='commentsChildInfo[list.child_id].fid==list.created_by'><?php echo Yii::t("newDS", "From student's Dad");?></span>
                                    <span v-if='commentsChildInfo[list.child_id].mid==list.created_by'><?php echo Yii::t("newDS", "From student's Mom");?></span>
                                    <span class='ml5'>{{list.created_at}}</span> 
                                </p> 
                            </div>
                        </div>
                        <div v-if='list.creator_type=="staff"' style='margin-left:42px' class='pt10'>
                            <div v-if='list.mark_as_staff==1' class=' mb10 mb20'>
                                <p class='font12 color3'>
                                    <span class='glyphicon glyphicon-pushpin mr10' style='color:#EC971FFF'></span>{{staffInfoList[list.created_by].name}} <?php echo Yii::t("newDS", "marked No Reply Needed");?> <span class='font14 color6 ml10'>{{list.created_at}}</span>
                                </p>
                                <div  class='font12 color9' style='padding-left:15px'>
                                    <?php echo Yii::t("newDS", "(This message is invisible to parents)");?>
                                </div>
                            </div>
                            <div v-else class='mb10'>
                                <div class="media " v-if='staffInfoList[list.created_by]'>
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                        <img class="media-object img-circle image32"  :src="staffInfoList[list.created_by].photoUrl" data-holder-rendered="true" >
                                        </a>
                                    </div>
                                    <div class="media-body media-middle">
                                        <h4  class="media-heading font14  color3 lineHeight32">{{staffInfoList[list.created_by].name}} <span class='font12 color6 ml5'><?php echo Yii::t("global", "reply");?></span></h4>
                                    </div>
                                </div>
                                <div style='margin-left:42px'>
                                    <div class='font14 mt5' v-html='list.content'></div>
                                    <div class='mt10'>
                                        <ul class='mb12 imgLi' :id='list.creator_type+"_"+idx+"_"+item'  v-if='list.imgUrl.length!=0'>
                                            <li v-for='(_list,j) in list.imgUrl'>
                                                <img :src="_list.url" class='fileImg mr8' @click='showImg(list,idx,item)' alt=""  >
                                            </li>
                                        </ul>
                                        <div >
                                            <div class='flex fileLink' v-for='(item,j) in list.pdfUrl'>
                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='item.mimetype=="application/pdf"' />
                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='item.mimetype=="application/vnd.ms-excel" || item.mimetype=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='item.mimetype=="application/msword" || item.mimetype=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='item.mimetype=="application/x-zip-compressed"' />
                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                                <a class='flex1 ml5' target= "_blank" :href="item.url">{{item.title}}
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <p class='text-muted pt10'>{{list.created_at}}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <hr v-if='index!=childIdList.length-1'>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
            </div>
            </div>
        </div>
    </div>
    <!-- 查看内容 -->
    <div class="modal fade" id="contentModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "Content");?></h4>
            </div>
            <div class="modal-body" v-if='Object.keys(contentData).length!=0'>
                <form class="form-horizontal">
                    <div class="form-group">
                        <div class="col-sm-12">
                            <h3 class='titleCn'>{{contentData.q_cn}}</h3>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-12">
                            <h3 class='titleEn'>{{contentData.q_en}}</h3>
                        </div>
                    </div>
                    <div class="form-group" >
                        <div class="col-sm-12 pt7 summary_cn" >
                            {{contentData.summary_cn}}
                        </div>
                    </div>   
                    <div class="form-group" >
                        <div class="col-sm-12 pt7 summary_en">
                            {{contentData.summary_en}}
                        </div>
                    </div>   
                    <div class="form-group" >
                        <div class="col-sm-12 pt7">
                        <span class="label label-default defaultBg mr5 fontWeightNormal" v-for='(list,index) in contentData.grade_text'>{{list}}</span>
                        </div>
                    </div>   
                    <div class="form-group">
                        <div class="col-sm-12 pt7" style="padding: 0 30px;">
                            <div class='content contentCn' v-html='contentData.content'></div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-12 pt7" style="padding: 0 30px;">
                            <div class='content contentEn' v-html='contentData.content_en'></div>
                        </div>
                    </div>
                    <div class='mt24 mb24' v-if='contentData.joint_admins && contentData.joint_admins.length!=0'>
                        <p class='color3 font14'><strong><?php echo Yii::t("directMessage", "Collaboration"); ?></strong> <span class="badge bg7 ml5">{{contentData.joint_admins.length}}</span></p>
                            <div v-for='(list,index) in contentData.joint_admins' class=' mt8 mb8' >
                                <div class="media">
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                            <img class="contentAvatar" :src="contentData.joint_admins_info[list].photoUrl" data-holder-rendered="true" >
                                        </a>
                                    </div>
                                    <div class="media-body pt8 media-middle">
                                        <h4  class="media-heading font12">{{contentData.joint_admins_info[list].name}}</h4>
                                        <div class='text-muted'>{{contentData.joint_admins_info[list].hrPosition}}</div>
                                    </div>
                                </div>
                            </div>
                    </div>
                    <div class="form-group" >
                        <div class="col-sm-12 pt7" >
                            <div class="media " v-if='contentData.sign_as_uid && addresser[contentData.sign_as_uid]'>
                                <div class="media-left pull-left media-middle">
                                    <a href="javascript:void(0)">
                                        <img class="media-object img-circle image" :src="addresser[contentData.sign_as_uid].photoUrl" data-holder-rendered="true" >
                                    </a>
                                </div>
                                <div class="media-body pt10 media-middle">
                                    <h4  class="media-heading font12">{{addresser[contentData.sign_as_uid].name}}</h4>
                                    <div class='text-muted'>{{contentData.sign_title}}</div>
                                </div>
                            </div>
                            <div class='pt10' v-if='contentData.publish_type!=0'>
                                <span class='text-muted'><?php echo Yii::t("newDS", "Published:");?> {{contentData.format_publish_at}}</span>
                                <span class='text-muted ml20' v-if='contentData.expired_at==0'><?php echo Yii::t("newDS", "Expire: never");?></span>
                                <span class='text-muted ml20' v-else>{{release(contentData,'date')}}</span>
                            </div>
                            <p class='pt10'>
                                <span class='text-muted'><?php echo Yii::t("newDS", "Edit: ");?>{{userInfo(contentData,'name')}}</span>
                                <span class='text-muted ml20'><?php echo Yii::t("newDS", "Edit Time:");?> {{contentData.format_updated}}</span>
                            </p>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-12 pt7" >
                            <p v-for='(list,index) in attachments'>
                                <a target="_blank" style='line-height:26px' :href='list.file_key' >{{list.title}}</a>
                            </p>
                        </div>
                    </div>
                </form>
            </div>
            </div>
        </div>
    </div>
    <!-- 二维码 -->
    <div class="modal fade" id="qrcodeModal" tabindex="-1" role="dialog"  >
        <div class="modal-dialog" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("newDS", "Qr-codes list");?></h4>
            </div>
            <div class="modal-body">
                <div class='col-md-6 col-sm-6 text-center'>
                    <p><strong><?php echo Yii::t("newDS", "For Preview Only");?></strong> </p>                   
                    <div><?php echo Yii::t("newDS", "Don't Share");?></div>        
                    <div class='mt20'>
                        <img class='qrcodeImg' :src="previewQrcode" alt="">
                    </div>    
                    <div class='mt15 mb20'>
                        <button type="button" class="btn btn-primary"  @click='shareImg("view")'><?php echo Yii::t("reg", "Beautify this QrCode");?></button>
                    </div>       
                </div>
                <div class='col-md-6 col-sm-6 text-center'>
                    <p><strong><?php echo Yii::t("newDS", "For Parent Sharing");?></strong></p>                   
                    <div><?php echo Yii::t("newDS", "Need Parent Identity");?></div> 
                    <div class='mt20'>
                        <img class='qrcodeImg' :src="shareQrcode" alt="">
                    </div>   
                    <div class='mt15 mb20'>
                        <button type="button" class="btn btn-primary" @click='shareImg("parent")'><?php echo Yii::t("reg", "Beautify this QrCode");?></button>
                    </div>        
                </div>
                <div class='clearfix'></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
            </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="shareModal" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("reg", "QrCode template");?></h4>
            </div>
            <div class="modal-body">
                <?php $this->renderPartial("share");?>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
            </div>
            </div>
        </div> 
    </div>
    <!-- 删除确认框 -->
    <div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("newDS", "Delete");?></h4>
            </div>
            <div class="modal-body">          
                <div  v-if='delStatus==10'><?php echo Yii::t("newDS", "Cannot delete due to existence of batch emailing, make it offline instead?");?></div>
                <div v-else><?php echo Yii::t("newDS", "Confirm to delete this item?");?></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                <button type="button" v-if='delStatus==10' class="btn btn-primary" @click='nociceOffline()'><?php echo Yii::t("workflow", "Offline");?></button>
                <button type="button" v-else class="btn btn-primary" @click='delList()'><?php echo Yii::t("newDS", "Delete");?></button>
            </div>
            </div>
        </div>
    </div>
    <!-- 发布设置 -->
    <div class="modal fade" id="publishModal" tabindex="-1" role="dialog"  >
        <div class="modal-dialog" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("newDS", "Notice Publish");?></h4>
            </div>
            <div class="modal-body">
                <p class='color3 font14'>{{publishInfoData.q}}</p>
                <div class="form-horizontal color3" >
                    <div class="form-group">
                        <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t("newDS", "Status");?><?php echo Yii::t("global", ": ");?></label>
                        <div class="col-sm-10">
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" v-model='publishStatus'><?php echo Yii::t("newDS", "Make Online");?>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group" v-show='publishStatus'>
                        <label for="inputPassword3" class="col-sm-2 control-label"><?php echo Yii::t("newDS", "Period");?><?php echo Yii::t("global", ": ");?></label>
                        <div class="col-sm-10">
                            <p class='mt10 warning'><span class='glyphicon glyphicon-info-sign'></span> <?php echo Yii::t("newDS", "Display in notice list in this period (parent side)");?> </p>
                            <div class='mt15' >
                                <div class="col-sm-2 p0"><?php echo Yii::t("newDS", "Start");?></div>
                                <div class="col-sm-10 p0">
                                    <input type="text" class="form-control select_2 pull-left mr20 ml0" @blur='publish_atVal' id="publish_at" v-model='publish_at'  placeholder="<?php echo Yii::t("newDS", "Select a date");?>"  :value='publish_at'>
                                    <select class="form-control hoursMinutes"   v-model='publish_atHours'>
                                        <option v-for='(list,index) in 25'>{{index < 10?'0'+index:index}}</option>
                                    </select>
                                    <select class="form-control ml5 hoursMinutes"   v-model='publish_atMinutes'>
                                        <option v-for='(list,index) in 61' >{{index < 10?'0'+index:index}}</option>
                                    </select>
                                </div>
                                <div class="col-sm-2 p0 mt10"><?php echo Yii::t("newDS", "Expire at");?></div>
                                <div class="col-sm-10 p0 mt10">
                                    <div class="radio">
                                        <label>
                                            <input type="radio" v-model='endStatus' value='0'> <?php echo Yii::t("newDS", "No expiration (Always display)");?>
                                        </label>
                                    </div>
                                    <div class="radio">
                                        <label>
                                            <input type="radio" v-model='endStatus' value='1'>  <?php echo Yii::t("newDS", "Specify a time");?>
                                        </label>
                                    </div>
                                    <div class='mt5'   v-show='endStatus==1'>
                                        <input type="text" class="form-control select_2 pull-left mr20"  @blur='expired_atVal' id="expired_at" v-model='expired_at'  placeholder="<?php echo Yii::t("newDS", "Select a date");?>"  :value='expired_at'>
                                        <select class="form-control hoursMinutes"   v-model='expired_atHours'>
                                            <option v-for='(list,index) in 25'>{{index < 10?'0'+index:index}}</option>
                                        </select>
                                        <select class="form-control ml5 hoursMinutes"   v-model='expired_atMinutes'>
                                            <option v-for='(list,index) in 61' >{{index < 10?'0'+index:index}}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class='clearfix'></div>
                            </div>
                            <div class="checkbox mt20">
                                <p class='mt10 warning'><span class='glyphicon glyphicon-info-sign'></span> <?php echo Yii::t("newDS", "Applicable Grades cannot be changed once the following option is checked.");?> </p>
                                <label class='color6'>
                                    <input type="checkbox" :disabled='publishInfoData.send_step>0?true:false'   v-model='require_emailing'>
                                    <div class='mb5'>
                                        <?php echo Yii::t("newDS", "Send parent notification (email, wechat) when period starts");?>
                                        <p class='color3'><?php echo Yii::t("newDS", "Notifications will be sent to parents of following grade groups:");?>
                                        <span v-for='(list,i) in publishInfoData.grade_text'>{{list}} </span>
                                        </p>
                                    </div> 
                                    <div class="alert alert-danger" v-if='publishInfoData.send_step>0' role="alert">{{publishInfoData.stepList[publishInfoData.send_step]}}</div>
                                </label>
                            </div>
                           
                        </div>
                    </div>
                    <div class="form-group" v-show='!publishStatus&&publishInfoData.send_step>=0&&ivystaff_it==1'>
                        <div class="col-sm-2"></div>
                        <div class="col-sm-10">
                            <input type="checkbox" id="clean_email" v-model='clean_email'>
                            <label for="clean_email"><?php echo Yii::t("newDS", "Clear send logs");?></label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                <button type="button" class="btn btn-primary" @click='publishNotice()'><?php echo Yii::t("global", "Save");?></button>
            </div>
            </div>
        </div>
    </div>
    <!-- 复制公告 -->
    <div class="modal fade" id="copyModal" tabindex="-1" role="dialog"  >
        <div class="modal-dialog" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("newDS", "Duplicate To Other Campus");?></h4>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning" role="alert"><?php echo Yii::t('newDS', 'Duplicated notices are set to offline, please check signature, qrcodes and links before making online.'); ?></div>
                <p class='color3 font14'><strong>{{copyData.q}}</strong></p>
                <p><strong><?php echo Yii::t('newDS', 'Applicable to'); ?>：</strong></p>
                <div v-if='copyData.isNext==1'>
                    <div v-for='(list,index) in copyList'>
                        <div class="checkbox">
                            <label>
                                <input type="checkbox" :value="list.schoolId" v-model='copyModel[list.schoolId].schoolId' @change='allSchool(list,list.schoolId,$event)'>
                                {{list.title}}
                            </label>
                        </div>
                        <div  class='ml20 gradeList'>
                            <label class="checkbox-inline"  v-for='(item,key,idx) in list.child_mark'>
                                <input type="checkbox" id="inlineCheckbox1" :value="key" v-model='copyModel[list.schoolId].nextGrades' @change='allNextGrade(list,key,list.schoolId,$event)'>{{item}}
                            </label>
                        </div>
                        
                    </div>
                </div>
                <div v-else>
                    <div v-for='(list,index) in copyList'>
                        <div class="checkbox">
                            <label>
                                <input type="checkbox" :value="list.schoolId" v-model='copyModel[list.schoolId].schoolId' @change='allSchool(list,list.schoolId,$event)'>
                                {{list.title}}
                            </label>
                        </div>
                        <div  class='ml20 gradeList'>
                            <div v-for='(item,i) in list.grades'>
                                <div class="checkbox">
                                    <label>
                                        <input type="checkbox" :value="item.key"  v-model='copyModel[list.schoolId].group' @change='allGroup(item,list.schoolId,$event)' >
                                        {{item.value}}
                                    </label>
                                </div>
                                <div class='ml20'>
                                    <label class="checkbox-inline" v-for='(_item,idx) in item.item'>
                                        <input type="checkbox" id="inlineCheckbox1" :value="_item.key" v-model='copyModel[list.schoolId].grades' @change='allGrade(item,list.schoolId,$event)'>{{_item.value}}
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                <button type="button" class="btn btn-primary" @click='saveCopy()'><?php echo Yii::t("global", "Save");?></button>
            </div>
            </div>
        </div>
    </div>
    <!-- 教师列表 -->
    <div class="modal fade" id="teacherList" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content"  @click='showSelect=false'>
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" ><?php echo Yii::t('newDS','Name of signature') ?> <span id="teacherList_t"></span></h4>
                </div>
                <div class="form-horizontal">
                    <div class="modal-body">
                        <div class='flex'>
                            <div class="relative ">
                                <div class="inputSelect" >
                                    <input type="text" class="form-control pl30" id="exampleInputAmount" autocomplete="off" placeholder="<?php echo Yii::t("attends", "Input name to filter"); ?>" v-model="searchTeacher" @click.stop="showFocus">
                                </div>
                                <div class='selectTeacher' v-if='showSelect'>
                                    <div v-if='teacherSearchData.length==0'>
                                    <p class='font14 color3 text-center mt20 color9'><?php echo Yii::t('ptc', 'No Data'); ?></p>
                                    </div>
                                    <div v-else>
                                        <div  v-for='(list,index) in teacherSearchData'>
                                            <div class="selectName font14 color3 cur-p" @click.stop="getUrl(list)">
                                                {{list.name}} <span v-if='list.level==0' class="label label-default color6 font12 tagLabel ml5"><?php echo Yii::t('newDS', 'Resigned'); ?></span></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <span  class='flex1'></span>
                        </div>
                        <hr >
                        <div name='teacherId'>
                            <a class='mb5 btn btn-default mr10'  href='javascript:;' @click='getUrl("")'>
                                所有 All
                            </a>
                            <a v-for='teacherId in normalList'  class='mb5 btn btn-default mr10'  href='javascript:;' @click='getUrl(teacherId)'>
                                {{teacherId.name}} <span class="badge ml10 bg7">{{teacherId.total}}</span>
                            </a>
                        </div>
                        <div v-if='resignList.length!=0'>
                            <h4><?php echo Yii::t('newDS', 'Resigned'); ?></h4>
                            <div name='teacherId'>
                                <a v-for='teacherId in resignList'  class='mb5 btn btn-default mr10' href='javascript:;' @click='getUrl(teacherId)'>
                                    {{teacherId.name}} <span class="badge ml10 bg7">{{teacherId.total}}</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--重置-->
    <div class="modal fade" id="surveyResetModal" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("reg", "重置");?></h4>
            </div>
            <div class="modal-body">
                <div>是否确认重置并清除该学生所填写的内容？</div>
                <div>重置之后家长可以再次填写问卷</div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                <button type="button" class="btn btn-primary" :disabled='ResetBtn' @click='surveyReset("modal")'><?php echo Yii::t("user", "确定");?></button>
            </div>
            </div>
        </div> 
    </div>
    <div class="modal fade" id="categoryModal" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("newDS", "Change Category");?></h4>
            </div>
            <div class="modal-body">
                <div class="radio" v-for='list in categoryList'>
                    <label class='font14'>
                        <input type="radio" class='mt5' name="optionsRadios" :value="list.key" v-model='checkCategory' :disabled='currentCategory.category==list.key?true:false'>
                        {{list.value}}
                    </label>
                </div>
                <div class='borderTop' v-if='ivystaff_it==1'>
                    <div class="checkbox">
                        <label class='font14'>
                            <input type="checkbox"  class='mt5' v-model='isDev'>此操作将不更新最后编辑人信息
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel");?></button>
                <button type="button" class="btn btn-primary"  @click='categoryUpdate(currentCategory,currentCategoryIndex,"modal")'><?php echo Yii::t("global", "OK");?></button>
            </div>
            </div>
        </div>  
    </div>
</div>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    $(document).ready(function () {
    // 通过该方法来为每次弹出的模态框设置最新的zIndex值，从而使最新的modal显示在最前面
        $(document).on('show.bs.modal', '.modal', function (event) {
            var zIndex = 1040 + (10 * $('.modal:visible').length);
            $(this).css('z-index', zIndex);
            setTimeout(function() {
                $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
            }, 0);
        });
    });
    var data = <?php echo json_encode($data); ?>;
    var toReplyNum = '<?php echo $this->toReplyNum; ?>';
    // var staff = '<?php echo CHtml::encode(Yii::app()->request->getParam('staff','')); ?>';
    var height=document.documentElement.clientHeight;
    var branchId = '<?php echo $this->branchId;?>';
    var container = new Vue({
        el: "#container",
        data: {
            branchId:branchId,
            height:height,
            toReplyNum:toReplyNum,
            emailType:'emailFail',
            gradeList:data.configList.gradeList,
            gradeGroupList:data.configList.gradeGroupList,
            levelList:data.configList.levelList,
            publishTypeList:data.configList.publishTypeList,
            gradeGroupType:'',
            groupStudentNum:data.groupStudents,
            dataList:[],
            page:{},
            userInfoList:[],
            responseData:{},
            emailTaskData:{},
            sendEmailList:{},
            emailTemplate:'',
            sendnewDS:{},
            stepList:{},
            noEmailList:[],
            childInfo:{},
            errorList:[],
            loading:false,
            initLoading:false,
            commentsList:[],
            childIdList:{},
            staffInfoList:{},
            commentsPage:{},
            commentsChildInfo:{},
            commentsClassInfo:{},
            wantSayData:{},
            commentsPageNum:1,
            contentData:{},
            attachments:[],
            confirmData:{},
            confirmType:'',
            classList:{},
            classTitle:{},
            delData:{},
            qrcodeImg:'',
            previewQrcode:'',
            shareQrcode:'',
            sendConfirm:false,
            sendPassword:'',
            allGrades:[],
            addresser:{},
            delStatus:1,
            viewList:[],
            optionA:['A','B','C','D','E','F','G','H','I','J'],
            optionId:'',
            questionData:{},
            sort:[],
            options:{},
            color:{
                A:'#759FFC',
                B:'#F9C838',
                C:'#FA904B',
                D:'#51CA98',
                E:'#7D78EE',
                F:'#C65BF9',
                G:'#22BEE9',
                H:'#0D8151',
                I:'#C2CC4F',
                J:'#FF7D71'
            },
            optionMemo:{},
            optionChildInfo:{},
            noticeChildOption:{},
            exportBtn:false,
            optionMemoList:[],
            publish_at:'',
            publish_atHours:'00',
            publish_atMinutes:'00',
            publishInfoData:{},
            expired_at:'',
            expired_atHours:'00',
            expired_atMinutes:'00',
            endStatus:'',
            publishStatus:false,
            publish_send:false,
            require_emailing:false,
            publishInfoId:'',
            copyList:{},
            copyData:{},
            copyModel:{},
            copyLen:{},
            multiple: <?php echo $this->multipleBranch ? 1 : 0; ?>,
            clean_email: false,
            ivystaff_it: <?php echo Yii::app()->user->checkAccess('ivystaff_it') ? 1 : 0 ?>,
            notice_id:"",
            answerType:'',
            questionsList:{},
            questionsIndex:null,
            currentQuestionData:{},
            questionsOptionMemoList:[],
            questionOptionData:{},
            questionGrade:'0',
            questionsOptionMemo:[],
            shareList:{},
            shareType:'',
            showSummary:true,
            showPublisher:true,
            htmlContent:1,
            resignList:[],
            normalList:[],
            staff:{},
            searchText:'',
            copySearchText:'',
            isSearch:false,
            sign_as_uid:[],
            qrcodeBox:false,
            wechatData:{},
            scanQrcodeBox:false,
            resetChild:'',
            ResetBtn:false,
            school_title:'',
            timer:'',
            showImgBig:false,
            categoryList:[],
            categoryFilter:'',
            currentCategory:{},
            currentCategoryIndex:'',
            checkCategory:'',
            isDev:true,
            CopyPages:{},
            echartsHeight:0,
            echartsHeightOne:0,
            searchTeacher:'',
            showSelect:false,

        },
        watch: {
            
        },
        created: function() {
            this.getList()
            this.showWechatQrcode()
        },
        computed: {
            teacherSearchData: function() {
                var search = this.searchTeacher;
                var searchVal = ''; //搜索后的数据
                if(search) {
                    searchVal =Object.values(this.userInfoList).filter(function(product) {
                        return Object.keys(product).some(function(key) {
                            return String(product['name'].toLowerCase()).indexOf(search.toLowerCase()) !== -1;
                        })
                    })
                    return searchVal;
                }
                return this.userInfoList;
            },
        },
        methods: {
            showFocus(){
                this.showSelect=!this.showSelect
            },
            showCategoryList(id){
                return this.categoryList.find(item2 =>item2.key === id).value   
            },
            categoryUpdate(list,index,type){
                if(type=='show'){
                    this.currentCategory=list
                    this.currentCategoryIndex=index
                    $("#categoryModal").modal('show');
                    this.isDev=true
                    return
                }
                if(this.checkCategory==''){
                    return
                }
                let that=this
                let postData={}
                if(this.ivystaff_it==1){
                    postData={
                        id:this.currentCategory._id,
                        category:this.checkCategory,
                        isDev:this.isDev
                    }
                }else{
                    postData={
                        id:this.currentCategory._id,
                        category:this.checkCategory,
                    }
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("categoryUpdate") ?>',
                    type: "post",
                    dataType: 'json',
                    data: postData,
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg:data.state
                            })
                            $("#categoryModal").modal('hide');
                            Vue.set(that.dataList[that.currentCategoryIndex], 'category',that.checkCategory);    
                        }
                    },
                    error: function(data) {
                        
                    },
                })
            },
            showUnWechatQrcode(){
                let that=this
                if(this.scanQrcodeBox){
                    clearInterval(this.timer);
                    that.scanQrcodeBox=!that.scanQrcodeBox
                    return
                }else{
                    this.timer =setInterval(this.showWechatQrcode, 5000);   
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("directMessage/teacherBindQrcode") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {},
                    success: function(data) {
                        if (data.state == 'success') {
                            that.scanQrcodeBox=!that.scanQrcodeBox
                            that.$nextTick(() => {
                                $('#wechatQrcodeBind').qrcode({width: 124,height: 124,text:data.data});
                            })                            
                        }
                    },
                    error: function(data) {
                        
                    },
                })
            },
            hideQrcode(){
                this.qrcodeBox=false;
                this.scanQrcodeBox=false;
                clearInterval(this.timer);
            },
            showWechatQrcode(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("directMessage/teacherBindInfo") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {},
                    success: function(data) {
                        if (data.state == 'success') {
                            that.wechatData=data.data
                            if(data.data.state==1){
                                if(that.scanQrcodeBox){
                                    that.scanQrcodeBox=false
                                    that.qrcodeBox=true
                                }
                                clearInterval(that.timer);
                            }
                        }
                    },
                    error: function(data) {
                        
                    },
                })
            },
            unbind(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("directMessage/teacherUnbind") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {},
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg:data.message
                            })
                            that.qrcodeBox=false
                            that.showWechatQrcode()
                        }else{
                            resultTip({
                                    error: 'warning',
                                    msg:data.message
                                })
                        }
                    },
                    error: function(data) {
                        
                    },
                })
            },
            chooseTeacher(){
                var resignList=[]
                var normalList=[]
                this.userInfoList.forEach(item => { 
                    if (this.sign_as_uid.indexOf(parseInt(item.uid))!=-1){
                        if(item.level=='0'){
                            resignList.push(item)
                        }else{
                            normalList.push(item)
                        }
                    }
                })
                this.normalList=this.sortTea(normalList)
                this.resignList=this.sortTea(resignList)
                this.userInfoList=this.sortTea(this.userInfoList)
                this.showSelect=false
                this.searchTeacher=''
                $("#teacherList").modal('show');
            },
            sortTea(list){
                list.sort((x,y)=>{
                    return x['name'].localeCompare(y['name'])
                })
                return list
            },
            getUrl(teacher) {
                this.staff=teacher
                $("#teacherList").modal('hide');
                this.getList()
            },
            saveImg(){
                html2canvas(document.querySelector(".shareContent",{
                    allowTaint: false,
                    useCORS: true,//允许跨域
                })).then(canvas => {
                    $('#canvas').html(canvas)
                    let src = canvas.toDataURL('image/png', 1)
                    let image = new Image()
                    image.src = src
                    let url = image.src.replace(/^data:image\/[^;]/, 'data:application/octet-stream')//输出类型
                    let a = document.createElement('a');//随便创建一个元素
                    a.download = '公告.png'// 设置下载的文件名，默认是'下载'
                    a.href = url
                    document.body.appendChild(a)
                    a.click()
                    a.remove() // 下载之后把创建的元素删除
                });
            },
            shareImg(type){
                this.htmlContent=1
                this.showSummary=true
                this.showPublisher=true
                this.shareType=type
                $('#shareModal').modal('show')
            },
            publish_atVal(){
                let that=this
                setTimeout(function() {
                    that.$nextTick(()=>{
                        container.publish_at = $('#publish_at').val();
                    })
                }, 500);
            },
            expired_atVal(){
                let that=this
                setTimeout(function() {
                    that.$nextTick(()=>{
                        container.expired_at = $('#expired_at').val();
                    })
                }, 500);
            },
            release(list,type){
                let that=this
                var result
                if(list.expired_at!=0){
                    var releaseDate=list.expired_at
                    var nowDate=that.newDate('')
                    var date1 = new Date(releaseDate);
                    var date2 = new Date(nowDate);

                    if(date2 > date1){
                        if(type){
                            result='<?php echo Yii::t("newDS", "Expire: ");?>'+releaseDate
                        }else{
                          result='<span class="text-muted"><?php echo Yii::t("newDS", "expired");?></span> '
                        }
                    }else{
                        if(list.publish_type==0){
                            result="<i class='point red'></i><?php echo Yii::t("newDS", "offline");?>"
                        }else if(list.publish_type==10){
                            if(type){
                                result='<?php echo Yii::t("newDS", "Expire: ");?>'+ releaseDate
                            }else{
                            result='<i class="point green"></i><?php echo Yii::t("newDS", "online");?>'
                            }
                        } 
                    }
                }else{
                    if(list.publish_type==0){
                        result="<i class='point red'></i><?php echo Yii::t("newDS", "offline");?>"
                    }else if(list.publish_type==10){
                        result='<i class="point green"></i><?php echo Yii::t("newDS", "online");?>'
                    }else{
                        result='<i class="point yellow"></i><?php echo Yii::t("newDS", "offline");?>'
                    }
                }
               return result
            },
            newDate(dateTime,type){
                if(dateTime!=''){                    
                    var date = new Date(dateTime.replace(/\-/g, '/'));
                }else{
                    var date = new Date();
                }
                const Y = date.getFullYear() + '-';
                const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
                const D = (date.getDate() < 10 ? '0'+date.getDate() : date.getDate()) + ' ';
                const h = (date.getHours() < 10 ? '0'+date.getHours() : date.getHours()) ;
                const m = (date.getMinutes() <10 ? '0'+date.getMinutes() : date.getMinutes());
                const s = date.getSeconds(); // 秒
                const dateString = Y + M + D + h+':' + m+':' + s;
                if(type=='hours'){
                    return h
                } if(type=='minutes'){
                    return m
                } if(type=='date'){
                    return Y + M + D 
                }
                if(!type){
                    return dateString;
                }
            },
            userInfo(list,type){
                var data
                this.userInfoList.forEach(item => {
                     if (item.uid==list.updated_by) {
                         data= item[type]
                    } 
                })
                return data
            },
            numberPeople(list,type){
                var result
                var total=list.student_num;
                var num=0;
                // for(var key in  this.groupStudentNum){
                //     if (list.grade_groups.indexOf(key)!=-1) {                        
                //         total+= parseInt(this.groupStudentNum[key].childInfo.length)
                //     } 
                // }
                if(this.responseData[list._id]!=undefined){
                    num=this.responseData[list._id].confirmedNum
                }
                var progress=num <= 0? "0%" : Math.round((num / total) * 10000) / 100.0 + "%";
                if(type=='look'){
                    if(this.responseData[list._id]!=undefined){
                        result=this.responseData[list._id].reviewedChildNum
                    }else{
                        result=0
                    }
                }
                else if(type=='progressWidth'){ 
                   result='width:'+progress
                }else if(type=='progress'){
                    result='('+num+'/'+total+')'+progress
                }
                return result
            },
            getList(page){
                let that=this
                if(page){
                    that.loading=true;
                }else{
                    that.initLoading=true
                }
                
                $.ajax({
                    url: '<?php echo $this->createUrl("getList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        gradeGroup:that.gradeGroupType,
                        pageNum: page ? this.pageNum : 1,
                        staff: this.staff.uid,
                        searchText:this.searchText,
                        category:that.categoryFilter
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            if(page){
                                that.loading=false;
                            }else{
                                that.initLoading=false
                            }
                            if(data.data.list && data.data.list!=null){
                                that.dataList = data.data.list
                            }
                            that.school_title=data.data.school_title
                            
                            that.userInfoList=data.data.userInfoList
                            that.responseData=data.data.responseData
                            that.emailTaskData=data.data.emailTaskData
                            that.addresser=data.data.addresser
                            that.categoryList=data.data.categoryList
                            that.sign_as_uid=data.data.sign_as_uid
                            that.$nextTick(() => {    
                                var listTitle =document.querySelectorAll(".listTitle");
                                var instance = new Mark(listTitle);
                                instance.unmark(that.copySearchText ,{separateWordSearch: false});
                                var listSummary = document.querySelectorAll(".listSummary");
                                var instance = new Mark(listSummary);
                                instance.unmark(that.copySearchText ,{separateWordSearch: false});
                            })
                            if(that.searchText!=''){
                                that.isSearch=true
                                that.copySearchText= JSON.parse(JSON.stringify(that.searchText))
                                that.$nextTick(() => {    
                                    var listTitle =document.querySelectorAll(".listTitle");
                                    var instance = new Mark(listTitle);
                                    instance.mark(that.copySearchText ,{separateWordSearch: false});
                                    var listSummary = document.querySelectorAll(".listSummary");
                                    var instance = new Mark(listSummary);
                                    instance.mark(that.copySearchText ,{separateWordSearch: false});
                                })
                            }else{
                                that.copySearchText=''
                                that.isSearch=false
                            }
                            that.CopyPages=data.data.pagination
                            if(!page){
                                that.pages = data.data.pagination
                                that.pageNum='1'
                                that.initPage()
                            }
                        }
                    },
                    error: function(data) {
                        if(page){
                            that.loading=false;
                        }else{
                            that.initLoading=false
                        }
                    },
                })
            },
            initPage(){
                var _this = this;
                _this.CopyPages=JSON.parse(JSON.stringify(_this.pages))
                if(_this.CopyPages.pages>=10){
                    _this.pages.pages=[1,2,3,4,5,6,7,8,9,10]
               }else{
                    var numPage=[]
                    for(var i=1;i<=_this.CopyPages.pages;i++){
                        numPage.push(i)
                    }
                    _this.pages.pages=numPage
               }
            },
            plus(index) { 
                var _this = this;
                _this.pageNum = Number(index)
                this.pagesSize()
                this.getList('page')
            },
            pagesSize(){
                var _this = this;
                if(_this.pageNum<10){
                    if(_this.CopyPages.pages>=10){
                        _this.pages.pages=[1,2,3,4,5,6,7,8,9,10]
                    }else{
                        var numPage=[]
                        for(var i=1;i<=_this.CopyPages.pages;i++){
                            numPage.push(i)
                        }
                        _this.pages.pages=numPage
                    }
                }else if(_this.pageNum<=_this.CopyPages.pages){
                    if(_this.CopyPages.pages-_this.pageNum>=4){
                        var minPage=_this.pageNum-5
                        var maxPage=_this.pageNum+4
                    }else{
                        var minPage=_this.pageNum-(9-((_this.CopyPages.pages-_this.pageNum)))
                        var maxPage=_this.pageNum+(_this.CopyPages.pages-_this.pageNum)
                    }
                    var numPage=[]
                    for(var i=minPage;i<=maxPage;i++){
                        numPage.push(i)
                    }
                    _this.pages.pages=numPage
                }
            },
            next(index){
                var _this = this;
                _this.pageNum = Number(index) + 1
                this.pagesSize()
                this.getList('page')
            },
            prev(index) {
                var _this = this;
                _this.pageNum = Number(index) - 1
                this.pagesSize()
                this.getList('page')
            },
            delNotice(list){
                this.delData=list;
                this.delStatus=1;
                $('#deleteModal').modal('show')
            },
            delList(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("delete") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:that.delData._id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.delStatus=data.data
                            if(data.data==1){
                                that.dataList.forEach((item,index) => {
                                    if (item._id==that.delData._id) {
                                        that.dataList.splice(index, 1)
                                    } 
                                })
                               $('#deleteModal').modal('hide')
                            }else if(data.data==10){
                               $('#deleteModal').modal('show')
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg:'<?php echo Yii::t("newDS", "Cannot delete due to existence of batch emailing.");?>'
                                })
                               $('#deleteModal').modal('hide')
                            }
                        } else {
                            resultTip({
                                error: 'warning',
                                msg:data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg:data.message
                        });
                    },
                })
            },
            nociceOffline(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("offline") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:that.delData._id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getList()
                            $('#deleteModal').modal('hide')
                            resultTip({
                                msg:data.message
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg:data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg:data.message
                        });
                    },
                })
            },
            parentReply(list,type){
                this.confirmType=type
                this.confirmData=list
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getResponseData") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:list._id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.classList=data.data.noticeClassChild
                            that.viewList=data.data.noticeChildViewed
                            that.options=data.data.config.options
                            that.sort=data.data.config.sort
                            if(data.data.noticeChildOption){
                                var stringOption={}
                                for(var key in data.data.noticeChildOption){
                                    stringOption[key]=data.data.noticeChildOption[key]+''
                                }
                                that.noticeChildOption=stringOption
                            }
                            $('#parentReplyModal').modal('show')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg:data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg:data.message
                        });
                    },
                })
            },
            hrefChild(id){
               window.open('<?php echo $this->createUrl('/child/index/index'); ?>&childid='+id)
            },
            confirmedChildid(id,type){
                var result;
                if(type=='confirm'){
                    var list=this.responseData[this.confirmData._id]
                    if(list){
                        if(list.confirmedChildids.indexOf(id)!=-1){
                            result=true
                        }
                    }else{
                        result=false
                    }
                }else{
                    if(this.viewList.indexOf(id)!=-1){
                        result=true
                    }
                }
                return result
            },
            sendEmail(list){
                this.sendEmailList=list
                this.sendConfirm=false
                this.sendPassword == ''
                $('#sendEmailModal').modal('show')  
            },
            emailList(type){
                this.emailType=type
            },
            emailData(list){
                let that=this
                this.emailType='emailFail'
                $.ajax({
                    url: '<?php echo $this->createUrl("emailReport") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {id:list._id},
                    success: function(data) {
                        if (data.state == 'success') {
                           that.sendnewDS=data.data
                           that.stepList=data.data.stepList
                           that.noEmailList=data.data.noEmailList
                           that.childInfo=data.data.childInfo
                           that.errorList=data.data.errorList
                           that.notice_id = list._id
                           $('#emailModal').modal('show')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            syncEmailTask() {
                let that=this
                this.emailType='emailFail'
                $.ajax({
                    url: '<?php echo $this->createUrl("emailReport") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {id: that.notice_id},
                    success: function(data) {
                        if (data.state == 'success') {
                           that.sendnewDS=data.data
                           that.stepList=data.data.stepList
                           that.noEmailList=data.data.noEmailList
                           that.childInfo=data.data.childInfo
                           that.errorList=data.data.errorList
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            wantSay(list,type){
                this.wantSayData=list
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("comments") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:list._id,
                        // pageNum: type ? this.commentsPageNum : 1,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            data.data.childIdList.forEach((item) => {
                                data.data.comments[item].forEach((list) => {
                                    list.imgUrl=[]
                                    list.pdfUrl=[]
                                    if(list.attachments.length!=0){
                                        list.attachments.forEach((item) => {
                                            if(data.data.attachmentList[item]){
                                                let type=data.data.attachmentList[item].mimetype.split('/')[0]
                                                if(type=="image"){
                                                list.imgUrl.push(data.data.attachmentList[item])
                                                }else{
                                                list.pdfUrl.push(data.data.attachmentList[item])
                                                }
                                            }
                                        })
                                    }
                                })
                            })
                            that.commentsList=data.data.comments;
                            that.childIdList=data.data.childIdList;
                            that.staffInfoList=data.data.staffInfoList;
                            that.commentsPage=data.data.pagination;
                            that.commentsChildInfo=data.data.childInfo;
                            that.commentsClassInfo=data.data.classInfo;
                            if(!type){
                                $('#wantSayModal').modal('show')
                            }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            viewTemplate(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("emailReport") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {id:that.sendEmailList._id},
                    success: function(data) {
                        if (data.state == 'success') {
                           that.emailTemplate=data.data
                        
                            $('#temaplateModal').modal('show')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            sendTemplate(){
                let that=this
                if(!this.sendConfirm){
                    resultTip({
                        error: 'warning',
                        msg: '请勾选确认发送'
                    });
                    return
                }  
                if(this.sendPassword == ''){
                    resultTip({
                        error: 'warning',
                        msg: '<?php echo Yii::t("newDS", "Please input your DaystarOnline password to preceed");?>'
                    });
                    return
                }   
                
                $.ajax({
                    url: '<?php echo $this->createUrl("sendEmail") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {id:that.sendEmailList._id,pass:that.sendPassword},
                    success: function(data) {
                        if (data.state == 'success') {
                            $('#sendEmailModal').modal('hide')  
                            resultTip({
                                msg:'<?php echo Yii::t("newDS", "Task queued, waiting for sending.");?>'
                            });
                            that.getList()
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            viewContent(list){ 
                let that=this
                that.contentData={}
                $.ajax({
                    url: '<?php echo $this->createUrl("getOne") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id: list._id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.contentData=data.data
                           $('#contentModal').modal('show')
                           that.$nextTick(() => {    
                            if(that.copySearchText!='' && that.isSearch){
                                    var context = document.querySelector(".contentCn");
                                    var instance = new Mark(context);
                                    instance.mark(that.copySearchText ,{separateWordSearch: false});
                                    var contentEn = document.querySelector(".contentEn");
                                    var instance = new Mark(contentEn);
                                    instance.mark(that.copySearchText ,{separateWordSearch: false});
                                    var title = document.querySelector(".titleCn");
                                    var instance = new Mark(title);
                                    instance.mark(that.copySearchText ,{separateWordSearch: false});
                                    var titleEn = document.querySelector(".titleEn");
                                    var instance = new Mark(titleEn);
                                    instance.mark(that.copySearchText ,{separateWordSearch: false});
                                    var summary_cn = document.querySelector(".summary_cn");
                                    var instance = new Mark(summary_cn);
                                    instance.mark(that.copySearchText ,{separateWordSearch: false});
                                    var summary_en = document.querySelector(".summary_en");
                                    var instance = new Mark(summary_en);
                                    instance.mark(that.copySearchText ,{separateWordSearch: false});
                                }
                           })
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
                
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/getAttachments") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        linkId: list._id,
                        linkType:'notice'
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.attachments=data.data.data
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            oneQrcode(list){
                this.shareList=list
                this.shareList.school_title=this.school_title
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getQrCode") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:list._id,
                        type:'preview'
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.previewQrcode=data.data.preview
                            that.shareQrcode=data.data.share
                            $('#qrcodeModal').modal('show')
                        }
                    },
                    error: function(data) {
                        
                    },
                })
            },
            question(list,type){
                this.optionId=''
                this.confirmData=list
                this.answerType=type
                let that=this
                if(type=='one'){
                    $.ajax({
                        url: '<?php echo $this->createUrl("confirmOverview") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            id:list._id,
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.questionData=data.data
                                that.options=data.data.config.options
                                that.sort=data.data.config.sort
                                that.optionChildInfo=data.data.childInfo
                                var dataList=[]
                                that.optionMemoList=[]
                                for(var j=0;j<that.sort.length;j++){
                                    let key=that.sort[j]
                                    dataList.push({
                                        value:data.data.optionNum[key],
                                        name:that.optionA[j]
                                    })
                                    that.optionMemoList.push(that.optionA[j] +'：'+data.data.config.options[key].cn+'-'+data.data.config.options[key].en)
                                }
                                that.optionRemark('all')
                                $('#echart').removeAttr('_echarts_instance_');
                                $('#questionModal').modal('show')
                                setTimeout(() => {
                                    that.$nextTick(function(){
                                        let id=document.getElementById('echart')
                                        var myCharts = echarts.init(id)
                                        var option = that.optionData(myCharts,dataList)
                                        myCharts.setOption(option,true);
                                        myCharts.resize()
                                        if(that.$refs.echartsHeightOne){
                                            that.echartsHeightOne = that.$refs.echartsHeightOne.offsetHeight-70;
                                        }
                                    })
                                },200);
                                
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        },
                    })
                }else{
                    that.questionsIndex=null
                    that.currentQuestionData={}
                    $.ajax({
                        url: '<?php echo $this->createUrl("getSurveyResultQuestions") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            sid:list.survey_id,
                            id:list._id,

                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.grade=''
                                that.questionsList=data.data
                                that.showQuestions(data.data.questions_list[Object.keys(data.data.questions_list)[0]],0)
                                $('#questionModal').modal('show')
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        },
                    })
                }
              
            },
            showQuestions(obj,index){ 
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getSurveyResult") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        "sid":this.confirmData.survey_id,
                        "id":this.confirmData._id,
                        "question_id": obj.id,
                        grade:this.questionGrade
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.questionsIndex=index
                            that.currentQuestionData=obj
                            that.questionOptionData=data.data
                            that.questionOptionData.sort=[]
                            if(obj.option){
                                var options=obj.option
                                var dataList=[]
                                that.questionsOptionMemoList=[]
                                for(var j=0;j<options.length;j++){
                                    let key=options[j]
                                    that.questionOptionData.sort.push(key.id)
                                    dataList.push({
                                        value:data.data.option_total[key.id],
                                        name:that.optionA[j]
                                    })
                                    that.questionsOptionMemoList.push(that.optionA[j] +'：'+key.title)
                                }
                                that.questionOptionRemark('all')
                                $('#multipleEchart').removeAttr('_echarts_instance_');
                                that.$nextTick(function(){
                                    let id=document.getElementById('multipleEchart')
                                    var myCharts = echarts.init(id)
                                    var option = that.optionData(myCharts,dataList)
                                    myCharts.setOption(option,true);
                                    myCharts.resize()
                                })
                                that.$nextTick(function(){
                                    if(that.$refs.echartsHeight){
                                        that.echartsHeight = that.$refs.echartsHeight.offsetHeight-70;
                                    }
                                })
                            }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
                
                
            },
            questionOptionRemark(list){
                let dataList=JSON.parse(JSON.stringify(this.questionOptionData.answer_list))
                this.questionsOptionMemo=[]
                if(list=='all'){
                    this.optionId=''
                    for(var key in dataList){
                        for(var j=0;j<dataList[key].length;j++){
                            for(var i=0;i<dataList[key][j].option.length;i++){
                                dataList[key][j].option[i]=this.questionOptionData.sort.indexOf(dataList[key][j].option[i])
                            }
                        }
                        this.questionsOptionMemo.push(...dataList[key]);
                    }
                }else{
                    this.optionId=list
                    if(dataList[list]){
                        for(var j=0;j<dataList[list].length;j++){
                            for(var i=0;i<dataList[list][j].option.length;i++){
                                dataList[list][j].option[i]=this.questionOptionData.sort.indexOf(dataList[list][j].option[i])
                            }
                        }
                        this.questionsOptionMemo=dataList[list]
                    }else{
                        this.questionsOptionMemo=[]
                    }
                }
                var obj = {};
                this.questionsOptionMemo = this.questionsOptionMemo.reduce(function(item, next) {
                    obj[next.child_id] ? '' : obj[next.child_id] = true && item.push(next);
                    return item;
                }, []);
            },
            optionRemark(list){
                let dataList=JSON.parse(JSON.stringify(this.questionData.optionMemo))
                this.optionMemo=[]
                if(list=='all'){
                    this.optionId=''
                    for(var key in dataList){
                        for(var j=0;j<dataList[key].length;j++){
                            dataList[key][j].option=this.sort.indexOf(key)
                        }
                        this.optionMemo.push(...dataList[key]);
                    }
                }else{
                    this.optionId=list
                    if(dataList[list]){
                        for(var j=0;j<dataList[list].length;j++){
                            dataList[list][j].option=this.sort.indexOf(list)
                            }
                        this.optionMemo=dataList[list]
                    }else{
                        this.optionMemo=[]
                    }
                }
            },
            optionData(myCharts, destData){
                var option = {
                    tooltip: {
                        trigger: 'item',
                        formatter: '{a} <br/>{b} : {c} ({d}%)'
                    },
                    legend: {
                        // orient: 'vertical',
                        bottom: 10,
                        left: 'center',
                    },
                    color:['#759FFC','#F9C838','#F99653','#51CA98','#7D78EE'],
                    series: [
                        {
                            name: '<?php echo Yii::t("newDS", "Read Report");?>',
                            type: 'pie',
                            radius: '55%',
                            center: ['50%', '45%'],
                            data:destData,
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            }
                        }
                    ]
                };
                return option
            },
            exportData(){
               let that=this
               that.exportBtn=true
                $.ajax({
                    url: '<?php echo $this->createUrl("surveyExport") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:this.confirmData._id,
                    },
                    success: function(data) {
                        const filename ='notice_survey_'+that.confirmData._id+'.xlsx';
                        const ws_name = "SheetJS";
                        var exportDatas = [];
                        for(var i=0;i<data.length;i++){
                            exportDatas.push(
                                {'<?php echo Yii::t('labels', 'Student ID') ?>':data[i].id,
                                '<?php echo Yii::t('labels', 'Name')?>':data[i].name,
                                "<?php echo Yii::t('labels', 'Class') ?>":data[i].className,
                                "<?php echo Yii::t('labels', 'Father Mobile') ?>":data[i].fPhone,
                                "<?php echo Yii::t('labels', 'Father Email')?>":data[i].fEmail,
                                "<?php echo Yii::t('labels', 'Mother Mobile')?>":data[i].mPhone,
                                "<?php echo Yii::t('labels', 'Mother Email')?>":data[i].mEmail,
                                "<?php echo Yii::t('report','Options')?>":data[i].option,
                                "<?php echo Yii::t("labels","Memo");?>":data[i].memo,
                                });
                        }
                        var da=XLSX.utils.json_to_sheet(exportDatas,{
                            origin:'A1',// 从A1开始增加内容
                            header: ['<?php echo Yii::t('labels', 'Student ID') ?>',
                                '<?php echo Yii::t('labels', 'Name')?>',
                                '<?php echo Yii::t('labels', 'Class') ?>',
                                '<?php echo Yii::t('labels', 'Father Mobile') ?>',
                                '<?php echo Yii::t('labels', 'Father Email')?>',
                                '<?php echo Yii::t('labels', 'Mother Mobile')?>',
                                '<?php echo Yii::t('labels', 'Mother Email')?>',
                                '<?php echo Yii::t('report','Options')?>',
                                '<?php echo Yii::t("labels","Memo");?>'],
                        });
                        const workbook = XLSX.utils.book_new();
                        XLSX.utils.book_append_sheet(workbook, da, ws_name);
                        const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                        const blob = new Blob([wbout], {type: 'application/octet-stream'});
                        let link = document.createElement('a');
                        link.href = URL.createObjectURL(blob);
                        link.download = filename;
                        link.click();
                        setTimeout(function() {
                            // 延时释放掉obj
                            URL.revokeObjectURL(link.href);
                            link.remove();
                            that.exportBtn=false
                        }, 500);
                    },
                    error: function(data) {
                        that.exportBtn=false
                    },
                })
                
            },
            surveyReset(child_id){
                if (child_id!='modal') {
                    this.resetChild=child_id
                    $('#surveyResetModal').modal('show')
                    return
                }
                    let that=this
                    that.ResetBtn=true
                $.ajax({
                    url: '<?php echo $this->createUrl("resetSurvey") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:this.confirmData._id,
                        child_id:this.resetChild,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                error: 'success',
                                msg: data.message
                            });
                            $('#surveyResetModal').modal('hide')
                                            
                            var list=that.responseData[that.confirmData._id]
                            var index=list.confirmedChildids.indexOf(that.resetChild)
                            if(index!=-1){
                                list.confirmedChildids.splice(index,1)
                            }
                            if(that.confirmData.require_response==2){            
                                delete that.noticeChildOption[that.resetChild]
                            }
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.ResetBtn=false
                    },
                    error: function(data) {
                        that.ResetBtn=false
                    },
                })
            },
            exportSurveyData(){
               let that=this
               that.exportBtn=true
                $.ajax({
                    url: '<?php echo $this->createUrl("getSurveyResultExport") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:this.confirmData._id,
                        sid:this.confirmData.survey_id,
                    },
                    success: function(data) {
                        const filename ='notice_survey_'+that.confirmData._id+'.xlsx';
                        const ws_name = "SheetJS";
                        const header = ['A', 'B', 'C' , 'D', 'E', 'F', 'G', 'H'];

                        var exportDatas = [];
                        exportDatas.push(
                                {
                                    'A':'<?php echo Yii::t('labels', 'Student ID') ?>',
                                    'B':'<?php echo Yii::t('labels', 'Name')?>',
                                    "C":'<?php echo Yii::t('labels', 'Class') ?>',
                                    "D":'<?php echo Yii::t('labels', 'Father Mobile')?>',
                                    "E": '<?php echo Yii::t('labels', 'Father Email')?>',
                                    "F": '<?php echo Yii::t('labels', 'Mother Mobile')?>',
                                    "G": '<?php echo Yii::t('labels', 'Mother Email')?>',
                                    "H": data.data.export_title,
                                });

                        for (let index = 0; index < data.data.export_list.length; index++) {
                            const exportItem = data.data.export_list[index]
                            const childId = exportItem.child_id;
                            const childName = exportItem.name
                            const className = exportItem.class_name
                            const fphone = exportItem.f_phone
                            const femail = exportItem.f_email
                            const mphone = exportItem.m_phone
                            const memail = exportItem.m_email
                            const answer = exportItem.answer_content
                            exportDatas.push(
                                {
                                    'A':childId,
                                    'B':childName,
                                    "C":className,
                                    "D":fphone,
                                    "E": femail,
                                    "F": mphone,
                                    "G": memail,
                                    "H":answer,
                                });
                        }

                        var da=XLSX.utils.json_to_sheet(exportDatas,{
                            origin:'A1',// 从A1开始增加内容
                            header: header,
                            skipHeader: true,
                        });
                        const workbook = XLSX.utils.book_new();
                        XLSX.utils.book_append_sheet(workbook, da, ws_name);
                        const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                        const blob = new Blob([wbout], {type: 'application/octet-stream'});
                        let link = document.createElement('a');
                        link.href = URL.createObjectURL(blob);
                        link.download = filename;
                        link.click();
                        setTimeout(function() {
                            // 延时释放掉obj
                            URL.revokeObjectURL(link.href);
                            link.remove();
                            that.exportBtn=false
                        }, 500);
                    },
                    error: function(data) {
                        that.exportBtn=false
                    },
                })
                
            },
            viewOption(id){
                if(this.sort.indexOf(id)!=-1){
                   return this.optionA[this.sort.indexOf(id)]
                }
                
            },
            publishInfo(list){
                let that=this
                this.publishInfoId=list._id
                $.ajax({
                    url: '<?php echo $this->createUrl("publishInfo") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        id:list._id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.publishInfoData=data.data
                            that.endStatus=that.publishInfoData.expired_at==0?0:1
                            that.require_emailing=that.publishInfoData.require_emailing==0?false:true
                            that.publishStatus=that.publishInfoData.publish_type==0?false:true
                            if(that.publishInfoData.expired_at!=0){
                                that.expired_atHours=that.newDate(that.publishInfoData.expired_at,'hours')
                                that.expired_atMinutes=that.newDate(that.publishInfoData.expired_at,'minutes')
                                that.expired_at=that.newDate(that.publishInfoData.expired_at,'date')
                            }else{
                                that.expired_atHours='00'
                                that.expired_atMinutes='00'
                                that.expired_at=''
                            }
                            if(that.publishInfoData.publish_at!=''){
                                that.publish_atHours=that.newDate(that.publishInfoData.publish_at,'hours')
                                that.publish_atMinutes=that.newDate(that.publishInfoData.publish_at,'minutes')
                                that.publish_at=that.newDate(that.publishInfoData.publish_at,'date')
                            }else{
                                that.publish_atHours='00'
                                that.publish_atMinutes='00'
                                that.publish_at=''
                            }
                            $('#publishModal').modal('show')
                            setTimeout(function() {
                                that.$nextTick(()=>{
                                    $("#publish_at").datepicker({
                                        dateFormat: "yy-mm-dd ",
                                    });
                                    $("#expired_at").datepicker({
                                        dateFormat: "yy-mm-dd ",
                                    });
                                })
                            }, 500);
                            
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            publishNotice(){
                let that=this
                if(this.publishStatus && $('#publish_at').val()==""){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("newDS", "Please select");?><?php echo Yii::t("newDS", "Start");?>'
                    });
                    return
                }
                if(this.endStatus!='0' && $('#expired_at').val()==''){     
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("newDS", "Please select");?><?php echo Yii::t("newDS", "Expire");?>'
                    });
                    return
                }
                this.publishInfoData.publish_type=this.publishStatus?10:0
                this.publishInfoData.require_emailing=this.require_emailing?1:0
                this.publishInfoData.expired_at=this.endStatus==0?0:$('#expired_at').val().trim()+' '+this.expired_atHours+':'+this.expired_atMinutes
                this.publishInfoData.publish_at=$('#publish_at').val().trim()+' '+this.publish_atHours+':'+this.publish_atMinutes
                $.ajax({
                    url: '<?php echo $this->createUrl("publish") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:this.publishInfoId,
                        "publish_type":this.publishInfoData.publish_type,
                        "publish_at": this.publishInfoData.publish_at,
                        "expired_at": this.publishInfoData.expired_at,
                        "require_emailing": this.publishInfoData.require_emailing,
                        "clean_email": this.clean_email ? 1 : 0,
                    },
                    success: function(data) {
                        if(data.state == 'success'){
                            that.getList()
                            $('#publishModal').modal('hide')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }

                    }
                })
            },
            copyNotice(list){
                let that=this
                that.copyData=list
                this.copyModel={}
                that.copyList={}
                that.copyLen={}
                $.ajax({
                    url: '<?php echo $this->createUrl("copyData") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.copyModel={}
                            for(var i=0;i<data.data.length;i++){
                                that.copyModel[data.data[i].schoolId]={
                                    schoolId:'',
                                    grades:[],
                                    group:[],
                                    nextGrades:[]
                                }
                                that.copyLen[data.data[i].schoolId]=0
                                for(j=0;j<data.data[i].grades.length;j++){
                                    that.copyLen[data.data[i].schoolId]+=data.data[i].grades[j].item.length
                                }
                            }
                            that.copyList=data.data
                            $('#copyModal').modal('show')
                        }
                    },
                    error: function(data) {
                        
                    },
                })
            },
            allSchool(list,schoolid,e){
                if(e.target.checked){
                    this.copyModel[schoolid].grades=[]
                    this.copyModel[schoolid].schoolId=schoolid
                    if(this.copyData.isNext==1){
                        for(var key in list.child_mark){
                            this.copyModel[schoolid].nextGrades.push(key)
                        }
                    }else{
                        for(var i=0;i<list.grades.length;i++){
                            for(var j=0;j<list.grades[i].item.length;j++){
                                this.copyModel[schoolid].grades.push(list.grades[i].item[j].key)
                            }
                            this.copyModel[schoolid].group.push(list.grades[i].key)
                        }
                    }
                   
                }else{
                    this.copyModel[schoolid].nextGrades=[]
                    this.copyModel[schoolid].grades=[]
                    this.copyModel[schoolid].schoolId=''
                    this.copyModel[schoolid].group=[]
                }
                this.$forceUpdate();

            },
            allGroup(list,schoolid,e){
                if(e.target.checked){
                    list.item.map((item) => {
                        if (this.copyModel[schoolid].grades.indexOf(item.key) <0) {
                            this.copyModel[schoolid].grades.push(item.key);
                        }
                    });
                    if(this.copyModel[schoolid].grades.length==this.copyLen[schoolid]){
                        this.copyModel[schoolid].schoolId=true
                    }
                }else{
                    list.item.map((item) => {
                        if (this.copyModel[schoolid].grades.indexOf(item.key) > -1) {
                            this.copyModel[schoolid].grades.splice(this.copyModel[schoolid].grades.indexOf(item.key), 1 );
                        }
                    });
                    if(this.copyModel[schoolid].grades.length!=this.copyLen[schoolid]){
                        this.copyModel[schoolid].schoolId=false
                    }
                }
                this.$forceUpdate();
            },
            allGrade(list,schoolid,e){
                if(e.target.checked){
                    let index = 0;
                    list.item.map((item) => {
                        if (this.copyModel[schoolid].grades.indexOf(item.key)  > -1) {
                            index += 1;
                        }
                    });
                    if(index==list.item.length){
                        this.copyModel[schoolid].group.push(list.key)
                    }
                    if(this.copyModel[schoolid].grades.length==this.copyLen[schoolid]){
                        this.copyModel[schoolid].schoolId=true
                    }
                }else{
                    if (this.copyModel[schoolid].group.indexOf(list.key) > -1) {
                        this.copyModel[schoolid].group.splice(this.copyModel[schoolid].group.indexOf(list.key), 1 );
                    }
                    if(this.copyModel[schoolid].grades.length!=this.copyLen[schoolid]){
                        this.copyModel[schoolid].schoolId=false
                    }
                }
                this.$forceUpdate();
            },
            allNextGrade(list,key,schoolid,e){
                if(e.target.checked){
                    if(this.copyModel[schoolid].nextGrades.length==Object.keys(list.child_mark).length ){
                        this.copyModel[schoolid].schoolId=true
                    }
                }else{
                    if(this.copyModel[schoolid].nextGrades.length!=Object.keys(list.child_mark).length){
                        this.copyModel[schoolid].schoolId=false
                    }
                }
                this.$forceUpdate();
            },
            saveCopy(){
                var copyData=[]
                if(this.copyData.isNext==1){
                    for(var key in this.copyModel){
                        if(this.copyModel[key].nextGrades.length!=0){
                            this.copyModel[key].schoolId=key
                        }
                        copyData.push({
                            schoolId:this.copyModel[key].schoolId,
                            grades:this.copyModel[key].nextGrades,
                        })
                    }
                }else{
                    for(var key in this.copyModel){
                        if(this.copyModel[key].grades.length!=0){
                            this.copyModel[key].schoolId=key
                        }
                        copyData.push({
                            schoolId:this.copyModel[key].schoolId,
                            grades:this.copyModel[key].grades,
                        })
                    }
                }
                
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("copy") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        copyData:copyData,
                        id:this.copyData._id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg: data.message
                            });
                            $('#copyModal').modal('hide')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            showImg(list,idx,item){
                var id=list.creator_type+'_'+idx+'_'+item
                var viewer = new Viewer(document.getElementById(id),{
                    fullscreen: false,
                    title:false,
                    show:function(){ 
                        if(list.imgUrl.length==1){
                            $('.viewer-prev').hide()
                            $('.viewer-next').hide()
                        }
                    },
                    hide:function(){ 
                        viewer.destroy()
                    }
                });
                $("#"+id).click();
            },
        }
    })
  
</script>