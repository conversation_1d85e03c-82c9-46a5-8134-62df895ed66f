<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="language" content="en"/>
</head>
<body>
<?php if ($withToolBar): ?>
    <div class="navbar navbar-fixed-top navbar-inverse" role="navigation">
        <?php
        echo CHtml::form($downloadActionUrl);
        echo CHtml::hiddenField('childid', $params['childid']);
        echo CHtml::hiddenField('id', $params['id']);
        echo CHtml::hiddenField('classid', $params['classid']);
        echo CHtml::hiddenField('yid', $params['yid']);
        echo CHtml::hiddenField('courseid', $params['courseid']);
        echo CHtml::submitButton('Download PDF / 下载 PDF', array('style' => 'font-family: Microsoft Yahei'));
        echo CHtml::endForm();
        ?>
    </div>
<?php endif; ?>

<?php

$MYP = array(
    Yii::t('principal', 'Self-Manage. Skills'),
    Yii::t('principal', 'Research Skills'),
    Yii::t('principal', 'Social Skills'),
    Yii::t('principal', 'Thinking Skills'),
    Yii::t('principal', 'Communication Skills'),
);


$comments = array();
foreach ($childcourse as $k => $_childcourse) {
    $comments[$k]['course'] = $_childcourse->reportCourse->getName();
    $comments[$k]['teacherName'] = $_childcourse->teacherName->getName();
    $comments[$k]['TeacherComments'] = ($_childcourse->teacher_message_en) ? nl2br($_childcourse->teacher_message_en) : "-";
    $comments[$k]['StudentComments'] = ($_childcourse->student_message_en) ? nl2br($_childcourse->student_message_en) : "-";
}

?>
<div class="report-wrapper web-view">
    <!--page one-->
    <div id="page-1" class="report-page page-break-after">
        <div class="pull-content">
            <div class="fl logo-top-center"></div>
            <div class="high_title fr">
                <h5 class="title_header"><?php echo Yii::t('principal', 'Student Achievement Report') ?></h5>
                <h5 class="title_header"><?php echo $report->getTitle() ?></h5>
                <?php
                foreach ($reportchild as $chidNameclass) {
                    if ($chidNameclass->id == $params['courseid']) {
                        echo "<p class='text-right' style='text-indent: 16px'>" . Yii::t('principal', 'Student Name: ') . "<span>" . $chidNameclass->childName->getChildName() . "</span></p>";
                        echo "<p class='text-indent' style='text-indent: 16px'>" . Yii::t('principal', 'Grade Level: ') . "<span class='text_blue'>" . $chidNameclass->childClass->title . "</span></p>";
                    }
                }
                ?>
                <p style="text-indent: 16px"
                   class="line_height18"><?php echo Yii::t('principal', 'Reporting Period: ') ?><span
                        class="text_blue"><?php echo $report->cycle ?>
                </span></p>

                <p style="text-indent: 16px" class="line_height18"><?php echo Yii::t('principal', 'Reporting Date: ') ?>
                    <span
                        class="text_blue"><?php echo date("Y-m-d", $report->end_time) ?></span></p>
            </div>
            <div class="clear"></div>
            <div class="dshr" style="margin-bottom: 28px"></div>
            <h3 class="text-center"
                style="line-height: 34px"><?php echo Yii::t('principal', 'The Mission of Daystar Academy') ?></h3>

            <p class="text-center"
               style="font-style: italic"><?php echo Yii::t('principal', 'Daystar Academy develops world citizens by embracing Chinese and Western culture through its integrated education model. Daystar students strive for distinction in Chinese and English studies, creative thinking and character development for the purpose of serving the community at large.') ?>
                <br/>
                <br/>
            </p>

            <h3 class="text-center"
                style="line-height: 34px"><?php echo Yii::t('principal', 'A Message from the Principals') ?></h3>

            <p class="text-left" style="font-style: italic"><?php echo nl2br($report->getPresident()); ?><br/>
            </p>

            <div class="clear"></div>

            <div class="bottomBox">
                <div class="left_title"><?php echo Yii::t('principal', 'Attendance Record') ?></div>
                <table class="text-center">
                    <tr class="bg_blue text_white">
                        <?php
                        $num = count($report_list);
                        foreach ($report_list as $k => $_report_list) {

                            echo "<th colspan='2' class='p6' width='111'>".Yii::t('principal', 'Reporting Period :num', array(':num' =>$k+1))."</th>";
                        }
                        for ($num; $num < 4; $num++) {
                            echo "<th colspan='2' class='p6' width='111'>".Yii::t('principal', 'Reporting Period :num', array(':num' =>$num + 1))."</th>";
                        } ?>
                        <th colspan="2" class="bg_gold p6"
                            width="111"><?php echo Yii::t('principal', 'Year-to-Date') ?></th>
                    </tr>
                    <tr class="bg_gray">
                        <?php
                        foreach ($report_list as $k => $_report_list) {
                            echo "<td width='100' style='padding: 6px 0' >" . Yii::t('principal', 'Days in the Reporting Period') . "</td>";
                            echo "<td width='100' style='padding: 6px 0' >" . Yii::t('principal', 'Days Absent') . "</td>";
                        }
                        $num = count($report_list);
                        for ($num; $num < 4; $num++) {
                            echo "<td width='100' style='padding: 6px 0' >" . Yii::t('principal', 'Days in the Reporting Period') . "</td>";
                            echo "<td width='100' style='padding: 6px 0' >" . Yii::t('principal', 'Days Absent') . "</td>";
                        } ?>

                        <td class="bg_yellow" style="padding: 6px 0"
                            width="100"><?php echo Yii::t('principal', 'Days in the Reporting Period') ?></td>
                        <td class="bg_yellow" style="padding: 6px 0"  width="100"><?php echo Yii::t('principal', 'Days Absent') ?></td>

                    </tr>

                    <tr style="height: 24px">
                        <?php
                        $num = 0;
                        $days = "";
                        foreach ($report_list as $k => $_report_list) {
                            foreach ($reportchild as $_reportchild) {
                                if ($_report_list->id == $_reportchild->report_id) {
                                    echo "<td>" . $_reportchild->evaluate_number . "</td>";
                                    echo "<td>" . $_reportchild->absent_days . "</td>";
                                    $number += $_reportchild->evaluate_number;
                                    $days += $_reportchild->absent_days;
                                    $num++;
                                }
                            }
                        }

                        //$num = count($report_list);
                        for ($num; $num < 4; $num++) {
                            echo "<td>" . "--" . "</td>";
                            echo "<td>" . "--" . "</td>";
                        }
                        ?>
                        <td><?php echo $number ?></td>
                        <td><?php echo $days ?></td>
                    </tr>
                </table>
                <div class="dshr"></div>
                <div style="line-height: 26px"><span
                        class="text_blue fl"><?php echo $teacherName['ms_principal'] ?></span><span
                        class="text_blue fr"><?php echo $teacherName['msv_principal'] ?></span></div>
                <div class="clear"></div>
                <div style="line-height: 26px"><span
                        class=" fl"><?php echo Yii::t('principal', 'MS Principal') ?></span><span
                        class=" fr"><?php echo Yii::t('principal', 'Head of School') ?></span></div>
                <div class="clear"></div>
                <br/>
<!--                -->
                <br/>
                <?php if(isset($teacherName['head_school']) && isset($teacherName['myp_coordinator'])){ ?>
                    <div style="line-height: 26px"><span
                            class="text_blue fl"><?php echo $teacherName['head_school'] ?></span><span
                            class="text_blue fr"><?php echo $teacherName['myp_coordinator'] ?></span>
                    </div>
                    <div class="clear"></div>
                    <div style="line-height: 26px"><span
                            class=" fl"><?php echo Yii::t('principal', 'MS Vice Principal') ?></span><span
                            class=" fr"><?php echo Yii::t('principal', 'MYP Coordinator') ?></span></div>
                    <div class="clear"></div>
                <?php } ?>
<!--                -->
            </div>
        </div>
    </div>


    <!--page2-->

    <div id="page-2" class="report-page page-break-after" style="margin-bottom: 20px">
        <div class="pull-content">
            <div class="left_title"><?php echo Yii::t('principal', 'Quantitative Achievement Summary') ?></div>
            <table class="text-center">
                <tr class="bg_blue text_white" style="height: 38px">
                    <th rowspan="2" class="paddinglr6" width="100"><?php echo Yii::t('principal', 'Course Title') ?>
                        <br/><?php echo Yii::t('principal', 'Teacher Name') ?></th>
                    <th rowspan="2" class="paddinglr6" width="40"><?php echo Yii::t('principal', 'Criteria') ?></th>
                    <th colspan="4" class="paddinglr6"><?php echo Yii::t('principal', 'Reporting Period') ?></th>
                    <th rowspan="2" class="paddinglr6 bg_gold"
                        width="40"><?php echo Yii::t('principal', 'Final Criteria Score') ?></th>
                    <th rowspan="2" class="paddinglr6 bg_gold"
                        width="40"><?php echo Yii::t('principal', 'Year-End Final Grade') ?></th>
                </tr>
                <tr class="bg_gray">
                    <?php
                    foreach ($report_list as $k => $_report_list) {
                        echo "<td width='80'>" . ($k + 1) . "</td>";
                    }
                    $num = count($report_list);
                    for ($num; $num < 4; $num++) {
                        echo "<td width='80'>" . ($num + 1) . "</td>";
                    }
                    ?>
                </tr>
                <?php
                foreach ($childcourse as $_childcourse) {
                    $frecyion_list = json_decode($_childcourse->childTotal()->frecyion_total, TRUE);
                    ?>
                    <?php
                    foreach ($_childcourse->reportCourse->courseScores as $k => $val) {
                        $standards['x' . $val->id] = nl2br($val->getName());
                        $array = array();
                        foreach ($val->items as $_item) {
                            $array[$_item->id] = $_item->fraction;
                        }
                        ?>
                        <tr>
                            <?php if ($k == 0) { ?>
                                <td rowspan="<?php echo count($_childcourse->reportCourse->courseScores) ?>">
                                    <?php
                                    echo $_childcourse->reportCourse->getName();
                                    echo "<br>";
                                    echo $_childcourse->teacherName->getName();
                                    ?>
                                </td>
                            <?php } ?>
                            <td>
                                <a role="button" tabindex="0" data-trigger="focus" data-placement="top" data-html='true'
                                   class="pointer description x<?php echo $val->id; ?>"><?php echo $val->title_cn; ?></a>
                            </td>
                            <?php
                            foreach ($report_list as $_reportlist) {
                                $status = ($array[$options[$_childcourse->courseid][$val->id][$_reportlist->id]] != "") ? $array[$options[$_childcourse->courseid][$val->id][$_reportlist->id]] : "-";

                                echo "<td class='bg_gray'><a role='button' tabindex='0' class='pointer description x" . $options[$_childcourse->courseid][$val->id][$_reportlist->id] . "' data-trigger='focus' data-placement='top' data-html='true'>" . $status . "</a></td>";
                            }
                            $num = count($report_list);
                            for ($num; $num < 4; $num++) {
                                echo "<td class='bg_gray'>--</td>";
                            }
                            $numb = ($array[$frecyion_list[$val->id]]) ? $array[$frecyion_list[$val->id]] : "-";
                            echo "<td class='bg_gray'><div  role='button' tabindex='0' class='pointer description x" . $frecyion_list[$val->id] . "' data-trigger='focus' data-placement='top' data-html='true'>" . $numb . "</div></td>";
                            if ($k < 1) {
                                $sta = array("1" => 0, 1, 2, 3, 4, 5, 6, 7);
                                $fen = ($sta[$_childcourse->childTotal->oprion_value]) ? $sta[$_childcourse->childTotal->oprion_value] : "-";
                                echo "<td rowspan='" . count($_childcourse->reportCourse->courseScores) . "' class='paddinglr6 bg_yellow'>" . $fen . "</td>";
                            }
                            ?>

                        </tr>
                    <?php }
                } ?>
            </table>
        </div>
    </div>

    <!--page3-->
    <?php
    if ($comments):
        $comments = array_chunk($comments, 3);
        foreach ($comments as $_comments):
            ?>
            <div id="page-3" class="report-page page-break-after">
                <div class="pull-content">
                    <div class="left_title"><?php echo Yii::t('principal', 'Qualitative Achievement Summary') ?></div>
                    <table class="">
                        <tr class="bg_blue text_white text-center" style="height: 38px">
                            <th class="paddinglr6" width="100"><?php echo Yii::t('principal', 'Course Name') ?>
                                <br/><?php echo Yii::t('principal', 'Teacher Name') ?></th>
                            <th class="paddinglr6"
                                width="228"><?php echo Yii::t('principal', 'Teacher Comments') ?></th>
                            <th class="paddinglr6 bg_gold"
                                width="229"><?php echo Yii::t('principal', 'Student Comments') ?></th>
                        </tr>
                        <?php
                        $m = 0;
                        foreach ($_comments as $_comment) {
                            //i::msg(array_chunk($childcourse,2));
                            ?>
                            <tr>
                                <td class="text-center" style="padding: 10px">
                                    <?php
                                    echo $_comment['course'];
                                    echo "<br>";
                                    echo $_comment['teacherName'];
                                    ?>
                                </td>
                                <td class="bg_gray text-left" style="padding: 10px">
                                    <?php echo $_comment['TeacherComments']; ?>
                                </td>
                                <td class="bg_yellow text-left" style="padding: 10px">
                                    <?php echo $_comment['StudentComments']; ?>
                                </td>
                            </tr>
                        <?php } ?>
                    </table>
                </div>
            </div>
        <?php
        endforeach;
    endif;
    ?>


    <!--page4-->
    <div id="page-4" class="report-page" style="margin-top: 20px">
        <div class="pull-content">
            <div
                class="left_title"><?php echo Yii::t('principal', 'MYP Approaches to Learning Student Progress Summary') ?></div>
            <table class="text-center">
                <tr class="bg_blue text_white" style="height: 38px">
                    <th rowspan="2" class="paddinglr6" width="100"><?php echo Yii::t('principal', 'Course Name') ?></th>
                    <th colspan="5"
                        class="paddinglr6 bg_gold"><?php echo Yii::t('principal', 'Approaches to Learning Categories') ?></th>
                </tr>
                <tr>
                    <?php foreach ($MYP as $k => $_myp) {
                        echo "<td class='bg_yellow' width='91'>" . $_myp . "</td>";
                    } ?>
                </tr>

                <?php
                $MYP_value = array('E', 'P', 'L', 'N');

                foreach ($childcourse as $_childcourse) {
                    $myp_list = json_decode($_childcourse->myp);
                    ?>
                    <tr>
                        <td class='paddinglr6'><?php echo $_childcourse->reportCourse->getName(); ?></td>
                        <?php foreach ($MYP as $k => $_myp) {
                            $re = ($MYP_value[$myp_list[$k]]) ? $MYP_value[$myp_list[$k]] : "-";
                            echo "<td>" . $re . "</td>";
                        } ?>
                    </tr>
                <?php } ?>

            </table>
            <br/>
            <br/>
            <br/>
            <br/>

            <div class="left_title"><?php echo Yii::t('principal', 'Description of Symbols & Abbreviations') ?></div>
            <table class="text-center">
                <tr class="bg_blue text_white" style="height: 38px">
                    <th width="100"><?php echo Yii::t('principal', 'Abbreviation') ?></th>
                    <th class="paddinglr6 bg_gold"><?php echo Yii::t('principal', 'Description') ?></th>
                </tr>
                <tr>
                    <td>--</td>
                    <td class="paddinglr6 text-left"><?php echo Yii::t('principal', 'Not applicable for current reporting period') ?></td>
                </tr>
                <tr>
                    <td>E</td>
                    <td class="paddinglr6 text-left"><?php echo Yii::t('principal', 'Expert - Students can show others how to use the skill and accurately assess how effectively the skill is used (self-regulation)') ?></td>
                </tr>
                <tr>
                    <td>P</td>
                    <td class="paddinglr6 text-left"><?php echo Yii::t('principal', 'Practitioner - Students employ the skill confidently and effectively (demonstration)') ?></td>
                </tr>
                <tr>
                    <td>L</td>
                    <td class="paddinglr6 text-left"><?php echo Yii::t('principal', 'Learner - Students copy other who use the skill and use the skill with scaffolding and guidance (emulation)') ?></td>
                </tr>
                <tr>
                    <td>N</td>
                    <td class="paddinglr6 text-left"><?php echo Yii::t('principal', 'Novice - Students are introduced to the skill, and can watch others performing it (observation)') ?></td>
                </tr>
            </table>


            <br/>
            <br/>
            <br/>
            <br/>
            <br/>

            <div class="left_title"><?php echo Yii::t('principal', 'Homeroom Teacher Comments') ?></div>
            <div>
                <?php
                foreach ($reportchild as $v) {
                    if ($v->id == $params['courseid']) {
                        echo nl2br($v->counselor_message_en);
                    }
                }
                ?>
            </div>
            <br/>
            <br/>
            <br/>
            <br/>
        </div>
    </div>
</div>
<style>
    .web-view * {
        margin: 0;
        padding: 0
    }

    .popover {
        padding: 10px;
    }

    body {
        font-size: 10px;
        line-height: 1.42857143;
        color: #333333;
        margin: 0;
        padding: 0;
        background-color: #efefef;
    }

    .web-view a {
        outline: none;
    }

    table {
        width: 100%;
        border-collapse: collapse;
    }

    .web-view table {
        border: 1px solid #999;
        margin-bottom: 1.4em;
    }

    .web-view table th {
        text-align: center;
        border: 1px solid #999;
    }

    .web-view table td {
        text-align: center;
        border: 1px solid #999;
    }

    .web-view * {
        box-sizing: border-box;
        -moz-box-sizing: border-box;
        -webkit-box-sizing: border-box;
    }

    .page-break-after {
        page-break-after: always;
        margin-bottom: 13px;
    }

    .web-view {
        font-size: 10px;
    }

    .web-view p {
        margin: 0;
        line-height: 18px;
    }

    .navbar-fixed-top {
        top: 0;
        position: fixed;
        right: 0;
        left: 0;
        z-index: 1030;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }

    .navbar-inverse {
        background-color: #333333;
        border-color: #efefef;
    }

    .navbar-inverse form {
        line-height: 50px;
        height: 50px;
        text-align: center !important;
    }

    .navbar-inverse form input {
        margin-top: 13px;
    }

    .paddinglr6 {
        padding: 0 6px;
    }

    .p6 {
        padding: 6px;
    }

    .text-right {
        text-align: right !important;
    }

    .text-center {
        text-align: center !important;
    }

    .text-left {
        text-align: left !important;
    }

    .clear {
        clear: both;
    }

    .fl {
        float: left;
    }

    .fr {
        float: right;
    }

    .report-page {
        min-height: 877px;
        position: relative;
        background-color: #fff;
    }

    .dshr {
        width: 100%;
        border-bottom: 1px solid #92a2af;
    }

    .high_title {
        width: 440px;
        border-top: 2px solid #4f91ce;
    }

    .line_height18 {
        line-height: 18px !important;
    }

    .text_blue {
        color: #71aeca
    }

    .text_white {
        color: white;
    }

    .report-wrapper {
        width: 620px;
        margin: 0 auto;
        padding: 0;
    }

    .pull-content {
        padding: 40px 30px 0;
    }

    .web-view .title_header {
        color: #626e60;
        line-height: 24px;
        margin: 0;
        text-align: right
    }

    .left_title {
        line-height: 35px;
        font-weight: bold;
    }

    .bg_blue {
        background: #2374b3;
    }

    .bg_gray {
        background: #dfe9f5;
    }

    .bg_yellow {
        background: #fde69a;
    }

    .bg_gold {
        background: #fcc10f;
    }

    .logo-top-center {
        width: 110px;
        height: 120px;
        background: url('http://ivyschools-www-uploads.oss-cn-beijing.aliyuncs.com/files/daystar_logo_v3.png') no-repeat;
        margin: 11px 0 0;
        background-size: 100%;
    }
    .bottomBox{

        padding: 0 30px 40px;
        position: absolute;
        left: 0;
        bottom: 0;
    }
</style>
</body>
<script>
    var standards = <?php echo json_encode($standards); ?>;// ABCD
    var introduction = <?php echo json_encode($introduction); ?>;// 选项

    for (val in standards) {
        $('.' + val).attr('data-content', function () {
            return standards[val];
        })
    }
    for (val in introduction) {
        $('.' + val).attr('data-content', function () {
            return introduction[val];
        })
    }
    $('.description').popover();


</script>
</html>
