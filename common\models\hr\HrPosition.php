<?php

/**
 * This is the model class for table "ivy_hr_position".
 *
 * The followings are the available columns in table 'ivy_hr_position':
 * @property integer $id
 * @property string $cn_name
 * @property string $en_name
 * @property string $duty
 * @property integer $type
 * @property integer $parent_id
 * @property integer $weight
 * @property integer $status
 * @property integer $deleted_at
 */
class HrPosition extends CActiveRecord
{
    const IVY_TEACHER = '57,58,59,60,61,94';//定义校园老师范围（57:中文班长,58:中文班长,59:中文助教,60:英文助教,61:保育员,91:机动老师）
    const IVY_CAMPUS_ADMIN = '147,54,55,56,80';//定义校园行政范围（147:行政园长,54:园长,55:校园行政经理,56:校园行政助理,80:校园医生）
    public $_role = array();
    /**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return HrPosition the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_hr_position';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('cn_name, en_name, _role', 'required'),
			array('type, parent_id, weight, status', 'numerical', 'integerOnly'=>true),
			array('cn_name, en_name', 'length', 'max'=>255),
			array('duty', 'length', 'max'=>500),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, cn_name, en_name, duty, type, parent_id, weight, status', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'link' => array(self::HAS_ONE, 'DepPosLink', 'position_id'),
            'role' => array(self::HAS_MANY, 'HrTitleRole', 'title_id'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'cn_name' => 'Cn Name',
			'en_name' => 'En Name',
			'duty' => 'Duty',
			'type' => 'Type',
			'parent_id' => 'Parent',
			'weight' => 'Weight',
			'status' => 'Status',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('cn_name',$this->cn_name,true);
		$criteria->compare('en_name',$this->en_name,true);
		$criteria->compare('duty',$this->duty,true);
		$criteria->compare('type',$this->type);
		$criteria->compare('parent_id',$this->parent_id);
		$criteria->compare('weight',$this->weight);
		$criteria->compare('status',$this->status);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function getName()
	{
		return (Yii::app()->language == "zh_cn") ?  $this->cn_name : $this->en_name;
	}

	public function delete()
    {
        $this->deleted_at = time();
        if ($this->save(false)) {
            return true;
        }
        else {
            return false;
        }
    }

    public function findAll($criteria)
    {
        if ($criteria) {
            $criteria->compare('deleted_at', 0);
            return parent::findAll($criteria);
        }
        else {
            return parent::findAll();
        }

    }
}