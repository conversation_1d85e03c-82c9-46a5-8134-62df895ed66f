<?php

class ArticleController extends ProtectedController
{
    var $serverID = 1; //图片存储服务器编号

    public function init()
    {
        if(!Yii::app()->user->checkAccess("oSuperAdmin")){
            throw new CException('Permission Denied');
        }
        parent::init();
    }

	/**
	 * Creates a new model.
	 * If creation is successful, the browser will be redirected to the 'view' page.
	 */
	public function actionCreate()
	{
        $id = Yii::app()->request->getParam('id',0);
		$model= $this->loadModel($id);
		// Uncomment the following line if AJAX validation is needed
		// $this->performAjaxValidation($model);

		if(isset($_POST['ClubArticle']))
		{
			$model->attributes=$_POST['ClubArticle'];
            $model->op_uid = Yii::app()->user->getId();
            $model->updated = time();
            if ($model->isNewRecord){
                $model->created = time();
            }
			if($model->save()){
                if ($_POST['ClubArticleContent']['content']){
                    $model->content->content = $_POST['ClubArticleContent']['content'];
                    if ($model->content->save()){
                        //调用生成缓存接口
                        if ($model->publish){
                            $url = Yii::app()->request->getBaseUrl(true).'/clubApi/processArticle';
                            $params = array(
                                'flagid'=> $model->id,
                                'postTime'=>  time(),
                                'postKey'=>md5(sprintf("%s&%s&%s", time(), $model->id, OA::SECURITY_KEY)),
                            );
                            $options = array(
                                'http'=>array(
                                    'method' => 'POST',
                                    'content' => http_build_query($params,'','&'),
                                )
                            );
                            $context = stream_context_create($options);
                            $result = file_get_contents($url,FALSE,$context);
                        }
                        Yii::app()->user->setFlash('success',Yii::t('club', '数据保存成功！'));
                    }
                }else{
                    $articleContent = new ClubArticleContent();
                    $articleContent->id = $model->id;
                    if ($articleContent->save()){
                        $this->redirect($this->createUrl('/club/article/create',array('id'=>$model->id)));
                    }
                }
            }
		}
        if (!empty($model)){
            $model->author_email = $model->user->email;
        }
		$this->render('create',array(
			'model'=>$model,
		));
	}

	/**
	 * Deletes a particular model.
	 * If deletion is successful, the browser will be redirected to the 'index' page.
	 * @param integer $id the ID of the model to be deleted
	 */
	public function actionDelete($id)
	{
		if(Yii::app()->request->isPostRequest)
		{
			// we only allow deletion via POST request
            $model = $this->loadModel($id);
            $model->delete();
            $model->content->delete();
			// if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
			if(!isset($_GET['ajax']))
				$this->redirect(isset($_POST['returnUrl']) ? $_POST['returnUrl'] : array('admin'));
		}
		else
			throw new CHttpException(400,'Invalid request. Please do not repeat this request again.');
	}

	/**
	 * Lists all models.
	 */
	public function actionIndex()
	{
		$model=new ClubArticle('search');
		$model->unsetAttributes();  // clear any default values
		if(isset($_GET['ClubArticle']))
			$model->attributes=$_GET['ClubArticle'];

		$this->render('index',array(
			'model'=>$model,
		));
	}
    
    /*
     * 批量生成缓存
     */
    public function actionGenerateCache(){
        $ret = false;
        $id = Yii::app()->request->getParam('article_id', null);
        if (!empty($id)){
            $url = Yii::app()->request->getBaseUrl(true) . '/clubApi/processArticle';
            foreach ($id as $val){
                $model = ClubArticle::model()->findByPk($val);
                $params = array(
                    'flagid' => $model->id,
                    'postTime' => time(),
                    'postKey' => md5(sprintf("%s&%s&%s", time(), $model->id, OA::SECURITY_KEY)),
                );
                $options = array(
                    'http' => array(
                        'method' => 'POST',
                        'content' => http_build_query($params, '', '&'),
                    )
                );
                $context = stream_context_create($options);
                $result = file_get_contents($url, FALSE, $context);
                $ret = true;
            }
        }
        echo CJSON::encode($ret);
    }

    	/**
	 * Returns the data model based on the primary key given in the GET variable.
	 * If the data model is not found, an HTTP exception will be raised.
	 * @param integer the ID of the model to be loaded
	 */
	public function loadModel($id)
	{
		$model=ClubArticle::model()->with('content','user')->findByPk((int)$id);
		if($model===null){
            $model = new ClubArticle();
            $model->content = new ClubArticleContent();
        }
		return $model;
	}
    

	/**
	 * Performs the AJAX validation.
	 * @param CModel the model to be validated
	 */
	protected function performAjaxValidation($model)
	{
		if(isset($_POST['ajax']) && $_POST['ajax']==='club-article-form')
		{
			echo CActiveForm::validate($model);
			Yii::app()->end();
		}
	}
    
    /*
     * 查询作者（家长）在系统中的信息
     * @param string email
     * @return json 
     */
    public function actionSearchUser(){
        $email = Yii::app()->request->getPost('email',null);
        if (!empty($email)){
            $model = User::model()->find('email=:email and isstaff=:isstaff',array(':email'=>$email,':isstaff'=>0));
            if (!empty($model)){
                $this->addMessage('state', 'success');
                $this->addMessage('data', array('uid'=>$model->uid,'name'=>$model->getName())); 
            }else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '无效邮箱地址！');
            }
        }else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '邮箱地址不能为空！');
        }
        $this->showMessage();
    }

    public function actionUpimage($id=0, $type='')
    {
        $upfile = CUploadedFile::getInstanceByName('upfile');
        $upResult = OA::processPicUpload($upfile, 'articlePhoto');
        if($upResult){
            $model = new ClubUploads;
            $model->article_id = (int)$id;
            $model->title = $upfile->getName();
            $model->mime_type = $upfile->getType();
            $model->filename = $upResult['filename'];
            $model->server_id = $this->serverID;
            $model->sizes = $upResult['width'].','.$upResult['height'];
            $model->subfolder = date('Ym');
            if($model->save()){
                $editorId=$_GET['editorid'];
                $url = Yii::app()->params['OAUploadBaseUrl']."/article/".$model->subfolder.'/thumbs/'.$model->filename;
                if($type == 'ajax'){
                    echo $url;
                }
                else{
                    echo "<script>parent.UM.getEditor('". $editorId ."').getWidgetCallback('image')('" . $url . "','SUCCESS')</script>";
                }
            }
            else{
                print_r($model->getErrors());
            }
        }
    }

    public function actionImageMgt($id=0)
    {
        $ret = array();
        if($id){
            $action = Yii::app()->request->getParam('action', 'get');
            if ($action == 'get'){
                $model = ClubArticle::model()->findByPk($id);
                $crit = new CDbCriteria();
                $crit->compare('article_id', $id);
                $crit->order = 'title ASC';
                $items = ClubUploads::model()->findAll($crit);
                foreach($items as $item){
                    $ret[] = array(
                        'id' => $item->id,
                        'url' => Yii::app()->params['OAUploadBaseUrl']."/article/".$item->subfolder.'/thumbs/'.$item->filename,
                        'title' => $item->title,
                        'size' => $item->sizes,
                        'cover' => $model->cover == $item->id ? 1 : 0,
                    );
                }
            }
            elseif($action == 'del'){
                $_id = Yii::app()->request->getParam('_id', 0);
                $model = ClubUploads::model()->findByPk($_id);
                if($model->delete()){
                    @unlink(Yii::app()->params['OAUploadBasePath'].'/article/'.$model->subfolder.'/'.$model->filename);
                    @unlink(Yii::app()->params['OAUploadBasePath'].'/article/'.$model->subfolder.'/thumbs/'.$model->filename);
                    $ret['state']='SUCCESS';
                }
            }
            elseif($action == 'update'){
                $_id = Yii::app()->request->getParam('_id', 0);
                $val = Yii::app()->request->getParam('val', '');

                $model = ClubUploads::model()->findByPk($_id);
                $model->title = $val;
                if ($model->save()){
                    echo $model->title;die;
                }
                else{
                    print_r($model->getErrors());die;
                }
            }
            elseif($action == 'cover'){
                $_id = Yii::app()->request->getParam('_id', 0);
                ClubArticle::model()->updateByPk($id, array('cover'=>$_id, 'updated'=>time()));
                $ret['state']='SUCCESS';
            }
        }
        echo CJSON::encode($ret);
    }
}
