<div>
    <div class='formData'>
        <form class="J_ajaxForm formStu" action="<?php echo $this->createUrl("saveNode");?>" method="post" onsubmit="return check(this)">
            <div class="panel panel-default">
                <div class="panel-heading"><?php echo Yii::t('withdrawal','Management Interview(s)');?></div>
                <div class="panel-body">
                    <div class='ml8'>
                        <div class=' form-horizontal font14'>
                            <div class='flex flexStart mb20'>
                                <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Interviewer');?></div>
                                <div class='flex1 color3 relative'>
                                    <span class='talkUser'></span>
                                    <label class="checkbox-inline ml24 pt0 lableHeight">
                                        <input type="checkbox" class='notTalk' name="no_talk"  value="1"> <?php echo Yii::t('withdrawal','Skip Interview');?>
                                    </label>
                                </div>
                            </div>
                            <div class='notTalkData'>
                                <div class='flex mb20 flexStart'>
                                    <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Withdrawal Date');?></div>
                                    <div class='flex1'>
                                        <input type="text" class="form-control length_3 pull-left mr20" name='withdrawal_date'   id="dropout_date"   placeholder="<?php echo Yii::t("newDS", "Select a date");?>"  >
                                    </div>
                                </div>
                                <div class='flex mb20 flexStart'>
                                    <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Where are you transferring to?');?></div>
                                    <div class='flex1'>
                                        <div class='mb12 relative' id='where'>
                                        </div>
                                        <div>
                                            <input type="text" name='withdrawal_where_memo' class="form-control" placeholder="<?php echo Yii::t('withdrawal','Comment');?>">
                                        </div>
                                    </div>
                                    
                                </div>
                                <div class='flex mb20 flexStart'>
                                    <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Reason for withdrawal');?></div>
                                    <div class='flex1'>
                                        <div class='mb12 relative' id='withdrawal_reason'></div>
                                        <input type="text" class="form-control"  name='withdrawal_reason_memo' placeholder="<?php echo Yii::t('withdrawal','Comment');?>">
                                    </div>
                                </div>
                                <div class='flex mb20 flexStart'>
                                    <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Are there any aspects of school you are unsatisfied with?');?></div>
                                    <div class='flex1'>
                                        <textarea class="form-control" name='withdrawal_unsatisfied' rows="3"></textarea>
                                    </div>
                                </div>
                                <div class='flex mb20 flexStart'>
                                    <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Do you have any advice for us?');?></div>
                                    <div class='flex1'>
                                        <textarea class="form-control" name='withdrawal_recommendations' rows="3"></textarea>
                                    </div>
                                </div>
                                <div class='flex mb20 flexStart'>
                                    <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Comment');?></div>
                                    <div class='flex1'>
                                        <textarea class="form-control" name='memo' rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class='modal-footer borderTopNone p0'>
                <button type="submit" class="btn btn-primary ml24 submitSend"><?php echo Yii::t('workflow','Next');?></button>
                <button type="button" class="btn btn-primary J_ajax_submit_btn ml24"><?php echo Yii::t('workflow','Next');?></button>
                <input type="hidden" name="type" id="type"  value='talk01'>
                <input type="hidden" name="applicationId" id="applicationId">
                <input type="hidden" name="callback" value="talkStep">
                <input type="hidden" name="node" id="node">
            </div>
        </form>
    </div>
    <div class='hide result'>
        <form class="J_ajaxForm formSub" action="<?php echo $this->createUrl("saveNode");?>" method="post"  method="post" onsubmit="return submitCheck(this)">
            <div class="panel panel-default">
                <div class="panel-heading"><?php echo Yii::t('withdrawal','Management Interview(s)');?></div>
                <div class="panel-body">
                    <div class='ml8'>
                        <div class=' form-horizontal font14'>
                            <div class='flex flexStart mb20'>
                                <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Interviewer');?></div>
                                <div class='flex1 color3'>
                                <span class='talkUser'></span>
                                </div>
                            </div>
                            <div class='flex mb20 flexStart'>
                                <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Interview result');?></div>
                                <div class='flex1'>
                                    <div class='mb12'>
                                        <div class="radio mb10 talkResultFlex">
                                            <label class='lableHeight'>
                                                <input type="radio" name="talk_result" class='talkResult ' value="1">
                                                <span><?php echo Yii::t('withdrawal','Schedule school board interview');?></span> 
                                            </label>
                                            <span id='talkList' class='hide'></span>
                                        </div>
                                        <div class="radio mb10">
                                            <label class='lableHeight'>
                                                <input type="radio" name="talk_result" class='talkResult' value="2">
                                                <?php echo Yii::t('withdrawal','End withdrawal, continue study');?>
                                            </label>
                                        </div>
                                        <div class="radio mb10">
                                            <label class='lableHeight'>
                                                <input type="radio" name="talk_result" class='talkResult' value="3">
                                                <?php echo Yii::t('withdrawal','Continue withdrawal');?>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class='modal-footer borderTopNone p0'>
                <input type="hidden" name="talk_other" >
                <input type="hidden" name="type" id="type"  value='talk01'>
                <input type="hidden" name="applicationId" id="applicationId">
                <input type="hidden" name="node" id="node">
                <label class="checkbox-inline">
                    <input type="checkbox" name="status" value="1"> <?php echo Yii::t('withdrawal', 'Mark this step as completed'); ?>
                </label>
                <button type="submit" class="btn btn-primary ml24 submitCheck"><?php echo Yii::t('global','Submit');?></button>
                <button type="button" class="btn btn-primary J_ajax_submit_btn ml24"><?php echo Yii::t('global','Submit');?></button>
            </div>
        </form>
    </div>
</div>
<script>
    // 第一步
    initDate()
    function initDate() {
        const otherTalk =  childDetails.forms.filter(item => item.key === 'talk03');
        if(otherTalk.length!=0){
            $('.talkResultFlex').hide()
        }
        $('input[name="applicationId"]').val(container.applicationId)
        $('input[name="node"]').val(container.stepName)
        let indexData=container.indexDataList
        for(let key in indexData.whereList){
            let label='<label class="radio-inline lableHeight">'+
                '<input type="radio" name="withdrawal_where" value="'+key+'"> '+indexData.whereList[key]+''+
            '</label>'
            $('#where').append(label)
        }
        for(let key in indexData.reasonList){
            let option='<label class="radio-inline lableHeight mr10">'+
                '<input type="radio" name="withdrawal_reason" value="'+key+'"> '+indexData.reasonList[key]+''+
            '</label>'
            $('#withdrawal_reason').append(option)
        }
        $('#dropout_date').datepicker({
            dateFormat: 'yy-mm-dd',
            selectOtherMonths: false,
            showOtherMonths: false,
            changeMonth: true,
            changeYear: true,
            firstDay: 0,
            onSelect: function (date,str) {
                $('#dropout_date').val(date)
            }
        });
        $('.submitSend').show()
        $('.J_ajax_submit_btn').hide()
        let dataKey=childDetails.forms.find(item2 =>item2.key === 'talk01')
        let data=dataKey.data
        $('.talkUser').text(childDetails.info.appInfo.staffInfo[childDetails.info.appInfo.currentStaff].name)
        if(data.withdrawal_date){
            if(data.no_talk=='1'){
                $('input[name="no_talk"]').prop('checked', true);
                $('.notTalkData').hide()
            }
            $('input[type="radio"][name="withdrawal_reason"][value='+data.withdrawal_reason+']').prop('checked', true);
            $('input[type="radio"][name="withdrawal_where"][value='+data.withdrawal_where+']').prop('checked', true);
            $('input[name="withdrawal_date"]').val(data.withdrawal_date)
            $('input[name="withdrawal_where_memo"]').val(data.withdrawal_where_memo)
            $('input[name="withdrawal_reason_memo"]').val(data.withdrawal_reason_memo)
            $('textarea[name="withdrawal_unsatisfied"]').val(data.withdrawal_unsatisfied)
            $('textarea[name="withdrawal_recommendations"]').val(data.withdrawal_recommendations)
            $('textarea[name="memo"]').val(data.memo)
        }else{
            let applyUser=childDetails.info.appInfo.applyUser
            $('input[type="radio"][name="withdrawal_reason"][value='+applyUser.withdrawal_reason+']').prop('checked', true);
            $('input[type="radio"][name="withdrawal_where"][value='+applyUser.withdrawal_where+']').prop('checked', true);
            $('input[name="withdrawal_date"]').val(applyUser.withdrawal_date)
            $('input[name="withdrawal_where_memo"]').val(applyUser.withdrawal_where_memo)
            $('input[name="withdrawal_reason_memo"]').val(applyUser.withdrawal_reason_memo)
            $('textarea[name="withdrawal_unsatisfied"]').val(applyUser.withdrawal_unsatisfied)
            $('textarea[name="withdrawal_recommendations"]').val(applyUser.withdrawal_recommendations)
            $('textarea[name="memo"]').val(applyUser.memo)
        }
    }
    $('.notTalk').on('click', function() {
        if ($(this).prop('checked')) {
            $('.notTalkData').hide()
        } else {
            $('.notTalkData').show()
        }
    });
    
    function check(_this){
        let no_talk=$('input[name="no_talk"]:checked').val()
        $('#J_fail_info').remove();
        var button = $(_this).find('button[type="submit"]');
        var flag = true, msg=[];
        if(no_talk=='1'){
            $('input[type="radio"][name="withdrawal_where"]').val('');
            $('input[type="radio"][name="withdrawal_reason"]').val('');
            $('input[name="withdrawal_date"]').val('');
            $('input[name="withdrawal_where_memo"]').val('');
            $('input[name="withdrawal_reason_memo"]').val('');
            $('textarea[name="withdrawal_unsatisfied"]').val('');
            $('textarea[name="withdrawal_recommendations"]').val('');
        }else{
            if($('input[name="withdrawal_date"]').val()==''){
                flag = false;
                msg.push('<?php echo Yii::t('newDS','Please select');?> <?php echo Yii::t("withdrawal","Withdrawal Date");?>');
            }
            if($('input[name="withdrawal_where"]:checked').length == 0){
                flag = false;
                msg.push('<?php echo Yii::t("teaching","Where are you transferring to?");?>');
            }
            if($('input[name="withdrawal_reason"]:checked').length == 0){
                flag = false;
                msg.push('<?php echo Yii::t('newDS','Please select');?> <?php echo Yii::t("withdrawal","Reason for withdrawal");?>');
            }
            if($('textarea[name="withdrawal_unsatisfied"]').val()==''){
                flag = false;
                msg.push('<?php echo Yii::t('withdrawal','Are there any aspects of school you are unsatisfied with?');?>');
            }
            if($('textarea[name="withdrawal_recommendations"]').val()==''){
                flag = false;
                msg.push('<?php echo Yii::t('withdrawal','Do you have any advice for us?');?>');
            }
        }
        if(flag){
            $('.submitSend').hide()
            $('.J_ajax_submit_btn').show()
            $('.formStu .J_ajax_submit_btn').click();
            return false;
        } else{
            $( '<span id="J_fail_info" class="text-warning"><i class="glyphicon glyphicon-remove text-warning"></i> ' + msg.join('、') + '</span>' ).appendTo(button.parent()).fadeIn( 'fast' );
            return false;
        }
    }
    function talkStep(){
        $('.formData').hide()
        $('.result').removeClass('hide')
        $('.submitCheck').show()
        $('.formSub .J_ajax_submit_btn').hide()
        let step=childDetails.forms.find(item2 =>item2.key === 'talk01')
        let data=step.data
        $('input[type="radio"][name="talk_result"][value='+data.talk_result+']').prop('checked', true);
        if(data.talk_other){
            container.addOtherList=data.staff_info[data.talk_other]
        }else{
            container.addOtherList=childDetails.info.appInfo.staffInfo[childDetails.info.appInfo.talkDefault]
        }
        addTalkDom()
        if(data.talk_result=='1'){
            $('#talkList').removeClass('hide')
        }
    }
    //第二步
    $('.talkResult').on('click', function() {
        if ($(this).val()=='1') {
            $('#talkList').removeClass('hide')
        } else {
            $('#talkList').addClass('hide')
        }
    });
    function addTallk(){
        $('#addOtherTalkModal').modal('show')
    }
    function addTalkDom(){
       
        var talkDom='<span class="flex align-items addTalk">'+
            '<img alt="" class="avatar20" src='+container.addOtherList.photoUrl+'> '+
            '<div class="color3 font12 ml4">'+container.addOtherList.name+'</div>'+
            "<span class='bluebg ml10  font12 cur-p' onclick='addTallk()'>替换</span>"+
        '</span>'
        $('#talkList').html(talkDom)
    }
    // function delTalk(html){
    //     let id = $(html).attr('data-id')
    //     console.log(id)
    //     for(let i=0;i<container.addOtherList.length;i++){
    //         if(container.addOtherList[i].uid==id){
    //             console.log(i)
    //             container.addOtherList.splice(i,1)
    //             addTalkDom()
    //         }
    //     }
    // }
    function submitCheck(_this){
        $('#J_fail_info').remove();
        var button = $(_this).find('button[type="submit"]');
        var flag = true, msg=[];
        if($('input[name="talk_result"]:checked').length == 0){
            flag = false;
            msg.push('<?php echo Yii::t("teaching","请选择面谈结果");?>');
        }
       if($('input[name="talk_result"]:checked').val()==1){
           $('input[name="talk_other"]').val(container.addOtherList.uid)
       }else{
            $('input[name="talk_other"]').val('')
       }
        if(flag){
            $('.submitCheck').hide()
            $('.formSub .J_ajax_submit_btn').show()
            $('.formSub .J_ajax_submit_btn').click();
            return false;
        } else{
            $( '<span id="J_fail_info" class="text-warning"><i class="glyphicon glyphicon-remove text-warning"></i> ' + msg.join('、') + '</span>' ).appendTo(button.parent()).fadeIn( 'fast' );
            return false;
        }
    }
</script>
<style>
    .avatar20{
        width: 20px;
        height: 20px;
        border-radius: 50%;
        object-fit: cover;
    }
    .addTalk{
        padding:4px;
        display:inline-flex;
        background: #F2F3F5;
        border-radius: 2px;
        margin-left:16px;
    }
    .pt0{
        padding-top:0 !important
    }
    .talkResultFlex{
        display: flex;
        align-items: center;
        height:24px;
        padding-top: 0 !important;
    }
</style>