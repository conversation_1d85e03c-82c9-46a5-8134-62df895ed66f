<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
        <li class="active">申请列表</li>
    </ol>

    <div class="row">
        <div class="col-md-2 col-sm-2 mb10">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->getMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
                'itemCssClass' => ''
            ));
            ?>
        </div>
        <div class="col-md-10 col-sm-10">
            <div class="btn-group mb10 row">
                <!-- 搜索框 -->
                <form action="<?php echo $this->createUrl('scholarshipApplyList'); ?>" method="get">
                    <?php echo Chtml::hiddenField('branchId',$this->branchId); ?>
                    <div class="col-lg-12 form-inline mb10">
                        <!-- 名字 -->
                        <div class="form-group">
                            <input type="text" class="form-control" name="username" placeholder="孩子姓名" value="<?php echo Yii::app()->request->getParam('username','')?Yii::app()->request->getParam('username',''):''; ?>">
                        </div>
                        <!-- 类型 -->
                        <div class="form-group">
                            <?php echo Chtml::dropDownList('type', $type, $applyType,array('class'=>'form-control', 'empty' => '请选择类型')); ?>
                        </div>
                        <div class="form-group">
                            <?php echo Chtml::dropDownList('year', $year, $yearList,array('class'=>'form-control', 'empty' => '请选择学年')); ?>
                        </div>
                        <!-- 内容匹配 -->
                        <div class="form-group">
                            <button class="btn btn-default" type="submit"><span class="glyphicon glyphicon-search"> </span> </button>
                            <!--<a href="javascript:void(0);" onclick="exportInfo()" class="btn btn-info" target="_blank"><?php /*echo Yii::t('user', 'Export');*/?></a>-->
                        </div>
                    </div>
                </form>
            </div>
            <div class="panel panel-default">
                <div class="panel-body">
                    <?php
                    $this->widget('ext.ivyCGridView.BsCGridView', array(
                        'id'=>'scholarship-list',
                        'afterAjaxUpdate' => 'js:head.Util.modal',
                        'dataProvider'=>$dataProvider,
                        //	'filter'=>$model,
                        'template'=>"{items}{pager}{summary}",
                        'colgroups'=>array(
                            array(
                                "colwidth"=>array(80,150,100,100,150,150,150,150,250),
                                //  "htmlOptions"=>array("align"=>"left", "span"=>2)
                            )
                        ),
                        'columns'=>array(
                            array(
                                'name'=> Yii::t('user','学年'),
                                'type'=>'raw',
                                'value'=>'$data->startyear?$data->startyear:Yii::t(\'attends\', \'Not Assigned Year\')',
                            ),
                            array(
                                'name'=> Yii::t('user','奖学金类型'),
                                'type'=>'raw',
                                'value' => array($this, 'getApplyType'),
                            ),
                            array(
                                'name'=> Yii::t('campus','Student name'),
                                'type'=>'raw',
                                'value' => array($this, 'getChildName'),
                            ),
                            array(
                                'name'=> Yii::t('user','Date Of Birth'),
                                'type'=>'raw',
                                'value'=>'date("Y-m-d", $data->birthday)',
                            ),
                            array(
                                'name'=> Yii::t('user','申请时间'),
                                'type'=>'raw',
                                'value'=>'date("Y-m-d H:i:s", $data->created_at)',
                            ),
                            array(
                                'name'=> Yii::t('user','家长邮箱'),
                                'type'=>'raw',
                                'value'=>'$data->parent_email',
                            ),
                            array(
                                'name'=> Yii::t('user','家长电话'),
                                'type'=>'raw',
                                'value'=>'$data->parent_phone',
                            ),
                            array(
                                'name'=> Yii::t('user','申请文件'),
                                'type'=>'raw',
                                'value' => array($this, 'getButtunFile'),
                            ),
                            array(
                                'name'=> Yii::t('asa','action'),
                                'type'=>'raw',
                                'value' => array($this, 'getButtun'),
                            ),
                        ),
                    )); ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    var modal = '<div class="modal fade" id="modal" class="1123" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog" role="document"><div class="modal-content"></div></div></div>';
    $('body').append(modal);

    function cbSuccess() {
        $('#modal').modal('hide');
        $.fn.yiiGridView.update('scholarship-list', {
            complete: function () {
                head.Util.ajaxDel();
                head.Util.modal();
            }
        });
    }

    function exportInfo() {
        var type = '<?php echo Yii::app()->request->getParam('type','') ?>';
        var year = '<?php echo Yii::app()->request->getParam('year','') ?>';
        var url = '<?php echo $this->createUrl('exportInfo') ?>';
        $.ajax({
            url: url,
            type: 'POST',
            data: {type:type,year:year},
            success: function (res) {
                if (res.state == 'success') {
                    var data = res.data.items;
                    const filename = res.data.title;
                    const ws_name = "SheetJS";

                    const worksheet = XLSX.utils.aoa_to_sheet(data);
                    const workbook = XLSX.utils.book_new();
                    XLSX.utils.book_append_sheet(workbook, worksheet, ws_name);
                    // generate Blob
                    const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                    const blob = new Blob([wbout], {type: 'application/octet-stream'});
                    // save file
                    let link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = filename;
                    link.click();
                    setTimeout(function() {
                        // 延时释放掉obj
                        URL.revokeObjectURL(link.href);
                        link.remove();
                    }, 500);
                }
            },
            dataType: 'json'
        });
    }

</script>
