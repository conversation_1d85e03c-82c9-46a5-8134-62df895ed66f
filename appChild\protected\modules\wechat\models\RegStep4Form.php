<?php
class RegStep4Form extends CFormModel
{
	public $preferredHospitalOne;
	public $preferredHospitalTwo;
	public $insuranceCompany;
	public $oneEmergencyName;
	public $oneEmergencyPhone;
	public $twoEmergencyName;
	public $twoEmergencyPhone;
	public $ADHD;
	public $heartDisorder;
	public $allergies;
	public $frequent;
	public $asthma;
	public $hepatitis;
	public $problems;
	public $gastrointertianl;
	public $fractures;
	public $skinProblems;
	public $diabetes;
	public $visionProblems;
	public $tuberculosis;
	public $tuberculosisOne;
	public $tuberculosisTwo;
	public $tuberculosisThree;
	public $tuberculosisFour;
	public $vision;
	public $seizureDisorde;
	public $specialFood;
	public $other;
	public $otherDiseases;
	public $medicalReport = array();
	public $medicalReportFile;
	public $vaccineReport = array();
	public $vaccineReportFile;
	public $healthReport = array();
	public $healthReportFile;
	public $filedata;

	public function rules()
	{
		return array(
			array('preferredHospitalOne, preferredHospitalTwo, oneEmergencyName, oneEmergencyPhone, twoEmergencyPhone, twoEmergencyName, ADHD, heartDisorder, allergies, frequent, asthma, hepatitis, problems, gastrointertianl, fractures, skinProblems, diabetes, visionProblems, tuberculosisOne, tuberculosis, tuberculosisTwo, tuberculosisThree, tuberculosisFour, seizureDisorde, specialFood, other, otherDiseases, filedata, vaccineReport', 'required', 'on'=>'submit'),
			array('medicalReport, vaccineReport, healthReport, insuranceCompany, medicalReportFile, vaccineReportFile, healthReportFile', 'safe'),
			array("medicalReportFile, vaccineReportFile, healthReportFile","file", "types" => "jpg, gif, png, jpeg", "maxSize" => 1024*1024*5, "tooLarge"=>Yii::t('reg', 'File should not exceed 5M'), 'allowEmpty' => true, 'on'=>'uploadFile'),
		);
	}

	public function attributeLabels()
	{
		return array(
			'preferredHospitalOne' => Yii::t("reg", "Preferred Hospital 1"),
			'preferredHospitalTwo' => Yii::t("reg", "Preferred Hospital 2"),
			'insuranceCompany' => Yii::t("reg", "Insurance Company"),
			'oneEmergencyName' => Yii::t('reg', 'Emergency Contact Person 1'),
			'oneEmergencyPhone' => Yii::t('reg', 'Emergency Contact Person Phone 1'),
			'twoEmergencyName' => Yii::t('reg', 'Emergency Contact Person 2'),
			'twoEmergencyPhone' => Yii::t('reg', 'Emergency Contact Person Phone 2'),
			'specialFood' => Yii::t('reg', 'Allergy information (Please specify) (Including but not limited to food and medicine)'),
			'other' => Yii::t('reg', 'Other (please specify)'),
			'otherDiseases' => Yii::t('reg', 'Does the student take any medication (orally or by injection) on a regular basis? If yes, please describe in details. '),
			'ADHD' => Yii::t('reg', 'ADD/ADHD'),
			'heartDisorder' => Yii::t('reg', 'Heart Disorder'),
			'allergies' => Yii::t('reg', 'Allergies (food,medications,etc.)'),
			'frequent' => Yii::t('reg', 'Frequent Ear Infections/Hearing Problems'),
			'asthma' => Yii::t('reg', 'Asthma'),
			'hepatitis' => Yii::t('reg', 'Hepatitis'),
			'problems' => Yii::t('reg', 'Back problems or scoliosis'),
			'gastrointertianl' => Yii::t('reg', 'Gastrointestinal Disorder'),
			'fractures' => Yii::t('reg', 'Bone Fractures'),
			'skinProblems' => Yii::t('reg', 'Skin Problems'),
			'diabetes' => Yii::t('reg', 'Diabetes'),
			'visionProblems' => Yii::t('reg', 'Vision/Color Vision Problems'),
			'seizureDisorde' => Yii::t('reg', 'Epilepsy/Seizure Disorder'),
			'tuberculosis' => Yii::t('reg', 'Tuberculosis'),
            'tuberculosisOne' => Yii::t('reg', 'Cough and expectoration lasting more than 2 weeks'),
            'tuberculosisTwo' => Yii::t('reg', 'There is blood in the mucus repeatedly coughed up'),
            'tuberculosisThree' => Yii::t('reg', 'Recurrent fever lasting more than 2 weeks'),
            'tuberculosisFour' => Yii::t('reg', 'Are there tuberculosis patients among the family members, relatives and friends who are often in contact with the immediate family over the last 2 years'),
			'vision' => Yii::t('reg', 'Vision/Color Vision Problems'),
			'medicalReport' => Yii::t('reg', 'Physical Examination Report'),
			'medicalReportFile' => Yii::t('reg', 'Physical Examination Report'),
			'vaccineReport' => Yii::t('reg', 'Copy of the Vaccination Record'),
			'vaccineReportFile' => Yii::t('reg', 'Copy of the Vaccination Record '),
			'healthReport' => Yii::t('reg', '北京儿童保健记录'),
			'healthReportFile' => Yii::t('reg', '北京儿童保健记录'),
		);
	}


	public static function StudentHealthHistory()
	{
		return array(
			'ADHD' => Yii::t('reg', 'ADD/ADHD'),
			'heartDisorder' => Yii::t('reg', 'Heart Disorder'),
			'allergies' => Yii::t('reg', 'Allergies(food,medications,etc.)'),
			'frequent' => Yii::t('reg', 'Frequent Ear Infections /Hearing Problems'),
			'asthma' => Yii::t('reg', 'Asthma'),
			'hepatitis' => Yii::t('reg', 'Hepatitis'),
			'problems' => Yii::t('reg', 'Back problems or scoliosis'),
			'gastrointertianl' => Yii::t('reg', 'Gastrointestinal Disorder'),
			'fractures' => Yii::t('reg', 'Bone Fractures'),
			'skinProblems' => Yii::t('reg', 'Skin Problems'),
			'diabetes' => Yii::t('reg', 'Diabetes'),
			'visionProblems' => Yii::t('reg', 'Vision/Color Vision Problems'),
			'seizureDisorde' => Yii::t('reg', 'Epilepsy/Seizure Disorder'),
            //'vision' => Yii::t('reg', 'Vision/Color Vision Problems'),
			'tuberculosis' => Yii::t('reg', 'Tuberculosis'),

            'tuberculosisOne' => Yii::t('reg', 'Cough and expectoration lasting more than 2 weeks'),
            'tuberculosisTwo' => Yii::t('reg', 'There is blood in the mucus repeatedly coughed up'),
            'tuberculosisThree' => Yii::t('reg', 'Recurrent fever lasting more than 2 weeks'),
            'tuberculosisFour' => Yii::t('reg', 'Are there tuberculosis patients among the family members, relatives and friends who are often in contact with the immediate family over the last 2 years'),
		);
	}

	public function uploadFile()
	{
		$oss = CommonUtils::initOSS('private');
	
		$this->medicalReportFile ? $this->medicalReport = array() : '';
		foreach ($this->medicalReportFile as $item) {
			$fileName = 'medicalReport_' . time() . '_' .uniqid() . '.' . $item->getExtensionName();
			if ($res = $this->upload($oss, $item, $fileName)) {
				$this->medicalReport[] = $res;
			} else {
				return false;
			}
		}

		$this->vaccineReportFile ? $this->vaccineReport = array() : '';
		foreach ($this->vaccineReportFile as $item) {
			$fileName = 'vaccineReport_' . time() . '_' .uniqid() . '.' . $item->getExtensionName();
			if ($res = $this->upload($oss, $item, $fileName)) {
				$this->vaccineReport[] = $res;
			} else {
				return false;
			}
		}

		$this->healthReportFile ? $this->healthReport = array() : '';
		foreach ($this->healthReportFile as $item) {
			$fileName = 'healthReport_' . time() . '_' .uniqid() . '.' . $item->getExtensionName();
			if ($res = $this->upload($oss, $item, $fileName)) {
				$this->healthReport[] = $res;
			} else {
				return false;
			}
		}

		return true;
	}

	public function upload($oss, $file, $fileName)
	{
		if ($file) {
			// 上传到 OSS
			$objectPath = 'regextrainfo/';

		    if ($oss->uploadFile($objectPath . $fileName, $file->getTempName())) {
		    	return $fileName;
		    }
		}
	    return false;
	}

	public function getOssImageUrl($fileName)
	{
		$fileName = 'regextrainfo/' . $fileName;
		// 获取文件临时地址
		$osscs = CommonUtils::initOSSCS('private');

	    $object = $fileName;
	    $style = 'style/w200';
	    $imageUrl = $osscs->getImageUrl($object, $style);
	    return $imageUrl;
	}

    public function updataSignature()
    {
        // 判断是否为 base64 图片
        if (strpos($this->filedata, 'base64') === false) {
            return true;
        }
        $data = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $this->filedata));

        $normalDir = rtrim(Yii::app()->params['xoopsVarPath'], '/') .'/';

        $fileName = uniqid() . '.png';

        $filePath = $normalDir . $fileName;

        if (file_put_contents($filePath, $data)) {
            $this->filedata = $fileName;

            if ($this->filedata) {
                // 上传到 OSS
                $objectPath = 'regextrainfo/';
                $oss = CommonUtils::initOSS('private');
                if ($oss->uploadFile($objectPath . $this->filedata, $filePath)) {
                    unlink($filePath);
                    return true;
                }
            }
        }
        return false;
    }

}