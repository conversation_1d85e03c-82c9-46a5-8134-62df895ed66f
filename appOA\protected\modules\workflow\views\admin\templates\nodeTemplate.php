<!--单个节点模版-->
<script type="text/template" id="workflow-node-template">
    <div class="col-sm-2 workflow-block" id="workflow-node-<%= data.get('node_id') %>" data-nodeid="<%= data.get('node_id') %>">
        <a type="button" class="close J_ajax_del" aria-label="Close" href="<?php echo $this->createUrl('/workflow/admin/deleteNode');?>?definationId=<%= data.get('defination_id') %>&nodeId=<%= data.get('node_id') %>""><span aria-hidden="true">&times;</span></a>
        <div class="bg-success" style="padding:10px;cursor:pointer;" ondblclick="editNode(<%= data.get('node_id') %>);">
            <span class="glyphicon glyphicon-move mr5"></span>
            <%if (data.get('node_name_cn')){%><%= data.get('node_name_cn') %><%}else{%>双击编辑<%}%>
            <span class="badge pull-right" style="color:#dff0d8;background-color:#ffffff;"><%=length%></span>
        </div>
    </div>
</script>

<!--节点编辑模版-->
<script type="text/template" id="workflow-node-edit-template">
    <div class="form-group" model-attribute="node_name_cn">
        <?php echo CHtml::label($clabels['node_name_cn'], CHtml::getIdByName('WorkflowNode[node_name_cn]'), array('class'=>'col-sm-3 control-label'));?>
        <div class="col-sm-9">
            <?php echo CHtml::textField('WorkflowNode[node_name_cn]','<%= data.get("node_name_cn") %>', array('encode'=>false,'class'=>'form-control length_5')); ?>
        </div>
    </div>
    <div class="form-group" model-attribute="node_name_en">
        <?php echo CHtml::label($clabels['node_name_en'], CHtml::getIdByName('WorkflowNode[node_name_en]'), array('class'=>'col-sm-3 control-label'));?>
        <div class="col-sm-9">
            <?php echo CHtml::textField('WorkflowNode[node_name_en]','<%= data.get("node_name_en") %>', array('encode'=>false,'class'=>'form-control length_5')); ?>
        </div>
    </div>
    <div class="form-group" model-attribute="node_type">
        <?php echo CHtml::label($clabels['node_type'], CHtml::getIdByName('WorkflowNode[node_type]'), array('class'=>'col-sm-3 control-label'));?>
        <div class="col-sm-9">
            <?php echo CHtml::dropDownList('WorkflowNode[node_type]','<%= data.get("node_type") %>',WorkflowNode::getNodeType(), array('class'=>'form-control select_3','empty'=>Yii::t('global', 'Please Select')));?>
        </div>
    </div>
    <div class="form-group" model-attribute="field">
        <?php echo CHtml::label($clabels['field'], CHtml::getIdByName('WorkflowNode[field]'), array('class'=>'col-sm-3 control-label'));?>
        <div class="col-sm-9">
            <?php echo CHtml::textField('WorkflowNode[field]','<%= data.get("field") %>', array('encode'=>false,'class'=>'form-control length_5')); ?>
        </div>
    </div>
    <div class="form-group" model-attribute="field">
        <?php echo CHtml::label($clabels['field2'], CHtml::getIdByName('WorkflowNode[field2]'), array('class'=>'col-sm-3 control-label'));?>
        <div class="col-sm-9">
            <?php echo CHtml::textField('WorkflowNode[field2]','<%= data.get("field2") %>', array('encode'=>false,'class'=>'form-control length_5')); ?>
        </div>
    </div>
    <div class="form-group" model-attribute="executor">
        <?php echo CHtml::label($clabels['executor'], CHtml::getIdByName('WorkflowNode[executor]'), array('class'=>'col-sm-3 control-label'));?>
        <div class="col-sm-9">
            <?php echo CHtml::radioButtonList('WorkflowNode[executor]','',WorkflowDefination::getWorkflowAuth(),array('onclick'=>'appendHtml(this)','separator'=>'&nbsp;&nbsp;'));?>
                <?php echo CHtml::hiddenField('nodeId', '<%=data.get("node_id")%>',array('encode'=>false));?>
                <?php echo CHtml::hiddenField('definationId', '<%=data.get("defination_id")%>',array('encode'=>false));?>
        </div>
    </div>
    <div class="form-group" model-attribute="executorId">
        <?php echo CHtml::label('', CHtml::getIdByName('WorkflowNode[executorId]'), array('class'=>'col-sm-3 control-label'));?>
        <div class="col-sm-9" id="label-executorid">
          
        </div>
    </div>
</script>    