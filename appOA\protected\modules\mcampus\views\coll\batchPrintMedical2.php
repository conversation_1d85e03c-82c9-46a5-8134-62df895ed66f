<?php
$medicalCn = '';
$medicalEn = '';
$filedata = '';
$data = '';
if($model){
    $data = json_decode($model->data);
    $filedata = ($data->filedata) ? $data->filedata : '' ;
}
?>
<style>
.container-fluid{
    width:860px;
    margin:0 auto
}
.page{
    margin-top:20px
}
img{
    margin-top:10px;
    max-width:100%;
    height:auto
}
.sign{
    float: right;
    margin-right:20px;
    margin-bottom:20px;
    max-height: 15mm;
}
.sign img{
    width:200px;
    height: 110px;
}
@media print {
    .print_img{
        max-height: 280mm;
        display: block;
        margin: 0 auto;
    }
    .alwaysAttr{
        page-break-after:always;
    }
    .print_protocol_img
    {
        max-height: 280mm;
        display: block;
        margin: 0 auto;
    }
}
</style>
<?php $studentNum = count($child_infos);$i=0;?>
<?php foreach ($child_infos as $child_id=>$child_info){?>
    <?php
    $i++;
    //将所有图片合并
    //体检报告
    $all_photo[$child_id] = array();
    foreach ($step_data[$child_id]['examination_reports'] as $examination_reports){
        $all_photo[$child_id][] = $examination_reports;

    }
    //  <!--        疫苗-->
    foreach ($step_data[$child_id]['vaccines_reports'] as $vaccines_reports){
        $all_photo[$child_id][] = $vaccines_reports;
    }
    //保险
    foreach ($step_data[$child_id]['insurance_card_photos'] as $insurance_card_photos){
        $all_photo[$child_id][] = $insurance_card_photos;
    }


    ?>
    <div class="container-fluid">
        <div class ="row">
            <table class="table">
                <tbody>
                <tr>
                    <th  width="25%"><?php echo Yii::t('reg', 'Name') ?></th>
                    <td colspan="3"><?php echo $child_info->getChildName() ?></td>
                </tr>
                <tr>
                    <th><?php echo Yii::t('labels', 'Date of Birth') ?>期</th>
                    <td colspan="3"><?php echo $child_info->birthday_search ?></td>
                </tr>
                <tr>
                    <th><?php echo Yii::t('labels', 'Class') ?></th>
                    <td colspan="3"><?php echo $child_info->ivyclass->title?></td>
                </tr>
                <?php foreach ($step_data[$child_id]['intended_hospitals'] as $k=>$v){?>
                    <tr>
                        <th><?php echo Yii::t('reg', 'Preferred Hospital') ?> <?php echo $k+1?></th>
                        <td colspan="3"><?php echo $v?></td>
                    </tr>
                <?php }?>
                <tr>
                    <th><?php echo Yii::t('reg', 'Personal Medical Insurance Information') ?></th>
                    <td colspan="3"><?php echo $step_data[$child_id]['insurance_companies']?></td>
                </tr>
                <?php foreach ($step_data[$child_id]['emergency_contact'] as $k=>$v){?>
                    <tr>
                        <th><?php echo Yii::t('site', 'Emergency Contact') ?> <?php echo $k+1?></th>
                        <td colspan="3"><?php echo $v['name']?></td>
                    </tr>
                    <?php if(!empty($v['mobile'])){?>
                        <tr>
                            <th><?php echo Yii::t('user', 'Emergency Contact Phone') ?> <?php echo $k+1?></th>
                            <td colspan="3"><?php echo $v['mobile']?></td>
                        </tr>
                    <?php }?>

                    <?php if(!empty($v['email'])){?>
                        <tr>
                            <th><?php echo Yii::t('user', 'Emergency Contact Email') ?> <?php echo $k+1?></th>
                            <td colspan="3"><?php echo $v['email']?></td>
                        </tr>
                    <?php }?>

                <?php }?>
                <tr>
                    <th><?php echo Yii::t('reg', 'ADD/ADHD') ?></th>
                    <td><?php echo $step_data[$child_id]['is_adhd']==1 ?  "是":  "否" ?></td>
                    <th width="20%"><?php echo Yii::t('reg', 'Heart Disorder') ?></th>
                    <td><?php echo $step_data[$child_id]['is_heart_disease']==1 ?  "是":  "否" ?></td>
                </tr>

                <tr>
                    <th><?php echo Yii::t('reg', 'Frequent Ear Infections /Hearing Problems') ?></th>
                    <td><?php echo $step_data[$child_id]['frequent']==1 ?  "是":  "否" ?></td>
                    <th width="20%"><?php echo Yii::t('reg', 'Asthma') ?></th>
                    <td><?php echo $step_data[$child_id]['asthma']==1 ?  "是":  "否" ?></td>
                </tr>

                <tr>
                    <th><?php echo Yii::t('reg', 'Hepatitis') ?></th>
                    <td><?php echo $step_data[$child_id]['hepatitis']==1 ?  "是":  "否" ?></td>
                    <th width="20%"><?php echo Yii::t('reg', 'Back problems or scoliosis') ?></th>
                    <td><?php echo $step_data[$child_id]['problems']==1 ?  "是":  "否" ?></td>
                </tr>
                <tr>
                    <th><?php echo Yii::t('reg', 'Gastrointestinal Disorder') ?></th>
                    <td><?php echo $step_data[$child_id]['gastrointertianl']==1 ?  "是":  "否" ?></td>
                    <th width="20%"><?php echo Yii::t('reg', 'Bone Fractures') ?></th>
                    <td><?php echo $step_data[$child_id]['fractures']==1 ?  "是":  "否" ?></td>
                </tr>
                <tr>
                    <th><?php echo Yii::t('reg', 'Skin Problems') ?></th>
                    <td><?php echo $step_data[$child_id]['skinProblems']==1 ?  "是":  "否" ?></td>
                    <th width="20%"><?php echo Yii::t('reg', 'Diabetes') ?></th>
                    <td><?php echo $step_data[$child_id]['diabetes']==1 ?  "是":  "否" ?></td>
                </tr>
                <tr>
                    <th><?php echo Yii::t('reg', 'Vision/Color Vision Problems') ?></th>
                    <td><?php echo $step_data[$child_id]['visionProblems']==1 ?  "是":  "否" ?></td>
                    <th width="20%"><?php echo Yii::t('reg', 'Epilepsy/Seizure Disorder') ?></th>
                    <td><?php echo $step_data[$child_id]['seizureDisorde']==1 ?  "是":  "否" ?></td>
                </tr>
                <tr>
                    <th><?php echo Yii::t('reg', 'Tuberculosis') ?></th>
                    <td><?php echo $step_data[$child_id]['is_tuberculosis']==1 ?  "是":  "否" ?></td>
                    <th width="20%"><?php echo Yii::t('reg', 'Cough and expectoration lasting more than 2 weeks') ?></th>
                    <td><?php echo $step_data[$child_id]['tuberculosisOne']==1 ?  "是":  "否" ?></td>
                </tr>
                <tr>
                    <th><?php echo Yii::t('reg', 'There is blood in the mucus repeatedly coughed up') ?></th>
                    <td><?php echo $step_data[$child_id]['tuberculosisTwo']==1 ?  "是":  "否" ?></td>
                    <th width="20%"><?php echo Yii::t('reg', 'Recurrent fever lasting more than 2 weeks') ?></th>
                    <td><?php echo $step_data[$child_id]['tuberculosisThree']==1 ?  "是":  "否" ?></td>
                </tr>

                <tr>
                    <th><?php echo Yii::t('reg', 'Are there tuberculosis patients among the family members, relatives and friends who are often in contact with the immediate family over the last 2 years') ?></th>
                    <td><?php echo $step_data[$child_id]['tuberculosisFour']==1 ? "是":  "否"?></td>
                    <th width="20%"></th>
                    <td></td>
                </tr>
                <tr>
                    <th><?php echo Yii::t('reg', 'Allergies(food,medications,etc.)') ?></th>
                    <td><?php echo $step_data[$child_id]['allergies']==1 ? "是":  "否"?></td>
                    <!--                allergies_other  allergies_food allergies_medication-->
                    <th width="20%"><?php echo Yii::t('reg', 'Allergy') ?></th>
                    <?php if($step_data[$child_id]['allergies']==1){?>
                        <td><?php echo $step_data[$child_id]['allergies_food'] .PHP_EOL. $step_data[$child_id]['allergies_medication'].PHP_EOL.$step_data[$child_id]['allergies_other']?></td>
                    <?php }else{?>
                        <td>无</td>
                    <?php }?>
                </tr>
                <tr>
                    <th><?php echo Yii::t('reg', 'Does the student take any medication (orally or by injection) on a regular basis? If yes, please describe in details. ') ?> </th>
                    <td colspan="3"><?php echo $step_data[$child_id]['is_use_drugs']==1? $step_data[$child_id]['use_drugs'] : '否'?></td>
                </tr>
                <tr>
                    <th> <?php echo Yii::t('reg', 'Other Illnesses (please specify)') ?> </th>
                    <td colspan="3"><?php echo $step_data[$child_id]['other']?></td>
                </tr>
                </tbody>
            </table>
        </div>
        <div style="page-break-after:always;"></div>
        <?php if(isset($step_data[$child_id]['extraInfo']['protocol']) && !empty($step_data[$child_id]['extraInfo']['protocol'])){?>
            <!--    协议-->
            <div class="row">
                <?php $protocol_num = count($step_data[$child_id]['extraInfo']['protocol'])?>
                <?php foreach ($step_data[$child_id]['extraInfo']['protocol'] as $k=>$v){?>
                    <div>
                        <?php echo $child_info->getChildName() ?>
                        <?php echo $child_info->ivyclass->title?>
                    </div>
                    <p><img  class="img-responsive print_protocol_img" src="<?php echo $v; ?>"></p>
                    <?php if(($k+1)<$protocol_num){?>
                        <div style="page-break-after:always;"></div>
                    <?php }?>
                    <?php if(($k+1)==$protocol_num){?>
                        <?php if(!empty($step_data[$child_id]['childStatus']['sign_url'])){?>
                            <div class="sign">
                                <img src="<?php echo $step_data[$child_id]['childStatus']['sign_url']?>" alt="" >
                            </div>
                        <?php }?>
                        <div style="page-break-after:always;"></div>
                    <?php }?>
                <?php }?>
            </div>
        <?php }?>
        <div class="row">
            <?php if(!empty($all_photo[$child_id])){?>
                <?php foreach ($all_photo[$child_id] as $k=>$val){?>
                    <div>
                        <?php echo $child_info->getChildName() ?>
                        <?php echo $child_info->ivyclass->title?>
                    </div>
                    <p class="pageBreak">
                    <?php if(count($all_photo[$child_id]) == $k+1){?>
                        <img  class="img-responsive print_img" src="<?php echo $val; ?>">
                    <?php }else{?>
                        <img  class="img-responsive print_img alwaysAttr" src="<?php echo $val; ?>">
                    <?php }?>
                    </p>
                <?php }?>
            <?php }?>
        </div>
    </div>
    <?php if($studentNum != $i){?>
        <div style="page-break-after:always;"></div>
    <?php }?>
<?php }?>
