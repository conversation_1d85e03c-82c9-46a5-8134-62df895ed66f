<?php $form=$this->beginWidget('CActiveForm', array(
    'id'=>'pack-form',
    'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
));
?>
<div style="max-height: 350px;overflow-y: auto;">
    <?php
    $this->widget('ext.ivyCGridView.BsCGridView', array(
        'id'=>'pack-grid',
        'dataProvider'=>$models,
        'columns'=>array(
            array(
                'name'=>'商品',
                'value'=>'$data->Product->getContent()',
            ),
            'credits',
            'quantity',
            array(
                'name'=>'孩子',
                'value'=>'$data->ChildProfile->getChildName()',
            ),
        ),
    )); ?>

    <div class="pop_cont">
        <div class="form-group">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($omodel,'shipinfo'); ?></label>
            <div class="col-xs-9">
                <?php echo $form->textField($omodel,'shipinfo',array('class'=>'form-control')); ?>
            </div>
        </div>
        <div class="form-group">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($smodel,'memo'); ?></label>
            <div class="col-xs-9">
                <?php echo $form->textArea($smodel, 'memo', array('class'=>'form-control', 'rows'=>3))?>
            </div>
        </div>
    </div>
</div>
<div class="pop_bottom">
    <button id="J_dialog_close" type="button" class="btn btn-default pull-right"><?php echo Yii::t('global','Cancel');?></button>
    <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
</div>
<?php $this->endWidget(); ?>