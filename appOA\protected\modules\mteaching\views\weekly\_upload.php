<div id="container" class="mb15">
    <a id="pickfiles" href="javascript:;" class="btn btn-primary"><span class="glyphicon glyphicon-plus"></span>
        <?php echo Yii::t('teaching','Select Files');?></a>
    <a id="uploadfiles" href="javascript:;" class="btn btn-primary mr10"><span class="glyphicon glyphicon-arrow-up"></span>
        <?php echo Yii::t('teaching','Start Upload');?>
    </a>
    <?php if($taskData['uploadType']=='photo'):?>
    <span><?php echo Yii::t('teaching','Please note: small photo below 400×400(px) size will be ignored.');?></span>
    <?php endif;?>
</div>
<div class="row">
    <div class="col-md-4">
        <div class="panel panel-default" id="filelist">
            <div class="panel-heading"><?php echo Yii::t('teaching','Upload queue');?> <small class="text-primary">0</small></div>
            <div class="panel-body imgbox">
                <p class="text-muted drag-tip"><?php echo Yii::t('teaching','Drag files here.');?></p>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="panel panel-danger" id="unacceptlist" style="display: none;">
            <div class="panel-heading"><?php echo Yii::t('teaching','Photo not processed.');?> <small class="text-primary">0</small></div>
            <div class="panel-body"></div>
        </div>
        <div class="panel panel-default" id="uploadedlist">
            <div class="panel-heading"><?php echo Yii::t('teaching','Uploaded');?> <small class="text-primary">0</small></div>
            <div class="panel-body imgbox"></div>
        </div>
    </div>
</div>

<?php
$postParams = array();
$postParams = $taskData['xParams'];
$postParams['token'] = $taskData['upToken'];
?>

<script type="text/javascript">
    var count = 0, count1 = 0, count2 = 0;
    var uploader = new plupload.Uploader({
        runtimes : 'html5,flash,silverlight,html4',
        browse_button : 'pickfiles', // you can pass in id...
        container: document.getElementById('container'), // ... or DOM Element itself
        url : '<?php echo $taskData['sConfs']['uploadUrl'];?>',
        flash_swf_url : '<?php echo Yii::app()->themeManager->baseUrl.'/base/js/plupload/Moxie.swf'?>',
        silverlight_xap_url : '<?php echo Yii::app()->themeManager->baseUrl.'/base/js/plupload/Moxie.xap'?>',
        multipart_params : <?php echo CJSON::encode($postParams);?>,

        resize : {
            width : 2000,
            height : 2000
        },

        filters : {
            max_file_size : '<?php echo ($taskData['uploadType']=='photo')? '15mb' : '500mb';?>',
            mime_types: [
                <?php
                if ($taskData['uploadType']=='photo'):
                ?>
                {title : "<?php echo Yii::t('teaching', 'Image files');?>", extensions : "jpg,gif,png,jpeg"}
                <?php else:?>
                {title : "<?php echo Yii::t('teaching', 'Video files');?>", extensions : "mp4,avi,mpg,wmv,mov,3gp,FLV,flv"}
                <?php endif;?>

            ]
        },

        drop_element: document.getElementById('filelist'),

        init: {
            PostInit: function() {
                document.getElementById('uploadfiles').onclick = function() {
                    $(this).addClass('disabled');
                    uploader.start();
                    $(window).bind('beforeunload',function(){
                        return '正在上传文件，确定离开此页面吗？';
                    });
                    return false;
                };
            },

            Browse: function(up) {
                // Called when file picker is clicked
            },

            Refresh: function(up) {
                // Called when the position or dimensions of the picker change
            },

            StateChanged: function(up) {
                // Called when the state of the queue is changed
            },

            QueueChanged: function(up) {
                // Called when queue is changed by adding or removing files
            },

            OptionChanged: function(up, name, value, oldValue) {
                // Called when one of the configuration options is changed
            },

            BeforeUpload: function(up, file) {
                // Called right before the upload for a given file starts, can be used to cancel it if required
            },

            FileFiltered: function(up, file) {
                // Called when file successfully files all the filters
                var filelist = $('#filelist');
                queueHtml(file);
                filelist.find('div.panel-heading small').html(++count);

                $('#'+file.id+' span').click(function(){
                    $('#'+file.id).remove();
                    uploader.removeFile(file);
                });
            },

            FilesAdded: function(up, files) {

            },

            UploadProgress: function(up, file) {
                $('#'+file.id+' dt span').hide();
                $('#'+file.id+' div.progress-bar').attr('aria-valuenow', file.percent).css('width', file.percent+'%').html(file.percent+'%');
            },

            FilesRemoved: function(up, files) {
                // Called when files are removed from queue
                count-=files.length;
                $('#filelist div.panel-heading small').html(count);
            },

            FileUploaded: function(up, file, info) {
                // Called when file has finished uploading
                var response = eval('('+info.response+')');
                if(info.status == 200){
                    if(typeof(response.photoOK) != 'undefined' && response.photoOK == 0){
                        var obj = $('#unacceptlist');
                        if(obj.is(':hidden')){
                            obj.slideDown();
                        }
                        obj.find('div.panel-heading small').html(++count2);
                    }
                    else{
                        var obj = $('#uploadedlist');
                        obj.find('div.panel-heading small').html(++count1);
                    }

                    <?php if ($taskData['uploadType']=='photo'):?>
                    var img = new Image();
                    img.src = '<?php echo $taskData['sConfs']['url']?>'+response.key+'!h120';
                    img.className = 'phide';

                    var imgcss = '';
                    if($.browser.msie){
                        var div = document.createElement("div");
                        div.id = 'success_'+file.id;
                        div.className = 'pull-left mr10 mb10';
                        obj.find('div.panel-body').prepend( div );
                        obj.find('div.panel-body #success_'+file.id).html(img);
                        imgcss = 'img-thumbnail';
                    }
                    else{
                        obj.find('div.panel-body').prepend( img );
                        imgcss = 'img-thumbnail mr10 mb10';
                    }
                    img.onload = function(){
                        $(img).addClass(imgcss).removeClass('phide');
                    }
                    <?php else:?>
                    obj.find('div.panel-body').prepend( '<p class="text-success">'+file.name+' <small class="text-primary">视频已经上传成功！系统转码中，稍后可在媒体管理查看。</small></p>' );
                    <?php endif;?>
                    $('#'+file.id).fadeOut('normal', function(){$(this).remove()});
                    count-=1;
                    $('#filelist div.panel-heading small').html(count);
                }
            },

            UploadComplete: function(up, files) {
                // Called when all files are either uploaded or failed
                $('#uploadfiles').removeClass('disabled');
                $(window).unbind('beforeunload');
            },

            Error: function(up, err) {
                if(err.response){
                    var response = eval('('+err.response+')');
                }
                else{
                    var response = {message: err.message};
                    queueHtml(err.file);
                }
                $('#'+err.file.id).addClass('text-danger');
                $('#'+err.file.id+' dt span').removeClass('glyphicon-remove').addClass('glyphicon-exclamation-sign').show();
                $('#'+err.file.id+' dd.progress').removeClass('progress').html( response.error );
                if(response.error == 'expired token'){
                    uploader.stop();
                    $.getJSON('<?php echo $this->createUrl('qiniuOpt')?>', {classid: classid, weeknum: weeknum, fileType: '<?php echo $taskData['uploadType']?>'}, function(data){
                        var pars = [];
                        pars = data.xParams;
                        pars['token'] = data.upToken;
                        up.setOption('multipart_params', pars);
                        up.start();
                    });
                }
            }
        }
    });

    uploader.init();

    function queueHtml(file)
    {
        var filelist = $('#filelist');
        var queueHtml = '<dl id="' + file.id + '"><dt><span class="glyphicon glyphicon-remove"></span> ';
        queueHtml += file.name+' <small>('+plupload.formatSize(file.size)+')</small>';
        queueHtml += '</dt><dd class="progress">';
        queueHtml += '<div class="progress-bar" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>';
        queueHtml += '</div></dd></dl>';
        filelist.find('div.panel-body').append(queueHtml);
    }
</script>

<style>
    .imgbox{height: 450px;overflow-y: auto;}
    .phide{-webkit-transform: scale(0);transition-delay: 0;}
    .drag-tip{position: absolute; top: 0;bottom:0;left:0;right:0;text-align: center;vertical-align: middle;line-height: 400px;}
    .imgbox dl{position: relative;background-color: #fff;margin-bottom: 0;padding-bottom: 17px;}
    dd.progress{margin-bottom:0;}
</style>