<!-- Modal -->
<?php echo CHtml::form('/workflow/entrance/index', 'post',array('class'=>'J_ajaxForm'));?>
<div class="modal fade" id="<?php echo $data['modalNameId']?>" tabindex="-1" role="dialog" aria-labelledby="workflowModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel"><?php echo CommonUtils::autoLang($data['workflow']->definationObj->defination_name_cn,$data['workflow']->definationObj->defination_name_en);?> - <?php echo CommonUtils::autoLang($data['workflow']->nodeObj->node_name_cn,$data['workflow']->nodeObj->node_name_cn);?></h4>
            </div>
            <div class="modal-body" id="workflow-modal-body">
                <ul class="nav nav-wizard mb20">
                    <li class="active" style="width:33%;"><a>第一步：选择退费孩子</a></li>
                    <li style="width:33%;"><a>第二步：选择退费账单</a></li>
                    <li style="width:33%;"><a>第三步：确认退费金额</a></li>
                </ul>
                <div class="form-group">
                    <label for="searchChild">查询孩子</label>
                    <?php $this->widget('ext.search.ChildSearchBox', array(
                            'acInputCSS' => 'form-control',
                            'allowMultiple' => false,
                            'simpleDisplay' => false,
                            'extendCss' => false,
                            'useModel' => false,
                            'withAlumni' => true,
                            'name' => 'RefundFee[childid]',
                            'htmlOptions' => array('class'=>'form-control'),
                            'select'=>$data['select'],
                            'data'=>$data['ftsChild']
                        )) ?>
                </div>
                <div class="form-group">
                    <label>退费类别</label>
                    <?php foreach ($data['type'] as $k=>$type): ?>
                        <p>
                            <input onclick="getRefundType(this);" type="radio" value="<?php echo $k;?>" name="RefundFee[refund_type]" id="RefundFee_refund_type<?php echo $k;?>"<?php if($data['workflow']->getOperateValue('exte3') == $k) echo ' checked=checked' ?>>
                            <label for="RefundFee_refund_type<?php echo $k;?>"><?php echo $type; ?></label>
                            <p class="help-block">&nbsp;&nbsp;&nbsp;&nbsp;<?php echo $data['typeDesc'][$k]; ?></p>
                        </p>
                    <?php endforeach; ?>
                </div>
                <div class="form-group" id="refund-date">
                    <?php if (isset($data['workflow']->opertationObj)): ?>
                        <?php if (in_array($data['workflow']->getOperateValue('exte3'), array(20, 50))): ?>
                            <label>选择日期</label>
                            <input class="input form-control ui-autocomplete-input" placeholder="选择日期" id="RefundFee_leave_date" type="text" name="RefundFee[leave_date]" value="<?php echo OA::formatDateTime($data['workflow']->getOperateValue('exte1'));?>">
                        <?php else:?> 
                            <label>退费日期</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <input class="input form-control" placeholder="退费开始日期" id="RefundFee_refund_startdate" type="text" name="RefundFee[refund_startdate]" value="<?php echo OA::formatDateTime($data['workflow']->getOperateValue('exte1'));?>">
                                </div>
                                <div class="col-md-6">
                                    <input class="input form-control" placeholder="退费结束日期" id="RefundFee_refund_enddate" type="text" name="RefundFee[refund_enddate]" value="<?php echo OA::formatDateTime($data['workflow']->getOperateValue('exte2'));?>">
                                </div>
                            </div>
                        <?php endif;?>
                    <?php endif;?>
                </div>
                <div class="form-group">
                    <label for="exampleInputEmail1">意见</label>
                    <?php echo CHtml::textArea('RefundFee[memo]',$data['processDesc'],array('class'=>'form-control','row'=>3,'placeholder'=>'退费请注明原因'));?>
                </div>
            </div>
            <div class="modal-footer">
                <?php echo CHtml::hiddenField('definationId', $data['workflow']->getWorkflowId());?>
                <?php echo CHtml::hiddenField('nodeId', $data['workflow']->getCurrentNodeId());?>
                <?php echo CHtml::hiddenField('branchId', $data['workflow']->schoolId);?>
                <?php echo CHtml::hiddenField('operationId', $data['workflow']->getOperateId());?>
                <?php echo CHtml::hiddenField('step', 'one');?>
                <?php echo CHtml::hiddenField('buttonCategory', '');?>
                <?php echo CHtml::hiddenField('action', 'publicSave');?>
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('workflow', 'Cancel');?></button>
                <button type="button" class="btn btn-primary J_ajax_submit_btn" data-category="next">下一步</button>
            </div>
        </div>
    </div>
</div>
<?php CHtml::endForm();?>
<?php 
$str =<<<js
    <script language="javascript" type="text/javascript"> 
        function mySelect(elem){
            var v=$(elem).attr('uid');
            jQuery('#searchChild').data('autocomplete').close();
            $('#searchChild').val("");
            if(v>0)
            $('#RefundFee_childid').append('<option value="'+v+'" selected="selected">'+$(elem).html()+'</option>');
        }
        jQuery('#searchChild').autocomplete({'minLength':'1','delay':1000,'position':{'my':'left top','at':'left bottom','collision':'none'},'source':'/backend/search/childoutput?displayType=&withAlumni=withAlumni&allowMultipleSchool=1'});
        jQuery('#searchChild').data( 'autocomplete' )._renderItem = function( ul, item ) {
				return $( '<li onclick=\'mySelect(this);return false;\'></li>' )
					.attr('uid',item.value)
					.data( 'item.autocomplete', item )
					.append( item.label)
					.appendTo( ul );			
        };
    </script>
js;
    echo $str;
?>
<script>
    var REFUND_FEE = <?php echo $data['refundFee'];?>;
    var ext3 = "<?php if ($data['workflow']->getOperateValue('exte3')){ echo $data['workflow']->getOperateValue('exte3');}else{echo "";}?>";
    if (ext3 && ext3 == 10){
        $('#RefundFee_refund_startdate').datepicker({'dateFormat':'yy-mm-dd'});
        $('#RefundFee_refund_enddate').datepicker({'dateFormat':'yy-mm-dd'});
    }else if (ext3 && ext3 == 20){
        $('#RefundFee_leave_date').datepicker({'dateFormat':'yy-mm-dd'});
    }
</script>