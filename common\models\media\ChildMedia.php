<?php

/**
 * This is the model class for table "child_media_2013".
 *
 * The followings are the available columns in table 'child_media_2013':
 * @property integer $id
 * @property string $schoolid
 * @property integer $classid
 * @property integer $yid
 * @property integer $weeknum
 * @property string $tag
 * @property string $type
 * @property string $filename
 * @property integer $server
 * @property integer $timestamp
 * @property integer $uid
 */
class ChildMedia extends CActiveRecord
{
    public static $SandBox = true;
    public static $StartYear = 2013;


	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
        if(self::$SandBox){
            return 'ivy_child_media_sandbox_'.self::$StartYear;
        }else{
            return 'ivy_child_media_'.self::$StartYear;
        }
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('schoolid, classid, yid, weeknum, filename, server, timestamp, uid', 'required'),
			array('classid, yid, weeknum, server, timestamp, uid', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>30),
			array('type', 'length', 'max'=>5),
			array('filename, tag', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, schoolid, classid, yid, weeknum, tag, type, filename, server, timestamp, uid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'schoolid' => 'Schoolid',
			'classid' => 'Classid',
			'yid' => 'Yid',
			'weeknum' => 'Weeknum',
			'tag' => 'Tag',
			'type' => 'Type',
			'filename' => 'Filename',
			'server' => 'Server',
			'timestamp' => 'Timestamp',
			'uid' => 'Uid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('weeknum',$this->weeknum);
		$criteria->compare('tag',$this->tag,true);
		$criteria->compare('type',$this->type,true);
		$criteria->compare('filename',$this->filename,true);
		$criteria->compare('server',$this->server);
		$criteria->compare('timestamp',$this->timestamp);
		$criteria->compare('uid',$this->uid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return ChildMedia the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public static function setOptions($sandbox, $startYear)
    {
        self::$SandBox = $sandbox;
        self::$StartYear = $startYear;
    }
}
