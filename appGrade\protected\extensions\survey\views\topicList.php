<?php 
/*
 * <PERSON>.<PERSON>
* 2012-7-3
*
*/

?>


<div class="survey_box">
	<?php if($template_count):?>
	<?php $form = $this->beginWidget('CActiveForm', array(
			'id'=>'topic-list-form',
			'enableAjaxValidation'=>false,

	));

	$template_index = 1;
	foreach ($topic_list as $template_id => $topics):

	?>

	<div class="template" id="template_<?php echo $template_id;?>">

		<?php if(!empty($template_list[$template_id])):?>
		<h3>
			<?php echo $template_list[$template_id];?>
		</h3>
		<?php 	else:?>
		<?php 	if($template_index > 1):?>
		<h3>
			<?php echo sprintf(Yii::t("survey", 'Part %s'),$template_index);?>
		</h3>
		<?php 	endif;?>
		<?php 	endif;?>
		<?php 	$template_index++;?>
	</div>

	<?php  
	$this_dir = dirname(__FILE__);
	foreach ($topics as $tid => $topic){
		$topic->topicNum = ++$tid;
		// 将 渲染整个 题目的内容独立出来
		$this->renderInternal($this_dir.'/topicView.php',array('topic'=>$topic,'langKey'=>$langKey,'this_dir'=>$this_dir));
	}
	?>

	<?php  endforeach; ?>
	<?php  if(true === $this->allowFeedback): ?>

	<div class="actions">
		<input type="hidden" value="<?php echo $this->surveyId;?>"
			name="survey_id" id="survey_id" /> <input type="hidden"
			value="<?php echo $this->childId;?>" name="childid" id="childid" />
		<?php echo  $this->ajaxSubmitButton; ?>


		<?php $this->beginWidget('zii.widgets.jui.CJuiDialog', array(
				'id'=>'mydialog',
				// additional javascript options for the dialog plugin
				'options'=>array(
						// 提示
						'title'=>Yii::t("survey", 'Hint'),
						'autoOpen'=>false,
				),
		)); ?>
		<?php $this->endWidget('zii.widgets.jui.CJuiDialog'); ?>



	</div>
	<?php  endif; ?>

	<?php $this->endWidget(); ?>
	<?php else:?>

	<div class="no-data">
		<ul>
			<li><span> <?php 
			//暂无数据
			echo Yii::t("survey", 'No Data');
			?>
			</span>
			</li>
		</ul>
	</div>
	<?php endif;?>

</div>

<script type="text/javascript">
   
    $(document).ready(function() {
        var o_id = null;
        $(".userinput li.option .rb:checked").each(function(i, item) {
            $("#label_" + item.id).removeClass('rb_off').addClass('rb_on');
        });
        $(".userinput li.option .cb:checked").each(function(i, item) {
            $("#label_" + item.id).removeClass('cb_off').addClass('cb_on');
        });
        $("#is_feedback").val(0);
        
        setup();
    });
/*
 * 提交表单之前调用的方法，这里主要做 验证用
 */
    function beforeSendFunc(){
    	var checked_num = 0;
    	var has_error = false;
    	$("div.topic[is_required=true]").each(function(i, item) {
    		checked_num = $("input[name^='option[" + $(item).attr('topic_id') + "]']:checked").length;
			if(parseInt(checked_num) < 1) {
				$(this).removeClass('required').addClass('required');
				has_error = true;
			}else{
                $(this).removeClass('required');
            }
		});

		if(true == has_error) {
			var first_required = $(".survey_box .required").get(0);
			var prev_node = $(first_required).prev('.topic');
			var prev_node_id = "";
			if(prev_node.length == 0) {
				prev_node_id = $(first_required).attr("id");
			} else {
				prev_node_id = $(prev_node).attr("id");
			}
			window.location.hash = prev_node_id;
			return false;
		}
		var is_feedback = $("#is_feedback").val();
		if(is_feedback == "1") {
			return false;
		}
		$("#is_feedback").val(1);
		return true;
    }

    /*
    * 成功之后的处理方法
    */
    function successFunc(rt){

		var notice_info = "";
		var redirect_url = "<?php echo Yii::app()->homeUrl; ?>";
    	if('success' == rt.flag){
        	// 成功了。
    		notice_info = "<?php echo Yii::t("survey", 'We have successfully received your survey submission, thank you!'); ?>";
    	}else if('not_login' == rt.flag){
    		// 请登陆！
    		notice_info = "<?php echo Yii::t("user", 'Please Login'); ?>";
    		redirect_url = "<?php echo Yii::app()->createUrl('user/login'); ?>";
    	}else if('already_fb' == rt.flag){
    		// 你已经回复过了
    		notice_info = "<?php echo Yii::t("survey", 'Already Feedback'); ?>";
    	}else{
    		// 操作失败，请重试！
    		notice_info = "<?php echo Yii::t("survey", 'Failed,Please Try Again'); ?>";
    	}
    	$("#mydialog").html("<div style='text-align:center'><p>"+notice_info+"</p><p><img src='<?php echo Yii::app()->theme->baseUrl; ?>/images/loading.gif'></p></div>");
    	$("#mydialog").dialog("open");
    	setTimeout('location.href = "'+redirect_url+'";',1200);
    	
    }

    
</script>
