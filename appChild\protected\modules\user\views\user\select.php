<h1><label><?php echo Yii::t("user", "Children"); ?></label></h1>
<p>
	<?php echo Yii::t("global", "Select a child");?>
</p>
<ul>
	<?php
	$i = 1;
	foreach($children as $child):
		$class = "span-6";
		$class .= ($i % 2 == 0) ? " last" : "";
		$childName = $child->getChildName();
		$flag = CommonUtils::isGradeSchool($child->classid, true);
		if ($flag === true){
			$casaUrl = Yii::app()->params['gradeUrl']."/{$child->childid}";
		}elseif($child->schoolid == 'BJ_DS' && $flag === false){
			$casaUrl = Yii::app()->params['casaUrl']."/{$child->childid}";
		}else{
			$casaUrl = $this->createUrl("//child/profile/welcome", array("childid"=>$child->childid));
		}
		echo CHtml::openTag("li", array("class"=>$class));
		echo CHtml::openTag("a", array(
				"href"=>$casaUrl,
				"target"=>"_blank",
				"class"=>"face",
				"title"=>$childName,
		));

		echo CHtml::image(CommonUtils::childPhotoUrl($child->photo), $childName );
		echo CHtml::openTag("p");
		echo CHtml::openTag("span",array("class"=>"childname"));
		echo $childName;
		echo CHtml::closeTag("span",array("class"=>"childname"));
		echo CHtml::closeTag("p");
		echo CHtml::closeTag("a");
		echo CHtml::closeTag("li");
		$i++;
	endforeach;?>
</ul>