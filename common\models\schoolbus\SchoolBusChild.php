<?php

/**
 * This is the model class for table "ivy_schoolbus_childinfo".
 *
 * The followings are the available columns in table 'ivy_schoolbus_childinfo':
 * @property integer $id
 * @property integer $childid
 * @property integer $busid
 * @property integer $startyear
 * @property string $time1
 * @property string $time2
 * @property string $schoolid
 * @property integer $userid
 * @property integer $update_timestamp
 * @property string $remark
 */
class SchoolBusChild extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return SchoolBusChild the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_schoolbus_childinfo';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('schoolid, userid,startyear,busid', 'required'),
			array('childid, busid, startyear, userid, update_timestamp', 'numerical', 'integerOnly'=>true),
			array('time1, time2,addr1,addr2', 'length', 'max'=>255),
			array('schoolid', 'length', 'max'=>10),
			array('remark', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, childid, busid, startyear, time1, time2, schoolid, userid, update_timestamp, remark', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'childinfo' => array(self::HAS_ONE, 'ChildProfileBasic', array('childid'=>'childid')),
			'homeaddress' => array(self::HAS_ONE, 'HomeAddress', array('childid'=>'childid'), 'select'=>'en_address'),
            'childmisc' => array(self::HAS_ONE, 'ChildMisc', array('childid'=>'childid')),
            'businfo' => array(self::BELONGS_TO, 'SchoolBus', 'busid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'childid' => Yii::t("navs", 'Child Search'),
			'busid' => Yii::t("labels", 'Bus NO.'),
			'startyear' => Yii::t("campus", 'School Year'),
			'time1' => Yii::t("labels", 'Pick-up Time'),
			'time2' => Yii::t('labels', 'Drop-off Time'),
			'schoolid' => 'Schoolid',
			'userid' => 'Userid',
			'update_timestamp' => 'Update Timestamp',
			'remark' => Yii::t("invoice", 'Memo'),
            'addr1' => Yii::t("labels", '接站地址'),
            'addr2' => Yii::t("labels", '抵达地址'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('busid',$this->busid);
		$criteria->compare('startyear',$this->startyear);
		$criteria->compare('time1',$this->time1,true);
		$criteria->compare('time2',$this->time2,true);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('userid',$this->userid);
		$criteria->compare('update_timestamp',$this->update_timestamp);
		$criteria->compare('remark',$this->remark,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

}