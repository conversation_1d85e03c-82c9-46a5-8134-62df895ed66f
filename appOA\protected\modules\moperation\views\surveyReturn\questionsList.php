<style>
[v-cloak] {
		display: none;
	}
table {
        /* margin:auto; */
        border: none;
        /* margin-top:-8px; */
    }
    table  tbody{
        margin-left:20px
    }
    table  tbody tr:before{
        border: none;
        padding-left:20px
    }
    table tbody tr td {
        border: none;
        padding:0 10px;
        padding-left:50px;
        vertical-align: top;
        border-left:1px solid #ccc;
        
    }
    table tr th {
        border: none;
        padding:10px 10px;
        vertical-align: top;

    }
    .table > thead > tr > th, .table > tbody > tr > th, .table > tfoot > tr > th, .table > thead > tr > td, .table > tbody > tr > td, .table > tfoot > tr > td{
        border: none;
    }
    .options{
        margin-left:30px;
        /* margin-top:10px; */
        /* margin:auto; */
    }
    .options table{
        margin-left:20px
    }
    .options tbody tr th{
        position: relative;
        min-width: 100px;
    }
    .options tbody tr th .option{
        margin-left:50px;
        margin-top:10px
    }
    .options tbody tr th .line{
        display: inline-block;
        border-bottom: 1px solid #ccc;
        width: 50px;
        margin-bottom: 5px;
        margin-right: 10px;
        position: absolute;
        left: -40px;
        top: 17px;
    }
    .borderBto{
        display:inline-block;
        border-bottom:1px solid #ccc;
        width:30px;
        margin-bottom:5px;
        margin-right:10px;
    }
    .borderHead{
        display:inline-block;
        border-bottom:1px solid #ccc;
        width:40px;
        margin-bottom:5px;
        margin-right:10px;
        position: absolute;
        left: 10px;
        top: 18px;
    }
    .options thead tr td table{
        margin-left:-15px
    }


    *{
 box-sizing: border-box;
 margin: 0;padding: 0;
}
*:before,*:after{
 box-sizing: border-box;
}
ul,
li {
 list-style: none;
}
.current{
 color: #e9c309 !important
}
.l_tree_container {
 box-shadow: 0 0 3px #ccc;
 margin: 13px;
 position: relative;
}

.l_tree {
 padding-left: 15px;
}
.l_tree_branch {
 /* display:inline-block; */
 padding: 13px;
 position: relative;
}

.l_tree_branch .l_tree_children_btn {
 width: 12px;
 height: 12px;
 background-color: #515a68;
 font-size: 8px;
 text-align: center;
 color: #bbbec1;
 outline: none;
 border: 0;
 cursor: pointer;
 border: 1px solid #bbbec1;
 line-height: 11px;
 margin-left: 5px;
}

ul.l_tree:before {
 content: '';
 border-left: 1px dashed #999999;
 height: calc(100% - 24px);
 position: absolute;
 left:7px;
 top:2px;
}
.l_tree,
.l_tree_branch {
 position: relative;
}

.l_tree_branch::after {
 content: '';
 width: 18px;
 height: 0;
 border-bottom: 1px dashed #bbbec1;
 position: absolute;
 right: calc(100% - 10px);
 top: 24px;
 left: -5px;
}

.l_tree_container>.l_tree::before,
.l_tree_container>.l_tree>.l_tree_branch::after {
 display: none;
}
.l_folder {
 font-size:11px;
 margin-left: 5px;
 display: inline;
 color: #bbbec1;
 cursor: pointer;
}

.bold {
  font-weight: bold;
}
.l_tree{
  padding-left: 1em;
  line-height: 1.5em;
  list-style-type: dot;
   /* font-family: Menlo, Consolas, monospace; */
   color: #444;
}
.contentLine{
    display:inline-block;
    border-bottom:1px solid #ccc;
    width:20px;
    margin-bottom:5px;
    margin-right:10px;
    position: absolute;
    left: -10px;
    top: 18px;
}
input[type="radio"] {
    margin-left: -20px;
}
.red{
    color:#0e80f5
}
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','HQ Operations'), array('/moperation/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','调查列表'), array('/moperation/surveyReturn/template'))?></li>
        <li><?php echo Yii::t('site','调查列表'); ?></li>
    </ol>
    <div id='dataList' v-cloak>
        <button type="button" class="btn btn-primary" v-if='treeData.length==0' @click='addques()'>添加问题</button>
        <template v-else>
            <!-- <template v-for='items in treeData'>
               <span>{{items.titleCn}}</span> -->
                <tree-item
                    class="item"
                    :item="treeData"
                    v-if="DestroyIncomeStatistics == true"
                ></tree-item>
            <!-- </template> -->
        </template>
        <div class="modal fade bs-example-modal-lg" id="company" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true"  data-backdrop="static">
            <div class="modal-dialog modal-lg">
                <?php $form = $this->beginWidget('CActiveForm', array(
                        'id' => 'expense-forms',
                        'enableAjaxValidation' => false,
                        'action' => ($model->id) ? $this->createUrl('expenesAdd', array('expenesId' => $model->id)) : $this->createUrl('updateQuetion'),
                        'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form', 'enctype' => "multipart/form-data"),
                    )); ?>
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                        <h4 class="modal-title" id="myModalLabel">编辑信息</h4>
                    </div>
                    <div class="modal-body">
                        <div class='row mb20'>
                            <div class='col-sm-2'>
                                <select class="form-control"  v-model="optionType">
                                    <option value='select'>请选择</option>
                                    <option value='question'>问题</option>
                                    <option value='content'>提示文本</option>
                                </select>
                            </div>
                        </div>
                        <form class="form-horizontal" v-if='optionType!="select"'>
                            <input type="text" :value='optionType' name='type' class="hidden">
                            <input type="text" :value='survey_id' name='survey_id'  class="hidden">
                            <input type="text" :value='questionDataId' name='id' v-if='isAdd==1' class="hidden">
                            <input type="text" :value='questionDataPid' name='pid' class="hidden" v-if='isAdd==1'>
                            <input type="text" :value='questionDataId' name='pid' class="hidden" v-if='isAdd==2'>
                            <input type="text" :value='delOptions'   v-if='delOptions.length==0' class="hidden">
                            <input type="text" :value='delOptions' name='option_ids'  v-else class="hidden">
                            <input type="text" value="0"  name='SurveyReturnQuestions[is_required]'  class="hidden">
                            <input type="text" value="0"  name='SurveyReturnQuestions[is_multiple]'  class="hidden">
                            <input type="text" value="0"  name='SurveyReturnQuestions[is_memo]'  class="hidden">
                            <input type="text" value="0"  name='SurveyReturnQuestions[is_signature]'  class="hidden">

                            <template v-if='optionType!="select"'>
                                <div v-if='optionType=="content"'>
                                    <div class="form-group mt15">
                                        <label for="inputPassword" class="col-sm-2 control-label">提示文字中文</label>
                                        <div class="col-sm-10">
                                            <textarea class="form-control" placeholder="请输入提示文字中文"   :value='questionData.titleCn' rows='4' name='SurveyReturnQuestions[cn_title]' ></textarea>
                                        </div>
                                    </div>
                                    <div class="form-group mt15">
                                        <label for="inputPassword" class="col-sm-2 control-label">提示文字英文</label>
                                        <div class="col-sm-10">
                                            <textarea class="form-control" placeholder="请输入提示文字英文"  :value='questionData.titleEn'  rows='4'  name='SurveyReturnQuestions[en_title]'></textarea>
                                        </div>
                                    </div>
                                    <div class="form-group mt15">
                                        <label for="inputPassword" class="col-sm-2 control-label">跳转到问卷</label>
                                        <div class="col-sm-10">
                                            <input class="form-control" placeholder="请输入跳转问卷的ID" :value='questionData.to_survey' name='SurveyReturnQuestions[to_survey]'>
                                        </div>
                                    </div>
                                </div>
                                <div v-else>
                                    <div  v-if="isRouterAlive">  
                                        <h4 class='pt10'>题目</h4>
                                        <div class="form-group">
                                            <label for="cn_title" class="col-sm-2 control-label">中文题目：</label>
                                            <div class="col-sm-10">
                                                <input type="text" class="form-control" :value='questionData.titleCn' v-model='questionData.titleCn'  placeholder="请输入中文题目" id="cn_title"  name='SurveyReturnQuestions[cn_title]'>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="en_title" class="col-sm-2 control-label">英文题目：</label>
                                            <div class="col-sm-10">
                                                <input type="text" class="form-control" :value='questionData.titleEn' v-model='questionData.titleEn'  placeholder="请输入英文题目"  id="en_title" name='SurveyReturnQuestions[en_title]'>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-sm-2 control-label"></label>
                                            <div class="col-sm-10">
                                                <label class="checkbox-inline col-sm-3">
                                                    <input type="checkbox" value="1" :checked="questionData.is_required==1" name='SurveyReturnQuestions[is_required]'> 是否必选
                                                </label>
                                                <label class="checkbox-inline  col-sm-3">
                                                    <input type="checkbox" value="1" :checked="questionData.is_multiple==1"  name='SurveyReturnQuestions[is_multiple]'>是否多选
                                                </label>
                                                <label class="checkbox-inline col-sm-2">
                                                    <input type="checkbox" value="1" @change='isSign($event)' :checked="questionData.is_signature==1"  name='SurveyReturnQuestions[is_signature]'> 是否有签名
                                                </label>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-sm-2 control-label"></label>
                                            <div class="col-sm-10">
                                                <label class="checkbox-inline col-sm-3">
                                                    <input type="radio" value="0" :checked="questionData.is_memo==0"  name='SurveyReturnQuestions[is_memo]'> 无备注
                                                </label>
                                                <label class="checkbox-inline col-sm-3">
                                                    <input type="radio" value="1" :checked="questionData.is_memo==1"  name='SurveyReturnQuestions[is_memo]'> 有备注(选填)
                                                </label>
                                                <label class="checkbox-inline col-sm-3">
                                                    <input type="radio" value="2" :checked="questionData.is_memo==2"  name='SurveyReturnQuestions[is_memo]'> 有备注(必填)
                                                </label>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="en_title" class="col-sm-2 control-label">图片链接中文：</label>
                                            <div class="col-sm-10">
                                                <textarea class="form-control" rows="3" :value='questionData.content_ext' v-model='questionData.content_ext'  name='SurveyReturnQuestions[content_ext]'></textarea>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="en_title" class="col-sm-2 control-label">图片链接英文：</label>
                                            <div class="col-sm-10">
                                                <textarea class="form-control" rows="3" :value='questionData.content_ext_en' v-model='questionData.content_ext_en'  name='SurveyReturnQuestions[content_ext_en]'></textarea>
                                            </div>
                                        </div>
                                        <h4 class='pt10'>选项</h4>
                                        <template v-if='optionsItems'>
                                            <div v-for="(val,index) in optionsItems" :key="index">
                                                <div class='col-sm-12'>
                                                <div  class="col-sm-10">
                                                    <div class='form-group'>
                                                        <label for="cn_title" class="col-sm-2 control-label">中文选项{{index+1}}：</label>
                                                        <div class="col-sm-10">
                                                            <input type="text" class="form-control"  :name="'option[' + index+'][cn_title]'" :value='val.titleCn'  v-model="val.titleCn">
                                                        </div>
                                                    </div>
                                                    <div class='form-group'>
                                                        <label for="cn_title" class="col-sm-2 control-label">英文选项{{index+1}}：</label>
                                                        <div class="col-sm-10">
                                                            <input type="text" class="form-control" :name="'option[' + index+'][en_title]'" :value='val.titleEn' v-model="val.titleEn">
                                                        </div>
                                                        <input type="text" :value='val.id' :name="'option[' + index+'][id]'" class="hidden">
                                                    </div>
                                                </div>
                                                <div class="col-sm-2">
                                                    <a href="javascript:;" class="btn btn-xs btn-primary" draggable="true" @dragstart="handleDragStart($event, val)" @dragover.prevent="handleDragOver($event, val)" @dragenter="handleDragEnter($event, val)" @dragend="handleDragEnd($event, val)" title="可拖动"><span class="glyphicon glyphicon-move"></span></a>
                                                    <a href="javascript:;" class="btn btn-xs btn-danger"  @click='delOptionLists(val,index)'><span class="glyphicon glyphicon-trash"></span></a>
                                                </div>
                                                </div>
                                                <div style='clear:both'></div>
                                                <div class="form-group">
                                                    <label for="en_title" class="col-sm-2 control-label"></label>
                                                    <div class="col-sm-10">
                                                    <input type="text" value="" :name="'option[' + index+'][is_invoice]'"  class="hidden">

                                                        <label class="checkbox-inline col-sm-3">
                                                            <template v-if='val.is_invoice!=""'>
                                                                <input type="checkbox" checked="checked" v-model='val.is_invoice'> 是否生成定位金
                                                            </template>
                                                            <template v-else>
                                                                <input type="checkbox"  v-model="val.is_invoice" true-value="all" false-value=""> 是否生成定位金
                                                            </template>
                                                        </label>
                                                        <div class='col-sm-3'  v-if='val.is_invoice!=""'>
                                                            <select class="form-control"  v-model="val.is_invoice"  :name="'option[' + index+'][is_invoice]'">
                                                            <option :value='school.branchid'  v-for='school in branchInfo'>{{school.title}}</option>
                                                            </select>
                                                        </div>
                                                        <div class="text-warning col-sm-12 row mt15"  v-if='val.is_invoice!=""'>生成定位金需要设置所选择学校的下学年校历，并配置好金额</div>

                                                    </div>
                                                </div>
                                            </div>
                                        </template>
                                        <template v-else>
                                            <div v-for='(list,index) in option'>
                                            <div class="col-sm-12">
                                                    <div class='col-sm-11'>
                                                        <div class="form-group"  v-for="(val,key) in list.label" :key="key">
                                                            <label for="cn_title" class="col-sm-2 control-label">{{val.label}}{{index+1}}：</label>
                                                            <div class="col-sm-10">
                                                                <input type="text" class="form-control" :name="'option[' + index +']['+ val.name+']'"  :placeholder="val.label" >
                                                                <input type="text" value='' :name="'option[' + index+'][id]'" class="hidden">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-sm-1">
                                                        <a href="javascript:;" class="btn btn-xs btn-danger"  @click='delOptionNoVal(list,index)'><span class="glyphicon glyphicon-trash"></span></a>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                <input type="text" value="" :name="'option[' + index+'][is_invoice]'"  class="hidden">

                                                    <label for="en_title" class="col-sm-2 control-label"></label>
                                                    <div class="col-sm-10">
                                                        <label class="checkbox-inline col-sm-3">
                                                            <input type="checkbox" v-model="list.check" true-value="all" false-value="">是否生成定位金
                                                        </label>
                                                        <div class='col-sm-3' v-if='list.check!=""'>
                                                            <select class="form-control"  :name="'option[' + index+'][is_invoice]'" >
                                                                <option :value='school.branchid' v-for='school in branchInfo'>{{school.title}}</option>
                                                            </select>
                                                        </div>
                                                        <div class="text-warning col-sm-12 row mt15"  v-if='list.check!=""'>生成定位金需要设置所选择学校的下学年校历，并配置好金额</div>
                                                    </div>
                                                </div>
                                            </div>
                                            </div>
                                        </template>
                                        <div class="form-group">
                                            <label for="en_title" class="col-sm-2 control-label"></label>
                                            <div class="col-sm-10">
                                            <button type="button" class="btn btn-primary" @click='addoption'>
                                                <span class="glyphicon glyphicon-plus" aria-hidden="true"></span> 添加选项
                                            </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary J_ajax_submit_btn">确定</button>
                        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    </div>
                </div>
                <!-- /.modal-content -->
                <?php $this->endWidget(); ?>
            </div>
            <!-- /.modal -->
        </div>
        <div class="modal fade bs-example-modal-lg" id="jumpModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true"  data-backdrop="static">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                        <h4 class="modal-title" id="myModalLabel">跳转</h4>
                    </div>
                    <div class="modal-body">
                    <select class="form-control" v-model='jumpId'>
                        <option value=''>请选择</option>
                        <option v-for='(list,index) in jumpList' :value='list.id'>{{list.id}} {{lang=='zh_cn'?list.titleCn:list.titleEn}}</option>
                    </select>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary " @click='jumpQuestion()'>确定</button>
                        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    </div>
                </div>
            </div>
            <!-- /.modal -->
        </div>
    </div>
</div>
<script type="text/x-template" id="item-template">
    <table >
    <tr>
        <tbody class='options mb20' v-for="(child, index) in item" :key="index">

            <tr v-if='child.type=="question"' > 
                <span class="label " :class='child.id==HighlightId?"label-warning":"label-default"' :id="'o_' + child.id">{{child.id}}</span> {{lang=='zh_cn'?child.titleCn:child.titleEn}}
                <p class='mt10 font12 red'><span class="label label-primary"   :id='child.jump_id' onclick='jumpHighlight(this)'>{{child.jump_id}}</span></p>
                <button type="button" class="btn btn-default btn-xs" @click='addQuestions(child)' >
                    <span class='glyphicon glyphicon-edit'></span>
                </button>
                <button type="button" class="btn btn-default btn-xs ml5"  @click='deleteList(child)'>
                    <span class="glyphicon glyphicon-trash" aria-hidden="true"></span>
                </button>
                <button type="button" class="btn btn-default btn-xs ml5"  @click='jump(child)'>
                    <span class="glyphicon glyphicon-share" aria-hidden="true"></span>
                </button>
                <template v-if='child.children1' >
                    <button type="button" class="btn btn-default btn-xs  ml10"  disabled="disabled" >
                        <span class="glyphicon glyphicon-plus" aria-hidden="true"></span>
                    </button>
                </template>
                <template v-else>
                    <button type="button" class="btn btn-default btn-xs  ml10"  @click='addOption(child)' >
                        <span class="glyphicon glyphicon-plus" aria-hidden="true"></span>
                    </button>
                </template>
            </tr>
            <th v-if='child.type=="option"'>
                <span class="label " :class='child.id==HighlightId?"label-warning":"label-default"' :id="'o_' + child.id">{{child.id}}</span> {{lang=='zh_cn'?child.titleCn:child.titleEn}}
                <p class='mt10 font12 red'><span class="label label-primary"  :id='child.jump_id' onclick='jumpHighlight(this)'>{{child.jump_id}}</span></p>
                <button type="button" class="btn btn-default btn-xs"  @click='jump(child)'>
                    <span class="glyphicon glyphicon-share" aria-hidden="true"></span>
                </button>
                <template v-if='child.children1'>
                    <button type="button" class="btn btn-default btn-xs"  disabled="disabled" >
                        <span class="glyphicon glyphicon-plus" aria-hidden="true"></span>
                    </button>
                </template>
                <template v-else>
                    <button type="button" class="btn btn-default btn-xs"  @click='addOption(child)'>
                        <span class="glyphicon glyphicon-plus" aria-hidden="true"></span>
                    </button>
                </template>
            </th>
            <th  v-if='child.children1'>
                <tree-item   :item="child.children" :item1="child.children1"></tree-item>  
            </th> 
            <tr  v-else>
                <tree-item   :item="child.children" :item1="child.children1"></tree-item>  
            </tr>    
        </tbody>
        <th v-for="(child, index) in item1" :key="index"  class='options mb20'>
            <td v-if='child.type=="question"' > 
                <span class='borderHead'></span>
                <span class="label " :class='child.id==HighlightId?"label-warning":"label-default"' :id="'o_' + child.id">{{child.id}}</span> {{lang=='zh_cn'?child.titleCn:child.titleEn}}  
                <p class='mt10 font12 red'><span class="label label-primary" v-if='child.jump_id!=null && child.jump_id!="" && child.jump_id!="0"'  :id='child.jump_id' onclick='jumpHighlight(this)'>{{child.jump_id}} </span></p>
                <button type="button" class="btn btn-default btn-xs" @click='addQuestions(child)' >
                    <span class='glyphicon glyphicon-edit'></span>
                </button>
                <button type="button" class="btn btn-default btn-xs"  @click='deleteList(child)'>
                    <span class="glyphicon glyphicon-trash" aria-hidden="true"></span>
                </button>
                <button type="button" class="btn btn-default btn-xs"  @click='jump(child)'>
                    <span class="glyphicon glyphicon-share" aria-hidden="true"></span>
                </button>
                <template v-if='child.children1'>
                    <button type="button" class="btn btn-default btn-xs  ml5"  disabled="disabled" >
                        <span class="glyphicon glyphicon-plus" aria-hidden="true"></span>
                    </button>
                </template>
                <template v-else>
                    <button type="button" class="btn btn-default btn-xs  ml5"  @click='addOption(child)' >
                        <span class="glyphicon glyphicon-plus" aria-hidden="true"></span>
                    </button>
                </template>
                <tr>
                    <tree-item :item="child.children" :item1="child.children1"></tree-item>
                </tr>
            </td>
            <td v-if='child.type=="content"'>
                <span class='borderHead'></span>
                <span class="label " :class='child.id==HighlightId?"label-warning":"label-default"' :id="'o_' + child.id">{{child.id}}</span> {{lang=='zh_cn'?child.titleCn:child.titleEn}} 
                <p class='mt10 font12 red'><span class="label label-primary" v-if='child.jump_id!=null && child.jump_id!="" && child.jump_id!="0"' :id='child.jump_id' onclick='jumpHighlight(this)'>{{child.jump_id}} </span></p>
                <button type="button" class="btn btn-default btn-xs" @click='addQuestions(child)' >
                    <span class='glyphicon glyphicon-edit'></span>
                </button>
                <button type="button" class="btn btn-default btn-xs"  @click='deleteList(child)'>
                    <span class="glyphicon glyphicon-trash" aria-hidden="true"></span>
                </button>
            </td> 
        </th>
    </tr>
    </table>
</script>
<script>
   var items = <?php echo json_encode($question) ?>;
    var itemList = <?php echo json_encode($question) ?>;
    var survey_id = <?php echo json_encode($survey_id) ?>;
    var spareitems=<?php echo json_encode($question) ?>;
    var branchInfo=<?php echo json_encode($branchInfo) ?>;
    var lang='<?php echo Yii::app()->language;?>'
    var groups={}
    function jumpHighlight(obj){
        let id=$(obj).attr('id')
        $('#o_'+id).addClass('label-warning')
        setTimeout (function(){
            $('#o_'+id).removeClass('label-warning')
        },1000)
    }
    function isdata(pid,item){
        if(itemList.length==0){return []}
        for(var i=0;i<itemList.length;i++){
            if(groups[itemList[i].pid]){
                if(itemList[i].type=='question'){
                    groups[itemList[i].pid].push(itemList[i]);
                }
                else{
                    groups[itemList[i].pid].push(itemList[i]);
                }
            }
            else{
                groups[itemList[i].pid]=[];
                groups[itemList[i].pid].push(itemList[i]);
            }
        }
    }
    var sparegroups={}
    function spareData(pid){
        if(spareitems.length==0){return []}
        for(var i=0;i<spareitems.length;i++){
            if(sparegroups[spareitems[i].pid]){
                if(spareitems[i].type=='option'){
                    sparegroups[spareitems[i].pid].push(spareitems[i]);
                }
            }else{
                sparegroups[spareitems[i].pid]=[];
                sparegroups[spareitems[i].pid].push(spareitems[i]);
            }
        }
    }
    function toTree(data) {
        let treeData = [];
        if (!Array.isArray(data)) return treeData;
        let map = {};
        data.forEach(item => {
            map[item.id] = item;
        });
        data.forEach(item => {
            let parent = map[item.pid];  //判断item的pid是否是否存在map中
            if (parent) {  //如果存在则表示item不是最顶层的数据
                if(item.type=='option'){
                    (parent.children || (parent.children = [])).push(item)
                } else {
                     (parent.children1 || (parent.children1 = [])).push(item)
                }
            }
            else {
            treeData.push(item)  // 如果不存在 则是顶层数据
            }
        });
        return treeData;
    }
    var jumpList=[]
    for(var i=0;i<itemList.length;i++){
        if(itemList[i].type=='question' || itemList[i].type=='content'){
            jumpList.push(itemList[i])
        }
    }
    spareData(0)
    isdata(0)
    var options={
        label:[{label:'中文选项',name:'cn_title'},
        {label:'英文选项',name:'en_title'},],
        check:''
    }
    Vue.component('tree-item', {
        template: '#item-template',
        props: {
            item: [Object, Array],
            item1: [Object, Array],
        },
        data: function () {
            return {
                isOpen: false,
                delIndex:'',
                option:options,
                optionLists:'',
                groups:groups,
                itemsList:items,
                sparegroups:sparegroups,
                HighlightId:'',
                lang:lang
                
            }
        },
        created:function(){
        },
        computed: {
        },
        methods: {
            addQuestions(data){
                let list=data
                dataList.questionData=list
                let optionList=[]
                if(this.sparegroups[list.id]){
                    for(var i=0;i<this.sparegroups[list.id].length;i++){
                        if(this.sparegroups[list.id][i].type=="option"){
                            optionList.push(this.sparegroups[list.id][i])
                        }
                    }
                }
                dataList.optionsItems=optionList
                dataList.questionDataId=data.id
                dataList.questionDataPid=data.pid
                dataList.isAdd=1
                dataList.optionType=list.type
                dataList.isRouterAlive = false
                dataList.$nextTick(() => (dataList.isRouterAlive = true))
                $('#company').modal('show')
            },
            addOption(data){
                dataList.questionData={}
                dataList.optionsItems=""
                dataList.questionDataId=data.id
                dataList.questionDataPid=data.pid
                dataList.isAdd=2
                dataList.optionType='select'
                $('#company').modal('show')
            },
            deleteList(data){
                let index=data.id
                var pid=data.pid
                var that=this
                $.ajax({
                    url: "<?php echo $this->createUrl('deleteTopic')?>",
                    type: 'post',
                    dataType: 'json',
                    data:{
                        id:data.id,
                    },
                    success:function(data){
                        if(data.state == 'success') {
                            resultTip({
                                "msg": data.message
                            })
                            setTimeout(function(){
                                window.location.reload();
                            },1000);
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error:function(data) {
                        alert('请求错误')
                    }
                })
            },
            jump(data){
                if(data.jump_id!=null && data.jump_id!=''){
                    dataList.jumpId=data.jump_id
                }else{
                    dataList.jumpId=""
                }
                dataList.jumpData=data
                
                $('#jumpModal').modal('show')
            }
        }
    })

    var dataList = new Vue({
        el: '#dataList',
        data: {
            treeData:'',
            groups:groups,
            option:[options],
            questionData:{},
            delOptions:[],
            optionType:'',
            isAdd:'',
            items:items,
            survey_id:survey_id,
            popStatus:true,
            optionsItems:"",
            questionDataId:'',
            questionDataPid:'',
            branchInfo:branchInfo,
            isRouterAlive: true,
            jumpList:jumpList,
            jumpId:'',
            jumpData:{},
            DestroyIncomeStatistics:true,
            lang:lang
        },
        created:function(){
            if(JSON.stringify(groups) != "{}"){
                this.treeData=toTree(items)
            }
        },
        methods: {
            component1DataChange(params) {
                this.treeData = params;
                this.DestroyIncomeStatistics=false
                this.$nextTick(() => {
                    this.DestroyIncomeStatistics = true;
                });
            },
            edit(data){
                // console.log(data)
            },
            addques(){
                this.isAdd=2
                this.optionType='select'
               
                $('#company').modal('show')
            },
            isSign(e){
                if(e.target.checked){
                    if(!this.optionsItems){
                        this.option=[]
                    }
                }else{
                    if(!this.optionsItems){
                    var list={
                        label:[{label:'中文选项',name:'cn_title'},
                        {label:'英文选项',name:'en_title'},],
                        check:''
                    }
                    this.option.push(list); 
                    }
                }
            },
            addoption(){
                // console.log(this.optionsItems)
                if(this.optionsItems){
                    let addoption={
                        titleEn: "",
                        titleCn: "",
                        is_invoice: "",
                    }
                    this.optionsItems.push(addoption)
                    // console.log(this.optionsItems)
                }else{
                    var list={
                            label:[{label:'中文选项',name:'cn_title'},
                            {label:'英文选项',name:'en_title'},],
                            check:''
                        }
                    this.option.push(list);
                    // console.log( this.option)
                }
            },
            delOptionLists(data,index){
                // console.log(data)
                // console.log(index)
                if(data.id){
                    this.delOptions.push(data.id)
                }
                this.optionsItems.splice(index,1);
            },
            delOptionNoVal(data,index){
                this.option.splice(index,1);
            },
            handleDragStart(e, item) {
                this.dragging = item;
            },
            handleDragEnd(e, item) {
                this.dragging = null
            },
            handleDragOver(e) {
                e.dataTransfer.dropEffect = 'move' // e.dataTransfer.dropEffect="move";//在dragenter中针对放置目标来设置!
            },
            handleDragEnter(e, item) {
                e.dataTransfer.effectAllowed = "move" //为需要移动的元素设置dragstart事件
                if(item === this.dragging) {
                    return
                }
                const newItems = [...this.optionsItems]
                const src = newItems.indexOf(this.dragging)
                const dst = newItems.indexOf(item)
                newItems.splice(dst, 0, ...newItems.splice(src, 1))
                this.optionsItems = newItems
            },
            jumpQuestion(){
                $.ajax({
                    url: "<?php echo $this->createUrl('saveJump')?>",
                    type: 'post',
                    dataType: 'json',
                    data:{
                        id:this.jumpData.id,
                        jumpId:this.jumpId
                    },
                    success:function(data){
                        if(data.state == 'success') {
                            resultTip({
                                "msg": data.message
                            })
                            setTimeout(function(){
                                window.location.reload();
                            },1000);
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error:function(data) {
                        alert('请求错误')
                    }
                })
            }
        }
    })
</script>
