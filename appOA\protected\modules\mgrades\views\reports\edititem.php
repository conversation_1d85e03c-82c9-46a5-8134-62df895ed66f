<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo Yii::t('site','Campus Workspace');?></li>
        <li><?php echo Yii::t('site','Reports Templates');?></li>
        <li class="active"><?php echo $model->getTitle();?></li>
    </ol>

    <div class="row">
        <?php if($items):?>
        <div class="col-md-2">
            <ul class="nav nav-pills nav-stacked">
                <?php foreach($items as $item):?>
                <li <?php if($cid==$item->id){echo 'class="active"';}?>>
                    <?php echo CHtml::link($programs[$item->program], array('edititem', 'id'=>$model->id, 'cid'=>$item->id))?></li>
                <?php endforeach;?>
            </ul>
        </div>
        <div class="col-md-10">
            <?php if($subs):?>
            <div class="panel panel-info">
                <div class="panel-heading"><?php echo Yii::t('report','Please selecte options for this category');?></div>
                <div class="panel-body">
                    <?php echo CHtml::dropDownList('group',$items[$cid]->option_groupid,
                        CHtml::listData($groups, 'id', CommonUtils::autoLang('title_cn', 'title_en')),
                        array('class'=>'form-control select_4', 'empty'=>Yii::t('global', 'Please Select'), 'onchange'=>"showOption(this,{$cid})"));?>
                        <span class="label" id="label-group-<?php echo $cid;?>"></span>
                </div>
            </div>

            <?php foreach($subs as $sub):?>
            
            <div class="panel panel-default">
                <div class="panel-heading">
                    <?php echo CHtml::encode($sub->getTitle());?>
                    <button type="button" class="pull-right btn btn-xs btn-primary" onclick="addItem(<?php echo $sub->id;?>);">
                        <span class="glyphicon glyphicon-plus"></span></button>
                </div>
                <div class="panel panel-info" style="margin:5px;">
                    <div class="panel-heading"><?php echo Yii::t('report','Please selecte options for this category');?></div>
                    <div class="panel-body">
                        <?php echo CHtml::dropDownList('group', $sub->option_groupid,
                            CHtml::listData($groups, 'id', CommonUtils::autoLang('title_cn', 'title_en')),
                            array('class'=>'form-control select_4', 'empty'=>Yii::t('global', 'Please Select'), 'onchange'=>"showOption(this,{$sub->id})"));?>
                            <span class="label" id="label-group-<?php echo $sub->id;?>"></span>
                    </div>
                </div>
                <ul class="list-group">
                    <?php foreach($sub->items as $item):?>
                        <li class="list-group-item" data-id="<?php echo $item->id;?>" data-sid="<?php echo $sub->id;?>">
                            <div class="row">
                                <div class="col-md-2">
                                    <button type="button" class="btn btn-xs btn-primary" onclick="editItem(<?php echo $sub->id;?>, <?php echo $item->id;?>);"><span class="glyphicon glyphicon-pencil"></span></button>
                                    <a role="button" class="btn btn-xs btn-danger J_ajax_del" href="<?php echo $this->createUrl
                                        ('delItem', array('id'=>$item->id))?>"><span class="glyphicon glyphicon-minus"></span></a>
                                </div>
                                <div class="col-md-2">
                                    <p><?php echo CHtml::encode($item->standard_code);?></p>
                                </div>
                                <div class="col-md-7">
                                    <p><?php echo CHtml::encode($item->title_cn);?></p>
                                    <p><?php echo CHtml::encode($item->title_en);?></p>
                                </div>
                                <div class="col-md-1">
                                    <a role="button" class="btn btn-xs btn-success" style="cursor: move;"><span class="glyphicon
                            glyphicon-move sort-header"></span></a>
                                </div>
                            </div>

                        </li>
                    <?php endforeach;?>
                </ul>
            </div>
            <?php
            endforeach;
            endif;
            ?>
        </div>
        <?php else:?>
            <div class="col-md-12">
                <div class="alert alert-warning" role="alert">
                    <?php echo Yii::t('report','Please add report categories first.');?>
                </div>
            </div>
        <?php endif;?>
    </div>
</div>

<?php
$labels = ReportsItem::attributeLabels();
?>

<div class="modal" id="itemModal" tabindex="-1" role="dialog" aria-labelledby="categoryEditModalLabel">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title"><?php echo Yii::t('global','Edit');?> <small></small></h4>
            </div>
            <form class="form-horizontal J_ajaxForm"
                  action="<?php echo $this->createUrl('saveItem');?>"
                  method="POST">
                <div class="modal-body">
                    <div class="form-group" model-attribute="standard_code">
                        <?php echo CHtml::label($labels['standard_code'], CHtml::getIdByName('ReportsItem[standard_code]'), array('class'=>'col-sm-3 control-label'));?>
                        <div class="col-sm-9">
                            <?php echo CHtml::textField('ReportsItem[standard_code]', '', array('class'=>'form-control','encode'=>false));?>
                        </div>
                    </div>
                    <div class="form-group" model-attribute="title_cn">
                        <?php echo CHtml::label($labels['title_cn'], CHtml::getIdByName('ReportsItem[title_cn]'), array('class'=>'col-sm-3 control-label'));?>
                        <div class="col-sm-9">
                            <?php echo CHtml::textField('ReportsItem[title_cn]', '', array('class'=>'form-control','encode'=>false));?>
                        </div>
                    </div>
                    <div class="form-group" model-attribute="title_en">
                        <?php echo CHtml::label($labels['title_en'], CHtml::getIdByName('ReportsItem[title_en]'), array('class'=>'col-sm-3 control-label'));?>
                        <div class="col-sm-9">
                            <?php echo CHtml::textField('ReportsItem[title_en]', '', array('class'=>'form-control','encode'=>false));?>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <input type="hidden" name="id" id="id">
                    <input type="hidden" name="ReportsItem[category_id]" id="ReportsItem_category_id">
                    <input type="hidden" name="ReportsItem[template_id]" id="ReportsItem_template_id" value="<?php echo $model->id;?>">
                    <button type="button" class="btn btn-default pull-right" data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
                    <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="modal" id="addModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title"><?php echo Yii::t('report','Items Edit');?> <small></small></h4>
            </div>
            <form class="form-horizontal J_ajaxForm"
                  action="<?php echo $this->createUrl('saveItembat');?>"
                  method="POST">
                <div class="modal-body">
                    <?php for($i=0; $i<11; $i++):?>
                    <div class="form-group">
                        <div class="col-sm-4">
                            <?php echo CHtml::textField('standard_code[]', '', array('class'=>'form-control',
                                'encode'=>false, 'placeholder'=>$labels['standard_code']));?>
                        </div>
                        <div class="col-sm-4">
                            <?php echo CHtml::textField('title_cn[]', '', array('class'=>'form-control',
                                'encode'=>false, 'placeholder'=>$labels['title_cn']));?>
                        </div>
                        <div class="col-sm-4">
                            <?php echo CHtml::textField('title_en[]', '', array('class'=>'form-control',
                                'encode'=>false, 'placeholder'=>$labels['title_en']));?>
                        </div>
                    </div>
                    <?php endfor;?>
                </div>
                <div class="modal-footer">
                    <input type="hidden" name="category_id" id="ReportsItem_category_id">
                    <input type="hidden" name="template_id" id="ReportsItem_template_id" value="<?php echo $model->id;?>">
                    <button type="button" class="btn btn-default pull-right" data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
                    <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    function addItem(pid)
    {
        $('#addModal #ReportsItem_category_id').val(pid);
        $('#addModal').modal({backdrop: 'static', keyboard: false});
    }
    function editItem(pid, id)
    {
        if(id != 0){
            $.ajax({
                type: 'get',
                url: '<?php echo $this->createUrl('getItem')?>',
                data: {id: id},
                dataType: 'json',
                async: false
            }).done(function(data){
                $('#ReportsItem_title_en').val(data.title_en);
                $('#ReportsItem_title_cn').val(data.title_cn);
            });
        }
        else{
            $('#ReportsItem_title_en').val('');
            $('#ReportsItem_title_cn').val('');
        }
        $('#id').val(id);
        $('#itemModal #ReportsItem_category_id').val(pid);
        $('#itemModal').modal();
    }

    $( ".list-group" ).sortable({
        placeholder: "ui-state-highlight",
        handle: ".sort-header",
        axis: 'y',
        opacity: 0.8,
        start: function( event, ui ) {
            $('.ui-state-highlight').height($(ui['item'][0]).outerHeight());
        },
        update: function(event, ui){
            var sort = {};
            var cc = 1;
            $(ui['item'][0]).parents('.list-group').find('li').each(function(){
                sort[$(this).data('id')] = cc++;
            });
            $.post('<?php echo $this->createUrl('updateSort');?>', {id: $(ui['item'][0]).data('sid'), sort: sort}, function(data){

            });
        }
    }).disableSelection();

    function showOption(_this,cid)
    {
        var groupid = $(_this).val();
        $.post('<?php echo $this->createUrl('editOption')?>', {groupid: groupid, cid: cid}, function(data){
                if(data.state == 'success'){
                    $('#label-group-'+cid).addClass('label-success');
                    $('#label-group-'+cid).html('Success');
                    //$('div#item-option-'+data.data.cid+'-'+data.data.id).removeClass('text-muted').addClass('text-primary');
                }else{
                    $('#label-group-'+cid).addClass('label-warning');
                    $('#label-group-'+cid).html('Warning');
                }
        }, 'json');
    }
</script>

<style>
    .ui-state-highlight{
        background-color: #FCFAF1;
        list-style-type: none;
        border: 0px;
    }
</style>