<?php

class BulkmailController extends BranchBasedController
{
    //测试帐号地址
    public $testAccout = '<EMAIL>';


    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mcampus/bulkmail/index');
    }

    
    /**
     * 邮件群发
     */
    public function actionIndex()
    {

        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/clipboard.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/bootstrap-select.min.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl.'/base/css/bootstrap-select.min.css');

        $model = new BulkMailForm();

        $schoolid = $this->branchId;
        $criteria=new CDbCriteria;
        $criteria->compare('schoolid',$schoolid);
        $criteria->compare('stat',10);
        $class = IvyClass::model()->findAll($criteria);

        $criteria = new CDbCriteria;
        $criteria->compare('schoolid', $schoolid);
        $criteria->compare('stat', 0);
        $nextClass = IvyClass::model()->findAll($criteria);
        
        $classType = Yii::app()->request->getParam('showChild');
        // 展示当前学年班级学生
        if ($classType == 'current') {
            $classid = Yii::app()->request->getParam('classid','');
            if(!$classid){
                foreach ($class as $val){
                    $classid[] = $val->classid;
                }
            }

            $criteria=new CDbCriteria;
            $criteria->compare('schoolid',$schoolid);
            $criteria->compare('classid',$classid);
            $criteria->compare('status',array(10,20));
            $childern = ChildProfileBasic::model()->findAll($criteria);
            $childName = array();
            $email = array();
            foreach ($childern as $child) {
                $parent = $child->getParents();
                $str = '@';
                if (strpos($parent['father']['email'],$str)) {
                   $email[$child->childid] = $parent['father']['email'] . ';';
                }
                if (strpos($parent['mother']['email'],$str)) {
                   $email[$child->childid] .= $parent['mother']['email'];
                }
                $email[$child->childid] = trim($email[$child->childid] , ';');
                //去除重复
                $email = array_unique($email);
                $childName[$child->childid] = $child->getChildName();
            }
            echo CJSON::encode(array(
                'childName' => $childName,
                'email' => $email,
            ));
            return;
        }
        // 展示下半年班级学生
        if ($classType == 'next') {
            Yii::import('common.models.invoice.ChildReserve');
            $classid = Yii::app()->request->getParam('classid','');
            if(!$classid){
                foreach ($nextClass as $val){
                    $classid[] = $val->classid;
                }
            }

            $criteria = new CDbCriteria();
            $criteria->compare('schoolid', $schoolid);
            $criteria->compare('classid', $classid);
            $criteria->compare('stat', array(10,20));
            $criteria->index = 'childid';
            $reserve = ChildReserve::model()->findAll($criteria);
            $childern = ChildProfileBasic::model()->findAllByPk(array_keys($reserve));
            $childName = array();
            $email = array();
            foreach ($childern as $child) {
                $parent = $child->getParents();
                $str = '@';
                if (strpos($parent['father']['email'],$str)) {
                   $email[$child->childid] = $parent['father']['email'] . ';';
                }
                if (strpos($parent['mother']['email'],$str)) {
                   $email[$child->childid] .= $parent['mother']['email'];
                }
                $email[$child->childid] = trim($email[$child->childid] , ';');
                //去除重复
                $email = array_unique($email);
                $childName[$child->childid] = $child->getChildName();
            }
            echo CJSON::encode(array(
                'childName' => $childName,
                'email' => $email,
            ));
            return;
        }
        // 验证表单
        if (isset($_POST['bulkmail'])) {
            $model->attributes = Yii::app()->request->getPost('bulkmail');
            if ($model->validate()) {
                //获取需要发送邮件的邮箱
                $emails = $model->attributes['to'];
                $emails = str_replace(',',';',$emails);
                $emails = str_replace(' ','',$emails);
                $emails = explode(';',trim($emails,';'));
                // $emails = array_unique($emails);
                $emails = array_filter($emails);
                sort($emails);
                echo CJSON::encode(array(
                    'msg' => 'success',
                    'mailinfo' => $model->attributes,
                    'emails' => $emails,
                ));
                return;
            } else {
                $errors = current($model->errors);
                echo CJSON::encode(array(
                    'msg' => 'error',
                    'info'=> $errors[0],
                ));
                return;
            }
        }

        $this->render('index',array(
            'class'=>$class,
            'nextClass'=>$nextClass,
            ));
    }

    /**
     * 发送邮件
     */
    public function actionSendEmail()
    {
        if ($email=Yii::app()->request->getPost('email')) {
            $mail = Yii::createComponent('common.extensions.mailer.EMailer2');
            //验证是否为正确的邮件地址
            $str = '@';
            if (strpos($email,$str)) {
                $mailinfo=Yii::app()->request->getPost('mailinfo');
                $mail->IsSMTP();     
                $mail->CharSet = "utf-8";                                 
                $mail->Host = $mailinfo['smtp']; 
                $mail->Port = $mailinfo['port'];  
                $mail->SMTPAuth = true;     
                $mail->Username = $mailinfo['user'];  
                $mail->Password = $mailinfo['password']; 
                //邮件内容
                
                $mail->setFrom($mailinfo['user'],$mailinfo['fromname']);
                $mail->FromName = $mailinfo['fromname'];
                if (!empty($mailinfo['replyto'])) {
                    $mail->addReplyTo($mailinfo['replyto']);
                }
                $mail->AddAddress($email);
                $mail->Subject = $mailinfo['theme'];
                $mail->Body    = $mailinfo['main'];

                if (!empty($mailinfo['atta'])) {
                    $filePath = Yii::app()->params['OAUploadBasePath'] . '/mailattachment/';
                    foreach ($mailinfo['atta'] as $atta) {
                        $atta = explode(';', $atta);
                        $mail->addAttachment($filePath.$atta[0],$atta[1]);
                    }
                }
                if($mail->Send()){
                    echo CJSON::encode(array(
                        'msg' => 'success',
                        'email' => $email,
                        'mailinfo' => $mailinfo,
                        ));
                    return;
                }else{
                    echo $mail->ErrorInfo;
                }
            }
            echo CJSON::encode(array(
                'msg' => 'error',
                'email' => $email,
                'mailinfo' => $mailinfo,
                ));
            return;
        }
    }

    /**
     * 测试发信帐号
     */
    public function actionTestSend()
    {
        if (isset($_POST['bulkmail'])) {
            $mail = Yii::createComponent('common.extensions.mailer.EMailer2');

            $mailinfo=Yii::app()->request->getPost('bulkmail');
            $mail->IsSMTP();     
            $mail->CharSet = "utf-8";                                 
            $mail->Host = $mailinfo['smtp']; 
            $mail->Port = $mailinfo['port'];  
            $mail->SMTPAuth = true;     
            $mail->Username = $mailinfo['user'];  
            $mail->Password = $mailinfo['password']; 

            $mail->setFrom($mailinfo['user'],$mailinfo['fromname']);
            $mail->FromName = $mailinfo['fromname'];
            $mail->addBCC($this->testAccout);
            $mail->Subject = '艾毅群发邮件测试';
            $mail->Body    = '测试成功';

            if($mail->Send()){
                echo CJSON::encode(array(
                    'msg' => 'success',
                    ));
                return;
            }
            echo CJSON::encode(array(
                'msg' => $mail->ErrorInfo,
                ));
            return;
        }
    }

    /**
     * 上传附件
     */
    public function actionUploadatta()
    {
        $file = CUploadedFile::getInstanceByName('upload_file');
        $msg = '没有附件选中';
        if ($file) {
            if ($file->size > 10*1024*1024) {
                $msg = '文件过大';
            } else{
                $needType = array('jpg','jpeg','png','rar','doc','txt','pdf','gif','zip','xlsx','xls','docx');
                if (!in_array(strtolower($file->getExtensionName()), $needType)) {
                    $msg = '此文件类型不允许上传';
                }else{
                    $filePath = Yii::app()->params['OAUploadBasePath'] . '/mailattachment/';
                    $ext = $file->getExtensionName();
                    $fileName = $file->name;
                    $saveName = '5_' . uniqid() . '.' . $ext;
                    if ($file->saveAs($filePath . $saveName)){
                        $msg = 'success';
                        $baseUrl = Yii::app()->params['OAUploadBaseUrl'].'/mailattachment/';
                    }else{
                        $msg = '文件上传失败';
                    }
                }
            }
        }
        echo CJSON::encode(array(
            'url' => $baseUrl . $saveName . '?v' . time(),
            'fileName' => $fileName,
            'saveName' => $saveName,
            'msg' => $msg,
        ));
        
    }

}