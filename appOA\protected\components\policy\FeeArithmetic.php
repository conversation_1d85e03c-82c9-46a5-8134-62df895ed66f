<?php
class FeeArithmetic
{
	public $configs;
	public $schoolday;
	public $startDate;
	public $endDate;
	public $globalDiscount;
    public $classtype;
    public $multFeeBySchool = false;
    function __construct($configs,$schoolday,$startDate,$endDate,$globalDiscount=null,$classtype=null)
	{
		$this->configs = $configs;
		$this->schoolday = $schoolday;
		$this->startDate = $startDate;
		$this->endDate = $endDate;
		$this->globalDiscount = $globalDiscount;
        $this->classtype = $classtype;
        //学校多收费模式
        if (isset($configs[MORE_FEE_BY_SCHOOL]) && $configs[MORE_FEE_BY_SCHOOL] === true){
            if (empty($this->classtype)){
                throw new Exception('Class type is not set',500);
            }
            $this->multFeeBySchool = true;
        }
	}

	public function getTuitionFee($tuitionCalcBase,$amount,$schoolId,$discountId,$byType)
	{
		$result = null;
		$discountModel = null;
		if ($amount<=0)
		{
			return $result;
		}
		if ($discountId)
		{
			$criteria = new CDbCriteria;
			$criteria->compare('t.id', $discountId);
			$criteria->compare('t.stat', 10);
			$criteria->compare('t.schoolid',$schoolId);
			$criteria->compare('t.flag', 0);
			$criteria->addCondition('t.expire_date>='.strtotime('today'));
			$discountModel = DiscountSchool::model()->with('discountTitle')->dis()->find($criteria);
		}
        //学校多收费模式
        if ($this->multFeeBySchool === true){
            $result = $this->getGradeTuition($amount,$discountModel);
        }else{
            if ($tuitionCalcBase == BY_MONTH){
                $result = $this->getMIKTuition($amount,$discountModel,$byType);
            }else{
                $result = $this->getIATuition($amount,$discountModel);
            }
        }
		return $result;
	}

	public function getMIKTuition($amount,$discountModel,$byType)
	{
		$monthAmount = 0;
		$totalAmount = 0;
		$globalDiscountResult = false;
		$holidayMonth = false;
		$lastMonth = null;
		//学校多收费模式
        if ($this->multFeeBySchool === true){
            $configs = $this->configs[TUITION_CALCULATION_BASE][$this->classtype];
        }else{
            $configs = $this->configs[TUITION_CALCULATION_BASE];
        }
        //年付折扣
		$globalDiscount = isset($configs[BY_MONTH][GLOBAL_DISCOUNT][$this->globalDiscount]) ? explode(':', $configs[BY_MONTH][GLOBAL_DISCOUNT][$this->globalDiscount]) : array();
		if (count($globalDiscount) && in_array($byType, array(PAYGROUP_ANNUAL,PAYGROUP_SEMESTER)))
		{
			if ($this->startDate<=strtotime($globalDiscount[2]) && strtotime($globalDiscount[2])<$this->endDate)
			{
				$globalDiscountResult = true;
			}
		}
		//寒暑假
		if (isset($this->configs[HOLIDAY_MONTH]) && count($this->configs[HOLIDAY_MONTH]))
		{
			$holidayMonth = true;
		}
		foreach ( $this->schoolday['byMonth'] as $key =>$value )
		{
			$amountFactor = $amount;
			if ($byType == HOLIDAY_MONTH)
			{
				if ($holidayMonth && count($this->configs[HOLIDAY_MONTH][MONTH][$key]))
				{
					$totalAmount +=  $amountFactor/$this->configs[HOLIDAY_MONTH][MONTH][$key]*$value['actualSchoolday'];
					$result['part'][$key]['amount'] = round($totalAmount);
				}
				else
				{
					$totalAmount +=  0;
					$result['part'][$key]['amount'] = round($totalAmount);
				}
				$result['part'][$key]['startDate'] = ($value['flag'] == 'first') ? $this->startDate : $value['startDate'];
				$result['part'][$key]['endDate'] = $value['endDate'];
			}
			else
			{
				if ($holidayMonth==false || ($holidayMonth && !$this->configs[HOLIDAY_MONTH][MONTH][$key]))
				{
                    //计算总金额
                    if (isset($this->configs[TUITION_CALCULATION_BASE][BY_MONTH][MONTH_FACTOR]) && count($this->configs[TUITION_CALCULATION_BASE][BY_MONTH][MONTH_FACTOR])) 
                    {
                        $amountFactor = isset($this->configs[TUITION_CALCULATION_BASE][BY_MONTH][MONTH_FACTOR][$key]) ? $this->configs[TUITION_CALCULATION_BASE][BY_MONTH][MONTH_FACTOR][$key] * $amount : $amount;
                    }
                    else
                    {
                        $amountFactor = $amount;
                    }
					if ($value['flag'] == 'first')
					{
						if ($value['schoolday'] == $value['actualSchoolday'])
						{
							$totalAmount += $amountFactor;
						}
						else
						{
							$totalAmount +=  $amountFactor/$value['schoolday']*$value['actualSchoolday'];
						}
					}
					else
					{
						$totalAmount +=  $amountFactor;
					}
					if ($value['flag'] != 'last')
					{
						if ($value['schoolday'] != $value['actualSchoolday'])
						{
							$amountFactor = $amountFactor/$value['schoolday']*$value['actualSchoolday'];
						}
						if ($globalDiscountResult === true && !empty($discountModel))
						{
//							$result['part'][$key]['amount'] = round(floor($amountFactor*$globalDiscount[0]/$globalDiscount[1])*$globalDiscount[1]*($discountModel->discount/100));
							$result['part'][$key]['amount'] = round(
                                    getTrimData($amountFactor, $globalDiscount) *
                                    ($discountModel->discount/100));
							$monthAmount += $result['part'][$key]['amount'];
						}
						elseif ($globalDiscountResult === true)
						{
//							$result['part'][$key]['amount'] = round(floor($amountFactor*$globalDiscount[0]/$globalDiscount[1])*$globalDiscount[1]);
							$result['part'][$key]['amount'] = round(getTrimData($amountFactor, $globalDiscount));
							$monthAmount += $result['part'][$key]['amount'];
						}
						elseif (!empty($discountModel))
						{
							$result['part'][$key]['amount'] = round($amountFactor*($discountModel->discount/100));
							$monthAmount += $result['part'][$key]['amount'];
						}
						else
						{
							$result['part'][$key]['amount'] = round($amountFactor);
							$monthAmount += $result['part'][$key]['amount'];
						}
					}
					else
					{
						$lastMonth = $key;
					}
					$result['part'][$key]['startDate'] = ($value['flag'] == 'first') ? $this->startDate : $value['startDate'];
					$result['part'][$key]['endDate'] = $value['endDate'];
				}
			}
		}
		//处理有寒暑假的学校
		if ($holidayMonth == true)
		{
			$currentResult = end($result['part']);
			$this->endDate = $currentResult['endDate'];
		}
        if ($globalDiscountResult === true && !empty($discountModel))
        {
//            $result['full']['noDiscountAmount'] = floor($totalAmount*$globalDiscount[0]/$globalDiscount[1])*$globalDiscount[1];
            $result['full']['noDiscountAmount'] = getTrimData($totalAmount, $globalDiscount);
//            $result['full']['amount'] = round(floor($totalAmount*$globalDiscount[0]/$globalDiscount[1])*$globalDiscount[1]*($discountModel->discount/100));
            $result['full']['amount'] = round(
                getTrimData($totalAmount, $globalDiscount) *
                ($discountModel->discount/100)
            );
            $result['full']['discountId'] = $discountModel->id;
			$result['full']['discountTitle'] = $discountModel->discountTitle->title_cn;
        }
        elseif ($globalDiscountResult === true)
        {
//            $result['full']['noDiscountAmount'] = floor($totalAmount*$globalDiscount[0]/$globalDiscount[1])*$globalDiscount[1];
            $result['full']['noDiscountAmount'] = getTrimData($totalAmount, $globalDiscount);
//			$result['full']['amount'] = floor($totalAmount*$globalDiscount[0]/$globalDiscount[1])*$globalDiscount[1];
			$result['full']['amount'] = getTrimData($totalAmount,$globalDiscount);
        }
        elseif(!empty($discountModel))
        {
            $result['full']['noDiscountAmount'] = round($totalAmount);
            $result['full']['amount'] = round($totalAmount*($discountModel->discount/100));
			$result['full']['discountId'] = $discountModel->id;
			$result['full']['discountTitle'] = $discountModel->discountTitle->title_cn;
        }
        else
        {
            $result['full']['noDiscountAmount'] = round($totalAmount);
            $result['full']['amount'] = round($totalAmount);
        }
        $result['full']['startDate'] = $this->startDate;
        $result['full']['endDate'] = $this->endDate;
		if (!empty($lastMonth))
		{
			$result['part'][$lastMonth]['amount'] = round($result['full']['amount'] - $monthAmount);
		}
		return $result;
	}

    
    
	public function getIATuition($amount,$discountModel)
	{
		//day's amount
		$dayAmount = $amount / $this->schoolday['totalSchoolday'];
		$result['full'] = array(
			'noDiscountAmount' => round($dayAmount * $this->schoolday['totalActualSchoolday']),
			'amount'=>round($dayAmount * $this->schoolday['totalActualSchoolday']),
			'startDate'=>$this->startDate,
			'endDate'=>$this->endDate,
		);
		if (!empty($discountModel))
		{
				
			$result['full']['amount'] = round($dayAmount * $this->schoolday['totalActualSchoolday']*($discountModel->discount/100));
			$result['full']['discountId'] = $discountModel->id;
			$result['full']['discountTitle'] = $discountModel->discountTitle->title_cn;
		}
		$monthAmount = 0;
		foreach ( $this->schoolday['byMonth'] as $key =>$value )
		{
			if ($value['flag'] == 'last')
			{
				$result['part'][$key] = array(
				'amount' => $result['full']['amount']-$monthAmount,
				'startDate'=>($value['flag'] == 'first') ? $this->startDate : $value['startDate'],
				'endDate'=>$value['endDate'],
				);
			}
			else
			{
				$result['part'][$key] = array(
					'amount' => (!empty($discountModel)) ? round($dayAmount * $value['actualSchoolday'] * ($discountModel->discount/100)) : round($dayAmount * $value['actualSchoolday']),
					'startDate'=>($value['flag'] == 'first') ? $this->startDate : $value['startDate'],
					'endDate'=>$value['endDate'],
				);
				$monthAmount += $result['part'][$key]['amount'];
			}
		}
		return $result;
	}
    
    public function getGradeTuition($amount,$discountModel) {
        if (isset($this->configs[NEWFUNCTION]) && $this->configs[NEWFUNCTION]) {
            return $this->getGradeTuitionV2($amount, $discountModel);
        }
		if ($this->multFeeBySchool === true){
			$configs = $this->configs[TUITION_CALCULATION_BASE][$this->classtype];
		}else{
			$configs = $this->configs[TUITION_CALCULATION_BASE];
		}
		$globalDiscount = isset($configs[BY_MONTH][GLOBAL_DISCOUNT][$this->globalDiscount]) ? explode(':', $configs[BY_MONTH][GLOBAL_DISCOUNT][$this->globalDiscount]) : array(1,1,null);
        $numMonth = count($this->schoolday['byMonth']);
		$_amount = getTrimData($numMonth * $amount, $globalDiscount);
		$result['full'] = array(
			'noDiscountAmount' => $_amount,
			'amount'=>$_amount,
			'startDate'=>$this->startDate,
			'endDate'=>$this->endDate,
		);
		if (!empty($discountModel))
		{
			$result['full']['amount'] = round($_amount*($discountModel->discount/100));
			$result['full']['discountId'] = $discountModel->id;
			$result['full']['discountTitle'] = $discountModel->discountTitle->title_cn;
		}
		$monthAmount = 0;
		foreach ( $this->schoolday['byMonth'] as $key =>$value )
		{
			if ($value['flag'] == 'last')
			{
				$result['part'][$key] = array(
				'amount' => $result['full']['amount']-$monthAmount,
				'startDate'=>($value['flag'] == 'first') ? $this->startDate : $value['startDate'],
				'endDate'=>$value['endDate'],
				);
			}
			else
			{
				$result['part'][$key] = array(
					'amount' => (!empty($discountModel)) ? round($_amount * ($discountModel->discount/100)) : $_amount,
					'startDate'=>($value['flag'] == 'first') ? $this->startDate : $value['startDate'],
					'endDate'=>$value['endDate'],
				);
				$monthAmount += $result['part'][$key]['amount'];
			}
		}
		return $result;
    }

    public function getGradeTuitionV2($amount,$discountModel) {
        if ($this->multFeeBySchool === true){
            $configs = $this->configs[TUITION_CALCULATION_BASE][$this->classtype];
        }else{
            $configs = $this->configs[TUITION_CALCULATION_BASE];
        }

        $monthAmount = 0;
        $totalAmount = 0;
        $noDistotalAmount = 0;
        $lastMonth = null;
        foreach ( $this->schoolday['byMonth'] as $key =>$value )
        {
            if (isset($configs[BY_MONTH][MONTH_FACTOR]) && count($configs[BY_MONTH][MONTH_FACTOR]))
            {
                $amountFactor = isset($configs[BY_MONTH][MONTH_FACTOR][$key]) ? $configs[BY_MONTH][MONTH_FACTOR][$key] * $amount : $amount;
            }
            else
            {
                $amountFactor = $amount;
            }

            $result['part'][$key] = array(
                'amount' => (!empty($discountModel)) ? round($amountFactor * ($discountModel->discount/100)) : $amountFactor,
                'startDate'=>($value['flag'] == 'first') ? $this->startDate : $value['startDate'],
                'endDate'=>$value['endDate'],
            );
            $totalAmount += $result['part'][$key]['amount'];
            $noDistotalAmount += $amountFactor;

            if ($value['flag'] == 'last') {
                $lastMonth = $key;
            }
            else {
                $monthAmount += $result['part'][$key]['amount'];
            }
        }

        $globalDiscount = isset($configs[BY_MONTH][GLOBAL_DISCOUNT][$this->globalDiscount]) ? explode(':', $configs[BY_MONTH][GLOBAL_DISCOUNT][$this->globalDiscount]) : array(1,1,null);
        $_amount = getTrimData($noDistotalAmount, $globalDiscount);

        $result['full'] = array(
            'noDiscountAmount' => $noDistotalAmount,
            'amount'=>$_amount,
            'startDate'=>$this->startDate,
            'endDate'=>$this->endDate,
        );
        if (!empty($discountModel))
        {
            $result['full']['amount'] = round($_amount*($discountModel->discount/100));
            $result['full']['discountId'] = $discountModel->id;
            $result['full']['discountTitle'] = $discountModel->discountTitle->title_cn;
        }
        $result['part'][$lastMonth]['amount'] = round($result['full']['amount'] - $monthAmount);

        return $result;
    }

    /**
     * 基本餐费计算方式
     * @param type $amount
     * @param type $lunchType
     * @return array
     */
    public function getBaseLunchFee($amount,$lunchType){
        $result = null;
		if ($amount<=0)
		{
			return $result;
		}
		$monthAmount = 0;
        $lunchType = is_array($lunchType) ? $lunchType : unserialize($lunchType);
		if (in_array(40, $lunchType))
		{
			$lunchType = $lunchType + array("sat"=>10,"sun"=>10);
			foreach ( $this->schoolday['byMonth'] as $key =>$value )
			{
				$i=0;
				foreach ($value['day'] as $day)
				{
					if ($lunchType[strtolower(date('D',strtotime($key.$day)))] == 10)
					{
						$i++;	
					}
				}
				$result['part'][$key] = array(
					'amount' => $amount*$i,
					'startDate'=>($value['flag'] == 'first') ? $this->startDate : $value['startDate'],
					'endDate'=>$value['endDate'],
				);
				$monthAmount += $amount*$i;
			}
			$result['full'] = array(
				'amount'=>$monthAmount,
				'startDate'=>$this->startDate,
				'endDate'=>$this->endDate,
			);
		}
		else
		{
			foreach ( $this->schoolday['byMonth'] as $key =>$value )
			{
				$result['part'][$key] = array(
					'amount' => $amount*ceil($value['actualSchoolday']),
					'startDate'=>($value['flag'] == 'first') ? $this->startDate : $value['startDate'],
					'endDate'=>$value['endDate'],
				);
			}
			$result['full'] = array(
				'amount'=>$amount * ceil($this->schoolday['totalActualSchoolday']),
				'startDate'=>$this->startDate,
				'endDate'=>$this->endDate,
			);
		}
		return $result;
    }
    
    /**
     * 小学餐费计算方式
     */
    public function getGradeLunchFee($amount){
        if (isset($this->configs[NEWFUNCTION]) && $this->configs[NEWFUNCTION]) {
            return $this->getGradeBusV2($amount);
        }

        $numMonth = count($this->schoolday['byMonth']);
		$result['full'] = array(
			'noDiscountAmount' => round($numMonth * $amount, 2),
			'amount'=>round($numMonth * $amount, 2),
			'startDate'=>$this->startDate,
			'endDate'=>$this->endDate,
		);
		$monthAmount = 0;
		foreach ( $this->schoolday['byMonth'] as $key =>$value )
		{
			if ($value['flag'] == 'last')
			{
				$result['part'][$key] = array(
				'amount' => $result['full']['amount']-$monthAmount,
				'startDate'=>($value['flag'] == 'first') ? $this->startDate : $value['startDate'],
				'endDate'=>$value['endDate'],
				);
			}
			else
			{
				$result['part'][$key] = array(
					'amount' => round($numMonth * $amount, 2),
					'startDate'=>($value['flag'] == 'first') ? $this->startDate : $value['startDate'],
					'endDate'=>$value['endDate'],
				);
				$monthAmount += $result['part'][$key]['amount'];
			}
		}
		return $result;
    }

    public function getLunchFee($amount,$lunchType)
	{
		if ($this->multFeeBySchool === true or isset($this->configs[LUNCH_PERMONTH])){
            $result = $this->getGradeLunchFee($amount);
        }else{
            $result = $this->getBaseLunchFee($amount, $lunchType);
        }
        return $result;
	}

	public function getChildcareFee($amount,$byType)
	{
		$result = null;
		if ($amount<=0.01)
		{
			return $result;
		}
		$monthAmount = 0;
		$totalAmount = 0;
		$annualPay = false;
        $amountAmountFactor = $amount/12;
		foreach ( $this->schoolday['byMonth'] as $key =>$value )
		{
			if ($value['flag'] == 'first')
			{
				if ($value['schoolday'] == $value['actualSchoolday'])
				{
					$totalAmount += $amountAmountFactor;
                    $amountFactor = $amountAmountFactor;
					if ($key == $this->configs[TIME_POINTS][0] && $byType == PAYGROUP_ANNUAL)
					{
						$annualPay = true;
					}
				}
				else
				{
                    $amountFactor = $amountAmountFactor/$value['schoolday']*$value['actualSchoolday'];
					$totalAmount +=  $amountAmountFactor/$value['schoolday']*$value['actualSchoolday'];
				}
			}
			else
			{
				$totalAmount +=  $amountAmountFactor;
                $amountFactor = $amountAmountFactor;
			}
			if ($value['flag'] == 'last')
			{
				$lastMonth = $key;
			}
			else
			{
				$result['part'][$key]['amount'] = round($amountFactor);
				$monthAmount += $result['part'][$key]['amount'];
			}
			$result['part'][$key]['startDate'] = ($value['flag'] == 'first') ? $this->startDate : $value['startDate'];
			$result['part'][$key]['endDate'] = $value['endDate'];
		}
		$result['full'] = array(
				'amount'=>($annualPay) ? $this->configs[ANNUAL_CHILDCARE_AMOUNT] : round($totalAmount),
				'startDate'=>$this->startDate,
				'endDate'=>$this->endDate,
		);
		$result['part'][$lastMonth]['amount'] = round($result['full']['amount'] - $monthAmount);
		return $result;
	}

	public function getBaseBusFee($amount,$byType)
	{
		$result = null;
		if ($amount<0)
		{
			return $result;
		}
		$monthAmount = 0;
		$totalAmount = 0;
		$holidayMonth = false;
		$lastMonth = null;
		//寒暑假
		if (isset($this->configs[HOLIDAY_MONTH]) && count($this->configs[HOLIDAY_MONTH]))
		{
			$holidayMonth = true;
		}
		foreach ( $this->schoolday['byMonth'] as $key =>$value )
		{
			$amountFactor = $amount;
			if ($byType == HOLIDAY_MONTH)
			{
				if ($holidayMonth && count($this->configs[HOLIDAY_MONTH][MONTH][$key]))
				{
					$totalAmount +=  $amountFactor/$this->configs[HOLIDAY_MONTH][MONTH][$key]*$value['actualSchoolday'];
					$result['part'][$key]['amount'] = round($totalAmount);
				}
				else
				{
					$totalAmount +=  0;
					$result['part'][$key]['amount'] = round($totalAmount);
				}
				$result['part'][$key]['startDate'] = ($value['flag'] == 'first') ? $this->startDate : $value['startDate'];
				$result['part'][$key]['endDate'] = $value['endDate'];
			}
			else
			{
				if ($holidayMonth==false || ($holidayMonth && !$this->configs[HOLIDAY_MONTH][MONTH][$key]))
				{
                    if (isset($this->configs[TUITION_CALCULATION_BASE][BY_MONTH][MONTH_FACTOR]) && count($this->configs[TUITION_CALCULATION_BASE][BY_MONTH][MONTH_FACTOR]))
					{
						$amountFactor =  isset($this->configs[TUITION_CALCULATION_BASE][BY_MONTH][MONTH_FACTOR][$key]) ?  $this->configs[TUITION_CALCULATION_BASE][BY_MONTH][MONTH_FACTOR][$key]* $amount : $amount;
					}
                    else
                    {
                        $amountFactor = $amount;
                    }
					if ($value['flag'] == 'first')
					{
						if ($value['schoolday'] == $value['actualSchoolday'])
						{
							$totalAmount += $amountFactor;
						}
						else
						{
							$totalAmount +=  $amountFactor/$value['schoolday']*$value['actualSchoolday'];
							$amountFactor = $amountFactor/$value['schoolday']*$value['actualSchoolday'];
						}
					}
					else
					{
						$totalAmount +=  $amountFactor;
					}
					if ($value['flag'] == 'last')
					{
						$lastMonth = $key;
					}
					else
					{
						$result['part'][$key]['amount'] = round($amountFactor);
						$monthAmount += $result['part'][$key]['amount'];
					}
					$result['part'][$key]['startDate'] = ($value['flag'] == 'first') ? $this->startDate : $value['startDate'];
					$result['part'][$key]['endDate'] = $value['endDate'];
				}
			}
		}
		
		//处理有寒暑假的学校
		if ($holidayMonth == true)
		{
			$currentResult = end($result['part']);
			$this->endDate = $currentResult['endDate'];
		}
		
		$result['full'] = array(
				'amount'=>round($totalAmount),
				'startDate'=>$this->startDate,
				'endDate'=>$this->endDate,
		);
		if (!empty($lastMonth))
		{
			$result['part'][$lastMonth]['amount'] = round($result['full']['amount'] - $monthAmount);
		}
		return $result;
	}
    
    public function getGradeBusFee($amount){
        if (isset($this->configs[NEWFUNCTION]) && $this->configs[NEWFUNCTION]) {
            return $this->getGradeBusV2($amount);
        }

        $numMonth = count($this->schoolday['byMonth']);
		$result['full'] = array(
			'noDiscountAmount' => round($numMonth * $amount),
			'amount'=>round($numMonth * $amount),
			'startDate'=>$this->startDate,
			'endDate'=>$this->endDate,
		);
		$monthAmount = 0;
		foreach ( $this->schoolday['byMonth'] as $key =>$value )
		{
			if ($value['flag'] == 'last')
			{
				$result['part'][$key] = array(
				'amount' => $result['full']['amount']-$monthAmount,
				'startDate'=>($value['flag'] == 'first') ? $this->startDate : $value['startDate'],
				'endDate'=>$value['endDate'],
				);
			}
			else
			{
				$result['part'][$key] = array(
					'amount' => round($numMonth * $amount),
					'startDate'=>($value['flag'] == 'first') ? $this->startDate : $value['startDate'],
					'endDate'=>$value['endDate'],
				);
				$monthAmount += $result['part'][$key]['amount'];
			}
		}
		return $result;
    }

    public function getGradeBusV2($amount)
    {
        if ($this->multFeeBySchool === true){
            $configs = $this->configs[TUITION_CALCULATION_BASE][$this->classtype];
        }else{
            $configs = $this->configs[TUITION_CALCULATION_BASE];
        }

        $totalAmount=0;
        $monthAmount=0;
        foreach ( $this->schoolday['byMonth'] as $key =>$value )
        {
            if (isset($configs[BY_MONTH][MONTH_FACTOR]) && count($configs[BY_MONTH][MONTH_FACTOR]))
            {
                $amountFactor = isset($configs[BY_MONTH][MONTH_FACTOR][$key]) ? $configs[BY_MONTH][MONTH_FACTOR][$key] * $amount : $amount;
            }
            else
            {
                $amountFactor = $amount;
            }

            $result['part'][$key] = array(
                'amount' => $amountFactor,
                'startDate'=>($value['flag'] == 'first') ? $this->startDate : $value['startDate'],
                'endDate'=>$value['endDate'],
            );
            $totalAmount += $result['part'][$key]['amount'];

            if ($value['flag'] == 'last') {
                $lastMonth = $key;
            }
            else {
                $monthAmount += $result['part'][$key]['amount'];
            }
        }

        $result['full'] = array(
            'amount'=>$totalAmount,
            'startDate'=>$this->startDate,
            'endDate'=>$this->endDate,
        );

        return $result;
    }
    
    public function getBusFee($amount,$byType){
        if ($this->multFeeBySchool === true){
            $result = $this->getGradeBusFee($amount);
        }else{
            $result = $this->getBaseBusFee($amount, $lunchType);
        }
        return $result;
    }
}

function getTrimData($_amount, $_discount) {
    //表示要四舍五入
    if(isset($_discount[3]) && $_discount[3] == 'round') {
        return round($_amount * $_discount[0] / $_discount[1]) * $_discount[1];
    }else{
        return floor($_amount * $_discount[0] / $_discount[1]) * $_discount[1];
    }
}