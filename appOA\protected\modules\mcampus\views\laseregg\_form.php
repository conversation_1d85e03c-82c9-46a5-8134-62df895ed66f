<div class="modal-header">
	<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
	<h4 class="modal-title" id="exampleModalLabel"><?php echo $model->id?'更新记录仪：':'添加记录仪：'; ?></h4>
</div>
<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'laseregg-info-form',
	'enableAjaxValidation'=>false,
	'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
)); ?>
<div class="modal-body">
	<div class="form-group">
		<label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'laseregg_id'); ?></label>
		<div class="col-xs-8">
			<?php echo $form->textField($model,'laseregg_id',array('maxlength'=>255,'class'=>'form-control')); ?>
		</div>
	</div>
	<div class="form-group">
		<label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'schoolid'); ?></label>
		<div class="col-xs-8">
			<?php if($model->isNewRecord)$model->schoolid=$this->branchId;echo $form->dropDownList($model,'schoolid', $this->getAllBranchId() , array('class'=>'form-control', 'empty'=>Yii::t("global", 'Please Select'))); ?>
		</div>
	</div>
	<div class="form-group">
		<label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'laseregg_name'); ?></label>
		<div class="col-xs-8">
			<?php echo $form->textField($model,'laseregg_name',array('maxlength'=>255,'class'=>'form-control')); ?>
		</div>
	</div>
	<div class="form-group">
		<label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'laseregg_mac'); ?></label>
		<div class="col-xs-8">
			<?php echo $form->textField($model,'laseregg_mac',array('maxlength'=>255,'class'=>'form-control')); ?>
		</div>
	</div>
</div>
<div class="modal-footer">
	<button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
	<button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
</div>

<?php $this->endWidget(); ?>
