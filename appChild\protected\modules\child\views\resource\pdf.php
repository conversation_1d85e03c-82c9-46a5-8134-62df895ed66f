<?php
$this->breadcrumbs = array(
    Yii::t("navigations", "Resources") => array('/child/resource/index'),
    Yii::t("navigations", "Parent Handbook"),
);
?>
<h1>
    <label><?php echo Yii::t("navigations", "Parent Handbook"); ?></label>
</h1>

<p class="cover">
    <?php
    echo CHtml::link(CHtml::image(Yii::app()->theme->baseUrl . '/images/parent_handbook.jpg', Yii::t("navigations", "Parent Handbook")), $this->createUrl("//child/resource/readPdf", array("category"=>"handbook")), array('class' => 'bigger', "target" => "_blank", 'title' => Yii::t("resource", 'Click to read')));
    ?>
    <span class="text">
    <?php
    echo CHtml::link('中文版', $this->createUrl("//child/resource/readPdf", array("category"=>"handbook","v"=>"zh_cn")), array("target" => "_blank"));
    echo CHtml::link('English Version', $this->createUrl("//child/resource/readPdf", array("category"=>"handbook","v"=>"en_us")), array("target" => "_blank"));
    ?>
    </span>
</p>

<div class="clear"></div>

<style>
p.cover{
    text-align: center;
    position: relative;
}
p.cover img{
    border: 5px solid #FFFFFF;
    box-shadow: 2px 2px 3px #666666;
}
p.cover span.text{
    position: absolute;
    font-size: 20px;
    left: 0;
    bottom: 100px;
    width: 400px;
    text-align: center;
}
p.cover span.text a{
    margin-right: 10px;
}
</style>