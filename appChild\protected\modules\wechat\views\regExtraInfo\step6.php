<?php
$langModels = Term::model()->lang()->findAll();

$titleLang = (Yii::app()->language == "zh_cn") ? "cntitle" : "entitle";
$langcn = CHtml::listData($langModels, 'diglossia_id', $titleLang);

?>
<?php echo $this->renderPartial('steps_header', array('currentStep'=>6)); ?>
<div class="container">
     <h5 class="htitle"><span class="fas fa-globe-asia"></span> <?php echo $this->getStepTitle($this->step); ?></h5>
    <?php echo $this->renderPartial('_nouser_header'); ?>

    <div class="edit-content">
        <!-- 英文名 -->
        <?php 
//            $content = RegConfig::getContent($this->regStudent->reg_id, $this->step);
//            if ($content) {
//                echo CommonUtils::autoLang(base64_decode($content['cn']['lang']), base64_decode($content['en']['lang']));
//            }
         ?>  
        <?php $form = $this->beginWidget('CActiveForm', array(
            'id' => 'sep1-form',
            'enableAjaxValidation' => false,
            'htmlOptions' => array(
                // 'class'=>'J_ajaxForm form-horizontal',
                'role' => 'form',
            ),
        )); ?>
    </div>

    <div class="form-group">
        <?php echo $form->labelEx($formModel6, 'firstLanguage'); ?>
        <?php echo $form->dropDownList($formModel6, 'firstLanguage', $langcn, array('class' => 'form-control mb10', 'change' => 'asd()', 'empty' => Yii::t('reg', 'Choose'))); ?>
    </div>
    <div class="form-group">
        <?php echo $form->labelEx($formModel6, 'familyLanguage'); ?>
        <?php echo $form->dropDownList($formModel6, 'familyLanguage', $langcn, array('class' => 'form-control mb10', 'empty' => Yii::t('reg', 'Choose'))); ?>
    </div>
    <div class="form-group">
        <?php echo $form->labelEx($formModel6, 'commonlyLanguage'); ?>
        <?php echo $form->dropDownList($formModel6, 'commonlyLanguage', $langcn, array('class' => 'form-control mb10', 'empty' => Yii::t('reg', 'Choose'))); ?>
    </div>
    <div class="mt-3">
        <button type="submit" class="btn btn-primary btn-lg btn-block examine"><?php echo Yii::t('reg', 'Submit') ?></button>
        <p class="text-black-50 mt-3 text-center  examine_status">

        </p>
    </div>
    <?php $this->endWidget(); ?>
</div>
