<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
class ChartsController extends BranchBasedController{
    public function init(){
        if(!Yii::app()->user->checkAccess("oChartsAdmin")){
			throw new CException('Permission Denied');
		}	
        parent::init();
        Yii::import('common.models.charts.IvyStatsItem');
        $this->branchSelectParams["urlArray"] = array("//operations/charts/index");
    }
    
    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }
    
    public function actionIndex($code=IvyStatsItem::CODE_FINANCE_ACTUAL)
	{
        //类型代码
        $code=strtolower(Yii::app()->request->getParam('code',IvyStatsItem::CODE_FINANCE_ACTUAL));
		$code= in_array($code ,array(IvyStatsItem::CODE_FINANCE_ACTUAL, IvyStatsItem::CODE_FINANCE_BUDGET)) ? $code : IvyStatsItem::CODE_FINANCE_ACTUAL;
		$_GET['code'] = $code;
        //财务类型列表
        $financeType = IvyStatsItem::getFinanceType();
        $subCode = $financeType['subcode'];
        if ($code == IvyStatsItem::CODE_FINANCE_BUDGET){
            unset($subCode[IvyStatsItem::SUBCODE_FINANCE_CASH_FLOW]);
        }
        //类型菜单
        foreach ($financeType['code'] as $key=> $val){
            $pageSubMenu[] = array(
                'label'=>$val,
                'url'=>array('//operations/charts/index', 'code'=>$key),
            );
        }
        //所选学校校历的开始年
        Yii::import('common.models.calendar.CalendarSemester');
        $calendarList = CalendarSemester::model()->getSemesterTimeStamp($this->allBranch[$this->branchId]['yid']);
        //调已存储数据（报表）
        $crite = new CDbCriteria;
        $crite->select = 'period,subcode,data,id,memo';
        $crite->compare('schoolid', $this->branchId);
        $crite->compare('category', IvyStatsItem::CATEGORY_FINANCE);
        $crite->compare('code', $code);
        $model = IvyStatsItem::model()->findAll($crite);
        $financeData = array();
        $jsMemo = array();
        if (!empty($model)){
            foreach ($model as $val){
                $financeData[$val->period][$val->subcode]['data'] = $val->data;
                $financeData[$val->period][$val->subcode]['id'] = $val->id;
                $jsMemo[$val->id] = $val->memo;
            }
        }
        Yii::app()->clientScript->registerCssFile( Yii::app()->theme->baseUrl.'/css/teaching.css');
		$this->render('index',array(
                'pageSubMenu'=>$pageSubMenu,
                'subCode'=>$subCode,
                'fallStart'=>$calendarList['fall_start'],
                'financeData'=>$financeData,
                'jsMemo'=> $jsMemo,
            )
        );
    }
    
    public function actionUpdate()
    {
        if (Yii::app()->request->isAjaxRequest && Yii::app()->request->isPostRequest){
           if (isset($_POST)){
               $crite = new CDbCriteria;
               $crite->compare('schoolid', $this->branchId);
               $crite->compare('category', IvyStatsItem::CATEGORY_FINANCE);
               $crite->compare('period', $_POST['period']);
               $crite->compare('code', $_POST['code']);
               $crite->compare('subcode', $_POST['subcode']);
               $model = IvyStatsItem::model()->find($crite);
               if (empty($model)){
                    $model = new IvyStatsItem;
                    $model->created = time();
               }
               $model->attributes = $_POST;
               $model->period_type = IvyStatsItem::TYPE_M;
               $model->schoolid = $this->branchId;
               $model->category = IvyStatsItem::CATEGORY_FINANCE;
               $model->startyear = IvyStatsItem::getStartYear($model->period);
               $model->userid = Yii::app()->user->getId();
               $model->updated = time();
               if ($model->save()){
                   $this->addMessage('state', 'success');
                   $this->addMessage('data', array('id'=>$model->id, 'code'=>$model->code));
                   $this->showMessage();
               }else{
                   $this->addMessage('state', 'fail');
                   $this->addMessage('message', Yii::t("charts", "Update fail, please contact IT Dept."));
                   $this->showMessage();
               }
           }
        }
    }
    
    public function actionUpdateMemo() {
        $this->layout = '//layouts/dialog';
        $id = Yii::app()->request->getParam('id', 0);
        $model = IvyStatsItem::model()->findByPk($id);
        if (!empty($model)) {
            if (isset($_POST['memo'])) {
                $model->memo = $_POST['memo'];
                $model->updated = time();
                if ($model->save()) {
                    $this->addMessage('state', 'success');
                    $this->addMessage('callback', 'cb');
                    $this->addMessage('data', array('id'=>$model->id, 'memo'=>$model->memo));
                    $this->addMessage('message', Yii::t("charts", "Success"));
                    $this->showMessage();
                } else {
                    $this->addMessage('state', 'fail');
                    $this->showMessage();
                }
            }
        }
        /*
        $this->render('memo',array(
                'model'=>$model,
            )
        );
         * */
    }

    public function actionSelect()
	{
		$this->branchSelectParams["showList"] = true;
		$this->render('select');
	}
}
?>
