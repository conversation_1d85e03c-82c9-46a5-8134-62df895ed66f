<?php

class GlobalController extends ProtectedController
{
    public $actionAccessAuths = array(
        'Branch'        => array('access'=>'o_X_Access', 'admin'=>'o_X_Adm_Common'),
        'DelBranch'     => 'o_X_Adm_Common',
        'Term'          => array('access'=>'o_X_Access', 'admin'=>'o_X_Adm_Common'),
        'Delete'        => 'o_X_Adm_Common',
        'DeleteCate'    => 'o_X_Adm_Common',
        'DelLogo'       => 'o_X_Adm_Common',
    );

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'sysConfig';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','System Management');
    }

    /**
     * 校园管理（基本信息、校园图标、账单脚注、积分兑换、班级类型）
     */
    public function actionBranch(){
        $category = Yii::app()->request->getParam('category', null);
        $cs = Yii::app()->clientScript;
        switch($category){
            case 'basic';
                if(isset($_POST['Branch']) && isset($_POST['BranchInfo'])){
                    $new = isset($_POST['new']) ? $_POST['new'] : 0;
                    if($new){
                        $count = Branch::model()->countByAttributes(array('branchid'=>$_POST['Branch']['branchid']));
                        if($count){
                            $this->addMessage('message', '学校ID已经存在！');
                            $this->addMessage('state', 'fail');
                            $this->showMessage();
                        }
                        else{
                            $model = new Branch();
                            $model->info = new BranchInfo();
                        }
                    }
                    else{
                        $model = Branch::model()->findByPk($_POST['Branch']['branchid']);
                    }

                    $model->attributes = $_POST['Branch'];
                    $model->created_timestamp = time(); # 实际为更新时间
                    $isNew = false;
                    if($model->isNewRecord){
                        $isNew = true;
                    }
                    $model->opentime = strtotime($model->opentime);
                    if($model->type != 20)
                        $model->group=$model->type;
                    if($model->save()){
                        $model->info->branchid=$model->branchid;
                        $model->info->attributes  = $_POST['BranchInfo'];
                        $model->info->update_user = Yii::app()->user->id;
                        $model->info->update_timestamp = time();
                        if($model->info->save()){
                            $this->addMessage('state', 'success');
//                            $this->addMessage('refresh', true);
                            $this->addMessage('message', '保存成功');
                            $this->addMessage('callback', 'callback');
                            $data = array('basic'=>$model->attributes, 'info'=>$model->info->attributes, 'id'=>$model->branchid);
                            $data['basic']['opentime']=OA::formatDateTime($data['basic']['opentime']);
                            $this->addMessage('data', array('data'=>$data, 'isNew'=>$isNew));
                        }
                        else{
                            $this->addMessage('state', 'fail');
                            $errs = current($model->info->getErrors());
                            $this->addMessage('message', $errs?$errs[0]:'保存扩展信息失败！');
                        }
                    }
                    else{
                        $errs = current($model->getErrors());
                        $this->addMessage('message', $errs?$errs[0]:'保存基本信息失败！');
                        $this->addMessage('state', 'fail');
                    }
                    $this->showMessage();
                }

                $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
                $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
                $cs->registerCoreScript('jquery.ui');
                $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
                break;
            case 'bankinfo';
                if(isset($_POST['tdata']) && $_POST['branchid']){
                    $dArray = array();
                    foreach($_POST['tdata'] as $id=>$tdata){
                        $dArray['d'.$id] = array(
                            'display'=>isset($_POST['tid'][$id]) ? $_POST['tid'][$id] : 0,
                            'tdata'=>$tdata,
                        );
                    }

                    $branchid = $_POST['branchid'] ? $_POST['branchid'] : '';
                    $criteria = new CDbCriteria();
                    $criteria->compare('branchid', $branchid);
                    $criteria->compare('category', 'bankInfo');
                    $model = BranchVar::model()->find($criteria);
                    if($model === null)
                        $model = new BranchVar();
                    $model->branchid = $branchid;
                    $model->category = 'bankInfo';
                    $model->flag = 0;
                    $model->data = base64_encode(serialize($dArray));
                    $model->updated = time();
                    $model->user = Yii::app()->user->id;
                    if($model->save()){
                        $this->addMessage('state', 'success');
                        $this->addMessage('callback', 'callback');
                        $this->addMessage('data', array('data'=>unserialize(base64_decode($model->data)), 'branchid'=>$branchid));
                        $this->addMessage('message', '保存成功');
                    }
                    else{
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', '保存失败');
                    }
                    $this->showMessage();
                }
                $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
                $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
                break;
            case 'points';
                if(isset($_POST['BranchVar'])){
                    $i=0;$j=0;
                    $crit = new CDbCriteria();
                    $crit->compare('category', 'pointsExchange');
                    $crit->index = 'branchid';
                    $models = BranchVar::model()->findAll($crit);
                    foreach($_POST['BranchVar'] as $branchid=>$bVar){
                        $model = isset($models[$branchid]) && $models[$branchid]!==null ? $models[$branchid] : new BranchVar();
                        $model->attributes=$_POST['BranchVar'][$branchid];
                        if(empty($model->data))
                            $model->data=0;
                        $model->branchid=$branchid;
                        $model->category='pointsExchange';
                        $model->user=Yii::app()->user->id;
                        $model->updated=time();
                        $model->setScenario('points');
                        if($model->save()){
                            $j++;
                        }
                        $i++;
                    }
                    $this->addMessage('state', $j>0?'success':'fail');
                    $this->addMessage('message', sprintf('更新成功: %d, 更新失败: %d', $j, $i-$j));
                    $this->addMessage('refresh', true);
                    $this->showMessage();
                }
                break;
            case 'logo':
                if(Yii::app()->request->isPostRequest && isset($_POST['branchid'])){
                    $branchid = $_POST['branchid'] ? $_POST['branchid'] : '';

                    $model = Branch::model()->findByPk($branchid);
                    $model->upload = CUploadedFile::getInstanceByName('logo');
                    if ($model->upload) {
                        $upResult = OA::processPicUpload($model->upload, 'branchPhoto', $model->logo);
                        $model->logo = $upResult['filename'];
                    }
                    if($model->save()){
                        $this->addMessage('state', 'success');
                        $this->addMessage('refresh', true);
                        $this->addMessage('message', '上传成功！');
                    }
                    else{
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', '上传失败！');
                    }
                    $this->showMessage();
                }
                break;
            case 'classcfg':
            case 'holidaycfg':
                if(isset($_POST['BranchVar'])){
                    $i=0;$j=0;
                    $postData = $_POST['BranchVar'];
                    $crit = new CDbCriteria();
                    $crit->compare('category', $category);
                    $crit->index = 'branchid';
                    $models = BranchVar::model()->findAll($crit);
                    foreach(array_keys($postData) as $bid){
                        $_data = trim($postData[$bid]['data']);
                        if(empty($models[$bid]) && $postData[$bid]['flag']==0 && empty($_data) ){
                            continue;
                        }
                        if(empty($models[$bid])){
                            $model = new BranchVar();
                            $model->setAttributes(array(
                                'branchid' => $bid,
                                'category' => $category,
                            ));
                        }else{
                            $model = $models[$bid];
                        }

                        if($postData[$bid]['flag']==0 && empty($_data) ){
                            $model->delete();
                        }else{
                            $model->setAttributes(array(
                                'flag'=>$postData[$bid]['flag'],
                                'data'=>$_data,
                                'user'=>Yii::app()->user->getId(),
                                'updated'=>time(),
                            ));
                            if( $model->save()){
                                $j++;
                            }else{
                                $i++;
                            }
                        }

                    }
                    $this->addMessage('state', $j>0?'success':'fail');
                    $this->addMessage('message', sprintf('更新成功: %d, 更新失败: %d', $j, $i));
                    $this->addMessage('refresh', true);
                    $this->showMessage();
                }
                break;
        }
        $this->render('branch', array('category'=>$category));
    }

    /**
     * 删除校园
     */
    public function actionDelBranch()
    {
        $id = Yii::app()->request->getParam('id', 0);
        Yii::import('common.models.visit.IvyVisitsRecord');

        $visitCount = IvyVisitsRecord::model()->countByAttributes(array('schoolid'=>$id));
        if($visitCount){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '已有来访家长不能删除！');
        }
        else{
            $staffCount = UserProfile::model()->countByAttributes(array('branch'=>$id));
            if($staffCount){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '已有员工存在不能删除！');
            }
            else{
                $childCount = ChildProfileBasic::model()->countByAttributes(array('schoolid'=>$id));
                if($childCount){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '已有孩子存在不能删除！');
                }
                else{
                    $classCount = IvyClass::model()->countByAttributes(array('schoolid'=>$id));
                    if($classCount){
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', '已有班级存在不能删除！');
                    }
                    else{
                        $model = Branch::model()->findByPk($id);
                        if($model->delete()){
                            if($model->info->delete()){
                                $this->addMessage('state', 'success');
                                $this->addMessage('message', '删除成功');
                                $this->addMessage('callback', 'delCB');
                                $this->addMessage('data', array('id'=>$id));
                            }
                        }
                    }
                }
            }
        }
        $this->showMessage();
    }

    /**
     * 双语配置页面及保存处理
     */
    public function actionTerm()
    {
        if(Yii::app()->request->isPostRequest && Yii::app()->request->isAjaxRequest){
            if(isset($_POST['newroot'])){
                $model = new DiglossiaCategory;
                $model->attributes =$_POST['newroot'];
                if($model->save()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message','Data saved!'));
                }
                else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', 'Fail!');
                }
            }
            elseif(isset($_POST['newchild'])){
                $model = new Term;
                $model->attributes = $_POST['newchild'];
                if($model->save()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message','Data saved!'));
                }
                else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', 'Fail!');
                }
            }
            elseif(isset($_POST['data'])){
                if(isset($_POST['data']['navid'])){
                    $model = Term::model()->findByPk($_POST['data']['navid']);
                    $model->attributes = $_POST['data'];
                    if($model->save()){
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', Yii::t('message','Data saved!'));
                    }
                    else{
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', 'Fail!');
                    }
                }
                elseif(isset($_POST['data']['cateid'])){
                    $model = DiglossiaCategory::model()->findByPk($_POST['data']['cateid']);
                    $model->attributes =$_POST['data'];
                    if($model->save()){
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', Yii::t('message','Data saved!'));
                    }
                    else{
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', 'Fail!');
                    }
                }
            }
            $this->showMessage();
        }

        $criteria = new CDbCriteria;
        $criteria->order='category_sign asc';
        $items = DiglossiaCategory::model()->with('diglossia')->findAll($criteria);

        $this->render('term', array('items'=>$items));
    }

    /**
     * 删除双语
     */
    public function actionDelete()
    {
        $id = Yii::app()->request->getParam('id', 0);
        if(Yii::app()->request->isPostRequest && $id){
            if(Term::model()->findByPk($id)->delete()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', '删除成功！');
                $this->addMessage('refresh', true);
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', 'Fail!');
            }
        }
        $this->showMessage();
    }

    public function actionClearTermCache(){
        if(Yii::app()->request->isPostRequest){
            Term::model()->clearCaches();
            $this->addMessage('state', 'success');
            $this->addMessage('message', '删除成功！');
        }else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'Fail!');
        }
        $this->showMessage();
    }

    /**
     * 删除双语分类
     */
    public function actionDeleteCate()
    {
        $id = Yii::app()->request->getParam('id', 0);
        if(Yii::app()->request->isPostRequest && $id){
            if(!Term::model()->countByAttributes(array('category_id'=>$id))){
                if(DiglossiaCategory::model()->findByPk($id)->delete()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', '删除成功！');
                    $this->addMessage('refresh', true);
                }
                else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', 'Fail!');
                }
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '请先删除类别下面的条目！');
            }
        }
        $this->showMessage();
    }

    /**
     * 清除校园LOGO
     */
    public function actionDelLogo()
    {
        if(Yii::app()->request->isPostRequest){
            $id = Yii::app()->request->getParam('id', '');
            if ( Branch::model()->updateByPk($id, array('logo'=>'')) ){
                $this->addMessage('state', 'success');
                $this->addMessage('refresh', true);
                $this->addMessage('message', '清除成功！');
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '清除失败！');
            }
            $this->showMessage();
        }
    }
}