<?php
/**
 * 跟framework 1.1.11 里的CHtml::radioButtonList函数几乎一样，唯一不一样的就是$id是通过参数传入，
 * 原函数中$id是从0开始
 **/
class MCHtml extends CHtml{
	public static function groupRadioButtonList($name,$select,$data, $startid=0,$htmlOptions=array()){
		$template=isset($htmlOptions['template'])?$htmlOptions['template']:'{input} {label}';
		$separator=isset($htmlOptions['separator'])?$htmlOptions['separator']:"<br/>\n";
		$container=isset($htmlOptions['container'])?$htmlOptions['container']:'span';
		unset($htmlOptions['template'],$htmlOptions['separator'],$htmlOptions['container']);

		$labelOptions=isset($htmlOptions['labelOptions'])?$htmlOptions['labelOptions']:array();
		unset($htmlOptions['labelOptions']);

		$items=array();
		$baseID=self::getIdByName($name);
		$id=$startid;
		foreach($data as $value=>$label)
		{
			$checked=!strcmp($value,$select);
			$htmlOptions['value']=$value;
			$htmlOptions['id']=$baseID.'_'.$id++;
			$option=self::radioButton($name,$checked,$htmlOptions);
                        $currentLabelOptions = $labelOptions;
                        if(!empty($currentLabelOptions)){
                            foreach ($currentLabelOptions as $l_k => $l_v) {
                                if('{value}' == $l_v){
                                    $currentLabelOptions[$l_k] = $value;
                                }
                            }
                        }
			$label=self::label($label,$htmlOptions['id'],$currentLabelOptions);
			$items[]=strtr($template,array('{input}'=>$option,'{label}'=>$label));
		}
		if(empty($container))
			return implode($separator,$items);
		else
			return self::tag($container,array('id'=>$baseID),implode($separator,$items));
	}
    
    public static function weiboImage($src, $alt='', $htmlOptions=array(), $hasPermission=true)
    {
//        Yii::app()->clientScript->registerCssFile(Yii::app()->theme->baseUrl.'/css/wbshare.css');
        Yii::app()->clientScript->registerCssFile(Yii::app()->theme->baseUrl.'/css/wbshare.css?t='.Yii::app()->params['refreshAssets']);
        Yii::app()->clientScript->registerScriptFile(Yii::app()->theme->baseUrl.'/js/wbshare.js');
        $shareimg = isset($htmlOptions['init']) ? $htmlOptions['init'] : $src;
        $wbimg = '';
        $wbimg .= '<div class="weibo"><a href="javascript:;" class="wa">';
        $wbimg .= parent::image($src, $alt, $htmlOptions);
        $wbimg .= '<div class="wbbtn">';
        $baiduStats = isset(Yii::app()->params['baiduStats']) && Yii::app()->params['baiduStats'] ? "_hmt.push(['_trackEvent', 'weibo', 'click', 'sina']);" : "";
		if($hasPermission){
			$wbimg .= "<div class='sinabtn' title='".Yii::t('portfolio', 'Share to Sina Weibo')."' onclick=\"".$baiduStats."sinaWb('".$shareimg."', '".urlencode($alt)."');\"></div>";
			$wbimg .= "<div class='qqbtn' title='".Yii::t('portfolio', 'Share to QQ Weibo')."' onclick=\"".$baiduStats."qqWb('".$shareimg."', '".urlencode($alt)."');\"></div>";		
		}else{
			$wbimg .= "<div class='sinabtn' title='".Yii::t('portfolio', 'Share to Sina Weibo')."' onclick=\"previewOnly();\"></div>";
			$wbimg .= "<div class='qqbtn' title='".Yii::t('portfolio', 'Share to QQ Weibo')."' onclick=\"previewOnly();\"></div>";				
		}
        $wbimg .= '<div class="c"></div></div>';
        $wbimg .= '</a></div>';
        $wbimg .= '<div class="c"></div>';
        return $wbimg;
    }
    
    public static function flv($src, $width=400, $height=320)
    {
        Yii::app()->getController()->widget('application.extensions.EFlowPlayer.EFlowPlayer', array(
            'flv'=>$src,
            'htmlOptions'=>array(
                'style'=>'width: '.$width.'px; height: '.$height.'px;',
            ),
        ));
    }
    
    public static function mediaPlayer($src, $width=300, $height=20)
    {
        $m='<object type="application/x-shockwave-flash" data="'.Yii::app()->theme->baseUrl.'/js/player_mp3_maxi.swf" width="'.$width.'" height="'.$height.'">
            <param name="movie" value="'.Yii::app()->theme->baseUrl.'/js/player_mp3_maxi.swf" />
            <param name="FlashVars" value="mp3='.urlencode($src).'&width='.$width.'&height='.$height.'&showstop=1&showvolume=1&volume=200&bgcolor=0xDAE7E8" />
            <audio width="'.$width.'" height="'.$height.'" src="'.$src.'" preload controls></audio>
        </object>';
        echo $m;
    }

    public static function mp4($src)
    {
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/johndyer/mediaelement-and-player.min.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl.'/base/js/johndyer/mediaelementplayer.min.css');
        $cs->registerScript('mediaelementplayer', '$("video").mediaelementplayer();');
        echo '<video src="'.$src.'" type="video/mp4" controls="controls" preload="none" width="600" height="400"></video>';
    }
}