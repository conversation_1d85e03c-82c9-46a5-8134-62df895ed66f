<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
    </button>
    <h4 class="modal-title" id="exampleModalLabel">查看退费信息</h4>
</div>
<?php
$form = $this->beginWidget('CActiveForm', array(
    'id' => 'course-forms',
    'enableAjaxValidation' => false,
    'htmlOptions' => array('class' => 'form-horizontal', 'role' => 'form'),
)); ?>
<div class="modal-body">
    <!--课程名字-->
    <div class="form-group">
        <label class="col-xs-2 text-right"> # </label>

        <div class="col-xs-9">
            <div><?php echo $model->id ?></div>
        </div>
    </div>

    <div class="form-group">
        <label class="col-xs-2 text-right"><?php echo $form->labelEx($model, 'course_id'); ?></label>

        <div class="col-xs-9">
            <div><?php echo $model->asaCourse->title ?></div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 text-right"><?php echo Yii::t('child', '孩子姓名'); ?></label>

        <div class="col-xs-9">
            <div><?php echo ($childsName) ? $childsName->getChildName() : "" ?></div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 text-right"><?php echo Yii::t('child', '班级'); ?></label>

        <div class="col-xs-9">
            <div><?php echo ($childClass) ? $childClass->title : "" ?></div>
        </div>
    </div>
    <!--退费节数-->
    <div class="form-group">
        <label class="col-xs-2 text-right"><?php echo $form->labelEx($model, 'refund_class_count'); ?></label>

        <div class="col-xs-9">
            <div><?php echo $model->refund_class_count ?> 节课</div>
        </div>
    </div>
    <!--退费金额-->
    <div class="form-group">
        <label class="col-xs-2 text-right"><?php echo $form->labelEx($model, 'refund_total_amount'); ?></label>

        <div class="col-xs-9">
            <div><?php echo $model->refund_total_amount ?> 元</div>
        </div>
    </div>
    <!--退费生效日期-->
    <div class="form-group">
        <label class="col-xs-2 text-right"><?php echo $form->labelEx($model, 'dropout_date'); ?></label>

        <div class="col-xs-9">
            <div><?php echo date("Y-m-d", $model->dropout_date) ?></div>
        </div>
    </div>
    <!--在什么时候退费 开课前，开课中，特殊-->
    <div class="form-group">
        <label class="col-xs-2 text-right"><?php echo $form->labelEx($model, 'refund_type'); ?></label>
        
        <div class="col-xs-9">
            <div><?php echo $reFindType[$model->refund_type] ?></div>
        </div>
    </div>
    <!--退费原因-->
    <div class="form-group">
        <label class="col-xs-2 text-right"><?php echo $form->labelEx($model, 'refund_reason'); ?></label>
        
        <div class="col-xs-9">
            <div><?php echo $refundReason[$model->refund_reason] ?></div>
        </div>
    </div>
    <!--退费说明-->
    <div class="form-group">
        <label class="col-xs-2 text-right"><?php echo $form->labelEx($model, 'memo'); ?></label>

        <div class="col-xs-9">
            <div><?php echo $model->memo ?></div>
        </div>
    </div>
    <!--退费返回路径-->
    <div class="form-group">
        <label class="col-xs-2 text-right"><?php echo $form->labelEx($model, 'refund_method'); ?></label>

        <div class="col-xs-9">
            <div><?php echo $reFindList['refundMethod'][$model->refund_method]['cn']; ?></div>
        </div>
    </div>
    <!--存款人信息-->
    <div class="form-group">
        <label class="col-xs-2 text-right"><?php echo $form->labelEx($model, 'payee_info'); ?></label>

        <div class="col-xs-9">
            <table class="table table-bordered">
                <tbody>
                    <tr>
                        <td><?php echo Yii::t('asa', 'bank'); ?></td>
                        <td><?php echo Yii::t('asa', 'accountName'); ?></td>
                        <td><?php echo Yii::t('asa', 'accountNumber'); ?></td>
                    </tr>
                    <tr>
                        <?php $info = CJSON::decode($model->payee_info);
                        foreach ($info as $k => $v) {
                            echo "<td>" . $v . "</td>";
                        }
                        ?>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- 退费详细信息 -->
    <?php if (in_array($model->refund_type, array(4, 5)) && $exchengeRefund) : ?>
        <div class="form-group">
            <label class="col-xs-2 text-right">换课信息</label>

            <div class="col-xs-9">
                <h4>旧课信息</h4>
                <table class="table table-bordered col-xs-12">
                    <tr>
                        <td class="col-xs-2"><?php echo Yii::t('asa', '课程名称'); ?></td>
                        <td class="col-xs-2"><?php echo Yii::t('asa', '课程课时和价格'); ?></td>
                        <td class="col-xs-2"><?php echo Yii::t('asa', '已上课程'); ?></td>
                        <td class="col-xs-2"><?php echo Yii::t('asa', '已退课时'); ?></td>
                        <td class="col-xs-2"><?php echo Yii::t('asa', '旧课余额'); ?></td>
                    </tr>
                    <tr>
                        <td class="col-xs-2"><?php echo $exchengeRefund['oldCourseTitle']; ?></td>
                        <td class="col-xs-2"><?php echo $exchengeRefund['oldCourse']; ?></td>
                        <td class="col-xs-2"><?php echo $exchengeRefund['oldCourseNum']; ?></td>
                        <td class="col-xs-2"><?php echo $exchengeRefund['oldRefundCount']; ?></td>
                        <td class="col-xs-2"><?php echo $exchengeRefund['oldAmount']; ?></td>
                    </tr>
                </table>
                <h4>新课信息</h4>
                <table class="table table-bordered col-xs-12">
                    <tr>
                        <td class="col-xs-2"><?php echo Yii::t('asa', '新课名称'); ?></td>
                        <td class="col-xs-2"><?php echo Yii::t('asa', '课程课时和价格'); ?></td>
                        <td class="col-xs-2"></td>
                        <td class="col-xs-2"></td>
                        <td class="col-xs-2"><?php echo Yii::t('asa', ' 新课所需金额'); ?></td>
                    </tr>
                    <tr>
                        <td class="col-xs-2"><?php echo $exchengeRefund['newCourseTitle']->getTitle(); ?></td>
                        <td class="col-xs-2"><?php echo $exchengeRefund['newCourse']; ?></td>
                        <td class="col-xs-2"></td>
                        <td class="col-xs-2"></td>
                        <td class="col-xs-2"><?php echo $exchengeRefund['newCourseNum']; ?></td>
                    </tr>
                </table>

                <div>
                    <span>价格差额</span>
                    <span class="pull-right"><?php echo $exchengeRefund['difference'] ?></span>
                </div>
            </div>
        </div>
    <?php endif; ?>
    <!--退费文件-->
    <div class="form-group">
        <label class="col-xs-2 text-right"><?php echo $form->labelEx($model, 'refund_files'); ?></label>

        <div class="col-xs-9">
            <?php
            foreach (CJSON::decode($model->refund_files) as $file) {
                echo CHtml::link('点击查看图片', array('downloadsRefund', 'fileName' => $file), array('class' => 'btn btn-info btn-xs', 'target' => "_blank"));
            ?>
            <?php } ?>
        </div>
    </div>
    <!--银行回执单-->
    <div class="form-group">
        <label class="col-xs-2 text-right"><?php echo $form->labelEx($model, 'receipt_no'); ?></label>

        <div class="col-xs-9">
            <div><?php echo $model->receipt_no ?></div>
        </div>
    </div>
    <!--操作时间-->
    <div class="form-group">
        <label class="col-xs-2 text-right"><?php echo $form->labelEx($model, 'updated'); ?></label>

        <div class="col-xs-3">
            <div><?php echo date('Y-m-d H:i:s', $model->updated) ?></div>
        </div>

        <label class="col-xs-2 text-right"><?php echo $form->labelEx($model, 'updated_by'); ?></label>

        <div class="col-xs-3">
            <div><?php echo ($updatedName[$model->updated_by]) ? $updatedName[$model->updated_by]->getName() : "" ?></div>
        </div>
    </div>

    <div class="form-group">
        <label class="col-xs-2 text-right"><?php echo $form->labelEx($model, 'updated_confirm'); ?></label>

        <div class="col-xs-3">
            <div><?php echo ($model->updated_confirm) ? date('Y-m-d H:i:s', $model->updated_confirm) : "" ?></div>
        </div>

        <label class="col-xs-2 text-right"><?php echo $form->labelEx($model, 'updated_confirm_by'); ?></label>

        <div class="col-xs-3">
            <div><?php echo ($updatedName[$model->updated_confirm_by]) ? $updatedName[$model->updated_confirm_by]->getName() : "" ?></div>
        </div>
    </div>

    <div class="form-group">
        <label class="col-xs-2 text-right"><?php echo $form->labelEx($model, 'updated_done'); ?></label>

        <div class="col-xs-3">
            <div><?php echo ($model->updated_done) ? date('Y-m-d H:i:s', $model->updated_done) : "" ?></div>
        </div>

        <label class="col-xs-2 text-right"><?php echo $form->labelEx($model, 'updated_done_by'); ?></label>

        <div class="col-xs-3">
            <div><?php echo ($updatedName[$model->updated_done_by]) ? $updatedName[$model->updated_done_by]->getName() : "" ?></div>
        </div>
    </div>
    <?php if ($model->status != 1 && $model->refund_method != 1): ?>
        <div class="modal-footer">
            <h4 class="text-center">个人账户退费会在每周五统一操作，银行卡退费请联系财务。</h4>
        </div>
    <?php endif; ?>
    <?php if ($model->status == 1) : ?>
            <?php if ($access): ?>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" onclick="refund(this, '<?php echo $model->id; ?>')"><?php echo Yii::t('global', '通过'); ?></button>
                    <button type="button" class="btn btn-danger" onclick="cancel(this, '<?php echo $model->id; ?>')"><?php echo Yii::t('global', '驳回'); ?></button>
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
                </div>
            <?php else: ?>
                <div class="modal-footer">
                    <h4 class="text-center">等待园长或校园课后课负责人审批</h4>
                </div>
            <?php endif; ?>
    <?php endif; ?>
</div>
<?php $this->endWidget(); ?>
<script>
    head.Util.ajaxDel();

    function cb(data) {
        $('#modal').modal('hide');
        if (data.state == 'success') {
            resultTip({"msg": data.message});
        } else {
            resultTip({"msg": data.message, "error": 1});
        }

        $.fn.yiiGridView.update('refund');
        $.fn.yiiGridView.update('review');
        $.fn.yiiGridView.update('refundIntermediate');
    }

    function refund(btn, id) {
        $(btn).addClass('disabled');
        $.ajax({
            url: '<?php echo $this->createUrl('refundreview'); ?>',
            data: {
                status: 4,
                id: id
            },
            type: 'post',
            dataType: 'json',
            success: function(data) {
                if (data.state == 'success') {
                    cb(data)
                } else {
                    cb(data)
                }
                $(btn).removeClass('disabled');
            },
            error: function(data) {
                $(btn).removeClass('disabled');
            }
        });
    }

    function cancel(btn, id) {
        $(btn).addClass('disabled');
        $.ajax({
            url: '<?php echo $this->createUrl('refundreview'); ?>',
            data: {
                status: 2,
                id: id
            },
            type: 'post',
            dataType: 'json',
            success: function(data) {
                if (data.state == 'success') {
                    cb(data)
                } else {
                    cb(data)
                }
                $(btn).removeClass('disabled');
            },
            error: function(data) {
                $(btn).removeClass('disabled');
            }
        });
    }
</script>