<?php

class DefaultController extends WeChatBasedController
{
	private $iniEmailProduction = true;
	
	//初次验证开发者时需注释
	public function beforeAction($action){
		if(Yii::app()->request->isPostRequest){
			if($this->wechatObj->validateRequest()){
				if(
					!is_null($this->userMsg) &&
					strtolower($this->userMsg->MsgType)=='event' &&
					!is_null($this->userMsg->Event) &&
					in_array(strtoupper($this->userMsg->Event), array('SUBSCRIBE', 'SCAN'))
				){
					return true;
				}
				elseif( $this->isBound < self::STAT_BOUND ){
					$this->welcome('..');
					Yii::app()->end();
				}else{
					return true;
				}
			}
		}
		return false;
	}
		
	public function actionIndex()
	{
		//$this->wechatObj->initialValidate();
		if(!is_null($this->userMsg)){
			switch(strtolower($this->userMsg->MsgType)){
				case 'image':
					$this->processImage();
				    break;
				case 'text':
					$this->processText();
				    break;
                case 'voice':
                    $this->processVoice();
                    break;
                case 'video':
                    $this->processVideo();
                    break;
				case 'event':
					$this->processEvent();	
				    break;
			}
			//后续处理，触发微信高级接口
			try{
				$this->updateLastContact();
			}catch(Exception $e){
				
			}
		}
	}

    public function actionIndex2()
    {
//        $this->wechatObj->initialValidate();die;
        if(!is_null($this->userMsg)){
            switch(strtolower($this->userMsg->MsgType)){
                case 'image':
                    $this->processImage();
                    break;
                case 'text':
                    $this->processText();
                    break;
                case 'voice':
                    $this->processVoice();
                    break;
                case 'video':
                    $this->processVideo();
                    break;
                case 'event':
                    $this->processEvent();
                    break;
            }
            //后续处理，触发微信高级接口
            try{
                $this->updateLastContact();
            }catch(Exception $e){

            }
        }
    }

    private function processVoice(){
        $this->text("我们暂时不能处理语音消息，请使用本服务号提供的菜单进行操作，谢谢！");
    }
	
    private function processVideo(){
        $this->text("我们暂时不能处理视频消息，请使用本服务号提供的菜单进行操作，谢谢！");
    }

	private function processText(){
		if(!empty($this->userMsg->Content)){
			switch(strtolower(trim($this->userMsg->Content))){
				case 'qxbd': //取消帐号绑定
					$this->text(MimsAPI::unBindUser($this->userMsg->FromUserName));
				break;
				case 'text':
					$this->text("测试消息");
				break;
				case 'news':
					$this->news();
				break;
				case 'welcome':
					$this->welcome('>');
				break;
				case 'lunch':
					$this->lunchMenu();
				break;
				case 'sztx':
					$index = 1; //单个孩子
					$this->setAvatar($index);
				break;
				case 'xhbm': //小伙伴们
					$this->classMates();
				break;
				default:
					if(!$this->parseUserInput(trim($this->userMsg->Content))){
						$this->text("我们暂时不能处理您的消息，请使用本服务号提供的菜单进行操作。英文字符，包括#号，请不要使用全角字符。");
					}
				break;
			}
		}
	}
	
	private function processImage(){
		//$text = $this->userMsg->FromUserName .  '  ' . $this->userMsg->PicUrl;
		$result = MimsAPI::saveImageUploads($this->userMsg->FromUserName, $this->userMsg->PicUrl);
		if($result){
			$this->processImageInstruction();
		}
		//$this->text($text . ' ' . $result);
	}
	
	private function processImageInstruction(){
		if(count($this->activeChildIds) > 1){
			$instruction = "图片已经收到！如果您希望用这张图片作为孩子在幼儿园系统的头像，请输入\r\n\r\nSZTX#孩子序号\r\n\r\n";
			$instruction .= "孩子序号：\r\n";
			$i = 1;
			foreach($this->activeChildIds as $childid){
				$child = $this->myChildObjs[$childid];
				$instruction .= sprintf("★ %s %s\r\n", $i, $this->getChildTitle($child, '', true));
				$i++;
			}
			$this->text($instruction);
		}else{
			$this->text("图片已经收到！如果您希望用这张图片作为孩子在幼儿园系统的头像，请输入设置头像命令：\r\nSZTX");
		}
	}
	
	private function processEvent(){
		switch(strtoupper($this->userMsg->Event)){
			case 'SUBSCRIBE':
				if($this->userMsg->EventKey){
					$result = MimsAPI::processQRScene($this->userMsg->FromUserName, $this->userMsg->EventKey);
					
					$this->processCustomQrCodeResult($result);
					
					/*
					if($result == MimsAPI::BOUND_USER_DONE){
						$text = "★ 账户绑定成功 \r\n";
						$text.= "请使用“帐号账单”中的“绑定艾毅在线帐号”查看帐号信息";
					}elseif($result == MimsAPI::BOUND_USER_MAXEXCEEDED){
						$text = "★ 此帐号绑定的微信已达上限 \r\n";
						$text.= "为保证账户安全，请点击任意菜单按照提示手动输入帐号信息进行绑定";					
					}else{
						$text = '绑定失败，请重试' . $result;
					}
					$this->text($text);
					*/
				}else{
					$this->welcome('>>');
				}
			break;
			case 'SCAN':
				if($this->userMsg->EventKey){
					$result = MimsAPI::rebind($this->userMsg->FromUserName, $this->userMsg->EventKey);

					$this->processCustomQrCodeResult($result);
					
					/*
					switch($result){
						case MimsAPI::BOUND_USER_MAXEXCEEDED:
							$text = "★ 此帐号绑定的微信已达上限 \r\n";
							$text.= "为保证账户安全，请点击任意菜单按照提示手动输入帐号信息进行绑定";
						break;
						case MimsAPI::BOUND_USER_ALREADY:
							$text = "★ 已经绑定过此账户 \r\n";
							$text.= "请使用“帐号账单”中的“绑定艾毅在线帐号”查看帐号信息";
						break;
						case MimsAPI::BOUND_USER_REPLACED:
							$text = "★ 已经绑定新账户，先前绑定的账户失效 \r\n";
							$text.= "请使用“帐号账单”中的“绑定艾毅在线帐号”查看帐号信息";
						break;
						case MimsAPI::BOUND_USER_DONE:
							$text = "★ 账户绑定成功 \r\n";
							$text.= "请使用“帐号账单”中的“绑定艾毅在线帐号”查看帐号信息";
						break;
						default:
							$text = "★ 账户绑定失败 \r\n";
							$text.= $result;
						break;
					}
					$this->text($text);
					*/
				}
			break;
			case 'UNSUBSCRIBE':
				$this->unsubscribe();
			break;
			case 'CLICK':
				switch($this->userMsg->EventKey){
					case 'ACCOUNT_BINDING':
						$childInfo  = "您已经绑定幼儿园系统帐号"."\r\n";
						$childInfo .= "孩子信息："."\r\n";
						foreach($this->myChildObjs as $child){
							$childInfo .= $this->getChildTitle($child,'★');
						}
						$this->text($childInfo);
					break;
					case 'ACCOUNT_UNBIND':
						$this->text("您确定要取消绑定吗？\r\n请输入 QXBD 继续。");
					break;
					case 'SPT_BALANCE':
						$childIds = array_keys($this->myChildObjs);
						$balances = MimsAPI::getBalance($childIds);
						$text = "";
						foreach($this->myChildObjs as $childid => $child){
							$text .= $this->getChildTitle($child, '');
							$text .= sprintf('★ 个人余额：%s元', isset($balances['credit'][$childid])? $balances['credit'][$childid] : 0) . "\r\n";
							
							$depositStr = "";
							foreach($balances['deposit'][$childid] as $year=>$deposit){
								if($deposit['amount']>0.001){
									if(isset($deposit['frozen']) && $deposit['frozen'] > 0.001 ){
										$depositStr .= sprintf('☆ %d学年：%s元（首次账单将使用 %s）', $year, $deposit['amount'], $deposit['frozen']);
									}else{
										$depositStr .= sprintf('☆ %d学年：%s元', $year, $deposit['amount']);
									}
									$depositStr .="\r\n";
								}
							}
							if($depositStr != ''){
								$text .= '★ 预缴学费：' . "\r\n" . $depositStr;
							}
						}
						$this->text($text);
					break;
					case 'SPT_UNPAID':
					case 'SPT_RECENT_PAID':
						$limit = 10;
						$invoices = MimsAPI::getInvoices(array_keys($this->myChildObjs), $this->userMsg->EventKey, $limit);
						$text = '';
						$cCount = count($this->myChildObjs);
						$cName = array();
						if($cCount>1){
							foreach($this->myChildObjs as $childid => $child){
								$cName[$childid] = $this->getChildTitle($child, '', true);
							}
						}
						foreach($invoices as $invoice){
							if($invoice->original_amount != $invoice->amount ){
								$text .= sprintf('★ %s %s 元，需支付 %s 元', $invoice->title, $invoice->original_amount, $invoice->amount);
							}
							else{
								$text .= sprintf('★ %s %s 元', $invoice->title, $invoice->original_amount);
							}
							if($cCount>1){
								$text .= ' ' . $cName[$invoice->childid];
							}
							
							$text .= "\r\n";
						}
						if($text == ""){
							$text .= ($this->userMsg->EventKey == 'SPT_UNPAID') ? '★ 没有未支付的账单，感谢您对校园工作的支持！' : '没有找到账单信息';
						}
						if(count($invoices)==$limit){
							$text .= sprintf("◆只显示%s条信息，更多信息请登录幼儿园管理系统查看。\r\n", $limit);
						}
						$this->text($text);
					break;
					case 'MINE_FAPIAO':
						$this->fapiaoWelcome();
					break;
					case 'MINE_LUNCH_CANCEL':
					case 'MINE_LUNCH_RECOVER':
						$this->lunchWelcome($this->userMsg->EventKey);
					break;
					case 'MINE_CONTACT':
						$this->contactCampusWelcome();
					case 'MINE_JOURNAL':
						$this->recentJournals(5);
					break;
					case 'LUNCH_MENU':
						$this->lunchMenu(5);
					break;
					case 'MINE_AVATAR':
						$text = "★请用微信发送一张头像照片，上传完成之后再按照提示进行操作！";
						$this->text($text);
					break;
					case 'MINE_CLASSMATES':
						$this->classMates();
					break;
					default:
						$this->text("系统开发中，敬请期待！" . $this->userMsg->EventKey);
					break;
				}
			break;
			default:
				$this->text($this->userMsg->Event);
			break;
		}
	}
	
	public function actionGetRaw($id){
		$fromCache = Yii::app()->cache->get($id);
		echo $fromCache;
	}
	
	private function parseUserInput($userInput){
		$pattern = '/^([a-zA-Z]{4})#(.*)/';
		$result = preg_match($pattern,$userInput,$matches);
		if($result === 0){
			return $result;
		}
		
		$command = strtolower($matches[1]);
		$content = $matches[2];
		
		switch($command){
			//发票抬头
			case 'fptt':
				$subPattern = '/^([0-9]{1})#(.*)/';
				$multiChildOp = preg_match($subPattern, $content, $subMatches);
				if($multiChildOp){
					$childIndex = $subMatches[1];
					$fapiaoTitle = $subMatches[2];
				}else{
					$childIndex = 0;
					$fapiaoTitle = $content;
				}
				$this->processFapiaoTitle($fapiaoTitle, $childIndex);
			break;
			
			case 'qxyc': //取消用餐
			case 'hfyc': //恢复用餐
				$subPattern = '/^([0-9]{1})#(.*)/';
				$multiChildOp = preg_match($subPattern, $content, $subMatches);

				if($multiChildOp){
					$childIndex = $subMatches[1] - 1;
					$targetDate = $subMatches[2];
				}else{
					$childIndex = 0;
					$targetDate = $content;
				}
				$type = ($command == 'qxyc')? 'MINE_LUNCH_CANCEL' : 'MINE_LUNCH_RECOVER';
				
				if(!isset($this->activeChildIds[$childIndex]) || strtotime($targetDate) === false ){
					$this->lunchWelcome($type);
				}else{
					$child = $this->myChildObjs[$this->activeChildIds[$childIndex]];
					$text = $this->getChildTitle($child, '★', true) . "\r\n";
					if($command == 'qxyc'){
						$ret = MimsAPI::lunchCancel($child->childid, $targetDate, $this->wechatUser->userid);
						if($ret === 0){
							$this->sendLunchEmail($child, $targetDate,'cancel');
						
							$text .= "成功◆取消◆用餐！\r\n目标日期为\r\n" . date('Y-m-d', strtotime($targetDate));
							$this->text($text);
						}else{
							$text .= $ret;
							$this->text($text);
						}
					}
					else{
						$ret = MimsAPI::lunchRecover($child->childid, $targetDate, $this->wechatUser->userid);
						if($ret === 0){
							$this->sendLunchEmail($child, $targetDate,'recover');
							
							$text .= "成功◆恢复◆用餐！\r\n目标日期为\r\n" . date('Y-m-d', strtotime($targetDate));
							$this->text($text);
						}else{
							$text .= $ret;
							$this->text($text);
						}
					}
				}
			break;
			
			//联系校园
			case 'lxxy':				
				$child = array_shift($this->myChildObjs);
				$campus = Branch::model()->with('info')->findByPk($child->schoolid);
				
                $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
                $mailer->Subject = Mims::unIvy() ? sprintf(Yii::app()->params['ownerConfig']['name_cn'].'【微信助手 - %s】孩子信息：%s（联系校园）' , $campus->abb, $child->getChildName()) : sprintf('【艾毅在线微信助手 - %s】孩子信息：%s（联系校园）' , $campus->abb, $child->getChildName());
				
				$pEmails = $this->getParentsEmail($child);
				
				//$content .= implode(',',$pEmails);
				
                $mailer->AddAddress($campus->info->support_email);
				$mailer->AddReplyTo($campus->info->support_email);
				foreach($pEmails as $email){
					$mailer->AddCC($email);
				}
				
                $mailer->iniMail($this->iniEmailProduction, 'sendmail'); // 此行代码要放到AddAddress, AddCC方法下面
                $mailer->getView('wechatContact', array('userMsg'=>$content), 'wechat');
                if ($mailer->Send()) {
					$this->text("您的信息已经发送至校园支持团队，内容如下：\r\n" . $content );
				}else{
					$this->text("系统错误，请稍后再试\r\n");
				}
			break;
			//设置头像
			case 'sztx':
				$index = ( intval($content) ) ? intval($content) : 1;
				$this->setAvatar($index);
			break;
		}
	}
	
	private function setAvatar($index=null){
	
		if(count($this->activeChildIds) == 0 ){
			$this->text("对不起，孩子当前已经不在读，请用电脑操作，谢谢！");	
		}
	
		$wechatUpload = MimsAPI::getLastUploadByOpenId($this->userMsg->FromUserName);
		if(empty($wechatUpload)){
			$this->text("请先微信发送一张孩子头像的照片");	
		}else{
			if($wechatUpload->status){
				$this->text("您发送的照片已经设置为头像啦！如果要重新设置，请重新发送照片。");	
			}else{
				$index = intval($index) ? intval($index) : 1;
				
				$text = "正在处理中，更新到网站可能需要花费几分钟，请稍后查看，谢谢！";
				$this->text($text);

				//异步处理开始
				$ch = curl_init();
				$data = array(
					"index" => $index,
					"id" => $wechatUpload->id,
					"openid" => $wechatUpload->openid,
					"key" => md5($index  . $wechatUpload->id . $wechatUpload->openid . $this->securityKey),
				);
				//curl_setopt($ch, CURLOPT_URL, $this->createUrl("/weixin/user/uploadChildAvatar")); 
				curl_setopt($ch, CURLOPT_URL, $this->createUrl("/weixin/user/uploadChildAvatar",$data)); 
				//curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
				curl_setopt($ch, CURLOPT_TIMEOUT, 1);
				curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
				curl_exec($ch);
				curl_close($ch);
				//异步处理结束
			}
		}
		$text = "对不起，系统开发中". intval($index) . "\r\n";
		$this->text($text);	
	}
	
	private function getParentsEmail($child){
		$emails = array();
		$pids = array();
		if($child->fid) $pids[] = $child->fid;
		if($child->mid) $pids[] = $child->mid;
		$parents = User::model()->findAllByPk($pids, '`level`>:level', array(':level'=>0));
		if(!empty($parents)){
			foreach($parents as $parent){
				$emails[] = $parent->email;
			}
		}
		return $emails;		
	}
	
	private function sendLunchEmail($child, $targetDate, $action){
		$campus = Branch::model()->with('info')->findByPk($child->schoolid);
		$mailer = Yii::createComponent('common.extensions.mailer.EMailer');
		$actTitle = ($action=='cancel') ? '取消用餐' : '恢复用餐';
		$mailer->Subject = Mims::unIvy() ? sprintf(Yii::app()->params['ownerConfig']['name_cn'].'【微信助手 - %s】孩子信息：%s（%s）' , $campus->abb, $child->getChildName(), $actTitle) : sprintf('【艾毅在线微信助手 - %s】孩子信息：%s（%s）' , $campus->abb, $child->getChildName(), $actTitle);
		$pEmails = $this->getParentsEmail($child);
		$mailer->AddAddress($campus->info->support_email);
		$mailer->AddReplyTo($campus->info->support_email);
		foreach($pEmails as $_pe){
			$mailer->AddCC($_pe);
		}
		
		$mailer->iniMail($this->iniEmailProduction, 'sendmail'); // 此行代码要放到AddAddress, AddCC方法下面
		$mailer->getView('lunchCancel', array(
				'child'=>$child,
				'targetDate'=>date('Y-m-d', strtotime($targetDate)),
				'action'=>$action,
				'supportEmail'=> $campus->info->support_email,
				'otherinfo' => '',//implode(',',$pEmails)
			), 'wechat');
		return $mailer->Send();	
	}
	
	private function parseContent($reg, $content, &$matches){
		return preg_match($reg, $content, $matches);
	}
	
	private function getChildTitle($childObj, $intend='　',$nameOnly=false){
		$result = $intend . $childObj->getChildName();
		if(!$nameOnly){
			$result .= ' ' . Mims::formatDateTime($childObj->birthday, 'medium') . ' ' . ( ($childObj->gender == 1) ? "男" : "女" ) . "\r\n";
		}
		return $result;
	}
	
	/**
	 * 修改发票抬头时的引导信息
	 * @return	Object		Description
	 */
	private function fapiaoWelcome(){
		$cCount = count($this->myChildObjs);
		$text = "";
		$i=1;
		foreach($this->myChildObjs as $child){
			$text .= sprintf("★ %s %s 的发票抬头：\r\n %s", $i, $this->getChildTitle($child, '', true), (trim($child->invoice_title)=="") ? "未设置" : $child->invoice_title);
			$text .= "\r\n";
			$i++;
		}
		$text .= "----------------\r\n";
		if($cCount < 2){
			$text .= "修改发票抬头，请输入 FPTT#发票抬头名称 进行修改\r\n";
			$text .= "比如 FPTT#xxx公司\r\n";
		}else{
			$text .= "修改发票抬头，请输入 FPTT#孩子序号#发票抬头名称 进行修改\r\n";
			$text .= "比如 FPTT#1#xxx公司\r\n";
			$text .= "孩子序号请按照上面孩子姓名前的序号进行编写，序号为0表示所有孩子都做相同修改\r\n";
		}
		
		$this->text($text);	
	}
	
	/**
	 * Summary 修改发票标题
	 * @param	String	$title		Description
	 * @param	Number	$childIndex	Description
	 * @return	Object				Description
	 */
	private function processFapiaoTitle($title="", $childIndex=0){
		$childIds = array_keys($this->myChildObjs);
		if(trim($title)!=""){
			if( $childIndex > 0 ){
				if(count($childIds)>1){
					if(isset($this->myChildObjs[$childIds[$childIndex-1]])){
						$child = $this->myChildObjs[$childIds[$childIndex-1]];
						$child->invoice_title = ($title);
						$child->save();
					}
				}
			}else{
				foreach($this->myChildObjs as $child){
					$child->invoice_title = ($title);
					$child->save();
				}
			}
		}
		$this->fapiaoWelcome();
	}
	
	/**
	 * Summary 联系校园介绍
	 * @return	Object		Description
	 */
	private function contactCampusWelcome(){
		$text = "联系校园，请输入 LXXY#内容\r\n";
		$this->text($text);
	}
	
	private function lunchWelcome($type){
		//状态100以下的孩子才进行处理
		$text = "";
		if(count($this->activeChildIds) < 2){
			foreach($this->activeChildIds as $childid){
				$child = $this->myChildObjs[$childid];
				$text .= sprintf("★ %s\r\n", $this->getChildTitle($child, '', true));
				$i++;
			}
			switch($type){
				case 'MINE_LUNCH_CANCEL':
					$text .= "请输入 QXYC#八位数目标日期\r\n";
					$text .= "◆如 QXYC#20130601 表示取消2013年6月1日的用餐";
				break;
				case 'MINE_LUNCH_RECOVER':
					$text .= "请输入 HFYC#八位数目标日期\r\n";
					$text .= "◆如 HFYC#20130601 表示恢复2013年6月1日的已取消用餐";
				break;
			}
		}else{
			$i=1;
			foreach($this->activeChildIds as $childid){
				$child = $this->myChildObjs[$childid];
				$text .= sprintf("★ %s %s\r\n", $i, $this->getChildTitle($child, '', true));
				$i++;
			}
			switch($type){
				case 'MINE_LUNCH_CANCEL':
					$text .= "请输入 QXYC#孩子序号#八位数目标日期\r\n";
					$text .= "◆如 QXYC#1#20130601 表示取消序号为1的孩子在2013年6月1日的用餐";
				break;
				case 'MINE_LUNCH_RECOVER':
					$text .= "请输入 HFYC#孩子序号#八位数目标日期\r\n";
					$text .= "◆如 HFYC#1#20130601 表示恢复序号为1孩子在2013年6月1日的已取消用餐";
				break;
			}
		}
		$this->text($text);
	}
	
	private function recentJournals($limit=0){
		$notes = MimsAPI::getRecentJournals(array_keys($this->myChildObjs), $limit);
		$this->journals($notes);
	}
	
	public function text($str="Testing", $endApp=true){
		$textTpl = "<xml>
					<ToUserName><![CDATA[%s]]></ToUserName>
					<FromUserName><![CDATA[%s]]></FromUserName>
					<CreateTime>%s</CreateTime>
					<MsgType><![CDATA[%s]]></MsgType>
					<Content><![CDATA[%s]]></Content>
					<FuncFlag>0</FuncFlag>
					</xml>";             
		$msgType = "text";
		$resultStr = sprintf($textTpl, $this->userMsg->FromUserName, $this->userMsg->ToUserName, time(), $msgType, $str);
		Yii::app()->cache->set("weixinreply", $resultStr);
		echo $resultStr;
		//if($endApp){
		//	Yii::app()->end();
		//}
	}
	
	public function link($str="Testing"){
		$textTpl = "<xml>
					<ToUserName><![CDATA[%s]]></ToUserName>
					<FromUserName><![CDATA[%s]]></FromUserName>
					<CreateTime>%s</CreateTime>
					<MsgType><![CDATA[%s]]></MsgType>
					<Content><![CDATA[<a href='http://www.ivyschools.com'>%s</a>]]></Content>
					<FuncFlag>0</FuncFlag>
					</xml>";             
		$msgType = "text";
		$resultStr = sprintf($textTpl, $this->userMsg->FromUserName, $this->userMsg->ToUserName, time(), $msgType, $str);
		Yii::app()->cache->set("weixinreply", $resultStr);
		echo $resultStr;
	}
	
	public function welcome($flag='。'){
		$checkKey = md5(substr($this->userMsg->FromUserName, 3, 3) . $this->securityKey);
		$newsTpl = "<xml>
			<ToUserName><![CDATA[%s]]></ToUserName>
			<FromUserName><![CDATA[%s]]></FromUserName>
			<CreateTime>%s</CreateTime>
			<MsgType><![CDATA[%s]]></MsgType>
			<ArticleCount>1</ArticleCount>
			<Articles>
			<item>
			<Title><![CDATA[%s]]></Title> 
			<Description><![CDATA[%s]]></Description>
			<PicUrl><![CDATA[%s]]></PicUrl>
			<Url><![CDATA[%s]]></Url>
			</item>
			</Articles>
			</xml> 
		";
		$msgType = "news";
        if(Mims::unIvy()){
            $news = array(
                '0'=>array(
                    'title'=>Yii::app()->params['ownerConfig']['wechatWelcomeTitle'].$flag,
                    'picurl'=>Yii::app()->params['ownerConfig']['wechatWelcomePic'],
                    'url'=>$this->createUrl('//weixin/user/index', array('mk'=>$checkKey, 'md'=>$this->userMsg->FromUserName)),
                )
            );
        }
        else{
            $news = array(
                '0'=>array(
                    'title'=>'本号为艾毅微信服务号，旨在为艾毅的家长提供更好的服务，请点击绑定艾毅在线的账户'.$flag,
                    'picurl'=>'http://www.ivyonline.cn/themes/mobile/images/welcome.jpg?',
                    'url'=>$this->createUrl('//weixin/user/index', array('mk'=>$checkKey, 'md'=>$this->userMsg->FromUserName)),
                )
            );
        }
		$contentStr = "http://www.ivyschools.com";
		$resultStr = sprintf($newsTpl, $this->userMsg->FromUserName, $this->userMsg->ToUserName, time(), $msgType,
							$news[0]['title'],
							'',
							$news[0]['picurl'],
							$news[0]['url']
							);
		echo $resultStr;	
	}
	
	public function unsubscribe(){
		MimsAPI::unBindUser($this->userMsg->FromUserName);
	}
	
	public function lunchMenu($count=4){
		if(count($this->activeChildIds)){
			$childId = $this->activeChildIds[0];
			$branchId = $this->myChildObjs[$childId]->schoolid;
			
			Yii::import('common.models.lunchmenu.*');
			$menuLinks = MimsAPI::getRecentMenus($branchId, $count);
							
			$this->displayLunchMenus($menuLinks);
			
		}else{
			$this->text("没有找到当前就读信息");
		}
	}
	
	
	public function displayLunchMenus($menuLinks){
		$number = count($menuLinks);
		if(!$number){
			$this->text('敬请见谅！还没有找到完成的每周食谱');
		}
		
		$data = array();
        $i=0;
		$time = time();
		foreach($menuLinks as $menuLink){			
			$data[$i] = array(
				'title' => sprintf("第%s周 %s\r\n%s - %s", $menuLink->week_num, $menuLink->branch->title, CommonUtils::formatDateTime($menuLink->monday_timestamp), CommonUtils::formatDateTime($menuLink->monday_timestamp + 5 * 24 * 3600)),
				'description'=>'',
				'picurl' => '',
				'url'=> MimsAPI::createLunchMenuUrl($menuLink, $time, $this->userMsg->FromUserName, $this->securityKey),
			);
			$i++;
		}
		
		
		
		$lunchMenuStr = sprintf("<xml>
			<ToUserName><![CDATA[%s]]></ToUserName>
			<FromUserName><![CDATA[%s]]></FromUserName>
			<CreateTime>%s</CreateTime>
			<MsgType><![CDATA[%s]]></MsgType>
			<ArticleCount>%s</ArticleCount>
			<Articles>", $this->userMsg->FromUserName, $this->userMsg->ToUserName, time(), 'news', $number);
		
		for($i=0; $i<$number; $i++){
			$lunchMenuStr .= sprintf("
				<item>
				<Title><![CDATA[%s]]></Title>
				<Description><![CDATA[%s]]></Description>
				<PicUrl><![CDATA[%s]]></PicUrl>
				<Url><![CDATA[%s]]></Url>
				</item>
			", $data[$i]['title'],$data[$i]['description'],$data[$i]['picurl'],$data[$i]['url']);
		}
		
		$lunchMenuStr .= "
			</Articles>
			</xml>		
		";
		
		echo $lunchMenuStr;
	}	
	
	
	

	public function journals($notesObj){
		$number = count($notesObj);
		if(!$number){
			$this->text('敬请见谅！还没有找到完成的周报告');
		}
		
		Yii::import('common.models.portfolio.*');

		$data = array();
        $i=0;
		$time = time();
		$picsByYear = array();
		foreach($notesObj as $note){
			if($note->startyear){
				if(trim($note->pids)!=""){
					$_pids = explode(',', $note->pids);
					$_index = rand(1, count($_pids)) - 1;
					$randPic = $_pids[$_index];
					$picIds[$i] = $randPic;
					$picsByYear[$note->startyear][] = $randPic;
				}else{
					$picIds[$i] = 0;
				}
			}else{
				$picIds[$i] = 0;
			}
			
			$data[$i] = array(
				'title' => sprintf("第%s周 %s\r\n%s", $note->weeknumber, $this->myChildObjs[$note->childid]->getChildName(), $note->cInfo->title),// . print_r($_pids, true) . print_r($picIds[$i],true) . print_r($picsByYear,true)),
				'description'=>'',
				'pic' => array($note->startyear,$picIds[$i]),
				'url'=> MimsAPI::createJournalUrl($note, $time, $this->userMsg->FromUserName, $this->securityKey),
			);
			$i++;
		}


		$photos = array();
		$photos = MimsAPI::getPhotos($picsByYear);
		//foreach($picsByYear as $startY=>$_pids){
		//	$childMediaModel[$startY] = ChildMedia::model();
		//	$childMediaModel[$startY]::setStartYear($startY);
		//	$crit = new CDbCriteria;
		//	$crit->compare('id', $_pids);
		//	$crit->index = 'id';
		//	$photos[$startY] = $childMediaModel[$startY]->findAll($crit);
		//}

		foreach($data as $_index=>$_item){
			list($_s,$_p) = $_item['pic'];
			if($_p){
				$thumb = ($_index != 0)? true:false;
                    if(isset($photos[$_s][$_p]))
						$data[$_index]['picurl'] = MimsAPI::getMediaUrl($thumb, $photos[$_s][$_p]);
                    else{
						$data[$_index]['picurl'] = ($_index != 0)?
							 Yii::app()->theme->baseUrl . '/images/journal_default_thumb.jpg' :
							 Yii::app()->theme->baseUrl . '/images/journal_default_normal.jpg';
					}
			}else{
				$data[$_index]['picurl'] = ($_index != 0)?
					Yii::app()->theme->baseUrl . '/images/journal_default_thumb.jpg' :
					Yii::app()->theme->baseUrl . '/images/journal_default_normal.jpg';
					
				$data[$_index]['picurl'] = Yii::app()->homeUrl . $data[$_index]['picurl'];
			}
		}
		$journalStr = sprintf("<xml>
			<ToUserName><![CDATA[%s]]></ToUserName>
			<FromUserName><![CDATA[%s]]></FromUserName>
			<CreateTime>%s</CreateTime>
			<MsgType><![CDATA[%s]]></MsgType>
			<ArticleCount>%s</ArticleCount>
			<Articles>", $this->userMsg->FromUserName, $this->userMsg->ToUserName, time(), 'news', $number);
		
		for($i=0; $i<$number; $i++){
			$journalStr .= sprintf("
				<item>
				<Title><![CDATA[%s]]></Title>
				<Description><![CDATA[%s]]></Description>
				<PicUrl><![CDATA[%s]]></PicUrl>
				<Url><![CDATA[%s]]></Url>
				</item>
			", $data[$i]['title'],$data[$i]['description'],$data[$i]['picurl'],$data[$i]['url']);
		}
		
		$journalStr .= "
			</Articles>
			</xml>		
		";
		
		echo $journalStr;
	}
	
	public function classMates(){
		$toDispayChildIds = array();
	
		//当前没有就读的孩子
		if(count($this->activeChildIds) < 1){
			$this->text("★ 没有找到当前就读的信息，历史信息请登幼儿园系统查询，谢谢！");
		}
		
		//有就读信息
		$schoolsIds = array();
		$classIds = array();
		foreach($this->activeChildIds as $childid){
			if($this->myChildObjs[$childid]->classid){
				$toDispayChildIds[$childid]['childid'] = $childid;
				$schoolsIds[$this->myChildObjs[$childid]->schoolid] = $this->myChildObjs[$childid]->schoolid;
				$classIds[$this->myChildObjs[$childid]->classid] = $this->myChildObjs[$childid]->classid;
			}
		}
		
		if(count($toDispayChildIds)){
			$crit = new CDbCriteria;
			$crit->compare('branchid', $schoolsIds);
			$crit->index = 'branchid';
			$campuses = Branch::model()->with('currentClasses')->findAll($crit);
			
			$text = "";
			$validClasses = array();
			foreach($campuses as $campus){
				foreach($campus->currentClasses as $class){
					$validClasses[$campus->branchid][$class->classid] = $class->title;
				}
			}
			
			foreach(array_keys($toDispayChildIds) as $cid){
				if( in_array($this->myChildObjs[$cid]->classid, array_keys($validClasses[$this->myChildObjs[$cid]->schoolid])) ){
					$toDispayChildIds[$cid]['classId'] = $this->myChildObjs[$cid]->classid;
					$toDispayChildIds[$cid]['campus'] = $campuses[$this->myChildObjs[$cid]->schoolid]->title;
					$toDispayChildIds[$cid]['classTitle'] = $validClasses[$this->myChildObjs[$cid]->schoolid][$this->myChildObjs[$cid]->classid] ;
				}else{
					unset($toDispayChildIds[$cid]);
				}
			}
			if(count($toDispayChildIds)){
				$this->displayClassMatesLink($toDispayChildIds);
			}else{
				$this->text("★ 没有找到孩子的班级信息，可能校园管理人员尚未分班，请联系校园，谢谢！");
			}
		}else{
			$this->text("★ 没有找到孩子的班级信息，可能校园管理人员尚未分班，请联系校园，谢谢！");
		}
		
	}
	
	public function displayClassMatesLink($data){
		$time = time();
		$classMatesStr = sprintf("<xml>
			<ToUserName><![CDATA[%s]]></ToUserName>
			<FromUserName><![CDATA[%s]]></FromUserName>
			<CreateTime>%s</CreateTime>
			<MsgType><![CDATA[%s]]></MsgType>
			<ArticleCount>%s</ArticleCount>
			<Articles>", $this->userMsg->FromUserName, $this->userMsg->ToUserName, time(), 'news', count($data));
		
		foreach($data as $cid=>$cdata){
			$classMatesStr .= sprintf("
				<item>
				<Title><![CDATA[%s]]></Title>
				<Description><![CDATA[%s]]></Description>
				<PicUrl><![CDATA[%s]]></PicUrl>
				<Url><![CDATA[%s]]></Url>
				</item>
			", $data[$cid]['campus'] . ' ' .$data[$cid]['classTitle'] . "\r\n我的小伙伴们",'','',MimsAPI::genClassMatesLink($data[$cid]['classId'], $time, $this->userMsg->FromUserName, $this->securityKey, true ));
		}
		
		$classMatesStr .= "
			</Articles>
			</xml>		
		";
		
		echo $classMatesStr;
	}
	
	public function updateLastContact(){
	
		$time = time();
		if(!is_null( $this->wechatUser ) && isset($this->branchList[$this->campusId]) && isset($this->branchList[$this->campusId]['abb']) ){
			//更新本地微信用户数据
			if( ( $time - $this->wechatUser->last_contact ) > 1800	){
				$this->wechatUser->last_contact = $time;
				$this->wechatUser->child_active = count($this->activeChildIds);
				$this->wechatUser->campus = $this->branchList[$this->campusId]['abb'];
				$this->wechatUser->save();
			}
			//更新远程微信用户组
			if( ( $this->wechatUser->group_sync == 0 ) || ($time - $this->wechatUser->group_sync) > 2592000 ){
			
				$ch = curl_init();
				$time = time();
				$command = 'doGroup';
				$data = array(
					"postTime" => $time,
					"command" => $command,
					"params" => base64_encode(serialize(array(
						'openid' => $this->wechatUser->openid,
						'groupKey' => $this->branchList[$this->campusId]['abb'],
					))),
					"postKey" => md5(sprintf("%s&%s&%s", $time, $command, $this->securityKey))
					);
				curl_setopt($ch, CURLOPT_URL,  $this->createUrl("/weixin/remoteCall/doGroup")); 
				curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
				curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
				curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
				$return = curl_exec($ch);
				if($return){
					$array = CJSON::decode($return);
					if($array['errcode'] < 1){
						$this->wechatUser->group_sync = time();
						$this->wechatUser->save();
					}
				}
				curl_close($ch);
			}
		}
	}
	
	public function processCustomQrCodeResult($data){
		if($data['code']){
			$text = "";
			$childInfo = "";
			switch($data['code']){
				case MimsAPI::BOUND_USER_MAXEXCEEDED:
					$text = "★ 此帐号绑定的微信已达上限 \r\n";
					$text.= "为保证账户安全，请选择“绑定幼儿园系统帐号”菜单按提示手动输入帐号信息进行绑定";
				break;
				case MimsAPI::BOUND_USER_ALREADY:
					$text = "★ 已经绑定过此账户 \r\n";
					$text .= "孩子信息：\r\n";
				break;
				case MimsAPI::BOUND_USER_REPLACED:
					$text = "★ 已经绑定新账户，先前绑定的账户失效 \r\n";
					$text .= "孩子信息：\r\n";
				break;
				case MimsAPI::BOUND_USER_DONE:
					$text = "★ 账户绑定成功 \r\n";
					$text .= "孩子信息：\r\n";
				break;
				default:
					$text = "★ 账户绑定失败 \r\n";
					$text .= isset($data['message'])?$data['message']:$text;
				break;
			}
			if(isset($data['childData']) && isset($data['childData']['data'])){
				foreach($data['childData']['data'] as $_child){
					$text .= sprintf("%s %s %s", $_child['childName'], $_child['campus'], ($_child['active'])?'就读中':'非就读中') . "\r\n";
				}
				if(!$data['childData']['hasActive']){
					$text .= '★ 抱歉微信应用暂时只对在读中的孩子提供服务，请使用电脑端访问非在读孩子信息。';
				}
			}
			$this->text($text);
		}
	}
	
	public function news(){
		$newsTpl = "<xml>
			<ToUserName><![CDATA[%s]]></ToUserName>
			<FromUserName><![CDATA[%s]]></FromUserName>
			<CreateTime>%s</CreateTime>
			<MsgType><![CDATA[%s]]></MsgType>
			<ArticleCount>3</ArticleCount>
			<Articles>
			<item>
			<Title><![CDATA[%s]]></Title> 
			<Description><![CDATA[%s]]></Description>
			<PicUrl><![CDATA[%s]]></PicUrl>
			<Url><![CDATA[%s]]></Url>
			</item>
			<item>
			<Title><![CDATA[%s]]></Title>
			<Description><![CDATA[%s]]></Description>
			<PicUrl><![CDATA[%s]]></PicUrl>
			<Url><![CDATA[%s]]></Url>
			</item>
			<item>
			<Title><![CDATA[%s]]></Title>
			<Description><![CDATA[%s]]></Description>
			<PicUrl><![CDATA[%s]]></PicUrl>
			<Url><![CDATA[%s]]></Url>
			</item>
			</Articles>
			</xml> 
		";
		$msgType = "news";
		$news = array(
			'0'=>array(
				'title'=>'《好孕妈妈》专访：园长解读宝宝开心入园四步曲',
				'description'=>'2013年6月，艾毅教育机构多元智能总监顾克女士接受了《好孕妈妈》杂志专访。',
				'picurl'=>'http://www.ivyschools.com/upload/news/thumbs/news_51f75317406d5.jpg',
				'url'=>'http://www.ivyschools.com/Information/News/792?lang=zh_cn',
			),
			'1'=>array(
				'title'=>'艾毅新城国际校园探秘艺术世界',
				'description'=>'2013年7月20日，艾毅国际幼儿园新城国际校园第二次在新城国际社区中心花园与小朋友和家长见面，大家的热情依然有增无减。',
				'picurl'=>'http://www.ivyschools.com/upload/news/thumbs/news_51f22e49e2ba4.jpg',
				'url'=>'http://www.ivyschools.com/Information/News/790?lang=zh_cn',									
			),
			'2'=>array(
				'title'=>'绑定艾毅在线帐号',
				'description'=>'绑定艾毅在线帐号',
				'picurl'=>'http://www.ivyschools.com/upload/news/thumbs/news_51f22e49e2ba4.jpg',
				'url'=>'http://www.ivyonline.cn/weixin/user/index',
			)
		);
		$contentStr = "http://www.ivyschools.com";
		$resultStr = sprintf($newsTpl, $this->userMsg->FromUserName, $this->userMsg->ToUserName, time(), $msgType,
							$news[0]['title'],
							$news[0]['description'],
							$news[0]['picurl'],
							$news[0]['url'],
							$news[1]['title'],
							$news[1]['description'],
							$news[1]['picurl'],
							$news[1]['url'],
							$news[2]['title'],
							$news[2]['description'],
							$news[2]['picurl'],
							$news[2]['url']
							);
		echo $resultStr;
	}
}
