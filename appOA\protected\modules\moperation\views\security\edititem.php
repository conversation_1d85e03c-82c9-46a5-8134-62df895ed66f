<?php
$year = array(date("Y", time()) => date("Y", time()),  date("Y", time())+1 => date("Y", time())+1);

$month = array();
for ($m = 1; $m < 13; $m++) {
    $month[sprintf("%02d", $m)] = sprintf("%02d", $m);
}
$yearDate = date("Y" , time());
$mo = date("Y-m" , time());
$monthDate =  date("m",strtotime("$mo +1 month"));
$status = array(
    0 => '<span class="label label-default">无效</span>',
    1=> '<span class="label label-success">有效</span>',
    9999 => '<span class="label label-danger">作废</span>'
)
?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','HQ Operations'), array('/moperation'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Basic Configurations'), array('/moperation'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','安全报告模板'), array('/moperation/security/updateConfiguration'))?></li>
        <li class="active"><?php echo $safetyModel->getName() ?></li>
    </ol>

    <?php if ($model): ?>
        <div class="row">
            <div class="col-md-2">
                <ul class="nav nav-pills nav-stacked background-gray">
                    <?php foreach ($model as $_item) { ?>
                    <li <?php if ($scid == $_item->id) {
                        echo 'class="active"';
                    } ?>>
                        <?php echo CHtml::link($_item->getName(), array('edititem', 'sid' => $_item->sid, 'scid' => $_item->id)) ?></li>
                    </li>
                <?php } ?>
                </ul>
            </div>
            <div class="col-md-10">
                <?php if ($scid): ?>
                    <div class="mb10">
                        <button type="button" class="btn btn-primary mr5" onclick="addItem(9);">
                            <?php echo Yii::t('report', '增加选项');?></button>
                    </div>
                    <?php if ($subs): ?>
                        <div class="panel panel-default">
                            <ul class="list-group">
                                <?php foreach ($subs as $item): ?>
                                    <li class="list-group-item" data-id="<?php echo $item->id; ?>"
                                        data-sid="<?php echo $item->id; ?>">
                                        <div class="row">
                                            <div class="col-md-10">
                                                <p class="col-md-11 text-info"><?php echo $status[$item->status] ?> <?php echo nl2br($item->getName()) ?></p>
                                            </div>
                                            <div class="col-md-2">
                                                <a role="button" class="btn btn-xs btn-danger J_ajax_del pull-right" href="<?php echo $this->createUrl
                                                ('invalidRule', array('id' => $item->id)) ?>"><span class="glyphicon glyphicon-minus"></span></a>
                                                <a href="<?php echo $this->createUrl('updatedRule', array("srid" => $item->id)); ?>"
                                                   class="J_modal btn btn-xs btn-primary pull-right mr5"><span class="glyphicon glyphicon-pencil"></span></a>
                                                <a role="button" class="btn btn-xs btn-success sort-header pull-right mr5" style="cursor: move;"><span class="glyphicon glyphicon-move"></span></a>
                                            </div>
                                        </div>

                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    <?php else: ?>
        <div class="col-md-12">
            <div class="alert alert-warning" role="alert">
                <?php echo Yii::t('report', 'Please add report categories first.'); ?>
            </div>
        </div>
    <?php endif; ?>
</div>

<div class="modal fade" id="addModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title"><?php echo Yii::t('report', '增加详细选项'); ?>
                    <small></small>
                </h4>
            </div>
            <form class="form-horizontal J_ajaxForm"
                  action="<?php echo $this->createUrl('createdRule'); ?>"
                  method="POST">
                <div class="modal-body">
                    <?php for ($i = 0; $i < 10; $i++): ?>
                        <blockquote>
                            <div class="form-group">
                                <div class="col-sm-3">
                                    <?php echo CHtml::textArea('title_en[]', '', array('class' => 'form-control', 'rows'=>4,  'placeholder' => '英文内容')); ?>
                                </div>
                                <div class="col-sm-3">
                                    <?php echo CHtml::textArea('title_cn[]', '', array('class' => 'form-control', 'rows'=>4, 'placeholder' => '中文内容')); ?>
                                </div>
                                <div class="col-sm-2">
                                    <?php echo CHtml::textField('document_en[]', '', array('class' => 'form-control', 'placeholder' => '记录和文档_英文')); ?>
                                    <br>
                                    <?php echo CHtml::textField('document_cn[]', '', array('class' => 'form-control', 'placeholder' => '记录和文档_中文')); ?>
                                </div>
                                <div class="col-sm-4">
                                    <div class="col-xs-6">
                                        <?php echo CHtml::dropDownList('year[]', $yearDate, $year,  array('class' => 'form-control',)); ?>
                                        <br>
                                        <?php echo CHtml::dropDownList('month[]', $monthDate, $month, array('class' => 'form-control')); ?>
                                    </div>
                                </div>
                            </div>
                        </blockquote>
                        <?php if($i != 9 ){
                            echo "<hr/>";
                        }?>
                    <?php endfor; ?>
                </div>
                <div class="modal-footer">
                    <input type="hidden" name="sid" value="<?php echo $sid; ?>">
                    <input type="hidden" name="scid" value="<?php echo $scid; ?>">
                    <button type="button" class="btn btn-default pull-right"
                            data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
                    <button type="submit"
                            class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global', 'Submit'); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    function addItem(pid) {
        $('#addModal').modal({backdrop: 'static', keyboard: false});
    }

    function cbSuccess() {
        setTimeout(function () {
            location.reload();
        }, 1000)
    }

    // 模态框
    var modal = '<div class="modal fade" id="modal" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog" role="document"><div class="modal-content"></div></div></div>';
    $('body').append(modal);
    $(".list-group").sortable({
        placeholder: "ui-state-highlight",
        handle: ".sort-header",
        axis: 'y',
        opacity: 0.8,
        start: function (event, ui) {
            $('.ui-state-highlight').height($(ui['item'][0]).outerHeight()).css("list-style-type", "none");
        },
        update: function (event, ui) {
            var sort = {};
            var cc = 1;
            $(ui['item'][0]).parents('.list-group').find('li').each(function () {
                sort[$(this).data('id')] = cc++;

            });
            $.post('<?php echo $this->createUrl('updateRuleSort');?>', {sort: sort}, function (data) {
                if(data.state == 'fail'){
                    resultTip({error: 'warning', msg: data.message});
                }
            }, 'json');
        }
    })
</script>