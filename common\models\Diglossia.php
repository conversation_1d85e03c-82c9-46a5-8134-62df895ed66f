<?php

/**
 * This is the model class for table "ivy_diglossia".
 *
 * The followings are the available columns in table 'ivy_diglossia':
 * @property integer $diglossia_id
 * @property integer $category_id
 * @property string $entitle
 * @property string $cntitle
 * @property integer $weight
 */
class Diglossia extends CActiveRecord {

    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return Diglossia the static model class
     */
    public static function model($className = __CLASS__) {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName() {
        return 'ivy_diglossia';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules() {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('category_id, weight', 'numerical', 'integerOnly' => true),
            array('entitle, cntitle', 'length', 'max' => 150),
            // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array('diglossia_id, category_id, entitle, cntitle, weight', 'safe', 'on' => 'search'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations() {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
            'category' => array(self::BELONGS_TO, 'DiglossiaCategory', 'category_id')
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels() {
        return array(
            'diglossia_id' => 'Diglossia',
            'category_id' => 'Category',
            'entitle' => 'Entitle',
            'cntitle' => 'Cntitle',
            'weight' => 'Weight',
        );
    }

    public function getDiglossiaList($category = 'lang', $firstLabel = null, $lang = false) {

        $criteria = new CDbCriteria();
        if (false === $lang) {
            $lang = Yii::app()->language;
        }
        if (true === $lang) {
            $cacheId = __CLASS__ . '_' . $category . '_all_' . Yii::app()->language;
        } else {
            $cacheId = __CLASS__ . '_' . $category . '_' . $lang;
        }
        if ('zh_cn' == Yii::app()->language) {
            $criteria->order = 't.weight ASC, t.cntitle ASC';
        } else {
            $criteria->order = 't.weight ASC, t.entitle ASC';
        }

        $diglossiaList = Yii::app()->cache->get($cacheId);

        if (false === $diglossiaList) {
            $criteria->compare('category.category_sign', $category);
            $criteria->with = 'category';
            $diglossiaInfo = $this->findAll($criteria);
            if (!empty($diglossiaInfo)) {
                if ($firstLabel) {
                    $diglossiaList[0] = $firstLabel;
                }
                foreach ($diglossiaInfo as $d) {
                    $diglossiaList[$d->diglossia_id] = ToolKits::getContentByLang($d->cntitle, $d->entitle, $lang);
                }
            }
            Yii::app()->cache->set($cacheId, $diglossiaList, 3600 * 24 * 7);
        }

        return $diglossiaList;
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search() {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('diglossia_id', $this->diglossia_id);
        $criteria->compare('category_id', $this->category_id);
        $criteria->compare('entitle', $this->entitle, true);
        $criteria->compare('cntitle', $this->cntitle, true);
        $criteria->compare('weight', $this->weight);

        return new CActiveDataProvider($this, array(
                    'criteria' => $criteria,
                ));
    }

    public function getLanguage($language = '')
    {
        $languageList = array();
        $criteria = new CDbCriteria;
        $criteria->compare('category_id', 1);
        $select = Yii::app()->language == 'zh_cn' ? 'cntitle' : 'entitle';
        if($language){
            $select = 'entitle';
        }
        $model = $this->findAll($criteria);
        foreach ($model as $v) {
            $languageList[$v->diglossia_id] = $v->$select;
        }
        return $languageList;
    }

}